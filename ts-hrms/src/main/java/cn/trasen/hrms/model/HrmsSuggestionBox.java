package cn.trasen.hrms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;

import cn.trasen.homs.core.model.BaseBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "hrms_suggestion_box")
@Setter
@Getter
public class HrmsSuggestionBox  extends BaseBean {

    /**
     * 是否查看
     */
    @Column(name = "status")
    @ApiModelProperty(value = "是否查看")
    private String status;

    /**
     * 查看人名称
     */
    @Column(name = "look_name")
    @ApiModelProperty(value = "查看人名称")
    private String lookName;
    
    
    /**
     * 查看人名称
     */
    @Column(name = "submit_ip")
    @ApiModelProperty(value = "提交人ip")
    private String submitIp;
    

    /**
     * 查看人工号
     */
    @Column(name = "look_code")
    @ApiModelProperty(value = "查看人工号")
    private String lookCode;

    /**
     * 查看时间
     */
    @Column(name = "look_time")
    @ApiModelProperty(value = "查看时间")
    private Date lookTime;

    /**
     * 意见
     */
    @Column(name = "result")
    @ApiModelProperty(value = "处理结果")
    private String result;

    /**
     * 意见
     */
    @Column(name = "opinion")
    @ApiModelProperty(value = "意见")
    private String opinion;
    
    @Column(name = "box_type")
    private String boxType;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
}
package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;


@Table(name = "hrms_scheduling_grouping")
@Setter
@Getter
public class HrmsSchedulingGrouping {

    /**
     * 考勤组id
     */
    @Id
    @Column(name = "frequency_grouping_id")
    @ApiModelProperty(value = "考勤组id")
    private String frequencyGroupingId;

    /**
     * 考勤组名称
     */
    @Column(name = "frequency_grouping_name")
    @ApiModelProperty(value = "考勤组名称")
    private String frequencyGroupingName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    @Column(name = "auto_scheduling")
    @ApiModelProperty(value = "开启自动排班  0否 1是")
    private String autoScheduling;
    
    @Column(name = "auto_jurisdiction_id")
    @ApiModelProperty(value = "自动排班班次id")
    private String autoJurisdictionId;
    
    @Column(name = "auto_jurisdiction_name")
    @ApiModelProperty(value = "自动排班班次名称")
    private String autoJurisdictionName;
    
    @Column(name = "auto_start_date")
    @ApiModelProperty(value = "自动排班班开始日期")
    private String autoStartDate;
    
    @Column(name = "auto_end_date")
    @ApiModelProperty(value = "自动排班结束日期")
    private String autoEndDate;
    
    @Transient
    @ApiModelProperty(value = "考勤组人员工号(逗号分隔)")
    private String employeeNo;
    
    @Transient
    @ApiModelProperty(value = "考勤组人员(逗号分隔)")
    private String employeeName;
    
    @Transient
    @ApiModelProperty(value = "考勤组人员Id(逗号分隔)")
    private String employeeId;
    
    @Transient
    @ApiModelProperty(value = "合计")
    private String counting;
    
    @Transient
    @ApiModelProperty(value = "考勤组人员集合")
    private List<HrmsSchedulingGroupingEmp> schedulingGroupingEmp;
    
    @Column(name = "org_id")
    @ApiModelProperty(value = "科室ID")
    private String orgId;
    
    @Column(name = "org_name")
    @ApiModelProperty(value = "科室名称")
    private String orgName;
    
	@ApiModelProperty(value = "查询时组织机构集合")
	private List<String> orgIds;
	
	@Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Transient
    @ApiModelProperty(value = "排序号")
    private Integer sort;
    @Transient
    private String phone;  //手机好
    
    @Transient
    private String searchStartDate;
    @Transient
    private String searchEndDate;
    @Transient
    private String movementType;  //请求类型 1排班  2 查看
}
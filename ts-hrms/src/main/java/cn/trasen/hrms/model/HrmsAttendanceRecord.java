package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_attendance_record")
@Setter
@Getter
public class HrmsAttendanceRecord {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "attendance_record_id")
	@ApiModelProperty(value = "主键ID")
	private String attendanceRecordId;

	/**
	 * 员工ID
	 */
	@Column(name = "employee_id")
	@ApiModelProperty(value = "员工ID")
	private String employeeId;

	/**
	 * 员工工号
	 */
	@Column(name = "employee_no")
	@ApiModelProperty(value = "员工工号")
	private String employeeNo;
	
	/**
	 * 所属机构
	 */
	@Column(name = "belong_org")
	@ApiModelProperty(value = "所属机构")
	private String belongOrg;
	

	/**
	 * 考勤月份
	 */
	@Column(name = "attendance_date")
	@ApiModelProperty(value = "考勤月份")
	private String attendanceDate;

	/**
	 * 审核状态
	 */
	@Column(name = "approval_status")
	@ApiModelProperty(value = "审核状态")
	private String approvalStatus;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;
	
	
	//科室审核
	@Column(name = "dept_check_date")
	@ApiModelProperty(value = "科室审核时间")
	private Date deptCheckDate;
	
	@Column(name = "dept_check_userName")
	@ApiModelProperty(value = "科室审核人")
	private String deptCheckUserName;
	
	@Column(name = "dept_check_userId")
	@ApiModelProperty(value = "科室审核人id")
	private String deptCheckUserId;
	
	
	//人事考勤审核
	@Column(name = "rskq_check_status")
	@ApiModelProperty(value = "人事考勤审核状态")
	private String rskqCheckStatus;
	
	@Column(name = "rskq_check_userId")
	@ApiModelProperty(value = "人事考勤审核人")
	private String rskqCheckUserId;
	
	@Column(name = "rskq_check_date")
	@ApiModelProperty(value = "人事考勤审核时间")
	private Date rskqCheckDate;
	

	//晚夜班审核
	@Column(name = "rswyb_check_status")
	@ApiModelProperty(value = "人事晚夜班审核状态")
	private String rswybCheckStatus;
	
	@Column(name = "rswyb_check_userId")
	@ApiModelProperty(value = "人事晚夜班审核审核人")
	private String rswybCheckUserId;
	
	@Column(name = "rswyb_check_date")
	@ApiModelProperty(value = "人事晚夜班审核时间")
	private Date rswybCheckDate;
	   


	
	

	// ------- 扩展字段 ------- //
	/**
	 * 审核状态文本值
	 */
	@Transient
	@ApiModelProperty(value = "审核状态文本值")
	private String approvalStatusText;

	/**
	 * 员工姓名
	 */
	@Transient
	@ApiModelProperty(value = "员工姓名")
	private String employeeName;
    
	/**
	 * 组织机构ID集合-权限使用
	 */
	@Transient
	@ApiModelProperty(value = "组织机构ID集合-权限使用")
	private List<String> orgIdList;

	/**
	 * 用户Code-权限使用
	 */
	@Transient
	@ApiModelProperty(value = "用户Code-权限使用")
	private String userCode;
	
	@Transient
	private String employeeStatus;  //人员状态
	
	@Transient
	private String searchDate;  //查询时间
	
	@Transient
	private String isDeclare;  //是否上报（0未上报，1已上报）
	
	@Transient
	private String orgRangs;  //机构下面的数据权限
	
	@Transient
	private String reviewDepart;  //审核科室
	
	@Transient
	private String isQQ;  //0全勤，1晚夜班
	
	@Transient
	private String title;  //excel标题
	
	@Transient
	private String deptCheck; //科室审核
	
	@Transient
	private String searchOrgId; //搜索科室id
	
	@Transient
	private String unseSta; 	//1：科室审核查看未上报 
	
	@Transient
	private List<String> searchOrgIds; //要显示的科室
	
	@Transient
	private String overtime; 	//1:查看晚夜班
	
	@Column(name = "set_qq")
	@ApiModelProperty(value = "全勤设置 0未设置 1已设置")
	private String setQq;
	

}
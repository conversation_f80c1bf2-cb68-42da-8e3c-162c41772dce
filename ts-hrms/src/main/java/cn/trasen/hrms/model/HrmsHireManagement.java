package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "hrms_hire_management")
@Setter
@Getter
public class HrmsHireManagement {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "hire_management_id")
    @ApiModelProperty(value = "主键ID")
    private String hireManagementId;

    /**
     * 姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    /**
     * 联系电话
     */
    @Column(name = "telephone_number")
    @ApiModelProperty(value = "联系电话")
    private String telephoneNumber;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 入职机构ID
     */
    @Column(name = "entry_org_id")
    @ApiModelProperty(value = "入职机构ID")
    private String entryOrgId;

    /**
     * 入职机构名称
     */
    @Column(name = "entry_org_name")
    @ApiModelProperty(value = "入职机构名称")
    private String entryOrgName;

    /**
     * 入职岗位
     */
    @Column(name = "entry_post")
    @ApiModelProperty(value = "入职岗位")
    private String entryPost;

    /**
     * 预计到岗时间
     */
    @Column(name = "estimated_arrival_time")
    @ApiModelProperty(value = "预计到岗时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date estimatedArrivalTime;

    /**
     * 试用期(月)
     */
    @Column(name = "probation_month")
    @ApiModelProperty(value = "试用期(月)")
    private String probationMonth;

    /**
     * 试用期薪资
     */
    @Column(name = "probation_salary")
    @ApiModelProperty(value = "试用期薪资")
    private BigDecimal probationSalary;

    /**
     * 转正后薪资
     */
    @Column(name = "turnning_salary")
    @ApiModelProperty(value = "转正后薪资")
    private BigDecimal turnningSalary;

    /**
     * 录用状态
     */
    @Column(name = "hire_status")
    @ApiModelProperty(value = "录用状态")
    private String hireStatus;

    /**
     * 通知方式
     */
    @Column(name = "notice_way")
    @ApiModelProperty(value = "通知方式")
    private String noticeWay;

    /**
     * 通知内容
     */
    @Column(name = "notice_content")
    @ApiModelProperty(value = "通知内容")
    private String noticeContent;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    // ------- 扩展字段 ------- //
    /**
     * 面试记录ID
     */
    @Transient
	@ApiModelProperty(value = "面试记录ID")
	private String interviewManagementId;
    
    /**
     * 面试状态ID
     */
    @Transient
	@ApiModelProperty(value = "面试状态ID")
    private String interviewStatus;
    
    /**
     * 录用状态文本值
     */
    @Transient
    @ApiModelProperty(value = "录用状态文本值")
    private String hireStatusText;
    
    /////////// 导出 /////////////////
    /**
     * 预计到岗时间
     */
    @Transient
    private String estimatedArrivalTimeExport;
    
    
    @Column(name = "resume_storehouse_id")
    private String resumeStorehouseId; //简历库id
    
}
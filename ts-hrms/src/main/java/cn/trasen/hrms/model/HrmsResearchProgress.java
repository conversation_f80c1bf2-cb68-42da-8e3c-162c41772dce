package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_research_progress")
@Setter
@Getter
public class HrmsResearchProgress {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    private String id;

    /**
     * 课题id
     */
    @Column(name = "topic_id")
    @ApiModelProperty(value = "课题id")
    private String topicId;

    /**
     * 是否延期  0未延期 1已延期
     */
    @ApiModelProperty(value = "是否延期  0未延期 1已延期")
    private String delay;

    /**
     * 附件id，多个附件逗号隔开
     */
    @Column(name = "files_id")
    @ApiModelProperty(value = "附件id，多个附件逗号隔开")
    private String filesId;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "is_deleted")
    private String isDeleted;

    /**
     * 计划进展
     */
    @Column(name = "plan_content")
    @ApiModelProperty(value = "计划进展")
    private String planContent;

    /**
     * 实际进展
     */
    @Column(name = "actual_content")
    @ApiModelProperty(value = "实际进展")
    private String actualContent;

    /**
     * 延期原因
     */
    @Column(name = "delay_remark")
    @ApiModelProperty(value = "延期原因")
    private String delayRemark;

    /**
     * 实际说明
     */
    @Column(name = "progress_remark")
    @ApiModelProperty(value = "实际说明")
    private String progressRemark;
    
    
    @Column(name = "progress_type")
    @ApiModelProperty(value = "进展类型  1更新进展 2信息变更")
    private String progressType;
    
    
    @Column(name = "change_remark")
    @ApiModelProperty(value = "变更说明")
    private String changeRemark;
}

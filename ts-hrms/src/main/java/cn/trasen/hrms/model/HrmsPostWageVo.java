package cn.trasen.hrms.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.math.BigDecimal;

@Data
public class HrmsPostWageVo {
    @Id
    @ApiModelProperty(value = "主键ID")
    private String     postWageId;

    @ApiModelProperty(value = "岗位ID")
    private String     postId;

    @ApiModelProperty(value = "岗位工资")
    private BigDecimal postWage = BigDecimal.ZERO;

    @ApiModelProperty(value = "绩效工资")
    private BigDecimal performanceWage = BigDecimal.ZERO;

    @ApiModelProperty(value = "奖励性绩效")
    private BigDecimal awardWage = BigDecimal.ZERO;
    
    @ApiModelProperty(value = "岗位名称")
    private String     postName;

    @ApiModelProperty(value = "岗位类别ID")
    private String     postCategory;
    
    @ApiModelProperty(value = "岗位类别名称")
    private String     postCategoryName;

    @Column(name = "policy_standard_id")
    @ApiModelProperty(value = "政策标准id")
    private String policyStandardId;

}
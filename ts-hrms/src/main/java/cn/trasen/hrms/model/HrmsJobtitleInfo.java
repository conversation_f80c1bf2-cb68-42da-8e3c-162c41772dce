package cn.trasen.hrms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.jeecgframework.poi.excel.annotation.Excel;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "hrms_jobtitle_info")
@Setter
@Getter
public class HrmsJobtitleInfo {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	/**
	 * 主键ID
	 */
	@Id
	@ApiModelProperty(value = "主键ID")
	private String id;

	/**
	 * 员工ID
	 */
	@Column(name = "employee_id")
	@ApiModelProperty(value = "员工ID")
	private String employeeId;
	
	@Excel(name = "人员工号")
	@Transient
	@ApiModelProperty(value = "人员工号")
	private String employeeNo;
	
	@Excel(name = "人员姓名")
	@ApiModelProperty(value = "人员姓名")
	@Transient
	private String employeeName;

	/**
	 * 职称类别
	 */
	@Excel(name = "职称类别")
	@Column(name = "jobtitle_category")
	@ApiModelProperty(value = "职称类别")
	private String jobtitleCategory;

	/**
	 * 职称等级
	 */
	@Excel(name = "职称级别")
	@Column(name = "jobtitle_level")
	@ApiModelProperty(value = "职称等级")
	private String jobtitleLevel;

	/**
	 * 职称名称
	 */
	@Excel(name = "职称名称")
	@Column(name = "jobtitle_name")
	@ApiModelProperty(value = "职称名称")
	private String jobtitleName;

	/**
	 * 专业名称
	 */
	@Excel(name = "专业名称")
	@Column(name = "professional_name")
	@ApiModelProperty(value = "专业名称")
	private String professionalName;

	/**
	 * 获取途径
	 */
	@Excel(name = "获取途径")
	@Column(name = "accept_method")
	@ApiModelProperty(value = "获取途径")
	private String acceptMethod;

	/**
	 * 证书编号
	 */
	@Excel(name = "证书编号")
	@Column(name = "certificate_number")
	@ApiModelProperty(value = "证书编号")
	private String certificateNumber;

	/**
	 * 聘任日期
	 */
	@Column(name = "assessment_date")
	@ApiModelProperty(value = "聘任日期")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date assessmentDate;

	/**
	 * 评定机构
	 */
	@Excel(name = "评定机构（审批机关）")
	@Column(name = "assessment_agency")
	@ApiModelProperty(value = "评定机构")
	private String assessmentAgency;

	/**
	 * 最高职称: 1=是; 2=否;
	 */
	@Excel(name = "是否最高职称")
	@Column(name = "highest_level")
	@ApiModelProperty(value = "最高职称: 1=是; 2=否;")
	private String highestLevel;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 职称类别文本值
	 */
	@Transient
	@ApiModelProperty(value = "职称类别文本值")
	private String jobtitleCategoryText;

	/**
	 * 职称等级文本值
	 */
	@Transient
	@ApiModelProperty(value = "职称等级文本值")
	private String jobtitleLevelText;

	/**
	 * 职称名称文本值
	 */
	@Transient
	@ApiModelProperty(value = "职称名称文本值")
	private String jobtitleNameText;

	/**
	 * 获取途径文本值
	 */
	@Transient
	@ApiModelProperty(value = "获取途径文本值")
	private String acceptMethodText;
	
	@Column(name = "inaugural_date")
	@ApiModelProperty(value = "任职时间")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date inauguralDate;

	@Column(name = "downward_date")
	@ApiModelProperty(value = "低一级聘任时间")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date downwardDate;
	
	@Column(name = "acquisition_date")
	@ApiModelProperty(value = "获取时间")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date acquisitionDate;
	
	
	@Excel(name = "聘任日期")
	@Transient
	private String assessmentDateStr;
	
	@Excel(name = "取得时间")
	@Transient
	private String acquisitionDateStr;
	
	@Excel(name = "低一级聘任时间")
	@Transient
	private String downwardDateStr;
	
	@Excel(name = "任职日期")
	@Transient
	private String inauguralDateStr;
	
	@Column(name = "business_id")
	@ApiModelProperty(value = "附件id")
	private String businessId;
	
	@Transient
	private String gender;
	
	@Transient
	private String employeeStatus;

	//导出序号
	@Transient
	private Integer pm;
}
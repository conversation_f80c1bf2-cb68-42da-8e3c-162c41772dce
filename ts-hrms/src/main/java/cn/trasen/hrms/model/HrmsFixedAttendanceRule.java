package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_fixed_attendance_rule")
@Setter
@Getter
public class HrmsFixedAttendanceRule {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "fixed_attendance_rule_id")
    @ApiModelProperty(value = "主键ID")
    private String fixedAttendanceRuleId;

    /**
     * 适用科室
     */
    @Column(name = "suit_department")
    @ApiModelProperty(value = "适用科室")
    private String suitDepartment;

    /**
     * 上班时间
     */
    @Column(name = "start_work_time")
    @ApiModelProperty(value = "上班时间")
    private String startWorkTime;

    /**
     * 下班时间
     */
    @Column(name = "off_work_time")
    @ApiModelProperty(value = "下班时间")
    private String offWorkTime;

    /**
     * 午休开始时间
     */
    @Column(name = "siesta_start_time")
    @ApiModelProperty(value = "午休开始时间")
    private String siestaStartTime;

    /**
     * 午休结束时间
     */
    @Column(name = "siesta_end_time")
    @ApiModelProperty(value = "午休结束时间")
    private String siestaEndTime;

    /**
     * 午休是否需要打卡
     */
    @Column(name = "need_check")
    @ApiModelProperty(value = "午休是否需要打卡")
    private String needCheck;

    /**
     * 考勤地点
     */
    @Column(name = "attendance_place")
    @ApiModelProperty(value = "考勤地点")
    private String attendancePlace;

    /**
     * 允许偏差范围
     */
    @ApiModelProperty(value = "允许偏差范围")
    private String deviation;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人ID")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 科室ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "科室ID")
    private String orgId;

    /**
     * 科室名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "科室名称")
    private String orgName;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;
}
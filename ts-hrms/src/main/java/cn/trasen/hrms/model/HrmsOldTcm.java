package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;
import org.jeecgframework.poi.excel.annotation.Excel;

@Table(name = "hrms_old_tcm")
@Setter
@Getter
public class HrmsOldTcm {



    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工ID")
    private String employeeId;

    /**
     * 年度
     */
    @Column(name = "year_date")
    @ApiModelProperty(value = "年度")
    private String yearDate;

    /**
     * 科室id
     */
    @Column(name = "old_org_id")
    @ApiModelProperty(value = "科室id")
    private String oldOrgId;

    /**
     * 科室名称
     */
    @Column(name = "old_org_name")
    @ApiModelProperty(value = "科室名称")
    private String oldOrgName;

    /**
     * 身份证号码
     */
    @Column(name = "identity_number")
    @ApiModelProperty(value = "身份证号码")
    private String identityNumber;

    /**
     * 姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    /**
     * 工号
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "工号")
    private String employeeNo;

    /**
     * 原职务ID
     */
    @Column(name = "old_position_id")
    @ApiModelProperty(value = "原职务ID")
    private String oldPositionId;

    @Column(name = "old_position_name")
    @ApiModelProperty(value = "原职务名称")
    private String oldPositionName;



    /**
     * 岗位名称
     */
    @Column(name = "old_personal_identity")
    @ApiModelProperty(value = "岗位名称")
    private String oldPersonalIdentity;

    @Column(name = "old_personal_identity_name")
    @ApiModelProperty(value = "岗位名称中文")
    private String oldPersonalIdentityName;



    /**
     * 导师姓名
     */
    @ApiModelProperty(value = "导师姓名")
    private String dsxm;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 组织机构名称
     */
    @Excel(name = "机构名称")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    @Transient
    private Integer no;
}
package cn.trasen.hrms.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * 退休人员导入DTO
 */
@Setter
@Getter
public class HrmsPersonnelRetirementImportDto {

    /**
     * 员工Id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工Id")
    private String employeeId;

    /**
     * 员工工号
     */
    @Excel(name = "工号")
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 员工姓名
     */
    @Excel(name = "姓名")
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    /**
     * 事件类型
     */
    @Excel(name = "退休类型")
    @ApiModelProperty(value = "事件类型")
    private String incidentType;

    /**
     * 事件类别: 1=离职; 2=退休; 3=死亡; 4=延聘; 5=返聘;
     */
    @ApiModelProperty(value = "事件类别: 1=离职; 2=退休; 3=死亡; 4=延聘; 5=返聘;")
    private String incidentCategory = "2";

    /**
     * 审批状态: 1=未审批; 2=审批中; 3=已退回; 4=已审批
     */
    @ApiModelProperty(value = "审批状态: 1=未审批; 2=审批中; 3=已退回; 4=已审批")
    private String approvalStatus = "1";



    /**
     * 事件时间(针对不同类型事件只有一个日期的存这个字段,例如:离职日期)
     */
    @Excel(name = "退休日期")
    @ApiModelProperty(value = "事件时间(针对不同类型事件只有一个日期的存这个字段,例如:离职日期)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String incidentTimeStr;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 组织机构ID
     */
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Excel(name = "部门")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    @Excel(name = "档案出生日期")
    @ApiModelProperty(value = "档案出生日期")
    private String txRecordTime;

    @Excel(name = "退休工资")
    @ApiModelProperty(value = "退休工资")
    private String retiredMoney;

    @Excel(name = "津贴补助")
    @ApiModelProperty(value = "津贴补助")
    private String subsidyMoney;

    /**
     * 职位/岗位ID
     */
    @ApiModelProperty(value = "职位/岗位ID")
    @Column(name = "position_id")
    private String positionId;

    /**
     * 职位/岗位
     */
    @Transient
    @ApiModelProperty(value = "职位/岗位")
    private String positionName;


    @Column(name = "personal_identity")
    @ApiModelProperty(value = "岗位")
    private String personalIdentity;
    
    @Transient
    @ApiModelProperty(value = "岗位")
    private String personalIdentityText;

    @ApiModelProperty(value = "编制类型")
    private String establishmentType; //编制类型


}
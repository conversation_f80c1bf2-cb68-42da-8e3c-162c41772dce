package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "hrms_interview_management")
@Setter
@Getter
public class HrmsInterviewManagement {
	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "interview_management_id")
	@ApiModelProperty(value = "主键ID")
	private String interviewManagementId;

	/**
	 * 员工姓名
	 */
	@Column(name = "employee_name")
	@ApiModelProperty(value = "员工姓名")
	private String employeeName;

	/**
	 * 应聘岗位
	 */
	@Column(name = "apply_post")
	@ApiModelProperty(value = "应聘岗位")
	private String applyPost;

	/**
	 * 联系电话
	 */
	@Column(name = "telephone_number")
	@ApiModelProperty(value = "联系电话")
	private String telephoneNumber;

	/**
	 * 邮箱
	 */
	@ApiModelProperty(value = "邮箱")
	private String email;

	/**
	 * 面试负责人ID
	 */
	@Column(name = "interview_principal_id")
	@ApiModelProperty(value = "面试负责人ID")
	private String interviewPrincipalId;
	
	/**
	 * 面试负责人姓名
	 */
	@Column(name = "interview_principal_name")
	@ApiModelProperty(value = "面试负责人姓名")
	private String interviewPrincipalName;

	/**
	 * 通知方式
	 */
	@Column(name = "notice_way")
	@ApiModelProperty(value = "通知方式")
	private String noticeWay;

	/**
	 * 通知状态: 1=已通知; 2=未通知
	 */
	@Column(name = "notice_status")
	@ApiModelProperty(value = "通知状态: 1=已通知; 2=未通知")
	private String noticeStatus;

	/**
	 * 面试时间
	 */
	@Column(name = "interview_time")
	@ApiModelProperty(value = "面试时间")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date interviewTime;

	/**
	 * 面试地点
	 */
	@Column(name = "interview_place")
	@ApiModelProperty(value = "面试地点")
	private String interviewPlace;

	/**
	 * 联系人
	 */
	@Column(name = "contarct_person")
	@ApiModelProperty(value = "联系人")
	private String contarctPerson;

	/**
	 * 联系人电话
	 */
	@Column(name = "contarct_person_telephone")
	@ApiModelProperty(value = "联系人电话")
	private String contarctPersonTelephone;

	/**
	 * 面试状态
	 */
	@Column(name = "interview_status")
	@ApiModelProperty(value = "面试状态")
	private String interviewStatus;
	
	/**
	 * 评价状态(1:未评价 2:已评价)
	 */
	@Column(name = "evaluation_status")
	@ApiModelProperty(value = "评价状态(1:未评价 2:已评价)")
	private Integer evaluationStatus;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 简历库ID
	 */
	@Column(name = "resume_storehouse_id")
	@ApiModelProperty(value = "简历库ID")
	private String resumeStorehouseId;
	
	/**
	 * 面试状态文本值
	 */
	@Transient
	@ApiModelProperty(value = "面试状态文本值")
	private String interviewStatusText;
	
	/**
	 * 评价状态文本值
	 */
	@Transient
	@ApiModelProperty(value = "评价状态文本值")
	private String evaluationStatusText;
	
	//////////// 导出 /////////////
	/**
	 * 面试时间
	 */
	@Transient
	private String interviewTimeExport;

}
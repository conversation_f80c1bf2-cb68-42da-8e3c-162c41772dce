package cn.trasen.hrms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 人事事件定时任务
 * <AUTHOR>
 *
 */
@Table(name = "hrms_personnel_execution")
@Setter
@Getter
public class HrmsPersonnelExecution {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 员工Id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工Id")
    private String employeeId;
    

    /**
     * 老员工状态
     */
    @Column(name = "old_employee_status")
    @ApiModelProperty(value = "老员工状态")
    private String oldEmployeeStatus;

    /**
     * 新员工状态
     */
    @Column(name = "new_employee_status")
    @ApiModelProperty(value = "新员工状态")
    private String newEmployeeStatus;

    /**
     * 要执行的时间
     */
    @Column(name = "execution_time")
    @ApiModelProperty(value = "要执行的时间")
    private String executionTime;
    
    /**
     * 实际执行时间
     */
    @Column(name = "occurrence_time")
    @ApiModelProperty(value = "实际执行时间")
    private Date occurrenceTime;
    
    /**
     * 是否已执行0:未执行 1已执行
     */
    @Column(name = "stop_event")
    @ApiModelProperty(value = "是否已执行")
    private String stopEvent;
    
    /**
     * 任务描述
     */
    @Column(name = "remark")
    @ApiModelProperty(value = "任务描述")
    private String remark;
    
    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

}
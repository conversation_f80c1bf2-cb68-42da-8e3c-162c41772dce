package cn.trasen.hrms.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@Table(name = "hrms_education_info")
@Setter
@Getter
public class HrmsEducationInfo {

	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "id")
	@ApiModelProperty(value = "主键ID")
	private String id;

	/**
	 * 员工ID
	 */
	@Column(name = "employee_id")
	@ApiModelProperty(value = "员工ID")
	private String employeeId;
	
	/**
	 * 员工编码
	 */
	@Excel(name = "人员工号")
	@Column(name = "employee_no")
	@ApiModelProperty(value = "员工编码")
	private String employeeNo;
	
	/**
	 * 员工姓名
	 */
	@Excel(name = "人员姓名")
	@Column(name = "employee_name")
	@ApiModelProperty(value = "员工姓名")
	private String employeeName;
	

	/**
	 * 毕业学校
	 */
	@Excel(name = "毕业学校")
	@Column(name = "school_name")
	@ApiModelProperty(value = "毕业学校")
	private String schoolName;

	/**
	 * 学历类型
	 */
	@Excel(name = "学历类型")
	@Column(name = "education_type")
	@ApiModelProperty(value = "学历类型")
	private String educationType;

	/**
	 * 学历最高等级: 1=是; 2=否;
	 */
	@Excel(name = "是否最高学历")
	@Column(name = "highest_level")
	@ApiModelProperty(value = "最高等级: 1=是; 2=否;")
	private String highestLevel;

	/**
	 * 证书编号
	 */
	@Excel(name = "学历编号")
	@Column(name = "certificate_number")
	@ApiModelProperty(value = "证书编号")
	private String certificateNumber;

	/**
	 * 所学专业
	 */
	@Excel(name = "专业名称")
	@Column(name = "professional")
	@ApiModelProperty(value = "所学专业")
	private String professional;

	/**
	 * 学习方式
	 */
	
	@Excel(name = "学习形式")
	@Column(name = "learn_way")
	@ApiModelProperty(value = "学习方式")
	private String learnWay;

	/**
	 * 学制
	 */
	@Excel(name = "学制")
	@Column(name = "school_system")
	@ApiModelProperty(value = "学制")
	private String schoolSystem;

	/**
	 * 学位
	 */
	@Excel(name = "学位名称")
	@Column(name = "degree")
	@ApiModelProperty(value = "学位")
	private String degree;

	/**
	 * 学位编号
	 */
	@Excel(name = "学位编号")
	@Column(name = "degree_number")
	@ApiModelProperty(value = "学位编号")
	private String degreeNumber;

	/**
	 * 开始日期
	 */
	@Column(name = "start_time")
	@ApiModelProperty(value = "开始日期")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date startTime;

	/**
	 * 结束日期
	 */
	@Column(name = "end_time")
	@ApiModelProperty(value = "结束日期")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date endTime;

	/**
	 * 获得证书日期
	 */
	@Column(name = "acquisition_date")
	@ApiModelProperty(value = "获得证书日期")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date acquisitionDate;

	/**
	 * 证明人
	 */
	@Column(name = "witness")
	@ApiModelProperty(value = "证明人")
	private String witness;

	/**
	 * 证明人电话
	 */
	@Column(name = "witness_phone")
	@ApiModelProperty(value = "证明人电话")
	private String witnessPhone;

	/**
	 * 备注
	 */
	@Column(name = "remark")
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;
	
	/**
     * 审批状态: 1=未审批; 2=审批中; 3=已退回; 4=已审批
     */
    @Column(name = "approval_status")
    @ApiModelProperty(value = "审批状态: 1=未审批; 2=审批中; 3=已退回; 4=已审批")
	private String approvalStatus;

	// ------- 扩展字段 ------- //
	/**
	 * 学历类型文本值
	 */
	@Transient
	@ApiModelProperty(value = "学历类型文本值")
	private String educationTypeText;

	/**
	 * 学习方式文本值
	 */
	@Transient
	@ApiModelProperty(value = "学习方式文本值")
	private String learnWayText;

	/**
	 * 学制文本值
	 */
	@Transient
	@ApiModelProperty(value = "学制文本值")
	private String schoolSystemText;
	
	@ApiModelProperty(value = "附件ID")
	@Column(name = "business_id")
	private String businessId;

	@ApiModelProperty(value = "学历附件")
	@Column(name = "xlfj")
	private String xlfj;
	
	@Excel(name = "学位类型")
    @Column(name = "education_degree")
    @ApiModelProperty(value = "学位类型")
	private String educationDegree;
	
  
    
    @Excel(name = "入学时间")
    @Transient
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startTimeStr;
   
	@Excel(name = "毕业时间")
    @Transient
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endTimeStr;
	
	@Transient
	private String htOrgIdList;
	
    
}
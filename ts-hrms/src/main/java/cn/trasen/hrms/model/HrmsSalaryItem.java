package cn.trasen.hrms.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 考勤项目
 *
 */
@Table(name = "hrms_salary_item")
@Setter
@Getter
public class HrmsSalaryItem {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	@Id
	@Column(name = "salary_item_id")
	@ApiModelProperty(value = "主键ID名称")
	private String salaryItemId;

	/**
	 * 项目名称
	 */
	@Column(name = "salary_item_name")
	@ApiModelProperty(value = "项目名称")
	private String salaryItemName;

	/**
	 * 项目编码
	 */
	@Column(name = "salary_item_code")
	@ApiModelProperty(value = "项目编码")
	private String salaryItemCode;

	/**
	 * 数据类别: 1=薪酬项目; 2=考勤项目
	 */
	@Column(name = "data_category")
	@ApiModelProperty(value = "数据类别: 1=薪酬项目; 2=考勤项目")
	private String dataCategory;

	/**
	 * 项目类型
	 */
	@Column(name = "salary_item_type")
	@ApiModelProperty(value = "项目类型")
	private String salaryItemType;

	/**
	 * 计算类型
	 */
	@Column(name = "count_type")
	@ApiModelProperty(value = "计算类型")
	private String countType;

	/**
	 * 计算公式
	 */
	@Column(name = "count_formula")
	@ApiModelProperty(value = "计算公式")
	private String countFormula;

	/**
	 * 计算公式文本显示
	 */
	@Column(name = "count_formula_text")
	@ApiModelProperty(value = "计算公式文本显示")
	private String countFormulaText;

	/**
	 * 金额
	 */
	@Column(name = "salary_item_amount")
	@ApiModelProperty(value = "金额")
	private BigDecimal salaryItemAmount;

	/**
	 * 是否实发: 1=是; 2=否;
	 */
	@Column(name = "is_real")
	@ApiModelProperty(value = "是否实发: 1=是; 2=否;")
	private String isReal;

	/**
	 * 排序号
	 */
	@Column(name = "serial_number")
	@ApiModelProperty(value = "排序号")
	private Integer serialNumber;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 主键ID(前端多选用)
	 */
	@Transient
	@ApiModelProperty(value = "主键ID(前端多选用)")
	private String id;
	
	/**
	 * 薪酬项目类型文本值
	 */
	@Transient
	@ApiModelProperty(value = "薪酬项目类型文本值")
	private String salaryItemTypeText;

	/**
	 * 计算类型文本值
	 */
	@Transient
	@ApiModelProperty(value = "计算类型文本值")
	private String countTypeText;
	
	/**
	 * 考勤类型 1特殊出勤2请假3全勤
	 */
	@Column(name = "attendance_type")
	@ApiModelProperty(value = "考勤类型")
	private String attendanceType;

}
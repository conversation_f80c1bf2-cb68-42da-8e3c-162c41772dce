package cn.trasen.hrms.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**    
  * <P> @Description: 考勤员被考勤人员关系表实体</p>
  * <P> @Date: 2020年8月28日  下午3:30:31 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
 
@Table(name = "hrms_attendance_timekeeper")
@Setter
@Getter
public class HrmsTimekeeper {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    @Id
    @ApiModelProperty(value = "id")
    @Column(name = "id")
    private String id;
    
    @ApiModelProperty(value = "考勤员ID")
    @Column(name = "timekeeper_id")
    private String timekeeperId;
    
    
    @ApiModelProperty(value = "被考勤人员ID")
    @Column(name = "employee_id")
    private String employeeId;
    
    @ApiModelProperty(value = "被考勤人员名字")
    @Column(name = "employee_name")
    private String employeeName;
	
	
	   /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    @Transient
    private String timekeeperName;
    
    @Transient
    private String timekeeperNo;
    @Transient
    private String name;
    
    @Transient
    private String byEmployeeNo;  //被考勤人工号
    @Transient
    private String byEmployeeName; //被考勤人名字
    @Transient
    private String byOrgName;	//被考勤人科室
    
    @Transient
    private String timeEmployeeNo;  //考勤人工号
    @Transient
    private String timeEmployeeName; //考勤人名字
    @Transient
    private String timeOrgName;	//考勤人科室
    
    @Transient
    private String employeeNo;	//查询的工号

    @ApiModelProperty("考勤上报1.科室上报")
    private Integer uploadType;
}

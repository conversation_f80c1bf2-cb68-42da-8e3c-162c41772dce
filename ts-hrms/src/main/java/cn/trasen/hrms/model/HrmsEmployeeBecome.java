package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

/**
 * 转正管理
 *
 */
@Table(name = "hrms_employee_become")
@Setter
@Getter
public class HrmsEmployeeBecome {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;


    @Id
    private String id;

    /**
     * 员工姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    /**
     * 员工编码
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "员工编码")
    private String employeeNo;
    
    
    @Column(name = "apply_dept_id")
    @ApiModelProperty(value = "员工部门编码")
    private String applyDeptId;
    
    
    @Column(name = "apply_dept_name")
    @ApiModelProperty(value = "员工部门名称")
    private String applyDeptName;

    /**
     * 岗位
     */
    @Column(name = "personal_identity_name")
    @ApiModelProperty(value = "岗位")
    private String personalIdentityName;

    /**
     * 入职时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "entry_date")
    @ApiModelProperty(value = "入职时间")
    private Date entryDate;

    /**
     * 职务
     */
    @Column(name = "position_name")
    @ApiModelProperty(value = "职务")
    private String positionName;

    /**
     * 转正日期
     */
    @Column(name = "become_date")
    @ApiModelProperty(value = "转正日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date becomeDate;
    
    
    
    @Column(name = "business_id")
    @ApiModelProperty(value = "附件id")
    private String businessId;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;
    

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;
    
    @Column(name = "education_type_name")
    @ApiModelProperty(value = "最高学历")
    private String educationTypeName;
    
    
    @Column(name = "workflow_id")
    @ApiModelProperty(value = "流程id")
    private String workflowId;
    
    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;
    
    @Transient
    @ApiModelProperty(value = "查询状态 1查询待转正 2 查询已转正 3 离职人员")
    private Integer status;
    
    @Transient
    @ApiModelProperty(value = "入职开始时间:用于查询")
    private String entryDateStartTime;
    @Transient
    @ApiModelProperty(value = "入职结束时间:用于查询")
    private String entryDateEndTime;
    
    
    @Transient
    @ApiModelProperty(value = "转正开始时间:用于查询")
    private String becomeStartTime;
    @Transient
    @ApiModelProperty(value = "转正结束时间:用于查询")
    private String becomeEndTime;
    
    @Transient
    @ApiModelProperty(value = "最高学历字典值")
    private String educationType;
    
    @Transient
    @ApiModelProperty(value = "预计转正日期")
    private String estimateDate;
    
    @Transient
    @ApiModelProperty(value = "转正倒计时天数")
    private String countDownDays;
    
    @Column(name = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @Column(name = "change_status")
    @ApiModelProperty(value = "")
    private String changeStatus;
    
    @Transient
    private Date positiveTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "用开始日期")
    private Date syksrq; //试用开始日期

    @Transient
    private String identityNumber;
    
    
}
package cn.trasen.hrms.model;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**    
  * <P> @Description: 快捷菜单bean</p>
  * <P> @Date: 2020年6月29日  下午2:45:28 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 

@Table(name = "hrms_commonly_menu")
@Setter
@Getter
public class HrmsCommonMenu {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;


    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    @Column(name = "user_id")
    @ApiModelProperty(value = "用户ID")
    private String userId;
    
    @Column(name = "menu_name")
    @ApiModelProperty(value = "菜单名称")
    private String menuName;
    
    @Column(name = "menu_url")
    @ApiModelProperty(value = "菜单地址")
    private String menuUrl;
    
    @Column(name = "menu_icon")
    @ApiModelProperty(value = "icon")
    private String menuIcon;
    
    @Column(name = "sequence")
    @ApiModelProperty(value = "排序号")
    private String sequence;
    
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;
    
}

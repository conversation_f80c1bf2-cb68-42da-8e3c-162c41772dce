package cn.trasen.hrms.model;

import cn.trasen.homs.core.enums.EnableEnum;
import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

@Table(name = "hrms_salary_level")
@Setter
@Getter
public class HrmsSalaryLevel {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "salary_level_id")
    @ApiModelProperty(value = "主键ID")
    private String salaryLevelId;

    /**
     * 薪级名称
     */
    @Column(name = "salary_level_name")
    @ApiModelProperty(value = "薪级名称")
    private String salaryLevelName;

    /**
     * 薪级类别
     */
    @Column(name = "salary_level_category")
    @ApiModelProperty(value = "薪级类别")
    private String salaryLevelCategory;

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    private String grade;

    /**
     * 是否启用: 1=是; 2=否;
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String isEnable;

    /**
     * 是否启用:
     */
    @Transient
    @ApiModelProperty(value = "是否启用文字描述")
    private String isEnableLable;


    /**
     * 上级薪级等级id
     */
    @Column(name = "parent_level_id")
    @ApiModelProperty(value = "上级薪级等级id")
    private String parentLevelId;

    /**
     * 上级薪级等级名称
     */
    @Transient
    @ApiModelProperty(value = "上级薪级等级名称")
    private String parentLevelName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    // ------- 扩展字段 ------- //
    /**
     * 薪级类别名称
     */
    @Transient
    @ApiModelProperty(value = "薪级类别名称")
    private String salaryLevelCategoryName;

    public String getIsEnableLable(){
        if(StringUtils.isNotBlank(this.isEnable)){
            return EnableEnum.getValByKey(this.isEnable);
        }
        return null;
    }

    /**
     * 政策标准id
     */
    @Transient
    @ApiModelProperty(value = "政策标准id")
    private String policyStandardId;
}
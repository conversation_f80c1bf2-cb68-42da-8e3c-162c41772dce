package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_scheduling_manage")
@Setter
@Getter
public class HrmsSchedulingManage {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;


    /**
     * 主键
     */
    @Id
    @Column(name = "scheduling_id")
    @ApiModelProperty(value = "主键")
    private String schedulingId;

    /**
     * 排班日期
     */
    @Column(name = "scheduling_date")
    @ApiModelProperty(value = "排班日期")
    private String schedulingDate;

    /**
     * 人员id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "人员id")
    private String employeeId;
    

    /**
     * 班次id
     */
    @Column(name = "frequency_id")
    @ApiModelProperty(value = "班次id")
    private String frequencyId;
    

    @Transient
    @ApiModelProperty(value = "班次名称")
    private String frequencyName;
    
    @Transient
    @ApiModelProperty(value = "颜色")
    private String frequencyColour;
    
    /**
     * 人员所在科室
     */
    @Column(name = "emp_org_id")
    @ApiModelProperty(value = "人员所在科室")
    private String empOrgId;

    /**
     * 人员所在考勤组id
     */
    @Column(name = "frequency_grouping_id")
    @ApiModelProperty(value = "人员所在考勤组id")
    private String frequencyGroupingId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    @Transient
    private List<String> employeeIds;  //员工id
    
    @Transient
    private String startDate;  //开始时间
    
    @Transient 
    private String endDate;  //结束时间
  
    @Transient
    private String identityNumber;  //身份证号码
    
    @Transient
    private Double dayLong;  //考勤天数
    
    @Transient
    private Integer dayHours;  //考勤时长
    
    @Transient
    private String employeeName;  
    
    @Transient
    private String orgName;  
    
    @Transient
    private String employeeNo;
    
    @Transient
    private String isDetails;  //2 统计看明细
    
    @Transient
    private String dayRest; //休息天数
    
    @Transient
    private String dayFalse; //假勤天数
    
    @Transient
    private String frequencyType; //排班类型
   
    @Transient
    private String frequencyTime;  //考勤时间
    
    
    @Column(name = "sync")
    @ApiModelProperty(value = "同步来的数据")
    private String sync;  //1 同步来的数据 
    
    @Transient
    private String jjrType;  //节假日加班统计类型
    
    @Transient
    private String ksfbNo;  //科室排序号
    
    
    @Transient
    private String sort;  //科室排序号
    
    @Transient
    private String  condition;  // 1周 2 月
    @Transient
    private String empOrgName;
    @Transient
    private String orgAttributes;
   
    
    @Transient
    private List<String> frequencyNames;
    
    @Transient
    private List<String> days;
    
}
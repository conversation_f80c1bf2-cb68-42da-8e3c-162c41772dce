package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_blacklist")
@Setter
@Getter
public class HrmsBlacklist {

	@Column(name = "sso_org_code")
	private String ssoOrgCode;
	/**
	 * 主键
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@ApiModelProperty(value = "主键")
	private String id;

	/**
	 * 姓名
	 */
	@Column(name = "employee_name")
	@ApiModelProperty(value = "姓名")
	private String employeeName;

	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别")
	private String gender;

	/**
	 * 身份证号码
	 */
	@Column(name = "identity_number")
	@ApiModelProperty(value = "身份证号码")
	private String identityNumber;

	/**
	 * 添加原因
	 */
	@Column(name = "add_reason")
	@ApiModelProperty(value = "添加原因")
	private String addReason;

	/**
	 * 创建日期
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建日期")
	private Date createDate;

	/**
	 * 创建人ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建人ID")
	private String createUser;

	/**
	 * 创建人姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建人姓名")
	private String createUserName;

	/**
	 * 更新日期
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新日期")
	private Date updateDate;

	/**
	 * 更新人ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新人ID")
	private String updateUser;

	/**
	 * 更新人姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新人姓名")
	private String updateUserName;

	/**
	 * 是否删除
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "是否删除")
	private String isDeleted;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	// ------- 扩展字段 ------- //
	/**
	 * 性别文本值
	 */
	@Transient
	@ApiModelProperty(value = "性别文本值")
	private String genderText;
}
package cn.trasen.hrms.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**    
  * <P> @Description: 执业情况</p>
  * <P> @Date: 2021年3月9日  下午2:19:51 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
@Table(name = "hrms_doctor_certified")
@Setter
@Getter    
public class HrmsDoctorCertified {

	@Column(name = "sso_org_code")
	private String ssoOrgCode;
    /**
     * 主键ID
     */
    @Id
    @Column(name = "doctor_certified_id")
    @ApiModelProperty(value = "主键ID")
    private String doctorCertifiedId;

    /**
     * 员工id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工id")
    private String employeeId;
    
    /**
     * 首次注册时间
     */
    @Column(name = "register_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "首次注册时间")
    private Date registerDate;
	
    
    /**
     * 注册机构
     */
    @Column(name = "register_org")
    @ApiModelProperty(value = "注册机构")
    private String registerOrg;
	
    
    /**
     * 执业范围
     */
    @Column(name = "register_scope")
    @ApiModelProperty(value = "执业范围")
    private String registerScope;
	
    
    /**
     * 证书编号
     */
    @Column(name = "register_number")
    @ApiModelProperty(value = "证书编号")
    private String registerNumber;
	
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	@Column(name = "remark")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;
}

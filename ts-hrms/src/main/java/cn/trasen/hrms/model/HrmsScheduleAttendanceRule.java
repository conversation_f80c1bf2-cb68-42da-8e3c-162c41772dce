package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 
  * <P> @Description: 考勤设置-排班考勤规则</p>
  * <P> @Date: 2020年3月26日  下午4:48:38 </p>
  * <P> @Author: zh </p>
  * <P> @Company: 湖南爱笑恩信息科技有限公司 </p>
  * <P> @version V1.0    </p>
 */
@Table(name = "hrms_schedule_attendance_rule")
@Setter
@Getter
public class HrmsScheduleAttendanceRule {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 适用科室
     */
    @Column(name = "suit_department")
    @ApiModelProperty(value = "适用科室")
    private String suitDepartment;

    /**
     * 班次名称
     */
    @Column(name = "shift_name")
    @ApiModelProperty(value = "班次名称")
    private String shiftName;

    /**
     * 考勤开始时间
     */
    @Column(name = "attendance_start_time")
    @ApiModelProperty(value = "考勤开始时间")
    private Date attendanceStartTime;

    /**
     * 考勤结束时间
     */
    @Column(name = "attendance_end_time")
    @ApiModelProperty(value = "考勤结束时间")
    private Date attendanceEndTime;

    /**
     * 考勤地点
     */
    @Column(name = "attendance_place")
    @ApiModelProperty(value = "考勤地点")
    private String attendancePlace;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String latitude;

    /**
     * 允许偏差范围
     */
    @ApiModelProperty(value = "允许偏差范围")
    private String deviation;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人ID")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 科室ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "科室ID")
    private String orgId;

    /**
     * 科室名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "科室名称")
    private String orgName;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;
}
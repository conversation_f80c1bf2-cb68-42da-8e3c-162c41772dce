package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_document_accessory")
@Setter
@Getter
public class HrmsDocumentAccessory {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键id
     */
    @Id
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 业务id
     */
    @Column(name = "business_id")
    @ApiModelProperty(value = "业务id")
    private String businessId;


    /**
     * 附件服务ID
     */
    @Column(name = "file_id")
    @ApiModelProperty(value = "附件服务ID")
    private String fileId;
    
    /**
     * 文件大小
     */
    @Column(name = "file_size")
    @ApiModelProperty(value = "文件大小")
    private String fileSize;
    


    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    @Column(name = "file_name")
    private String fileName;

    /**
     * 附件原始名称
     */
    @Column(name = "file_real_name")
    @ApiModelProperty(value = "附件原始名称")
    private String fileRealName;
    
    /**
     * 附件路径
     */
    @Column(name = "file_path")
    @ApiModelProperty(value = "附件路径")
    private String filePath;
    
    /**
     * 解除附件 1：解除
     */
    @Column(name = "relieve")
    @ApiModelProperty(value = "解除附件 1：解除")
    private String relieve;
    
    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 创建人部门编号
     */
    @Column(name = "create_dept")
    @ApiModelProperty(value = "创建人部门编号")
    private String createDept;

    /**
     * 创建人部门名称
     */
    @Column(name = "create_dept_name")
    @ApiModelProperty(value = "创建人部门名称")
    private String createDeptName;

    /**
     * 创建时间(发布时间)
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间(发布时间)")
    private Date createDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "org_code")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;


}
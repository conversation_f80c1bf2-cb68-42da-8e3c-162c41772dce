package cn.trasen.hrms.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@Table(name = "hrms_out_record_xx")
@Setter
@Getter
public class HrmsOutRecordXx {
    /**
     * 主键
     */
	@Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 省内或省外
     */
    @Column(name = "province_type")
    @ApiModelProperty(value = "省内或省外")
    private String provinceType;

    /**
     * 申请时间
     */
    @Column(name = "apply_time")
    @ApiModelProperty(value = "申请时间")
    private String applyTime;

    /**
     * 外出类型
     */
    @Column(name = "out_type")
    @ApiModelProperty(value = "外出类型")
    private String outType;

    /**
     * 岗位类别
     */
    @ApiModelProperty(value = "岗位类别")
    private String jobs;

    /**
     * 外出开始时间
     */
    @Column(name = "start_time")
    @ApiModelProperty(value = "外出开始时间")
    private String startTime;

    /**
     * 外出结束时间
     */
    @Column(name = "end_time")
    @ApiModelProperty(value = "外出结束时间")
    private String endTime;

    /**
     * 外出天数
     */
    @Column(name = "out_days")
    @ApiModelProperty(value = "外出天数")
    private String outDays;

    /**
     * 外出地点
     */
    @Column(name = "out_address")
    @ApiModelProperty(value = "外出地点")
    private String outAddress;

    /**
     * 外出事由
     */
    @Column(name = "out_remark")
    @ApiModelProperty(value = "外出事由")
    private String outRemark;

    /**
     * 附件id
     */
    @Column(name = "file_id")
    @ApiModelProperty(value = "附件id")
    private String fileId;

    /**
     * 附件名称
     */
    @Column(name = "file_name")
    @ApiModelProperty(value = "附件名称")
    private String fileName;

    /**
     * 附件路径
     */
    @Column(name = "file_path")
    @ApiModelProperty(value = "附件路径")
    private String filePath;

    /**
     * 申请人
     */
    @Column(name = "apply_user")
    @ApiModelProperty(value = "申请人编码")
    private String applyUser;
    
    /**
     * 申请人
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "申请人id")
    private String employeeId;
    

    /**
     * 申请人名称
     */
    @Column(name = "apply_user_name")
    @ApiModelProperty(value = "申请人名称")
    private String applyUserName;

    /**
     * 申请人部门
     */
    @Column(name = "apply_dept")
    @ApiModelProperty(value = "申请人部门")
    private String applyDept;

    /**
     * 申请人部门名称
     */
    @Column(name = "apply_dept_name")
    @ApiModelProperty(value = "申请人部门名称")
    private String applyDeptName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 流程id
     */
    @Column(name = "work_id")
    @ApiModelProperty(value = "流程业务id")
    private String workId;
    
    @Column(name = "status")
    @ApiModelProperty(value = "状态  1正常  2取消")
    private String status;
    
    @Transient
    private String endStartTime;  //外出 结束  开始时间
    @Transient
    private String endEndTime;  //外出结束  结束时间
    
    @Column(name = "real_start_time")
    @ApiModelProperty(value = "实际外出开始时间")
    private String realStartTime;
    
    @Column(name = "real_end_time")
    @ApiModelProperty(value = "实际外出结束时间")
    private String realEndTime;
    
    @Transient
    private String roleCode;
}
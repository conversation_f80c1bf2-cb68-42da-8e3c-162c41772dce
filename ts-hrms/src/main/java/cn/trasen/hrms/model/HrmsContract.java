package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import org.jeecgframework.poi.excel.annotation.Excel;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

/**
 * 员工合同
 *
 */
@Table(name = "hrms_contract")
@Setter
@Getter
public class HrmsContract {



    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键
     */
    @Id
    @Column(name = "contract_id")
    @ApiModelProperty(value = "主键")
    private String contractId;

    /**
     * 员工id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工id")
    private String employeeId;
    
    /**
     * 员工工号
     */
    @Excel(name = "员工工号")
    @Column(name = "employee_no")
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;
    
    /**
     * 员工名字
     */
    @Excel(name = "员工名字")
    @Column(name = "employee_name")
    @ApiModelProperty(value = "员工名字")
    private String employeeName;
    
    /**
     * 身份证
     */
    @Column(name = "card")
    @ApiModelProperty(value = "身份证")
    private String card;

    /**
     * 合同编号
     */
    @Excel(name = "合同编号")
    @Column(name = "contract_number")
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同类型
     */
    @Column(name = "contract_type")
    @ApiModelProperty(value = "合同类型")
    private String contractType;
    
    /**
     * 合同类型名称
     */
    @Excel(name = "合同类型")
    @Column(name = "contract_type_name")
    @ApiModelProperty(value = "合同类型名称")
    private String contractTypeName;

    /**
     * 合同签订日期
     */
    @Column(name = "sign_time")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "合同签订日期")
    private Date signTime;
    
    @Excel(name = "合同签订日期")
    @ApiModelProperty(value = "合同签订日期(导入导出)")
    @Transient
    private String signTimeStr;
    
    
    /**
     * 合同签订状态
     */
    
    @Column(name = "sign_type")
    @ApiModelProperty(value = "合同签订状态 1:已签订、2:已续签、3:已到期、4:已解除、5:已删除")
    private String signType;
  
    @Excel(name = "签订状态")
    @Transient
    @ApiModelProperty(value = "合同签订状态中文值  1:已签订、2:已续签、3:已到期、4:已解除、5:已删除")
    private String signTypeName;

    /**
     * 合同期限: 1=有限; 2=无限;
     */
    @Excel(name = "合同期限")
    @Column(name = "alloted_time")
    @ApiModelProperty(value = "合同期限: 1=有限; 2=无限;")
    private String allotedTime;

    /**
     * 期限时间
     */
    @Excel(name = "期限时间")
    @Column(name = "alloted_date")
    @ApiModelProperty(value = "期限时间")
    private String allotedDate;

    /**
     * 期限类型（Y 年 M 月 D 天）
     */
    @Excel(name = "期限单位")
    @Column(name = "alloted_type")
    @ApiModelProperty(value = "期限类型（Y 年 M 月 D 天）")
    private String allotedType;

    /**
     * 合同开始日期
     */
   
    @Column(name = "start_time")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "合同开始日期")
    private Date startTime;

    @Excel(name = "合同开始日期")
    @Transient
    private String startTimeStr;
    
    
    /**
     * 合同结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "end_time")
    @ApiModelProperty(value = "合同结束日期")
    private Date endTime;

    @Excel(name = "合同结束日期")
    @ApiModelProperty(value = "合同结束日期(导入导出)")
    @Transient
    private String endTimeStr;
    
    
    /**
     * 合同签订次数
     */
    @Excel(name = "合同签订次数")
    @ApiModelProperty(value = "合同签订次数")
    @Column(name = "frequency")
    private String frequency;

    /**
     * 约定岗位
     */
    @Column(name = "contrac_jobs")
    @ApiModelProperty(value = "约定岗位")
    private String contracJobs;
    
    /**
     * 约定岗位
     */
    @Excel(name = "约定岗位")
    @Column(name = "contrac_jobs_name")
    @ApiModelProperty(value = "约定名称")
    private String contracJobsName;

    /**
     * 存档地址
     */
    
    
    @Excel(name = "存档地")
    @Column(name = "archive_site")
    @ApiModelProperty(value = "存档地址")
    private String archiveSite;
    
    @Excel(name = "是否提醒")
    @Column(name = "remind")
    @ApiModelProperty(value = "到期提醒1:提醒 、2：不提醒")
    private String remind;
    ;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    
    
    /**
     * 合同解除日期
     */
    @Column(name = "relieve_date")
    @ApiModelProperty(value = "合同解除日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date relieveDate;
    
    /**
     * 合同解除日期
     */
    @Column(name = "relieve_explain")
    @ApiModelProperty(value = "解除合同原因")
    private String relieveExplain;
    
    
    
    /**
     * 员工所属科室id
     */
    @Column(name = "emp_org_id")
    @ApiModelProperty(value = "员工所属科室id")
    private String empOrgId;
    
    /**
     * 组织机构名称
     */
    @Column(name = "emp_org_name")
    @ApiModelProperty(value = "员工所属科室名称")
    private String empOrgName;
    

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    /**
     * 编辑类型
     */
    @ApiModelProperty(value = "1:签订、2:编辑、3：续签、4：解除、5：删除")
    @Transient
    private String editType;
    
    /**
     * 原合同号
     */
    @ApiModelProperty(value = "原合同号")
    @Transient
    private String beforeContractNumber;
    
    
    /**
     * 员工状态
     */
    @ApiModelProperty(value = "员工状态")
    @Transient
    private String employeeStatus;
    
    
    @ApiModelProperty(value = "员工状态名称")
    @Transient
    private String employeeStatusName;
    
    /**
     * 入职日期
     */
    @ApiModelProperty(value = "入职日期")
    @Transient
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entryDate;
    
    @Transient
    private String entryDateStr;   //导出用的字段
	
    @ApiModelProperty(value = "入职开始时间（查询）")
    @Transient
	private String entryDateStart;  
    
    @ApiModelProperty(value = "入职结束时间（查询）")
	@Transient
	private String entryDateEnd;  
    
    @ApiModelProperty(value = "即将到期时间（查询）")
	@Transient
	private String searchTime;  
    
    //查询条件
    @Transient
    private List<String> empIdList;
    
    @ApiModelProperty(value = "附件List")
    @Transient
    private List<HrmsDocumentAccessory> accessoryList;
  
    @Transient
    private List<HrmsContract> contract;
    
	@Transient
	private int no;  //导出序号
	
	@ApiModelProperty(value = "到期倒计时")
	@Transient
	private String countDown;  //到期倒计时
	
	@ApiModelProperty(value = "编制类型")
	@Transient
	private String establishmentType;  //编制类型
	
	
	@ApiModelProperty(value = "签订开始时间（查询）")
	@Transient
	private String signTimeStart;  
	
	@ApiModelProperty(value = "签订结束时间（查询）")
	@Transient
	private String signTimeEnd;  
	
	@ApiModelProperty(value = "合同结束开始时间（查询）")
	@Transient
	private String signTimeStartStr;  
	
	@ApiModelProperty(value = "合同结束结束时间（查询）")
	@Transient
	private String signTimeEndStr;
	
	@ApiModelProperty(value = "合同剩余天数")
	@Transient
	private String remainingDays;
	
	@Transient
	private String gender;
	
	@Transient
	private String phoneNumber;
	
	@Transient
	private String startExpireDate;  //下个季度到期
	
	@Transient
	private String endExpireDate;  //下个季度到期
	
	@Transient
	private String  isJk;  //经开医院排除8开头的账号
	
}
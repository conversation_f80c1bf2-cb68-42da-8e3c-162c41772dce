package cn.trasen.hrms.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import org.jeecgframework.poi.excel.annotation.Excel;
import javax.persistence.Transient;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**    
  * <P> @Description: 电子工资条</p>
  * <P> @Date: 2020年7月16日  下午2:48:04 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 

@Table(name = "hrms_electronics_salary_sheet")
@Setter
@Getter   
public class HrmsElectronicsSalarySheet {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;


	@Id
	@Column(name = "id")
	@ApiModelProperty(value = "主键ID")
	private  String id;
	
	@Column(name = "send_month")
	@ApiModelProperty(value = "发放月份")
	private String sendMonth;
	
	@Excel(name = "科室")
	@Column(name = "dept_name")
	@ApiModelProperty(value = "科室")
	private String deptName;
	
	@Excel(name = "职员代码")
	@Column(name = "sheet_zydm")
	@ApiModelProperty(value = "职员代码")
	private String sheetZydm;
	
	@Excel(name = "职员姓名")
	@Column(name = "sheet_empname")
	@ApiModelProperty(value = "职员姓名")
	private String sheetEmpname;
	
	@Excel(name = "身份证号码")
	@Column(name = "sheet_sfzhm")
	@ApiModelProperty(value = "身份证号码")
	private String sheetSfzhm;
	
	@Excel(name = "建行职工账号")
	@Column(name = "sheet_zgjhzh")
	@ApiModelProperty(value = "建行职工账号")
	private String sheetZgjhzh;
	
	@Excel(name = "基本工资")
	@Column(name = "sheet_jbgz")
	@ApiModelProperty(value = "基本工资")
	private Double sheetJbgz;
	
	@Excel(name = "活动工资")
	@Column(name = "sheet_hdgz")
	@ApiModelProperty(value = "活动工资")
	private Double sheetHdgz;
	
	@Excel(name = "月绩效工资核减额")
	@Column(name = "sheet_yjxgzhje")
	@ApiModelProperty(value = "月绩效工资核减额")
	private Double sheetYjxgzhje;

	@Excel(name = "护士工资")
	@Column(name = "sheet_hsgz")
	@ApiModelProperty(value = "护士工资")
	private Double sheetHsgz;
	
	@Excel(name = "地方补贴")
	@Column(name = "sheet_dfbt")
	@ApiModelProperty(value = "地方补贴")
	private Double sheetDfbt;
	
	@Excel(name = "护士津贴")
	@Column(name = "sheet_hsjt")
	@ApiModelProperty(value = "护士津贴")
	private Double sheetHsjt;
	
	
	@Excel(name = "卫生津贴")	
	@Column(name = "sheet_wsjt")
	@ApiModelProperty(value = "卫生津贴")
	private Double sheetWsjt;
	
	@Excel(name = "适当补贴")	
	@Column(name = "sheet_sdbt")
	@ApiModelProperty(value = "适当补贴")
	private Double sheetSdbt;
	
	@Excel(name = "独生子女补助")	
	@Column(name = "sheet_dsznbt")
	@ApiModelProperty(value = "独生子女补助")
	private Double sheetDsznbt;
	
	@Excel(name = "高龄补贴")
	@Column(name = "sheet_glbt")
	@ApiModelProperty(value = "高龄补贴")
	private Double sheetGlbt;
	
	@Excel(name = "交通补贴")
	@Column(name = "sheet_jtbt")
	@ApiModelProperty(value = "交通补贴")
	private Double sheetJtbt;
	
	@Excel(name = "节日加班费")
	@Column(name = "sheet_jjrjb")
	@ApiModelProperty(value = "节日加班费")
	private Double sheetJjrjb;
	
	@Excel(name = "补发工资")
	@Column(name = "sheet_bfgz")
	@ApiModelProperty(value = "补发工资")
	private Double sheetBfgz;
	
	@Excel(name = "基础性绩效")
	@Column(name = "sheet_jcxjx")
	@ApiModelProperty(value = "基础性绩效")
	private Double sheetJcxjx;
	
	@Excel(name = "里程补助")
	@Column(name = "sheet_lcbt")
	@ApiModelProperty(value = "里程补助")
	private Double sheetLcbt;
	
	
	@Excel(name = "基本养老金")
	@Column(name = "sheet_jbylj")
	@ApiModelProperty(value = "基本养老金")
	private Double sheetJbylj;
	
	@Excel(name = "退休职业年金")
	@Column(name = "sheet_txzynj")
	@ApiModelProperty(value = "退休职业年金")
	private Double sheetTxzynj;
	
	@Excel(name = "其他")
	@Column(name = "sheet_qt")
	@ApiModelProperty(value = "其他")
	private Double sheetQt;

	@Excel(name = "应发1合计")
	@Column(name = "sheet_yf1hj")
	@ApiModelProperty(value = "应发1合计")
	private Double sheetYf1hj;
	
	@Excel(name = "代扣公积金")
	@Column(name = "sheet_dkgjj")
	@ApiModelProperty(value = "代扣公积金")
	private Double sheetDkgjj;
	
	@Excel(name = "代扣养老保险金")
	@Column(name = "sheet_dkyanlaobxj")
	@ApiModelProperty(value = "代扣养老保险金")
	private Double sheetDkyanlaobxj;
	
	@Excel(name = "代扣医疗保险金")
	@Column(name = "sheet_dkyiliaobxj")
	@ApiModelProperty(value = "代扣医疗保险金")
	private Double sheetDkyiliaobxj;
	
	@Excel(name = "代扣失业保险金")
	@Column(name = "sheet_dksybxj")
	@ApiModelProperty(value = "代扣失业保险金")
	private Double sheetDksybxj;
	
	@Excel(name = "职业年金")
	@Column(name = "sheet_zynj")
	@ApiModelProperty(value = "职业年金")
	private Double sheetZynj;
	
	@Excel(name = "代扣工会会费")
	@Column(name = "sheet_dkghhh")
	@ApiModelProperty(value = "代扣工会会费")
	private Double sheetDkghhh;
	
	@Excel(name = "代扣税金1")
	@Column(name = "sheet_dksj1")
	@ApiModelProperty(value = "代扣税金1")
	private Double sheetDksj1;
	
	@Excel(name = "代扣房租费")
	@Column(name = "sheet_dkfzf")
	@ApiModelProperty(value = "代扣房租费")
	private Double sheetDkfzf;
	
	@Excel(name = "代扣自订报刊费")
	@Column(name = "sheet_dkzdbkf")
	@ApiModelProperty(value = "代扣自订报刊费")
	private Double sheetDkzdbkf;
	
	@Excel(name = "其他扣项")
	@Column(name = "sheet_qtkx")
	@ApiModelProperty(value = "其他扣项")
	private Double sheetQtkx;
	
	@Excel(name = "扣款合计")
	@Column(name = "sheet_kkhj")
	@ApiModelProperty(value = "扣款合计")
	private Double sheetKkhj;
	
	@Excel(name = "实发1合计")
	@Column(name = "sheet_sf1hj")
	@ApiModelProperty(value = "实发1合计")
	private Double sheetSf1hj;
	
	/**
	 * 备注
	 */
	@Column(name = "remark")
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;
	
	@Transient
	private List<String> monthList;
	
	@Transient
	private String gender; 		//性别

}

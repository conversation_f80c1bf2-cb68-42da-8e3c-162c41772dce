package cn.trasen.hrms.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "hrms_salary_plan_detail")
@Setter
@Getter
public class HrmsSalaryPlanDetail {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	@Id
	@Column(name = "salary_plan_detail_id")
	private String salaryPlanDetailId;

	/**
	 * 方案ID
	 */
	@Column(name = "salary_plan_id")
	@ApiModelProperty(value = "方案ID")
	private String salaryPlanId;

	/**
	 * 项目ID
	 */
	@Column(name = "item_id")
	@ApiModelProperty(value = "项目ID")
	private String itemId;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 项目名称
	 */
	@Transient
	@ApiModelProperty(value = "项目名称")
	private String salaryItemName;
	
	/**
	 * 项目编码
	 */
	@Transient
	@ApiModelProperty(value = "项目编码")
	private String salaryItemCode;

	/**
	 * 项目类型: 1=系统; 2=计算; 3=手工录入; 4=基础值;
	 */
	@Transient
	@ApiModelProperty(value = "项目类型: 1=系统; 2=计算; 3=手工录入; 4=基础值;")
	private String salaryItemType;

	/**
	 * 项目类型文本值
	 */
	@Transient
	@ApiModelProperty(value = "项目类型文本值")
	private String salaryItemTypeText;

	/**
	 * 计算类型: 1=收入; 2=扣减;
	 */
	@Transient
	@ApiModelProperty(value = "计算类型: 1=收入; 2=扣减;")
	private String countType;

	/**
	 * 计算类型文本值
	 */
	@Transient
	@ApiModelProperty(value = "计算类型文本值")
	private String countTypeText;
	
	/**
	 * 计算公式
	 */
	@Transient
	@ApiModelProperty(value = "计算公式")
	private String countFormula;

	/**
	 * 计算公式文本显示
	 */
	@Transient
	@ApiModelProperty(value = "计算公式文本显示")
	private String countFormulaText;
	
	/**
	 * 金额
	 */
	@Transient
	@ApiModelProperty(value = "金额")
	private BigDecimal salaryItemAmount;
	
	/**
	 * 方案项目ID集合
	 */
	@Transient
	@ApiModelProperty(value = "方案项目ID集合")
	private List<String> salaryItemIds;

}
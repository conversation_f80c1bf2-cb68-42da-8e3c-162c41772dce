package cn.trasen.hrms.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 请假单
 *
 */
@Table(name = "hrms_leave_report")
@Setter
@Getter
public class HrmsLeaveReport {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Id
    private String id;

    /**
     * 请假人
     */
    @Column(name = "employee_code")
    @ApiModelProperty(value = "请假人")
    private String employeeCode;

    /**
     * 请假人名称
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "请假人名称")
    private String employeeName;

    /**
     * 请假时间可能会存在跨月,所以存的是本月的
     */
    @ApiModelProperty(value = "本月请假天数")
    private BigDecimal days;

    /**
     * 请假类型
     */
    @Column(name = "leave_type")
    @ApiModelProperty(value = "请假类型")
    private String leaveType;

    /**
     * 操作人员
     */
    @Column(name = "operation_user")
    @ApiModelProperty(value = "操作人员")
    private String operationUser;

    /**
     * 操作人员名称
     */
    @Column(name = "operation_user_name")
    @ApiModelProperty(value = "操作人员名称")
    private String operationUserName;

    /**
     * 请假月份
     */
    @Column(name = "leave_month")
    @ApiModelProperty(value = "上报月份")
    private String leaveMonth;

    /**
     * 上报科室code
     */
    @Column(name = "dept_code")
    @ApiModelProperty(value = "上报科室code")
    private String deptCode;

    /**
     * 上报科室名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "上报科室名称")
    private String deptName;
    
    @ApiModelProperty(value = "状态 0:未上报 1 已上报 ")
    private Integer status;
    
    @Column(name = "is_update")
    @ApiModelProperty(value = "修改状态 0:未修改 1 已修改")
    private Long isUpdate;
    
    @Column(name = "start_date")
    @ApiModelProperty(value = "请假开始时间")
    private Date startDate;
    
    @Column(name = "start_date_value")
    @ApiModelProperty(value = "请假具体开始时间:1-上午开始，2-下午开始")
    private String startDateValue;
    
    @Column(name = "end_date")
    @ApiModelProperty(value = "请假结束时间")
    private Date endDate;
    
    @Column(name = "end_date_value")
    @ApiModelProperty(value = "请假具体结束时间:1-上午结束，2-下午结束")
    private String endDateValue;
    
    @Column(name = "workflow_id")
    @ApiModelProperty(value = "流程实例id")
    private String workflowId;
    
    @Column(name = "month_start_date")
    @ApiModelProperty(value = "请假开始时间")
    private Date monthStartDate;
    
    @Column(name = "month_end_date")
    @ApiModelProperty(value = "请假结束时间")
    private Date monthEndDate;
    
    @Column(name = "examine_status")
    @ApiModelProperty(value = "审核状态  0 未审核  1审核不通过 2 审核通过")
    private Integer examineStatus;
    
    @Column(name = "examine_reason")
    @ApiModelProperty(value = "不通过原因")
    private String examineReason;
    
    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;
    
    @Column(name = "reason")
    @ApiModelProperty(value = "请假事由")
    private String reason;

    /**
     * 销假总天数
     */
    @Transient
    private BigDecimal cancelLeaveDays;

    /**
     * 销假列表
     */
    @Transient
    private List<HrmsCancelLeaveReport> cancelLeaveReportList = new ArrayList<>();

    /**
     * 销假明细
     */
    @Transient
    private String cancelLeaveDetail;
    /**
     * 身份证号
     */
    @Transient
    private String identityNumber;
    
    /**
     * 请假总天数
     */
    @Transient
    private BigDecimal sumDays;
    
    @Transient
    @ApiModelProperty(value = "机构权限范围")
    private List<String> orgCodeList;
    
    @Transient
    @ApiModelProperty(value = "页面查询条件")
    private String orgId;
    
    @Transient
    private String employeeCodes;
    
    @Transient
    @ApiModelProperty(value = "本月总请假天数=请假天数-销假天数")
    private BigDecimal totalDays;
    
    @Transient
    @ApiModelProperty(value = "请假开始月份")
    private String startLeaveMonth;
    
    @Transient
    @ApiModelProperty(value = "请假结束月份")
    private String endLeaveMonth;
    
    @ApiModelProperty(value = "病假年统计")
    @Transient
    private Double bjCount;
    
    @Transient
    private String isWorkflow;

    @ApiModelProperty(value = "岗位名称")
    @Transient
    private String personalIdentityName;

    @ApiModelProperty(value = "科室名称")
    @Transient
    private String orgName;

    @Transient
    @ApiModelProperty(value = "机构id列表")
    private List<String> orgIds;

    @Transient
    @ApiModelProperty(value = "查询开始时间")
    private String searchStartDate;

    @Transient
    @ApiModelProperty(value = "查询结束时间")
    private String searchEndDate;
    
    @Transient
    @ApiModelProperty(value = "请假开始时间文本")
    private String startDateText;
    
    @Transient
    @ApiModelProperty(value = "请假结束时间文本")
    private String endDateText;
}
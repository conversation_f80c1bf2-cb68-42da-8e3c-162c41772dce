package cn.trasen.hrms.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * 人事事件
 *
 */
@Table(name = "hrms_personnel_incident")
@Setter
@Getter
public class HrmsPersonnelIncident {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "personnel_incident_id")
    @ApiModelProperty(value = "主键ID")
    private String personnelIncidentId;

    /**
     * 员工Id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工Id")
    private String employeeId;

    /**
     * 员工工号
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 员工姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    /**
     * 事件类型
     */
    @Column(name = "incident_type")
    @ApiModelProperty(value = "事件类型")
    private String incidentType;

    /**
     * 事件类别: 1=离职; 2=退休; 3=死亡; 4=延聘; 5=返聘;
     */
    @Column(name = "incident_category")
    @ApiModelProperty(value = "事件类别: 1=离职; 2=退休; 3=死亡; 4=延聘; 5=返聘;")
    private String incidentCategory;

    /**
     * 审批状态: 1=未审批; 2=审批中; 3=已退回; 4=已审批
     */
    @Column(name = "approval_status")
    @ApiModelProperty(value = "审批状态: 1=未审批; 2=审批中; 3=已退回; 4=已审批")
    private String approvalStatus;

    /**
     * 开始时间
     */
    @Column(name = "start_time")
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 事件时间(针对不同类型事件只有一个日期的存这个字段,例如:离职日期)
     */
    @Column(name = "incident_time")
    @ApiModelProperty(value = "事件时间(针对不同类型事件只有一个日期的存这个字段,例如:离职日期)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date incidentTime;

    /**
     * 备注
     */
    @Column(name = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @Column(name = "file_id")
    @ApiModelProperty(value = "附件id")
    private String fileId;
    
    @Column(name = "workflow_id")
    @ApiModelProperty(value = "工作流id")
    private String workflowId;

    /**
     * 关联事件人员工号
     */
    @Column(name = "related_user_id")
    @ApiModelProperty(value = "关联事件人员工号")
    private String relatedUserId;

    /**
     * 关联事件人员姓名
     */
    @Column(name = "related_user_name")
    @ApiModelProperty(value = "关联事件人员姓名")
    private String relatedUserName;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    /**
     * 事件类型文本值
     */
    @Transient
    @ApiModelProperty(value = "事件类型文本值")
    private String incidentTypeText;

    /**
     * 审批状态文本值
     */
    @Transient
    @ApiModelProperty(value = "审批状态文本值")
    private String approvalStatusText;

    /**
     * 科室ID
     */
    @Transient
    @ApiModelProperty(value = "科室id")
    private String employeeOrgId;

    /**
     * 科室名称
     */
    @Transient
    @ApiModelProperty(value = "科室名称")
    private String employeeOrgName;

    /**
     * 职位/岗位ID
     */
    @ApiModelProperty(value = "职位/岗位ID")
    @Column(name = "position_id")
    private String positionId;

    /**
     * 职位/岗位
     */
    @Transient
    @ApiModelProperty(value = "职位/岗位")
    private String positionName;

    /**
     * 删选开始时间
     */
    @Transient
    @ApiModelProperty(value = "删选开始时间")
    private String startDate;

    /**
     * 删选结束时间
     */
    @Transient
    @ApiModelProperty(value = "删选结束时间")
    private String endDate;


    /**
     * 导出-开始时间
     */
    @Transient
    @ApiModelProperty(value = "导出-开始时间")
    private String startTimeExport;

    /**
     * 导出-结束时间
     */
    @Transient
    @ApiModelProperty(value = "导出-结束时间")
    private String endTimeExport;

    /**
     * 导出-创建时间
     */
    @Transient
    @ApiModelProperty(value = "导出-创建时间")
    private String createDateExport;

    /**
     * 导出-事件时间(针对不同类型事件只有一个日期的存这个字段,例如:离职日期)
     */
    @Transient
    @ApiModelProperty(value = "导出-事件时间")
    private String incidentTimeExport;
    
    @Column(name = "stop_event")
    @ApiModelProperty(value = "停止事件（1已停止）")
    private String stopEvent;
    
    @Column(name = "new_org_id")
    @ApiModelProperty(value = "新部门id")
    private String newOrgId;
    
    @Column(name = "personal_identity")
    @ApiModelProperty(value = "岗位")
    private String personalIdentity;
    
    @Transient
    @ApiModelProperty(value = "岗位")
    private String personalIdentityText;
    
    
    @Column(name = "establishment_type")
    @ApiModelProperty(value = "编制类型")
    private String establishmentType; //编制类型
   
    @ApiModelProperty(value = "离职原因")
    private String cause; //离职原因
    @Transient
    private String causeText;  //离职原因文本
    
    
    @Transient
    private Integer no;
   
    @Transient
    private String htOrgIdList;
    
    @Transient
    private List<String> orgIds;
    
    
    
    @Column(name = "txRecordTime")
    @ApiModelProperty(value = "档案出生日期")
    private String txRecordTime;
    
    @Column(name = "txmoney")
    @ApiModelProperty
    private String txmoney;

    @Column(name = "retired_money")
    @ApiModelProperty(value = "退休工资")
    private String retiredMoney;

    @Column(name = "subsidy_money")
    @ApiModelProperty(value = "津贴补助")
    private String subsidyMoney;

    @Transient
	private String createStartDate;  //创建开始时间
	@Transient
	private String createEndDate;  //创建结束时间
	
    @Transient
    private List<String> establishmentTypes;
    
    //员工状态
	@Transient
	private String employeeStatus;  //员工状态

    @Transient
    private List<String> politicalStatusTypes;

    @Transient
    private String politicalStatus;   //政治面貌

    @Transient
    private String identityNumber;   //身份证号

    @Transient
    private String operatorUserName;  //操作人
    
    @Transient
    private String workStartDate;//参加工作时间字段
    

}
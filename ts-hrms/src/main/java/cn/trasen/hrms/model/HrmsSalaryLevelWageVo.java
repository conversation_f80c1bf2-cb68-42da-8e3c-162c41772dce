package cn.trasen.hrms.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Id;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class HrmsSalaryLevelWageVo {
    @Id
    @Column(name = "salary_level_wage_id")
    @ApiModelProperty(value = "主键ID")
    private String salaryLevelWageId;

    @Column(name = "salary_level_id")
    @ApiModelProperty(value = "薪级ID")
    private String salaryLevelId;

    @Column(name = "salary_level_wage")
    @ApiModelProperty(value = "薪级工资")
    private BigDecimal salaryLevelWage;

 	@ApiModelProperty(value = "薪级名称")
 	private String salaryLevelName;

 	@ApiModelProperty(value = "薪级类别ID")
 	private String salaryLevelCategory;

 	@ApiModelProperty(value = "薪级类别名称")
	private String salaryLevelCategoryName;
 	
    @ApiModelProperty(value = "等级")
    private String grade;

    @ApiModelProperty(value = "政策标准id")
    @Column(name = "policy_standard_id")
    private String policyStandardId;
}
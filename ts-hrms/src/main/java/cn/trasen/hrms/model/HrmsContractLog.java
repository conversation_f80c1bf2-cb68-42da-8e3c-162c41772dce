package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_contract_log")
@Setter
@Getter
public class HrmsContractLog {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 操作类型
     */
    @Column(name = "action_type")
    @ApiModelProperty(value = "操作类型")
    private String actionType;

    /**
     * 员工工号
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 员工姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    /**
     * 身份证
     */
    @Column(name = "card")
    @ApiModelProperty(value = "身份证")
    private String card;

    /**
     * 操作信息
     */
    @Column(name = "content")
    @ApiModelProperty(value = "操作信息")
    private String content;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    @Transient
    @ApiModelProperty(value = "搜索开始时间")
    private String operatinStartDate;
    @Transient
    @ApiModelProperty(value = "搜索结束时间")
    private String operatinEndDate;
}
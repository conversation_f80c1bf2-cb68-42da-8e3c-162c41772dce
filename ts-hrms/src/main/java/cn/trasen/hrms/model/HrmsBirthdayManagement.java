package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "hrms_birthday_management")
@Setter
@Getter
public class HrmsBirthdayManagement {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	/**
	 * 主键
	 */
	@Id
	@Column(name = "birthday_management_id")
	@ApiModelProperty(value = "主键")
	private String birthdayManagementId;

	/**
	 * 员工工号
	 */
	@Column(name = "employee_no")
	@ApiModelProperty(value = "员工工号")
	private String employeeNo;

	/**
	 * 姓名
	 */
	@Column(name = "employee_name")
	@ApiModelProperty(value = "姓名")
	private String employeeName;

	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别")
	private String gender;

	/**
	 * 出生日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@ApiModelProperty(value = "出生日期")
	private Date birthday;

	/**
	 * 生日日期
	 */
	@Column(name = "date_of_birth")
	@ApiModelProperty(value = "生日日期")
	private String dateOfBirth;

	/**
	 * 生日礼物
	 */
	@Column(name = "birthday_gift")
	@ApiModelProperty(value = "生日礼物")
	private String birthdayGift;

	/**
	 * 领取状态
	 */
	@Column(name = "receive_status")
	@ApiModelProperty(value = "领取状态")
	private String receiveStatus;

	/**
	 * 领取日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@Column(name = "receive_date")
	@ApiModelProperty(value = "领取日期")
	private Date receiveDate;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 领取状态文本值
	 */
	@Transient
	@ApiModelProperty(value = "领取状态文本值")
	private String receiveStatusText;

	/**
	 * 生日查询开始时间
	 */
	@Transient
	@ApiModelProperty(value = "生日查询开始时间")
	private String startDate;

	/**
	 * 生日查询结束时间
	 */
	@Transient
	@ApiModelProperty(value = "生日查询结束时间")
	private String endDate;

}
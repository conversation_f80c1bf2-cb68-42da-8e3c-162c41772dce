package cn.trasen.hrms.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

/**
 * 党委成员
 * 
 * <AUTHOR>
 *
 */

@Table(name = "hrms_committee_employee")
@Setter
@Getter
public class CommitteeEmployee {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "id")
	@ApiModelProperty(value = "主键ID")
	private String id;

	/**
	 * 主表ID
	 */
	@Column(name = "committee_id")
	@ApiModelProperty(value = "主表id")
	private String committeeId;
	
    /**
     * 附件id
     */
    @Column(name = "files_id")
    @ApiModelProperty(value = "附件id")
    private String filesId;

	/**
	 * 
	 */
	@Column(name = "employee_id")
	@ApiModelProperty(value = "员工ID")
	private String employeeId;

	/**
	 * 党委书记姓名
	 */
	@Column(name = "employee_name")
	@ApiModelProperty(value = "姓名")
	private String employeeName;

	/**
	 * 职务
	 */
	@Column(name = "post_id")
	@ApiModelProperty(value = "党内职务id")
	private String postId;

	/**
	 * 职务
	 */
	@Column(name = "post")
	@ApiModelProperty(value = "党内职务")
	private String post;

	/**
	 * 任职日期
	 */
	@Column(name = "input_date")
	@ApiModelProperty(value = "任职日期")
	private String inputDate;

	
	/**
	 * 离任日期
	 */
	@Column(name = "output_date")
	@ApiModelProperty(value = "离任日期")
	private String outputDate;

	@Transient
	@ApiModelProperty(value = "性别")
	private String sex;

	@Transient
	@ApiModelProperty(value = "身份证号")
	private String identityNumber;

	@Transient
	@ApiModelProperty(value = "年龄")
	private Integer age;

	@Transient
	@ApiModelProperty(value = "入党日期")
	private String partyDate;

	@Transient
	@ApiModelProperty(value = "学历")
	private String education;
	
	@Transient
	@ApiModelProperty(value = "姓名")
	private String employeeNo;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;
}

package cn.trasen.hrms.model;

import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 销假单
 * <AUTHOR>
 *
 */
@Table(name = "hrms_cancel_leave_report")
@Setter
@Getter
public class HrmsCancelLeaveReport {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Id
    private String id;

    /**
     * 销假人
     */
    @Column(name = "employee_code")
    @ApiModelProperty(value = "销假人")
    private String employeeCode;

    /**
     * 销假人名称
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "销假人名称")
    private String employeeName;

    /**
     * 销假天数
     */
    @ApiModelProperty(value = "销假天数")
    private BigDecimal days;

    /**
     * 销假类型
     */
    @Column(name = "cancel_leave_type")
    @ApiModelProperty(value = "销假类型")
    private String cancelLeaveType;

    /**
     * 上报月份
     */
    @Column(name = "cancel_leave_month")
    @ApiModelProperty(value = "上报月份")
    private String cancelLeaveMonth;

    /**
     * 科室code
     */
    @Column(name = "dept_code")
    @ApiModelProperty(value = "科室code")
    private String deptCode;

    /**
     * 科室名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "科室名称")
    private String deptName;

    /**
     * 销假开始时间
     */
    @Column(name = "start_date")
    @ApiModelProperty(value = "销假开始时间")
    private Date startDate;
    
    @Column(name = "start_date_value")
    @ApiModelProperty(value = "销假具体开始时间:1-上午开始，2-下午开始")
    private String startDateValue;

    /**
     * 销假结束时间
     */
    @Column(name = "end_date")
    @ApiModelProperty(value = "销假结束时间")
    private Date endDate;
    
    @Column(name = "end_date_value")
    @ApiModelProperty(value = "销假具体结束时间:1-上午结束，2-下午结束")
    private String endDateValue;

    /**
     * 流程实例id
     */
    @Column(name = "workflow_id")
    @ApiModelProperty(value = "流程实例id")
    private String workflowId;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    @Column(name = "leave_workflow_id")
    @ApiModelProperty(value = "请假流程实例id")
    private String leaveWorkflowId;

//    @Column(name = "relation_id")
    @Transient
    @ApiModelProperty(value = "关联请假流程id")
    private String relationId;


}
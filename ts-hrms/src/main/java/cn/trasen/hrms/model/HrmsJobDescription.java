package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_job_description")
@Setter
@Getter
public class HrmsJobDescription {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键
     */
	@Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 员工id
     */
    @Column(name = "emp_id")
    @ApiModelProperty(value = "员工id")
    private String empId;

    /**
     * 员工编号
     */
    @Column(name = "emp_code")
    @ApiModelProperty(value = "员工编号")
    private String empCode;

    /**
     * 员工姓名
     */
    @Column(name = "emp_name")
    @ApiModelProperty(value = "员工姓名")
    private String empName;

    /**
     * 所属部门
     */
    @Column(name = "emp_dept")
    @ApiModelProperty(value = "所属部门")
    private String empDept;

    /**
     * 所属部门名称
     */
    @Column(name = "emp_dept_name")
    @ApiModelProperty(value = "所属部门名称")
    private String empDeptName;

    /**
     * 岗位等级
     */
    @Column(name = "job_level")
    @ApiModelProperty(value = "岗位等级")
    private String jobLevel;

    /**
     * 隶属部门
     */
    @Column(name = "attached_dept")
    @ApiModelProperty(value = "隶属部门")
    private String attachedDept;

    /**
     * 职类职系
     */
    @Column(name = "job_type")
    @ApiModelProperty(value = "职类职系")
    private String jobType;

    /**
     * 职称
     */
    @Column(name = "emp_title")
    @ApiModelProperty(value = "职称")
    private String empTitle;

    /**
     * 直属上级
     */
    @Column(name = "direct_leader")
    @ApiModelProperty(value = "直属上级")
    private String directLeader;

    /**
     * 护士层级
     */
    @Column(name = "nurse_level")
    @ApiModelProperty(value = "护士层级")
    private String nurseLevel;

    /**
     * 请示上报(内部关系)
     */
    @Column(name = "internal_relations")
    @ApiModelProperty(value = "请示上报")
    private String internalRelations;

    /**
     * 临床护理(内部关系)
     */
    @Column(name = "clinical_nursing")
    @ApiModelProperty(value = "临床护理")
    private String clinicalNursing;

    /**
     * 外部关系
     */
    @Column(name = "external_relations")
    @ApiModelProperty(value = "外部关系")
    private String externalRelations;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 工作职责
     */
    @Column(name = "job_duties")
    @ApiModelProperty(value = "工作职责")
    private String jobDuties;

    /**
     * 任职资格
     */
    @ApiModelProperty(value = "任职资格")
    private String qualification;
}
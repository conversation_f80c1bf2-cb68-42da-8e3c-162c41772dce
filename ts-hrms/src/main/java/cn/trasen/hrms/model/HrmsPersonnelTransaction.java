package cn.trasen.hrms.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.UserInfoHolder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 人事异动记录表
 *
 */
@Table(name = "hrms_personnel_transaction")
@Setter
@Getter
public class HrmsPersonnelTransaction {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 员工工号
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 员工名字
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "员工名字")
    private String employeeName;

    /**
     * 员工id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    /**
     * 原机构ID
     */
    @Column(name = "old_org_id")
    @ApiModelProperty(value = "原机构ID")
    private String oldOrgId;

    /**
     * 原机构名称
     */
    @Column(name = "old_org_name")
    @ApiModelProperty(value = "原机构名称")
    private String oldOrgName;

    /**
     * 新机构ID
     */
    @Column(name = "new_org_id")
    @ApiModelProperty(value = "新机构ID")
    private String newOrgId;

    /**
     * 新机构名称
     */
    @Column(name = "new_org_name")
    @ApiModelProperty(value = "新机构名称")
    private String newOrgName;

    /**
     * 生效日期
     */
    @Column(name = "effective_date")
    @ApiModelProperty(value = "生效日期")
    private String effectiveDate;

    /**
     * 变动原因
     */
    @ApiModelProperty(value = "变动原因")
    private String cause;

    /**
     * 是否执行
     */
    @ApiModelProperty(value = "是否执行")
    private String execute;

    /**
     * 通知单编号
     */
    @Column(name = "batch_number")
    @ApiModelProperty(value = "通知单编号")
    private String batchNumber;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    @Transient
    private Integer no;  //编号
   
    @Transient
    private String sarchDate; //搜索时间
  
    @Transient
    private String createDateStr;  //创建时间字符串
    @Transient
    private String htOrgIdList; //权限

	public HrmsPersonnelTransaction() {
		super();
	}
    
	public HrmsPersonnelTransaction(String employeeNo, String employeeName, String employeeId,
			String oldOrgId, String oldOrgName, String newOrgId, String newOrgName, String effectiveDate,
			String cause,String execute, String batchNumber, String remark,  String orgId,
			String orgName) {
		this.employeeNo = employeeNo;
		this.employeeName = employeeName;
		this.employeeId = employeeId;
		this.oldOrgId = oldOrgId;
		this.oldOrgName = oldOrgName;
		this.newOrgId = newOrgId;
		this.newOrgName = newOrgName;
		this.effectiveDate = effectiveDate;
		this.cause = cause;
		this.execute = execute;
		this.batchNumber = batchNumber;
		this.remark = remark;
		this.createDate = new Date();
		this.createUser = UserInfoHolder.getCurrentUserCode();
		this.createUserName = UserInfoHolder.getCurrentUserName();
		this.orgId = orgId;
		this.orgName = orgName;
		this.isDeleted = Contants.IS_DELETED_FALSE;
		
	}
   
	@Transient	
	public List<String> orgIds;
	
	@Transient
	private String establishmentType;  // 编制类型
	
	@Transient
	private String effectiveStartDate;  //异动开始时间
	
	@Transient
	private String effectiveEndDate;  //异动结束时间

    @Transient
    private String identityNumber;  //身份证号

	
	private List<String> causes; //异动类型
}
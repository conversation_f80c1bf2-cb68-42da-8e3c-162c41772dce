package cn.trasen.hrms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.jeecgframework.poi.excel.annotation.Excel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description: 志愿者表
 * @Date: 2020年3月23日  下午4:00:02 
 * @Author: Z 
 * @Company: 湖南创星科技股份有限公司 
 * @version V1.0
*/
@Table(name = "hrms_voluntary")
@Setter
@Getter
public class HrmsVoluntary {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @ApiModelProperty(value = "主键ID")
    private String id;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 姓名
     */
    @Excel(name = "志愿者姓名")
    @Column(name = "employee_name")
    @ApiModelProperty(value = "姓名")
    private String employeeName;
    
    @Column(name = "employee_id")
    @ApiModelProperty(value = "姓名")
    private String employeeId;
    
    /**
     * 姓名
     */
    @Excel(name = "志愿者工号")
    @Column(name = "employee_no")
    @ApiModelProperty(value = "工号")
    private String employeeNo;
    
    
    @Excel(name = "志愿者科室")
    @Column(name = "employee_dept")
    @ApiModelProperty(value = "志愿者科室")
    private String employeeDept;
    
    
    @Excel(name = "志愿者岗位")
    @Column(name = "voluntary_post")
    @ApiModelProperty(value = "志愿者岗位")
    private String voluntaryPost;
    
    
    @Column(name = "voluntary_date")
    @ApiModelProperty(value = "志愿者时间")
    private String voluntaryDate;
    
    @Excel(name = "完成时长（小时）")
    @Column(name = "voluntary_duration")
    @ApiModelProperty(value = "完成时长")
    private Double voluntaryDuration;
    
    @Excel(name = "总计时长")
    @Column(name = "voluntary_duration")
    @ApiModelProperty(value = "总计时长")
    private Double voluntaryCountDuration;
    
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    @Transient
    private Date voluntaryStartDate;  //开始时间
    
    @Transient
    private Date voluntaryEndDate;   //结束时间
    
    @Transient
    private String startDate;  //开始时间
    
    @Transient
    private String endDate;   //结束时间
    
    @Transient
    private String tempSta;   //1模板下载，2导出数据
    
    @Transient
    private String voluntaryDateStr;  //志愿者完成时间字符串
   
    @Transient
    private String own; //是否查自己的 （1：自己）

    @Transient
    private Integer no;
    
    @Column(name = "phone_number")
    @ApiModelProperty(value = "手机号")
    private String phoneNumber;
   
    @Column(name = "gender")
    @ApiModelProperty(value = "性别")
    private String gender;
    
    @Transient
    private String oldOrgName;  //科室名称
    
    @Column(name = "education")
    @ApiModelProperty(value = "学历")
    private String education;
    
    @Column(name = "identity_number")
    @ApiModelProperty(value = "身份证")
    private String identityNumber;
    
    @Column(name = "political_status")
    @ApiModelProperty(value = "政治面貌")
    private String politicalStatus;
    
    @Column(name = "email")
    @ApiModelProperty(value = "邮箱")
    private String email;
    
    @Column(name = "birthday")
    @ApiModelProperty(value = "生日")
    private String birthday;

}
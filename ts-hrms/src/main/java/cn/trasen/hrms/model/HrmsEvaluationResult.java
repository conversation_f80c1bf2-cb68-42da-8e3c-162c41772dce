package cn.trasen.hrms.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.jeecgframework.poi.excel.annotation.Excel;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 年度考核javaBean
 * <AUTHOR>
 *
 */
@Table(name = "hrms_evaluation_result")
@Setter
@Getter
@ToString
public class HrmsEvaluationResult {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;


	/**
     * id
     */
    @Id
    @Column(name = "id")
    @ApiModelProperty(value = "hrms_evaluation_result")
    private String id;

	/**
	 * 员工ID
	 */
	@Column(name = "employee_id")
	@ApiModelProperty(value = "员工ID")
	private String employeeId;
	
	/**
	 * 考核年份
	 */
	@Excel(name = "考核年度")
	@Column(name = "assess_year")
	@ApiModelProperty(value = "考核年份")
	private String assessYear;

	/**
	 * 上报单位
	 */
	@Excel(name = "上报单位")
	@Column(name = "assess_org")
	@ApiModelProperty(value = "上报单位")
	private String assessOrg;
	
	/**
	 * 考核结果
	 */
	@Excel(name = "考核结果")
	@Column(name = "assess_result")
	@ApiModelProperty(value = "考核结果")
	private String assessResult;
	
	/**
	 * 考核说明
	 */
	@Excel(name = "考核说明")
	@Column(name = "remark")
	@ApiModelProperty(value = "考核说明")
	private String remark;
	
	

	 /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    /**
     * 论文附件;
     */
    @Column(name = "khfj")
    @ApiModelProperty(value = "考核附件")
    private String khfj;
    
	/**
	 * 员工工号
	 */
    @Excel(name = "员工工号")
	@Transient
	@ApiModelProperty(value = "员工工号")
    private String employeeNo;
    
	/**
	 * 员工姓名
	 */
    @Excel(name = "员工姓名")
	@Transient
	@ApiModelProperty(value = "员工姓名")
    private String employeeName;
	
	/**
	 * 员工身份证
	 */
    @Excel(name = "身份证")
	@Transient
	@ApiModelProperty(value = "员工身份证")
    private String identityNumber;
	
	/**
	 * 组织机构ID集合
	 */
	@ApiModelProperty(value = "组织机构ID集合")
	private List<String> orgIdList;
	
	/**
	 * 组织机构ID集合
	 */
	@ApiModelProperty(value = "组织机构ID集合")
	private List<String> yearList;
	
	
	/**
	 * 组织机构ID集合
	 */
	@Transient
	@ApiModelProperty(value = "序号")
	private Integer no;
	
	/**
	 * 编制类型
	 */
	@Transient
	@ApiModelProperty(value = "编制类型")
	private String establishmentType;  //编制类型
	
	

}

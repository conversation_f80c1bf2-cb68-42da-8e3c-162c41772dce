package cn.trasen.hrms.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

/**
 * 人事调用
 *
 */
@Table(name = "hrms_personnel_change")
@Setter
@Getter
public class HrmsPersonnelChange {

	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "personnel_change_id")
	@ApiModelProperty(value = "主键ID")
	private String personnelChangeId;

	/**
	 * 员工ID
	 */
	@Column(name = "employee_id")
	@ApiModelProperty(value = "员工ID")
	private String employeeId;

	/**
	 * 员工工号
	 */
	@Column(name = "employee_no")
	@ApiModelProperty(value = "员工工号")
	private String employeeNo;

	/**
	 * 员工姓名
	 */
	@Column(name = "employee_name")
	@ApiModelProperty(value = "员工姓名")
	private String employeeName;
	
	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别")
	private String gender;

	/**
	 * 原组织机构ID
	 */
	@Column(name = "old_org_id")
	@ApiModelProperty(value = "原组织机构ID")
	private String oldOrgId;

	/**
	 * 原组织机构名称
	 */
	@Column(name = "old_org_Name")
	@ApiModelProperty(value = "原组织机构名称")
	private String oldOrgName;

	/**
	 * 新组织机构ID
	 */
	@Column(name = "new_org_id")
	@ApiModelProperty(value = "新组织机构ID")
	private String newOrgId;

	/**
	 * 新组织机名称
	 */
	@Column(name = "new_org_name")
	@ApiModelProperty(value = "新组织机名称")
	private String newOrgName;

	/**
	 * 原职务ID
	 */
	@Column(name = "old_position_id")
	@ApiModelProperty(value = "原职务ID")
	private String oldPositionId;

	/**
	 * 新职务ID
	 */
	@Column(name = "new_position_id")
	@ApiModelProperty(value = "新职务ID")
	private String newPositionId;
	
	/**
	 * 原岗位
	 */
	@Column(name = "old_personal_identity")
	@ApiModelProperty(value = "原岗位")
	private String oldPersonalIdentity;

	/**
	 * 新岗位
	 */
	@Column(name = "new_personal_identity")
	@ApiModelProperty(value = "新岗位")
	private String newPersonalIdentity;

	/**
	 * 调动类型
	 */
	@Column(name = "change_type")
	@ApiModelProperty(value = "调动类型")
	private String changeType;

	/**
	 * 调动生效时间
	 */
	@Column(name = "change_start_date")
	@ApiModelProperty(value = "调动生效时间")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date changeStartDate;

	/**
	 * 审批状态: 1=未审批; 2=审批中; 3=已退回; 4=已审批
	 */
	@Column(name = "approval_status")
	@ApiModelProperty(value = "审批状态: 1=未审批; 2=审批中; 3=已退回; 4=已审批")
	private String approvalStatus;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	/**
	 * 附件Id-前台显示
	 */
	@Column(name = "file_id")
	@ApiModelProperty(value = "附件Id-前台显示")
	private String fileId;

	// ------- 扩展字段 ------- //
	/**
	 * 主键ID(前端多选用)
	 */
	@Transient
	@ApiModelProperty(value = "主键ID(前端多选用)")
	private String id;
	
	/**
	 * 性别文本值
	 */
	@ApiModelProperty(value = "性别文本值")
	@Transient
	private String genderText;

	/**
	 * 调动类型文本值
	 */
	@Transient
	@ApiModelProperty(value = "调动类型文本值")
	private String changeTypeText;

	/**
	 * 审批状态文本值
	 */
	@Transient
	@ApiModelProperty(value = "审批状态文本值")
	private String approvalStatusText;

	/**
	 * 删选开始时间
	 */
	@Transient
	@ApiModelProperty(value = "删选开始时间")
	private String startDate;

	/**
	 * 删选结束时间
	 */
	@Transient
	@ApiModelProperty(value = "删选结束时间")
	private String endDate;

	/**
	 * 原职务名称
	 */
	@Transient
	@ApiModelProperty(value = "原职务名称")
	private String oldPositionName;

	/**
	 * 新职务名称
	 */
	@Transient
	@ApiModelProperty(value = "新职务名称")
	private String newPositionName;

	/**
	 * 导出-调动生效时间
	 */
	@Transient
	@ApiModelProperty(value = "导出-调动生效时间")
	private String changeStartDateExport;

	/**
	 * 调动ID集合(前端多选用)
	 */
	@Transient
	@ApiModelProperty(value = "调动ID集合(前端多选用)")
	private List<String> changeIdList;
	
	/**
	 * 调动单生成日期
	 */
	@Transient
	@ApiModelProperty(value = "调动单生成日期")
	private String generateDate;
	
	@Column(name = "timekeeper_id")
	@ApiModelProperty(value = "考勤员")
	private String timekeeperId;
	
	@Transient
	@ApiModelProperty(value = "序号")
	private Integer no;
	
	@Transient
	@ApiModelProperty(value = "数据权限")
	private String htOrgIdList;
	
	@Transient
	private List<String> orgIds;
	
	@Column(name = "establishment_type")
	@ApiModelProperty(value = "编制类型  ")
	private String establishmentType;  //编制类型
	
	
	@Column(name = "job_attributes")
	@ApiModelProperty(value = "岗位属性")
	private String jobAttributes;  //岗位属性
	
	@Column(name = "post_type")
	@ApiModelProperty(value = "岗位类型")
	private String postType;  //岗位类型
	
	@Column(name = "org_attributes")
	@ApiModelProperty(value = "人员类别")
	private String orgAttributes;  //人员类别
	
	@Column(name = "old_job_attributes")
	@ApiModelProperty(value = "原岗位属性")
	private String oldJobAttributes;  //原岗位属性
	
	@Column(name = "old_post_type")
	@ApiModelProperty(value = "原岗位类型")
	private String oldPostType;  //原岗位类型
	
	@Column(name = "old_org_attributes")
	@ApiModelProperty(value = "原人员类别")
	private String oldOrgAttributes;  //原人员类别
	
	
	@Transient
	private String oldPersonalIdentityText;
	
	@Transient
	private String newPersonalIdentityText;        
	@Transient
	private String createStartDate;  //创建开始时间
	@Transient
	private String createEndDate;  //创建结束时间
	
	
	@Column(name = "shifouzhongcengganbu")
	@ApiModelProperty(value = "中层干部")
	private String shifouzhongcengganbu;  //中层干部
	
	@Transient
	private String role;  //经开根据角色查询

	@Transient
	private String operatorUserName;  //操作人
	
	@Transient
	private String identityNumber;//身份证号码
	
}
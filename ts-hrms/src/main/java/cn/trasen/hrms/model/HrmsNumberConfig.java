package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_number_config")
@Setter
@Getter
public class HrmsNumberConfig {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "number_config_id")
	@ApiModelProperty(value = "主键ID")
	private String numberConfigId;

	/**
	 * 配置类别(作用于的业务类别)
	 */
	@Column(name = "config_category")
	@ApiModelProperty(value = "配置类别(作用于的业务类别)")
	private String configCategory;

	/**
	 * 序号位数
	 */
	@Column(name = "serial_digits")
	@ApiModelProperty(value = "序号位数")
	private Integer serialDigits;

	/**
	 * 序号初始值
	 */
	@Column(name = "initial_number")
	@ApiModelProperty(value = "序号初始值")
	private String initialNumber;

	/**
	 * 序号前缀
	 */
	@Column(name = "serial_prefix")
	@ApiModelProperty(value = "序号前缀")
	private String serialPrefix;

	/**
	 * 序号后缀
	 */
	@Column(name = "serial_suffix")
	@ApiModelProperty(value = "序号后缀")
	private String serialSuffix;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 配置类别文本值(作用于的业务类别)
	 */
	@Transient
	@ApiModelProperty(value = "配置类别文本值(作用于的业务类别)")
	private String configCategoryText;
}
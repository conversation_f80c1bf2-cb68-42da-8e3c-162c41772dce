package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "hrms_party_members")
@Setter
@Getter
public class HrmsPartyMembers {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;


    /**
     * 主键
     */
	@Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 员工关联ID
     */
    @Column(name = "emp_id")
    @ApiModelProperty(value = "员工关联ID")
    private String empId;

    /**
     * 政治面貌
     */
    @Column(name = "political_status")
    @ApiModelProperty(value = "政治面貌")
    private String politicalStatus;

    /**
     * 入党日期
     */
    @Column(name = "party_date")
    @ApiModelProperty(value = "入党日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date partyDate;

    /**
     * 退休日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @Column(name = "retire_date")
    @ApiModelProperty(value = "退休日期")
    private Date retireDate;

    /**
     * 审核标示
     */
    @Column(name = "party_status")
    @ApiModelProperty(value = "审核标示")
    private String partyStatus;

    /**
     * 附件id
     */
    @Column(name = "files_id")
    @ApiModelProperty(value = "附件id")
    private String filesId;

    /**
     * 员工工号
     */
    @Column(name = "emp_code")
    @ApiModelProperty(value = "员工工号")
    private String empCode;

    /**
     * 员工姓名
     */
    @Column(name = "emp_name")
    @ApiModelProperty(value = "员工姓名")
    private String empName;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    private String idcard;

    /**
     * 岗位名称
     */
    @Column(name = "post_name")
    @ApiModelProperty(value = "岗位名称")
    private String postName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    /**
     * 审核人
     */
    @Column(name = "examine_user")
    @ApiModelProperty(value = "审核人")
    private String examineUser;
    
    /**
     * 审核人名称
     */
    @Column(name = "examine_user_name")
    @ApiModelProperty(value = "审核人名称")
    private String examineUserName;
    
    /**
     * 审核时间
     */
    @Column(name = "examine_date")
    @ApiModelProperty(value = "审核时间")
    private Date examineDate;
    
    @Transient
    private String partyDateStartTime;
    
    @Transient
    private String partyDateEndTime;
    
    @Transient
    private String nameOrCode;
    
    @Transient
    private String politicalStatusText;
    
    @Transient
    private String postNameText;
    
    @Transient
    private int pm;
    
    @Transient
    private String otherPartyMembers;
    
    @Transient
    @ApiModelProperty(value = "性别")
    private String gender;
    
    @Transient
    @ApiModelProperty(value = "民族")
    private String nationality;
    
    @Transient
    @ApiModelProperty(value = "出生年月")
    private String birthday;
    
    @Transient
    @ApiModelProperty(value = "最高学历")
    private String educationType;
    
    @Transient
    @ApiModelProperty(value = "最高职称")
    private String jobtitleName;
    
    @Transient
    @ApiModelProperty(value = "最高职称获取时间")
    private String assessmentDate;
    
    @Transient
    @ApiModelProperty(value = "家庭住址")
    private String address;
    
    @Transient
    @ApiModelProperty(value = "手机号码")
    private String phoneNumber;
    
    @Transient
    @ApiModelProperty(value = "籍贯")
    private String birthplace;
    
    @Transient
    @ApiModelProperty(value = "编制类型")
    private String establishmentType;
    
    @Transient
    @ApiModelProperty(value = "政治面貌")
    private List<String> politicalStatusList;
}
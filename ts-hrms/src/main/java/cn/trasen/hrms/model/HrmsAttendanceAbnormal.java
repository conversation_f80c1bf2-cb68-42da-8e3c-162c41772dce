package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 
  * <P> @Description: 考勤异常</p>
  * <P> @Date: 2020年3月26日  下午4:49:24 </p>
  * <P> @Author: panqic </p>
  * <P> @Company: 湖南爱笑恩信息科技有限公司 </p>
  * <P> @version V1.0    </p>
 */
@Table(name = "hrms_attendance_abnormal")
@Setter
@Getter
public class HrmsAttendanceAbnormal {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 考勤日期
     */
    @Column(name = "attendance_date")
    @ApiModelProperty(value = "考勤日期")
    private Date attendanceDate;

    /**
     * 员工工号
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    /**
     * 科室
     */
    @ApiModelProperty(value = "科室")
    private String department;

    /**
     * 签到时间
     */
    @Column(name = "sign_time")
    @ApiModelProperty(value = "签到时间")
    private Date signTime;

    /**
     * 签到地点
     */
    @Column(name = "sign_place")
    @ApiModelProperty(value = "签到地点")
    private String signPlace;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 考勤来源
     */
    @Column(name = "attendance_source")
    @ApiModelProperty(value = "考勤来源")
    private String attendanceSource;

    /**
     * 迟到时长
     */
    @Column(name = "late_duration")
    @ApiModelProperty(value = "迟到时长")
    private String lateDuration;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人ID")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 科室ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "科室ID")
    private String orgId;

    /**
     * 科室名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "科室名称")
    private String orgName;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;
}
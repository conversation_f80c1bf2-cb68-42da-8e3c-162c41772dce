package cn.trasen.hrms.model;

import io.swagger.annotations.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import lombok.*;

/**
 * 预警提醒设置表
 *
 */
@Table(name = "hrms_warning_setting")
@Setter
@Getter
public class HrmsWarningSetting {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    private String id;

    /**
     * 预警标题
     */
    @Column(name = "warning_title")
    @ApiModelProperty(value = "预警标题")
    private String warningTitle;

    /**
     * 预警类型  1到期提醒  2到龄提醒
     */
    @Column(name = "warning_type")
    @ApiModelProperty(value = "预警类型 ")
    private String warningType;

    /**
     * 提前预警天数
     */
    @Column(name = "advance_days")
    @ApiModelProperty(value = "提前预警天数")
    private Integer advanceDays;

    /**
     * 指定条件
     */
    @ApiModelProperty(value = "指定条件")
    private String conditions;

    /**
     * 指定条件数值
     */
    @Column(name = "conditions_numbers")
    @ApiModelProperty(value = "指定条件数值")
    private Integer conditionsNumbers;

    /**
     * 预警周期
     */
    @Column(name = "warning_cycle")
    @ApiModelProperty(value = "预警周期")
    private String warningCycle;

    /**
     * 周期时间
     */
    @Column(name = "cycle_date")
    @ApiModelProperty(value = "周期时间")
    private String cycleDate;
    
    @Column(name = "cycle_date_time")
    @ApiModelProperty(value = "周期时间")
    private String cycleDateTime;
    

    /**
     * 通知人
     */
    @Column(name = "notice_user")
    @ApiModelProperty(value = "通知人")
    private String noticeUser;

    /**
     * 通知人名称
     */
    @Column(name = "notice_user_name")
    @ApiModelProperty(value = "通知人名称")
    private String noticeUserName;

    /**
     * 通知方式  1短信  2微信
     */
    @Column(name = "notice_type")
    @ApiModelProperty(value = "通知方式  1短信  2微信")
    private String noticeType;

    /**
     * 通知模板
     */
    @Column(name = "notice_template")
    @ApiModelProperty(value = "通知模板")
    private String noticeTemplate;

    /**
     * 启动状态  1正常  2停用
     */
    @ApiModelProperty(value = "启动状态  1正常  2停用")
    private String status;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "is_deleted")
    private String isDeleted;
    
    @Transient
    private List<HrmsWarningCompare> hrmsWarningCompareList;

    /**
     * 仅人事预警设置页面列表使用
     */
    @Transient
    private String pid; //设置页面树结构数据

    @Transient
    private String name; //设置页面树结构数据

    @Transient
    private List<HrmsWarningSetting> children = new ArrayList<>();
}
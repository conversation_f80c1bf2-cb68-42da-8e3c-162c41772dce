package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_leave_report_operation_record")
@Setter
@Getter
public class HrmsLeaveReportOperationRecord {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    private String id;
    
    /**
     * 请假人
     */
    @Column(name = "employee_code")
    @ApiModelProperty(value = "请假人")
    private String employeeCode;

    /**
     * 请假人名称
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "请假人名称")
    private String employeeName;

    @Column(name = "leave_month")
    private String leaveMonth;

    @Column(name = "leave_type")
    private String leaveType;

    @Column(name = "befor_days")
    private BigDecimal beforDays;

    @Column(name = "after_days")
    private BigDecimal afterDays;

    private String remark;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;
}
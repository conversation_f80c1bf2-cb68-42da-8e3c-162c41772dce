package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_scheduling_grouping_emp")
@Setter
@Getter
public class HrmsSchedulingGroupingEmp {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 考勤组id
     */
    @Id
    @Column(name = "frequency_grouping_emp_id")
    @ApiModelProperty(value = "考勤组id")
    private String frequencyGroupingEmpId;

    /**
     * 姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "姓名")
    private String employeeName;
    
    /**
     * 姓名
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "工号")
    private String employeeNo;

    /**
     * 员工id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    /**
     * 考勤组id
     */
    @Column(name = "frequency_grouping_id")
    @ApiModelProperty(value = "考勤组id")
    private String frequencyGroupingId;
    

    /**
     * 考勤组名称
     */
    @Transient
    @ApiModelProperty(value = "考勤组名称")
    private String frequencyGroupingName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
   
    @Column(name = "org_id")
    @ApiModelProperty(value = "考勤组所属科室id")
    private String orgId;
    
    @Transient
    @ApiModelProperty(value = "老考勤组id")
    private String oldGroupingId;
    
    @Transient
    @ApiModelProperty(value = "新考勤组id")
    private String newGroupingId;
    
    @Transient
    @ApiModelProperty(value = "岗位名称")
    private String personalIdentity; //岗位名称
    
    @Transient
    @ApiModelProperty(value = "排序号")
    private Integer sort; //排序号
    
    @Transient
    private String searchStartDate;
    @Transient
    private String searchEndDate;
    
    @Transient
    private String movementType;  //请求类型 1排班  2 查看
    @Transient
    private String phone;
}
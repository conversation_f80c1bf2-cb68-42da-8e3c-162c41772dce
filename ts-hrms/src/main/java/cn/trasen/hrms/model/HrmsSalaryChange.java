package cn.trasen.hrms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**   
 * @ClassName:  HrmsSalaryChange   
 * @Description:薪酬项目改变表 
 * @author: WZH
 * @date:   2021年10月28日 下午3:32:43      
 * @Copyright:  
 */
@Table(name = "hrms_salary_change")
@Setter
@Getter
public class HrmsSalaryChange {
	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "salary_change_id")
	@ApiModelProperty(value = "主键ID")
	private String salaryChangeId;

	/**
	 * 员工ID
	 */
	@Column(name = "employee_id")
	@ApiModelProperty(value = "员工ID")
	private String employeeId;
	
	/**
	 * 变更时间
	 */
	@Column(name = "salary_change_time")
	@ApiModelProperty(value = "变更时间")
	private String salaryChangeTime;
	
	/**
	 * 变更项目编码
	 */
	@Column(name = "salary_item_code")
	@ApiModelProperty(value = "变更项目编码")
	private String salaryItemCode;
	
	/**
	 * 变更项目名称
	 */
	@Column(name = "salary_item_name")
	@ApiModelProperty(value = "变更项目名称")
	private String salaryItemName;
	
	/**
	 * 改变前的值
	 */
	@Column(name = "salary_change_old")
	@ApiModelProperty(value = "改变前的值")
	private BigDecimal salaryChangeOld;
	
	/**
	 * 改变后的值
	 */
	@Column(name = "salary_change_new")
	@ApiModelProperty(value = "改变后的值")
	private BigDecimal salaryChangeNew;
	


	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;
	
    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

}
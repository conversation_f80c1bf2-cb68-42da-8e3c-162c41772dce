package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "hrms_research_topic")
@Setter
@Getter
public class HrmsResearchTopic {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    private String id;

    /**
     * 项目来源
     */
    @Column(name = "project_source")
    @ApiModelProperty(value = "项目来源")
    private String projectSource;

    /**
     * 项目编号
     */
    @Column(name = "project_number")
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;

    /**
     * 课题名称
     */
    @Column(name = "topic_name")
    @ApiModelProperty(value = "课题名称")
    private String topicName;

    /**
     * 开始时间
     */
    @Column(name = "start_time")
    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date endTime;

    /**
     * 负责人编号
     */
    @Column(name = "handler_user")
    @ApiModelProperty(value = "负责人编号")
    private String handlerUser;

    /**
     * 负责人名称
     */
    @Column(name = "handler_user_name")
    @ApiModelProperty(value = "负责人名称")
    private String handlerUserName;

    /**
     * 参与人编号
     */
    @Column(name = "participate_user")
    @ApiModelProperty(value = "参与人编号")
    private String participateUser;

    /**
     * 参与人名称
     */
    @Column(name = "participate_user_name")
    @ApiModelProperty(value = "参与人名称")
    private String participateUserName;

    /**
     * 资助金额
     */
    @ApiModelProperty(value = "资助金额")
    private BigDecimal grants;

    /**
     * 配套金额
     */
    @ApiModelProperty(value = "配套金额")
    private BigDecimal amount;

    /**
     * 填报合计金额
     */
    @Column(name = "total_price")
    @ApiModelProperty(value = "填报合计金额")
    private BigDecimal totalPrice;

    /**
     * 使用合计金额
     */
    @Column(name = "total_price_use")
    @ApiModelProperty(value = "使用合计金额")
    private BigDecimal totalPriceUse;

    /**
     * 状态 1进行中 2已完结
     */
    @ApiModelProperty(value = "状态 1进行中 2已完结")
    private Integer status;

    /**
     * 办结时间
     */
    @Column(name = "finish_time")
    @ApiModelProperty(value = "办结时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date finishTime;

    /**
     * 办结附件id
     */
    @Column(name = "finish_files_id")
    @ApiModelProperty(value = "办结附件id")
    private String finishFilesId;

    @Column(name = "create_date")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "update_date")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date updateDate;

    @Column(name = "is_deleted")
    private String isDeleted;
    
    /**
     * 办结说明
     */
    @Column(name = "finish_remark")
    @ApiModelProperty(value = "办结说明")
    private String finishRemark;
    
    @Transient
    private String isResearchAdmin;
    
    @Transient
    private String timeProgress;
    
    @Transient
    private Integer dataRefTable;
    
    @Transient
    private Integer pm;
    
    @Transient
    private BigDecimal surplusPrice;
    
    @Transient
    private List<HrmsResearchProgress> progressList;
    
    @Transient
    private List<HrmsResearchCost> costList;
    
    @Transient
    private String monthVal;
    
    @Transient
    private Integer urgeNumber;//催办次数
    
    @Transient
    @ApiModelProperty(value = "催办内容")
    private String urgeProcessingContent;
    
}
package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "cust_emp_info")
@Setter
@Getter
public class CustEmpInfo {
    /**
     * 员工id
     */
    @Id
    @Column(name = "info_id")
    @ApiModelProperty(value = "员工id")
    private String infoId;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    private String nationality;

    /**
     * 政治面貌
     */
    @Column(name = "political_status")
    @ApiModelProperty(value = "政治面貌")
    private String politicalStatus;

    /**
     * 入党时间
     */
    @Column(name = "party_date")
    @ApiModelProperty(value = "入党时间")
    private String partyDate;

    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    private String birthplace;

    /**
     * 血型
     */
    @Column(name = "blood_group")
    @ApiModelProperty(value = "血型")
    private String bloodGroup;

    /**
     * 现住址
     */
    @ApiModelProperty(value = "现住址")
    private String address;

    /**
     * 出生地址编码
     */
    @Column(name = "born_address")
    @ApiModelProperty(value = "出生地址编码")
    private String bornAddress;

    /**
     * 出生地址名称
     */
    @Column(name = "born_address_name")
    @ApiModelProperty(value = "出生地址名称")
    private String bornAddressName;

    /**
     * 户籍地址
     */
    @Column(name = "residence_address")
    @ApiModelProperty(value = "户籍地址")
    private String residenceAddress;

    /**
     * 紧急联系人
     */
    @Column(name = "emergency_contact")
    @ApiModelProperty(value = "紧急联系人")
    private String emergencyContact;

    /**
     * 紧急联系电话
     */
    @Column(name = "emergency_tel")
    @ApiModelProperty(value = "紧急联系电话")
    private String emergencyTel;

    /**
     * 员工职称ID
     */
    @Column(name = "emp_title_id")
    @ApiModelProperty(value = "员工职称ID")
    private String empTitleId;

    /**
     * 员工职称名称
     */
    @Column(name = "emp_title_name")
    @ApiModelProperty(value = "员工职称名称")
    private String empTitleName;

    /**
     * 健康状况
     */
    @Column(name = "health_status")
    @ApiModelProperty(value = "健康状况")
    private String healthStatus;

    /**
     * 身份证附件
     */
    @Column(name = "id_card_file")
    @ApiModelProperty(value = "身份证附件")
    private String idCardFile;

    /**
     * 婚姻状况
     */
    @Column(name = "marriage_status")
    @ApiModelProperty(value = "婚姻状况")
    private String marriageStatus;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String postcode;

    /**
     * 最高学历
     */
    @Column(name = "first_education_type")
    @ApiModelProperty(value = "最高学历")
    private String firstEducationType;

    /**
     * 入校时间
     */
    @ApiModelProperty(value = "入校时间")
    private String rxsj;

    /**
     * 毕业时间
     */
    @ApiModelProperty(value = "毕业时间")
    private String bysj;

    /**
     * 拟定预备党员日期
     */
    @Column(name = "prepare_party_date")
    @ApiModelProperty(value = "拟定预备党员日期")
    private String preparePartyDate;

    /**
     * 曾用名
     */
    @Column(name = "used_name")
    @ApiModelProperty(value = "曾用名")
    private String usedName;

    /**
     * 参加工作时间
     */
    @Column(name = "work_start_date")
    @ApiModelProperty(value = "参加工作时间")
    private String workStartDate;

    /**
     * 学信网电子注册备案
     */
    @Column(name = "xuexin_net_file")
    @ApiModelProperty(value = "学信网电子注册备案")
    private String xuexinNetFile;

    /**
     * 编制类别
     */
    @Column(name = "employee_category")
    @ApiModelProperty(value = "编制类别")
    private String employeeCategory;

    /**
     * 编制类型
     */
    @Column(name = "establishment_type")
    @ApiModelProperty(value = "编制类型")
    private String establishmentType;

    /**
     * 编制所属机构
     */
    @Column(name = "authorized_org")
    @ApiModelProperty(value = "编制所属机构")
    private String authorizedOrg;

    /**
     * 连续工龄
     */
    @ApiModelProperty(value = "连续工龄")
    private String bdwlxgl;

    /**
     * 入职文件
     */
    @Column(name = "business_id")
    @ApiModelProperty(value = "入职文件")
    private String businessId;

    /**
     * 医师执业资格证
     */
    @Column(name = "doctor_qualification_certificate")
    @ApiModelProperty(value = "医师执业资格证")
    private String doctorQualificationCertificate;

    /**
     * 考勤科室
     */
    @Column(name = "check_work_depart")
    @ApiModelProperty(value = "考勤科室")
    private String checkWorkDepart;

    /**
     * 兼任职务
     */
    @Column(name = "concurrent_position")
    @ApiModelProperty(value = "兼任职务")
    private String concurrentPosition;

    /**
     * 兼任职务时间
     */
    @Column(name = "concurrent_position_time")
    @ApiModelProperty(value = "兼任职务时间")
    private String concurrentPositionTime;

    /**
     * 聘任职务
     */
    @Column(name = "employ_duty")
    @ApiModelProperty(value = "聘任职务")
    private String employDuty;

    /**
     * 聘任日期
     */
    @Column(name = "employ_duty_date")
    @ApiModelProperty(value = "聘任日期")
    private Date employDutyDate;

    /**
     * 任现职年限
     */
    @Column(name = "employ_duty_duration")
    @ApiModelProperty(value = "任现职年限")
    private Integer employDutyDuration;

    /**
     * 任同职级时间
     */
    @Column(name = "employ_duty_equally_date")
    @ApiModelProperty(value = "任同职级时间")
    private Date employDutyEquallyDate;

    /**
     * 现职务任职时间
     */
    @Column(name = "job_deion_type_time")
    @ApiModelProperty(value = "现职务任职时间")
    private Date jobDeionTypeTime;

    /**
     * 兼任职务时间
     */
    @ApiModelProperty(value = "兼任职务时间")
    private String jrzwsj;

    /**
     * 聘任附件
     */
    @ApiModelProperty(value = "聘任附件")
    private String prfj;

    /**
     * 聘任该职务年限
     */
    @ApiModelProperty(value = "聘任该职务年限")
    private String prlx;

    /**
     * 离职原因
     */
    @Column(name = "fire_reason")
    @ApiModelProperty(value = "离职原因")
    private String fireReason;

    /**
     * 擅长
     */
    @Column(name = "good_at")
    @ApiModelProperty(value = "擅长")
    private String goodAt;

    /**
     * 是否退休过，1退休过
     */
    @Column(name = "is_retire")
    @ApiModelProperty(value = "是否退休过，1退休过")
    private String isRetire;

    /**
     * 是否重复入职
     */
    @Column(name = "is_duplicate_entry")
    @ApiModelProperty(value = "是否重复入职")
    private String isDuplicateEntry;

    /**
     * 是否领导
     */
    @Column(name = "is_leader")
    @ApiModelProperty(value = "是否领导")
    private String isLeader;

    /**
     * 是否退伍军人
     */
    @Column(name = "is_veteran")
    @ApiModelProperty(value = "是否退伍军人")
    private String isVeteran;

    /**
     * 部队名称
     */
    @Column(name = "unit_name")
    @ApiModelProperty(value = "部队名称")
    private String unitName;

    /**
     * 岗位描述
     */
    @Column(name = "job_deion_type")
    @ApiModelProperty(value = "岗位描述")
    private String jobDeionType;

    /**
     * 助产士
     */
    @ApiModelProperty(value = "助产士")
    private String midwife;

    /**
     * 岗位ID
     */
    @Column(name = "post_id")
    @ApiModelProperty(value = "岗位ID")
    private String postId;

    /**
     * 编制号
     */
    @Column(name = "preparation_no")
    @ApiModelProperty(value = "编制号")
    private String preparationNo;

    /**
     * 离职日期
     */
    @Column(name = "quit_date")
    @ApiModelProperty(value = "离职日期")
    private Date quitDate;

    /**
     * 延聘日期
     */
    @Column(name = "reemployment_date")
    @ApiModelProperty(value = "延聘日期")
    private Date reemploymentDate;

    /**
     * 退休日期
     */
    @Column(name = "retire_date")
    @ApiModelProperty(value = "退休日期")
    private Date retireDate;

    /**
     * 退休日期
     */
    @Column(name = "retirement_time")
    @ApiModelProperty(value = "退休日期")
    private String retirementTime;

    /**
     * 审核科室
     */
    @Column(name = "review_depart")
    @ApiModelProperty(value = "审核科室")
    private String reviewDepart;

    /**
     * 任现职年限
     */
    @ApiModelProperty(value = "任现职年限")
    private String rtjnx;

    /**
     * 任同职级时间
     */
    @ApiModelProperty(value = "任同职级时间")
    private String rtzsj;

    /**
     * 是否规培人员
     */
    @ApiModelProperty(value = "是否规培人员")
    private String shifouguipeirenyuan;

    /**
     * 是否规培人员
     */
    @Column(name = "compliance_training")
    @ApiModelProperty(value = "是否规培人员")
    private String complianceTraining;

    /**
     * 是否杏林人才
     */
    @ApiModelProperty(value = "是否杏林人才")
    private String shifouxinglinrencai;

    /**
     * 是否中层干部
     */
    @ApiModelProperty(value = "是否中层干部")
    private String shifouzhongcengganbu;

    /**
     * 是否专业技术人员
     */
    @Column(name = "work_nature")
    @ApiModelProperty(value = "是否专业技术人员")
    private String workNature;

    /**
     * 是否专业英才
     */
    @ApiModelProperty(value = "是否专业英才")
    private String zhuanyeyingcai;

    /**
     * 起聘时间
     */
    @Column(name = "start_employ_date")
    @ApiModelProperty(value = "起聘时间")
    private String startEmployDate;

    /**
     * 终聘时间
     */
    @Column(name = "end_employ_date")
    @ApiModelProperty(value = "终聘时间")
    private String endEmployDate;

    /**
     * 合同到期
     */
    @Column(name = "is_hetongdaoqi")
    @ApiModelProperty(value = "合同到期")
    private String isHetongdaoqi;

    /**
     * 人员去向
     */
    @Column(name = "work_status")
    @ApiModelProperty(value = "人员去向")
    private String workStatus;

    /**
     * 存档地址
     */
    @Column(name = "archive_address")
    @ApiModelProperty(value = "存档地址")
    private String archiveAddress;

    /**
     * 系数
     */
    @ApiModelProperty(value = "系数")
    private String coefficient;

    /**
     * 个人简介
     */
    @Column(name = "personal_profile")
    @ApiModelProperty(value = "个人简介")
    private String personalProfile;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 岗位职责
     */
    @Column(name = "GWZZ")
    @ApiModelProperty(value = "岗位职责")
    private String gwzz;

    /**
     * 座机号码
     */
    @Column(name = "landline_number")
    @ApiModelProperty(value = "座机号码")
    private String landlineNumber;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String lianxiren;

    /**
     * 中层干部首次任职时间
     */
    @ApiModelProperty(value = "中层干部首次任职时间")
    private String zcgbscrzsj;

    /**
     * 首次注册时间
     */
    @Column(name = "operation_date")
    @ApiModelProperty(value = "首次注册时间")
    private Date operationDate;

    /**
     * 执业证书编号
     */
    @Column(name = "operation_number")
    @ApiModelProperty(value = "执业证书编号")
    private String operationNumber;

    /**
     * 注册机构
     */
    @Column(name = "operation_org")
    @ApiModelProperty(value = "注册机构")
    private String operationOrg;

    /**
     * 执业范围
     */
    @Column(name = "operation_scope")
    @ApiModelProperty(value = "执业范围")
    private String operationScope;

    /**
     * 执业类别
     */
    @Column(name = "operation_type")
    @ApiModelProperty(value = "执业类别")
    private String operationType;

    /**
     * 执业附件
     */
    @ApiModelProperty(value = "执业附件")
    private String zyfj;

    /**
     * 本单位开始工作日期
     */
    @Column(name = "unit_start_date")
    @ApiModelProperty(value = "本单位开始工作日期")
    private Date unitStartDate;

    /**
     * 开户行
     */
    @ApiModelProperty(value = "开户行")
    private String bankcardname;

    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "银行卡号")
    private String bankcardno;

    /**
     * 公积金购买日期
     */
    @Column(name = "buy_provident_date")
    @ApiModelProperty(value = "公积金购买日期")
    private String buyProvidentDate;

    /**
     * 社保购买日期
     */
    @Column(name = "buy_social_date")
    @ApiModelProperty(value = "社保购买日期")
    private String buySocialDate;

    /**
     * 岗位等级
     */
    @ApiModelProperty(value = "岗位等级")
    private String gwdj;

    /**
     * 提高10%标识(平江用于是否计算提高10%工资)
     */
    @Column(name = "improve_flag")
    @ApiModelProperty(value = "提高10%标识(平江用于是否计算提高10%工资)")
    private String improveFlag;

    /**
     * 岗位类别
     */
    @ApiModelProperty(value = "岗位类别")
    private String plgw;

    /**
     * 岗位薪资
     */
    @Column(name = "post_wage")
    @ApiModelProperty(value = "岗位薪资")
    private String postWage;

    /**
     * 试用期薪资
     */
    @Column(name = "probation_salary")
    @ApiModelProperty(value = "试用期薪资")
    private String probationSalary;

    /**
     * 转正薪资
     */
    @Column(name = "regular_salary")
    @ApiModelProperty(value = "转正薪资")
    private String regularSalary;

    /**
     * 是否定薪
     */
    @Column(name = "salary_appoint")
    @ApiModelProperty(value = "是否定薪")
    private Integer salaryAppoint;

    /**
     * 薪级ID
     */
    @Column(name = "salary_level_id")
    @ApiModelProperty(value = "薪级ID")
    private String salaryLevelId;

    /**
     * 薪资类别
     */
    @Column(name = "salary_level_type")
    @ApiModelProperty(value = "薪资类别")
    private String salaryLevelType;

    /**
     * 薪级工资
     */
    @Column(name = "salary_level_wage")
    @ApiModelProperty(value = "薪级工资")
    private String salaryLevelWage;

    /**
     * 薪资福利说明
     */
    @Column(name = "salary_remark")
    @ApiModelProperty(value = "薪资福利说明")
    private String salaryRemark;

    /**
     * 升级标识(平江用于年度统一调薪的判断依据)
     */
    @Column(name = "upgrade_flag")
    @ApiModelProperty(value = "升级标识(平江用于年度统一调薪的判断依据)")
    private String upgradeFlag;

    /**
     * 测试时间
     */
    @ApiModelProperty(value = "测试时间")
    private String cssj;

    /**
     * 院外地址
     */
    @ApiModelProperty(value = "院外地址")
    private String wydz;

    /**
     * 测试个人字段
     */
    @ApiModelProperty(value = "测试个人字段")
    private String csgrzd;

    /**
     * 测试123
     */
    @ApiModelProperty(value = "测试123")
    private String test123;

    /**
     * 测试
     */
    @ApiModelProperty(value = "测试")
    private String cs1;

    /**
     * 测试1
     */
    @ApiModelProperty(value = "测试1")
    private String css;
}
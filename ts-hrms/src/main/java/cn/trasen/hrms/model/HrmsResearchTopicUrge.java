package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_research_topic_urge")
@Setter
@Getter
public class HrmsResearchTopicUrge {
    /**
     * 主键
     */
	@Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 课题id
     */
    @Column(name = "topic_id")
    @ApiModelProperty(value = "课题id")
    private String topicId;

    /**
     * 催办内容
     */
    @Column(name = "urge_processing_content")
    @ApiModelProperty(value = "催办内容")
    private String urgeProcessingContent;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "is_deleted")
    private String isDeleted;
}
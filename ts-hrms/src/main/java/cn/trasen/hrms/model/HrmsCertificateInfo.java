package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "hrms_certificate_info")
@Setter
@Getter
public class HrmsCertificateInfo {

	@Column(name = "sso_org_code")
	private String ssoOrgCode;
	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "certificate_info_id")
	@ApiModelProperty(value = "主键ID")
	private String certificateInfoId;

	/**
	 * 员工ID
	 */
	@Column(name = "employee_id")
	@ApiModelProperty(value = "员工ID")
	private String employeeId;

	/**
	 * 员工姓名
	 */
	@Column(name = "employee_name")
	@ApiModelProperty(value = "员工姓名")
	private String employeeName;
	
	/**
	 * 员工工号
	 */
	@Column(name = "employee_no")
	@ApiModelProperty(value = "员工工号")
	private String employeeNo;
	
	/**
	 * 证书编号
	 */
	@Column(name = "certificate_number")
	@ApiModelProperty(value = "证书编号")
	private String certificateNumber;

	/**
	 * 证书名称
	 */
	@Column(name = "certificate_name")
	@ApiModelProperty(value = "证书名称")
	private String certificateName;

	/**
	 * 证书类型
	 */
	@Column(name = "certificate_type")
	@ApiModelProperty(value = "证书类型")
	private String certificateType;

	/**
	 * 证书机构
	 */
	@Column(name = "certificate_agency")
	@ApiModelProperty(value = "证书机构")
	private String certificateAgency;

	/**
	 * 获取途径
	 */
	@Column(name = "accept_method")
	@ApiModelProperty(value = "获取途径")
	private String acceptMethod;

	/**
	 * 获得证书日期
	 */
	@Column(name = "acquisition_date")
	@ApiModelProperty(value = "获得证书日期")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date acquisitionDate;

	/**
	 * 备注
	 */
	@Column(name = "remark")
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;
	
	/**
	 * 审批标识: 0=待审批 1=审批通过;
	 */
	@Column(name = "approval_status")
	@ApiModelProperty(value = "审批标识: 0=待审批 4=审批通过")
	private String approvalStatus;
	

	// ------- 扩展字段 ------- //
	/**
	 * 证书类型文本值
	 */
	@Transient
	@ApiModelProperty(value = "证书类型文本值")
	private String certificateTypeText;

	/**
	 * 获取途径文本值
	 */
	@Transient
	@ApiModelProperty(value = "获取途径文本值")
	private String acceptMethodText;
	
	/**
	 * 获取审批文本值
	 */
	@Transient
	@ApiModelProperty(value = "获取审批文本值")
	private String approvalStatusText;
	  
    /**
     * 附件Id
     */
    @Transient
    @ApiModelProperty(value = "附件Id")
    private String businessId;

    @Transient
    private String htOrgIdList;

}
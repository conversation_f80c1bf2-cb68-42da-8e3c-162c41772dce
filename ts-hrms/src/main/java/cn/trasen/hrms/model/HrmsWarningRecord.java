package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.*;

/**
 * 预警记录表
 *
 */
@Table(name = "hrms_warning_record")
@Setter
@Getter
public class HrmsWarningRecord {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 员工编号
     */
    @Column(name = "emp_code")
    @ApiModelProperty(value = "员工编号")
    private String empCode;
    
    /**
     * 员工编号
     */
    @Column(name = "emp_id")
    @ApiModelProperty(value = "员工编号")
    private String empId;

    /**
     * 员工姓名
     */
    @Column(name = "emp_name")
    @ApiModelProperty(value = "员工姓名")
    private String empName;
    
    @Column(name = "emp_name_pinyin")
    @ApiModelProperty(value = "员工拼音")
    private String empNamePinyin;

    /**
     * 联系电话
     */
    @Column(name = "emp_phone")
    @ApiModelProperty(value = "联系电话")
    private String empPhone;

    /**
     * 员工所属部门编号
     */
    @Column(name = "emp_dept")
    @ApiModelProperty(value = "员工所属部门编号")
    private String empDept;

    /**
     * 员工所属部门名称
     */
    @Column(name = "emp_dept_name")
    @ApiModelProperty(value = "员工所属部门名称")
    private String empDeptName;

    /**
     * 员工性别
     */
    @Column(name = "emp_sex")
    @ApiModelProperty(value = "员工性别")
    private String empSex;

    /**
     * 预警名称
     */
    @Column(name = "warning_title")
    @ApiModelProperty(value = "预警名称")
    private String warningTitle;

    /**
     * 到期时间
     */
    @Column(name = "due_date")
    @ApiModelProperty(value = "到期时间")
    private String dueDate;

    /**
     * 剩余时间
     */
    @Column(name = "surplus_date")
    @ApiModelProperty(value = "剩余时间")
    private String surplusDate;
    
    @Column(name = "setting_id")
    @ApiModelProperty(value = "设置id")
    private String settingId;
    

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 逾期天数
     */
    @Column(name = "overdue_days")
    @ApiModelProperty(value = "逾期天数")
    private String overdueDays;

    /**
     * 状态 0-提醒中 1-已提醒 2-已处理
     */
    @Column(name = "warning_status")
    @ApiModelProperty(value = "状态 0-提醒中 1-已提醒 2-已处理")
    private String warningStatus;

    /**
     * 备注
     */
    @Column(name = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @Transient
    private Integer pm;
    
    @Transient
    private String sidx;
    
    @Transient
    private String sord;

    private List<String> warningStatusList;
}
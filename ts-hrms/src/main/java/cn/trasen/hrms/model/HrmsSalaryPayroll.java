package cn.trasen.hrms.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Table(name = "hrms_salary_payroll")
@Setter
@Getter
@ToString
public class HrmsSalaryPayroll {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "salary_payroll_id")
	@ApiModelProperty(value = "主键ID")
	private String salaryPayrollId;

	/**
	 * 薪酬方案ID
	 */
	@Column(name = "salary_plan_id")
	@ApiModelProperty(value = "薪酬方案ID")
	private String salaryPlanId;

	/**
	 * 员工ID
	 */
	@Column(name = "employee_id")
	@ApiModelProperty(value = "员工ID")
	private String employeeId;

	/**
	 * 工资发放日期
	 */
	@Column(name = "payroll_date")
	@ApiModelProperty(value = "工资发放日期")
	@JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
	private String payrollDate;

	/**
	 * 发送状态: 1=未发送; 2=已发送; 3=撤回;
	 */
	@Column(name = "send_status")
	@ApiModelProperty(value = "发送状态: 1=未发送; 2=已发送; 3=撤回;")
	private String sendStatus;

	/**
	 * 发送时间
	 */
	@Column(name = "send_time")
	@ApiModelProperty(value = "发送时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date sendTime;

	/**
	 * 发送方式
	 */
	@Column(name = "send_method")
	@ApiModelProperty(value = "发送方式")
	private String sendMethod;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 发送状态文本值
	 */
	@Transient
	@ApiModelProperty(value = "发送状态文本值")
	private String sendStatusText;
	/**
	 * 员工工号
	 */
	@Transient
	@ApiModelProperty(value = "员工工号")
	private String employeeNo;

	/**
	 * 员工姓名
	 */
	@Transient
	@ApiModelProperty(value = "员工姓名")
	private String employeeName;

	/**
	 * 员工类别
	 */
	@Transient
	@ApiModelProperty(value = "员工类别")
	private String employeeCategory;

	/**
	 * 组织机构名称
	 */
	@Transient
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 手机号码
	 */
	@Transient
	@ApiModelProperty(value = "手机号码")
	private String phoneNumber;

	/**
	 * 邮箱
	 */
	@Transient
	@ApiModelProperty(value = "邮箱")
	private String email;

	/**
	 * 岗位名称
	 */
	@Transient
	@ApiModelProperty(value = "岗位名称")
	private String postName;

	/**
	 * 薪级名称
	 */
	@Transient
	@ApiModelProperty(value = "薪级名称")
	private String salaryLevelName;

	/**
	 * 组织机构ID集合-权限使用
	 */
	@Transient
	@ApiModelProperty(value = "组织机构ID集合-权限使用")
	private List<String> orgIdList;

	/**
	 * 用户Code-权限使用
	 */
	@Transient
	@ApiModelProperty(value = "用户Code-权限使用")
	private String userCode;
	
	@Transient
	@ApiModelProperty(value = "职称登记")
	private String jobTitleLevel;
	
	@Transient
	@ApiModelProperty(value = "职称名称")
	private String jobTitleName;
	
	@Transient
	private String payrollDateStr;
	
	@Transient
	@ApiModelProperty(value = "编制类型")
	private String establishmentType;
	
	@Transient
	@ApiModelProperty(value = "员工状态")
	private String employeeStatus;
	
	@Transient
	@ApiModelProperty(value = "员工状态")
	private String isDetails;
	
	@Transient
	@ApiModelProperty(value = "岗位类别")
	private String personalIdentity;  //岗位类别是护士的 算护龄工资
	
	@Transient
	@ApiModelProperty(value = "岗位类别名称")
	private String personalIdentityText;  //岗位类别是护士的 算护龄工资
	
	
	
}
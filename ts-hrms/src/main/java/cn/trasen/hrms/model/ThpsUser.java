package cn.trasen.hrms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "thps_user")
@Setter
@Getter
public class ThpsUser {
	/**
	 * 唯一标示 （ceshi）
	 */
	@Id
	@ApiModelProperty(value = "唯一标示")
	private String id;

	/**
	 * 机构编码
	 */
	@ApiModelProperty(value = "机构编码")
	private String corpcode;

	/**
	 * 登录帐号
	 */
	@ApiModelProperty(value = "登录帐号")
	private String usercode;

	/**
	 * 登录用户名
	 */
	@ApiModelProperty(value = "登录用户名")
	private String username;

	/**
	 * 部门编码
	 */
	@ApiModelProperty(value = "部门编码")
	private String deptcode;

	/**
	 * 密码
	 */
	@ApiModelProperty(value = "密码")
	private String password;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private Integer status;

	/**
	 * 登录时间
	 */
	@ApiModelProperty(value = "登录时间")
	private Date logintime;

	/**
	 * 登出时间
	 */
	@ApiModelProperty(value = "登出时间")
	private Date logouttime;

	/**
	 * 每次操作激活时间
	 */
	@ApiModelProperty(value = "每次操作激活时间")
	private Date activetime;

	/**
	 * 备忘记录关联id
	 */
	@ApiModelProperty(value = "备忘记录关联id")
	private String remarkid;

	/**
	 * 登录IP
	 */
	@ApiModelProperty(value = "登录IP")
	private String loginip;

	/**
	 * email帐户
	 */
	@ApiModelProperty(value = "email帐户")
	private String emailaccount;

	/**
	 * 邮箱密码
	 */
	@ApiModelProperty(value = "邮箱密码")
	private String emailpassword;

	/**
	 * 邮箱地址
	 */
	@ApiModelProperty(value = "邮箱地址")
	private String emailaddress;

	/**
	 * 外网pop3
	 */
	@ApiModelProperty(value = "外网pop3")
	private String emailpop3;

	/**
	 * 外网smtp
	 */
	@ApiModelProperty(value = "外网smtp")
	private String emailsmtp;

	/**
	 * 邮件标题
	 */
	@ApiModelProperty(value = "邮件标题")
	private String emailsubject;

	/**
	 * 代理人
	 */
	@ApiModelProperty(value = "代理人")
	private String agentcode;

	/**
	 * 代理人开始时间
	 */
	@ApiModelProperty(value = "代理人开始时间")
	private Date agentstartdatetime;

	/**
	 * 代理人结束时间
	 */
	@ApiModelProperty(value = "代理人结束时间")
	private Date agentenddatetime;

	/**
	 * 微信账号
	 */
	@ApiModelProperty(value = "微信账号")
	private String wxcode;

	/**
	 * 员工编号
	 */
	@ApiModelProperty(value = "员工编号")
	private String hrcode;

	/**
	 * 系统自动退出时间间隔
	 */
	@ApiModelProperty(value = "系统自动退出时间间隔")
	private Integer autoexittime;

	/**
	 * 登录次数
	 */
	@ApiModelProperty(value = "登录次数")
	private Integer logins;

	/**
	 * 创建人员
	 */
	@ApiModelProperty(value = "创建人员")
	private String appuser;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date apptime;

	/**
	 * 更新修改人员
	 */
	@ApiModelProperty(value = "更新修改人员")
	private String updateuser;

	/**
	 * 最后修改时间
	 */
	@ApiModelProperty(value = "最后修改时间")
	private Date updatetime;

	/**
	 * 直接领导
	 */
	@ApiModelProperty(value = "直接领导")
	private String dleader;

	/**
	 * 职务代码
	 */
	@Column(name = "dutyCode")
	@ApiModelProperty(value = "职务代码")
	private String dutycode;

	/**
	 * 登录会话ID
	 */
	@ApiModelProperty(value = "登录会话ID")
	private String sessionid;

	private String usericon;

	/**
	 * 令牌
	 */
	@ApiModelProperty(value = "令牌")
	private String token;

	/**
	 * 旧系统密码
	 */
	@ApiModelProperty(value = "旧系统密码")
	private String oldpassword;

	/**
	 * 旧系统帐户
	 */
	@ApiModelProperty(value = "旧系统帐户")
	private String oldusercode;

	private String sex;

	private String wxuserid;

	private String wxdepartment;

	private String mobileNo;
}
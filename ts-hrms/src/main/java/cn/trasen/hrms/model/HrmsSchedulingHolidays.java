package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**   
 * @ClassName:  HrmsSchedulingHolidays   
 * @Description:法定节假日表   
 * @author: WZH
 * @date:   2021年10月23日 下午5:54:17      
 * @Copyright:  
 */
@Table(name = "hrms_scheduling_holidays")
@Setter
@Getter
public class HrmsSchedulingHolidays {
    /**
     * 节假日id
     */
    @Id
    @Column(name = "holidays_id")
    @ApiModelProperty(value = "节假日id")
    private String holidaysId;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 节假日时间
     */
    @Column(name = "holidays_date")
    @ApiModelProperty(value = "节假日时间")
    private String holidaysDate;
    
    /**
     * 节假日时间
     */
    @Column(name = "holidays_type")
    @ApiModelProperty(value = "节假日类型")
    private String holidaysType;  //1、工作日。2周末，3节假日
    
    
   //元旦节、春节、清明节、端午节、劳动节、中秋节、国庆节、妇女节、青年节、儿童节、建军节
    @Column(name = "holidays_name")
    @ApiModelProperty(value = "节假日名称")
    private String holidaysName;  

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
  
    
    @Transient
    private String searchStartDate;
    
    @Transient
    private String searchEndDate;
    
    @Transient
    private String searchType;
    
}
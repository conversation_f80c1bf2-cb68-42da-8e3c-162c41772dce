package cn.trasen.hrms.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "hrms_salary_plan_employee")
@Setter
@Getter
public class HrmsSalaryPlanEmployee {

	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	@Id
	@Column(name = "salary_plan_employee_id")
	private String salaryPlanEmployeeId;

	/**
	 * 方案ID
	 */
	@Column(name = "salary_plan_id")
	@ApiModelProperty(value = "方案ID")
	private String salaryPlanId;

	/**
	 * 员工ID
	 */
	@Column(name = "employee_id")
	@ApiModelProperty(value = "员工ID")
	private String employeeId;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 员工工号
	 */
	@Transient
	@ApiModelProperty(value = "员工工号")
	private String employeeNo;
	
	/**
	 * 员工姓名
	 */
	@Transient
	@ApiModelProperty(value = "员工姓名")
	private String employeeName;

	/**
	 * 组织机构名称
	 */
	@Transient
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 员工类别
	 */
	@Transient
	@ApiModelProperty(value = "员工类别")
	private String employeeCategory;

	/**
	 * 岗位名称
	 */
	@Transient
	@ApiModelProperty(value = "岗位名称")
	private String postName;

	/**
	 * 岗位工资
	 */
	@Transient
	@ApiModelProperty(value = "岗位工资")
	private BigDecimal postWage;

	/**
	 * 薪级工资
	 */
	@Transient
	@ApiModelProperty(value = "薪级工资")
	private BigDecimal salaryLevelWage;

	/**
	 * 绩效工资
	 */
	@Transient
	@ApiModelProperty(value = "基础性绩效")
	private BigDecimal performanceWage;
	
	@Transient
	@ApiModelProperty(value = "工龄工资")
	private BigDecimal salaryGlgz;
	
	@Transient
	@ApiModelProperty(value = "奖励性绩效")
	private BigDecimal awardWage;
	
	
	@Transient
	@ApiModelProperty(value = "护龄工资")
	private BigDecimal salaryFlgz;
	
	@Transient
	@ApiModelProperty(value = "卫生费")
	private BigDecimal salaryWsf;
	
	@Transient
	@ApiModelProperty(value = "应发工资")
	private BigDecimal salaryShould;
	
	@Transient
	@ApiModelProperty(value = "扣款小计")
	private BigDecimal salaryDeduction;
	
	@Transient
	@ApiModelProperty(value = "实发工资")
	private BigDecimal salaryPractical;
	
	

	/**
	 * 方案人员ID集合
	 */
	@Transient
	@ApiModelProperty(value = "方案人员ID集合")
	private List<String> employeeIds;
	
	
	//益阳妇幼
	@Transient
	@ApiModelProperty(value = "基础工资")
	private BigDecimal baseJcgz;
	
	@Transient
	@ApiModelProperty(value = "学历工资")
	private BigDecimal baseXlgz;
	
	
	
	//益阳妇幼
	@Transient
	@ApiModelProperty(value = "院龄工资")
	private BigDecimal baseYlgz;
	
	@Transient
	@ApiModelProperty(value = "职称工资")
	private BigDecimal baseZcgz;
	
	@Transient
	@ApiModelProperty(value = "职称登记")
	private String jobTitleLevel;
	
	@Transient
	@ApiModelProperty(value = "职称名称")
	private String jobTitleName;
	
	@Transient
	@ApiModelProperty(value = "员工状态")
	private String employeeStatus;
	
	
	

}
package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

/**    
  * <P> @Description: 员工扩展信息表</p>
  * <P> @Date: 2021年2月23日  上午9:54:10 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
    
@Table(name = "hrms_employee_extend")
@Setter
@Getter
public class HrmsEmployeeExtend {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "employee_extend_id")
    @ApiModelProperty(value = "主键ID")
    private String employeeExtendId;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工ID")
    private String employeeId;

    /**
     * 老系统员工工号
     */
    @Column(name = "old_employee_no")
    @ApiModelProperty(value = "老系统员工工号")
    private String oldEmployeeNo;

    /**
     * HIS系统员工工号
     */
    @Column(name = "his_employee_no")
    @ApiModelProperty(value = "HIS系统员工工号")
    private String hisEmployeeNo;

    /**
     * 入职日期
     */
    @Column(name = "entry_date")
    @ApiModelProperty(value = "入职日期")
    private Date entryDate;

    /**
     * 退休日期
     */
    @Column(name = "retire_date")
    @ApiModelProperty(value = "退休日期")
    private Date retireDate;

    /**
     * 离职日期
     */
    @Column(name = "quit_date")
    @ApiModelProperty(value = "离职日期")
    private Date quitDate;

    /**
     * 返聘日期
     */
    @Column(name = "reemployment_date")
    @ApiModelProperty(value = "返聘日期")
    private Date reemploymentDate;

    /**
     * 入党日期
     */
    @Column(name = "party_date")
    @ApiModelProperty(value = "入党日期")
    private Date partyDate;

    /**
     * 开始工作日期
     */
    @Column(name = "work_start_date")
    @ApiModelProperty(value = "开始工作日期")
    private Date workStartDate;

    /**
     * 岗位受聘日期
     */
    @Column(name = "unit_start_date")
    @ApiModelProperty(value = "岗位受聘日期")
    private Date unitStartDate;

    /**
     * 个人身份
     */
    @Column(name = "personal_identity")
    @ApiModelProperty(value = "个人身份")
    private String personalIdentity;

    /**
     * 用工性质
     */
    @Column(name = "work_nature")
    @ApiModelProperty(value = "用工性质")
    private String workNature;

    /**
     * 擅长
     */
    @Column(name = "good_at")
    @ApiModelProperty(value = "擅长")
    private String goodAt;

    /**
     * 考勤科室
     */
    @Column(name = "check_work_depart")
    @ApiModelProperty(value = "考勤科室")
    private String checkWorkDepart;

    /**
     * 审核科室
     */
    @Column(name = "review_depart")
    @ApiModelProperty(value = "审核科室")
    private String reviewDepart;

    /**
     * 升级标识(平江用于年度统一调薪的判断依据)
     */
    @Column(name = "upgrade_flag")
    @ApiModelProperty(value = "升级标识(平江用于年度统一调薪的判断依据)")
    private String upgradeFlag;

    /**
     * 提高10%标识(平江用于是否计算提高10%工资)
     */
    @Column(name = "improve_flag")
    @ApiModelProperty(value = "提高10%标识(平江用于是否计算提高10%工资)")
    private String improveFlag;

    /**
     * 是否重复入职
     */
    @Column(name = "is_duplicate_entry")
    @ApiModelProperty(value = "是否重复入职")
    private String isDuplicateEntry;

    /**
     * 紧急联系人
     */
    @Column(name = "emergency_contact")
    @ApiModelProperty(value = "紧急联系人")
    private String emergencyContact;

    /**
     * 紧急联系电话
     */
    @Column(name = "emergency_tel")
    @ApiModelProperty(value = "紧急联系电话")
    private String emergencyTel;

    /**
     * 试用期薪资
     */
    @Column(name = "probation_salary")
    @ApiModelProperty(value = "试用期薪资")
    private BigDecimal probationSalary;

    /**
     * 转正薪资
     */
    @Column(name = "regular_salary")
    @ApiModelProperty(value = "转正薪资")
    private BigDecimal regularSalary;

    /**
     * 社保购买日期
     */
    @Column(name = "buy_social_date")
    @ApiModelProperty(value = "社保购买日期")
    private Date buySocialDate;

    /**
     * 公积金购买日期
     */
    @Column(name = "buy_provident_date")
    @ApiModelProperty(value = "公积金购买日期")
    private Date buyProvidentDate;

    /**
     * 薪资福利说明
     */
    @Column(name = "salary_remark")
    @ApiModelProperty(value = "薪资福利说明")
    private String salaryRemark;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
	
	
	/*** 锦屏添加字段****/
    
    @ApiModelProperty(value = "编制所属机构")
    @Column(name = "authorized_org")
	private String authorizedOrg;  //编制所属机构
	
	//岗位描述
    @ApiModelProperty(value = "岗位描述")
    @Column(name = "job_description_type")
	private String jobDescriptionType;
	
	
	@ApiModelProperty(value = "现职务任职时间")
    @Column(name = "job_description_type_time")
	private String jobDescriptionTypeTime;
	
	
	@ApiModelProperty(value = "兼任职务")
    @Column(name = "concurrent_position")
	private String concurrentPosition;
	
	@ApiModelProperty(value = "兼任职务时间")
    @Column(name = "concurrent_position_time")
	private String concurrentPositionTime;
	
	@ApiModelProperty(value = "是否领导")
    @Column(name = "is_leader")
	private String isLeader;
	
	@ApiModelProperty(value = "岗位类型")
    @Column(name = "post_type")
	private String postType;
	
	@ApiModelProperty(value = "医师执业资格证")
    @Column(name = "doctor_qualification_certificate")
	private String doctorQualificationCertificate;
	
	@ApiModelProperty(value = "助产士")
    @Column(name = "midwife")
	private String midwife;
	
	@ApiModelProperty(value = "起聘时间")
    @Column(name = "start_employ_date")
	private String startEmployDate;
	
	@ApiModelProperty(value = "终聘时间")
    @Column(name = "end_employ_date")
	private String endEmployDate;
	
	@ApiModelProperty(value = "是否退伍军人")
    @Column(name = "is_veteran")
	private String isVeteran;
	
	@ApiModelProperty(value = "部队名称")
    @Column(name = "unit_name")
	private String unitName;

	@ApiModelProperty(value = "入职文件")
    @Column(name = "business_id")
	private String businessId;
	
	@ApiModelProperty(value = "聘任情况文件文件")
    @Column(name = "business_id2")
	private String businessId2;
	
	@ApiModelProperty(value = "执业情况文件")
    @Column(name = "business_id3")
	private String businessId3;
	
	@ApiModelProperty(value = "出生地址")
    @Column(name = "born_address")
	private String bornAddress;
	
	@ApiModelProperty(value = "出生地址编码")
    @Column(name = "born_address_name")
	private String bornAddressName;
	
	
   
    @ApiModelProperty(value = "第一学历")
    @Column(name = "first_education_type")
	private String firstEducationType;  //第一学历
	
	@Transient
	private String firstEducationTypeText;  //第一学历文本
	
	//职称聘任字段
	
    @Column(name = "employ_duty")
    @ApiModelProperty(value = "聘任职务")
    private String employDuty;
    
    @Column(name = "employ_duty_date")
    @ApiModelProperty(value = "聘任日期")
    private String employDutyDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "employ_duty_equally_date")
    @ApiModelProperty(value = "任同职级时间")
    private Date employDutyEquallyDate;
    
    @Column(name = "employ_duty_duration")
    @ApiModelProperty(value = "任现职年限")
    private String employDutyDuration;
    
    @Column(name = "compliance_training")
    @ApiModelProperty(value = "是否规培人员")
    private String complianceTraining;
    
    
    @Column(name = "operation_date")
    @ApiModelProperty(value = "任现职年限")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date operationDate;    //首次执业时间
    
    @Column(name = "operation_org")
    @ApiModelProperty(value = "注册机构")
    private String operationOrg;  //注册机构
    
    @Column(name = "operation_scope")
    @ApiModelProperty(value = "执业范围")
    private String operationScope;  //执业范围
    
    @Column(name = "operation_type")
    @ApiModelProperty(value = "执业类别")
    private String operationType;  //执业类别
   
    @Column(name = "operation_number")
    @ApiModelProperty(value = "证书编号")
    private String  operationNumber;  //证书编号
    
    @Column(name = "archive_address")
    @ApiModelProperty(value = "存档地址")
    private String  archiveAddress;  //证书编号
    
    @Transient
    private String engagePost;  //聘用岗位
    
    @Transient
    private String engageLevel;  //聘用等级
    
    

}
package cn.trasen.hrms.model;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "hrms_scheduling_sort")
@Setter
@Getter
public class HrmsSchedulingSort {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;


    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @ApiModelProperty(value = "主键")
    private String id;


    /**
     * 人员id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "人员id")
    private String employeeId;
    

    
    /**
     * 人员所在科室
     */
    @Column(name = "emp_org_id")
    @ApiModelProperty(value = "人员所在科室")
    private String empOrgId;

    
    @Column(name = "sort")
    private String sort;  //科室排序号
     
    
}
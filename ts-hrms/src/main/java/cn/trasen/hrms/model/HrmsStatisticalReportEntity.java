package cn.trasen.hrms.model;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * @Title: HrmsStatisticalReport.java 
 * @Package cn.trasen.hrms.model 
 * @Description: 统计报表 请求参数实体
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月21日 下午1:52:39 
 * @version V1.0   
 */
@Data
public class HrmsStatisticalReportEntity {

	private String ssoOrgCode;

	/**
	 * 组织机构ID集合
	 */
	@ApiModelProperty(value = "组织机构ID集合")
	private List<String> orgIdList;
	
	
	/**
	 * 性别类型集合
	 */
	@ApiModelProperty(value = "性别类型")
	private List<String> genderList;
	
	/**
	 * 学历类型ID集合
	 */
	@ApiModelProperty(value = "学历类型集合")
	private List<String> eduTypeList;
	
	@ApiModelProperty(value = "医共体外部事业单位")
	private String ygtwqtsydwCode;  //医共体外部事业单位

}

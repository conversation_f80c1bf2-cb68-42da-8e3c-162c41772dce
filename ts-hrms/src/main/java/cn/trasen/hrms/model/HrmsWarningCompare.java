package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_warning_compare")
@Setter
@Getter
public class HrmsWarningCompare {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    private String id;

    /**
     * 预警id
     */
    @Column(name = "warning_id")
    @ApiModelProperty(value = "预警id")
    private String warningId;

    /**
     * 比较类型
     */
    @Column(name = "compare_type")
    @ApiModelProperty(value = "比较类型")
    private String compareType;

    /**
     * 比较符
     */
    @Column(name = "compare_symbols")
    @ApiModelProperty(value = "比较符")
    private String compareSymbols;

    /**
     * 比较值
     */
    @Column(name = "compare_value")
    @ApiModelProperty(value = "比较值")
    private String compareValue;

    /**
     * 比较逻辑
     */
    @Column(name = "compare_logic")
    @ApiModelProperty(value = "比较逻辑")
    private String compareLogic;
}
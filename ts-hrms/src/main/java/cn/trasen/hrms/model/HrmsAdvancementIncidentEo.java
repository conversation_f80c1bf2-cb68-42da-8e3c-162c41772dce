package cn.trasen.hrms.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * 岗位晋级事件
 * @TableName hrms_advancement_incident
 */
@Table(name = "hrms_advancement_incident")
@Setter
@Getter
public class HrmsAdvancementIncidentEo implements Serializable {
    /**
     * 主键ID
     */
    @Id
    private String id;

    /**
     * 员工工号
     */
    private String employeeNo;

    /**
     * 员工名字
     */
    private String employeeName;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 旧的岗位类别
     */
    private String oldPlgw;

    /**
     * 新的岗位类别
     */
    private String newPlgw;

    /**
     * 旧的岗位等级
     */
    private String oldGwdj;

    /**
     * 新的岗位等级
     */
    private String newGwdj;

    /**
     * 旧的薪级等级
     */
    private String newSalaryLevelId;

    /**
     * 新的薪级等级
     */
    private String oldSalaryLevelId;

    /**
     * 新的薪级类别
     */
    private String newSalaryLevelType;

    /**
     * 旧的薪级类别
     */
    private String oldSalaryLevelType;

    /**
     * 生效日期
     */
    private String effectiveDate;

    /**
     * 变动原因
     */
    private String reason;

    /**
     * 0.档案修改 1.晋升修改 2.薪酬修改
     */
    private Integer sourceType;

    /**
     * 审批状态: 1=未审批; 2=审批中; 3=已退回; 4=已审批
     */
    private Integer approvalStatus;

    /**
     * 是否执行(0执行1停止)
     */
    private Integer stopEvent;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建者ID
     */
    private String createUser;

    /**
     * 创建者姓名
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新者ID
     */
    private String updateUser;

    /**
     * 更新者姓名
     */
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    private String isDeleted;

    /**
     * 
     */
    private String ssoOrgCode;

    /**
     * 
     */
    private String ssoOrgName;

    /**
     * 审批状态文本值
     */
    @Transient
    @ApiModelProperty(value = "审批状态文本值")
    private String approvalStatusText;

    @Transient
    @ApiModelProperty(value = "新的薪资类别的中文")
    private String newSalaryLevelTypeText;

    @Transient
    @ApiModelProperty(value = "新的岗位等级的中文")
    private String newGwdjText;

    @Transient
    @ApiModelProperty(value = "新的岗位类别的中文")
    private String newPlgwText;

    @Transient
    @ApiModelProperty(value = "新的薪级等级的中文")
    private String newSalaryLevelIdText;

    @Transient
    @ApiModelProperty(value = "旧的薪资类别的中文")
    private String oldSalaryLevelTypeText;

    @Transient
    @ApiModelProperty(value = "旧的岗位等级的中文")
    private String oldGwdjText;

    @Transient
    @ApiModelProperty(value = "旧的岗位类别的中文")
    private String oldPlgwText;

    @Transient
    @ApiModelProperty(value = "旧的薪级等级的中文")
    private String oldSalaryLevelIdText;

    /**
     * 0.档案修改 1.晋升修改 2.薪酬修改
     */
    @Transient
    private String sourceTypeText;

    @Transient
    @ApiModelProperty(value = "部门id")
    private String employeeOrgId;

    @Transient
    @ApiModelProperty(value = "部门")
    private String employeeOrgName;

    @Transient
    @ApiModelProperty(value = "开始时间")
    private String startDate;

    @Transient
    @ApiModelProperty(value = "结束时间")
    private String endDate;

    @Transient
    @ApiModelProperty(value = "是否新增 0不新增")
    private Integer type;

    /**
     * 现任聘时间
     */
    private String jobDeionTypeTime;

    private static final long serialVersionUID = 1L;
}
package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import org.jeecgframework.poi.excel.annotation.Excel;

import lombok.*;

/**
 * @Description: 字典表 实体
 * @Date: 2020年3月23日  下午4:00:02 
 * @Author: Z 
 * @Company: 湖南创星科技股份有限公司 
 * @version V1.0
*/
@Table(name = "hrms_dict_info")
@Setter
@Getter
public class HrmsDictInfo {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "dict_info_id")
    @ApiModelProperty(value = "主键ID")
    private String dictInfoId;

    /**
     * 字典值
     */
    @Excel(name = "字典值")
    @Column(name = "dict_value")
    @ApiModelProperty(value = "字典值")
    private String dictValue;

    /**
     * 字典名称
     */
    @Excel(name = "字典名称")
    @Column(name = "dict_name")
    @ApiModelProperty(value = "字典名称")
    private String dictName;

    /**
     * 字典类型
     */
    @Excel(name = "字典类型")
    @Column(name = "dict_type")
    @ApiModelProperty(value = "字典类型")
    private String dictType;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;
}
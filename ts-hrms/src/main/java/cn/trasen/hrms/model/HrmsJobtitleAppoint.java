package cn.trasen.hrms.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 职称聘任
 *
 */
@Table(name = "hrms_jobtitle_appoint")
@Setter
@Getter
public class HrmsJobtitleAppoint {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "jobtitle_appoint_id")
    @ApiModelProperty(value = "主键ID")
    private String jobtitleAppointId;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工ID")
    private String employeeId;

    /**
     * 员工工号
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 员工姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    /**
     * 职称类别
     */
    @Column(name = "jobtitle_category")
    @ApiModelProperty(value = "职称类别")
    private String jobtitleCategory;

    /**
     * 职称级别
     */
    @Column(name = "jobtitle_level")
    @ApiModelProperty(value = "职称级别")
    private String jobtitleLevel;

    /**
     * 职称名称
     */
    @Column(name = "jobtitle_name")
    @ApiModelProperty(value = "职称名称")
    private String jobtitleName;

    /**
     * 评定资格时间
     */
    @Column(name = "assess_time")
    @ApiModelProperty(value = "评定资格时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date assessTime;

    /**
     * 评定机构名称
     */
    @Column(name = "mechanism_name")
    @ApiModelProperty(value = "评定机构名称")
    private String mechanismName;

    /**
     * 获取途径
     */
    @Column(name = "accept_method")
    @ApiModelProperty(value = "获取途径")
    private String acceptMethod;

    /**
     * 证书编号
     */
    @Column(name = "certificate_number")
    @ApiModelProperty(value = "证书编号")
    private String certificateNumber;
    
    /**
	 * 最高职称: 1=是; 2=否;
	 */
	@Column(name = "highest_level")
	@ApiModelProperty(value = "最高职称: 1=是; 2=否;")
	private String highestLevel;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 审批状态: 1=未审批; 2=审批中; 3=已退回; 4=已审批
     */
    @Column(name = "approval_status")
    @ApiModelProperty(value = "审批状态: 1=未审批; 2=审批中; 3=已退回; 4=已审批")
    private String approvalStatus;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    // ------- 扩展字段 ------- //
    /**
     * 职称类别文本值
     */
    @Transient
    @ApiModelProperty(value = "职称类别文本值")
    private String jobtitleCategoryText;

    /**
     * 职称等级文本值
     */
    @Transient
    @ApiModelProperty(value = "职称等级文本值")
    private String jobtitleLevelText;

    /**
     * 职称名称文本值
     */
    @Transient
    @ApiModelProperty(value = "职称名称文本值")
    private String jobtitleNameText;

    /**
     * 获取途径文本值
     */
    @Transient
    @ApiModelProperty(value = "获取途径文本值")
    private String acceptMethodText;

    /**
     * 审批状态文本值
     */
    @Transient
    @ApiModelProperty(value = "审批状态文本值")
    private String approvalStatusText;

    /**
     * 导出-职称评定时间
     */
    @Transient
    @ApiModelProperty(value = "导出-职称评定时间")
    private String assessTimeExport;
    
    /**
     * 附件Id
     */
    @Transient
    @ApiModelProperty(value = "附件Id")
    private String businessId;
  
    @Transient
    private String htOrgIdList;
}
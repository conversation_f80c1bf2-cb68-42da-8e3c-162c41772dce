package cn.trasen.hrms.model;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class ThpsDept {

	private String orgFlag;
	private String deptcode;

	private String id;

	private String corpcode;

	private String deptname;

	private String deptlevel;

	private String pdcode;

	private Integer status;

	private String managercode;

	private String remark;

	private String createuser;

	private Date createtime;

	private String updateuser;

	private Date updatetime;

	private String codestring;

	private String clerkname;// 科室秘书名称

	private String clerkid;// 科室秘书ID

	private String headnursename;// 护士长名称

	private String headnurseid;// 护士长ID

	private String directorofdepartmentname;// 科室主任名称

	private String directorofdepartmentid;// 科室主任ID

	private String medicaldirectorname;// 医疗主任名称

	private String medicaldirectorid;// 医疗主任ID

	private String departmentheadname;// 部门领导名称

	private String departmentheadid;// 部门领导ID

	private String managementleadname;// 分管领导名称

	private String managementleadid;// 分管领导ID

	private String directleadershipname;// 直接领导名称

	private String directleadershipid;// 直接领导ID

	private String departmentephorname;// 部门长名称

	private String departmentephorid;// 部门长id

}
package cn.trasen.hrms.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
/**
 * 
* @ClassName: HrmsPost
* @Description: 岗位等级 
* <AUTHOR>
* @date 2024年6月28日 上午10:28:37
*
 */
@Table(name = "comm_post")
@Setter
@Getter
public class HrmsPost {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "post_id")
    @ApiModelProperty(value = "主键ID")
    private String postId;

    /**
     * 岗位名称
     */
    @Column(name = "post_name")
    @ApiModelProperty(value = "岗位名称")
    private String postName;

    /**
     * 岗位类别
     */
    @Column(name = "post_category")
    @ApiModelProperty(value = "岗位类别")
    private String postCategory;

    /**
     * 是否启用: 1=是; 2=否;
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String isEnable;
    
    
    @Column(name = "upgrade_time")
    @ApiModelProperty(value = "升下一等级时间")
    private Integer upgradeTime;  //升级时间
  
    @Column(name = "upgrade_system")
    @ApiModelProperty(value = "是否在升级体系内")
    private String upgradeSystem;   //是否在升级体系内
    
    
    @Column(name = "upgrade_no")
    @ApiModelProperty(value = "升级排序号")
    private Integer upgradeNo;   //升级排序号
    
    

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    // ------- 扩展字段 ------- //
    @Transient
    @ApiModelProperty(value = "岗位类别名称")
    private String postCategoryName;
    
    @Transient
    @ApiModelProperty(value = "移动类型")
    private String type;
}
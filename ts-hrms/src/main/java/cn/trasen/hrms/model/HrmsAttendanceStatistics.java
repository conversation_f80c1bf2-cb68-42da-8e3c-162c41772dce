package cn.trasen.hrms.model;

import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_attendance_statistics")
@Setter
@Getter
public class HrmsAttendanceStatistics {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "attendance_statistics_id")
    @ApiModelProperty(value = "主键ID")
    private String attendanceStatisticsId;

    /**
     * 考勤记录ID
     */
    @Column(name = "attendance_record_id")
    @ApiModelProperty(value = "考勤记录ID")
    private String attendanceRecordId;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工ID")
    private String employeeId;

    /**
     * 考勤项目ID
     */
    @Column(name = "attendance_item_id")
    @ApiModelProperty(value = "考勤项目ID")
    private String attendanceItemId;

    /**
     * 考勤天数
     */
    @Column(name = "attendance_days")
    @ApiModelProperty(value = "考勤天数")
    private Long attendanceDays;
    
    /**  
      * 统计月份
      */  
        
    @Column(name = "count_date")
    @ApiModelProperty(value = "统计月份")
    private String countDate;
    

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    // ------- 扩展字段 ------- //
    /**
     * 考勤日期
     */
    @Transient
    @ApiModelProperty(value = "考勤日期")
    private String attendanceDate;
    
    /**
     * 考勤项目名称
     */
    @Transient
    @ApiModelProperty(value = "考勤项目名称")
    private String attendanceItemName;
    
    /**
     * 考勤项目金额
     */
    @Transient
    @ApiModelProperty(value = "考勤项目金额")
    private BigDecimal attendanceItemAmount;
    
    /**
     * 考勤项目类型
     */
    @Transient
    @ApiModelProperty(value = "考勤项目类型")
    private String attendanceItemType;
    
    /**
     * 考勤项目编码
     */
    @Transient
    @ApiModelProperty(value = "考勤项目类型")
    private String salaryItemCode;
    
    
    
	//科室审核
    @Transient
	@ApiModelProperty(value = "科室审核时间")
	private Date deptCheckDate;
	
    @Transient
	@ApiModelProperty(value = "科室审核人")
	private String deptCheckUserName;
	
    @Transient
	@ApiModelProperty(value = "科室审核人id")
	private String deptCheckUserId;
	
	
	//人事考勤审核
    @Transient
	@ApiModelProperty(value = "人事考勤审核状态")
	private String rskqCheckStatus;
	
    @Transient
	@ApiModelProperty(value = "人事考勤审核人")
	private String rskqCheckUserId;
	
    @Transient
	@ApiModelProperty(value = "人事考勤审核时间")
	private Date rskqCheckDate;
	

	//晚夜班审核
    @Transient
	@ApiModelProperty(value = "人事晚夜班审核状态")
	private String rswybCheckStatus;
	
    @Transient
	@ApiModelProperty(value = "人事晚夜班审核审核人")
	private String rswybCheckUserId;
	
    @Transient
	@ApiModelProperty(value = "人事晚夜班审核时间")
	private Date rswybCheckDate;
   
    @Transient
	@ApiModelProperty(value = "审核状态")
	private String approvalStatus;
    
    @Transient
	@ApiModelProperty(value = "审核科室")
	private String reviewDepart;
    
    @Transient
	@ApiModelProperty(value = "全勤编号")
    private  String  attendanceNumber;
    
    @Transient
	@ApiModelProperty(value = "是否全勤")
    private String isQQ;
   
    @Transient
    private String deptCheck;  //是否科室审核
   
    @Transient
    private List<String> dateSection;//时间区间参数
    
    @Transient
    private String employeeNo;  
    
    @Transient
    private String employeeName; 
    
    @Transient
    private String overtime;  //1查看晚夜班
    
}
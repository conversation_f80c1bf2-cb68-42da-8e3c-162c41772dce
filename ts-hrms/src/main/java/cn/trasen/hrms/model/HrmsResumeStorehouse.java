package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_resume_storehouse")
@Setter
@Getter
public class HrmsResumeStorehouse {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	/**
	 * 主键
	 */
	@Id
	@Column(name = "resume_storehouse_id")
	@ApiModelProperty(value = "主键")
	private String resumeStorehouseId;

	/**
	 * 员工姓名
	 */
	@Column(name = "employee_name")
	@ApiModelProperty(value = "员工姓名")
	private String employeeName;

	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别")
	private String gender;

	/**
	 * 应聘岗位
	 */
	@Column(name = "apply_post")
	@ApiModelProperty(value = "应聘岗位")
	private String applyPost;

	/**
	 * 联系电话
	 */
	@Column(name = "telephone_number")
	@ApiModelProperty(value = "联系电话")
	private String telephoneNumber;

	/**
	 * 邮箱
	 */
	@ApiModelProperty(value = "邮箱")
	private String email;

	/**
	 * 身份证号
	 */
	@Column(name = "identity_number")
	@ApiModelProperty(value = "身份证号")
	private String identityNumber;

	/**
	 * 年龄
	 */
	@ApiModelProperty(value = "年龄")
	private Integer age;

	/**
	 * 婚姻状况
	 */
	@Column(name = "marriage_status")
	@ApiModelProperty(value = "婚姻状况")
	private String marriageStatus;

	/**
	 * 同行经验(年)
	 */
	@Column(name = "peer_experience")
	@ApiModelProperty(value = "同行经验(年)")
	private Integer peerExperience;

	/**
	 * 毕业院校
	 */
	@Column(name = "graduated_school")
	@ApiModelProperty(value = "毕业院校")
	private String graduatedSchool;

	/**
	 * 学历类型
	 */
	@Column(name = "education_type")
	@ApiModelProperty(value = "学历类型")
	private String educationType;

	/**
	 * 专业
	 */
	@ApiModelProperty(value = "专业")
	private String profession;

	/**
	 * 来源渠道
	 */
	@Column(name = "source_channel")
	@ApiModelProperty(value = "来源渠道")
	private String sourceChannel;

	/**
	 * 期望薪资
	 */
	@Column(name = "expect_salary")
	@ApiModelProperty(value = "期望薪资")
	private BigDecimal expectSalary;

	/**
	 * 期望工作区域
	 */
	@Column(name = "expect_work_region")
	@ApiModelProperty(value = "期望工作区域")
	private String expectWorkRegion;

	/**
	 * 简历状态
	 */
	@Column(name = "resume_status")
	@ApiModelProperty(value = "简历状态")
	private String resumeStatus;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 简历状态文本值
	 */
	@Transient
	@ApiModelProperty(value = "简历状态文本值")
	private String resumeStatusText;

	/**
	 * 性别文本值
	 */
	@Transient
	@ApiModelProperty(value = "性别文本值")
	private String genderText;

	/**
	 * 学历类型文本值
	 */
	@Transient
	@ApiModelProperty(value = "学历类型文本值")
	private String educationTypeText;

	/**
	 * 婚姻状况文本值
	 */
	@Transient
	@ApiModelProperty(value = "婚姻状况文本值")
	private String marriageStatusText;

	/**
	 * 来源渠道文本值
	 */
	@Transient
	@ApiModelProperty(value = "来源渠道文本值")
	private String sourceChannelText;
	
	/**
     * 附件Id-前台
     */
    @Transient
    private String businessId;
}
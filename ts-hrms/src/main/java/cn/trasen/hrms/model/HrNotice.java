package cn.trasen.hrms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Table(name = "hr_notice")
@Setter
@Getter
@ToString
public class HrNotice {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 公告ID
     */
    @Id
    @Column(name = "notice_id")
    @ApiModelProperty(value = "公告ID")
    private String noticeId;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 状态0:未处理，1:已处理
     */
    @ApiModelProperty(value = "状态0:未处理，1:已处理")
    private Integer status;
    
    /**
     * 所属公司
     */
    @Column(name = "company_code")
    @ApiModelProperty(value = "所属公司")
    private String companyCode;

    /**
     * 所属公司名称
     */
    @Column(name = "company_name")
    @ApiModelProperty(value = "所属公司名称")
    private String companyName;


    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 审核状态(1:待审核;2:已审核;3:撤回)
     */
    @Column(name = "audit_status")
    @ApiModelProperty(value = "审核状态(1:待审核;2:已审核;3:撤回)")
    private Integer auditStatus;

    /**
     * 审核人员
     */
    @Column(name = "audit_user")
    @ApiModelProperty(value = "审核人员")
    private String auditUser;

    /**
     * 审核人名称
     */
    @Column(name = "audit_user_name")
    @ApiModelProperty(value = "审核人名称")
    private String auditUserName;

    /**
     * 审核时间
     */
    @Column(name = "audit_date")
    @ApiModelProperty(value = "审核时间")
    private Date auditDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    
    /**
     * 内容
     */
    @Column(name = "content")
    @ApiModelProperty(value = "内容")
    private String content;
    
    /**
     * 发送类型(1:集团 2:公司 3:部门 4:其他)
     */
    @Column(name = "send_type")
    @ApiModelProperty(value = "发送类型(1:集团 2:公司 3:部门 4:其他)")
    private int sendType;
    
    /**
     * 接收code(有多个以','分割)
     */
    @Column(name = "receive_code")
    @ApiModelProperty(value = "接收code(有多个以','分割)")
    private String receiveCode;
    
    /**
     * 公告类型(1:制度类 2:通知类 3:其他类)
     */
    @Column(name = "notice_type")
    @ApiModelProperty(value = "发送类型(1:集团 2:公司 3:部门 4:其他)")
    private String noticeType;
    
    @Column(name = "org_id")
    private String orgId;
    
    @Column(name = "org_name")
    private String orgName;
    
    /**
     * 公告类型(1:制度类 2:通知类 3:其他类)
     */
    @Transient
    private String noticeTypeStr;
    
    
    /**
     * 接收人公司
     */
    @Transient
    private String receiverCompany;

    /**
     * 接受人部门
     */
    @Transient
    private String receiverDepartment;
    
    /**
     * 接收人编号
     */
    @Transient
    private String receiverNo;

    /**
     * 接受人名称
     */
    @Transient
    private String receiverName;
    
    /**
     * 业务ID（用于附件）
     */
    @Transient
    private String businessId;
    
    @Transient
    private String startReleaseTime;
    @Transient
    private String endReleaseTime;
    
}
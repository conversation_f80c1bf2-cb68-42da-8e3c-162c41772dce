package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

/**
 * 科研论文
 *
 */
@Table(name = "hrms_papers_books")
@Setter
@Getter
public class HrmsPapersBooks {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工ID")
    private String employeeId;
    
    /**
     * 员工name
     */
    @Transient
    @ApiModelProperty(value = "员工ID")
    private String employeeName;
    
    @Transient
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;


    /**
     * 论文著作标题
     */
    @Column(name = "papers_books_title")
    @ApiModelProperty(value = "论文著作标题")
    private String papersBooksTitle;

    /**
     * 论文著作专业
     */
    @Column(name = "papers_books_speciality")
    @ApiModelProperty(value = "论文著作专业")
    private String papersBooksSpeciality;

    /**
     * 发布日期
    
    @Column(name = "publish_date")
    @ApiModelProperty(value = "发布日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date publishDate;
     */
    
    
    /**
     * 发布日期
     */
    @Column(name = "publish_date")
    @ApiModelProperty(value = "发布日期")
    private String publishDate;

    /**
     * 出版处
     */
    @Column(name = "publishing_office")
    @ApiModelProperty(value = "出版处")
    private String publishingOffice;

    /**
     * 排序号
     */
    @Column(name = "serial_number")
    @ApiModelProperty(value = "排序号")
    private Integer serialNumber;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    /**
     * 论文附件;
     */
    @Column(name = "lwfj")
    @ApiModelProperty(value = "论文附件")
    private String lwfj;
     
    /**
     * 员工name
     */
    @Transient
    @ApiModelProperty(value = "搜索开始时间")
    private String thesisStartTime;
    
    /**
     * 员工name
     */
    @Transient
    @ApiModelProperty(value = "搜索结束时间")
    private String thesisEndTime;
    
    /**
     * 员工name
     */
    @Transient
    @ApiModelProperty(value = "序号")
    private Integer pm;
    
    @Transient
    @ApiModelProperty(value = "时间")
    private String createDateStr;
}
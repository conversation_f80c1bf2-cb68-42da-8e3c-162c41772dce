package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_research_cost")
@Setter
@Getter
public class HrmsResearchCost {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    private String id;

    /**
     * 课题id
     */
    @Column(name = "topic_id")
    @ApiModelProperty(value = "课题id")
    private String topicId;

    /**
     * 经费编码
     */
    @Column(name = "cost_code")
    @ApiModelProperty(value = "经费编码")
    private String costCode;

    /**
     * 经费名称
     */
    @Column(name = "cost_name")
    @ApiModelProperty(value = "经费名称")
    private String costName;

    /**
     * 经费说明
     */
    @Column(name = "cost_content")
    @ApiModelProperty(value = "经费说明")
    private String costContent;

    /**
     * 使用科室编码
     */
    @Column(name = "use_dept")
    @ApiModelProperty(value = "使用科室编码")
    private String useDept;

    /**
     * 使用科室名称
     */
    @Column(name = "use_dept_name")
    @ApiModelProperty(value = "使用科室名称")
    private String useDeptName;

    /**
     * 使用人员编码
     */
    @Column(name = "use_user")
    @ApiModelProperty(value = "使用人员编码")
    private String useUser;

    /**
     * 使用人员名称
     */
    @Column(name = "use_user_name")
    @ApiModelProperty(value = "使用人员名称")
    private String useUserName;

    /**
     * 使用金额
     */
    @Column(name = "use_cost")
    @ApiModelProperty(value = "使用金额")
    private BigDecimal useCost;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "is_deleted")
    private String isDeleted;
    
    @Transient
    private int pm; 
}
package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_work_experience_hospital")
@Setter
@Getter
public class HrmsWorkExperienceHospital {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工ID")
    private String employeeId;

    /**
     * 原岗位
     */
    @Column(name = "old_post")
    @ApiModelProperty(value = "原岗位")
    private String oldPost;

    /**
     * 现岗位
     */
    @Column(name = "new_post")
    @ApiModelProperty(value = "现岗位")
    private String newPost;

    /**
     * 原职务
     */
    @Column(name = "old_position")
    @ApiModelProperty(value = "原职务")
    private String oldPosition;

    /**
     * 现职务
     */
    @Column(name = "new_position")
    @ApiModelProperty(value = "现职务")
    private String newPosition;

    /**
     * 调动类型
     */
    @Column(name = "change_type")
    @ApiModelProperty(value = "调动类型")
    private String changeType;

    /**
     * 生效时间
     */
    @Column(name = "change_start_date")
    @ApiModelProperty(value = "生效时间")
    private Date changeStartDate;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String gzjlfj;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "old_dept")
    @ApiModelProperty(value = "原科室")
    private String oldDept;
    
    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "new_dept")
    @ApiModelProperty(value = "新科室")
    private String newDept;
}
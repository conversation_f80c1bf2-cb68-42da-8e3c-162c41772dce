package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**   
 * @ClassName:  HrmsSchedulingFrequencyGroup   
 * @Description:组合班次   
 * @author: WZH
 * @date:   2021年7月14日 下午3:15:15      
 * @Copyright:  
 */
@Table(name = "hrms_scheduling_frequency_group")
@Setter
@Getter
public class HrmsSchedulingFrequencyGroup {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 组合班次id
     */
    @Id
    @Column(name = "frequency_group_id")
    @ApiModelProperty(value = "组合班次id")
    private String frequencyGroupId;

    /**
     * 组合班次名称
     */
    @Column(name = "frequency_group_name")
    @ApiModelProperty(value = "组合班次名称")
    private String frequencyGroupName;

    /**
     * 组合班次内容
     */
    @Column(name = "frequency_group_content")
    @ApiModelProperty(value = "组合班次内容")
    private String frequencyGroupContent;

    /**
     * 组合班次内容id
     */
    @Column(name = "frequency_group_content_id")
    @ApiModelProperty(value = "组合班次内容id")
    private String frequencyGroupContentId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;
    
    /**
     * 更新者姓名
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "机构id")
    private String orgId;
    
    /**
     * 更新者姓名
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    
    @Transient
    @ApiModelProperty(value = "颜色")
    private String frequencyColours;
    
    @Transient
    @ApiModelProperty(value = "时间")
    private String frequencyTimes;
}
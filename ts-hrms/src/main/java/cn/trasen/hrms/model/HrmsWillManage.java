package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

/**
 * 遗嘱表
 *
 */
@Table(name = "hrms_will_manage")
@Setter
@Getter
public class HrmsWillManage {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键
     */
	@Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    private String idcard;

    /**
     * 金额（原标准）
     */
    @Column(name = "primary_price")
    @ApiModelProperty(value = "金额（原标准）")
    private String primaryPrice;

    /**
     * 金额（现标准）
     */
    @Column(name = "now_price")
    @ApiModelProperty(value = "金额（现标准）")
    private String nowPrice;

    /**
     * 执行时间
     */
    @Column(name = "execute_time")
    @ApiModelProperty(value = "执行时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date executeTime;

    /**
     * 联系人
     */
    @Column(name = "contacts_name")
    @ApiModelProperty(value = "联系人")
    private String contactsName;

    /**
     * 联系人关系
     */
    @Column(name = "contacts_relation")
    @ApiModelProperty(value = "联系人关系")
    private String contactsRelation;

    /**
     * 联系人身份证
     */
    @Column(name = "contacts_idcard")
    @ApiModelProperty(value = "联系人身份证")
    private String contactsIdcard;

    /**
     * 联系人电话
     */
    @Column(name = "contacts_phone")
    @ApiModelProperty(value = "联系人电话")
    private String contactsPhone;

    /**
     * 收款账号
     */
    @Column(name = "account_number")
    @ApiModelProperty(value = "收款账号")
    private String accountNumber;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    @Transient
    private String executeTimeStartTime;
    
    @Transient
    private String executeTimeEndTime;
    
    @Transient
    private int pm;
}
package cn.trasen.hrms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "hrms_family_info")
@Setter
@Getter
public class HrmsFamilyInfo {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "family_info_id")
	@ApiModelProperty(value = "主键ID")
	private String familyInfoId;

	/**
	 * 员工ID
	 */
	@Column(name = "employee_id")
	@ApiModelProperty(value = "员工ID")
	private String employeeId;

	/**
	 * 家庭成员姓名
	 */
	@Column(name = "member_name")
	@ApiModelProperty(value = "家庭成员姓名")
	private String memberName;

	/**
	 * 与本人关系
	 */
	@ApiModelProperty(value = "与本人关系")
	private String relationship;

	/**
	 * 工作单位
	 */
	@Column(name = "work_unit")
	@ApiModelProperty(value = "工作单位")
	private String workUnit;

	/**
	 * 担任职务
	 */
	@ApiModelProperty(value = "担任职务")
	private String post;

	/**
	 * 政治面貌
	 */
	@Column(name = "political_status")
	@ApiModelProperty(value = "政治面貌")
	private String politicalStatus;

	/**
	 * 联系电话
	 */
	@Column(name = "contact_number")
	@ApiModelProperty(value = "联系电话")
	private String contactNumber;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 与本人关系文本值
	 */
	@Transient
	@ApiModelProperty(value = "与本人关系文本值")
	private String relationshipText;

	/**
	 * 政治面貌文本值
	 */
	@Transient
	@ApiModelProperty(value = "政治面貌文本值")
	private String politicalStatusText;
}
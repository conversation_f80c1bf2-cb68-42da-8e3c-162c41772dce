package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_salary_plan")
@Setter
@Getter
public class HrmsSalaryPlan {
	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "salary_plan_id")
	@ApiModelProperty(value = "主键ID")
	private String salaryPlanId;

	/**
	 * 薪酬方案名称
	 */
	@Column(name = "salary_plan_name")
	@ApiModelProperty(value = "薪酬方案名称")
	private String salaryPlanName;

	/**
	 * 薪酬方案编码
	 */
	@Column(name = "salary_plan_code")
	@ApiModelProperty(value = "薪酬方案编码")
	private String salaryPlanCode;

	/**
	 * 考勤计薪规则
	 */
	@Column(name = "attendance_pay_rule")
	@ApiModelProperty(value = "考勤计薪规则")
	private String attendancePayRule;

	/**
	 * 是否启用: 1=是; 2=否;
	 */
	@Column(name = "is_enable")
	@ApiModelProperty(value = "是否启用: 1=是; 2=否;")
	private String isEnable;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 考勤计薪规则名称
	 */
	@Transient
	@ApiModelProperty(value = "考勤计薪规则名称")
	private String attendancePayRuleName;
	
}
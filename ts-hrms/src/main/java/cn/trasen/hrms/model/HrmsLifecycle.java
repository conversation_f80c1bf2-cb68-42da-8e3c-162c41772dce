package cn.trasen.hrms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**   
 * @ClassName:  HrmsLifecycle   
 * @Description:生命周期实体类
 * @author: WZH
 * @date:   2021年11月13日 上午10:59:51      
 * @Copyright:  
 */
@Table(name = "hrms_lifecycle")
@Setter
@Getter
public class HrmsLifecycle {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 事件id
     */
    @Column(name = "incident_id")
    @ApiModelProperty(value = "事件id")
    private String incidentId;

    /**
     * 员工id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    /**
     *事件时间
     */
    @Column(name = "incident_date")
    @ApiModelProperty(value = "事件时间")
    private String incidentDate;
   
    
    /**
     *事件类型
     */
    @Column(name = "incident_type")
    @ApiModelProperty(value = "事件类型")
    private String incidentType;
    
    
    /**
     *事件lable
     */
    @Column(name = "incident_lable")
    @ApiModelProperty(value = "事件lable")
    private String incidentLable;
    
    /**
     *lable的值
     */
    @Column(name = "incident_value")
    @ApiModelProperty(value = "lable的值")
    private String incidentValue;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
}
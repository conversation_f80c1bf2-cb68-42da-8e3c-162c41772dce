package cn.trasen.hrms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**   
 * @ClassName:  HrmsSalaryProbation   
 * @Description:试用期工资管理   
 * @author: WZH
 * @date:   2021年11月2日 上午10:16:13      
 * @Copyright:  
 */
@Table(name = "hrms_salary_probation")
@Setter
@Getter
public class HrmsSalaryProbation {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;


    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private String id;

    /**
     * 适用科室
     */
    @Column(name = "post_wage")
    @ApiModelProperty(value = "试用期岗位工资")
    private BigDecimal postWage;
    
    
    @Column(name = "education_type")
    @ApiModelProperty(value = "学历类型")
    private String educationType;
   
    @Transient
    @ApiModelProperty(value = "学历类型")
    private String educationTypeText;
    
    
    
    /**
     * 是否启用: 1=是; 2=否;
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String isEnable;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人ID")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;
    
	/**
	 * 备注
	 */
    @Column(name = "remark")
	@ApiModelProperty(value = "备注")
	private String remark;


    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;
}

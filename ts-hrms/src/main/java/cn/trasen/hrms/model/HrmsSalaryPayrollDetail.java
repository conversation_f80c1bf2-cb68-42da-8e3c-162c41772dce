package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_salary_payroll_detail")
@Setter
@Getter
public class HrmsSalaryPayrollDetail {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "salary_payroll_detail_id")
	@ApiModelProperty(value = "主键ID")
	private String salaryPayrollDetailId;

	/**
	 * 工资表ID	
	 */
	@Column(name = "salary_payroll_id")
	@ApiModelProperty(value = "工资表ID")
	private String salaryPayrollId;

	/**
	 * 薪酬项目ID
	 */
	@Column(name = "salary_item_id")
	@ApiModelProperty(value = "薪酬项目ID")
	private String salaryItemId;

	/**
	 * 工资金额
	 */
	@Column(name = "salary_amount")
	@ApiModelProperty(value = "工资金额")
	private BigDecimal salaryAmount;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 薪酬项目名称
	 */
	@Transient
	@ApiModelProperty(value = "薪酬项目名称")
	private String salaryItemName;
	
	/**
	 * 计算类型
	 */
	@Transient
	@ApiModelProperty(value = "计算类型")
	private String countType;
	
	/**
	 * 项目类型
	 */
	@Transient
	@ApiModelProperty(value = "项目类型")
	private String salaryItemType;  

	/**
	 * 计算类型文本值
	 */
	@Transient
	@ApiModelProperty(value = "计算类型文本值")
	private String countTypeText;
	
	/**
	 * 员工ID
	 */
	@Transient
	@ApiModelProperty(value = "员工ID")
	private String employeeId;
	
	@Column(name = "payroll_date")
	@ApiModelProperty(value = "发放月份")
	private String payrollDate;
	
	@Transient
	@ApiModelProperty(value = "方案ID")
	private String salaryPlanId;
	
}
package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "hrms_recruitment_demand")
@Setter
@Getter
public class HrmsRecruitmentDemand {


	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	/**
	 * 主键ID
	 */
	@Id
	@Column(name = "recruitment_demand_id")
	@ApiModelProperty(value = "主键ID")
	private String recruitmentDemandId;

	/**
	 * 申请人ID
	 */
	@Column(name = "applicant_id")
	@ApiModelProperty(value = "申请人ID")
	private String applicantId;

	/**
	 * 申请人姓名
	 */
	@Column(name = "applicant_name")
	@ApiModelProperty(value = "申请人姓名")
	private String applicantName;

	/**
	 * 用人科室ID
	 */
	@Column(name = "employment_org_id")
	@ApiModelProperty(value = "用人科室ID")
	private String employmentOrgId;

	/**
	 * 用人科室名称
	 */
	@Column(name = "employment_org_name")
	@ApiModelProperty(value = "用人科室名称")
	private String employmentOrgName;

	/**
	 * 招聘岗位
	 */
	@Column(name = "recruitment_post")
	@ApiModelProperty(value = "招聘岗位")
	private String recruitmentPost;

	/**
	 * 岗位类别
	 */
	@Column(name = "post_category")
	@ApiModelProperty(value = "岗位类别")
	private String postCategory;

	/**
	 * 优先级
	 */
	@ApiModelProperty(value = "优先级")
	private String priority;

	/**
	 * 期望到岗时间
	 */
	@Column(name = "expect_arrive_time")
	@ApiModelProperty(value = "期望到岗时间")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date expectArriveTime;

	/**
	 * 招聘人数
	 */
	@Column(name = "recruitment_number")
	@ApiModelProperty(value = "招聘人数")
	private Integer recruitmentNumber;

	/**
	 * 招聘原因
	 */
	@Column(name = "recruitment_reason")
	@ApiModelProperty(value = "招聘原因")
	private String recruitmentReason;

	/**
	 * 工作内容
	 */
	@Column(name = "work_content")
	@ApiModelProperty(value = "工作内容")
	private String workContent;

	/**
	 * 任职资格
	 */
	@Column(name = "serve_qualification")
	@ApiModelProperty(value = "任职资格")
	private String serveQualification;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;
	
	
	@Column(name = "is_examine")
	@ApiModelProperty(value = "1已审核")
	private String isExamine;
	
	
	// ------- 扩展字段 ------- //
	/**
	 * 岗位类别文本值
	 */
	@Transient
	@ApiModelProperty(value = "岗位类别文本值")
	private String postCategoryText;
	
	/**
	 * 优先级文本值
	 */
	@Transient
	@ApiModelProperty(value = "优先级文本值")
	private String priorityText;
}
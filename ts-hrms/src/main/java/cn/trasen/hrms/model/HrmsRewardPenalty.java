package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "hrms_reward_penalty")
@Setter
@Getter
public class HrmsRewardPenalty {

	@Column(name = "sso_org_code")
	private String ssoOrgCode;

	/**
	 * 主键ID
	 */
	@Id
	@ApiModelProperty(value = "主键ID")
	private String id;

	/**
	 * 员工ID
	 */
	@Column(name = "employee_id")
	@ApiModelProperty(value = "员工ID")
	private String employeeId;

	/**
	 * 奖惩类型
	 */
	@Column(name = "reward_penalty_type")
	@ApiModelProperty(value = "奖惩类型")
	private String rewardPenaltyType;

	/**
	 * 奖惩标题
	 */
	@Column(name = "reward_penalty_title")
	@ApiModelProperty(value = "奖惩标题")
	private String rewardPenaltyTitle;

	/**
	 * 奖惩日期
	 */
	@Column(name = "reward_penalty_date")
	@ApiModelProperty(value = "奖惩日期")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private String rewardPenaltyDate;

	/**
	 * 奖惩原因
	 */
	@Column(name = "reward_penalty_reason")
	@ApiModelProperty(value = "奖惩原因")
	private String rewardPenaltyReason;

	/**
	 * 奖惩单位
	 */
	@Column(name = "reward_penalty_unit")
	@ApiModelProperty(value = "奖惩单位")
	private String rewardPenaltyUnit;

	/**
	 * 解除处分日期
	 */
	@Column(name = "release_date")
	@ApiModelProperty(value = "解除处分日期")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private String releaseDate;

	/**
	 * 解除处分原因
	 */
	@Column(name = "release_reason")
	@ApiModelProperty(value = "解除处分原因")
	private String releaseReason;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 组织机构ID
	 */
	@Column(name = "org_id")
	@ApiModelProperty(value = "组织机构ID")
	private String orgId;

	/**
	 * 组织机构名称
	 */
	@Column(name = "org_name")
	@ApiModelProperty(value = "组织机构名称")
	private String orgName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;

	// ------- 扩展字段 ------- //
	/**
	 * 奖惩类型文本值
	 */
	@Transient
	@ApiModelProperty(value = "奖惩类型文本值")
	private String rewardPenaltyTypeText;
}
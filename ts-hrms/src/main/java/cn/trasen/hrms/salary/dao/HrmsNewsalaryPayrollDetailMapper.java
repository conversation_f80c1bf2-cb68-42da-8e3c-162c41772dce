package cn.trasen.hrms.salary.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.hrms.salary.DTO.UpdateSalaryVo;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetail;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsNewsalaryPayrollDetailMapper extends Mapper<HrmsNewsalaryPayrollDetail> {
    Integer batchInsert(List<HrmsNewsalaryPayrollDetail> hrmsNewsalaryPayrollDetails);

    List<UpdateSalaryVo> getSalaryChangesData(HrmsNewsalaryOptionPayroll record);

    List<HrmsNewsalaryItem> getCalculateWagesHistoryTitle(HrmsNewsalaryOptionPayroll record);

    List<HrmsNewsalaryItem> getsalaryConfirmHistoryTitle(HrmsNewsalaryOptionPayroll record);
    
    int deleteByPayrollId(@Param("payrollId") String payroll_id);
}
package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.message.NoticeService;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryPayrollVo;
import cn.trasen.hrms.salary.DTO.SalarySendOut;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionEmpMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryPayrollMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayroll;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionPayrollService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryPayrollDetailService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryPayrollService;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

@Slf4j
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryPayrollServiceImpl implements HrmsNewsalaryPayrollService {

	@Autowired
	private HrmsNewsalaryPayrollMapper mapper;
	@Autowired
	HrmsNewsalaryOptionPayrollService hrmsNewsalaryOptionPayrollService;
	@Autowired
	HrmsNewsalaryPayrollDetailService hrmsNewsalaryPayrollDetailService;
	@Autowired
	private HrmsNewsalaryOptionEmpMapper hrmsNewsalaryOptionEmpMapper;

	@Autowired
	private HrmsNewsalaryItemService hrmsNewsalaryItemService;
	
	@Value("${appconfig.login}")
    private String appUrl;

//	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryPayroll record) {
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}



//	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryPayroll record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryPayroll record = new HrmsNewsalaryPayroll();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalaryPayroll selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalaryPayroll> getDataSetList(Page page, HrmsNewsalaryPayroll record) {
		Example example = new Example(HrmsNewsalaryPayroll.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalaryPayroll> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
//	@Transactional(readOnly = false)
	public Integer setIsView(String employeeId, String payrollDate) {
		Example example = new Example(HrmsNewsalaryPayroll.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StringUtils.isBlank(employeeId)) {
			employeeId = UserInfoHolder.getCurrentUserId();
		}
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo("payrollDate", payrollDate);
		HrmsNewsalaryPayroll bean = new HrmsNewsalaryPayroll();
		bean.setIsView("1");
		return mapper.updateByExampleSelective(bean,example);
	}

	@Override
	public DataSet<HrmsNewsalaryPayroll> getRecordslist(Page page, HrmsNewsalaryPayroll record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsNewsalaryPayroll> records = mapper.getRecordslist( page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}


	@Override
	public DataSet<HrmsNewsalaryPayroll> getCountTitleDetails(Page page, HrmsNewsalaryPayroll record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsNewsalaryPayroll> records = mapper.getCountTitleDetails( page,record);

		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer batchInsert(List<HrmsNewsalaryPayroll> payrollRecords) {
		int totalSize = payrollRecords.size(); // 总记录数
		int pageSize = 1000; // 每次新增的记录数
		int totalPage = totalSize / pageSize; // 总页数
		if (totalSize % pageSize != 0) {
			totalPage += 1;
			if (totalSize < pageSize) {
				pageSize = totalSize;
			}
		}
		for (int i = 1; i < totalPage + 1; i++) {
			int start = (i - 1) * pageSize;
			int end = i * pageSize > totalSize ? (totalSize) : i * pageSize;
			mapper.batchInsert(payrollRecords.subList(start, end));
		}
		return payrollRecords.size();
	}

	@Override
	public Integer deleteByEmployeeIdAndDate(String employeeId, String computeDate) {
		Example example = new Example(HrmsNewsalaryPayroll.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo("payrollDate", computeDate);
		List<HrmsNewsalaryPayroll> list = mapper.selectByExample(example);
		//先删除明细 然后删除记录
		if(!list.isEmpty()){
			list.forEach(item->{
				hrmsNewsalaryPayrollDetailService.deleteByOptionId(item.getId());
				mapper.deleteByPrimaryKey(item.getId());

			});
		}
		return list.size();
	}

	@Override
//	@Transactional(readOnly = false)
	public Integer deleteByExample(String optionId, String payrollDate) {
		Example example = new Example(HrmsNewsalaryPayroll.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("optionId", optionId);
		criteria.andEqualTo("payrollDate", payrollDate);
		return mapper.deleteByExample(example);
	}

	//发送工资条统计
	@Override
	public Map<String, String> getCount(String computeDate) {
		List<SalarySendOut> count = mapper.getCount(computeDate);
		Map<String, String> map = new HashMap<>();
		map.put("optionName","");
		map.put("optionId","");
		map.put("zrs","");
		map.put("yf","");
		map.put("wf","");
		map.put("yck","");
		map.put("wck","");
		if(count != null && count.size() ==1){
			SalarySendOut salarySendOut = count.get(0);
			map.put("optionName",salarySendOut.getOptionName());
			map.put("optionId",salarySendOut.getOptionId());
			map.put("zrs",salarySendOut.getZrs());
			map.put("yf",salarySendOut.getYf());
			map.put("wf",salarySendOut.getWf());
			map.put("yck",salarySendOut.getYck());
			map.put("wck",salarySendOut.getWck());
		}
		return  map;
	}

	@Override
	public List<Map<String, String>> getCountList(String computeDate) {
		List<Map<String, String>> count = mapper.getCountList(computeDate);
		return count;
	}

	//批量发送工资条
	@Override
//	@Transactional(readOnly = false)
	public Integer batchSend(String computeDate, List<String> optionId,String ids) {
		Assert.hasText(computeDate, "发放月份不能为空");
		if(!optionId.isEmpty()){
			for (int i = 0; i < optionId.size(); i++) {
				//判断不为空
				HrmsNewsalaryOptionPayroll req = new HrmsNewsalaryOptionPayroll();
				req.setOptionId(optionId.get(i));
				req.setComputeDate(computeDate);
				HrmsNewsalaryOptionPayroll calculationStatus = hrmsNewsalaryOptionPayrollService.getCalculationStatus(req);
				if(calculationStatus != null && "3".equals(calculationStatus.getComputeStatus())){
					Example example = new Example(HrmsNewsalaryPayroll.class);
					Example.Criteria criteria = example.createCriteria();
					criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
					criteria.andEqualTo("payrollDate", computeDate);
					criteria.andIn("optionId",optionId);
					
					if(StringUtils.isNotBlank(ids)) {
						criteria.andIn("id",Arrays.asList(ids.split(",")));
					}
					
					HrmsNewsalaryPayroll record = new HrmsNewsalaryPayroll();
					record.setSendStatus("1");
					record.setIsView("0");
					record.setSendTime(DateUtils.getPresentTimeStr());
					mapper.updateByExampleSelective(record,example);
					
					calculationStatus.setPaySlip("1");
					hrmsNewsalaryOptionPayrollService.update(calculationStatus);
					
					//查询发薪号
					List<String> receiver =  mapper.selectEmpPayroll(optionId.get(i),computeDate,null,null);
					if(!appUrl.endsWith("/")){
						appUrl += "/";
					}
					//调用手机通知
					NoticeReq notice = NoticeReq.builder()
							.content(computeDate + "的薪酬工资条已发送，请注意查收！")
							.noticeType("3").receiver(StringUtils.join(receiver, ","))
							.sender(UserInfoHolder.getCurrentUserCode()).senderName(UserInfoHolder.getCurrentUserName())
							.subject(computeDate + "薪酬工资条")
							.url(appUrl + "mobile-container/ts-mobile-oa/pages/payslip/salary-payslip-details?fromPage=salary-payslip-list&payrollDate=" + computeDate
									+ "&optionId=" + optionId.get(i))
							.wxSendType("1").build();
					
					NoticeService noticeService = new NoticeService();
					noticeService.sendNotice(notice);
				}
			}
			
		}else{
			throw new BusinessException("请选择要发送的薪酬组");
		}
		
		return optionId.size();
	}

	//全部撤回接口
	@Override
//	@Transactional(readOnly = false)
	public Integer revocation(HrmsNewsalaryPayroll record) {
		
		if(StringUtils.isNotBlank(record.getIds())) {
			
			record.setIdList(Arrays.asList(record.getIds().split(",")));
			
			Integer revocation = mapper.revocation(record);
			
			//查询发薪号
			List<String> receiver = mapper.selectEmpPayroll(record.getOptionId(),record.getPayrollDate(),null,null);
			
			//调用手机通知
			NoticeReq notice = NoticeReq.builder()
					.content(record.getPayrollDate() + "的薪酬工资条已被管理员撤回，请知悉！")
					.noticeType("3").receiver(StringUtils.join(receiver, ","))
					.sender(UserInfoHolder.getCurrentUserCode()).senderName(UserInfoHolder.getCurrentUserName())
					.wxSendType("2").build();
			
			NoticeService noticeService = new NoticeService();
			noticeService.sendNotice(notice);
			return revocation;
		}
		return 0;
	}

	//单个撤回
//	@Transactional(readOnly = false)
	@Override
	public Integer singleRevocation(HrmsNewsalaryPayroll record) {
		
		Integer singleRevocation = mapper.singleRevocation(record);
		
		
		HrmsNewsalaryPayroll hrmsNewsalaryPayroll = mapper.selectByPrimaryKey(record.getId());
		
		//查询发薪号
		List<String> receiver = mapper.selectEmpPayroll(hrmsNewsalaryPayroll.getOptionId(),hrmsNewsalaryPayroll.getPayrollDate(),hrmsNewsalaryPayroll.getEmployeeId(),null);
		
		//调用手机通知
		NoticeReq notice = NoticeReq.builder()
				.content(hrmsNewsalaryPayroll.getPayrollDate() + "的薪酬工资条已被管理员撤回，请知悉！")
				.noticeType("3").receiver(StringUtils.join(receiver, ","))
				.sender(UserInfoHolder.getCurrentUserCode()).senderName(UserInfoHolder.getCurrentUserName())
				.wxSendType("2").build();
		
		NoticeService noticeService = new NoticeService();
		noticeService.sendNotice(notice);
		 
		return singleRevocation;
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer singleSend(HrmsNewsalaryPayroll record) {
		
		Integer singleSend = mapper.singleSend(record);
		
		HrmsNewsalaryPayroll hrmsNewsalaryPayroll = mapper.selectByPrimaryKey(record.getId());
		
		//查询发薪号
		List<String> receiver = mapper.selectEmpPayroll(hrmsNewsalaryPayroll.getOptionId(),hrmsNewsalaryPayroll.getPayrollDate(),hrmsNewsalaryPayroll.getEmployeeId(),null);

		if(!appUrl.endsWith("/")){
			appUrl += "/";
		}
		//调用手机通知
		NoticeReq notice = NoticeReq.builder()
				.content(hrmsNewsalaryPayroll.getPayrollDate() + "的薪酬工资条已发送，请注意查收！")
				.noticeType("3").receiver(StringUtils.join(receiver, ","))
				.sender(UserInfoHolder.getCurrentUserCode()).senderName(UserInfoHolder.getCurrentUserName())
				.subject(hrmsNewsalaryPayroll.getPayrollDate() + "薪酬工资条")
				.url(appUrl + "mobile-container/ts-mobile-oa/pages/payslip/salary-payslip-details?fromPage=salary-payslip-list&payrollDate="
				+ hrmsNewsalaryPayroll.getPayrollDate() + "&optionId=" + hrmsNewsalaryPayroll.getOptionId())
				.wxSendType("1").build();
		
		NoticeService noticeService = new NoticeService();
		noticeService.sendNotice(notice);	
		
		return singleSend;
	}

	@Override
	public List<VueTableEntity> getSalaryMonthRecordHeadList(String employeeId) {
		Assert.hasText(employeeId, "员工id不能为空!");
		//查询员工所在薪酬方案组
		Example example = new Example(HrmsNewsalaryOptionEmp.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeId",employeeId);
		criteria.andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
		HrmsNewsalaryOptionEmp hrmsNewsalaryOptionEmp = hrmsNewsalaryOptionEmpMapper.selectOneByExample(example);
		List<VueTableEntity> retVueTableEntity = new ArrayList<>();
		if(ObjectUtil.isNotEmpty(hrmsNewsalaryOptionEmp)){
			String optionId = hrmsNewsalaryOptionEmp.getOptionId();
			retVueTableEntity.add(new VueTableEntity("薪酬组","optionName",null,null,null));
			List<HrmsNewsalaryItem> optionItemList = hrmsNewsalaryItemService.getByOption(optionId);
			if(CollUtil.isNotEmpty(optionItemList)){
				int width= 20;
				optionItemList.forEach(item->{
					retVueTableEntity.add(new VueTableEntity(item.getItemName(),item.getId(),null,item.getItemName().length() == 2 ? 80 : item.getItemName().length() * width,null));
				});
			}
		}
		return retVueTableEntity;
	}

	@Override
	public DataSet<HrmsNewsalaryPayrollVo> selectNewsalaryPayrollList(Page page, HrmsNewsalaryPayrollVo record) {
		
		List<HrmsNewsalaryPayrollVo> records = mapper.selectNewsalaryPayrollList( page,record);

		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

//	@Override
//	public Map<String, Object> summaryData(HrmsNewsalaryPayrollVo record) {
//		return mapper.summaryData(record);
//	}

	@Override
	public List<Map<String, Object>> summaryData(HrmsNewsalaryPayrollVo record) {
		return mapper.summaryData(record);
	}

	@Override
	public Map<String, Object> sendSalaryDeatilsSummaryData(HrmsNewsalaryPayrollVo record) {
		return mapper.sendSalaryDeatilsSummaryData(record);
	}

	@Override
	public List<Map<String, String>> getPayrollByEmployeeId(String employeeId, String computeDate) {
		return mapper.getPayrollByEmployeeId(employeeId,computeDate);
	}



	@Override
	public HrmsNewsalaryPayroll selectByEmpId(String employeeId, String computeDate) {
		HrmsNewsalaryPayroll record = new HrmsNewsalaryPayroll();
		record.setEmployeeId(employeeId);
		record.setPayrollDate(computeDate);
		record.setIsDeleted("N");
		return mapper.selectOne(record);
	}
}

package cn.trasen.hrms.salary.model;

import cn.hutool.core.date.DateUtil;
import cn.trasen.hrms.utils.DateUtils;
import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * 薪级定期调整提醒设置表
 *
 */
@Table(name = "hrms_newsalary_level_remind_setting")
@Setter
@Getter
public class HrmsNewsalaryLevelRemindSetting {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 薪酬提醒ID
     */
    @Column(name = "remind_id")
    @ApiModelProperty(value = "薪酬提醒ID")
    private String remindId;

    /**
     * 员工状态
     */
    @Column(name = "employee_status")
    @ApiModelProperty(value = "员工状态")
    private String employeeStatus;

    /**
     * 编制类型
     */
    @Column(name = "establishment_type")
    @ApiModelProperty(value = "编制类型")
    private String establishmentType;

    /**
     * 0-本年 1-上年
     */
    @Column(name = "assess_year")
    @ApiModelProperty(value = "0-本年 1-上年")
    private String assessYear;

    /**
     * 考核结果,多个值之间用逗号分割
     */
    @Column(name = "assess_result")
    @ApiModelProperty(value = "考核结果,多个值之间用逗号分割")
    private String assessResult;

    /**
     * 入院时间满(年)
     */
    @Column(name = "year_num")
    @ApiModelProperty(value = "入院时间满(年)")
    private String yearNum;

    /**
     * 自动调整时间
     */
    @Column(name = "auto_adjust_date")
    @ApiModelProperty(value = "自动调整时间")
    private String autoAdjustDate;

    /**
     * 是否启用  1正常  0停用
     */
    @Column(name = "is_enabled")
    @ApiModelProperty(value = "是否启用  1正常  0停用")
    private String isEnabled;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    /**
     * 下次调整时间
     */
    @Transient
    @ApiModelProperty(value = "下次调整时间")
    private String nextAdjustDate;

    public String getNextAdjustDate(){
        if(StringUtils.isNotBlank(this.autoAdjustDate)){
            Date nowDate = DateUtil.parse((DateUtils.getStringDateShort(new Date())),"yyyy-MM-dd");
            int nowYear = DateUtil.year(nowDate);
            Date yearAdjustDate = DateUtil.parse(nowYear+"-"+this.autoAdjustDate,"yyyy-MM-dd");
            if(nowDate.compareTo(yearAdjustDate) > 0){
                return (nowYear+1)+"-"+this.autoAdjustDate;
            }else{
                return nowYear+"-"+this.autoAdjustDate;
            }
        }
        return null;
    }
}
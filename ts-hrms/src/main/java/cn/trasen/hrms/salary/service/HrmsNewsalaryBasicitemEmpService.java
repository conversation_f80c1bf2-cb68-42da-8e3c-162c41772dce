package cn.trasen.hrms.salary.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.DTO.NewSalaryBasicItemBatchAdjustReq;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicColumn;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp;

/**
 * @ClassName HrmsNewsalaryBasicitemEmpService
 * @Description TODO
 * @date 2023��11��11�� ����4:32:36
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryBasicitemEmpService {





	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:32:36
	 * <AUTHOR>
	 */
	Integer save(List<HrmsNewsalaryBasicitemEmp> record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:32:36
	 * <AUTHOR>
	 */
	Integer update(List<HrmsNewsalaryBasicitemEmp> record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��11��11�� ����4:32:36
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryBasicitemEmp
	 * @date 2023��11��11�� ����4:32:36
	 * <AUTHOR>
	 */
	HrmsNewsalaryBasicitemEmp selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryBasicitemEmp>
	 * @date 2023��11��11�� ����4:32:36
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryBasicitemEmp> getDataSetList(Page page, HrmsNewsalaryBasicitemEmp record);
	/**
	 * @Title 根据id查询人员定薪信息
	 * @return
	 * @date 2023��11��11�� ����4:32:36
	 * <AUTHOR>
	 */
	List<HrmsNewsalaryBasicitemEmp> getDataByEmployeeId(String employeeId);

	/**
	 * 查询薪酬档案上面基本信息
	 * @param id
	 * @return
	 */
    Map<String, Object> getBaseData(String id);

	/**
	 * 定薪页面进来的查询方法
	 * @param record
	 * @return
	 */
	List<HrmsNewsalaryBasicColumn> getAllData(HrmsNewsalaryBasicColumn record);

	/**
	 * 调薪保存
	 * @param records
	 * @return
	 */
	Integer adjustSave(List<HrmsNewsalaryBasicitemEmp> records);

	/**
	 * 批量调薪
	 * @param record
	 */
	String batchAdjustSave(NewSalaryBasicItemBatchAdjustReq record);

    void taskAdjustSalary();

	/**
	 * 根据人员id拿到定薪基本信息
	 * @param empIds
	 * @return
	 */
	List<HrmsNewsalaryBasicitemEmp> getBasicSalaryByEmpIds(List<String> empIds);

	/**
	 * 批量导入员工薪酬项数据的方法
	 * @param data
	 * @param isSalaryAppoint 是否定薪 false-调薪 true-定薪
	 * @return
	 */
	Integer importEntryTemplateData(List<Map<String, String>> data,boolean isSalaryAppoint);

    Integer importUpdateEntryTemplateData(List<Map<String, String>> data);

	/**
	 * 其他规则查询
	 * @param empIds
	 * @return
	 */
//	List<HrmsNewsalaryBasicitemEmp> getCustomRuleByIds(List empIds);

	/**
	 * 根据类型和员工查询定薪项
	 * @param empIds
	 * @return
	 */
	List<HrmsNewsalaryBasicitemEmp> getEmpByTypeAndIds(String type,List empIds);

//	void calculaAchive(List<String> empIds);

	/**
	 * 根据人员查询定薪项
	 * @param employeeId
	 * @return
	 */
//	List<HrmsNewsalaryBasicitemEmp> getBasicByEmployeeId(String employeeId);

	/**
	 * 根据员工id计算当前已生效的定薪调薪数据
	 * @param empId
	 */
	void CalculatSingle(String empId);

	/**
	 * 根据员工id和生效日期计算当前未生效的定薪调薪数据
	 * @param empId
	 * @param effectiveDate
	 */
	void CalculatSingle2Ineffective(String empId,String effectiveDate);

}

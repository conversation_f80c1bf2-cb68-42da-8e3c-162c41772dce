package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryRemindSetting;

/**
 * @ClassName HrmsNewsalaryRemindSettingService
 * @Description TODO
 * @date 2024��10��25�� ����3:46:52
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryRemindSettingService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��10��25�� ����3:46:52
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryRemindSetting record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��10��25�� ����3:46:52
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryRemindSetting record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��10��25�� ����3:46:52
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryRemindSetting
	 * @date 2024��10��25�� ����3:46:52
	 * <AUTHOR>
	 */
	HrmsNewsalaryRemindSetting selectById(String id);

	/**
	 * @Title selectByType
	 * @Description 根据类型查询
	 * @return HrmsNewsalaryRemindSetting
	 * @date 2024��10��25�� ����3:46:52
	 * <AUTHOR>
	 */
	HrmsNewsalaryRemindSetting selectByType(Integer remindType);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryRemindSetting>
	 * @date 2024��10��25�� ����3:46:52
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryRemindSetting> getDataSetList(Page page, HrmsNewsalaryRemindSetting record);
}

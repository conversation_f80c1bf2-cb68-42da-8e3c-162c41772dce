package cn.trasen.hrms.salary.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalarySeniorityWage;
import cn.trasen.hrms.salary.service.HrmsNewsalarySeniorityWageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalarySeniorityWageController
 * @Description TODO
 * @date 2024��4��12�� ����11:08:24
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsNewsalarySeniorityWageController")
public class HrmsNewsalarySeniorityWageController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalarySeniorityWageController.class);

	@Autowired
	private HrmsNewsalarySeniorityWageService hrmsNewsalarySeniorityWageService;

	/**
	 * @Title saveHrmsNewsalarySeniorityWage
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��4��12�� ����11:08:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/newsalarySeniorityWage/save")
	public PlatformResult<String> saveHrmsNewsalarySeniorityWage(@RequestBody HrmsNewsalarySeniorityWage record) {
		try {
			hrmsNewsalarySeniorityWageService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalarySeniorityWage
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��4��12�� ����11:08:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/newsalarySeniorityWage/update")
	public PlatformResult<String> updateHrmsNewsalarySeniorityWage(@RequestBody HrmsNewsalarySeniorityWage record) {
		try {
			hrmsNewsalarySeniorityWageService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalarySeniorityWageById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalarySeniorityWage>
	 * @date 2024��4��12�� ����11:08:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/newsalarySeniorityWage/{id}")
	public PlatformResult<HrmsNewsalarySeniorityWage> selectHrmsNewsalarySeniorityWageById(@PathVariable String id) {
		try {
			HrmsNewsalarySeniorityWage record = hrmsNewsalarySeniorityWageService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalarySeniorityWageById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��4��12�� ����11:08:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/newsalarySeniorityWage/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalarySeniorityWageById(@PathVariable String id) {
		try {
			hrmsNewsalarySeniorityWageService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalarySeniorityWageList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalarySeniorityWage>
	 * @date 2024��4��12�� ����11:08:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/newsalarySeniorityWage/list")
	public DataSet<HrmsNewsalarySeniorityWage> selectHrmsNewsalarySeniorityWageList(Page page, HrmsNewsalarySeniorityWage record) {
		return hrmsNewsalarySeniorityWageService.getDataSetList(page, record);
	}
}

package cn.trasen.hrms.salary.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 薪酬项目基本库
 *
 */
@Table(name = "hrms_newsalary_item_library")
@Setter
@Getter
public class HrmsNewsalaryItemLibrary {
    /**
     * 主键id
     */
    @Id
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 薪酬项编码
     */
    @Column(name = "library_code")
    @ApiModelProperty(value = "薪酬项编码")
    private String libraryCode;

    /**
     * 薪酬项编码
     */
    @Column(name = "item_code")
    @ApiModelProperty(value = "薪酬项编码")
    private String itemCode;

    /**
     * 薪酬项目名称
     */
    @Column(name = "item_name")
    @ApiModelProperty(value = "项目名称")
    private String itemName;

    /**
     * 1基础信息2工资项
     */
    @Column(name = "item_type")
    @ApiModelProperty(value = "1基础信息2工资项")
    private String itemType;

    /**
     * 项目类型 1基本工资,2绩效工资,3考勤工资,4社保公积金,5个税,6福利补贴,7其他,8自定义
     */
    @Column(name = "library_type")
    @ApiModelProperty(value = "项目类型 1基本工资,2绩效工资,3考勤工资,4社保公积金,5个税,6福利补贴,7其他,8自定义")
    private String libraryType;

    /**
     * 进位规则 1四舍五入,2向下取数,3向上取数
     */
    @Column(name = "carry_rule")
    @ApiModelProperty(value = "进位规则 1四舍五入,2向下取数,3向上取数")
    private String carryRule;

    /**
     * 小数位
     */
    @Column(name = "item_digit")
    @ApiModelProperty(value = "小数位")
    private String itemDigit;

    /**
     * 1手工录入2固定值3从薪资档案取4自定义公式5自定义规则
     */
    @Column(name = "item_rule")
    @ApiModelProperty(value = "1手工录入2固定值3从薪资档案取4自定义公式5自定义规则")
    private String itemRule;

    /**
     * 来源 1引用,2自定义
     */
    @Column(name = "item_source")
    @ApiModelProperty(value = "来源 1引用,2自定义")
    private String itemSource;

    /**
     * 1加项 2减项3不参与计算
     */
    @Column(name = "count_type")
    @ApiModelProperty(value = "1加项 2减项3不参与计算")
    private String countType;

    /**
     * 备注
     */
    @Column(name = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 状态 1启用,2停用
     */
    @Column(name = "status")
    @ApiModelProperty(value = "状态 1启用,2停用")
    private String status;

    /**
     * 排序
     */
    @Column(name = "sort_no")
    @ApiModelProperty(value = "排序")
    private Integer sortNo;

    /**
     * 计算公式
     */
    @Column(name = "count_formula")
    @ApiModelProperty(value = "计算公式")
    private String countFormula;

    /**
     * 计算公式文本
     */
    @Column(name = "count_formula_text")
    @ApiModelProperty(value = "计算公式文本")
    private String countFormulaText;

    /**
     * 固定金额
     */
    @Column(name = "salary_item_amount")
    @ApiModelProperty(value = "固定金额")
    private BigDecimal salaryItemAmount;


    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 薪酬档案定薪id
     */
    @Column(name = "basic_item_id")
    @ApiModelProperty(value = "薪酬档案定薪id")
    private String basicItemId;

    /**
     * 下月引用当月数据
     */
    @Column(name = "next_month")
    @ApiModelProperty(value = "下月引用当月数据")
    private String nextMonth;

    /**
     * 自定义项目
     */
    @Column(name = "custom_rule")
    @ApiModelProperty(value = "自定义项目")
    private String customRule;

    /**
     * 基础库分组id
     */
    @Transient
    @ApiModelProperty(value = "基础库分组id")
    private String groupId;

    @ApiModelProperty(value = "来源类型(1.修改项2.定薪项)")
    private Integer sourceType;
}
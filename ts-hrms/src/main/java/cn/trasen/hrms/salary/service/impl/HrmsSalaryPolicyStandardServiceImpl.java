package cn.trasen.hrms.salary.service.impl;

import java.util.Date;
import java.util.List;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.bean.base.CommTableSnapshotSaveReq;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.base.FileAttachmentResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.CommTableSnapshotFeignService;
import cn.trasen.homs.feign.base.FileAttachmentFeignService;
import cn.trasen.hrms.model.HrmsSalaryLevel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import cn.trasen.hrms.salary.dao.HrmsSalaryPolicyStandardMapper;
import cn.trasen.hrms.salary.model.HrmsSalaryPolicyStandard;
import cn.trasen.hrms.salary.service.HrmsSalaryPolicyStandardService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @ClassName HrmsSalaryPolicyStandardServiceImpl
 * @Description TODO
 * @date 2025��2��24�� ����11:15:17
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsSalaryPolicyStandardServiceImpl implements HrmsSalaryPolicyStandardService {

	@Resource
	private HrmsSalaryPolicyStandardMapper mapper;

	@Autowired
	FileAttachmentFeignService fileAttachmentFeignService;

	@Autowired
	CommTableSnapshotFeignService commTableSnapshotFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsSalaryPolicyStandard record) {
		record.setPolicyStandardId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		// 增加处理快照
		CommTableSnapshotSaveReq commTableSnapshotSaveReq = new CommTableSnapshotSaveReq();
		commTableSnapshotSaveReq.setTableName("hrms_salary_policy_standard");
		commTableSnapshotSaveReq.setRowPkValue(record.getPolicyStandardId());
		commTableSnapshotSaveReq.setNow(record);
		fillDescField(record);
		commTableSnapshotFeignService.save(commTableSnapshotSaveReq);
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsSalaryPolicyStandard record) {
		HrmsSalaryPolicyStandard o = new HrmsSalaryPolicyStandard();
		if (!StringUtils.isBlank(record.getPolicyStandardId())) {
			o = mapper.selectByPrimaryKey(record.getPolicyStandardId());
		}

		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		int res = mapper.updateByPrimaryKeySelective(record);

		HrmsSalaryPolicyStandard n = mapper.selectByPrimaryKey(record.getPolicyStandardId());

		// 增加快照
		CommTableSnapshotSaveReq commTableSnapshotSaveReq = new CommTableSnapshotSaveReq();
		commTableSnapshotSaveReq.setTableName("hrms_salary_policy_standard");
		commTableSnapshotSaveReq.setRowPkValue(record.getPolicyStandardId());
		fillDescField(o);
		commTableSnapshotSaveReq.setOld(o);
		fillDescField(n);
		commTableSnapshotSaveReq.setNow(n);
		commTableSnapshotFeignService.save(commTableSnapshotSaveReq);

		return res;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsSalaryPolicyStandard record = new HrmsSalaryPolicyStandard();
		record.setPolicyStandardId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsSalaryPolicyStandard selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsSalaryPolicyStandard> getDataSetList(Page page, HrmsSalaryPolicyStandard record) {
		Example example = new Example(HrmsSalaryPolicyStandard.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(record!=null && StringUtils.isNotBlank(record.getPolicyStandardName())){
			criteria.andLike("policyStandardName",record.getPolicyStandardName());
		}
		if(record!=null && StringUtils.isNotBlank(record.getIsEnable())){
			criteria.andLike("isEnable",record.getIsEnable());
		}
		example.setOrderByClause(" sort_no,create_date desc");
		List<HrmsSalaryPolicyStandard> records = mapper.selectByExampleAndRowBounds(example, page);
		if(CollUtil.isNotEmpty(records)){
			records.stream().forEach(vo->{
				if(StringUtils.isNotBlank(vo.getFileId())){
					List<FileAttachmentResp>  fileAttachmentRespList=fileAttachmentFeignService.getFileAttachmentByBusinessId(vo.getFileId()).getObject();
					vo.setFileList(fileAttachmentRespList);
				}
			});
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsSalaryPolicyStandard> getList(HrmsSalaryPolicyStandard record) {
		Example example = new Example(HrmsSalaryPolicyStandard.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		example.setOrderByClause("sort_no asc,create_date desc");
		if(record!= null && StringUtils.isNotBlank(record.getIsEnable())){
			criteria.andEqualTo("isEnable", record.getIsEnable());
		}
		if(record!= null && StringUtils.isNotBlank(record.getSsoOrgCode())){
			criteria.andEqualTo("ssoOrgCode", record.getSsoOrgCode());
		}
		List<HrmsSalaryPolicyStandard> records = mapper.selectByExample(example);
		return records;
	}

	private void fillDescField(HrmsSalaryPolicyStandard record) {
		// 填充isEnableLable
		if (StringUtils.isNotBlank(record.getIsEnable())) {
			if(record.getIsEnable().equals("1")){
				record.setIsEnableLable("启用");
			}else{
				record.setIsEnableLable("停用");
			}
		}
	}
}

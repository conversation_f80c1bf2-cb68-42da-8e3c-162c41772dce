package cn.trasen.hrms.salary.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.*;

/**
 * 员工定薪项目历史表
 *
 */
@Table(name = "hrms_newsalary_basicitem_emp_history")
@Setter
@Getter
public class HrmsNewsalaryBasicitemEmpHistory {
    /**
     * 主键id
     */
    @Id
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 人员id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "人员id")
    private String employeeId;


    @Column(name = "basic_item_type")
    @ApiModelProperty(value = "类型")
    private String basicItemType;


    /**
     * 基本定薪项id
     */
    @Column(name = "basic_item_id")
    @ApiModelProperty(value = "基本定薪项id")
    private String basicItemId;

    @Transient
    @ApiModelProperty(value = "基本定薪项名称")
    private String basicItemName;


    @Transient
    @ApiModelProperty(value = "1 需要对比的列")
    private String compare;



    /**
     * 字段名称
     */
    @Column(name = "emp_field")
    @ApiModelProperty(value = "字段名称")
    private String empField;

    @Column(name = "emp_field_value")
    @ApiModelProperty(value = "字段名称值")
    private String empFieldValue;


    @Column(name = "emp_field_value_text")
    @ApiModelProperty(value = "字段名称值中文")
    private String empFieldValueText;

    /**
     * 定薪金额
     */
    @Column(name = "salary_amount")
    @ApiModelProperty(value = "定薪金额")
    private BigDecimal salaryAmount;

    /**
     * 调薪原因
     */
    @ApiModelProperty(value = "调薪原因")
    private String reason;

    /**
     * 生效日期
     */
    @Column(name = "effective_date")
    @ApiModelProperty(value = "生效日期")
    private String effectiveDate;

    /**
     * 0 未生效1已生效 2已过期
     */
    @Column(name = "data_status")
    @ApiModelProperty(value = "生效状态")
    private String dataStatus;

    /**
     * 历史版本号从1开始 自增长
     */
    @Column(name = "v_number")
    @ApiModelProperty(value = "历史版本号从1开始 自增长")
    private String vNumber;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    @Transient
    @ApiModelProperty(value = "字段名称值 字典对应的中文")
    private String empFieldText;

    @Transient
    private List<String> allTypeList;  //查询条件
}
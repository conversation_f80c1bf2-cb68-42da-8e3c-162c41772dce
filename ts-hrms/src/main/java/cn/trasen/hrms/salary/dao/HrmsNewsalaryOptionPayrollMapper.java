package cn.trasen.hrms.salary.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.DTO.*;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemRemindSetting;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.model.HrmsNewsalaryReportTotalEo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface HrmsNewsalaryOptionPayrollMapper extends Mapper<HrmsNewsalaryOptionPayroll> {

    Integer getSetCount(@Param("computeDate") String computeDate,@Param("optionId") String optionId, @Param("empIds") List<String> empIds);

    Integer getUpdateCount(@Param("computeDate") String computeDate,@Param("optionId") String optionId, @Param("empIds") List<String> empIds);

    List<CheckPersonnel> checkPersonnel(Page page, SearchListTable record);

    //薪酬核算定薪调薪列表表头
    List<BasicItemVO> makePayTableTitle(HrmsNewsalaryOptionPayroll record);

    //薪酬核算 - 核算数据
    List<Map<String, String>> getCalculateWagesData(Page page, HrmsNewsalaryOptionPayroll record);

    //获取上个月手工录入的数据
    List<LastSalaryOut> getLastSalary(@Param("optionId") String optionId, @Param("lastDate") String lastDate);

    //获取手工导入的当月数据
    List<LastSalaryOut> getNowSalary(@Param("optionId") String optionId,@Param("computeDate") String computeDate);

    List<HrmsNewsalaryOptionPayroll> getDataByComputeDate(Page page,HrmsNewsalaryOptionPayroll record);

    List<Map<String, String>> getCalculateWagesDataCurrent(Page page, HrmsNewsalaryOptionPayroll record);

    List<BasicItemVO> makePayTableTitleCount(HrmsNewsalaryOptionPayroll record);

    List<Map<String, Object>> salaryCountData(Page page,@Param("scsr") SalaryCountSearchReq scsr,@Param("salaryList") List<HrmsNewsalaryItem> salaryList, @Param("ssoOrgCode") String ssoOrgCode);

    Map<String,Integer> checkPersonnelCount(SearchListTable record);

    Map<String,Object> getHistoryDataCount(SearchListTable record);

    List<String> getMonthEntryFix(HrmsNewsalaryOptionPayroll record);

    List<String> getMonthEntryUnFix(HrmsNewsalaryOptionPayroll record);

    List<String> getChangeFix(HrmsNewsalaryOptionPayroll record);

    Integer getDtx(SearchListTable record);

    List<CheckPersonnel> getCheperList(Page page, SearchListTable record);

    List<Map<String, Object>> salaryTotalCountData(Page page,@Param("scsr") SalaryCountSearchReq scsr,@Param("totalList") List<HrmsNewsalaryReportTotalEo> totalList,
                                                   @Param("optionList") List<String> optionList,@Param("itemIdList") List<String> itemIdList,
                                                   @Param("ssoOrgCode") String ssoOrgCode);

    /**
     * 判断员工所在方案在薪酬方案是否已锁定 1-已锁定 0-未锁定 -1 -未绑定方案
     * @param computeDate
     * @param employeeId
     * @return
     */
    Integer getIsLockByEmpComputeDate(@Param("computeDate")  String computeDate,@Param("employeeId") String employeeId);

    /**
     * 根据员工id和核算月份获取薪酬项提醒设置对应的配置和金额
     * @param employeeId
     * @param computeDate
     * @return
     */
    List<HrmsNewsalaryItemRemindSetting> selectEmployeeSalaryChangeItemSettingDatas(@Param("employeeId") String employeeId, @Param("lastComputeDate")  String lastComputeDate,@Param("computeDate")  String computeDate);


    /**
     * 薪酬方案汇总报表数据
     * @param page
     * @param scsr
     * @param itemIdList
     * @return
     */
    List<Map<String, Object>> salaryOptionTotalCountData(Page page,@Param("scsr") SalaryCountSearchReq scsr,@Param("totalList") List<HrmsNewsalaryReportTotalEo> totalList, @Param("itemIdList") List<String> itemIdList);

}
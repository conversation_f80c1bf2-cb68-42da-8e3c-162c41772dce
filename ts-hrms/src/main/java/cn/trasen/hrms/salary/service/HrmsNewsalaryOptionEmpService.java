package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrmsNewsalaryOptionEmpService
 * @Description 方案人员
 * @date 2023��11��11�� ����4:36:17
 */
public interface HrmsNewsalaryOptionEmpService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2023��11��11�� ����4:36:17
     * <AUTHOR>
     */
    Integer save(HrmsNewsalaryOptionEmp record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2023��11��11�� ����4:36:17
     * <AUTHOR>
     */
    Integer update(HrmsNewsalaryOptionEmp record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2023��11��11�� ����4:36:17
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据OptionId删除所有人员
     * @date 2023��11��11�� ����4:36:17
     * <AUTHOR>
     */
    Integer deleteByOptionId(String id);

    /**
     * 人员排序
     * @param record
     * @return
     */
    Integer optionEmpSort(HrmsNewsalaryOptionEmp record);

    /**
     * @return HrmsNewsalaryOptionEmp
     * @Title selectById
     * @Description 根据ID查询
     * @date 2023��11��11�� ����4:36:17
     * <AUTHOR>
     */
    HrmsNewsalaryOptionEmp selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<HrmsNewsalaryOptionEmp>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2023��11��11�� ����4:36:17
     * <AUTHOR>
     */
    DataSet<HrmsNewsalaryOptionEmp> getDataSetList(Page page, HrmsNewsalaryOptionEmp record);

    //根据方案id查询方案下面的所有人
    List<HrmsNewsalaryOptionEmp> getAllByOptionId(String optionId, String empId);

    HrmsNewsalaryOptionEmp getEmpByOption(String id);

    List<HrmsNewsalaryOptionEmp> getEmployeeDetails(String optionId);

    List<HrmsNewsalaryOptionEmp> getAllEmployeeDetails(String optionId);

    List<HrmsNewsalaryOptionEmp> getOprionIdByEmpNo(List<String> empNo);

    HrmsNewsalaryOptionEmp destoryByEmployeeId(String employeeId);

    void perSave(String optionId, List<String> employeeIds);

    /**
     * 查询员工当前关联薪酬方案
     * @param employeeId
     * @return
     */
    HrmsNewsalaryOptionEmp getOptionByEmployeeId(String employeeId);

    /**
     * 移除员工当前所在薪酬方案
     * @param employeeId
     * @return
     */
    void deleteOptionByEmployeeId(String employeeId);

    /**
     * 移除人员
     * @param optionId
     * @param employeeIds
     */
    void deleteOption(String optionId, List<String> employeeIds);


    List<HrmsNewsalaryOptionEmp> getEmployeeDetailByOptionId(String optionId);
}

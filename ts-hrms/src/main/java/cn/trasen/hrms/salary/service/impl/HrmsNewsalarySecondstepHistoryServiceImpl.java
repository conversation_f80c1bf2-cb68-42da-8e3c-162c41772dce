package cn.trasen.hrms.salary.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.dao.HrmsNewsalarySecondstepHistoryMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.model.HrmsNewsalarySecondstepHistory;
import cn.trasen.hrms.salary.service.HrmsNewsalarySecondstepHistoryService;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsNewsalarySecondstepHistoryServiceImpl
 * @Description TODO
 * @date 2024��3��11�� ����5:53:01
 * <AUTHOR>
 * @version 1.0
 */
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalarySecondstepHistoryServiceImpl implements HrmsNewsalarySecondstepHistoryService {

	@Autowired
	private HrmsNewsalarySecondstepHistoryMapper mapper;

//	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalarySecondstepHistory record) {
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalarySecondstepHistory record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalarySecondstepHistory record = new HrmsNewsalarySecondstepHistory();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalarySecondstepHistory selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalarySecondstepHistory> getDataSetList(Page page, HrmsNewsalarySecondstepHistory record) {
		Example example = new Example(HrmsNewsalarySecondstepHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalarySecondstepHistory> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsNewsalarySecondstepHistory> getData(Page page, HrmsNewsalaryOptionPayroll record) {
		List<HrmsNewsalarySecondstepHistory> records = mapper.getData( page,record);
		return records;
	}

	@Override
	public Integer deleteByOptionIdAndDate(String optionId, String computeDate) {
		Example example = new Example(HrmsNewsalarySecondstepHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("payrollDate", computeDate);
		criteria.andEqualTo("optionId", optionId);
		return mapper.deleteByExample(example);
	}
}

package cn.trasen.hrms.salary.dao;

import cn.trasen.hrms.model.HrmsPostWageVo;
import cn.trasen.hrms.model.HrmsSalaryLevelWageVo;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface HrmsNewsalaryBasicitemEmpMapper extends Mapper<HrmsNewsalaryBasicitemEmp> {
    Map<String, String> getBaseData(@Param("id") String id, @Param("ssoOrgCode") String ssoOrgCode);

    //根据员工id返回定薪信息
    List<HrmsNewsalaryBasicitemEmp> getDataByEmployeeId(String employeeId);
    
    List<HrmsNewsalaryBasicitemEmp> getSalaryItemByOptionId(String optionId);

    //根据岗位等级id查询岗位等级名称
    String getGwdjText(String empFieldValue);

    String getSalaryLeavelText(String empFieldValue);

    //设置定薪
    Integer updateSalaryAppoint(String employeeId);

    Map<String, String> getBaseDataByEmpNo(@Param("employeeNo") String employeeNo, @Param("ssoOrgCode") String ssoOrgCode);

    List<String> getGwdjCode(@Param("plgwCode") String plgwCode, @Param("gwdjText") String gwdjText);

    List<String> getSalaryLevelId(@Param("salaryLevelTypeCode") String salaryLevelTypeCode, @Param("salaryLevelIdText")  String salaryLevelIdText);

    List<String> getGwSalary(@Param("plgwCode")  String plgwCode,@Param("gwdjCode")  String gwdjCode);  //获取岗位工资

    List<String> getXjSalary(@Param("salaryLevelTypeCode") String salaryLevelTypeCode,@Param("salaryLevelId")  String salaryLevelId);

    //修改人员岗位薪级
    Integer updateEmployeeSalary(@Param("employeeId") String employeeId, @Param("plgw") String plgw,
                                 @Param("gwdj")  String gwdj,  @Param("salaryLevelType") String salaryLevelType,
                                 @Param("salaryLevelId") String salaryLevelId);

    List<String> getJxSalary(@Param("plgwCode")  String plgwCode,@Param("gwdjCode")  String gwdjCode);
    
    Integer upsertSalaryBasicEmp(HrmsNewsalaryBasicitemEmp record);
    
    List<HrmsNewsalaryBasicitemEmp> getSalaryOptionEmp(@Param("optionId") String optionId, @Param("employeeId") String employeeId);

    List<HrmsNewsalaryBasicitemEmp> getSalaryOptionEmpEffDate(@Param("optionId") String optionId, @Param("employeeId") String employeeId, @Param("effectiveDate") String effectiveDate);

    String getEffectiveDate(@Param("employeeId") String employeeId, @Param("effectiveDate") String effectiveDate);
    
    List<HrmsNewsalaryItem> getSalaryItemLibrary();
    
    List<HrmsPostWageVo> selectPostWage();
    
    List<HrmsSalaryLevelWageVo> selectSalaryLevelWage();

    List<HrmsNewsalaryBasicitemEmp> getNewsalaryDataByEmployeeId(@Param("employeeId") String employeeId);
    
    List<String> getEmpIds();

}
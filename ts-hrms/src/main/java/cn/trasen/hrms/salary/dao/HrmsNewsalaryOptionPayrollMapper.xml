<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionPayrollMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="option_id" jdbcType="VARCHAR" property="optionId" />
    <result column="option_name" jdbcType="VARCHAR" property="optionName" />
    <result column="compute_date" jdbcType="VARCHAR" property="computeDate" />
    <result column="compute_status" jdbcType="CHAR" property="computeStatus" />
    <result column="head_count" jdbcType="INTEGER" property="headCount" />
    <result column="set_count" jdbcType="INTEGER" property="setCount" />
    <result column="update_count" jdbcType="INTEGER" property="updateCount" />
    <result column="compute_time" jdbcType="VARCHAR" property="computeTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>

  <!-- 查询定薪人数 -->
  <select id="getSetCount" resultType="java.lang.Integer">
      SELECT COUNT(*) FROM (
    SELECT
      employee_id
    FROM hrms_newsalary_basicitem_emp_history
    WHERE is_deleted='N'  AND (reason = '入职定薪' or reason = '')
      AND effective_date LIKE   CONCAT('%',#{computeDate},'%')
      <if test="empIds != null and empIds.size() > 0">
        and (employee_id in
        <foreach collection="empIds" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
        )
      </if>
      GROUP BY employee_id
    ) aaa
  </select>

  <select id="getUpdateCount" resultType="java.lang.Integer">
    SELECT
      count(distinct employee_id) AS dinxi
    FROM hrms_newsalary_basicitem_emp_history
    WHERE is_deleted='N'
      AND effective_date LIKE CONCAT('%',#{computeDate},'%')
      and reason!='入职定薪' and reason!=''
    <if test="empIds != null and empIds.size() > 0">
        and (employee_id in
        <foreach collection="empIds" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
        )
      </if>

  </select>

  <!-- 核对人员接口（未计算） -->
    <select id="checkPersonnel" resultType="cn.trasen.hrms.salary.DTO.CheckPersonnel"
            parameterType="cn.trasen.hrms.salary.DTO.SearchListTable">
      SELECT t2.employee_no,t2.employee_name,t1.employee_id,
             t2.establishment_type,
             t2.personal_identity,
             t2.employee_status,
             t2.entry_date,
             t2.positive_time,
             t2.retirement_time,
             t3.name as orgName,
             t4.option_name as optionName,
             t2.is_temp
      FROM hrms_newsalary_option_emp t1
             LEFT JOIN (
                SELECT
                    employee_name,employee_no,org_id,employee_id,gender,birthday,
                    employee_status,positive_time,retirement_time,
                    position_id,year_work,bankcardname,emp_age,
                    establishment_type,salary_appoint,is_deleted,
                    personal_identity,identity_number,bankcardno,
                    entry_date,'N' as is_temp
                FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
                UNION ALL
                SELECT
                    employee_name,employee_no,org_id,id as employee_id,gender,birthday,
                    (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
                    null as position_id,null as year_work,null as bankcardname,null as emp_age,
                    tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
                    tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
                    join_date as entry_date,'Y' as is_temp
                FROM  hrms_employee_temporary et where et.is_sync_salary = 'Y'
          ) t2 ON t1.employee_id = t2.employee_id
            LEFT JOIN comm_organization t3 ON t2.org_id = t3.organization_id
            LEFT JOIN hrms_newsalary_option t4 on t1.option_id = t4.id and t4.is_deleted = 'N'
      WHERE t1.is_deleted='N' AND t2.is_deleted='N'
            AND t1.option_id=#{optionId}
      <if test="employeeName != null and employeeName !=''">
            and ( t2.employee_name like CONCAT('%',#{employeeName},'%') or
                t2.employee_no like CONCAT('%',#{employeeName},'%') )
      </if>
        <if test='menu != null and menu == "2"'>
            and t2.employee_status = '1' and t2.salary_appoint is null
        </if>
        <if test='menu != null and menu == "3"'>
            and t2.employee_status in ('4','8')
        </if>
        <if test='menu != null and menu == "4"'>
            and t2.employee_id in (
            select distinct employee_id from hrms_newsalary_basicitem_emp_history
            where DATE_FORMAT(effective_date,'%Y-%m') = #{payrollDate}
            )
        </if>
        <if test="orgList != null and orgList.size() > 0">
            and (t2.org_id in
            <foreach collection="orgList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    <if test='employeeStatus != null and employeeStatus.size() > 0'>
        and t2.employee_status in
        <foreach collection="employeeStatus" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>

      <if test="orgName != null and orgName !=''">
         and  t3.name like CONCAT('%',#{orgName},'%')
      </if>
      <!-- 新入职 -->
    <if test='chageStatus != null and chageStatus == "1" '>
        and t2.entry_date like CONCAT('%',#{payrollDate},'%')

    </if>
        <!--    离职、退休 -->
    <if test='chageStatus != null and chageStatus == "2"'>
       and ( (t2.retirement_time like CONCAT('%',#{payrollDate},'%') )
             or(
                t1.employee_id in (
                SELECT employee_id FROM hrms_personnel_incident
                WHERE incident_category=1 AND is_deleted='N'
                AND incident_time LIKE CONCAT('%',#{payrollDate},'%')
                 )
            ) )
    </if>

	order by COALESCE(CAST(employee_no AS UNSIGNED),9999)
    </select>

    <!-- 薪酬核算定薪调薪列表表头-->
    <select id="makePayTableTitle" resultType="cn.trasen.hrms.salary.DTO.BasicItemVO">
        SELECT
            t2.basic_item_name,t2.compare,
            t1.basic_item_id
        FROM hrms_newsalary_basicitem_emp t1
        LEFT JOIN hrms_newsalary_basic_column t2 ON t1.basic_item_id= t2.id
        WHERE t2.is_deleted = 'N' AND t2.basic_item_type IN (1,2)
          and t1.employee_id in
              (
                  SELECT employee_id FROM hrms_newsalary_option_emp WHERE option_id =#{optionId} and is_deleted = 'N' 
              )
        GROUP BY t1.basic_item_id,t2.basic_item_name,t2.compare
        ORDER BY t2.number_sort ASC
    </select>


    <select id="getCalculateWagesData" resultType="java.util.Map">
        SELECT t2.employee_id,t2.employee_no,t2.employee_name,t3.name AS orgName,
               t2.personal_identity, t2.bankcardNo,
               t6.`ITEM_NAME` AS personal_identity_text,
               t2.establishment_type,
               t4.`ITEM_NAME` AS establishment_type_text,
               t5.ITEM_NAME AS employee_status_text,
               imp.update_date,
               imp.update_user_name,
               t2.employee_status,
               FLOOR(RAND() * 100 + 1) as defAmount
        <if test="salaryList != null and salaryList.size() > 0">
            ,
            <foreach collection="salaryList" index="index"
                     item="item" open="" separator="," close="">
                MAX(CASE  imp.item_id WHEN   #{item.id} THEN imp.salary ELSE NULL END) AS   '${item.id}'
            </foreach>
        </if>

        FROM hrms_newsalary_option_emp t1
         INNER JOIN (
            SELECT
                employee_name,employee_no,org_id,employee_id,gender,birthday,
                employee_status,positive_time,retirement_time,
                position_id,year_work,bankcardname,emp_age,
                establishment_type,salary_appoint,is_deleted,
                personal_identity,identity_number,bankcardno,
                entry_date,'N' as is_temp
            FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
            UNION ALL
            SELECT
                employee_name,employee_no,org_id,id as employee_id,gender,birthday,
                (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
                null as position_id,null as year_work,null as bankcardname,null as emp_age,
                tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
                tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
                join_date as entry_date,'Y' as is_temp
            FROM  hrms_employee_temporary et where et.is_sync_salary = 'Y'
        ) t2 ON t1.employee_id=t2.employee_id AND t2.is_deleted='N'
         LEFT JOIN comm_organization t3 ON t2.org_id=t3.organization_id
         LEFT JOIN (
            SELECT
            A.*
            FROM
            COMM_DICT_ITEM A
            LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
            WHERE
            B.TYPE_CODE = 'establishment_type'
            AND B.IS_DELETED = 'N'
            AND A.IS_DELETED = 'N'
            AND A.IS_ENABLE = '1'
            AND A.sso_org_code = #{ssoOrgCode}
         ) t4 ON t2.establishment_type = t4.item_code
         LEFT JOIN (
            SELECT
            A.*
            FROM
            COMM_DICT_ITEM A
            LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
            WHERE
            B.TYPE_CODE = 'employee_status'
            AND B.IS_DELETED = 'N'
            AND A.IS_DELETED = 'N'
            AND A.IS_ENABLE = '1'
            AND A.sso_org_code = #{ssoOrgCode}
         ) t5 ON t2.employee_status = t5.item_code
         LEFT JOIN (
            SELECT
            A.*
            FROM
            COMM_DICT_ITEM A
            LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
            WHERE
            B.TYPE_CODE = 'personal_identity'
            AND B.IS_DELETED = 'N'
            AND A.IS_DELETED = 'N'
            AND A.IS_ENABLE = '1'
            AND A.sso_org_code = #{ssoOrgCode}
        ) t6 ON t2.personal_identity = t6.item_code
        LEFT JOIN hrms_newsalary_payroll_detail_import imp ON t2.employee_no = imp.`employee_no` AND imp.`IS_DELETED`='N' AND  imp.import_date=#{computeDate}
        WHERE t1.is_deleted='N' and  t1.option_id=#{optionId}
        <if test="employeeName != null and employeeName !=''">
            and ( t2.employee_name like CONCAT('%',#{employeeName},'%') or
            t2.employee_no like CONCAT('%',#{employeeName},'%') )
        </if>
        <if test="orgList != null and orgList.size() > 0">
            and (t2.org_id in
            <foreach collection="orgList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test='employeeStatus != null and employeeStatus.size() > 0'>
            and t2.employee_status in
            <foreach collection="employeeStatus" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="orgName != null and orgName !=''">
            and  t3.name like CONCAT('%',#{orgName},'%')
        </if>

        <if test="reason != null and reason !=''">
            and  t1.reason like CONCAT('%',#{reason},'%')
        </if>
        GROUP BY  t1.employee_id,t2.employee_no,t2.employee_name,t3.name,
             t2.personal_identity, t2.bankcardNo,t6.ITEM_NAME,
             t2.establishment_type,t2.employee_status
        order by t1.sort_num
    </select>

    <select id="getCalculateWagesDataCurrent" resultType="java.util.Map">
        SELECT t2.employee_id,t2.employee_no,t2.employee_name,t3.name AS orgName,
        t2.personal_identity, t2.bankcardNo,
        t6.`ITEM_NAME` AS personal_identity_text,
        t2.establishment_type,
        t4.`ITEM_NAME` AS establishment_type_text,
        t5.ITEM_NAME AS employee_status_text,
        t2.employee_status,
        t2.birthday,
        t2.phone_number,
        nsp.update_user_name,
        nsp.update_date,
        nsp.id,
        sps.policy_standard_name,
	    IF(nsp.send_status ='1','已发送','未发送') AS  send_status_text,
        IF(nsp.is_view ='1','已查看','未查看') AS  is_view_text,
	    nsp.send_status,
        FLOOR(RAND() * 100 + 1) as defAmount,
        opt.option_name
        <if test="salaryList != null and salaryList.size() > 0">
            ,
            <foreach collection="salaryList" index="index"
                     item="item" open="" separator="," close="">
                MAX(CASE  npd.item_id WHEN   #{item.itemId} THEN npd.salary ELSE NULL END) AS   '${item.itemId}'
            </foreach>
        </if>
        FROM hrms_newsalary_payroll nsp
        INNER JOIN (
            SELECT
                employee_name,employee_no,org_id,employee_id,gender,birthday,
                employee_status,positive_time,retirement_time,
                position_id,year_work,bankcardname,emp_age,
                establishment_type,salary_appoint,is_deleted,
                personal_identity,identity_number,bankcardno,
                entry_date,'N' as is_temp,phone_number
            FROM cust_emp_base base left join cust_emp_info inf on base.employee_id = inf.info_id
            UNION ALL
            SELECT
                employee_name,employee_no,org_id,id as employee_id,gender,birthday,
                (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
                null as position_id,null as year_work,null as bankcardname,null as emp_age,
                tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
                tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
                join_date as entry_date,'Y' as is_temp,phone_number
            FROM  hrms_employee_temporary et where et.is_sync_salary = 'Y'
        ) t2 ON nsp.employee_id=t2.employee_id AND t2.is_deleted='N'
        LEFT JOIN hrms_newsalary_option_payroll oppay on oppay.id = option_payroll_id and oppay.is_deleted = 'N'
        LEFT JOIN hrms_salary_policy_standard sps  ON sps.policy_standard_id = nsp.policy_standard_id and sps.`IS_DELETED`='N'
        LEFT JOIN hrms_newsalary_option_emp t1  ON t1.employee_id = nsp.employee_id and t1.option_id = nsp.option_id
        LEFT JOIN comm_organization t3 ON t2.org_id=t3.organization_id
        LEFT JOIN (
            SELECT
            A.*
            FROM
            COMM_DICT_ITEM A
            LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
            WHERE
            B.TYPE_CODE = 'establishment_type'
            AND B.IS_DELETED = 'N'
            AND A.IS_DELETED = 'N'
            AND A.IS_ENABLE = '1'
            AND A.sso_org_code = #{ssoOrgCode}
        ) t4 ON t2.establishment_type = t4.item_code
        LEFT JOIN  (
            SELECT
            A.*
            FROM
            COMM_DICT_ITEM A
            LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
            WHERE
            B.TYPE_CODE = 'employee_status'
            AND B.IS_DELETED = 'N'
            AND A.IS_DELETED = 'N'
            AND A.IS_ENABLE = '1'
            AND A.sso_org_code = #{ssoOrgCode}
        ) t5 ON t2.employee_status = t5.item_code
        LEFT JOIN (
            SELECT
            A.*
            FROM
            COMM_DICT_ITEM A
            LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
            WHERE
            B.TYPE_CODE = 'personal_identity'
            AND B.IS_DELETED = 'N'
            AND A.IS_DELETED = 'N'
            AND A.IS_ENABLE = '1'
            AND A.sso_org_code = #{ssoOrgCode}
        ) t6 ON t2.personal_identity = t6.item_code
        LEFT JOIN hrms_newsalary_option opt on opt.id = nsp.option_id
        LEFT JOIN hrms_newsalary_payroll_detail npd ON nsp.id= npd.payroll_id and nsp.is_deleted=npd.is_deleted
        LEFT JOIN hrms_newsalary_payroll_detail_import imp ON t2.employee_no = imp.employee_no AND nsp.is_deleted= imp.is_deleted  AND  imp.import_date=#{computeDate}
        WHERE  nsp.payroll_date=#{computeDate}
        AND nsp.is_deleted='N'
        <if test="optionId != null and optionId !=''">
            AND  nsp.option_id=#{optionId}
        </if>
        <if test="employeeName != null and employeeName !=''">
            and ( t2.employee_name like CONCAT('%',#{employeeName},'%') or
            t2.employee_no like CONCAT('%',#{employeeName},'%') )
        </if>
        <if test="orgList != null and orgList.size() > 0">
            and (t2.org_id in
            <foreach collection="orgList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test='employeeStatus != null and employeeStatus.size() > 0'>
            and t2.employee_status in
            <foreach collection="employeeStatus" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="orgName != null and orgName !=''">
            and  t3.name like CONCAT('%',#{orgName},'%')
        </if>
        <if test="computeStatus != null and computeStatus !=''">
            and  oppay.compute_status = ${computeStatus}
        </if>
        <if test="reason != null and reason !=''">
            and  t1.reason like CONCAT('%',#{reason},'%')
        </if>
        <if test="sendStatus != null and (sendStatus == '0'.toString() or sendStatus == '1'.toString())">
            AND nsp.send_status = #{sendStatus}
	    </if>
	    <if test="sendStatus != null and sendStatus == '2'.toString()">
	        AND nsp.is_view = '1'
	    </if>
	    <if test="sendStatus != null and sendStatus == '3'.toString()">
	        AND nsp.is_view = '0'
	    </if>
	     <if test="sendStatus != null and sendStatus == '4'.toString()">
	        AND nsp.revocation_time is not null
	    </if>
        GROUP BY  t2.employee_id, t2.employee_no,t2.employee_name,t3.name,
        t2.personal_identity, t2.bankcardNo,t6.ITEM_NAME,
        t2.establishment_type,t2.employee_status,nsp.update_user_name,nsp.update_date,
        nsp.id,nsp.is_view,nsp.send_status,opt.option_name
         order by nsp.sort_num, COALESCE(CAST(t2.employee_no AS UNSIGNED),9999)

    </select>

    <select id="getLastSalary" resultType="cn.trasen.hrms.salary.DTO.LastSalaryOut">
        SELECT distinct t1.employee_id,t2.item_id,t2.salary
        FROM hrms_newsalary_option_emp tt
         LEFT JOIN hrms_newsalary_payroll t1 ON tt.employee_id =t1.employee_id and tt.is_Deleted = t1.is_Deleted
         LEFT JOIN hrms_newsalary_payroll_detail t2 ON t1.id = t2.payroll_id
         INNER JOIN hrms_newsalary_item t3 ON t2.item_id=t3.id
        WHERE t1.is_deleted='N'  AND t3.item_rule='1'  AND t1.option_id=#{optionId} AND t1.payroll_date = #{lastDate}
    </select>
    <select id="getNowSalary" resultType="cn.trasen.hrms.salary.DTO.LastSalaryOut">
        SELECT distinct t2.employee_id,t1.item_id,t1.salary
        FROM hrms_newsalary_payroll_detail_import t1
        INNER JOIN (
            SELECT
                employee_name,employee_no,org_id,employee_id,is_deleted
            FROM cust_emp_base
            UNION ALL
            SELECT
                employee_name,employee_no,org_id,id as employee_id,is_deleted
            FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
        ) t2 ON t1.employee_no = t2.employee_no AND t2.is_deleted='N'
            INNER JOIN hrms_newsalary_item t3 ON t1.item_id = t3.id
        WHERE t1.is_deleted='N' AND t3.item_rule='1' AND t1.import_date=#{computeDate}

    </select>


    <select id="getDataByComputeDate" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll">
        SELECT   t1.option_name,t1.id AS optionId,
        t2.compute_date,t2.compute_status,t2.pay_slip,  t2.head_count,  t2.set_count,  t2.update_count,
        t2.compute_time ,  t2.remark,t2.id
        FROM hrms_newsalary_option t1
        LEFT JOIN hrms_newsalary_option_payroll t2 ON t2.option_id = t1.id
        AND t2.compute_date=#{computeDate}
        WHERE t1.is_deleted='N'
        <!-- AND DATE_FORMAT(t1.create_date,'%Y-%m')  &lt;= #{computeDate} -->
      AND (
            (t1.is_enable='1')
                OR
            ( t1.id IN (SELECT option_id FROM hrms_newsalary_option_payroll WHERE is_deleted = 'N' AND compute_date = #{computeDate}) )
        )
        <if test="optionName != null and optionName != ''">
            and t1.option_name like concat('%',#{optionName},'%')
        </if>
        <if test="isCal != null and isCal != '1'">
            and t2.compute_status in ('1','2','3')
        </if>
        <if test="isCal != null and isCal != '2'">
            and t2.compute_status not in ('1','2','3')
        </if>
        <if test="isLock != null and isLock != '1'">
            and t2.compute_status = '3'
        </if>
        <if test="isLock != null and isLock != '2'">
            and t2.compute_status != '3'
        </if>
        <if test="paySlip != null and paySlip != ''">
            and t2.pay_slip = #{paySlip}
        </if>
    </select>

    <select id="makePayTableTitleCount" resultType="cn.trasen.hrms.salary.DTO.BasicItemVO">
        SELECT
            t2.basic_item_name,t2.compare,
            t1.basic_item_id
        FROM hrms_newsalary_basicitem_emp t1
                 LEFT JOIN hrms_newsalary_basic_column t2 ON t1.basic_item_id= t2.id
        WHERE t2.is_deleted = 'N' AND t2.basic_item_type IN (1,2)
        GROUP BY t1.basic_item_id,t2.basic_item_name,t2.compare,t2.number_sort
        ORDER BY t2.number_sort ASC

    </select>


    <select id="salaryCountData" resultType="java.util.Map">
        SELECT t2.employee_id,t2.employee_no,t2.employee_name,t3.name AS orgName,
        no.option_name as optionName,
        nsp.update_user_name,
        DATE_FORMAT(nsp.update_date, '%Y-%m-%d %H:%i:%s') as update_date,
        t2.personal_identity,
        t6.ITEM_NAME AS personal_identity_text,
        t2.establishment_type,
        t4.ITEM_NAME AS establishment_type_text,
        t5.ITEM_NAME AS employee_status_text,
        t2.employee_status,
        t2.entry_date,
        t2.positive_time,
        t2.identity_number,
        t2.bankcardname,
        t2.bankcardno,
        nsp.payroll_date
        <if test="salaryList != null and salaryList.size() > 0">
            ,
            <foreach collection="salaryList" index="index"
                     item="item" open="" separator="," close="">
                MAX(CASE  t7.item_code WHEN   #{item.itemCode} THEN npd.salary ELSE NULL END) AS '${item.itemCode}'
            </foreach>
        </if>

        FROM hrms_newsalary_payroll nsp
        LEFT JOIN hrms_newsalary_payroll_detail npd ON nsp.id= npd.payroll_id and nsp.is_deleted = npd.is_deleted
        left join hrms_newsalary_item t7 on npd.item_id = t7.id and nsp.option_id = t7.option_id and t7.is_enable = 1
        left join hrms_newsalary_option no on nsp.option_id = no.id
        INNER JOIN (
            SELECT
                employee_name,employee_no,org_id,employee_id,gender,birthday,
                employee_status,positive_time,retirement_time,
                position_id,year_work,bankcardname,emp_age,
                establishment_type,salary_appoint,is_deleted,
                personal_identity,identity_number,bankcardno,
                entry_date,'N' as is_temp
            FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
            UNION ALL
            SELECT
                employee_name,employee_no,org_id,id as employee_id,gender,birthday,
                (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
                null as position_id,null as year_work,null as bankcardname,null as emp_age,
                tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
                tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
                join_date as entry_date,'Y' as is_temp
            FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
        ) t2 ON nsp.employee_id=t2.employee_id AND t2.is_deleted='N'
        LEFT JOIN comm_organization t3 ON t2.org_id=t3.organization_id
        LEFT JOIN (
            SELECT
            A.*
            FROM
            COMM_DICT_ITEM A
            LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
            WHERE
            B.TYPE_CODE = 'establishment_type'
            AND B.IS_DELETED = 'N'
            AND A.IS_DELETED = 'N'
            AND A.IS_ENABLE = '1'
            AND A.sso_org_code = #{ssoOrgCode}
        ) t4 ON t2.establishment_type = t4.item_code
        LEFT JOIN (
            SELECT
            A.*
            FROM
            COMM_DICT_ITEM A
            LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
            WHERE
            B.TYPE_CODE = 'employee_status'
            AND B.IS_DELETED = 'N'
            AND A.IS_DELETED = 'N'
            AND A.IS_ENABLE = '1'
            AND A.sso_org_code = #{ssoOrgCode}
        ) t5 ON t2.employee_status = t5.item_code
        LEFT JOIN (
            SELECT
            A.*
            FROM
            COMM_DICT_ITEM A
            LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
            WHERE
            B.TYPE_CODE = 'personal_identity'
            AND B.IS_DELETED = 'N'
            AND A.IS_DELETED = 'N'
            AND A.IS_ENABLE = '1'
            AND A.sso_org_code = #{ssoOrgCode}
        ) t6 ON t2.personal_identity = t6.item_code
        left join (
        SELECT info.jobtitle_name, info.employee_id,info.jobtitle_category
        FROM hrms_jobtitle_info info
        inner join comm_jobtitle_basic b on info.jobtitle_category = b.jobtitle_basic_id
        WHERE info.is_deleted = 'N' and info.highest_level=1 GROUP BY info.employee_id,info.jobtitle_name,info.jobtitle_category
        )a3 on a3.employee_id = t2.employee_id
        left join (
        SELECT ed.employee_id,ed.education_type
        FROM hrms_education_info ed
        LEFT JOIN (
            SELECT
            A.*
            FROM
            COMM_DICT_ITEM A
            LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
            WHERE
            B.TYPE_CODE = 'education_type'
            AND B.IS_DELETED = 'N'
            AND A.IS_DELETED = 'N'
            AND A.IS_ENABLE = '1'
            AND A.sso_org_code = #{ssoOrgCode}
        ) dict on ed.education_type = dict.item_code
        WHERE ed.is_deleted = 'N' and ed.highest_level=1 GROUP BY ed.employee_id,ed.education_type
        )a4 on a4.employee_id = t2.employee_id

        WHERE   nsp.is_deleted='N'
        <if test="scsr.startDate != null and scsr.startDate != '' and scsr.endDate != null and scsr.endDate != ''">
            <![CDATA[ and  nsp.payroll_date >= #{scsr.startDate} AND  nsp.payroll_date <= #{scsr.endDate} ]]>
        </if>
        <if test="scsr.optionName != null and scsr.optionName !=''">
            and  no.option_name like CONCAT('%',#{scsr.optionName},'%')
        </if>
        <if test="scsr.optionId != null and scsr.optionId != ''">
           and  nsp.option_id = #{scsr.optionId}
        </if>
        <if test="scsr.employeeName != null and scsr.employeeName !=''">
            and ( t2.employee_name like CONCAT('%',#{scsr.employeeName},'%') or
            t2.employee_no like CONCAT('%',#{scsr.employeeName},'%') )
        </if>
        <if test="scsr.orgName != null and scsr.orgName !=''">
            and  t3.name like CONCAT('%',#{scsr.orgName},'%')
        </if>

         <!-- 复杂查询条件 -->
        <if test="scsr.birthdayStartTime!=null and scsr.birthdayStartTime!=''">
            and t2.birthday >=  #{scsr.birthdayStartTime}
        </if>

        <if test="scsr.birthdayEndTime!=null and scsr.birthdayEndTime!=''">
            and #{scsr.birthdayEndTime} >=t2.birthday

        </if>

        <if test="scsr.positiveTimeStartTime!=null and scsr.positiveTimeStartTime!=''">
            and t2.positive_time >=  #{scsr.positiveTimeStartTime}
        </if>

        <if test="scsr.positiveTimeEndTime!=null and scsr.positiveTimeEndTime!=''">
            and #{scsr.positiveTimeEndTime} >=t2.positive_time
        </if>

        <if test="scsr.entryDateStartTime!=null and scsr.entryDateStartTime!=''">
            and t2.entry_date >=  #{scsr.entryDateEndTime}
        </if>

        <if test="scsr.entryDateEndTime!=null and scsr.entryDateEndTime!=''">
            and #{scsr.entryDateEndTime} >=t2.entry_date
        </if>


        <if test="scsr.identityNumber!=null and scsr.identityNumber!=''">
            and t2.identity_number like concat('',#{scsr.identityNumber},'%')
        </if>

        <if test="scsr.phoneNumber!=null and scsr.phoneNumber!=''">
            and t2.phone_number like concat('',#{scsr.phoneNumber},'%')
        </if>

        <if test="scsr.empPayroll!=null and scsr.empPayroll!=''">
            and t2.emp_payroll like concat('',#{scsr.empPayroll},'%')
        </if>

        <if test="scsr.employeeStatuses != null and scsr.employeeStatuses.size() > 0">
            and (t2.employee_status in
            <foreach collection="scsr.employeeStatuses" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>


        <if test="scsr.establishmentTypes != null and scsr.establishmentTypes.size() > 0">
            and (t2.establishment_type in
            <foreach collection="scsr.establishmentTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test="scsr.orgAttributestypes != null and scsr.orgAttributestypes.size() > 0">
            and (t2.org_attributes in
            <foreach collection="scsr.orgAttributestypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test="scsr.employeeCategorys != null and scsr.employeeCategorys.size() > 0">
            and (t2.employee_category in
            <foreach collection="scsr.employeeCategorys" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test="scsr.politicalStatuses != null and scsr.politicalStatuses.size() > 0">
            and (t2.political_status in
            <foreach collection="scsr.politicalStatuses" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test="scsr.nationalityes != null and scsr.nationalityes.size() > 0">
            and (t2.nationality in
            <foreach collection="scsr.nationalityes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>


        <if test="scsr.marriageStatuses != null and scsr.marriageStatuses.size() > 0">
            and (t2.marriage_status in
            <foreach collection="scsr.marriageStatuses" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>


        <if test="scsr.positionNames != null and scsr.positionNames.size() > 0">
            and (t2.position_id in
            <foreach collection="scsr.positionNames" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test="scsr.personalIdentitys != null and scsr.personalIdentitys.size() > 0">
            and (t2.personal_identity in
            <foreach collection="scsr.personalIdentitys" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test="scsr.plgws != null and scsr.plgws.size() > 0">
            and (t2.plgw in
            <foreach collection="scsr.plgws" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test="scsr.operationTypes != null and scsr.operationTypes.size() > 0">
            and (t2.operation_type in
            <foreach collection="scsr.operationTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>



        <if test="scsr.gwdjs != null and scsr.gwdjs.size() > 0">
            and (t2.gwdj in
            <foreach collection="scsr.gwdjs" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test="scsr.jobtitleCategory != null and scsr.jobtitleCategory.size() > 0">
            and (a3.jobtitle_category in
            <foreach collection="scsr.jobtitleCategory" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test="scsr.jobtitleName != null and scsr.jobtitleName.size() > 0">
            and (a3.jobtitle_name in
            <foreach collection="scsr.jobtitleName" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test="scsr.educationTypes != null and scsr.educationTypes.size() > 0">
            and (a4.education_type in
            <foreach collection="scsr.educationTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>


        <if test="scsr.startAge!=null and scsr.startAge!=''">
            and t2.emp_age >=  #{scsr.startAge}
        </if>

        <if test="scsr.endAge!=null and scsr.endAge !=''">
            and #{scsr.endAge} >=t2.emp_age
        </if>
        <choose>
        	<when test="_databaseId=='postgresql' or _databaseId=='kingbase'">
				GROUP BY  t2.employee_id,t2.employee_no,t2.employee_name,t3.name,t4.ITEM_NAME, t5.ITEM_NAME,
				        no.option_name,nsp.update_user_name,nsp.update_date,t2.personal_identity,
		        t6.ITEM_NAME,t2.establishment_type,t2.employee_status,t2.entry_date,t2.positive_time,
				        t2.identity_number,t2.bankcardname,t2.bankcardno,nsp.payroll_date
        	</when>
        	<otherwise>
        		Group by t2.employee_id
        	</otherwise>
        </choose>
        order by nsp.sort_num
    </select>
    <select id="checkPersonnelCount" parameterType="cn.trasen.hrms.salary.DTO.SearchListTable" resultType="java.util.Map">
        SELECT count(*) as qbsx,
        SUM(case when t2.employee_status = '1' and t2.salary_appoint is null then 1 else 0 end) as zzwdx,
		SUM(case when t2.employee_status in('4','8') then 1 else 0 end) as lxt
        FROM hrms_newsalary_option_emp t1
        LEFT JOIN (SELECT
                        employee_name,employee_no,org_id,employee_id,gender,birthday,
                        employee_status,establishment_type,salary_appoint,is_deleted,
                        personal_identity,'N' as is_temp
                    FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
                    UNION ALL
                    SELECT
                        employee_name,employee_no,org_id,id as employee_id,gender,birthday,
                        (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,
                        tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
                        tmp_position as personal_identity,'Y' as is_temp
                    FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
        ) t2 ON t1.employee_id = t2.employee_id
        WHERE t1.is_deleted='N' AND t2.is_deleted='N'
        AND t1.option_id= #{optionId}
    </select>
    <select id="getHistoryDataCount" parameterType="cn.trasen.hrms.salary.DTO.SearchListTable" resultType="java.util.Map">
        SELECT
         IFNULL(SUM(case when ydqk = '1' then 1 else 0 end),0) as xrz,
         IFNULL(SUM(case when ydqk='2' then 1 else 0 end),0) as ltx,
         count(*) as ryzs
        FROM hrms_newsalary_firststep_history
        WHERE is_deleted = 'N' and payroll_date = #{payrollDate}
        and option_id = #{optionId}
    </select>
    <select id="getMonthEntryFix" parameterType="cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll" resultType="java.lang.String">
        SELECT DISTINCT e.employee_id FROM hrms_newsalary_option_emp e
        left join hrms_newsalary_option n on e.option_id = n.id and n.IS_DELETED = 'N'
        left join hrms_newsalary_basicitem_emp_history m on e.employee_id = m.employee_id
        WHERE e.IS_DELETED = 'N' and m.IS_DELETED = 'N' and DATE_FORMAT(m.effective_date,'%Y-%m') = #{computeDate}
        AND (m.reason = '' or m.reason = '入职定薪')
        and e.option_id = #{optionId}
    </select>
    <select id="getChangeFix" parameterType="cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll" resultType="java.lang.String">
        SELECT DISTINCT e.employee_id FROM hrms_newsalary_option_emp e
        left join hrms_newsalary_option n on e.option_id = n.id and n.IS_DELETED = 'N'
        left join hrms_newsalary_basicitem_emp_history m on e.employee_id = m.employee_id
        WHERE e.IS_DELETED = 'N' and m.IS_DELETED = 'N' and DATE_FORMAT(m.effective_date,'%Y-%m') = #{computeDate}
        AND m.reason != '' AND m.reason != '入职定薪'
        and e.option_id = #{optionId}
    </select>
    <select id="getMonthEntryUnFix" parameterType="cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll" resultType="java.lang.String">
        SELECT DISTINCT e.employee_id FROM hrms_newsalary_option_emp e
         left join hrms_newsalary_option n on e.option_id = n.id and n.IS_DELETED = 'N'
         left join cust_emp_base h on e.employee_id = h.employee_id
        WHERE e.IS_DELETED = 'N' and h.IS_DELETED = 'N' and e.option_id = #{optionId}
        and not EXISTS
        (
        select 1 from hrms_newsalary_basicitem_emp_history m where m.employee_id = h.employee_id and m.IS_DELETED = 'N'
        )
    </select>
    <select id="getDtx" parameterType="cn.trasen.hrms.salary.DTO.SearchListTable" resultType="java.lang.Integer">
        select count(distinct employee_id) from hrms_newsalary_basicitem_emp_history
        where DATE_FORMAT(effective_date,'%Y-%m') = #{payrollDate}
        and employee_id in
        <foreach collection="empList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getCheperList" resultType="cn.trasen.hrms.salary.DTO.CheckPersonnel"
            parameterType="cn.trasen.hrms.salary.DTO.SearchListTable">
        SELECT t1.employee_no,t1.employee_name,t2.employee_id,
        t1.establishment_type,
        t1.personal_identity,
        t1.employee_status,
        t1.entry_date,
        t1.positive_time,
        t1.retirement_time,
        t3.name as orgName,
        t4.option_name as optionName,
        t1.is_temp
        FROM (
            SELECT
            employee_name,employee_no,org_id,employee_id,gender,birthday,
            employee_status,positive_time,retirement_time,
            position_id,year_work,bankcardname,emp_age,
            establishment_type,salary_appoint,is_deleted,
            personal_identity,identity_number,bankcardno,
            entry_date,'N' as is_temp
            FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
            UNION ALL
            SELECT
            employee_name,employee_no,org_id,id as employee_id,gender,birthday,
            (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
            null as position_id,null as year_work,null as bankcardname,null as emp_age,
            tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
            tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
            join_date as entry_date,'Y' as is_temp
            FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
        ) t1
        LEFT JOIN hrms_newsalary_option_emp t2 ON t1.employee_id = t2.employee_id and t2.is_deleted='N'
        LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
        LEFT JOIN hrms_newsalary_option t4 on t2.option_id = t4.id and t4.is_deleted = 'N'
        WHERE  t1.is_deleted='N' and t1.employee_status = '1' AND t3.is_deleted = 'N'
        and not exists
        (select 1 from hrms_newsalary_option_emp t5 where t1.employee_id = t5.employee_id
        and t5.option_id = #{optionId} and t5.is_deleted='N')
        <if test="employeeName != null and employeeName !=''">
            and ( t1.employee_name like CONCAT('%',#{employeeName},'%') or
            t1.employee_no like CONCAT('%',#{employeeName},'%') )
        </if>
        <if test='employeeStatus != null and employeeStatus.size() > 0'>
            and t1.employee_status in
            <foreach collection="employeeStatus" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orgList != null and orgList.size() > 0">
            and (t1.org_id in
            <foreach collection="orgList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        and (t2.employee_id is not null or t2.employee_id = '')
    </select>
    <select id="salaryTotalCountData" resultType="java.util.Map">
        SELECT DISTINCT* FROM (
        <if test="optionList != null and optionList.size() > 0">
            <foreach collection="optionList" index="optionSize"
                     item="optionId" open="" separator="" close="">
                SELECT t2.employee_id,t2.employee_no,t2.employee_name,t3.name AS orgName,
                no.option_name as optionName,
                nsp.option_id,
                nsp.update_user_name,
                DATE_FORMAT(nsp.update_date, '%Y-%m-%d %H:%i:%s') as update_date,
                t2.personal_identity,
                t6.ITEM_NAME AS personal_identity_text,
                t2.establishment_type,
                t4.ITEM_NAME AS establishment_type_text,
                t5.ITEM_NAME AS employee_status_text,
                t2.employee_status,
                t2.entry_date,
                t2.positive_time,
                t2.identity_number,
                t2.bankcardname,
                t2.bankcardno,
                t7.col_code as colCode,
                t7.col_Name as colName,
                t8.sort_no as sortNo,
                nsp.payroll_date
                <if test="itemIdList != null and itemIdList.size() > 0">
                    ,
                    <foreach collection="itemIdList" index="index"
                             item="item" open="" separator="," close="">
                        SUM(CASE  WHEN npd.item_id = #{item} and npd.option_id = t7.option_id and (npd.item_id = t7.item_id or t7.item_id is null) THEN IF(npd.salary is null or npd.salary ='',0.00,npd.salary) ELSE 0.00 END) AS 'temp_${item}'
                    </foreach>
                </if>
                <if test="totalList != null and totalList.size() > 0">
                    ,
                    <foreach collection="totalList" index="index"
                             item="item" open="" separator="," close="">
                        MAX(CASE t7.col_code WHEN #{item.colCode} THEN IF(npd.salary is null or npd.salary ='',0.00,npd.salary) ELSE NULL END) AS '${item.colCode}'
                    </foreach>
                </if>
                FROM hrms_newsalary_payroll nsp
                LEFT JOIN hrms_newsalary_payroll_detail npd ON nsp.id= npd.payroll_id and nsp.is_deleted = npd.is_deleted
                left join hrms_newsalary_report_map t7 on (npd.item_id = t7.item_id or t7.count_formula like CONCAT('%{',npd.item_id,'[%')) and nsp.option_id = t7.option_id and t7.is_deleted = 'N'
                left join hrms_newsalary_report_total t8 on t7.col_id = t8.id and t8.is_deleted = 'N'
                <if test="scsr.reportId != null and scsr.reportId != ''">
                    and t8.report_id = #{scsr.reportId}
                </if>
                left join hrms_newsalary_option no on nsp.option_id = no.id
                INNER JOIN (
                    SELECT
                        employee_name,employee_no,org_id,employee_id,gender,birthday,
                        employee_status,positive_time,retirement_time,
                        position_id,year_work,bankcardname,emp_age,
                        establishment_type,salary_appoint,is_deleted,
                        personal_identity,identity_number,bankcardno,
                        entry_date,'N' as is_temp
                    FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
                    UNION ALL
                    SELECT
                        employee_name,employee_no,org_id,id as employee_id,gender,birthday,
                        (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
                        null as position_id,null as year_work,null as bankcardname,null as emp_age,
                        tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
                        tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
                        join_date as entry_date,'Y' as is_temp
                    FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
                ) t2 ON nsp.employee_id=t2.employee_id AND t2.is_deleted='N'
                LEFT JOIN comm_organization t3 ON t2.org_id=t3.organization_id
                LEFT JOIN (
                    SELECT
                    A.*
                    FROM
                    COMM_DICT_ITEM A
                    LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                    WHERE
                     ((B.TYPE_CODE='establishment_type' and t2.is_temp='N') or (B.TYPE_CODE='tmp_establishment_type' and t2.is_temp='Y'))
                    AND B.IS_DELETED = 'N'
                    AND A.IS_DELETED = 'N'
                    AND A.IS_ENABLE = '1'
                    AND A.sso_org_code = #{ssoOrgCode}
                ) t4 ON t2.establishment_type = t4.item_code
                LEFT JOIN (
                    SELECT
                    A.*
                    FROM
                    COMM_DICT_ITEM A
                    LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                    WHERE
                    B.TYPE_CODE = 'employee_status'
                    AND B.IS_DELETED = 'N'
                    AND A.IS_DELETED = 'N'
                    AND A.IS_ENABLE = '1'
                    AND A.sso_org_code = #{ssoOrgCode}
                ) t5 ON t2.employee_status = t5.item_code
                LEFT JOIN (
                    SELECT
                    A.*
                    FROM
                    COMM_DICT_ITEM A
                    LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                    WHERE
                    ((B.TYPE_CODE ='personal_identity' and t2.is_temp='N') or (B.TYPE_CODE ='tmp_personal_identity' and t2.is_temp='Y'))
                    AND B.IS_DELETED = 'N'
                    AND A.IS_DELETED = 'N'
                    AND A.IS_ENABLE = '1'
                    AND A.sso_org_code = #{ssoOrgCode}
                ) t6 ON t2.personal_identity = t6.item_code
                left join (
                SELECT info.jobtitle_name, info.employee_id,info.jobtitle_category
                FROM hrms_jobtitle_info info
                inner join comm_jobtitle_basic b on info.jobtitle_category = b.jobtitle_basic_id
                WHERE info.is_deleted = 'N' and info.highest_level=1 GROUP BY info.employee_id,info.jobtitle_name,info.jobtitle_category
                )a3 on a3.employee_id = t2.employee_id
                left join (
                SELECT ed.employee_id,ed.education_type
                FROM hrms_education_info ed
                LEFT JOIN (
                    SELECT
                    A.*
                    FROM
                    COMM_DICT_ITEM A
                    LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                    WHERE
                    B.TYPE_CODE = 'education_type'
                    AND B.IS_DELETED = 'N'
                    AND A.IS_DELETED = 'N'
                    AND A.IS_ENABLE = '1'
                    AND A.sso_org_code = #{ssoOrgCode}
                ) dict on ed.education_type = dict.item_code
                WHERE ed.is_deleted = 'N' and ed.highest_level=1 GROUP BY ed.employee_id,ed.education_type
                )a4 on a4.employee_id = t2.employee_id
                WHERE  nsp.is_deleted='N'
                <if test="scsr.startDate != null and scsr.startDate != '' and scsr.endDate != null and scsr.endDate != ''">
                    <![CDATA[ and  nsp.payroll_date >= #{scsr.startDate} AND  nsp.payroll_date <= #{scsr.endDate} ]]>
                </if>
                and  nsp.option_id = #{optionId}
                <if test="scsr.optionName != null and scsr.optionName !=''">
                    and  no.option_name like CONCAT('%',#{scsr.optionName},'%')
                </if>
                <if test="scsr.employeeName != null and scsr.employeeName !=''">
                    and ( t2.employee_name like CONCAT('%',#{scsr.employeeName},'%') or
                    t2.employee_no like CONCAT('%',#{scsr.employeeName},'%') )
                </if>
                <if test="scsr.orgName != null and scsr.orgName !=''">
                    and t3.name like CONCAT('%',#{scsr.orgName},'%')
                </if>

                <!-- 复杂查询条件 -->
                <if test="scsr.birthdayStartTime!=null and scsr.birthdayStartTime!=''">
                    and t2.birthday >= #{scsr.birthdayStartTime}
                </if>

                <if test="scsr.birthdayEndTime!=null and scsr.birthdayEndTime!=''">
                    and #{scsr.birthdayEndTime} >=t2.birthday

                </if>

                <if test="scsr.positiveTimeStartTime!=null and scsr.positiveTimeStartTime!=''">
                    and t2.positive_time >= #{scsr.positiveTimeStartTime}
                </if>

                <if test="scsr.positiveTimeEndTime!=null and scsr.positiveTimeEndTime!=''">
                    and #{scsr.positiveTimeEndTime} >=t2.positive_time
                </if>

                <if test="scsr.entryDateStartTime!=null and scsr.entryDateStartTime!=''">
                    and t2.entry_date >= #{scsr.entryDateEndTime}
                </if>

                <if test="scsr.entryDateEndTime!=null and scsr.entryDateEndTime!=''">
                    and #{scsr.entryDateEndTime} >=t2.entry_date
                </if>


                <if test="scsr.identityNumber!=null and scsr.identityNumber!=''">
                    and t2.identity_number like concat('',#{scsr.identityNumber},'%')
                </if>

                <if test="scsr.phoneNumber!=null and scsr.phoneNumber!=''">
                    and t2.phone_number like concat('',#{scsr.phoneNumber},'%')
                </if>

                <if test="scsr.empPayroll!=null and scsr.empPayroll!=''">
                    and t2.emp_payroll like concat('',#{scsr.empPayroll},'%')
                </if>

                <if test="scsr.employeeStatuses != null and scsr.employeeStatuses.size() > 0">
                    and (t2.employee_status in
                    <foreach collection="scsr.employeeStatuses" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                    )
                </if>


                <if test="scsr.establishmentTypes != null and scsr.establishmentTypes.size() > 0">
                    and (t2.establishment_type in
                    <foreach collection="scsr.establishmentTypes" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                    )
                </if>

                <if test="scsr.orgAttributestypes != null and scsr.orgAttributestypes.size() > 0">
                    and (t2.org_attributes in
                    <foreach collection="scsr.orgAttributestypes" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                    )
                </if>

                <if test="scsr.employeeCategorys != null and scsr.employeeCategorys.size() > 0">
                    and (t2.employee_category in
                    <foreach collection="scsr.employeeCategorys" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                    )
                </if>

                <if test="scsr.politicalStatuses != null and scsr.politicalStatuses.size() > 0">
                    and (t2.political_status in
                    <foreach collection="scsr.politicalStatuses" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                    )
                </if>

                <if test="scsr.nationalityes != null and scsr.nationalityes.size() > 0">
                    and (t2.nationality in
                    <foreach collection="scsr.nationalityes" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>


                <if test="scsr.marriageStatuses != null and scsr.marriageStatuses.size() > 0">
                    and (t2.marriage_status in
                    <foreach collection="scsr.marriageStatuses" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                    )
                </if>


                <if test="scsr.positionNames != null and scsr.positionNames.size() > 0">
                    and (t2.position_id in
                    <foreach collection="scsr.positionNames" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>

                <if test="scsr.personalIdentitys != null and scsr.personalIdentitys.size() > 0">
                    and (t2.personal_identity in
                    <foreach collection="scsr.personalIdentitys" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                    )
                </if>

                <if test="scsr.plgws != null and scsr.plgws.size() > 0">
                    and (t2.plgw in
                    <foreach collection="scsr.plgws" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>

                <if test="scsr.operationTypes != null and scsr.operationTypes.size() > 0">
                    and (t2.operation_type in
                    <foreach collection="scsr.operationTypes" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                    )
                </if>


                <if test="scsr.gwdjs != null and scsr.gwdjs.size() > 0">
                    and (t2.gwdj in
                    <foreach collection="scsr.gwdjs" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>

                <if test="scsr.jobtitleCategory != null and scsr.jobtitleCategory.size() > 0">
                    and (a3.jobtitle_category in
                    <foreach collection="scsr.jobtitleCategory" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                    )
                </if>

                <if test="scsr.jobtitleName != null and scsr.jobtitleName.size() > 0">
                    and (a3.jobtitle_name in
                    <foreach collection="scsr.jobtitleName" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>

                <if test="scsr.educationTypes != null and scsr.educationTypes.size() > 0">
                    and (a4.education_type in
                    <foreach collection="scsr.educationTypes" index="index" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                    )
                </if>


                <if test="scsr.startAge!=null and scsr.startAge!=''">
                    and t2.emp_age >= #{scsr.startAge}
                </if>

                <if test="scsr.endAge!=null and scsr.endAge !=''">
                    and #{scsr.endAge} >=t2.emp_age
                </if>
                <choose>
                	<when test="_databaseId=='kingbase' or _databaseId=='postgresql'">
                	GROUP BY  t2.employee_id,t2.employee_no,t2.employee_name,t3.name,t4.ITEM_NAME, t5.ITEM_NAME,
				        no.option_name,nsp.update_user_name,nsp.update_date,t2.personal_identity,
		        		t6.ITEM_NAME,t2.establishment_type,t2.employee_status,t2.entry_date,t2.positive_time,
				        t2.identity_number,t2.bankcardname,t2.bankcardno,nsp.payroll_date,t7.col_code, t7.col_Name,t8.sort_no
                	</when>
                	<otherwise>
		                GROUP BY t2.employee_id,nsp.payroll_date
                	</otherwise>
                </choose>
                <if test="optionSize != optionList.size() - 1">
                    union all
                </if>
            </foreach>
            ) t order by t.payroll_date desc ,t.sortNo asc
        </if>
    </select>

    <!-- 判断员工指定算薪周期是否已锁定-->
    <select id="getIsLockByEmpComputeDate" resultType="Integer">
          select case when t4.is_enable = '2' then -2 when t4.is_enable is null then -1
             else case when t3.compute_status in ('0','1','2') or t3.compute_status is null then 0 when t3.compute_status = 3 then 1 else -1 end end isLock
            FROM (
                SELECT employee_id,org_id,is_deleted  FROM cust_emp_base base
                UNION ALL
                SELECT id as employee_id,org_id,is_deleted FROM hrms_employee_temporary where is_sync_salary = 'Y'
            ) t1
            LEFT JOIN hrms_newsalary_option_emp t2 on t1.employee_id = t2.employee_id and t2.is_deleted = 'N'
            LEFT JOIN hrms_newsalary_option_payroll t3 ON t3.option_id = t2.option_id and t3.compute_date=#{computeDate}
            LEFT JOIN hrms_newsalary_option t4 on t4.id = t2.option_id and t4.is_deleted = 'N'
             where t1.is_deleted='N' and t1.employee_id = #{employeeId}
             group by t1.employee_id
    </select>

    <!-- 查询员工薪酬薪资异动配置 -->
    <select id="selectEmployeeSalaryChangeItemSettingDatas" parameterType="String" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItemRemindSetting">
        select p3.remind_id,p3.item_id,p3.is_back_payment,p3.back_payment_date,p.employee_id,p1.salary,p4.count_type
         from hrms_newsalary_payroll p
         inner join hrms_newsalary_payroll_detail p1 on p.id = p1.payroll_id and p1.is_deleted = 'N'
         inner join hrms_newsalary_option_payroll p2 on p.option_payroll_id = p2.id and p2.is_deleted = 'N' and p2.compute_status = 3
         inner join hrms_newsalary_item_remind_setting p3 on p1.item_id = p3.item_id and p3.is_enabled = '1' and p3.is_deleted = 'N'
         inner join hrms_newsalary_item_library p4 on p3.item_id = p4.id and p4.is_deleted = 'N'
         left join hrms_newsalary_item_remind_record p5 on p5.item_id = p3.item_id and p5.employee_id = p.employee_id and p5.handle_status = '1' and p5.compute_date = #{computeDate}
            where p.is_deleted = 'N' and p.employee_id = #{employeeId}
             and p.payroll_date = #{lastComputeDate}
             and p5.id is null
    </select>

    <!-- 薪酬方案汇总报表数据 -->
    <select id="salaryOptionTotalCountData" resultType="java.util.Map">
        SELECT
            nsp.payroll_date,
            nsp.option_id,
            no.option_name,
            no.establishment_types,
            count(distinct nsp.employee_id) as head_count,
            nsp.update_user_name,
            DATE_FORMAT(nsp.update_date, '%Y-%m-%d %H:%i:%s') as update_date,
            t7.col_code as colCode,
            t7.col_Name as colName,
            t8.sort_no as sortNo
            <if test="itemIdList != null and itemIdList.size() > 0">
                ,
                <foreach collection="itemIdList" index="index"
                         item="item" open="" separator="," close="">
                    SUM(CASE  WHEN npd.item_id = #{item} and npd.option_id = t7.option_id and (npd.item_id = t7.item_id or t7.item_id is null) THEN IF(npd.salary is null or npd.salary ='',0.00,npd.salary) ELSE 0.00 END) AS 'temp_${item}'
                </foreach>
            </if>
            <if test="totalList != null and totalList.size() > 0">
                ,
                <foreach collection="totalList" index="index"
                         item="item" open="" separator="," close="">
                    SUM(CASE WHEN t7.col_code = #{item.colCode} and npd.option_id = t7.option_id and npd.item_id = t7.item_id THEN IF(npd.salary is null or npd.salary ='',0.00,npd.salary) ELSE 0.00 END) AS '${item.colCode}'
                </foreach>
            </if>
        FROM hrms_newsalary_option no
        left join hrms_newsalary_payroll nsp on nsp.option_id = no.id and nsp.is_deleted='N'
        LEFT JOIN hrms_newsalary_payroll_detail npd ON nsp.id= npd.payroll_id and nsp.is_deleted =
        npd.is_deleted
        left join hrms_newsalary_report_map t7 on (npd.item_id = t7.item_id or t7.count_formula like CONCAT('%{',npd.item_id,'[%')) and nsp.option_id = t7.option_id and t7.is_deleted = 'N'
        left join hrms_newsalary_report_total t8 on t7.col_id = t8.id and t8.is_deleted = 'N'
        <if test="scsr.reportId != null and scsr.reportId != ''">
            and t8.report_id = #{scsr.reportId}
        </if>
        WHERE  no.is_deleted='N'
        <if test="scsr.payrollDate != null and scsr.payrollDate != ''">
            and nsp.payroll_date = #{scsr.payrollDate}
        </if>
        <choose>
            <when test="_databaseId=='kingbase' or _databaseId=='postgresql'">
                GROUP BY no.id,nsp.payroll_date, no.option_name, no.establishment_types,nsp.update_user_name,
                nsp.update_date, t7.col_code, t7.col_name, t8.sort_no as sortNo
            </when>
            <otherwise>
                GROUP BY no.id
            </otherwise>
        </choose>
          order by no.id asc,t8.sort_no asc
    </select>
</mapper>
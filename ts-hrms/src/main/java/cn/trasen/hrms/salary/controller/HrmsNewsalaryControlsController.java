package cn.trasen.hrms.salary.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryControls;
import cn.trasen.hrms.salary.service.HrmsNewsalaryControlsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalaryControlsController
 * @Description 薪酬控制表
 * @date 2023��11��11�� ����4:33:46
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "薪酬控制Controller")
public class HrmsNewsalaryControlsController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryControlsController.class);

	@Autowired
	private HrmsNewsalaryControlsService hrmsNewsalaryControlsService;

	/**
	 * @Title saveHrmsNewsalaryControls
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:33:46
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/salaryControls/save")
	public PlatformResult<String> saveHrmsNewsalaryControls(@RequestBody HrmsNewsalaryControls record) {
		try {
			hrmsNewsalaryControlsService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryControls
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:33:46
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryControls/update")
	public PlatformResult<String> updateHrmsNewsalaryControls(@RequestBody HrmsNewsalaryControls record) {
		try {
			hrmsNewsalaryControlsService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryControlsById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryControls>
	 * @date 2023��11��11�� ����4:33:46
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/salaryControls/{id}")
	public PlatformResult<HrmsNewsalaryControls> selectHrmsNewsalaryControlsById(@PathVariable String id) {
		try {
			HrmsNewsalaryControls record = hrmsNewsalaryControlsService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryControlsById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:33:46
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/salaryControls/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryControlsById(@PathVariable String id) {
		try {
			hrmsNewsalaryControlsService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryControlsList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryControls>
	 * @date 2023��11��11�� ����4:33:46
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/salaryControls/list")
	public DataSet<HrmsNewsalaryControls> selectHrmsNewsalaryControlsList(Page page, HrmsNewsalaryControls record) {
		return hrmsNewsalaryControlsService.getDataSetList(page, record);
	}
}

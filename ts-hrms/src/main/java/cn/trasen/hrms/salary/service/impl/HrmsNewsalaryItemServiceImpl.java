package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.hrms.salary.DTO.SalaryCountSearchReq;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryItemBasicMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryItemMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryPayrollMapper;
import cn.trasen.hrms.salary.model.*;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemGroupService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemService;
import cn.trasen.hrms.salary.service.IHrmsNewSalaryItemLibraryService;
import cn.trasen.hrms.salary.service.IHrmsNewsalaryPayslipService;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName HrmsNewsalaryItemServiceImpl
 * @Description TODO
 * @date 2023��11��11�� ����4:34:26
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryItemServiceImpl implements HrmsNewsalaryItemService {

	@Resource
	private HrmsNewsalaryItemMapper mapper;

	@Resource
	private HrmsNewsalaryItemBasicMapper basicMapper;

	@Autowired
	private DictItemFeignService dictItemFeignService;

	@Autowired
	private GlobalSettingsFeignService globalSettingsFeignService;

	@Autowired
	private IHrmsNewSalaryItemLibraryService hrmsNewSalaryItemLibraryService;

	@Autowired
	private HrmsNewsalaryItemGroupService hrmsNewsalaryItemGroupService;

	@Autowired
	private IHrmsNewsalaryPayslipService hrmsNewsalaryPayslipService;

	@Resource
	private HrmsNewsalaryPayrollMapper newsalaryPayrollMapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryItem record) {

		Example example = new Example(HrmsNewsalaryItem.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("optionId", record.getOptionId());
		criteria.andEqualTo("itemName", record.getItemName().trim());
		List<HrmsNewsalaryItem> hrmsNewsalaryItems = mapper.selectByExample(example);
		if (hrmsNewsalaryItems != null && hrmsNewsalaryItems.size() > 0) {
			throw new RuntimeException("项目已存在");
		}
		String itemCode = IdUtil.getId();
		Example example2 = new Example(HrmsNewsalaryItem.class);
		Example.Criteria criteria2 = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("itemName", record.getItemName().trim());
		List<HrmsNewsalaryItem> hrmsNewsalaryItems2 = mapper.selectByExample(example);
		if (hrmsNewsalaryItems2 != null && hrmsNewsalaryItems2.size() > 0) {
			itemCode = hrmsNewsalaryItems2.get(0).getItemCode();
		}
		record.setUid(IdUtil.getId());
		if (StrUtil.isBlank(record.getId())) {
			record.setId(IdUtil.getId());
		}
		if (StrUtil.isBlank(record.getItemCode())) {
			record.setItemCode(itemCode); // 编码 用于统计多个组同一个项目的
		}
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsEnable("1");
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		mapper.insertSelective(record);
		// 同步项目库
		if (StrUtil.equals("1", record.getSynFlag())) {
			HrmsNewsalaryItemLibrary library = BeanUtil.copyProperties(record, HrmsNewsalaryItemLibrary.class);
			hrmsNewSalaryItemLibraryService.save(library);
		}
		return 1;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryItem record) {

		// 判断名称是否已存在
		List<HrmsNewsalaryItem> list = mapper.checkUse(record.getId());
		/*
		 * if(!list.isEmpty()){ for (int i = 0; i < list.size(); i++) {
		 * if("4".equals(list.get(i).getItemRule()) &&
		 * !StringUtil.isEmpty(list.get(i).getCountFormula())){
		 * if(list.get(i).getCountFormula().contains(record.getId())){ throw new
		 * RuntimeException("项目被引用，不能删除"); } } } }
		 */

		Example example = new Example(HrmsNewsalaryItem.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("optionId", record.getOptionId());
		criteria.andEqualTo("itemName", record.getItemName().trim());
		criteria.andNotEqualTo("id", record.getId());
		List<HrmsNewsalaryItem> hrmsNewsalaryItems = mapper.selectByExample(example);
		if (hrmsNewsalaryItems != null && hrmsNewsalaryItems.size() > 0) {
			throw new RuntimeException("项目已存在");
		}
		if (!"4".equals(record.getItemRule())){
			record.setCountFormula("");
			record.setCountFormulaText("");
		}
		if (!"2".equals(record.getItemRule())) {
			record.setSalaryItemAmount(null);
		}
		if (!"3".equals(record.getItemRule())) {
			record.setCustomRule(null);
		}
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String uid, String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryItem record = new HrmsNewsalaryItem();
		record.setUid(uid);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		mapper.deleteByPrimaryKey(record);
		return 1;
	}

	@Override
	public HrmsNewsalaryItem selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalaryItem> getDataSetList(Page page, HrmsNewsalaryItem record) {
		Example example = new Example(HrmsNewsalaryItem.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalaryItem> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	/**
	 * @Title getDataSetList
	 * @Description 获取所有不重复的方案薪酬项列表
	 * @return DataSet<HrmsNewsalaryItem>
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsNewsalaryItem> getAllOptionSalaryItem(String ssoOrgCode){
		return mapper.getAllOptionSalaryItem(ssoOrgCode);
	}

	@Override
	@Transactional(readOnly = false)
	public List<HrmsNewsalaryItemBasic> getDataItemBasicList(Page page, HrmsNewsalaryItemBasic record) {
		PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getGlobalSetting("Y");
		if (globalSetting.isSuccess() && "cssdeshfly".equals(globalSetting.getObject().getOrgCode())) {
			PlatformResult<List<DictItemResp>> platResult = dictItemFeignService
					.getDictItemByTypeCode("SALARY_ITEM_BASIC_TYPE");
			List<DictItemResp> list = (List<DictItemResp>) platResult.getObject();
			ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();
			if (CollUtil.isEmpty(list)) {
				return null;
			}
			Example example = new Example(HrmsNewsalaryItemBasic.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("groupId", "0");
			List<HrmsNewsalaryItemBasic> itemBasicList = basicMapper.selectByExampleAndRowBounds(example, page);
			if (CollUtil.isNotEmpty(itemBasicList)) {
				list = list.stream().filter(
						o -> !itemBasicList.stream().anyMatch(r -> StrUtil.equals(o.getItemName(), r.getItemName())))
						.collect(Collectors.toList());
			}
			if (CollUtil.isNotEmpty(list)) {
				for (DictItemResp dictItem : list) {
					HrmsNewsalaryItemBasic itemBasic = new HrmsNewsalaryItemBasic();
					itemBasic.setId(IdUtil.getId());
					itemBasic.setGroupId("0");
					itemBasic.setItemCode(dictItem.getItemCode());
					itemBasic.setItemName(dictItem.getItemName());
					itemBasic.setItemType("1");
					itemBasic.setCountType("1");
					itemBasic.setItemRule("1");
					itemBasic.setIsHidden("1");
					itemBasic.setIsDeleted("N");
					itemBasic.setCreateDate(new Date());
					itemBasic.setCreateUser(userInfo.getUsercode());
					itemBasic.setCreateUserName(userInfo.getUsername());
					itemBasic.setSsoOrgCode(userInfo.getCorpcode());
					itemBasic.setSsoOrgName(userInfo.getOrgName());
					basicMapper.insertSelective(itemBasic);
				}
			}
			List<HrmsNewsalaryItemBasic> reList = basicMapper.selectByExampleAndRowBounds(example, page);
			return reList;
		} else {
			Example example = new Example(HrmsNewsalaryItemBasic.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			return basicMapper.selectByExampleAndRowBounds(example, page);
		}
	}

	@Override
	public List<HrmsNewsalaryItem> getDataByGroupId(String id) {
		Example example = new Example(HrmsNewsalaryItem.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("groupId", id);
		example.setOrderByClause(" sort_no ");
		return mapper.selectByExample(example);
	}

	@Override
	public List<HrmsNewsalaryItem> getByOption(String optionId) {
		return mapper.getByOption(optionId);
	}

	public List<HrmsNewsalaryItem> getItemByOptionIdAll(String optionId) {
		return mapper.getItemByOptionIdAll(optionId);
	}

	@Override
	@Transactional(readOnly = false)
	public Integer paySheet(List<HrmsNewsalaryItem> record) {
		if (!record.isEmpty()) {
			for (int i = 0; i < record.size(); i++) {
				record.get(i).setUpdateDate(new Date());
				ThpsUser user = UserInfoHolder.getCurrentUserInfo();
				if (user != null) {
					record.get(i).setUpdateUser(user.getUsercode());
					record.get(i).setUpdateUserName(user.getUsername());
				}
				mapper.updateByPrimaryKeySelective(record.get(i));
			}
		}

		return record.size();
	}

	@Override
	@Transactional(readOnly = false)
	public Integer sortNo(List<HrmsNewsalaryItem> record) {
		if (!record.isEmpty()) {
			for (int i = 0; i < record.size(); i++) {
				HrmsNewsalaryItem hrmsNewsalaryItem = mapper.selectByPrimaryKey(record.get(i));
				// hrmsNewsalaryItem.setId(record.get(i).getId());
				hrmsNewsalaryItem.setUpdateDate(new Date());
				ThpsUser user = UserInfoHolder.getCurrentUserInfo();
				if (user != null) {
					hrmsNewsalaryItem.setUpdateUser(user.getUsercode());
					hrmsNewsalaryItem.setUpdateUserName(user.getUsername());
				}
				hrmsNewsalaryItem.setSortNo(record.get(i).getSortNo());
				mapper.updateByPrimaryKeySelective(hrmsNewsalaryItem);
			}
		}
		return record.size();
	}

	@Override
	public List<HrmsNewsalaryItem> getItemByOptionId(String optionId) {
		return mapper.getItemByOptionId(optionId);
	}

	@Override
	public DataSet<HrmsNewsalaryItem> getPayrollByEmployee(Page page, HrmsNewsalaryItem record) {
		if (StringUtil.isEmpty(record.getEmployeeId())) {
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				record.setEmployeeId(user.getId());
			}
		}
		page.setSord("desc");
		page.setSidx("t1.payroll_date");
		List<HrmsNewsalaryItem> records = mapper.getPayrollByEmployee(page, record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public Map<String, Object> getPayrollByEmployeeDetails(HrmsNewsalaryItem record) {
		Map<String, Object> retMap = new LinkedHashMap<>();
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (StringUtil.isEmpty(record.getEmployeeId())) {
			if (user != null) {
				record.setEmployeeId(user.getId());
			}
		}
		if (StringUtil.isEmpty(record.getPayrollDate())) {
			record.setPayrollDate(DateUtils.getStringDateShortYM(new Date()));
		}
		//如果查看的方案id为空则根据用户id和核算月份获取方案id
		if(StringUtils.isEmpty(record.getOptionId())){
			Example example = new Example(HrmsNewsalaryPayroll.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("employeeId", record.getEmployeeId());
			criteria.andEqualTo("payrollDate", record.getPayrollDate());
			List<HrmsNewsalaryPayroll> payrolls = newsalaryPayrollMapper.selectByExample(example);
			if(CollUtil.isNotEmpty(payrolls)){
				record.setOptionId(payrolls.get(0).getOptionId());
			}
		}
		if(StringUtils.isEmpty(record.getOptionId())){
			throw new BusinessException("未找到["+record.getPayrollDate()+"]月份的的工资条数据，请稍后再试");
		}
		HrmsNewsalaryPayslip paySlip = hrmsNewsalaryPayslipService.getPaySlipByOptionId(record.getOptionId());
		retMap.put("payrollDate", record.getPayrollDate().replace("-", "年") + "月工资");
		retMap.put("实发工资", "");
		retMap.put("温馨提示",
				Objects.isNull(paySlip) || StrUtil.isBlank(paySlip.getHint()) ? "工资条属于敏感信息,请注意保密!" : paySlip.getHint());
		Map<String, Object> empInfoMap = new LinkedHashMap<>();
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsNewsalaryItem> top = mapper.getPayrollDetailsTop(record);
		if (top != null && top.size() == 1) {
			empInfoMap.put("姓名", top.get(0).getEmployeeName());
			empInfoMap.put("工号", top.get(0).getEmployeeNo());
			empInfoMap.put("部门", top.get(0).getOrgName());
			empInfoMap.put("岗位", top.get(0).getPersonalIdentityText());
		}
		retMap.put("基本信息", empInfoMap);
		List<HrmsNewsalaryItemGroup> salaryGroupList = hrmsNewsalaryItemGroupService
				.getSalaryGroupByOptionId(record.getOptionId());
		List<HrmsNewsalaryItem> list = mapper.getPayrollByEmployeeDetails(record);
		Map<String, Object> salaryGroupMap = new LinkedHashMap<>();
		for (HrmsNewsalaryItemGroup salaryGroup : salaryGroupList) {
			Map<String, Object> salaryItemMap = new LinkedHashMap<>();
			for (HrmsNewsalaryItem salaryItem : list) {
				if (StrUtil.equals(salaryGroup.getId(), salaryItem.getGroupId())) {
					if (StrUtil.isBlank(salaryItem.getSalaryRemark())) {
						salaryItemMap.put(salaryItem.getItemName(), salaryItem.getSalary());
					} else {
						salaryItemMap.put(salaryItem.getItemName(),
								salaryItem.getSalary() + "-" + salaryItem.getSalaryRemark());
					}
				}
				if (StrUtil.equals("1", salaryItem.getActualSalary())) {
					retMap.put("实发工资", salaryItem.getSalary());
				}
			}
			salaryGroupMap.put(salaryGroup.getItemGroup(), salaryItemMap);
		}
		retMap.put("薪资项", salaryGroupMap);
		return retMap;
	}

	@Override
	public List<HrmsNewsalaryItem> getItemByOptionAndrule(String optionId, String rule) {
		List<String> lsRule = Arrays.asList(rule.split(","));
		Example example = new Example(HrmsNewsalaryItem.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("optionId", optionId);
		criteria.andIn("itemRule", lsRule);
//		criteria.andEqualTo("itemRule", rule);
		return mapper.selectByExample(example);
	}

	@Override
	public List<VueTableEntity> salaryCountTitle(String optionId) {
		Assert.hasText(optionId, "optionId不能为空!");
		int width = 20;
		List<HrmsNewsalaryItem> list = mapper.salaryCountTitle(optionId);
		List<VueTableEntity> retVueTableEntity = new ArrayList<>();
		retVueTableEntity.add(new VueTableEntity("薪酬月份", "payroll_date", null, 80, null));
		retVueTableEntity.add(new VueTableEntity("工号", "employee_no", null, 80, null));
		retVueTableEntity.add(new VueTableEntity("姓名", "employee_name", null, 80, null));
		retVueTableEntity.add(new VueTableEntity("部门", "orgName", null, 80, null));
		retVueTableEntity.add(new VueTableEntity("薪酬组", "optionName", null, 80, null));
		if (!list.isEmpty() && list.size() > 0) {
			list.forEach(item -> {
				retVueTableEntity.add(new VueTableEntity(item.getItemName(), item.getItemCode(), null,
						item.getItemName().length() == 2 ? 80 : item.getItemName().length() * width, null));
			});
		}
		retVueTableEntity.add(new VueTableEntity("处理人", "update_user_name", null, 80, null));
		retVueTableEntity.add(new VueTableEntity("最后处理日期", "update_date", null, 80, null));
		return retVueTableEntity;
	}

	@Override
	public List<VueTableEntity> salaryCountTitleByDefinition(SalaryCountSearchReq record) {

		List<HrmsNewsalaryItem> list = mapper.salaryCountTitle(record.getOptionId());

		List<VueTableEntity> retVueTableEntity = new ArrayList<>();
		int width= 20;
		//手动写  一个个控制
		retVueTableEntity.add(new VueTableEntity("序号","no",null,80,null));
		retVueTableEntity.add(new VueTableEntity("薪酬月份","payroll_date",null,80,null));
		if(!StringUtil.isEmpty(record.getExportJson())){
			String exportJson = record.getExportJson();  //转json
			List<String> strList = Arrays.asList(exportJson.split(","));
			if(strList.contains("employee_name")){
				retVueTableEntity.add(new VueTableEntity("姓名","employee_name",null,80,null));
			}
			if(strList.contains("employee_no")){
				retVueTableEntity.add(new VueTableEntity("工号","employee_no",null,80,null));
			}
			if(strList.contains("orgName")){
				retVueTableEntity.add(new VueTableEntity("部门","orgName",null,80,null));
			}
			if(strList.contains("optionName")){
				retVueTableEntity.add(new VueTableEntity("薪酬组","optionName",null,80,null));
			}
			if(strList.contains("employee_status_text")){
				retVueTableEntity.add(new VueTableEntity("员工状态","employee_status_text",null,80,null));
			}
			if(strList.contains("personal_identity_text")){
				retVueTableEntity.add(new VueTableEntity("岗位名称","personal_identity_text",null,80,null));
			}
			if(strList.contains("establishment_type_text")){
				retVueTableEntity.add(new VueTableEntity("编制类型","establishment_type_text",null,80,null));
			}
			if(strList.contains("entry_date")){
				retVueTableEntity.add(new VueTableEntity("入职日期","entry_date",null,80,null));
			}
			if(strList.contains("positive_time")){
				retVueTableEntity.add(new VueTableEntity("转正日期","positive_time",null,80,null));
			}
			if(strList.contains("identity_number")){
				retVueTableEntity.add(new VueTableEntity("身份证","identity_number",null,80,null));
			}
			if(strList.contains("bankcardname")){
				retVueTableEntity.add(new VueTableEntity("开户行","bankcardname",null,80,null));
			}
			if(strList.contains("bankcardno")){
				retVueTableEntity.add(new VueTableEntity("银行账号","bankcardno",null,80,null));
			}

			if(!list.isEmpty() && list.size() > 0){
				list.forEach(item->{
					if(strList.contains(item.getItemCode())){
						retVueTableEntity.add(new VueTableEntity(item.getItemName(),item.getItemCode(),null,item.getItemName().length() == 2 ? 80 : item.getItemName().length() * width,true,null,null));
					}
				});
			}
		}else{
			retVueTableEntity.add(new VueTableEntity("工号","employee_no",null,80,null));
			retVueTableEntity.add(new VueTableEntity("姓名","employee_name",null,80,null));
			retVueTableEntity.add(new VueTableEntity("部门","orgName",null,80,null));
			retVueTableEntity.add(new VueTableEntity("薪酬组","optionName",null,80,null));
			if(!list.isEmpty() && list.size() > 0){
				list.forEach(item->{
					//和集合对比是否包含
					retVueTableEntity.add(new VueTableEntity(item.getItemName(),item.getItemCode(),null,120,true,null,null));
				});
			}
		}
		retVueTableEntity.add(new VueTableEntity("处理人","update_user_name",null,80,null));
		retVueTableEntity.add(new VueTableEntity("最后处理日期","update_date",null,80,null));
		return retVueTableEntity;
	}

	@Override
	public List<HrmsNewsalaryItem> getListItemCode(String optionId) {
		return mapper.salaryCountTitle(optionId);
	}

	@Override
	public List<LinkedHashMap<String, String>> getExportColumns(String optionId) {
		return mapper.getExportColumns(optionId);
	}

	// 判断薪酬档案项目是否被试用
	@Override
	public boolean checkUse(String id) {
		List<HrmsNewsalaryItem> list = mapper.checkUse(id);
		if (!list.isEmpty()) {
			return true;
		}
		return false;
	}

	@Override
	public List<HrmsNewsalaryItem> getALLByMap() {
		return mapper.getALLByMap();
	}

	@Override
	public Integer deleteByGroupId(String groupId) {
		return mapper.deleteByGroupId(groupId);
	}

	@Override
	public void updateArticle(HrmsNewsalaryItem record) {
		HrmsNewsalaryItem item = new HrmsNewsalaryItem();
		item.setId(record.getId());
		item.setIsArticle("2");
		item.setIsHidden("2");
		item.setActualSalary("2");
		item.setShSalary("2");
		item.setIsTotal("2");
        item.setTotalSortNo(999);
		mapper.updateByPrimaryKeySelective(item);
	}

	@Override
	public boolean checkManual(String optionId) {
		boolean check = false;
		Example example = new Example(HrmsNewsalaryItem.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("optionId", optionId);
		criteria.andEqualTo("itemRule", "1");
		List<HrmsNewsalaryItem> list = mapper.selectByExample(example);
		if (CollUtil.isNotEmpty(list)) {
			check = true;
		}
		return check;
	}

	@Override
	@Transactional(readOnly = false)
	public void enable(String uid, String isEable) {
		HrmsNewsalaryItem hrmsNewsalaryItem = new HrmsNewsalaryItem();
		hrmsNewsalaryItem.setUid(uid);
		hrmsNewsalaryItem.setIsEnable(isEable);
		HrmsNewsalaryItem salaryItem = mapper.selectByPrimaryKey(hrmsNewsalaryItem);
		String id = salaryItem.getId();
		/*
		 * if(StrUtil.equals("2",isEable)){ if(checkUse(id)){ throw new
		 * BusinessException("项目被引用，不能禁用!"); } }
		 */
		mapper.updateByPrimaryKeySelective(hrmsNewsalaryItem);
	}

	@Override
	public boolean checkQuote(String id) {
		boolean check = false;
		Example example = new Example(HrmsNewsalaryItem.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("id", id);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		List<HrmsNewsalaryItem> list = mapper.selectByExample(example);
		if (CollUtil.isNotEmpty(list)) {
			check = true;
		}
		return check;
	}

	@Override
	public List<HrmsNewsalaryItem> getManualByOptionId(String optionId) {
		return mapper.getManualByOptionId(optionId);
	}

	@Override
	public void deleteByOptionId(String optionId) {
		Example example = new Example(HrmsNewsalaryItem.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("optionId", optionId);
		HrmsNewsalaryItem hrmsNewsalaryItem = new HrmsNewsalaryItem();
		hrmsNewsalaryItem.setIsDeleted("Y");
		mapper.updateByExampleSelective(hrmsNewsalaryItem, example);
	}

	@Override
	public List<VueTableEntity> salaryTotalTitle(String reportId) {
		Assert.hasText(reportId, "reportId不能为空!");
		int width = 20;
		List<VueTableEntity> retVueTableEntity = new ArrayList<>();
		List<HrmsNewsalaryReportTotalEo> totalList = mapper.salaryTotalCountTitle(reportId);
		retVueTableEntity.add(new VueTableEntity("薪酬月份", "payroll_date", null, 80, null));
		retVueTableEntity.add(new VueTableEntity("工号", "employee_no", null, 80, null));
		retVueTableEntity.add(new VueTableEntity("姓名", "employee_name", null, 80, null));
		retVueTableEntity.add(new VueTableEntity("部门", "orgName", null, 80, null));
		retVueTableEntity.add(new VueTableEntity("薪酬组", "optionName", null, 80, null));
		if (!totalList.isEmpty() && totalList.size() > 0) {
			totalList.forEach(item -> {
				retVueTableEntity.add(new VueTableEntity(item.getColName(), item.getColCode(), null,
						item.getColName().length() == 2 ? 80 : item.getColName().length() * width, null));
			});
		}
		retVueTableEntity.add(new VueTableEntity("处理人", "update_user_name", null, 80, null));
		retVueTableEntity.add(new VueTableEntity("最后处理日期", "update_date", null, 80, null));
		return retVueTableEntity;
	}

	@Override
	public List<LinkedHashMap<String, String>> getTotalExportColumns(String reportId) {
		return mapper.getTotalExportColumns(reportId);
	}

	@Override
	public List<VueTableEntity> salaryTotalCountTitleByDefinition(SalaryCountSearchReq record) {
		List<HrmsNewsalaryReportTotalEo> list = mapper.salaryTotalCountTitle(record.getReportId());
		int width = 20;

		List<VueTableEntity> retVueTableEntity = new ArrayList<>();
		// 手动写 一个个控制
		retVueTableEntity.add(new VueTableEntity("序号", "no", null, 80, null));
		retVueTableEntity.add(new VueTableEntity("薪酬月份", "payroll_date", null, 80, null));
		if (!StringUtil.isEmpty(record.getExportJson())) {
			String exportJson = record.getExportJson(); // 转json
			List<String> strList = Arrays.asList(exportJson.split(","));
			if (strList.contains("employee_name")) {
				retVueTableEntity.add(new VueTableEntity("姓名", "employee_name", null, 80, null));
			}
			if (strList.contains("employee_no")) {
				retVueTableEntity.add(new VueTableEntity("工号", "employee_no", null, 80, null));
			}
			if (strList.contains("orgName")) {
				retVueTableEntity.add(new VueTableEntity("部门", "orgName", null, 80, null));
			}
			if (strList.contains("optionName")) {
				retVueTableEntity.add(new VueTableEntity("薪酬组", "optionName", null, 80, null));
			}
			if (strList.contains("employee_status_text")) {
				retVueTableEntity.add(new VueTableEntity("员工状态", "employee_status_text", null, 80, null));
			}
			if (strList.contains("personal_identity_text")) {
				retVueTableEntity.add(new VueTableEntity("岗位名称", "personal_identity_text", null, 80, null));
			}
			if (strList.contains("establishment_type_text")) {
				retVueTableEntity.add(new VueTableEntity("编制类型", "establishment_type_text", null, 80, null));
			}
			if (strList.contains("entry_date")) {
				retVueTableEntity.add(new VueTableEntity("入职日期", "entry_date", null, 80, null));
			}
			if (strList.contains("positive_time")) {
				retVueTableEntity.add(new VueTableEntity("转正日期", "positive_time", null, 80, null));
			}
			if (strList.contains("identity_number")) {
				retVueTableEntity.add(new VueTableEntity("身份证", "identity_number", null, 80, null));
			}
			if (strList.contains("bankcardname")) {
				retVueTableEntity.add(new VueTableEntity("开户行", "bankcardname", null, 80, null));
			}
			if (strList.contains("bankcardno")) {
				retVueTableEntity.add(new VueTableEntity("银行账号", "bankcardno", null, 80, null));
			}

			if (!list.isEmpty() && list.size() > 0) {
				list.forEach(item -> {
					if (strList.contains(item.getColCode())) {
						retVueTableEntity.add(new VueTableEntity(item.getColName(), item.getColCode(), null,
								item.getColName().length() == 2 ? 80 : item.getColName().length() * width, true, null,null));
					}
				});
			}
		} else {
			retVueTableEntity.add(new VueTableEntity("工号", "employee_no", null, 80, null));
			retVueTableEntity.add(new VueTableEntity("姓名", "employee_name", null, 80, null));
			retVueTableEntity.add(new VueTableEntity("部门", "orgName", null, 80, null));
			retVueTableEntity.add(new VueTableEntity("薪酬组", "optionName", null, 80, null));
			if (!list.isEmpty() && list.size() > 0) {
				list.forEach(item -> {
					// 和集合对比是否包含
					retVueTableEntity
							.add(new VueTableEntity(item.getColName(), item.getColCode(), null, 120, true, null,null));
				});
			}
		}
		retVueTableEntity.add(new VueTableEntity("处理人", "update_user_name", null, 80, null));
		retVueTableEntity.add(new VueTableEntity("最后处理日期", "update_date", null, 80, null));
		return retVueTableEntity;
	}
}

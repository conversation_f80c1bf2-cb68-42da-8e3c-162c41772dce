package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryItemMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionEmpMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionPayrollMapper;
import cn.trasen.hrms.salary.model.*;
import cn.trasen.hrms.salary.service.*;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.utils.IdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName HrmsNewsalaryOptionServiceImpl
 * @Description TODO
 * @date 2024��2��6�� ����10:03:32
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryOptionServiceImpl implements HrmsNewsalaryOptionService {

	@Resource
	private HrmsNewsalaryOptionMapper mapper;
	@Autowired
	private HrmsNewsalaryOptionEmpService hrmsNewsalaryOptionEmpService;
	@Resource
	private HrmsNewsalaryOptionEmpMapper hrmsNewsalaryOptionEmpMapper;
	
	@Autowired
	HrmsNewsalaryOptionPayrollService hrmsNewsalaryOptionPayrollService;

	@Autowired
	private IHrmsNewsalaryPayslipService hrmsNewsalaryPayslipService;

	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;

	@Autowired
	private HrmsNewsalaryItemService hrmsNewsalaryItemService;
	@Resource
	private HrmsNewsalaryItemMapper itemMapper;
	@Resource
	private HrmsNewsalaryOptionPayrollMapper payrollMapper;
	@Resource
	private HrmsNewsalaryOptionMapper optionMapper;

	@Transactional(readOnly = false)
	@Override
	public String save(HrmsNewsalaryOption record) {
		String id = IdUtil.getId();
		record.setId(id);
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		record.setIsEnable("2"); // 默认停用
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		String[] ids = null;
		if (StrUtil.isNotBlank(record.getEmployeeId())) {
			ids = record.getEmployeeId().split(",");
		}
		String[] names = null;
		if (StrUtil.isNotBlank(record.getEmployeeName())) {
			names = record.getEmployeeName().split(",");
		}
		if (ids != null && ids.length > 0 && names != null && names.length > 0) {
			for (int i = 0; i < ids.length; i++) {
				// 验证人员是否在别的方案
				HrmsNewsalaryOptionEmp empByOption = hrmsNewsalaryOptionEmpService.getEmpByOption(ids[i]);
				if (null != empByOption) {
					//从其他方案移除
					HrmsNewsalaryOptionEmp hrmsNewsalaryOptionEmpEo = hrmsNewsalaryOptionEmpService.destoryByEmployeeId(ids[i]);
					// 查询之前方案薪酬组数量
					if (Objects.nonNull(hrmsNewsalaryOptionEmpEo)) {
						HrmsNewsalaryOption optionRecord = optionMapper.selectByPrimaryKey(hrmsNewsalaryOptionEmpEo.getOptionId());
						List<HrmsNewsalaryOptionEmp> list = hrmsNewsalaryOptionEmpService
								.getAllByOptionId(hrmsNewsalaryOptionEmpEo.getOptionId(), null);
						optionRecord.setHeadCount(String.valueOf(list.size()));
						optionMapper.updateByPrimaryKeySelective(optionRecord);
					}
//					throw new BusinessException("[" + names[i] + "] 已在方案：" + empByOption.getOptionName() + "中");
				}
				HrmsNewsalaryOptionEmp item = new HrmsNewsalaryOptionEmp();
				item.setEmployeeId(ids[i]);
				item.setEmployeeName(names[i]);
				item.setOptionId(id);
				hrmsNewsalaryOptionEmpService.save(item);
			}
		}
		if (StrUtil.isBlank(record.getEmployeeId())) {
			record.setHeadCount("0");
		} else {
			record.setHeadCount(String.valueOf(ids.length));
		}
		mapper.insertSelective(record);
		// 创建工资条模板
		if (StrUtil.equals("1", record.getPaySlip())) {
			HrmsNewsalaryPayslip payslip = new HrmsNewsalaryPayslip();
			payslip.setOptionId(id);
			payslip.setOptionName(record.getOptionName());
			payslip.setSlipName(record.getOptionName() + "工资条模板");
			payslip.setIsEnable("1");
			payslip.setHint("工资属于敏感信息，请注意保密！");
			hrmsNewsalaryPayslipService.add(payslip);
		}
		return "1";
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryOption record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		// 同步工资条模板名称
		HrmsNewsalaryOption option = mapper.selectByPrimaryKey(record.getId());
		if (!StrUtil.equals(option.getOptionName(), record.getOptionName())) {
			hrmsNewsalaryPayslipService.sysSlipName(record.getId(), record.getOptionName());
		}
		hrmsNewsalaryOptionEmpService.deleteByOptionId(record.getId());
		if (StrUtil.isNotBlank(record.getEmployeeId())) {
			String[] ids = record.getEmployeeId().split(",");
			String[] names = record.getEmployeeName().split(",");
			if (ids != null && ids.length > 0 && names != null && names.length > 0) {
				for (int i = 0; i < ids.length; i++) {

					// 验证人员是否在别的方案
					HrmsNewsalaryOptionEmp empByOption = hrmsNewsalaryOptionEmpService.getEmpByOption(ids[i]);
					if (null != empByOption) {
						//从其他方案移除
						HrmsNewsalaryOptionEmp hrmsNewsalaryOptionEmpEo = hrmsNewsalaryOptionEmpService.destoryByEmployeeId(ids[i]);
						// 查询之前方案薪酬组数量
						if (Objects.nonNull(hrmsNewsalaryOptionEmpEo)) {
							HrmsNewsalaryOption optionRecord = optionMapper.selectByPrimaryKey(hrmsNewsalaryOptionEmpEo.getOptionId());
							List<HrmsNewsalaryOptionEmp> list = hrmsNewsalaryOptionEmpService
									.getAllByOptionId(hrmsNewsalaryOptionEmpEo.getOptionId(), null);
							optionRecord.setHeadCount(String.valueOf(list.size()));
							optionMapper.updateByPrimaryKeySelective(optionRecord);
						}
//						throw new BusinessException("[" + names[i] + "] 已在方案：" + empByOption.getOptionName() + "中");
					}

					HrmsNewsalaryOptionEmp item = new HrmsNewsalaryOptionEmp();
					item.setEmployeeId(ids[i]);
					item.setEmployeeName(names[i]);
					item.setOptionId(record.getId());
					hrmsNewsalaryOptionEmpService.save(item);
				}
			}
			Example example3 = new Example(HrmsNewsalaryOptionPayroll.class);
			Example.Criteria criteria3 = example3.createCriteria();
			criteria3.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria3.andEqualTo("optionId", record.getId());
			criteria3.andEqualTo("computeDate", option.getOptionCycle());
			HrmsNewsalaryOptionPayroll payroll = payrollMapper.selectOneByExample(example3);
			if (Objects.nonNull(payroll) && !Objects.equals(payroll.getComputeStatus(),"3")){
				payroll.setHeadCount(ids.length);
				payrollMapper.updateByPrimaryKeySelective(payroll);
			}
			record.setHeadCount(String.valueOf(ids.length));
		} else {
			record.setHeadCount("0");
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		// 有过核算记录的方案不能删除
		List<HrmsNewsalaryOptionPayroll> list = hrmsNewsalaryOptionPayrollService.getDataByOptionId(id);
		if (!list.isEmpty()) {
			throw new BusinessException("方案已被使用，不能删除");
		}
		List<HrmsNewsalaryOptionEmp> optionEmpList = hrmsNewsalaryOptionEmpService.getAllByOptionId(id, null);
		if (CollUtil.isNotEmpty(optionEmpList)) {
			throw new BusinessException("方案下存在算薪人员,不允许删除!");
		}
		HrmsNewsalaryOption record = new HrmsNewsalaryOption();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		// 删除方案下面的所有人员
		hrmsNewsalaryOptionEmpService.deleteByOptionId(id);
		// 删除方案下面的所有项目
		hrmsNewsalaryItemService.deleteByOptionId(id);
		return mapper.updateByPrimaryKeySelective(record);
	}
	/**
	 * selectById此方法存在逻辑问题，未来将进行清除
	 */
	@Override
	public HrmsNewsalaryOption selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryOption hrmsNewsalaryOption = mapper.selectByPrimaryKey(id);
		List<HrmsNewsalaryOptionEmp> list = hrmsNewsalaryOptionEmpService.getAllByOptionId(id, null);
		List<String> empNoList = new ArrayList<>();
		if (CollUtil.isNotEmpty(list)) {
			List<String> empIdList = new ArrayList<>();
			empIdList = list.stream().map(HrmsNewsalaryOptionEmp::getEmployeeId).collect(Collectors.toList());
			empNoList = hrmsEmployeeService.getEmpnoByEmpId(empIdList);
			StringBuffer sbids = new StringBuffer();
			StringBuffer sbnames = new StringBuffer();
			StringBuffer sbnos = new StringBuffer();
			for (String empNo : empNoList) {
				sbnos.append(empNo).append(",");
			}
			if (!list.isEmpty()) {
				list.forEach(item -> {
					sbids.append(item.getEmployeeId()).append(",");
					sbnames.append(item.getEmployeeName()).append(",");
				});
			}
			if(sbids.length() > 0) {
				sbids.deleteCharAt(sbids.length() - 1);
			}
			if(sbnames.length() > 0) {
				sbnames.deleteCharAt(sbnames.length() - 1);
			}
			if(sbnos.length() > 0) {
				sbnos.deleteCharAt(sbnos.length() - 1);
			}
			hrmsNewsalaryOption.setEmployeeId(sbids.toString());
			hrmsNewsalaryOption.setEmployeeNo(String.valueOf(sbnos));
			hrmsNewsalaryOption.setEmployeeName(sbnames.toString());
		}
		return hrmsNewsalaryOption;
	}

	public HrmsNewsalaryOption selectByOptionId(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryOption hrmsNewsalaryOption = mapper.selectByPrimaryKey(id);
		List<HrmsNewsalaryOptionEmp> list = hrmsNewsalaryOptionEmpMapper.getEmployeeDetails(id);
		if(CollUtil.isNotEmpty(list)) {
			hrmsNewsalaryOption.setEmpList(list);
			StringBuffer empIds = new StringBuffer();
			StringBuffer empNames = new StringBuffer();
			StringBuffer empNos = new StringBuffer();
			for (HrmsNewsalaryOptionEmp optEmp : list) {
				empIds.append(optEmp.getEmployeeId()).append(",");
				empNames.append(optEmp.getEmployeeName()).append(",");
				empNos.append(optEmp.getEmployeeNo()).append(",");
			}
			if (empIds.length() > 0 && empNames.length() > 0 && empNos.length() > 0) {
				empIds.deleteCharAt(empIds.length() - 1);
				empNames.deleteCharAt(empNames.length() - 1);
				empNos.deleteCharAt(empNos.length() - 1);
			}
			hrmsNewsalaryOption.setEmployeeId(empIds.toString());
			hrmsNewsalaryOption.setEmployeeNo(String.valueOf(empNos));
			hrmsNewsalaryOption.setEmployeeName(empNames.toString());
		}
		return hrmsNewsalaryOption;
	}
	
	@Transactional(readOnly = false)
	@Override
	public Integer enable(HrmsNewsalaryOption record) {

		Assert.hasText(record.getId(), "ID不能为空.");
		HrmsNewsalaryOption update = new HrmsNewsalaryOption();
		update.setUpdateDate(new Date());
		update.setId(record.getId());
		update.setIsEnable(record.getIsEnable());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			update.setUpdateUser(user.getUsercode());
			update.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(update);
	}

	@Override
	public DataSet<HrmsNewsalaryOption> getDataSetList(Page page, HrmsNewsalaryOption record) {
		Example example = new Example(HrmsNewsalaryOption.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if (!StringUtil.isEmpty(record.getIsEnable()) && "1".equals(record.getIsEnable())) {
			criteria.andCondition(" is_enable ='1' ");
		} else if (!StringUtil.isEmpty(record.getIsEnable()) && "2".equals(record.getIsEnable())) {
			criteria.andCondition(" is_enable ='2' ");
		} else {
			criteria.andCondition(" is_enable in ('1','2')   ");
		}
		if (StrUtil.isNotBlank(record.getOptionName())) {
			criteria.andLike("optionName", "%" + record.getOptionName() + "%");
		}

		page.setSidx("is_enable asc, create_date");
		page.setSord("desc");
		List<HrmsNewsalaryOption> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsNewsalaryOption> getData(String computeDate) {
		Example example = new Example(HrmsNewsalaryOption.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("isEnable", "1");
		criteria.andCondition(" DATE_FORMAT(create_date,'%Y-%m')  <= '" + computeDate + "' "); // 创建时间小于等级计算月份
		List<HrmsNewsalaryOption> records = mapper.selectByExample(example);
		return records;
	}

	@Override
	public String getOptionCode() {
		String optionCode = "";
		Example example = new Example(HrmsNewsalaryOption.class);
		Example.Criteria criteria = example.createCriteria();
		LocalDateTime dateTime = LocalDateTime.now();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
		String currentYearMonth = dateTime.format(formatter);
		criteria.andLike("optionCode", currentYearMonth + "%");
		List<HrmsNewsalaryOption> list = mapper.selectByExample(example);
		if (CollUtil.isNotEmpty(list)) {
			list = list.stream().sorted(Comparator.comparing(HrmsNewsalaryOption::getOptionCode).reversed())
					.collect(Collectors.toList());
			String code = list.get(0).getOptionCode();
			optionCode = String.valueOf(Integer.valueOf(code) + 1);
		} else {
			optionCode = currentYearMonth + "001";
		}
		return optionCode;
	}

	@Override
	public DataSet<HrmsNewsalaryOption> getPaySlipOption(HrmsNewsalaryOption record, Page page) {
		List<HrmsNewsalaryOption> list = mapper.getPaySlipOption(record, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}

	@Override
	@Transactional(readOnly = false)
	public void batchEnable(List<String> ids) {
		HrmsNewsalaryOption update = new HrmsNewsalaryOption();
		update.setUpdateDate(new Date());
		update.setIsEnable("1");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			update.setUpdateUser(user.getUsercode());
			update.setUpdateUserName(user.getUsername());
		}
		Example example = new Example(HrmsNewsalaryOption.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andIn("id", ids);
		mapper.updateByExampleSelective(update, example);
	}

	@Override
	@Transactional(readOnly = false)
	public void batchdisEnable(List<String> ids) {
		HrmsNewsalaryOption update = new HrmsNewsalaryOption();
		update.setUpdateDate(new Date());
		update.setIsEnable("2");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			update.setUpdateUser(user.getUsercode());
			update.setUpdateUserName(user.getUsername());
		}
		Example example = new Example(HrmsNewsalaryOption.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andIn("id", ids);
		mapper.updateByExampleSelective(update, example);
	}

	@Transactional(readOnly = false)
	@Override
	public void syncHeadCount(String id) {
		HrmsNewsalaryOption record = mapper.selectByPrimaryKey(id);
		if (null != record) {
			List<HrmsNewsalaryOptionEmp> list = hrmsNewsalaryOptionEmpService.getAllByOptionId(id, null);
			record.setHeadCount(String.valueOf(list.size()));
			mapper.updateByPrimaryKeySelective(record);
		}
	}

	@Override
	public List<HrmsNewsalaryItem> selectOptionByItemId(String id) {
		Example example = new Example(HrmsNewsalaryItem.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("optionId",id);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return itemMapper.selectByExample(example);
	}
}

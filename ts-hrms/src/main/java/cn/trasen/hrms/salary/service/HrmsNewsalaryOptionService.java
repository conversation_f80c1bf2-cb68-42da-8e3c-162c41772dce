package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOption;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;

import java.util.List;

/**
 * @ClassName HrmsNewsalaryOptionService
 * @Description TODO
 * @date 2024��2��6�� ����10:03:32
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryOptionService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��2��6�� ����10:03:32
	 * <AUTHOR>
	 */
	String save(HrmsNewsalaryOption record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��2��6�� ����10:03:32
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryOption record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��2��6�� ����10:03:32
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryOption
	 * @date 2024��2��6�� ����10:03:32
	 * <AUTHOR>
	 */
	HrmsNewsalaryOption selectById(String id);
	
	HrmsNewsalaryOption selectByOptionId(String optionId);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryOption>
	 * @date 2024��2��6�� ����10:03:32
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryOption> getDataSetList(Page page, HrmsNewsalaryOption record);

	//停用启用
	Integer enable(HrmsNewsalaryOption record);

	/**
	 * 所有启动薪酬组
	 * @param record
	 * @return
	 */
	List<HrmsNewsalaryOption> getData(String computeDate);

	/**
	 * 获取方案编号
	 * @return
	 */
	String getOptionCode();

	/**
	 * 工资条获取未关联薪酬方案
	 * @return
	 */
	DataSet<HrmsNewsalaryOption> getPaySlipOption(HrmsNewsalaryOption record,Page page);

	/**
	 * 批量启用
	 * @param ids
	 */
	void batchEnable(List<String> ids);

	/**
	 * 批量停用
	 * @param ids
	 */
	void batchdisEnable(List<String> ids);

	void syncHeadCount(String id);

	List<HrmsNewsalaryItem> selectOptionByItemId(String id);
}

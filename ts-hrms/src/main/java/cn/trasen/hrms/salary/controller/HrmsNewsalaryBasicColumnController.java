package cn.trasen.hrms.salary.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.hrms.bean.RequestContent;
import cn.trasen.hrms.common.KeyValue;
import cn.trasen.hrms.salary.DTO.*;
import cn.trasen.hrms.salary.enums.SalaryBaseColumnEnum;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicColumn;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicColumnService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicitemEmpService;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import cn.trasen.hrms.utils.CommonUtils;
import cn.trasen.hrms.utils.ExcelExportUtils;
import cn.trasen.hrms.utils.ExcelStyleUtil;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrmsNewsalaryBasicColumnController
 * @Description 薪酬档案基本列
 * @date 2023��11��11�� ����4:28:57
 */
@RestController
@Api(tags = "薪酬档案基本列Controller")
public class HrmsNewsalaryBasicColumnController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryBasicColumnController.class);

	@Autowired
	private HrmsNewsalaryBasicColumnService hrmsNewsalaryBasicColumnService;

	@Autowired
	HrmsNewsalaryBasicitemEmpService hrmsNewsalaryBasicitemEmpService;

	/**
	 * @param record
	 * @return PlatformResult<String>
	 * @Title saveHrmsNewsalaryBasicColumn
	 * @Description 新增
	 * @date 2023��11��11�� ����4:28:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/salaryBasicColumn/save")
	public PlatformResult<String> saveHrmsNewsalaryBasicColumn(@RequestBody HrmsNewsalaryBasicColumn record) {
		try {
			hrmsNewsalaryBasicColumnService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "排序", notes = "排序")
	@PostMapping("/api/salaryBasicColumn/updateNumberSort")
	public PlatformResult<String> updateNumberSort(@RequestBody List<HrmsNewsalaryBasicColumn> records) {
		try {
			hrmsNewsalaryBasicColumnService.updateNumberSort(records);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @param record
	 * @return PlatformResult<String>
	 * @Title updateHrmsNewsalaryBasicColumn
	 * @Description 编辑
	 * @date 2023��11��11�� ����4:28:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryBasicColumn/update")
	public PlatformResult<String> updateHrmsNewsalaryBasicColumn(@RequestBody HrmsNewsalaryBasicColumn record) {
		try {
			hrmsNewsalaryBasicColumnService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryBasicColumn>
	 * @Title selectHrmsNewsalaryBasicColumnById
	 * @Description 根据ID查询
	 * @date 2023��11��11�� ����4:28:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/salaryBasicColumn/{id}")
	public PlatformResult<HrmsNewsalaryBasicColumn> selectHrmsNewsalaryBasicColumnById(@PathVariable String id) {
		try {
			HrmsNewsalaryBasicColumn record = hrmsNewsalaryBasicColumnService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @param id
	 * @return PlatformResult<String>
	 * @Title deleteHrmsNewsalaryBasicColumnById
	 * @Description 根据ID删除
	 * @date 2023��11��11�� ����4:28:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/salaryBasicColumn/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryBasicColumnById(@PathVariable String id) {
		try {
			hrmsNewsalaryBasicColumnService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryBasicColumn>
	 * @Title selectHrmsNewsalaryBasicColumnList
	 * @Description 查询列表
	 * @date 2023��11��11�� ����4:28:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/salaryBasicColumn/list")
	public DataSet<HrmsNewsalaryBasicColumn> selectHrmsNewsalaryBasicColumnList(Page page,
			HrmsNewsalaryBasicColumn record) {
		return hrmsNewsalaryBasicColumnService.getDataSetList(page, record);
	}

	@ApiOperation(value = "查询基础字段枚举下拉", notes = "查询基础字段枚举下拉")
	@GetMapping(value = "/api/salaryBasicColumn/getBaseEnum")
	public PlatformResult<List<KeyValue>> getSalaryItemTypeList() {
		try {
			return PlatformResult.success(SalaryBaseColumnEnum.getBaseColumnEnumList());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}

	@ApiOperation(value = "薪酬档案字段列表", notes = "薪酬档案字段列表")
	@GetMapping("/api/salaryBasicColumn/listAll")
	public PlatformResult<Map<String, Object>> listAll() {
		try {
			Page page = new Page();
			page.setPageSize(Integer.MAX_VALUE);
			HrmsNewsalaryBasicColumn record = new HrmsNewsalaryBasicColumn();
			DataSet<HrmsNewsalaryBasicColumn> dataSetList = hrmsNewsalaryBasicColumnService.getDataSetList(page,
					record);
			Map<String, Object> retmap = new LinkedHashMap<String, Object>();
			List<HrmsNewsalaryBasicColumn> rows = dataSetList.getRows();
			retmap.put("updateName", "");
			retmap.put("updateDate", "");
			if (rows != null && rows.size() > 0) {
				List<HrmsNewsalaryBasicColumn> copyList = rows.stream().collect(Collectors.toList());
				copyList.sort(Comparator.comparing(HrmsNewsalaryBasicColumn::getUpdateDate).reversed());
				retmap.put("updateDate", copyList.get(0).getUpdateDate());
				retmap.put("updateName", copyList.get(0).getUpdateUserName());
			}
			retmap.put("data", rows);
			return PlatformResult.success(retmap);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}

	// 薪酬档案列表表头

	@ApiOperation(value = "薪酬档案列表表头", notes = "薪酬档案列表表头")
	@GetMapping("/api/salaryBasicColumn/listTableTitle")
	public PlatformResult<List<VueTableEntity>> listTableTitle() {
		try {
			List<VueTableEntity> list = hrmsNewsalaryBasicColumnService.listTableTitle();
			return PlatformResult.success(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}

	}

	// 薪酬档案列表数据
	@ApiOperation(value = "查询未定薪人员人数", notes = "查询未定薪人员人数")
	@GetMapping("/api/salaryBasicColumn/getUncertain")
	public PlatformResult<Integer> getUncertain() {
		try {
			Integer size = hrmsNewsalaryBasicColumnService.getUncertain();
			return PlatformResult.success(size);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "薪酬档案列表数据", notes = "薪酬档案列表数据")
	@GetMapping("/api/salaryBasicColumn/listTableData")
	public DataSet<Map<String, String>> listTableData(Page page, SearchListTable record) {
		return hrmsNewsalaryBasicColumnService.listTableData(page, record);
	}

	@ApiOperation(value = "员工个人薪酬台帐列表数据", notes = "员工个人薪酬台帐列表数据")
	@GetMapping("/api/salaryBasicColumn/empSalaryData")
	public DataSet<Map<String, String>> empSalaryData(Page page, SchEmpSalary record) {
		return hrmsNewsalaryBasicColumnService.EmpPayrollData(page, record);
	}

	// 定薪调薪模版导出
	@ApiOperation(value = "导出定薪调薪模版", notes = "导出定薪调薪模版")
	@RequestMapping(value = "/api/salaryBasicColumn/exportTemplateData",method = {RequestMethod.POST, RequestMethod.GET})
	public void exportTemplateData(HttpServletResponse response, SearchListTable record) {
		try {
			int index = 13; //表头创建开始位置
			String tempUrl = "template/dxtxTemplate.xlsx";
			if(CollUtil.isNotEmpty(record.getBasicItemIds())){
				index = 7;
				tempUrl = "template/dxtxTemplate_custom.xlsx";
			}
			// 创建Excel文档
			TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath(tempUrl),
					new Integer[0]);

			Workbook workbook = ExcelExportUtil.exportExcel(params, new HashMap());

			Sheet sheet1 = workbook.getSheet("Sheet1");

			// 设置样式
			CellStyle textStyle = workbook.createCellStyle();
			// 四个边框
			textStyle.setBorderTop(BorderStyle.THIN);
			textStyle.setBorderBottom(BorderStyle.THIN);
			textStyle.setBorderLeft(BorderStyle.THIN);
			textStyle.setBorderRight(BorderStyle.THIN);
			// 上下左右居中
			textStyle.setAlignment(HorizontalAlignment.CENTER);
			textStyle.setVerticalAlignment(VerticalAlignment.CENTER);

			textStyle.setDataFormat(workbook.getCreationHelper().createDataFormat().getFormat("text"));

			Row row0 = sheet1.getRow(0);
			Row row1 = sheet1.getRow(1);
			List<HrmsNewsalaryBasicColumn> allList = hrmsNewsalaryBasicColumnService.getAllListBaType23();
			//指定薪酬项导出
			if(CollUtil.isNotEmpty(record.getBasicItemIds())){
				allList = hrmsNewsalaryBasicColumnService.getAllList();
				if(CollUtil.isNotEmpty(allList)) {
					allList = allList.stream().filter(vo -> record.getBasicItemIds().contains(vo.getId())).collect(Collectors.toList());
				}
			}
			Cell cell = null;
			String clName = "";
			List<String> defaultItem = Arrays.asList(new String[]{"姓名", "工号", "薪酬组","政策标准"});
			for (HrmsNewsalaryBasicColumn basicColumn : allList) {
				if(CollUtil.isEmpty(record.getBasicItemIds())) {
					// 移除岗位工资和薪级工资
					if (!"岗位工资".equals(basicColumn.getBasicItemName())
							&& !"薪级工资".equals(basicColumn.getBasicItemName())) {
						cell = row1.createCell(index);
						cell.setCellStyle(textStyle);
						cell.setCellValue(basicColumn.getBasicItemName());
						row0.createCell(index).setCellStyle(textStyle);
						index++;
					}
				}else{
					//排除默认去掉的薪酬项
					if (!defaultItem.contains(basicColumn.getBasicItemName())) {
						cell = row1.createCell(index);
						cell.setCellStyle(textStyle);
						cell.setCellValue(basicColumn.getBasicItemName());
						row0.createCell(index).setCellStyle(textStyle);
						index++;
					}
				}
				if("2".equals(basicColumn.getBasicItemType())){ //如果是薪酬项，则添加薪酬项对应的百分比
					cell = row1.createCell(index);
					cell.setCellStyle(textStyle);
					cell.setCellValue(basicColumn.getBasicItemName()+"-百分比");
					row0.createCell(index).setCellStyle(textStyle);
					index++;
				}
			}

			List<CellRangeAddress> mergedRegions = new ArrayList<>();
			for (int i = 0; i < sheet1.getNumMergedRegions(); i++) {
				CellRangeAddress mergedRegion = sheet1.getMergedRegion(i);
				if ("A1:M1".equals(mergedRegion.formatAsString()) || "A1:G1".equals(mergedRegion.formatAsString())) {
					// 如果找到A1:J1的合并区域，则移除它
					sheet1.removeMergedRegion(i + 3);
				}
			}

			// 把姓名工号带出来
			Page page = new Page();
			page.setPageNo(1);
			page.setPageSize(Integer.MAX_VALUE);
			List<Map<String, String>> empList = hrmsNewsalaryBasicColumnService.listTableData(page,record).getRows();
			Row _rowData = null;
			Cell _rowCell0 = null;
			Cell _rowCell1 = null;
			Cell _rowCell2 = null;
			Cell _rowCell3 = null;
			String titleValue= "";
			if (!empList.isEmpty()) {
				Map<String,HrmsNewsalaryBasicColumn> basicItemTile = allList.stream().collect(Collectors.toMap(HrmsNewsalaryBasicColumn::getBasicItemName,Function.identity(), (entity1, entity2) -> entity1));
				HrmsNewsalaryBasicColumn basicColumn = null;
				if(CollUtil.isNotEmpty(record.getBasicItemIds())) {
					for (int i = 0; i < empList.size(); i++) {
						_rowData = sheet1.createRow(i + 2);
						for (int colIndx = 0; colIndx < index; colIndx++) {
							//获取标题列表
							titleValue = row1.getCell(colIndx).getStringCellValue();
                            _rowCell0 = _rowData.createCell(colIndx);
							_rowCell0.setCellStyle(textStyle);

							if("序号".equals(titleValue)){
								_rowCell0.setCellValue(i + 1);
							}else if("工号".equals(titleValue)){
								_rowCell0.setCellValue(empList.get(i).get("employee_no"));
							}else if("姓名".equals(titleValue)){
								_rowCell0.setCellValue(empList.get(i).get("employee_name"));
							}else if("薪酬组".equals(titleValue)){
								_rowCell0.setCellValue(empList.get(i).get("option_name"));
							}else if("员工状态".equals(titleValue)){
								_rowCell0.setCellValue(empList.get(i).get("employee_status"));
							}else if("编制类型".equals(titleValue)){
								_rowCell0.setCellValue(empList.get(i).get("establishment_type"));
							}else if("岗位名称".equals(titleValue)){
								_rowCell0.setCellValue(empList.get(i).get("personal_identity"));
							}else if("身份证号".equals(titleValue)){
								_rowCell0.setCellValue(empList.get(i).get("identity_number"));
							}else if("部门".equals(titleValue)){
								_rowCell0.setCellValue(empList.get(i).get("org_name"));
							}else{
								basicColumn = basicItemTile.get(titleValue);
								if(null != basicColumn) {
									_rowCell0.setCellValue("");
									if("3".equals(basicColumn.getBasicItemType())){
										_rowCell0.setCellValue(empList.get(i).get(basicColumn.getId()+"-percent"));
									}
									if("1".equals(basicColumn.getBasicItemType())){
										_rowCell0.setCellValue(empList.get(i).get(basicColumn.getId()));
									}
								}
								if(titleValue.indexOf("-百分比")>-1){
									basicColumn = basicItemTile.get(titleValue.replace("-百分比",""));
									if(null != basicColumn) {
										_rowCell0.setCellValue(empList.get(i).get(basicColumn.getId()+"-percent"));
									}
								}
							}
						}
					}
				}else {
					for (int i = 0; i < empList.size(); i++) {
						_rowData = sheet1.createRow(i + 2);
						_rowCell0 = _rowData.createCell(0);// 序号
						_rowCell1 = _rowData.createCell(1);// 工号
						_rowCell2 = _rowData.createCell(2);// 姓名
						_rowCell3 = _rowData.createCell(3); //薪酬组

						_rowCell0.setCellStyle(textStyle);
						_rowCell0.setCellValue(i + 1);

						_rowCell1.setCellStyle(textStyle);
						_rowCell1.setCellValue(empList.get(i).get("employee_no"));

						_rowCell2.setCellStyle(textStyle);
						_rowCell2.setCellValue(empList.get(i).get("employee_name"));

						_rowCell3.setCellStyle(textStyle);
						_rowCell3.setCellValue(empList.get(i).get("option_name"));
					}
				}
			}

			sheet1.addMergedRegion(new CellRangeAddress(0, 0, 0, index - 1));
			String encodeName = URLEncoder.encode("定薪调薪模版.xlsx", StandardCharsets.UTF_8.toString());
			// 设置响应头信息
			response.setContentType("application/vnd.ms-excel");
			response.setHeader("Content-Disposition",
					"attachment; filename=\"" + encodeName + "\"; filename*=utf-8''" + encodeName);

			// 将Excel文档写入响应流中
			ServletOutputStream outputStream = response.getOutputStream();
			workbook.write(outputStream);
			outputStream.flush();
			outputStream.close();
		} catch (Exception e) {
			logger.error("导出模版异常" + e.getMessage(), e);
		}
	}

	// 导入定薪调薪数据
	@SuppressWarnings("deprecation")
	@ApiOperation(value = "导入入职定薪模版数据", notes = "导入入职定薪模版数据")
	@PostMapping("/api/salaryBasicColumn/importEntryTemplateData")
	public PlatformResult<String> importEntryTemplateData(@RequestParam("file") MultipartFile file) {
		try {
			Workbook workbook = WorkbookFactory.create(file.getInputStream());
			Sheet sheet = workbook.getSheetAt(0);
			List<Map<String, String>> data = new ArrayList<>();
			for (Row row : sheet) { // 遍历每一行
				Map<String, String> rowData = new HashMap<>(); // 创建一个 Map 来存储每一行的值
				for (Cell cell : row) { // 遍历每一列
					// 获取列名和值
					String value = "";
					String columnName = sheet.getRow(1).getCell(cell.getColumnIndex()).getStringCellValue(); // 获取第一列表头
					if (StringUtils.isAllEmpty(columnName)) {
						return PlatformResult.failure("导入excel出错: Excel模板中存在多余的列，请检查");
					}
					if (cell.getCellType() == CellType.FORMULA.getCode()) {
						System.out.println("此列为公式");
						FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
						CellValue cellValue = evaluator.evaluate(cell);
						switch (cellValue.getCellType()) {
						case 0:
							System.out.println("公式计算结果为：" + cellValue.getNumberValue());
							value = String.valueOf(cellValue.getNumberValue());
							break;
						case 1:
							System.out.println("公式计算结果为：" + cellValue.getStringValue());
							value = cellValue.getStringValue();
							break;
						case 4:
							System.out.println("公式计算结果为：" + cellValue.getBooleanValue());
							value = (cellValue.getBooleanValue())? "是":"否";
							break;
						case 5:
							System.out.println("公式计算结果为：计算错误");
							value = "";
							break;
						default:
							System.out.println("未知类型");
							value = "";
						}
						
					} else {
						value = cell.toString();
					}
					if (!StringUtil.isEmpty(columnName) && !StringUtil.isEmpty(value)) {
						if ("工号".equals(columnName.trim())) {
							rowData.put(columnName.trim(), value.replaceAll("\\.0+|\\.00", "").trim());
						} else {
							rowData.put(columnName.trim(), value.trim());
						}
					}
				}
				if (!StringUtil.isEmpty(rowData.get("工号"))) { // 工号不为空 就插入数据
					data.add(rowData);
				}
			}
			Integer size = hrmsNewsalaryBasicitemEmpService.importEntryTemplateData(data,true);
			return PlatformResult.success("导入完成 " + size + " 条数据");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure("导入excel出错:" + e.getMessage());
		}
	}

	// 导入定薪调薪数据
//	@Deprecated
	@ApiOperation(value = "导入批量调薪模版数据", notes = "导入批量调薪模版数据")
	@PostMapping("/api/salaryBasicColumn/importUpdateEntryTemplateData")
	public PlatformResult<String> importUpdateEntryTemplateData(@RequestParam("file") MultipartFile file) {
		try {
			Workbook workbook = WorkbookFactory.create(file.getInputStream());
			Sheet sheet = workbook.getSheetAt(0);
			List<Map<String, String>> data = new ArrayList<>();
			for (Row row : sheet) { // 遍历每一行
				Map<String, String> rowData = new HashMap<>(); // 创建一个 Map 来存储每一行的值
				for (Cell cell : row) { // 遍历每一列
					// 获取列名和值
					String columnName = sheet.getRow(1).getCell(cell.getColumnIndex()).getStringCellValue(); // 获取第一列表头
					String value = cell.toString();
					if (!StringUtil.isEmpty(columnName) && !StringUtil.isEmpty(value)) {
						if ("工号".equals(columnName.trim())) {
							rowData.put(columnName.trim(), value.replaceAll("\\.0+|\\.00", "").trim());
						} else {
							rowData.put(columnName.trim(), value.trim());
						}
					}
				}
				if (!StringUtil.isEmpty(rowData.get("工号"))) { // 工号不为空 就插入数据
					data.add(rowData);
				}
			}
//			Integer size = hrmsNewsalaryBasicitemEmpService.importUpdateEntryTemplateData(data);
			Integer size = hrmsNewsalaryBasicitemEmpService.importEntryTemplateData(data,false);
			return PlatformResult.success("导入完成 " + size + " 人");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure("导入excel出错" + e.getMessage());
		}
	}

	// 薪酬档案导出
	@ApiOperation(value = "薪酬档案导出", notes = "薪酬档案导出")
	@GetMapping("/api/salaryBasicColumn/export")
	public void exportStatementAllList(HttpServletRequest request, HttpServletResponse response, Page page,
			SearchListTable record) {
		page.setPageSize(Integer.MAX_VALUE);
		String filename = "员工定薪记录";
		List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
		List<VueTableEntity> listTitle = hrmsNewsalaryBasicColumnService.listTableTitle();
		DataSet<Map<String, String>> retData = hrmsNewsalaryBasicColumnService.listTableData(page, record);
		List<Map<String, String>> rows = retData.getRows();
		// 时间格式转换
		/*
		 * if (CollUtil.isNotEmpty(rows)) { rows.forEach(item -> { LocalDateTime
		 * updateDate = LocalDateTime.parse(MapUtil.getStr(item, "update_date")); String
		 * formatDate =
		 * updateDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
		 * item.put("update_date", formatDate); }); }
		 */
		String title = null;
		for (VueTableEntity vueTableEntity : listTitle) {
		    title = vueTableEntity.getLabel();
			ExcelExportEntity eee = new  ExcelExportEntity(title, vueTableEntity.getProp());
			if (vueTableEntity.getIsNum()) {
				eee.setType(10);
			}
			colList.add(eee);
		}
		try {
			ExportParams exportParams =new ExportParams(filename, "员工定薪数据", ExcelType.XSSF);
			exportParams.setStyle(ExcelStyleUtil.class);
			Workbook workbook = ExcelExportUtil.exportExcel(exportParams,colList, rows);
			ExcelExportUtils.doubleStypeBug(workbook);  //修正值为空时，显示-1的bug;
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-disposition",
					"attachment; filename=" + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
			OutputStream fos = response.getOutputStream();
			workbook.write(fos);
			fos.close();
		} catch (Exception e) {
			logger.error("导出数据异常" + e.getMessage());
		}
	}
	
	@ApiOperation(value = "薪酬档案基本项-工资项", notes = "薪酬档案基本项-工资项")
	@GetMapping("/api/salaryBasicColumn/getBasicSalary")
	public PlatformResult<List<HrmsNewsalaryBasicColumn>> getBasicSalary() {
		try {
			List<HrmsNewsalaryBasicColumn> list = hrmsNewsalaryBasicColumnService.getBasicSalary();
			return PlatformResult.success(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "查询未加入薪酬组人员", notes = "查询未加入薪酬组人员")
	@GetMapping("/api/salaryBasicColumn/getUnoption")
	public PlatformResult<Map<String, Object>> getUnoption() {
		Map<String, Object> map = hrmsNewsalaryBasicColumnService.getUnoption();
		return PlatformResult.success(map);
	}

	@ApiOperation(value = "批量绑定薪酬组", notes = "批量绑定薪酬组")
	@PostMapping("/api/salaryBasicColumn/batchBindOption")
	public PlatformResult batchBindOption(@RequestBody HrmsNewsalaryOptionEmp hrmsNewsalaryOptionEmp) {
		try {

			if (hrmsNewsalaryOptionEmp.getOptionId() == null) {
				return PlatformResult.failure("请选择一个薪酬组");
			}

			if (hrmsNewsalaryOptionEmp.getEmployeeId() == null) {
				return PlatformResult.failure("请至少选择一个人员");
			}

			if (CommonUtils.stringToDate(hrmsNewsalaryOptionEmp.getEffectiveDate()) == null) {
				return PlatformResult.failure("生效时间格式不正确");
			}

			// 更新绑定关系
			hrmsNewsalaryBasicColumnService.batchBindOption(hrmsNewsalaryOptionEmp);

		} catch (Exception e) {
			return PlatformResult.failure(e.getMessage());
		}
		return PlatformResult.success();
	}

	@ApiOperation(value = "员工工资台账数据", notes = "薪酬员工工资台账数据")
	@GetMapping("/api/salary/listSalaryLedgerData")
	public DataSet<SalaryLedgerListVo> listSalaryLedgerData(Page page, SearchSalaryLedgerVo record) {
		return hrmsNewsalaryBasicColumnService.listSalaryLedgerData(page, record);
	}

	/**
	 *
	 * @Title: selectEmployeeList
	 * @Description: TODO(描述这个方法的作用)
	 * @param: @return
	 * @return: PlatformResult<List<Employee>>
	 * @author: YueC
	 * @date:   2021年8月17日 下午3:55:36
	 * @throws
	 */
	@ApiOperation(value = "提供给外部系统使用的员工薪酬基本数据", notes = "提供给外部系统使用的员工薪酬基本数据")
	@GetMapping("/salary/api/getEmployeBaseSalaryItemList")
	public PlatformResult getEmployeBaseSalaryItemList(HttpServletRequest request,RequestContent requestContent,Page page) {
		if(page == null){
			page = new Page();
			page.setPageSize(100);
			page.setPageNo(1);
		}
		String clientIP = ServletUtil.getClientIP(request, null);
		requestContent.setIpAddress(clientIP);
		try {
			Map<String, Object> result = hrmsNewsalaryBasicColumnService.findEmployeBaseSalaryItemList(page,requestContent);
			return PlatformResult.success(result);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
		}
	}
}

package cn.trasen.hrms.salary.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicItem;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalaryBasicItemController
 * @Description 薪酬项目基础库
 * @date 2023��11��11�� ����4:32:00
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "薪酬项目基础库Controller")
public class HrmsNewsalaryBasicItemController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryBasicItemController.class);

	@Autowired
	private HrmsNewsalaryBasicItemService hrmsNewsalaryBasicItemService;

	/**
	 * @Title saveHrmsNewsalaryBasicItem
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:32:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/salaryBasicItem/save")
	public PlatformResult<String> saveHrmsNewsalaryBasicItem(@RequestBody HrmsNewsalaryBasicItem record) {
		try {
			hrmsNewsalaryBasicItemService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryBasicItem
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:32:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryBasicItem/update")
	public PlatformResult<String> updateHrmsNewsalaryBasicItem(@RequestBody HrmsNewsalaryBasicItem record) {
		try {
			hrmsNewsalaryBasicItemService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryBasicItemById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryBasicItem>
	 * @date 2023��11��11�� ����4:32:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/salaryBasicItem/{id}")
	public PlatformResult<HrmsNewsalaryBasicItem> selectHrmsNewsalaryBasicItemById(@PathVariable String id) {
		try {
			HrmsNewsalaryBasicItem record = hrmsNewsalaryBasicItemService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryBasicItemById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:32:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/salaryBasicItem/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryBasicItemById(@PathVariable String id) {
		try {
			hrmsNewsalaryBasicItemService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryBasicItemList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryBasicItem>
	 * @date 2023��11��11�� ����4:32:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/salaryBasicItem/list")
	public DataSet<HrmsNewsalaryBasicItem> selectHrmsNewsalaryBasicItemList(Page page, HrmsNewsalaryBasicItem record) {
		return hrmsNewsalaryBasicItemService.getDataSetList(page, record);
	}



}

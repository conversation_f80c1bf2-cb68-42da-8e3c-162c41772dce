package cn.trasen.hrms.salary.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.fasterxml.jackson.databind.ObjectMapper;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.DTO.CheckPersonnel;
import cn.trasen.hrms.salary.DTO.SearchListTable;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryFirststepHistoryMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryFirststepHistory;
import cn.trasen.hrms.salary.service.HrmsNewsalaryFirststepHistoryService;
import cn.trasen.hrms.utils.IdUtil;
import lombok.extern.log4j.Log4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsNewsalaryFirststepHistoryServiceImpl
 * @Description TODO
 * @date 2024��3��11�� ����5:03:18
 * <AUTHOR>
 * @version 1.0
 */
@Log4j
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryFirststepHistoryServiceImpl implements HrmsNewsalaryFirststepHistoryService {

	@Autowired
	private HrmsNewsalaryFirststepHistoryMapper mapper;

//	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryFirststepHistory record) {
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryFirststepHistory record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryFirststepHistory record = new HrmsNewsalaryFirststepHistory();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalaryFirststepHistory selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalaryFirststepHistory> getDataSetList(Page page, HrmsNewsalaryFirststepHistory record) {
		Example example = new Example(HrmsNewsalaryFirststepHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalaryFirststepHistory> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public Integer deleteByOptionIdAndDate(String optionId, String computeDate) {
		Example example = new Example(HrmsNewsalaryFirststepHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("payrollDate", computeDate);
		criteria.andEqualTo("optionId", optionId);
		return mapper.deleteByExample(example);
	}

	@Override
	public List<CheckPersonnel> getHistoryData(Page page, SearchListTable record) {
		//获取数据
		List<HrmsNewsalaryFirststepHistory> list = mapper.getHistoryData(page,record);
		//拿到json转对象
		List<CheckPersonnel> retList = null;
		try {
			if(!list.isEmpty()){
				retList = new ArrayList<>();
				for (int i = 0; i < list.size(); i++) {
					ObjectMapper objectMapper = new ObjectMapper();
					CheckPersonnel bean = objectMapper.readValue(list.get(i).getVal(), CheckPersonnel.class);
					retList.add(bean);
				}
			}
		}catch (Exception e){
			log.error("转换出错了 ！！！！！！！！！！！！");
			log.error(e.getMessage(),e);
		}

		return retList;
	}
}

package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.enums.EmployeeTemporaryOpTypeEnum;
import cn.trasen.hrms.salary.enums.NewsalaryTemporaryAdjustOpTypeEnum;
import cn.trasen.hrms.salary.model.HrmsEmployeeTemporary;
import cn.trasen.hrms.salary.model.HrmsNewsalaryTemporaryAdjust;
import cn.trasen.hrms.salary.model.HrmsNewsalaryTemporaryAdjustChange;

/**
 * @ClassName HrmsNewsalaryTemporaryAdjustChangeService
 * @Description TODO
 * @date 2024��10��8�� ����3:13:33
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryTemporaryAdjustChangeService {

	/**
	 * @Title saveChanges
	 * @Description 保存操作记录
	 * @param oldObj 原数据
	 * @param newObj 新数据
	 * @param opTypeEnum 操作类型
	 * @return Integer
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	Integer saveChanges(HrmsNewsalaryTemporaryAdjust oldObj, HrmsNewsalaryTemporaryAdjust newObj, NewsalaryTemporaryAdjustOpTypeEnum opTypeEnum);

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��10��8�� ����3:13:33
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryTemporaryAdjustChange record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��10��8�� ����3:13:33
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryTemporaryAdjustChange record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��10��8�� ����3:13:33
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryTemporaryAdjustChange
	 * @date 2024��10��8�� ����3:13:33
	 * <AUTHOR>
	 */
	HrmsNewsalaryTemporaryAdjustChange selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryTemporaryAdjustChange>
	 * @date 2024��10��8�� ����3:13:33
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryTemporaryAdjustChange> getDataSetList(Page page, HrmsNewsalaryTemporaryAdjustChange record);
}

package cn.trasen.hrms.salary.service.impl;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.enums.SalaryCategoryStatusEnum;
import cn.trasen.hrms.enums.SalarySoureEnum;
import cn.trasen.hrms.model.HrmsAdvancementIncidentEo;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryBasicitemEmpMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryChangesDetailedMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryChangesDetailedEo;
import cn.trasen.hrms.salary.service.HrmsNewsalaryChangesDetailedService;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.utils.IdUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
*
*/
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryChangesDetailedServiceImpl implements HrmsNewsalaryChangesDetailedService {

    @Resource
    private HrmsNewsalaryChangesDetailedMapper hrmsNewsalaryChangesDetailedMapper;
    @Resource
    DictItemFeignService dictItemFeignService;
    @Autowired
    private HrmsDictInfoService hrmsDictInfoService;
    @Resource
    private HrmsNewsalaryBasicitemEmpMapper empMapper;

    @Override
    public String queryOrgName(String employeeId) {
        return hrmsNewsalaryChangesDetailedMapper.queryOrgName(employeeId);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(HrmsNewsalaryChangesDetailedEo hrmsNewsalaryChangesDetailedEo) {
         hrmsNewsalaryChangesDetailedMapper.insert(hrmsNewsalaryChangesDetailedEo);
    }

    @Override
    public List<HrmsNewsalaryChangesDetailedEo> getDataList(Page page, HrmsNewsalaryChangesDetailedEo entity) {

        Map<String, String> postCategoryDictMap = convertDictMap("post_category"); // 岗位类别
        Map<String, String> salaryLevelCategoryDictMap = convertDictMap("salary_level_category"); // 薪级类别
        Map<String, String> establishmentTypeMap = hrmsDictInfoService.convertDictMap(DictContants.ESTABLISHMENT_TYPE); //编制类型
        List<HrmsNewsalaryChangesDetailedEo> list = hrmsNewsalaryChangesDetailedMapper.getDataList(entity, page);
        list.forEach(hrmsNewsalaryChangesDetailedEo -> {
            //薪酬类别
            if (Objects.equals(SalaryCategoryStatusEnum.CONTRACT_STATUS_3.getKey(),hrmsNewsalaryChangesDetailedEo.getSalaryCategory())){
                hrmsNewsalaryChangesDetailedEo.setSalaryCategoryText(SalaryCategoryStatusEnum.CONTRACT_STATUS_3.getVal());
            }
            if (Objects.equals("岗位等级",hrmsNewsalaryChangesDetailedEo.getSalaryName())){
                hrmsNewsalaryChangesDetailedEo.setNowValue(empMapper.getGwdjText(hrmsNewsalaryChangesDetailedEo.getNowValue()));
                hrmsNewsalaryChangesDetailedEo.setAdjustValue(empMapper.getGwdjText(hrmsNewsalaryChangesDetailedEo.getAdjustValue()));
            }
            if (Objects.equals("岗位类别",hrmsNewsalaryChangesDetailedEo.getSalaryName())){
                hrmsNewsalaryChangesDetailedEo.setNowValue(postCategoryDictMap.get(hrmsNewsalaryChangesDetailedEo.getNowValue()));
                hrmsNewsalaryChangesDetailedEo.setAdjustValue(postCategoryDictMap.get(hrmsNewsalaryChangesDetailedEo.getAdjustValue()));
            }
            if (Objects.equals("薪级等级",hrmsNewsalaryChangesDetailedEo.getSalaryName())){
                hrmsNewsalaryChangesDetailedEo.setNowValue( empMapper.getSalaryLeavelText(hrmsNewsalaryChangesDetailedEo.getNowValue()));
                hrmsNewsalaryChangesDetailedEo.setAdjustValue( empMapper.getSalaryLeavelText(hrmsNewsalaryChangesDetailedEo.getAdjustValue()));
            }
            if (Objects.equals("薪级类别",hrmsNewsalaryChangesDetailedEo.getSalaryName())){
                hrmsNewsalaryChangesDetailedEo.setNowValue(salaryLevelCategoryDictMap.get(hrmsNewsalaryChangesDetailedEo.getNowValue()));
                hrmsNewsalaryChangesDetailedEo.setAdjustValue(salaryLevelCategoryDictMap.get(hrmsNewsalaryChangesDetailedEo.getAdjustValue()));
            }
            hrmsNewsalaryChangesDetailedEo.setEstablishmentTypeText(establishmentTypeMap.get(hrmsNewsalaryChangesDetailedEo.getEstablishmentType()));
            hrmsNewsalaryChangesDetailedEo.setSourceTypeText(SalarySoureEnum.getValByKey(hrmsNewsalaryChangesDetailedEo.getSourceType()));
        });
        return list;
    }

    @Override
    public List<HrmsNewsalaryChangesDetailedEo> queryCancelList(HrmsAdvancementIncidentEo entity) {
        Example example = new Example(HrmsNewsalaryChangesDetailedEo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("employeeId",entity.getEmployeeId());
        List<String> categoryList = Arrays.asList("1", "2");
        criteria.andIn("salaryCategory",categoryList);
        return hrmsNewsalaryChangesDetailedMapper.selectByExample(example);
    }

    @Override
    public void update(HrmsNewsalaryChangesDetailedEo hrmsNewsalaryChangesDetailedEo) {
        hrmsNewsalaryChangesDetailedEo.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        hrmsNewsalaryChangesDetailedEo.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        hrmsNewsalaryChangesDetailedEo.setUpdateDate(new Date());
        hrmsNewsalaryChangesDetailedMapper.updateByPrimaryKeySelective(hrmsNewsalaryChangesDetailedEo);
    }

    /**
     * 更新本次异动记录对应的值
     * @param employee
     * @param basicItemId
     * @param effectiveDate
     * @param salaryAmount
     * @return
     */
    @Override
    @Transactional(readOnly = false)
    public void updateNowValueByEffective(HrmsEmployee employee, String basicItemId, String effectiveDate,BigDecimal oldSalaryAmount, BigDecimal salaryAmount, String reason){
        Example example = new Example(HrmsNewsalaryChangesDetailedEo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("employeeId", employee.getEmployeeId());
        criteria.andEqualTo("salaryCategory", "3");
        criteria.andEqualTo("abnormalItems",basicItemId);
        criteria.andEqualTo("effectiveDate", effectiveDate);
        example.setOrderByClause("create_date desc");
        List<HrmsNewsalaryChangesDetailedEo> list = hrmsNewsalaryChangesDetailedMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(list)) {
            HrmsNewsalaryChangesDetailedEo detailedEo =list.get(0);
            detailedEo.setAdjustValue(oldSalaryAmount.toString());
            detailedEo.setNowValue(salaryAmount.toString());
            hrmsNewsalaryChangesDetailedMapper.updateByPrimaryKeySelective(detailedEo);
        }else{
            if(salaryAmount.compareTo(BigDecimal.ZERO)<=0){
                return ;
            }
            HrmsNewsalaryChangesDetailedEo hrmsNewsalaryChangesDetailedEo = new HrmsNewsalaryChangesDetailedEo();
            hrmsNewsalaryChangesDetailedEo.setEmployeeId( employee.getEmployeeId());
            hrmsNewsalaryChangesDetailedEo.setAdjustValue(oldSalaryAmount.toString());
            hrmsNewsalaryChangesDetailedEo.setNowValue(salaryAmount.toString());
            hrmsNewsalaryChangesDetailedEo.setAbnormalItems(basicItemId);
            hrmsNewsalaryChangesDetailedEo.setSalaryCategory(3);
            hrmsNewsalaryChangesDetailedEo.setId(IdUtil.getId());
            hrmsNewsalaryChangesDetailedEo.setCreateDate(new Date());
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user != null) {
                hrmsNewsalaryChangesDetailedEo.setCreateUser(user.getUsercode());
                hrmsNewsalaryChangesDetailedEo.setCreateUserName(user.getUsername());
                hrmsNewsalaryChangesDetailedEo.setUpdateUser(user.getUsercode());
                hrmsNewsalaryChangesDetailedEo.setUpdateUserName(user.getUsername());
            }
            hrmsNewsalaryChangesDetailedEo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            hrmsNewsalaryChangesDetailedEo.setOrgName(queryOrgName( employee.getEmployeeId()));
            hrmsNewsalaryChangesDetailedEo.setEffectiveDate(effectiveDate);
            hrmsNewsalaryChangesDetailedEo.setReason(reason);
            hrmsNewsalaryChangesDetailedEo.setEmployeeName( employee.getEmployeeName());
            hrmsNewsalaryChangesDetailedEo.setEmployeeNo(employee.getEmployeeNo());
            hrmsNewsalaryChangesDetailedEo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            hrmsNewsalaryChangesDetailedEo.setSourceType(2);
            hrmsNewsalaryChangesDetailedEo.setIsDeleted(Contants.IS_DELETED_FALSE);
            save(hrmsNewsalaryChangesDetailedEo);
        }
    }

    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }
}

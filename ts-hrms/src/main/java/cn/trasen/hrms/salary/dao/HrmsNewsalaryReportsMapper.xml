<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryReportsMapper">

    <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryReportsEo">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="reportName" column="report_name" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="VARCHAR"/>
            <result property="ssoOrgCode" column="sso_org_code" jdbcType="VARCHAR"/>
            <result property="ssoOrgName" column="sso_org_name" jdbcType="VARCHAR"/>
            <result property="reportsType" column="reports_type" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,report_name,remark,
        create_user,create_user_name,create_date,
        update_user,update_user_name,update_date,
        is_deleted,sso_org_code,sso_org_name,reports_type
    </sql>
    <select id="getDataSetList" resultType="cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportsVo">
        select
        <include refid="Base_Column_List" />
        from hrms_newsalary_reports where
        is_deleted = 'N'
        <if test="reportsVo.reportName != null and reportsVo.reportName != ''">
            and report_name like CONCAT('%', #{reportsVo.reportName}, '%')
        </if>
        <if test="reportsVo.reportsType != null and reportsVo.reportsType != ''">
            and reports_type = #{reportsVo.reportsType}
        </if>
    </select>

</mapper>

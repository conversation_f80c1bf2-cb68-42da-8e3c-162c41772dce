package cn.trasen.hrms.salary.controller;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.enums.EmployeeTemporaryOpTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.hrms.salary.model.HrmsEmployeeTemporaryChange;
import cn.trasen.hrms.salary.service.HrmsEmployeeTemporaryChangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.*;

/**
 * @ClassName HrmsEmployeeTemporaryChangeController
 * @Description TODO
 * @date 2024��10��8�� ����3:12:57
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "临时员工操作记录Controller")
public class HrmsEmployeeTemporaryChangeController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsEmployeeTemporaryChangeController.class);

	@Autowired
	private HrmsEmployeeTemporaryChangeService hrmsEmployeeTemporaryChangeService;

	/**
	 * @Title saveHrmsEmployeeTemporaryChange
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/employeeTemporaryChange/save")
	public PlatformResult<String> saveHrmsEmployeeTemporaryChange(@RequestBody HrmsEmployeeTemporaryChange record) {
		try {
			hrmsEmployeeTemporaryChangeService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsEmployeeTemporaryChange
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/employeeTemporaryChange/update")
	public PlatformResult<String> updateHrmsEmployeeTemporaryChange(@RequestBody HrmsEmployeeTemporaryChange record) {
		try {
			hrmsEmployeeTemporaryChangeService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsEmployeeTemporaryChangeById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsEmployeeTemporaryChange>
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/employeeTemporaryChange/{id}")
	public PlatformResult<HrmsEmployeeTemporaryChange> selectHrmsEmployeeTemporaryChangeById(@PathVariable String id) {
		try {
			HrmsEmployeeTemporaryChange record = hrmsEmployeeTemporaryChangeService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsEmployeeTemporaryChangeById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/employeeTemporaryChange/delete/{id}")
	public PlatformResult<String> deleteHrmsEmployeeTemporaryChangeById(@PathVariable String id) {
		try {
			hrmsEmployeeTemporaryChangeService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsEmployeeTemporaryChangeList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsEmployeeTemporaryChange>
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/employeeTemporaryChange/list")
	public DataSet<HrmsEmployeeTemporaryChange> selectHrmsEmployeeTemporaryChangeList(Page page, HrmsEmployeeTemporaryChange record) {
		return hrmsEmployeeTemporaryChangeService.getDataSetList(page, record);
	}

	/**
	 * @Title selectHrmsEmployeeTemporaryChangeList
	 * @Description 查询操作类别列表
	 * @return DataSet<HrmsEmployeeTemporaryChange>
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "操作类型", notes = "操作类型")
	@GetMapping("/api/employeeTemporaryChange/getOpTypeDatas")
	public PlatformResult getOpTypeDatas() {
		EmployeeTemporaryOpTypeEnum[] enums = EmployeeTemporaryOpTypeEnum.values();
		Map<String,String> map = new HashMap<>();
		List<Map<String,String>> list = new ArrayList<>(enums.length);
		for(EmployeeTemporaryOpTypeEnum en : enums){
			map = new HashMap<>();
			map.put("key",en.getKey());
			map.put("val",en.getVal());
			list.add(map);
		}
		return PlatformResult.success(list);
	}
}

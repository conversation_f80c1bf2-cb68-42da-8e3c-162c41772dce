<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryItemMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="item_type" jdbcType="VARCHAR" property="itemType" />
    <result column="item_digit" jdbcType="CHAR" property="itemDigit" />
    <result column="count_type" jdbcType="CHAR" property="countType" />
    <result column="item_rule" jdbcType="CHAR" property="itemRule" />
    <result column="count_formula" jdbcType="VARCHAR" property="countFormula" />
    <result column="count_formula_text" jdbcType="VARCHAR" property="countFormulaText" />
    <result column="salary_item_amount" jdbcType="DECIMAL" property="salaryItemAmount" />
    <result column="salary_remark" jdbcType="VARCHAR" property="salaryRemark" />
    <result column="is_hidden" jdbcType="VARCHAR" property="isHidden" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>

    <!-- 获取所有方案非重复薪酬项列表 -->
  <select id="getAllOptionSalaryItem" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
    select item_name,id as item_id from hrms_newsalary_item where is_enable = 1 and is_deleted='N'
    and sso_org_code = #{ssoOrgCode}
    group by item_name,id
  </select>

  <select id="getByOption" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
    SELECT t2.* FROM hrms_newsalary_item_group t1
     INNER JOIN hrms_newsalary_item t2 ON t1.id=t2.group_id
    WHERE t1.is_deleted='N' AND t2.is_deleted = 'N' AND t1.option_id=#{optionId} 
    ORDER BY t1.seq_no,t2.sort_no,t2.id
  </select>

  <!-- 显示方案对应的当前所有薪酬项目 -->
  <select id="getItemByOptionId" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
    SELECT t2.* FROM hrms_newsalary_item_group t1
     LEFT JOIN hrms_newsalary_item  t2 ON t1.id=t2.group_id
    WHERE t1.is_deleted='N' AND t2.is_deleted='N' AND t1.option_id=#{optionId} and t2.is_enable='1' 
    ORDER BY t1.seq_no , t2.sort_no,t2.id
  </select>
  <select id="getItemByTitlesbyId" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
    SELECT t2.id as item_id,t2.item_name  FROM hrms_newsalary_item_group t1
     LEFT JOIN hrms_newsalary_item  t2 ON t1.id=t2.group_id
    WHERE t1.is_deleted='N' AND t2.is_deleted='N' AND t1.option_id=#{optionId}
    ORDER BY t1.seq_no , t2.sort_no,t2.id
  </select>  
	<!-- 增加取方案自己与非自己的项目字段，方便公式计算 add by lijr#trasen 24/6/16-->
	<select id="getItemByOptionIdAll" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
	<!-- select id,item_code,item_name,basic_item_id,item_type,next_month,library_type,carry_rule,item_digit,item_rule,count_type,remark,count_formula,
		salary_item_amount, custom_rule
	from hrms_newsalary_item_library a where id not in (
		select id from hrms_newsalary_item b where option_id = #{optionId}) 
	union all 
	select id,item_code,item_name,basic_item_id,item_type,next_month,library_type,carry_rule,item_digit,item_rule,count_type,remark,count_formula,
		salary_item_amount, custom_rule 
	from hrms_newsalary_item where option_id = #{optionId} 
	order by item_rule, library_type  -->
    select * FROM (
	    select id,item_code,item_name,basic_item_id,item_type,next_month,library_type,carry_rule,item_digit,item_rule,count_type,remark,count_formula,
			salary_item_amount, custom_rule, 0 as group_id ,sort_no
		from hrms_newsalary_item_library a where id not in (
			select id from hrms_newsalary_item b) and is_deleted = 'N' and status='1'
		union all 
		select id,item_code,item_name,basic_item_id,item_type,next_month,library_type,carry_rule,item_digit,item_rule,count_type,remark,count_formula,
			salary_item_amount, custom_rule,group_id,sort_no
		from hrms_newsalary_item where option_id =  #{optionId} and is_deleted = 'N' and is_enable = '1' 
	) v left join hrms_newsalary_item_group a1  on v.group_id = a1.id
	order by a1.seq_no,v.sort_no,v.id
	<!-- SELECT t2.* FROM hrms_newsalary_item_group t1
     INNER JOIN hrms_newsalary_item t2 ON t1.id=t2.group_id
    WHERE t1.is_deleted='N' AND t2.is_deleted = 'N' AND t1.option_id=#{optionId}
    ORDER BY t1.seq_no,t2.sort_no,t2.id -->
	</select>
  <select id="getPayrollByEmployee" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
    SELECT distinct t1.payroll_date,
           t3.item_name,t2.salary,
           t1.employee_id, t1.option_id 
    FROM hrms_newsalary_payroll t1
           LEFT JOIN hrms_newsalary_payroll_detail t2 ON t1.id=t2.payroll_id
           LEFT JOIN hrms_newsalary_item t3 ON t2.item_id = t3.id
    WHERE  t1.is_deleted='N'
      AND t1.send_status= '1'
      AND t3.actual_salary = '1'
      AND t1.employee_id = #{employeeId}
    <if test="payrollDate != null and payrollDate != ''">
        and t1.payroll_date like CONCAT('%',#{payrollDate},'%')
    </if>
  </select>

  <select id="getPayrollDetailsTop" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">

    SELECT
      t4.employee_id,
      t4.employee_name,
      t4.`employee_no`,
      t5.name AS orgName,
      t6.`ITEM_NAME` AS personalIdentityText
    FROM (
      SELECT
			employee_name,employee_no,org_id,employee_id,is_deleted,personal_identity,'N' as is_temp
		FROM cust_emp_base
		UNION ALL
		SELECT
				employee_name,employee_no,org_id,id as employee_id,is_deleted,tmp_position as personal_identity,'Y' as is_temp
		FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
    )  t4
           LEFT JOIN comm_organization t5 ON t4.org_id=t5.organization_id
           LEFT JOIN (
              SELECT
              A.*
              FROM
              COMM_DICT_ITEM A
              LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
              WHERE
              B.TYPE_CODE = 'personal_identity'
              AND B.IS_DELETED = 'N'
              AND A.IS_DELETED = 'N'
              AND A.IS_ENABLE = '1'
              AND A.sso_org_code = #{ssoOrgCode}
           ) t6 ON t4.personal_identity = t6.item_code
    WHERE  t4.is_deleted='N'
      AND t4.employee_id =#{employeeId}


  </select>

  <select id="getPayrollByEmployeeDetails" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
      SELECT t1.payroll_date,
             t3.item_name,t2.salary,
             t1.employee_id,
             t3.actual_salary,t3.warm_reminder,t3.sort_no,
             t3.is_hidden,t3.salary_remark,
             t3.group_id,
             t3.custom_rule
      FROM hrms_newsalary_payroll t1
               LEFT JOIN hrms_newsalary_payroll_detail t2 ON t1.id=t2.payroll_id
               LEFT JOIN hrms_newsalary_item t3 ON t2.item_id = t3.id and t1.option_id = t3.option_id 
               LEFT JOIN hrms_newsalary_item_group t4 on t3.group_id = t4.id
      WHERE  t1.is_deleted='N'
       <!--  AND t1.send_status= '1' -->
        AND t4.is_deleted = 'N'
        AND t1.employee_id = #{employeeId}
        AND t3.is_article='1'
        AND t1.payroll_date= #{payrollDate}
    order by t4.seq_no,t3.sort_no,t3.id
  </select>

    <select id="salaryCountTitle" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
        SELECT distinct item_code,item_name FROM hrms_newsalary_item t1
        LEFT JOIN hrms_newsalary_item_group t2 on  t1.group_id = t2.id and t1.is_deleted=t2.is_deleted 
        WHERE t1.is_deleted='N' and t1.is_enable = 1 and t1.option_id = #{optionId}
        order by t2.seq_no,t1.sort_no 
    </select>

    <select id="getExportColumns" resultType="java.util.LinkedHashMap">
        SELECT item_code,item_name FROM hrms_newsalary_item
        WHERE is_deleted='N'  and option_id = #{optionId}
        ORDER BY group_id asc,sort_no ASC
    </select>

    <select id="checkUse" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
        SELECT * FROM hrms_newsalary_item
        WHERE is_deleted='N'
          AND ((
                   item_rule ='3' AND basic_item_id=#{id}
                   )OR(
                   item_rule='4' AND count_formula LIKE  CONCAT('%',#{id},'%')
               ))
    </select>

    <select id="getALLByMap" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
        SELECT option_id,id,item_name FROM hrms_newsalary_item
        WHERE is_deleted='N'
    </select>
    <update id="deleteByGroupId" parameterType="java.lang.String">
        update hrms_newsalary_item set is_deleted = 'Y' where group_id = #{groupId}
    </update>

    <select id="getManualByOptionId" parameterType="java.lang.String" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
    SELECT t2.* FROM hrms_newsalary_item_group t1
     LEFT JOIN hrms_newsalary_item  t2 ON t1.id=t2.group_id
    WHERE t1.is_deleted='N' AND t2.is_deleted='N' AND t1.option_id=#{optionId}
    AND t2.item_rule in ('1')  and t2.status = '1' and t2.is_enable = '1' 
    ORDER BY t1.seq_no,t2.sort_no
  </select>

    <select id="salaryTotalCountTitle" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryReportTotalEo">
         SELECT col_code,col_name FROM hrms_newsalary_report_total
        WHERE is_deleted='N'
        and report_id =#{reportId}  order by sort_no ASC
    </select>

    <select id="getTotalExportColumns" resultType="java.util.LinkedHashMap">
       SELECT col_code,col_name FROM hrms_newsalary_report_total
        WHERE is_deleted='N'
        and report_id =#{reportId}
    </select>
</mapper>
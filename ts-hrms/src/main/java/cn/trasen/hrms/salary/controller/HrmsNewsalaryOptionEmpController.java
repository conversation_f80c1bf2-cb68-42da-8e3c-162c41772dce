package cn.trasen.hrms.salary.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionEmpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * @ClassName HrmsNewsalaryOptionEmpController
 * @Description 薪酬方案人员关联表
 * @date 2023��11��11�� ����4:36:17
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "薪酬方案人员关联表Controller")
public class HrmsNewsalaryOptionEmpController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryOptionEmpController.class);

	@Autowired
	private HrmsNewsalaryOptionEmpService hrmsNewsalaryOptionEmpService;

	/**
	 * @Title saveHrmsNewsalaryOptionEmp
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:36:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/salaryOptionEmp/save")
	public PlatformResult<String> saveHrmsNewsalaryOptionEmp(@RequestBody HrmsNewsalaryOptionEmp record) {
		try {
			hrmsNewsalaryOptionEmpService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryOptionEmp
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:36:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryOptionEmp/update")
	public PlatformResult<String> updateHrmsNewsalaryOptionEmp(@RequestBody HrmsNewsalaryOptionEmp record) {
		try {
			hrmsNewsalaryOptionEmpService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryOptionEmpById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryOptionEmp>
	 * @date 2023��11��11�� ����4:36:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/salaryOptionEmp/{id}")
	public PlatformResult<HrmsNewsalaryOptionEmp> selectHrmsNewsalaryOptionEmpById(@PathVariable String id) {
		try {
			HrmsNewsalaryOptionEmp record = hrmsNewsalaryOptionEmpService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryOptionEmpById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:36:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/salaryOptionEmp/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryOptionEmpById(@PathVariable String id) {
		try {
			hrmsNewsalaryOptionEmpService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryOptionEmpList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryOptionEmp>
	 * @date 2023��11��11�� ����4:36:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/salaryOptionEmp/list")
	public DataSet<HrmsNewsalaryOptionEmp> selectHrmsNewsalaryOptionEmpList(Page page, HrmsNewsalaryOptionEmp record) {
		return hrmsNewsalaryOptionEmpService.getDataSetList(page, record);
	}

	@ApiOperation(value = "人员确认添加人员保存", notes = "人员确认添加人员保存")
	@PostMapping("/api/salaryOptionEmp/perSave/{optionId}")
	public PlatformResult perSave(@PathVariable String optionId,@RequestBody List<String> employeeIds) {
		if(StrUtil.isEmpty(optionId)){
			return PlatformResult.failure("optionId不能为空!");
		}
		if(CollUtil.isEmpty(employeeIds)){
			return PlatformResult.failure("employeeIds不能为空!");
		}
		try {
			hrmsNewsalaryOptionEmpService.perSave(optionId,employeeIds);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "移除人员", notes = "移除人员")
	@PostMapping("/api/salaryOptionEmp/deleteOption/{optionId}")
	public PlatformResult deleteOption(@PathVariable String optionId,@RequestBody List<String> employeeIds) {
		if(StrUtil.isEmpty(optionId)){
			return PlatformResult.failure("optionId不能为空!");
		}
		if(CollUtil.isEmpty(employeeIds)){
			return PlatformResult.failure("employeeIds不能为空!");
		}
		hrmsNewsalaryOptionEmpService.deleteOption(optionId,employeeIds);
		return PlatformResult.success();
	}
}

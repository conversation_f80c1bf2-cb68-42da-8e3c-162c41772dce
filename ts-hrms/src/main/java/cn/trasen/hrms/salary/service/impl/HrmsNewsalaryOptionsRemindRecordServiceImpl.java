package cn.trasen.hrms.salary.service.impl;

import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;
import cn.trasen.hrms.salary.model.HrmsNewsalaryRemindSetting;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicColumnService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryRemindSettingService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionsRemindRecordMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionsRemindRecord;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionsRemindRecordService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @ClassName HrmsNewsalaryOptionsRemindRecordServiceImpl
 * @Description TODO
 * @date 2024��10��29�� ����6:29:36
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryOptionsRemindRecordServiceImpl implements HrmsNewsalaryOptionsRemindRecordService {

	@Resource
	private HrmsNewsalaryOptionsRemindRecordMapper mapper;

	@Resource
	private InformationFeignService informationFeignService;

	@Autowired
    private HrmsNewsalaryRemindSettingService remindSettingService;

	@Autowired
	private HrmsNewsalaryBasicColumnService hrmsNewsalaryBasicColumnService;

	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryOptionsRemindRecord record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryOptionsRemindRecord record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryOptionsRemindRecord record = new HrmsNewsalaryOptionsRemindRecord();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalaryOptionsRemindRecord selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalaryOptionsRemindRecord> getDataSetList(Page page, HrmsNewsalaryOptionsRemindRecord record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsNewsalaryOptionsRemindRecord> records = mapper.getList(page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	/**
	 * 一键处理
	 * @param record
	 * @return
	 */
	@Transactional(readOnly = false)
	@Override
	public Integer batchAdjust(HrmsNewsalaryOptionsRemindRecord record){
		int count = 0;
		if(StringUtils.isNotEmpty(record.getId())){ //单条数据处理
			HrmsNewsalaryOptionsRemindRecord entity = selectById(record.getId());
			if(entity!=null && "0".equals(entity.getHandleStatus())){
				// 更新绑定关系
				HrmsNewsalaryOptionEmp hrmsNewsalaryOptionEmp = new HrmsNewsalaryOptionEmp();
				hrmsNewsalaryOptionEmp.setEmployeeId(entity.getEmployeeId());
				hrmsNewsalaryOptionEmp.setOptionId(record.getAfterOptionId());
				hrmsNewsalaryOptionEmp.setEffectiveDate(record.getEffectiveDate());
				hrmsNewsalaryOptionEmp.setRemark(record.getHandleDesc());
				hrmsNewsalaryBasicColumnService.batchBindOption(hrmsNewsalaryOptionEmp);
				//修改处理状态
				entity.setHandleStatus("1");
				entity.setIsLock("1");
				entity.setAfterOptionId(record.getAfterOptionId());
				entity.setEffectiveDate(record.getEffectiveDate());
				entity.setHandleDesc(record.getHandleDesc());
				count += update(entity);
			}
		}else { //一键处理
			record.setHandleStatus("0");
			if(StringUtils.isBlank(record.getHandleDesc())){
				record.setHandleDesc("一键确认");
			}
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			List<HrmsNewsalaryOptionsRemindRecord> list = mapper.getList(record);
			if (CollectionUtils.isNotEmpty(list)) {
				HrmsNewsalaryOptionEmp hrmsNewsalaryOptionEmp = null;
				for (HrmsNewsalaryOptionsRemindRecord vo : list) {
					if (StringUtils.isBlank(vo.getAfterOptionId())) {
						continue;
					}
					// 更新绑定关系
					hrmsNewsalaryOptionEmp = new HrmsNewsalaryOptionEmp();
					hrmsNewsalaryOptionEmp.setEmployeeId(vo.getEmployeeId());
					hrmsNewsalaryOptionEmp.setOptionId(vo.getAfterOptionId());
					hrmsNewsalaryOptionEmp.setEffectiveDate(record.getEffectiveDate());
					hrmsNewsalaryOptionEmp.setRemark(record.getHandleDesc());
					hrmsNewsalaryBasicColumnService.batchBindOption(hrmsNewsalaryOptionEmp);

					//修改处理状态
					vo.setHandleStatus("1");
					vo.setIsLock("1");
					vo.setEffectiveDate(record.getEffectiveDate());
					vo.setHandleDesc(record.getHandleDesc());
					count += update(vo);
				}
			}
		}
		return count;
	}

    @Transactional(readOnly = false)
	@Override
	public void newsalaryOptionsChange(){
		//处理需要锁定的数据
		List<HrmsNewsalaryOptionsRemindRecord> records = mapper.selectNeedLockOptionsRemindData();
		if(CollectionUtils.isNotEmpty(records)){
			records.forEach(vo->{
				vo.setIsLock("1");
				vo.setHandleDesc("自动锁定");
				mapper.updateByPrimaryKeySelective(vo);
			});
		}
	    //获取方案的提醒设置配置
        HrmsNewsalaryRemindSetting setting = remindSettingService.selectByType(1);
        if(setting!=null && "1".equals(setting.getIsEnabled())){
			String currentTime = DateUtil.format(DateUtil.date(),"HH:mm");
			if(StringUtils.isBlank(setting.getRemindTime()) || !currentTime.equals(setting.getRemindTime())){
				return;
			}
            List<HrmsNewsalaryOptionsRemindRecord> list = mapper.getNewsalaryOptionsChange(setting.getRemindCycle(),setting.getAdvanceDays()+"",setting.getRemindDate(),setting.getRemindTime());
            if(CollectionUtils.isNotEmpty(list)){
                Example example = null;
                Example.Criteria criteria = null;
                for(HrmsNewsalaryOptionsRemindRecord vo : list){
                	//如果员工状态和编制类型为空，则根据员工id查询员工档案详情获取员工状态和编制类别
                	if(StringUtils.isBlank(vo.getEmployeeStatus()) || StringUtils.isBlank(vo.getEstablishmentType())){
						HrmsEmployee hrmsEmployee = hrmsEmployeeService.findDetailById(vo.getEmployeeId());
						if(hrmsEmployee!=null){
							vo.setEmployeeStatus(hrmsEmployee.getEmployeeStatus());
							vo.setEstablishmentType(hrmsEmployee.getEstablishmentType());
						}
					}
                    //删除该员工本月未处理的提醒记录
                    example = new Example(HrmsNewsalaryOptionsRemindRecord.class);
                    criteria = example.createCriteria();
                    criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
                    criteria.andEqualTo("employeeId", vo.getEmployeeId());
                    criteria.andEqualTo("computeDate", vo.getComputeDate());
                    criteria.andEqualTo("handleStatus", "0");
					criteria.andEqualTo("isLock", "0");
                    mapper.deleteByExample(example);

                    //新增方案异动记录
                    vo.setRemindId(setting.getId());
                    vo.setIsLock("0");
                    save(vo);
                }

				try {
					String content = "本月有" + list.size() + "个员工的方案需要进行调整，请前往薪酬提醒-方案异动办理进行处理";
					//发送通知
					NoticeReq notice = NoticeReq.builder()
							.content(content)
							.noticeType("4")
							.receiver(setting.getNoticeUser())
							.sender("admin")
							.senderName("系统管理员")
							.subject("薪酬方案异动提醒通知")
							.toUrl("/ts-web-hrm/pay-manager/newsalary-remind")
							.wxSendType("2")
							.source("异动提醒")
							.build();
					informationFeignService.sendNotice(notice);
				}catch (Exception e){
					e.printStackTrace();
					System.out.println("发送通知异常:"+e.getMessage());
				}

				//如果是单次提醒，则需要修改启用状态为停用
				if("1".equals(setting.getRemindCycle())){
					setting.setIsEnabled("0");
					setting.setRemark("已完成提醒");
					remindSettingService.update(setting);
				}
            }
        }
	}
}

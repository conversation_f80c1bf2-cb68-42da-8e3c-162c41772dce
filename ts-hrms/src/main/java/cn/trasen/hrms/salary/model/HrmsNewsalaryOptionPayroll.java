package cn.trasen.hrms.salary.model;

import cn.trasen.hrms.salary.DTO.UpdateSalaryVo;
import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.*;

/**
 * 薪酬核算表
 *
 */
@Table(name = "hrms_newsalary_option_payroll")
@Setter
@Getter
public class HrmsNewsalaryOptionPayroll {
    /**
     * 核算记录id
     */
    @Id
    @ApiModelProperty(value = "核算记录id")
    private String id;

    /**
     * 方案id
     */
    @Column(name = "option_id")
    @ApiModelProperty(value = "方案id")
    private String optionId;

    /**
     * 方案名称
     */
    @Column(name = "option_name")
    @ApiModelProperty(value = "方案名称")
    private String optionName;

    /**
     * 核算月份
     */
    @Column(name = "compute_date")
    @ApiModelProperty(value = "核算月份")
    private String computeDate;

    /**
     * 核算状态 0 未计算 1已计算 2已完成 3已锁定
     */
    @Column(name = "compute_status")
    @ApiModelProperty(value = "核算状态 0 未计算 1已计算 2已完成  3锁定")
    private String computeStatus;

    /**
     * 算薪人数
     */
    @Column(name = "head_count")
    @ApiModelProperty(value = "算薪人数")
    private Integer headCount;

    /**
     * 定薪人数
     */
    @Column(name = "set_count")
    @ApiModelProperty(value = "定薪人数")
    private Integer setCount;

    /**
     * 调薪人数
     */
    @Column(name = "update_count")
    @ApiModelProperty(value = "调薪人数")
    private Integer updateCount;

    /**
     * 核算时间
     */
    @Column(name = "compute_time")
    @ApiModelProperty(value = "核算时间")
    private String computeTime;

    @Column(name = "pay_slip")
    @ApiModelProperty(value = "发放工资条  1 发放工资条")
    private String paySlip;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;


   @Transient
    private List<String> basicItemIds;

    @Transient
    @ApiModelProperty(value = "1 重新核算")
   private String restartStatus;

    @Transient
    @ApiModelProperty(value = "姓名工号条件")
    private String employeeName;

    @Transient
    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @Transient
    @ApiModelProperty(value = "员工状态")
    private List<String> employeeStatus;

    @Transient
    @ApiModelProperty(value = "调薪原因")
    private String reason; //

    @Transient
    @ApiModelProperty(value = "薪酬项目")
    List<HrmsNewsalaryItem> salaryList;
    @Transient
    @ApiModelProperty(value = "科室名称")
    private String orgName;

    @Transient
    @ApiModelProperty(value = "人员id")
    private String employeeId;

    @Transient
    @ApiModelProperty(value = "需要修改的工资项目")
    List<UpdateSalaryVo> updateSalaryList;
    
    @Transient
    private String salaryIndex;

    @Transient
    @ApiModelProperty(value = "是否核算查询 1是,2否")
    private String isCal;

    @Transient
    @ApiModelProperty(value = "是否锁定查询 1是,2否")
    private String isLock;

    @Transient
    @ApiModelProperty(value = "机构id")
    private String orgId;

    @Transient
    @ApiModelProperty(value = "机构id集合")
    private List<String> orgList;
    
    @Transient
    private String sendStatus;
}
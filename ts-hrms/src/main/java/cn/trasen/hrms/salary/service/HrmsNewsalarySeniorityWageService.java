package cn.trasen.hrms.salary.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalarySeniorityWage;

/**
 * @ClassName HrmsNewsalarySeniorityWageService
 * @Description TODO
 * @date 2024��4��12�� ����11:08:24
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalarySeniorityWageService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��4��12�� ����11:08:24
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalarySeniorityWage record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��4��12�� ����11:08:24
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalarySeniorityWage record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��4��12�� ����11:08:24
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalarySeniorityWage
	 * @date 2024��4��12�� ����11:08:24
	 * <AUTHOR>
	 */
	HrmsNewsalarySeniorityWage selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalarySeniorityWage>
	 * @date 2024��4��12�� ����11:08:24
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalarySeniorityWage> getDataSetList(Page page, HrmsNewsalarySeniorityWage record);

	//拿到所有数据
	List<HrmsNewsalarySeniorityWage> getAllList();

	void tsakSave(HrmsNewsalarySeniorityWage saveData);
}

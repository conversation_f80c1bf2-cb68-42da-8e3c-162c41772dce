<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionEmpMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="option_id" jdbcType="VARCHAR" property="optionId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>

    <select id="getEmpByOption" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp">
      SELECT t2.option_name AS optionName  FROM hrms_newsalary_option_emp t1
     LEFT JOIN hrms_newsalary_option t2 ON t1.option_id = t2.id
      WHERE t1.is_deleted='N' AND t1.employee_id=#{id}
        and t2.option_name is not null
      ORDER BY t2.option_name DESC LIMIT 1

    </select>

  <select id="getAllEmployeeDetails" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp">
    SELECT
    t1.employee_name,t1.employee_no,t1.employee_id,t3.name AS orgName, hno.option_name
    FROM  (
      SELECT
        employee_name,employee_no,org_id,employee_id,is_deleted,salary_appoint
      FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
      UNION ALL
      SELECT
         employee_name,employee_no,org_id,id as employee_id,is_deleted,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint
      FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
    ) t1
    LEFT JOIN hrms_newsalary_option_emp t2 ON t1.employee_id=t2.employee_id
    LEFT JOIN hrms_newsalary_option hno ON t2.option_id=hno.id
    LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
    WHERE t1.is_deleted = 'N' AND t1.salary_appoint ='1'
    AND hno.is_enable = '1' and t2.is_deleted = 'N' and hno.is_deleted = 'N'
    <if test="optionId != '' and optionId != null" >
      and hno.id = #{optionId}
    </if>
    ORDER BY t3.name,hno.option_name
  </select>

  <select id="getEmployeeDetails" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp">
    SELECT t1.id,t2.employee_name,t2.employee_no, t1.employee_id,t1.sort_num FROM hrms_newsalary_option_emp t1
    INNER JOIN (
        SELECT
			employee_name,employee_no,org_id,employee_id,is_deleted
		FROM cust_emp_base
		UNION ALL
		SELECT
				employee_name,employee_no,org_id,id as employee_id,is_deleted
		FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
    ) t2 ON t1.employee_id=t2.employee_id and t2.is_deleted = 'N'
    WHERE t1.is_deleted='N' AND t1.option_id= #{optionId}
    order by t1.sort_num,t2.org_id
  </select>

  <select id="getOprionIdByEmpNo" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp">

    SELECT  t1.employee_no,t2.option_id
    FROM  (
      SELECT
        employee_name,employee_no,org_id,employee_id,is_deleted
      FROM cust_emp_base
      UNION ALL
      SELECT
        employee_name,employee_no,org_id,id as employee_id,is_deleted
      FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
    ) t1
    LEFT JOIN hrms_newsalary_option_emp t2 ON t1.employee_id=t2.employee_id and t1.is_deleted = t2.is_deleted 
    WHERE t1.is_deleted = 'N'
    AND t1.employee_no in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>

  </select>
  <select id="getEmployeeDetailByOptionId" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp">
    SELECT
      t1.employee_name,t1.employee_no,t3.name AS orgName, t4.option_name
    FROM  (
      SELECT
        employee_name,employee_no,org_id,employee_id,is_deleted,salary_appoint
      FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
      UNION ALL
      SELECT
         employee_name,employee_no,org_id,id as employee_id,is_deleted,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint
      FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
    ) t1
    LEFT JOIN hrms_newsalary_option_emp t2 ON t1.employee_id=t2.employee_id and t1.is_deleted = t2.is_deleted 
    LEFT JOIN hrms_newsalary_option hno ON t2.option_id=hno.id
    LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
    LEFT JOIN hrms_newsalary_option t4 ON t2.option_id = t4.id
    WHERE t1.is_deleted = 'N' AND t1.salary_appoint ='1'
    AND hno.is_enable = '1' and hno.id = #{optionId}
    ORDER BY t3.name,t4.option_name
  </select>
  <select id="getIsSalary"  resultType="String">
  	select a.employee_no  from (
  	    SELECT
			employee_name,employee_no,org_id,employee_id,salary_appoint,is_deleted
		FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
		UNION ALL
		SELECT
            employee_name,employee_no,org_id,id as employee_id,
            (select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,
            is_deleted
		FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
  	) a inner join hrms_newsalary_option_emp b
	on a.employee_id = b.employee_id and a.salary_appoint != 1 and a.is_deleted = b.is_deleted 
	where b.option_id= #{optionId} and a.is_deleted = 'N' 
  </select>


</mapper>
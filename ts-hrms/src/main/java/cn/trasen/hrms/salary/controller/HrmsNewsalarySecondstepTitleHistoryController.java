package cn.trasen.hrms.salary.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalarySecondstepTitleHistory;
import cn.trasen.hrms.salary.service.HrmsNewsalarySecondstepTitleHistoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalarySecondstepTitleHistoryController
 * @Description TODO
 * @date 2024��3��11�� ����5:38:28
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsNewsalarySecondstepTitleHistoryController")
public class HrmsNewsalarySecondstepTitleHistoryController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalarySecondstepTitleHistoryController.class);

	@Autowired
	private HrmsNewsalarySecondstepTitleHistoryService hrmsNewsalarySecondstepTitleHistoryService;

	/**
	 * @Title saveHrmsNewsalarySecondstepTitleHistory
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��3��11�� ����5:38:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/newsalarysecondsteptitlehistory/save")
	public PlatformResult<String> saveHrmsNewsalarySecondstepTitleHistory(@RequestBody HrmsNewsalarySecondstepTitleHistory record) {
		try {
			hrmsNewsalarySecondstepTitleHistoryService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalarySecondstepTitleHistory
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��3��11�� ����5:38:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/newsalarysecondsteptitlehistory/update")
	public PlatformResult<String> updateHrmsNewsalarySecondstepTitleHistory(@RequestBody HrmsNewsalarySecondstepTitleHistory record) {
		try {
			hrmsNewsalarySecondstepTitleHistoryService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalarySecondstepTitleHistoryById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalarySecondstepTitleHistory>
	 * @date 2024��3��11�� ����5:38:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/newsalarysecondsteptitlehistory/{id}")
	public PlatformResult<HrmsNewsalarySecondstepTitleHistory> selectHrmsNewsalarySecondstepTitleHistoryById(@PathVariable String id) {
		try {
			HrmsNewsalarySecondstepTitleHistory record = hrmsNewsalarySecondstepTitleHistoryService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalarySecondstepTitleHistoryById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��3��11�� ����5:38:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/newsalarysecondsteptitlehistory/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalarySecondstepTitleHistoryById(@PathVariable String id) {
		try {
			hrmsNewsalarySecondstepTitleHistoryService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalarySecondstepTitleHistoryList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalarySecondstepTitleHistory>
	 * @date 2024��3��11�� ����5:38:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/newsalarysecondsteptitlehistory/list")
	public DataSet<HrmsNewsalarySecondstepTitleHistory> selectHrmsNewsalarySecondstepTitleHistoryList(Page page, HrmsNewsalarySecondstepTitleHistory record) {
		return hrmsNewsalarySecondstepTitleHistoryService.getDataSetList(page, record);
	}
}

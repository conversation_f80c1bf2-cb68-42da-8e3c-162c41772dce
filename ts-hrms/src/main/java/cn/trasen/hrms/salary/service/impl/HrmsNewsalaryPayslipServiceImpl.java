package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryPayslipMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemGroup;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayslip;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemGroupService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemService;
import cn.trasen.hrms.salary.service.IHrmsNewsalaryPayslipService;
import com.google.inject.internal.cglib.proxy.$Callback;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class HrmsNewsalaryPayslipServiceImpl implements IHrmsNewsalaryPayslipService {

    @Autowired
    private HrmsNewsalaryPayslipMapper payslipMapper;

    @Autowired
    private HrmsNewsalaryItemGroupService hrmsNewsalaryItemGroupService;

    @Autowired
    private HrmsNewsalaryItemService hrmsNewsalaryItemService;

    @Override
    public HrmsNewsalaryPayslip getPayslip(String optionId) {
        Assert.hasText(optionId, "薪酬组id不能为空!");
        Example example = new Example(HrmsNewsalaryPayslip.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("optionId",optionId);
        criteria.andEqualTo("isDeleted","N");
        HrmsNewsalaryPayslip payslip = payslipMapper.selectOneByExample(example);
        HrmsNewsalaryItemGroup salaryItemGroup = new HrmsNewsalaryItemGroup();
        salaryItemGroup.setOptionId(optionId);
        List<HrmsNewsalaryItemGroup> salaryItemGroupList = hrmsNewsalaryItemGroupService.listByOptionId(salaryItemGroup);
        if(ObjectUtil.isEmpty(payslip)){
            payslip = new HrmsNewsalaryPayslip();
            //新增默认展示全部工资项
            salaryItemGroupList.forEach(o ->{
                o.setIsArticle("1");
                List<HrmsNewsalaryItem> salaryItemList = o.getSalaryItem();
                salaryItemList.forEach(t -> {
                    t.setIsArticle("1");
                });
            });
        }
        //隐藏空数据项,应发工资,实发工资,个税
        List<String> hiddenList = new ArrayList<>();
        for (HrmsNewsalaryItemGroup itemGroup : salaryItemGroupList) {
            List<HrmsNewsalaryItem> itemList = itemGroup.getSalaryItem();
            for (HrmsNewsalaryItem item : itemList) {
                if(StrUtil.equals("1",item.getIsHidden())){
                    hiddenList.add(item.getId());
                }
                if(StrUtil.equals("1",item.getActualSalary())){
                    payslip.setShow(item.getId());
                }
                if(StrUtil.equals("1",item.getShSalary())){
                    payslip.setShSalary(item.getId());
                }
                if(StrUtil.equals("1",item.getPersonalTax())){
                    payslip.setPersonalTax(item.getId());
                }
                if(StrUtil.equals("1",item.getIsTotal())){
                    payslip.getTotalItems().add(item);
                }
            }
        }
        //汇总项排序
        List<HrmsNewsalaryItem> totalItemList = payslip.getTotalItems();
        if(CollUtil.isNotEmpty(totalItemList)){
            Collections.sort(totalItemList, (obj1, obj2) -> obj1.getTotalSortNo().compareTo(obj2.getTotalSortNo()));
            payslip.setTotalItems(totalItemList);
        }
        payslip.setHidden(hiddenList);
        payslip.setSalaryItemGroup(salaryItemGroupList);
        return payslip;
    }

    @Override
    @Transactional
    public void save(HrmsNewsalaryPayslip payslip) {
        String optionId = payslip.getOptionId();
        Assert.hasText(optionId, "薪酬组id不能为空!");
        Assert.hasText(payslip.getOptionName(), "薪酬组名称不能为空!");
        Assert.hasText(payslip.getSlipName(), "工资条名称不能为空!");
        Example example = new Example(HrmsNewsalaryPayslip.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("optionId",optionId);
        criteria.andEqualTo("isDeleted","N");
        HrmsNewsalaryPayslip hrmsNewsalaryPayslip = payslipMapper.selectOneByExample(example);
        ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();
        //修改工资条
        if(ObjectUtil.isEmpty(hrmsNewsalaryPayslip)){
            payslip.setId(String.valueOf(IdWork.id.nextId()));
            payslip.setCreateDate(new Date());
            payslip.setCreateUser(userInfo.getUsercode());
            payslip.setCreateUserName(userInfo.getUsername());
            payslip.setUpdateDate(new Date());
            payslip.setUpdateUser(userInfo.getUsercode());
            payslip.setUpdateUserName(userInfo.getUsername());
            payslip.setIsDeleted("N");
            payslip.setSsoOrgCode(userInfo.getCorpcode());
            payslip.setSsoOrgName(userInfo.getOrgName());
            payslipMapper.insertSelective(payslip);
        }else {
            if(StringUtils.isEmpty(payslip.getId())){
                throw new BusinessException("已存在当前方案的工资条模板，请勿重复新增");
            }
            payslip.setUpdateDate(new Date());
            payslip.setUpdateUser(userInfo.getUsercode());
            payslip.setUpdateUserName(userInfo.getUsername());
            payslipMapper.updateByPrimaryKeySelective(payslip);
        }
        List<HrmsNewsalaryItemGroup> salaryGroupList = payslip.getSalaryItemGroup();
        if(CollUtil.isNotEmpty(salaryGroupList)) {
            //分组显示
            for (HrmsNewsalaryItemGroup itemGroup : salaryGroupList) {
                hrmsNewsalaryItemGroupService.updateArticle(itemGroup);
            }
            //隐藏空数据项
            if (CollUtil.isNotEmpty(payslip.getHidden())) {
                List<String> hiddenList = payslip.getHidden();
                for (String id : hiddenList) {
                    for (HrmsNewsalaryItemGroup salaryItemGroup : salaryGroupList) {
                        List<HrmsNewsalaryItem> salaryItem = salaryItemGroup.getSalaryItem();
                        for (HrmsNewsalaryItem item : salaryItem) {
                            if (StrUtil.equals(id, item.getId())) {
                                item.setIsHidden("1");
                            }
                        }
                    }
                }
            }else{
                for (HrmsNewsalaryItemGroup salaryItemGroup : salaryGroupList) {
                    List<HrmsNewsalaryItem> salaryItem = salaryItemGroup.getSalaryItem();
                    for (HrmsNewsalaryItem item : salaryItem) {
                        item.setIsHidden("2");
                    }
                }
            }
            //合计项设置
            if (CollUtil.isNotEmpty(payslip.getTotalItems())) {
                for (HrmsNewsalaryItem totalItem : payslip.getTotalItems()) {
                    for (HrmsNewsalaryItemGroup salaryItemGroup : salaryGroupList) {
                        List<HrmsNewsalaryItem> salaryItem = salaryItemGroup.getSalaryItem();
                        for (HrmsNewsalaryItem item : salaryItem) {
                            if (StrUtil.equals(totalItem.getId(), item.getId())) {
                                item.setIsTotal("1");
                                item.setTotalTitleName(totalItem.getTotalTitleName());
                                item.setTotalSortNo(totalItem.getTotalSortNo());
                            }else {
                               long count = payslip.getTotalItems().stream().filter(vo-> StrUtil.equals(vo.getId(), item.getId())).count();
                               if(count<1) {
                                   item.setIsTotal("2");
                                   item.setTotalSortNo(999);
                               }
                            }
                        }
                    }
                }
            }else{
                for (HrmsNewsalaryItemGroup salaryItemGroup : salaryGroupList) {
                    List<HrmsNewsalaryItem> salaryItem = salaryItemGroup.getSalaryItem();
                    for (HrmsNewsalaryItem item : salaryItem) {
                        item.setIsTotal("2");
                        item.setTotalSortNo(999);
                    }
                }
            }
            //应发工资
            if (StrUtil.isNotBlank(payslip.getShSalary())) {
                for (HrmsNewsalaryItemGroup salaryItemGroup : salaryGroupList) {
                    List<HrmsNewsalaryItem> salaryItem = salaryItemGroup.getSalaryItem();
                    for (HrmsNewsalaryItem item : salaryItem) {
                        if (StrUtil.equals(payslip.getShSalary(), item.getId())) {
                            item.setShSalary("1");
                            item.setPersonalTax("2");
                            item.setActualSalary("2");
                        }else {
                        	item.setShSalary("2");                        }
                    }
                }
            }
            //实发工资
            if (StrUtil.isNotBlank(payslip.getShow())) {
                for (HrmsNewsalaryItemGroup salaryItemGroup : salaryGroupList) {
                    List<HrmsNewsalaryItem> salaryItem = salaryItemGroup.getSalaryItem();
                    for (HrmsNewsalaryItem item : salaryItem) {
                        if (StrUtil.equals(payslip.getShow(), item.getId())) {
                            item.setActualSalary("1");
                            item.setPersonalTax("2");
                            item.setShSalary("2");
                        }else {
                        	item.setActualSalary("2");
                        }
                    }
                }
            }
            //是否个税
            if (StrUtil.isNotBlank(payslip.getPersonalTax())) {
                for (HrmsNewsalaryItemGroup salaryItemGroup : salaryGroupList) {
                    List<HrmsNewsalaryItem> salaryItem = salaryItemGroup.getSalaryItem();
                    for (HrmsNewsalaryItem item : salaryItem) {
                        if (StrUtil.equals(payslip.getPersonalTax(), item.getId())) {
                            item.setPersonalTax("1");
                            item.setActualSalary("2");
                            item.setShSalary("2");
                        }else {
                        	item.setPersonalTax("2");
                        }
                    }
                }
            }
            //修改工资项
            salaryGroupList.forEach(o -> {
                hrmsNewsalaryItemService.paySheet(o.getSalaryItem());
            });
        }
    }

    @Override
    public DataSet<HrmsNewsalaryPayslip> getDataList(Page page, HrmsNewsalaryPayslip payslip) {
        Example example = new Example(HrmsNewsalaryPayslip.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("isDeleted","N");
        if(StrUtil.isNotBlank(payslip.getSlipName())){
            criteria.andLike("slipName","%"+payslip.getSlipName()+"%");
        }
        if(StrUtil.isNotBlank(payslip.getIsEnable())){
            criteria.andEqualTo("isEnable",payslip.getIsEnable());
        }
        if(StrUtil.isNotBlank(payslip.getOptionId())){
            criteria.andEqualTo("optionId",payslip.getOptionId());
        }
        List<HrmsNewsalaryPayslip> list = payslipMapper.selectByExampleAndRowBounds(example, page);
        if(CollUtil.isNotEmpty(list)){
            list.forEach(o ->{
                HrmsNewsalaryItemGroup salaryItemGroup = new HrmsNewsalaryItemGroup();
                salaryItemGroup.setOptionId(o.getOptionId());
                List<HrmsNewsalaryItemGroup> salaryItemGroupList = hrmsNewsalaryItemGroupService.listByOptionId(salaryItemGroup);
                //隐藏空数据项,应发工资,实发工资,个税
                List<String> hiddenList = new ArrayList<>();
                for (HrmsNewsalaryItemGroup itemGroup : salaryItemGroupList) {
                    List<HrmsNewsalaryItem> itemList = itemGroup.getSalaryItem();
                    for (HrmsNewsalaryItem item : itemList) {
                        if(StrUtil.equals("1",item.getIsHidden())){
                            hiddenList.add(item.getId());
                        }
                        if(StrUtil.equals("1",item.getActualSalary())){
                            o.setShow(item.getId());
                        }
                        if(StrUtil.equals("1",item.getShSalary())){
                            o.setShSalary(item.getId());
                        }
                        if(StrUtil.equals("1",item.getPersonalTax())){
                            o.setPersonalTax(item.getId());
                        }
                    }
                }
                o.setHidden(hiddenList);
                o.setSalaryItemGroup(salaryItemGroupList);
            });
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
    }

    @Override
    public void enable(List<String> ids) {
        HrmsNewsalaryPayslip payslip = new HrmsNewsalaryPayslip();
        payslip.setIsEnable("1");
        Example example = new Example(HrmsNewsalaryPayslip.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id",ids);
        payslipMapper.updateByExampleSelective(payslip,example);
    }

    @Override
    public void disEnable(List<String> ids) {
        HrmsNewsalaryPayslip payslip = new HrmsNewsalaryPayslip();
        payslip.setIsEnable("2");
        Example example = new Example(HrmsNewsalaryPayslip.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id",ids);
        payslipMapper.updateByExampleSelective(payslip,example);
    }

    @Override
    @Transactional
    public void add(HrmsNewsalaryPayslip payslip) {
        String optionId = payslip.getOptionId();
        Assert.hasText(optionId, "薪酬组id不能为空!");
        Assert.hasText(payslip.getOptionName(), "薪酬组名称不能为空!");
        Assert.hasText(payslip.getSlipName(), "工资条名称不能为空!");
        payslip.setId(String.valueOf(IdWork.id.nextId()));
        ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();
        payslip.setCreateDate(new Date());
        payslip.setCreateUser(userInfo.getUsercode());
        payslip.setCreateUserName(userInfo.getUsername());
        payslip.setUpdateDate(new Date());
        payslip.setUpdateUser(userInfo.getUsercode());
        payslip.setUpdateUserName(userInfo.getUsername());
        payslip.setIsDeleted("N");
        payslip.setSsoOrgCode(userInfo.getCorpcode());
        payslip.setSsoOrgName(userInfo.getOrgName());
        payslipMapper.insertSelective(payslip);
        List<HrmsNewsalaryItemGroup> salaryGroupList = payslip.getSalaryItemGroup();
        if(CollUtil.isNotEmpty(salaryGroupList)) {
            //分组显示
            for (HrmsNewsalaryItemGroup itemGroup : salaryGroupList) {
                hrmsNewsalaryItemGroupService.updateArticle(itemGroup);
            }
            //隐藏空数据项
            if (CollUtil.isNotEmpty(payslip.getHidden())) {
                List<String> hiddenList = payslip.getHidden();
                for (String id : hiddenList) {
                    for (HrmsNewsalaryItemGroup salaryItemGroup : salaryGroupList) {
                        List<HrmsNewsalaryItem> salaryItem = salaryItemGroup.getSalaryItem();
                        for (HrmsNewsalaryItem item : salaryItem) {
                            if (StrUtil.equals(id, item.getId())) {
                                item.setIsHidden("1");
                            }
                        }
                    }
                }
            }
            //应发工资
            if (StrUtil.isNotBlank(payslip.getShSalary())) {
                for (HrmsNewsalaryItemGroup salaryItemGroup : salaryGroupList) {
                    List<HrmsNewsalaryItem> salaryItem = salaryItemGroup.getSalaryItem();
                    for (HrmsNewsalaryItem item : salaryItem) {
                        if (StrUtil.equals(payslip.getShSalary(), item.getId())) {
                            item.setShSalary("1");
                        }
                    }
                }
            }
            //实发工资
            if (StrUtil.isNotBlank(payslip.getShow())) {
                for (HrmsNewsalaryItemGroup salaryItemGroup : salaryGroupList) {
                    List<HrmsNewsalaryItem> salaryItem = salaryItemGroup.getSalaryItem();
                    for (HrmsNewsalaryItem item : salaryItem) {
                        if (StrUtil.equals(payslip.getShow(), item.getId())) {
                            item.setActualSalary("1");
                        }
                    }
                }
            }
            //是否个税
            if (StrUtil.isNotBlank(payslip.getPersonalTax())) {
                for (HrmsNewsalaryItemGroup salaryItemGroup : salaryGroupList) {
                    List<HrmsNewsalaryItem> salaryItem = salaryItemGroup.getSalaryItem();
                    for (HrmsNewsalaryItem item : salaryItem) {
                        if (StrUtil.equals(payslip.getPersonalTax(), item.getId())) {
                            item.setPersonalTax("1");
                        }
                    }
                }
            }
            //修改工资项
            salaryGroupList.forEach(o -> {
                hrmsNewsalaryItemService.paySheet(o.getSalaryItem());
            });
        }
    }

    @Override
    @Transactional
    public void delete(HrmsNewsalaryPayslip payslip) {
        payslip.setIsDeleted("Y");
        payslipMapper.updateByPrimaryKeySelective(payslip);
        List<HrmsNewsalaryItemGroup> salaryItemGroup = payslip.getSalaryItemGroup();
        for (HrmsNewsalaryItemGroup itemGroup : salaryItemGroup) {
            itemGroup.setIsArticle("2");
            hrmsNewsalaryItemGroupService.updateArticle(itemGroup);
            List<HrmsNewsalaryItem> itemList = itemGroup.getSalaryItem();
            for (HrmsNewsalaryItem hrmsNewsalaryItem : itemList) {
                hrmsNewsalaryItemService.updateArticle(hrmsNewsalaryItem);
            }
        }
    }

    @Override
    public HrmsNewsalaryPayslip getPaySlipByOptionId(String optionId) {
        Example example = new Example(HrmsNewsalaryPayslip.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("optionId",optionId);
        criteria.andEqualTo("isDeleted","N");
        criteria.andEqualTo("isEnable","1");
        HrmsNewsalaryPayslip payslip = payslipMapper.selectOneByExample(example);
        return payslip;
    }

    @Override
    public void sysSlipName(String optionId,String optionName) {
        Example example = new Example(HrmsNewsalaryPayslip.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("isDeleted","N");
        criteria.andEqualTo("optionId",optionId);
        HrmsNewsalaryPayslip payslip = payslipMapper.selectOneByExample(example);
        if(ObjectUtil.isNotEmpty(payslip)){
            payslip.setOptionName(optionName);
            payslip.setSlipName(optionName);
            payslip.setUpdateUser(UserInfoHolder.getCurrentUserCode());
            payslip.setUpdateUserName(UserInfoHolder.getCurrentUserName());
            payslip.setUpdateDate(new Date());
            payslipMapper.updateByPrimaryKeySelective(payslip);
        }
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryBasicitemEmpHistoryMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmpHistory">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="basic_item_id" jdbcType="VARCHAR" property="basicItemId" />
    <result column="emp_field" jdbcType="VARCHAR" property="empField" />
    <result column="salary_amount" jdbcType="DECIMAL" property="salaryAmount" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="effective_date" jdbcType="VARCHAR" property="effectiveDate" />
    <result column="v_number" jdbcType="VARCHAR" property="vNumber" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>

    <!-- 查詢调薪记录 -->
    <select id="getDataSetList" resultType="java.util.Map"
            parameterType="cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmpHistory">
      SELECT
        effective_date,
        employee_id,
        remark,
        update_user_name,
        create_date as update_date,
        reason,
        case data_status when '1' then '已生效' when '2' then '已过期' ELSE '未生效' END as data_status,
      <if test="allTypeList != null and allTypeList.size() > 0">
        <foreach collection="allTypeList" index="index"
                 item="item" open="" separator="," close="">
          MAX(CASE basic_item_id WHEN  #{item} THEN salary_amount ELSE NULL END) AS  '${item}'
        </foreach>
      </if>
      FROM hrms_newsalary_basicitem_emp_history
      WHERE employee_id = #{employeeId}
      <if test="dataStatus != null and dataStatus !='' ">
        and data_status = '1'
      </if>
      GROUP BY v_number
      order by effective_date desc, create_date desc
    </select>


  <select id="currentSalary" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp">
    SELECT t1.*,t2.basic_item_name,t2.basic_item_type as basicItemType,
      COALESCE(t3.carry_rule,0) as carry_rule,COALESCE(t3.item_digit,0) as item_digit
     FROM hrms_newsalary_basicitem_emp_history t1
    LEFT JOIN hrms_newsalary_basic_column t2 ON t1.basic_item_id = t2.id
	left join hrms_newsalary_item t3 on t1.emp_field = t3.id and t1.is_deleted = t3.is_deleted
	    and t3.option_id = (select option_id from hrms_newsalary_option_emp where employee_id = t1.employee_id and is_deleted = 'N')
    WHERE  t1.data_status ='1' and   t1.is_deleted='N' AND  t1.employee_id=#{employeeId}
    order by t2.number_sort asc
  </select>


  <select id="getEmpTakeEffect" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmpHistory">
    SELECT * FROM hrms_newsalary_basicitem_emp_history
    WHERE is_deleted='N' AND data_status='0'
    <![CDATA[ and  effective_date <= #{takeEffect}]]>
  </select>


</mapper>
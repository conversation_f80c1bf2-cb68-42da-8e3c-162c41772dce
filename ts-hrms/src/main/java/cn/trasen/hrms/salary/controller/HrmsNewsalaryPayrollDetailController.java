package cn.trasen.hrms.salary.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetail;
import cn.trasen.hrms.salary.service.HrmsNewsalaryPayrollDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalaryPayrollDetailController
 * @Description 薪酬发放明细表
 * @date 2023��11��11�� ����4:38:30
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "薪酬发放明细Controller")
public class HrmsNewsalaryPayrollDetailController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryPayrollDetailController.class);

	@Autowired
	private HrmsNewsalaryPayrollDetailService hrmsNewsalaryPayrollDetailService;

	/**
	 * @Title saveHrmsNewsalaryPayrollDetail
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:38:30
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/salaryPayrollDetail/save")
	public PlatformResult<String> saveHrmsNewsalaryPayrollDetail(@RequestBody HrmsNewsalaryPayrollDetail record) {
		try {
			hrmsNewsalaryPayrollDetailService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryPayrollDetail
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:38:30
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryPayrollDetail/update")
	public PlatformResult<String> updateHrmsNewsalaryPayrollDetail(@RequestBody HrmsNewsalaryPayrollDetail record) {
		try {
			hrmsNewsalaryPayrollDetailService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryPayrollDetailById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryPayrollDetail>
	 * @date 2023��11��11�� ����4:38:30
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/salaryPayrollDetail/{id}")
	public PlatformResult<HrmsNewsalaryPayrollDetail> selectHrmsNewsalaryPayrollDetailById(@PathVariable String id) {
		try {
			HrmsNewsalaryPayrollDetail record = hrmsNewsalaryPayrollDetailService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryPayrollDetailById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:38:30
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/salaryPayrollDetail/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryPayrollDetailById(@PathVariable String id) {
		try {
			hrmsNewsalaryPayrollDetailService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryPayrollDetailList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryPayrollDetail>
	 * @date 2023��11��11�� ����4:38:30
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/salaryPayrollDetail/list")
	public DataSet<HrmsNewsalaryPayrollDetail> selectHrmsNewsalaryPayrollDetailList(Page page, HrmsNewsalaryPayrollDetail record) {
		return hrmsNewsalaryPayrollDetailService.getDataSetList(page, record);
	}
}

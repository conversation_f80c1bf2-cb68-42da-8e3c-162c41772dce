package cn.trasen.hrms.salary.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemRemindSetting;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface HrmsNewsalaryItemRemindSettingMapper extends Mapper<HrmsNewsalaryItemRemindSetting> {
    /**
     * @Title: getList
     * @Description: 查询薪酬薪资提醒设置列表
     * @param entity
     * @Return List<HrmsEmployeeTemporary>
     * <AUTHOR>
     * @date 2020年4月15日 下午2:45:21
     */
    List<HrmsNewsalaryItemRemindSetting> getList(Page page, HrmsNewsalaryItemRemindSetting entity);

    /**
     * @Title: getList
     * @Description: 查询薪酬薪资提醒设置列表
     * @param entity
     * @Return List<HrmsEmployeeTemporary>
     * <AUTHOR>
     * @date 2020年4月15日 下午2:45:21
     */
    List<HrmsNewsalaryItemRemindSetting> getList(HrmsNewsalaryItemRemindSetting entity);
}
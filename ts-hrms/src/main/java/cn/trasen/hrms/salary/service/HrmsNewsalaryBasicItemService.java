package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicItem;

/**
 * @ClassName HrmsNewsalaryBasicItemService
 * @Description TODO
 * @date 2023��11��11�� ����4:32:00
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryBasicItemService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:32:00
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryBasicItem record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:32:00
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryBasicItem record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��11��11�� ����4:32:00
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryBasicItem
	 * @date 2023��11��11�� ����4:32:00
	 * <AUTHOR>
	 */
	HrmsNewsalaryBasicItem selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryBasicItem>
	 * @date 2023��11��11�� ����4:32:00
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryBasicItem> getDataSetList(Page page, HrmsNewsalaryBasicItem record);
}

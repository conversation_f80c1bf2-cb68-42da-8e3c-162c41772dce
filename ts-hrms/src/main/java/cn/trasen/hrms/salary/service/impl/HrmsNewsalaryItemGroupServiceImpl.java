package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryItemGroupMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemGroup;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemGroupService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemService;
import cn.trasen.hrms.utils.IdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName HrmsNewsalaryItemGroupServiceImpl
 * @Description TODO
 * @date 2023��11��11�� ����4:34:59
 * <AUTHOR>
 * @version 1.0
 */
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryItemGroupServiceImpl implements HrmsNewsalaryItemGroupService {

	@Autowired
	private HrmsNewsalaryItemGroupMapper mapper;

	@Autowired
	HrmsNewsalaryItemService hrmsNewsalaryItemService;

//	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryItemGroup record) {
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	private String copySave(HrmsNewsalaryItemGroup record) {
		String id = IdUtil.getId();
		record.setId(id);
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		mapper.insertSelective(record);
		return id;
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryItemGroup record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		List<HrmsNewsalaryItem> salaryItemList = hrmsNewsalaryItemService.getDataByGroupId(id);
		if(CollUtil.isNotEmpty(salaryItemList)){
			throw  new BusinessException("存在薪酬项目,不允许删除!");
		}
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryItemGroup record = new HrmsNewsalaryItemGroup();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalaryItemGroup selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalaryItemGroup> getDataSetList(Page page, HrmsNewsalaryItemGroup record) {
		Example example = new Example(HrmsNewsalaryItemGroup.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("optionId", record.getOptionId());
		List<HrmsNewsalaryItemGroup> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	//从其它薪资组导入
//	@Transactional(readOnly = false)
	@Override
	public Integer copy(HrmsNewsalaryItemGroup record) {

		//查出薪酬组的所有分组 和明细
		HrmsNewsalaryItemGroup oldBean = new HrmsNewsalaryItemGroup();
		oldBean.setOptionId(record.getCopyOptionId());
		List<HrmsNewsalaryItemGroup> oldList = listByOptionId(oldBean);

		HrmsNewsalaryItemGroup newBean = new HrmsNewsalaryItemGroup();
		newBean.setOptionId(record.getOptionId());
		List<HrmsNewsalaryItemGroup> newList = listByOptionId(newBean);

		if(!oldList.isEmpty()){
			for (int i = 0; i < oldList.size() ; i++) {
				if(!newList.isEmpty()){
					boolean isAdd = true;
					for (int j = 0; j <newList.size() ; j++) {
						if(oldList.get(i).getItemGroup().equals(newList.get(j).getItemGroup())){
							isAdd = false;
							//处理里面的项目

							List<HrmsNewsalaryItem> _oldSalaryItem = oldList.get(i).getSalaryItem();
							List<HrmsNewsalaryItem> _newSalaryItem = newList.get(j).getSalaryItem();
							if(!_oldSalaryItem.isEmpty()){
								String _groupId = newList.get(j).getId();
								for (int k = 0; k < _oldSalaryItem.size(); k++) {
									if(!_newSalaryItem.isEmpty()){
										for (int l = 0; l < _newSalaryItem.size(); l++) {
											if(!_oldSalaryItem.get(k).getItemName().equals(_newSalaryItem.get(l).getItemName())){
												HrmsNewsalaryItem hrmsNewsalaryItem = _oldSalaryItem.get(k);
												hrmsNewsalaryItem.setGroupId(_groupId);
												hrmsNewsalaryItem.setOptionId(record.getOptionId());
												hrmsNewsalaryItemService.save(hrmsNewsalaryItem);
											}
										}
									}else{
										for (int l = 0; l < _oldSalaryItem.size(); l++) {
											 _oldSalaryItem.get(l).setGroupId(_groupId);
											_oldSalaryItem.get(l).setOptionId(record.getOptionId());
											hrmsNewsalaryItemService.save( _oldSalaryItem.get(l));
										}
									}
								}
							}
							break;
						}
					}
					if(isAdd){//添加
						HrmsNewsalaryItemGroup _addBean = oldList.get(i);
						_addBean.setOptionId(record.getOptionId());
						String _id = copySave(_addBean);
						//判断下面的项目是否存在  不存在就添加

						List<HrmsNewsalaryItem> oldSalaryItem = _addBean.getSalaryItem();
						oldSalaryItem.forEach(item->{
							item.setGroupId(_id);
							item.setOptionId(record.getOptionId());
							hrmsNewsalaryItemService.save(item);
						});

					}
				}else{
					HrmsNewsalaryItemGroup _addBean = oldList.get(i);
					_addBean.setOptionId(record.getOptionId());
					String _id = copySave(_addBean);

					//添加项目
					List<HrmsNewsalaryItem> salaryItem = _addBean.getSalaryItem();
					salaryItem.forEach(item->{
						item.setGroupId(_id);
						item.setOptionId(record.getOptionId());
						hrmsNewsalaryItemService.save(item);
					});
				}
			}
		}
		return 1;
	}

	@Override
	public List<HrmsNewsalaryItemGroup> listByOptionId(HrmsNewsalaryItemGroup record) {
		Assert.hasText(record.getOptionId(), "薪酬组id不能为空.");
		Example example = new Example(HrmsNewsalaryItemGroup.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("optionId", record.getOptionId());
		example.setOrderByClause(" seq_no ");
		List<HrmsNewsalaryItemGroup> groupList = mapper.selectByExample(example);
		if(!groupList.isEmpty()){
			groupList.forEach(item->{
				List<HrmsNewsalaryItem> _list = hrmsNewsalaryItemService.getDataByGroupId(item.getId());
				item.setSalaryItem(_list);
			});
		}
		return groupList;
	}

	@Override
	public List<HrmsNewsalaryItemGroup> listByOptionIdSel(HrmsNewsalaryItemGroup record) {
		Assert.hasText(record.getOptionId(), "薪酬组id不能为空.");
		Example example = new Example(HrmsNewsalaryItemGroup.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("optionId", record.getOptionId());
		example.setOrderByClause(" seq_no ");
		List<HrmsNewsalaryItemGroup> groupList = mapper.selectByExample(example);
		return groupList;
	}

	@Override
	public void updateArticle(HrmsNewsalaryItemGroup record) {
		HrmsNewsalaryItemGroup item = new HrmsNewsalaryItemGroup();
		item.setIsArticle(StrUtil.isBlank(record.getIsArticle())?"2":record.getIsArticle());
		item.setId(record.getId());
		mapper.updateByPrimaryKeySelective(item);
	}

	@Override
	public List<HrmsNewsalaryItemGroup> getSalaryGroupByOptionId(String optionId) {
		Example example = new Example(HrmsNewsalaryItemGroup.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD,Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("optionId",optionId);
		example.orderBy("seqNo").asc();
		List<HrmsNewsalaryItemGroup> list = mapper.selectByExample(example);
		return list;
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer importItem(String groupId,List<HrmsNewsalaryItem> record) {
		//导入工资项

		//根据分组id 查询方案 数据
		HrmsNewsalaryItemGroup hrmsNewsalaryItemGroup = mapper.selectByPrimaryKey(groupId);

		//1 查询分组里面是不是有这个项目
		//HrmsNewsalaryItemGroup hrmsNewsalaryItemGroup = mapper.selectByPrimaryKey(groupId);
		List<HrmsNewsalaryItem> dataByGroupId = hrmsNewsalaryItemService.getDataByGroupId(groupId);

		if(!dataByGroupId.isEmpty() ){
			for (int i = 0; i < record.size(); i++) {
				boolean contain= true;
				for (int j = 0; j < dataByGroupId.size(); j++) {
					if (record.get(i).getItemName().equals(dataByGroupId.get(j).getItemName())){
						contain = false;
						break;
					}
				}
				if(contain){
					record.get(i).setOptionId(hrmsNewsalaryItemGroup.getOptionId());
					record.get(i).setGroupId(groupId);
					if (StrUtil.isNotEmpty(record.get(i).getItemRule()) && Objects.equals(record.get(i).getItemRule(),"1")){
                       record.get(i).setSourceType(2);
					}
					hrmsNewsalaryItemService.save(record.get(i));
				}
			}
		}else{
			for (int i = 0; i < record.size(); i++) {
				record.get(i).setOptionId(hrmsNewsalaryItemGroup.getOptionId());
				record.get(i).setGroupId(groupId);
				if (StrUtil.isNotEmpty(record.get(i).getItemRule()) && Objects.equals(record.get(i).getItemRule(),"1")){
					record.get(i).setSourceType(2);
				}
				hrmsNewsalaryItemService.save(record.get(i));
			}
		}
		return 1;
	}
}

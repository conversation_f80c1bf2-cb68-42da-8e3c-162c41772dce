package cn.trasen.hrms.salary.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.model.HrmsNewsalaryChangesDetailedEo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
* @Entity generator.domain.HrmsNewsalaryChangesDetailed
*/
public interface HrmsNewsalaryChangesDetailedMapper extends Mapper<HrmsNewsalaryChangesDetailedEo> {


    String queryOrgName(@Param("employeeId") String employeeId);

    List<HrmsNewsalaryChangesDetailedEo> getDataList(@Param("entity") HrmsNewsalaryChangesDetailedEo entity, Page page);

    /**
     * 根据员工id 获取时间段内的薪酬调整原因分组
     * @param employeeId
     * @param startDate
     * @param endDate
     * @return
     */
    List<Map<String,String>> getEmployeeNewsalaryChangesGroupByDate(@Param("employeeId") String employeeId,@Param("startDate") String startDate,@Param("endDate") String endDate);
}

package cn.trasen.hrms.salary.service.impl;

import java.util.Date;
import java.util.List;

import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemRemindSetting;
import cn.trasen.hrms.salary.model.HrmsNewsalaryLevelRemindSetting;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemRemindSettingService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryLevelRemindSettingService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.hrms.salary.dao.HrmsNewsalaryRemindSettingMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryRemindSetting;
import cn.trasen.hrms.salary.service.HrmsNewsalaryRemindSettingService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @ClassName HrmsNewsalaryRemindSettingServiceImpl
 * @Description TODO
 * @date 2024��10��25�� ����3:46:52
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryRemindSettingServiceImpl implements HrmsNewsalaryRemindSettingService {

	@Resource
	private HrmsNewsalaryRemindSettingMapper mapper;
    @Resource
    private HrmsNewsalaryItemRemindSettingService newsalaryItemRemindSettingService;
	@Resource
	private HrmsNewsalaryLevelRemindSettingService newsalaryLevelRemindSettingService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryRemindSetting record) {

		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//保存薪酬薪资设置
        if(StringUtils.isNotBlank(record.getRemindType()) && "2".equals(record.getRemindType()) && CollectionUtil.isNotEmpty(record.getItemRemindSettingList())){
            record.setIsEnabled("1");
            record.getItemRemindSettingList().forEach(vo->{
                vo.setRemindId(record.getId());
                newsalaryItemRemindSettingService.save(vo);
            });
        }
		//保存薪级等级设置
		if(StringUtils.isNotBlank(record.getRemindType()) && "3".equals(record.getRemindType()) && CollectionUtil.isNotEmpty(record.getLevelRemindSettingList())){
			record.setIsEnabled("1");
			record.getLevelRemindSettingList().forEach(vo->{
				vo.setRemindId(record.getId());
				newsalaryLevelRemindSettingService.save(vo);
			});
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryRemindSetting record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
        //保存薪酬薪酬设置
        if(StringUtils.isNotBlank(record.getRemindType()) && "2".equals(record.getRemindType()) && CollectionUtil.isNotEmpty(record.getItemRemindSettingList())){
            newsalaryItemRemindSettingService.deleteByRemindId(record.getId());
            record.getItemRemindSettingList().forEach(vo->{
                vo.setRemindId(record.getId());
                newsalaryItemRemindSettingService.save(vo);
            });
        }
		//保存等级等级设置
		if(StringUtils.isNotBlank(record.getRemindType()) && "3".equals(record.getRemindType()) && CollectionUtil.isNotEmpty(record.getLevelRemindSettingList())){
			newsalaryLevelRemindSettingService.deleteByRemindId(record.getId());
			record.getLevelRemindSettingList().forEach(vo->{
				vo.setRemindId(record.getId());
				newsalaryLevelRemindSettingService.save(vo);
			});
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryRemindSetting record = new HrmsNewsalaryRemindSetting();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalaryRemindSetting selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public HrmsNewsalaryRemindSetting selectByType(Integer remindType) {
		Assert.notNull(remindType, "类型不能为空.");
		Example example = new Example(HrmsNewsalaryRemindSetting.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("remindType",remindType);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		HrmsNewsalaryRemindSetting result = mapper.selectOneByExample(example);
        if(result!=null && 2 == remindType.intValue()){
            HrmsNewsalaryItemRemindSetting itemParams = new HrmsNewsalaryItemRemindSetting();
            itemParams.setRemindId(result.getId());
			itemParams.setIsDeleted("N");
            List<HrmsNewsalaryItemRemindSetting> list =newsalaryItemRemindSettingService.getList(itemParams);
            result.setItemRemindSettingList(list);
        }
		if(result!=null && 3 == remindType.intValue()){
			HrmsNewsalaryLevelRemindSetting itemParams = new HrmsNewsalaryLevelRemindSetting();
			itemParams.setRemindId(result.getId());
			itemParams.setIsDeleted("N");
			List<HrmsNewsalaryLevelRemindSetting> list =newsalaryLevelRemindSettingService.getList(itemParams);
			result.setLevelRemindSettingList(list);
		}
        return result;
	}



	@Override
	public DataSet<HrmsNewsalaryRemindSetting> getDataSetList(Page page, HrmsNewsalaryRemindSetting record) {
		Example example = new Example(HrmsNewsalaryRemindSetting.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalaryRemindSetting> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

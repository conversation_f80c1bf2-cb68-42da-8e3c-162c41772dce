package cn.trasen.hrms.salary.controller;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.hrms.salary.model.HrmsNewsalaryLevelRemindSetting;
import cn.trasen.hrms.salary.service.HrmsNewsalaryLevelRemindSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalaryLevelRemindSettingController
 * @Description TODO
 * @date 2024��11��6�� ����10:32:10
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "hrmsNewsalaryLevelRemindSettingController")
public class HrmsNewsalaryLevelRemindSettingController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryLevelRemindSettingController.class);

	@Autowired
	private HrmsNewsalaryLevelRemindSettingService hrmsNewsalaryLevelRemindSettingService;

	/**
	 * @Title saveHrmsNewsalaryLevelRemindSetting
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��11��6�� ����10:32:10
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/hrmsNewsalaryLevelRemindSetting/save")
	public PlatformResult<String> saveHrmsNewsalaryLevelRemindSetting(@RequestBody HrmsNewsalaryLevelRemindSetting record) {
		try {
			hrmsNewsalaryLevelRemindSettingService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryLevelRemindSetting
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��11��6�� ����10:32:10
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/hrmsNewsalaryLevelRemindSetting/update")
	public PlatformResult<String> updateHrmsNewsalaryLevelRemindSetting(@RequestBody HrmsNewsalaryLevelRemindSetting record) {
		try {
			hrmsNewsalaryLevelRemindSettingService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryLevelRemindSettingById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryLevelRemindSetting>
	 * @date 2024��11��6�� ����10:32:10
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/hrmsNewsalaryLevelRemindSetting/{id}")
	public PlatformResult<HrmsNewsalaryLevelRemindSetting> selectHrmsNewsalaryLevelRemindSettingById(@PathVariable String id) {
		try {
			HrmsNewsalaryLevelRemindSetting record = hrmsNewsalaryLevelRemindSettingService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryLevelRemindSettingById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��11��6�� ����10:32:10
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/hrmsNewsalaryLevelRemindSetting/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryLevelRemindSettingById(@PathVariable String id) {
		try {
			hrmsNewsalaryLevelRemindSettingService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryLevelRemindSettingList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryLevelRemindSetting>
	 * @date 2024��11��6�� ����10:32:10
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/hrmsNewsalaryLevelRemindSetting/list")
	public DataSet<HrmsNewsalaryLevelRemindSetting> selectHrmsNewsalaryLevelRemindSettingList(Page page, HrmsNewsalaryLevelRemindSetting record) {
		return hrmsNewsalaryLevelRemindSettingService.getDataSetList(page, record);
	}
}

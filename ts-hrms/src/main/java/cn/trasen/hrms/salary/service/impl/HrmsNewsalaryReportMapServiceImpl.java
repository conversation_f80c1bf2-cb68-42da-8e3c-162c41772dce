package cn.trasen.hrms.salary.service.impl;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportMapVo;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryItemMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryReportMapMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryReportMapEo;
import cn.trasen.hrms.salary.service.HrmsNewsalaryReportMapService;
import cn.trasen.hrms.salary.utils.FormulaParse;
import cn.trasen.hrms.utils.IdUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName HrmsNewsalaryOptionTotalTypeServiceImpl
 * @Description TODO
 * @date 2024  0723
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryReportMapServiceImpl implements HrmsNewsalaryReportMapService {

	@Autowired
	private HrmsNewsalaryReportMapMapper mapper;
	@Autowired
	private HrmsNewsalaryItemMapper itemMapper;

	@Override
	@Transactional(readOnly = false)
	public Integer save(HrmsNewsalaryReportMapVo record) {
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		HrmsNewsalaryReportMapEo newsalaryOptionTotalTypeEo = copyObjectInfoEo(record);
		return mapper.insertSelective(newsalaryOptionTotalTypeEo);
	}

	@Override
	@Transactional(readOnly = false)
	public Integer update(HrmsNewsalaryReportMapVo record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		HrmsNewsalaryReportMapEo newsalaryOptionTotalTypeEo = copyObjectInfoEo(record);
		return mapper.updateByPrimaryKeySelective(newsalaryOptionTotalTypeEo);
	}

	@Override
	public HrmsNewsalaryReportMapVo selectById(String id) {
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryReportMapEo record = new HrmsNewsalaryReportMapEo();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public DataSet<HrmsNewsalaryReportMapVo> getDataSetList(Page page, HrmsNewsalaryReportMapVo record) {
		List<HrmsNewsalaryReportMapVo> records = mapper.getDataSetList(record, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<String> queryItem(String reportId) {
		Example example = new Example(HrmsNewsalaryItem.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("status",1);
		example.setDistinct(true);
		List<String> list = new ArrayList<>();
		list.addAll(mapper.queryItem(reportId));
		List<Map<String,String>> countFormulaItemList = mapper.queryCountFormulaItem(reportId);
		if (CollectionUtils.isNotEmpty(countFormulaItemList)){
			countFormulaItemList.forEach(map->{
				List<String> _item = FormulaParse.getCountFormulaList(map.get("count_formula")); // 单独是项目;
				if (CollectionUtils.isNotEmpty(_item)) {
					_item.forEach(item-> {
						Map<String, String> countFormulaCode = FormulaParse.getCountFormulaCode(item);
						if (null != countFormulaCode){
							list.add(countFormulaCode.get("code"));
						}
					});
				}
			});
		}
		if(CollectionUtils.isNotEmpty(list)) {
			criteria.andNotIn("id", list);
		}
		List<HrmsNewsalaryItem> hrmsNewsalaryItems = itemMapper.selectByExample(example);
		List<String> collect = hrmsNewsalaryItems.stream().map(HrmsNewsalaryItem::getItemName).distinct().collect(Collectors.toList());
		return collect;
	}

	private HrmsNewsalaryReportMapEo copyObjectInfoEo(HrmsNewsalaryReportMapVo record) {
		HrmsNewsalaryReportMapEo newsalaryOptionTotalTypeEo = new HrmsNewsalaryReportMapEo();
		BeanUtils.copyProperties(record,newsalaryOptionTotalTypeEo);
		return newsalaryOptionTotalTypeEo;
	}
}

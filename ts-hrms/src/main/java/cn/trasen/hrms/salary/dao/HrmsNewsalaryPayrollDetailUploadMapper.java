package cn.trasen.hrms.salary.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailUploadEo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
* @Entity generator.domain.HrmsNewsalaryPayrollDetailUpload
*/
public interface HrmsNewsalaryPayrollDetailUploadMapper extends Mapper<HrmsNewsalaryPayrollDetailUploadEo> {


    List<HrmsNewsalaryPayrollDetailUploadEo> getAllList();

    List<Map<String, Object>> listTableData(@Param("record") HrmsNewsalaryPayrollDetailUploadEo record, @Param("allList")List<HrmsNewsalaryPayrollDetailUploadEo> allList, @Param("employeeList") List<String> employeeList);

    List<String> getEmployeeList(@Param("usercode") String usercode);

    List<String> queryOptionId(@Param("employeeNo")String employeeNo, @Param("itemId")String itemId);

    List<Map<String, Object>> listTableDataPage(Page page, @Param("record") HrmsNewsalaryPayrollDetailUploadEo record, @Param("allList")List<HrmsNewsalaryPayrollDetailUploadEo> allList, @Param("employeeList") List<String> employeeList);

    List<String> selectOrgInfo();

    List<Map<String, Object>> queryUploadList(@Param("record")  HrmsNewsalaryPayrollDetailUploadEo record, @Param("allList") List<HrmsNewsalaryPayrollDetailUploadEo> allList, @Param("employeeList") List<String> emploeeyList, @Param("codeList") List<String> codeList);

    List<Map<String, String>> selectEmpAll(@Param("emploeeyList") List<String> emploeeyList,@Param("record") HrmsNewsalaryPayrollDetailUploadEo record);

    List<String> getEmploeeIdByCode(@Param("usercode") String usercode,@Param("payrollDate")String payrollDate);

    List<HrmsNewsalaryPayrollDetailUploadEo> queryUploadInfo(@Param("payrollDate")String payrollDate, @Param("optionId") String optionId);

    List<HrmsNewsalaryPayrollDetailUploadEo> getItemList(@Param("payrollDate")String payrollDate);
}

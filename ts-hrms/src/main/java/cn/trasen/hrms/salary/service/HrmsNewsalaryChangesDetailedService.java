package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.model.HrmsAdvancementIncidentEo;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.salary.model.HrmsNewsalaryChangesDetailedEo;

import java.math.BigDecimal;
import java.util.List;

/**
*
 * <AUTHOR>
 */
public interface HrmsNewsalaryChangesDetailedService {

    String queryOrgName(String employeeId);

    void save(HrmsNewsalaryChangesDetailedEo hrmsNewsalaryChangesDetailedEo);

    List<HrmsNewsalaryChangesDetailedEo> getDataList(Page page, HrmsNewsalaryChangesDetailedEo entity);

    List<HrmsNewsalaryChangesDetailedEo> queryCancelList(HrmsAdvancementIncidentEo hrmsPersonnelIncident);

    void update(HrmsNewsalaryChangesDetailedEo hrmsNewsalaryChangesDetailedEo);

    /**
     * 更新本次异动记录对应的值
     * @param employee
     * @param basicItemId
     * @param effectiveDate
     * @param oldSalaryAmount 原值
     * @param salaryAmount 现值
     * @return
     */
    void updateNowValueByEffective(HrmsEmployee employee, String basicItemId, String effectiveDate,BigDecimal oldSalaryAmount, BigDecimal salaryAmount, String reason);
}

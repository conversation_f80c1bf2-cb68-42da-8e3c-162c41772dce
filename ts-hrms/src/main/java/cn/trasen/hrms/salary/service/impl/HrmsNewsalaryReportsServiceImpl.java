package cn.trasen.hrms.salary.service.impl;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportsVo;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryReportsMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryReportsEo;
import cn.trasen.hrms.salary.service.HrmsNewsalaryReportsService;
import cn.trasen.hrms.utils.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;

/**
 * 薪酬报表设置
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrmsNewsalaryOptionTotalTypeServiceImpl
 * @Description TODO
 * @date 2024  0723
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryReportsServiceImpl implements HrmsNewsalaryReportsService {

    @Autowired
    private HrmsNewsalaryReportsMapper mapper;

    @Override
	@Transactional(readOnly = false)
    public Integer save(HrmsNewsalaryReportsVo record) {
        record.setId(IdUtil.getId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getUsercode());
            record.setSsoOrgName(user.getUsername());
        }
        HrmsNewsalaryReportsEo HrmsNewsalaryReportTotalEo = copyObjectInfoEo(record);
        return mapper.insertSelective(HrmsNewsalaryReportTotalEo);
    }

    private HrmsNewsalaryReportsEo copyObjectInfoEo(HrmsNewsalaryReportsVo record) {
        HrmsNewsalaryReportsEo HrmsNewsalaryReportTotalEo = new HrmsNewsalaryReportsEo();
        BeanUtils.copyProperties(record, HrmsNewsalaryReportTotalEo);
        return HrmsNewsalaryReportTotalEo;
    }

    @Override
	@Transactional(readOnly = false)
    public Integer update(HrmsNewsalaryReportsVo record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        HrmsNewsalaryReportsEo HrmsNewsalaryReportTotalEo = copyObjectInfoEo(record);
        return mapper.updateByPrimaryKeySelective(HrmsNewsalaryReportTotalEo);
    }

    @Override
	@Transactional(readOnly = false)
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        HrmsNewsalaryReportsEo record = new HrmsNewsalaryReportsEo();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public DataSet<HrmsNewsalaryReportsVo> getDataSetList(Page page, HrmsNewsalaryReportsVo record) {
        List<HrmsNewsalaryReportsVo> records = mapper.getDataSetList(record, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public HrmsNewsalaryReportsEo getById(String report){
        return mapper.selectByPrimaryKey(report);
    }
}

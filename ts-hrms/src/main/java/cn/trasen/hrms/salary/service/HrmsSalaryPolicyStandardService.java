package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsSalaryPolicyStandard;

import java.util.List;

/**
 * @ClassName HrmsSalaryPolicyStandardService
 * @Description TODO
 * @date 2025��2��24�� ����11:15:17
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsSalaryPolicyStandardService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��2��24�� ����11:15:17
	 * <AUTHOR>
	 */
	Integer save(HrmsSalaryPolicyStandard record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��2��24�� ����11:15:17
	 * <AUTHOR>
	 */
	Integer update(HrmsSalaryPolicyStandard record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��2��24�� ����11:15:17
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsSalaryPolicyStandard
	 * @date 2025��2��24�� ����11:15:17
	 * <AUTHOR>
	 */
	HrmsSalaryPolicyStandard selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsSalaryPolicyStandard>
	 * @date 2025��2��24�� ����11:15:17
	 * <AUTHOR>
	 */
	DataSet<HrmsSalaryPolicyStandard> getDataSetList(Page page, HrmsSalaryPolicyStandard record);

	/**
	 * @Title getDataSetList
	 * @Description 查询政策标准列表
	 * @param record
	 * @return List<HrmsSalaryPolicyStandard>
	 * @date 2025��2��24�� ����11:15:17
	 * <AUTHOR>
	 */
	List<HrmsSalaryPolicyStandard> getList(HrmsSalaryPolicyStandard record);
}

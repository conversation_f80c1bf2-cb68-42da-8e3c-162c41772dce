/************************************************
* @功能描述: 工资计算公式专用解析工具类
* @Title: FormulaParse.java
* @Package cn.trasen.hrms.salary.utils
* <AUTHOR>
* @date 2024年6月26日 下午3:05:30
* @version V1.0
*************************************************/
package cn.trasen.hrms.salary.utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;

/**
* @ClassName: FormulaParse
* @Description: 薪资计算公式解析，抽成公共方法 
* <AUTHOR>
* @date 2024年6月26日 下午3:05:30
*
*/
public class FormulaParse {

	/**
	 * 
	* @功能描述: 公式递归解析成最终无表达式的计算公式； 解析计算过程中可能漏算的情况
	* @Title: getCountFormulaListLoop
	* @param @param inputStr
	* @param @param salaryItem
	* @param @return    参数
	* @return String    返回类型
	* @throws
	* <AUTHOR>
	* @date 2024年6月18日 下午1:57:56
	 */
	public static String getCountFormulaListLoop(String inputStr, List<HrmsNewsalaryItem> salaryItem) {
		// 深层解析的核心方法，使用一个Set来避免重复处理相同的元素
		return parseFormula(inputStr, salaryItem, new HashSet<>());
	}

	private static String parseFormula(String formula, List<HrmsNewsalaryItem> salaryItems, Set<String> processedElements) {
		Pattern pattern = Pattern.compile("\\{([^}]+)}");
		Matcher matcher = pattern.matcher(formula);
		StringBuffer result = new StringBuffer();
		while (matcher.find()) {
			String arrayString = matcher.group(1);
			String[] arrayElements = arrayString.split(",");
			StringBuilder replacement = new StringBuilder("{");

			for (String element : arrayElements) {
				if (!processedElements.contains(element)) {
					String tmpStr = element.replaceAll("\\[.*?\\]", "");
					Optional<HrmsNewsalaryItem> salaryItemOpt = salaryItems.stream()
							.filter(item -> item.getId().equals(tmpStr)).findFirst();
					if (salaryItemOpt.isPresent()) {
						String countFormula = salaryItemOpt.get().getCountFormula();
						if (countFormula != null && !countFormula.trim().isEmpty()) {
							processedElements.add(element);
							countFormula = parseFormula(countFormula, salaryItems, processedElements); // 递归解析
							replacement.append("(").append(countFormula).append("),");
						} else {
							replacement.append(element).append(",");
						}
					} else {
						replacement.append(element).append(",");
					}
				} else {
					replacement.append(element).append(",");
				}
			}

			// 去掉最后的逗号，并补上右括号
			if (replacement.length() > 1) {
				replacement.setLength(replacement.length() - 1);
			}
			replacement.append("}");

			matcher.appendReplacement(result, replacement.toString());
		}
		matcher.appendTail(result);

		return result.toString();
	}
	
	// 计算公式返回数组
	public static List<String> getCountFormulaList(String inputStr) {
		List<String> strList = new ArrayList<>();
		Pattern pattern = Pattern.compile("\\{([^}]+)}");
		Matcher matcher = pattern.matcher(inputStr);
		while (matcher.find()) {
			String arrayString = matcher.group(1);
			arrayString = arrayString.replaceAll("\\{", "");
			arrayString = arrayString.replaceAll("\\(", "");
			arrayString = arrayString.replaceAll("\\)", "");
			String[] arrayElements = arrayString.split(",");
			for (String element : arrayElements) {
				strList.add(element);
			}
		}
		return strList;
	}
	

	public static Map<String, String> getCountFormulaCode(String input) {
		Map<String, String> codeMap = null;
		Pattern pattern = Pattern.compile("^((\\d+)|(\\d+(\\.\\d+)))\\[(\\d+)\\]$");
		Matcher matcher = pattern.matcher(input);
		if (matcher.find()) {
			codeMap = new HashMap<>();
			String code = matcher.group(1);
			String index = matcher.group(5);
			codeMap.put("code", code);
			codeMap.put("type", index);
		}
		return codeMap;
	}

	/**
	 * 
	 * @功能描述: 自定义规则在定薪、调薪时进行金额处理
	 * @Title: custRuleParse
	 * @param @param  customRule
	 * @param @return 参数
	 * @return BigDecimal 返回类型
	 * @throws
	 * <AUTHOR>
	 * @date 2024年6月25日 下午6:53:53
	 */
	public static BigDecimal custRuleParse(String customRule) {
		String[] rules = customRule.split("=");
		// 判断当前规则为女工费
		if (rules.length == 3 && "sex".equalsIgnoreCase(rules[0]) && "2".equals(rules[1])) {
			return new BigDecimal(rules[2]);
		}
		// 后续规则可以继续扩展其它不同规是

		return new BigDecimal(0);
	}
}

package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.enums.NewsalaryTemporaryAdjustOpTypeEnum;
import cn.trasen.hrms.salary.model.HrmsNewsalaryTemporaryAdjust;

import java.util.List;
import java.util.Map;

/**
 * @ClassName HrmsNewsalaryTemporaryAdjustService
 * @Description TODO
 * @date 2024��10��8�� ����3:13:22
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryTemporaryAdjustService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��10��8�� ����3:13:22
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryTemporaryAdjust record,NewsalaryTemporaryAdjustOpTypeEnum opTypeEnum);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��10��8�� ����3:13:22
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryTemporaryAdjust record,NewsalaryTemporaryAdjustOpTypeEnum opTypeEnum);

	/**
	 * 根据薪酬调整项目类型和计算类型查询员工调整记录
	 * @param employeeId
	 * @param tmpItem
	 * @param optionCycle
	 * @param countType
	 * @return
	 */
	HrmsNewsalaryTemporaryAdjust getEmployeeTemporaryAdjustByItem(String employeeId,String tmpItem,String optionCycle,String countType);

	/**
	 * 根据员工id列表和算薪周期锁定薪酬调整
	 * @param employeeids
	 * @param computeDate
	 * @return
	 */
	Integer lockAdjuct(List<String> employeeids,String computeDate);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��10��8�� ����3:13:22
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryTemporaryAdjust
	 * @date 2024��10��8�� ����3:13:22
	 * <AUTHOR>
	 */
	HrmsNewsalaryTemporaryAdjust selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryTemporaryAdjust>
	 * @date 2024��10��8�� ����3:13:22
	 * <AUTHOR>
	 */
	List<HrmsNewsalaryTemporaryAdjust> getDataSetList(Page page, HrmsNewsalaryTemporaryAdjust record);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param record
	 * @return DataSet<HrmsNewsalaryTemporaryAdjust>
	 * @date 2024��10��8�� ����3:13:22
	 * <AUTHOR>
	 */
	List<HrmsNewsalaryTemporaryAdjust> getList(HrmsNewsalaryTemporaryAdjust record);

	/**
	 *
	 * @Title importEmployeeTemporary
	 * @Description 导入
	 * @return Map
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	Map<String,Integer> importData(List<HrmsNewsalaryTemporaryAdjust> list);
}

package cn.trasen.hrms.salary.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * 薪资调整表
 *
 */
@Table(name = "hrms_newsalary_temporary_adjust")
@Setter
@Getter
public class HrmsNewsalaryTemporaryAdjust {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 序号
     */
    @Transient
    @ApiModelProperty(value = "序号")
    private Integer no;

    /**
     * 算薪周期
     */
    @Excel(name = "算薪周期")
    @Column(name = "option_cycle")
    @ApiModelProperty(value = "算薪周期")
    private String optionCycle;

    /**
     * 人员id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "人员id")
    private String employeeId;

    /**
     * 人员工号
     */
    @Excel(name = "工号")
    @Column(name = "employee_no")
    @ApiModelProperty(value = "人员工号")
    private String employeeNo;

    @Excel(name = "姓名")
    @Transient
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    /**
     * 项目类型（字典表）
     */
    @Column(name = "tmp_item")
    @ApiModelProperty(value = "项目类型")
    private String tmpItem;


    /**
     * 项目类型（字典表）
     */
    @Excel(name = "项目类型")
    @Transient
    @ApiModelProperty(value = "项目类型（字典表）")
    private String tmpItemName;

    /**
     * 调整项金额
     */
    @Excel(name = "金额")
    @Column(name = "salary_item_amount")
    @ApiModelProperty(value = "调整项金额")
    private BigDecimal salaryItemAmount;

    /**
     * 计算类型 1-增项 2-减项
     */
    @Excel(name = "计算类型")
    @Column(name = "count_type")
    @ApiModelProperty(value = "计算类型")
    private String countType;

    /**
     * 是否已使用: 1=是; 2=否; 
     */
    @Column(name = "is_use")
    @ApiModelProperty(value = "是否已使用 ")
    private String isUse;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建时间字符串
     */
    @Transient
    @ApiModelProperty(value = "创建时间字符串")
    private String createDateStr;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 创建时间字符串
     */
    @Transient
    @ApiModelProperty(value = "创建时间字符串")
    private String updateDateStr;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否已删除")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    @Transient
    @ApiModelProperty(value = "性别")
    private String gender;

    /**
     * 组织机构id
     */
    @Transient
    @ApiModelProperty(value = "组织机构id")
    private String orgId;
    /**
     * 组织机构名称
     */
    @Excel(name = "所属组织")
    @Transient
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 岗位
     */
    @Transient
    @ApiModelProperty(value = "岗位")
    private String position;

    /**
     * 岗位
     */
    @Excel(name = "岗位名称")
    @Transient
    @ApiModelProperty(value = "岗位名称")
    private String positionName;

    @Transient
    @ApiModelProperty(value = "查询开始时间")
    private String searchStartTime;

    @Transient
    @ApiModelProperty(value = "查询结束时间")
    private String searchEndTime;

    @Transient
    @ApiModelProperty(value = "员工状态")
    private String employeeStatus;
}
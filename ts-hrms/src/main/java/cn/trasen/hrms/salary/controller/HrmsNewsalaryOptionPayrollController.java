package cn.trasen.hrms.salary.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.DTO.CheckPersonnel;
import cn.trasen.hrms.salary.DTO.SearchListTable;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOption;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionPayrollService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryPayrollDetailService;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import cn.trasen.hrms.utils.ExcelExportUtils;
import cn.trasen.hrms.utils.ExcelStyleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrmsNewsalaryOptionPayrollController
 * @Description 薪酬核算控制器
 * @date 2024��2��23�� ����11:39:46
 */
@RestController
@Api(tags = "薪酬核算controller")
public class HrmsNewsalaryOptionPayrollController {

    private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryOptionPayrollController.class);

    @Autowired
    private HrmsNewsalaryOptionPayrollService hrmsNewsalaryOptionPayrollService;

    @Resource
    private HrmsNewsalaryOptionMapper hrmsNewsalaryOptionMapper;

    @Autowired
    HrmsNewsalaryPayrollDetailService hrmsNewsalaryPayrollDetailService;

    @Autowired
    HrmsNewsalaryItemService hrmsNewsalaryItemService;


    //薪酬核算列表接口
    @ApiOperation(value = "薪酬核算页面列表", notes = "薪酬核算页面列表")
    @GetMapping("/api/newSalaryOptionPayroll/list")
    public DataSet<HrmsNewsalaryOptionPayroll> allList(Page page, HrmsNewsalaryOptionPayroll record) {
        return hrmsNewsalaryOptionPayrollService.calculateList(page, record);
    }


    //核对人员接口
    @ApiOperation(value = "人员确认接口", notes = "人员确认接口")
    @GetMapping("/api/newSalaryOptionPayroll/checkPersonnel")
    public DataSet<CheckPersonnel> checkPersonnel(Page page, SearchListTable record) {
        return hrmsNewsalaryOptionPayrollService.checkPersonnel(page, record);
    }

    //核对人员接口
    @ApiOperation(value = "薪酬核算定薪调薪表头接口", notes = "薪酬核算定薪调薪表头接口")
    @GetMapping("/api/newSalaryOptionPayroll/makePayTableTitle")
    public PlatformResult<List<VueTableEntity>> makePayTableTitle(HrmsNewsalaryOptionPayroll record) {
        try {
            List<VueTableEntity> list = hrmsNewsalaryOptionPayrollService.makePayTableTitle(record);
            return PlatformResult.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "薪酬核算定薪调薪数据接口", notes = "薪酬核算定薪调薪数据接口")
    @GetMapping("/api/newSalaryOptionPayroll/makePayTableData")
    public DataSet<Map<String, String>> makePayTableData(Page page, HrmsNewsalaryOptionPayroll record) {
        return hrmsNewsalaryOptionPayrollService.makePayTableData(page, record);
    }


    @ApiOperation(value = "薪酬核算-核算表头", notes = "薪酬核算-核算表头")
    @GetMapping("/api/newSalaryOptionPayroll/calculateWagesTitle")
    public PlatformResult<List<VueTableEntity>> calculateWagesTitle(HrmsNewsalaryOptionPayroll record) {
        try {
            List<VueTableEntity> list = hrmsNewsalaryOptionPayrollService.calculateWagesTitle(record);
            return PlatformResult.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "薪酬核算-核算数据", notes = "薪酬核算-核算数据")
    @GetMapping("/api/newSalaryOptionPayroll/calculateWagesData")
    public DataSet<Map<String, String>> calculateWagesData(Page page, HrmsNewsalaryOptionPayroll record) {
        return hrmsNewsalaryOptionPayrollService.calculateWagesData(page, record);
    }

    //薪酬核算第三部导出数据
    @ApiOperation(value = "月度薪酬汇总导出", notes = "月度薪酬汇总导出")
    @GetMapping("/api/newSalaryOptionPayroll/calculateWagesDataExport")
    public void calculateWagesDataExport(HttpServletRequest request, HttpServletResponse response, Page page, HrmsNewsalaryOptionPayroll record) {
        String optinId = record.getOptionId();
        HrmsNewsalaryOption hno = hrmsNewsalaryOptionMapper.selectByPrimaryKey(optinId);
        String filename = record.getComputeDate() + "【" + hno.getOptionName() + "】薪酬数据";
        page.setPageSize(Integer.MAX_VALUE);
        List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
        List<VueTableEntity> listTitle = hrmsNewsalaryOptionPayrollService.calculateWagesTitle(record);

        DataSet<Map<String, String>> mapDataSet = hrmsNewsalaryOptionPayrollService.calculateWagesData(page, record);
        List<Map<String, String>> rows = mapDataSet.getRows();
        //时间格式转换
        if (CollUtil.isNotEmpty(rows)) {
            rows.forEach(item -> {
                String upDate = MapUtil.getStr(item, "update_date");
                if (StrUtil.isNotBlank(upDate)) {
                    LocalDateTime updateDate = LocalDateTime.parse(upDate);
                    String formatDate = updateDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    item.put("update_date", formatDate);
                }
            });
        }
        for (VueTableEntity vueTableEntity : listTitle) {
        	ExcelExportEntity eee = new ExcelExportEntity(vueTableEntity.getLabel(), vueTableEntity.getProp());
			if (vueTableEntity.getIsNum()) {
				eee.setType(10);
			}
            colList.add(eee);
        }

        PlatformResult<HrmsNewsalaryOptionPayroll> payroll = getCalculationStatus(record);
        List<HrmsNewsalaryItem> salaryList;
        List<String> collect;
        if (null == payroll || "0".equals(payroll.getObject().getComputeStatus())) {
            salaryList = hrmsNewsalaryItemService.getItemByOptionId(record.getOptionId());
            record.setSalaryList(salaryList);
            collect = salaryList.stream().map(HrmsNewsalaryItem::getId).collect(Collectors.toList());
        }else {
            salaryList = hrmsNewsalaryPayrollDetailService.getCalculateWagesHistoryTitle(record);
            record.setSalaryList(salaryList);
            collect= salaryList.stream().map(HrmsNewsalaryItem::getItemId).collect(Collectors.toList());
        }

        List<Map<String, String>> newMap = new ArrayList<>();
        List<Map<String, Object>> objectMap = new ArrayList<>();
        Map<String, String> afterResult = new HashMap<>();
        if (CollectionUtils.isNotEmpty(rows)){
			for (Map<String, String> map : rows) {
				Map<String, Object> result = new HashMap<>();
				for (Map.Entry<String, String> entry : map.entrySet()) {
					result.put(entry.getKey(), entry.getValue() == null ? null : entry.getValue());
				}
				objectMap.add(result);
			}
			newMap.addAll(rows);
		}
        if (CollectionUtils.isNotEmpty(salaryList)) {

            Map<String, BigDecimal> resultMap = new HashMap<>();
            // 遍历objectMap中的每个Map
            for (Map<String, Object> map : objectMap) {
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    String key = entry.getKey();
                    if (collect.contains(key)) {
                        BigDecimal value = new BigDecimal(entry.getValue().toString()); // 强转为BigDecimal

                        // 如果resultMap中已有该key，累加其值；否则直接存入
                        resultMap.put(key, resultMap.getOrDefault(key, BigDecimal.ZERO).add(value));
                    }
                }
            }
            for (Map.Entry<String, BigDecimal> entry : resultMap.entrySet()) {
                afterResult.put(entry.getKey(), entry.getValue() == null ? null : entry.getValue().toString());
            }

            newMap.add(afterResult);
        }
        try {
            ExportParams exportParams =new ExportParams(filename, "薪酬数据", ExcelType.XSSF);
            exportParams.setStyle(ExcelStyleUtil.class);
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, newMap);
            // 最后一行的合并单元格
            Sheet sheet = workbook.getSheetAt(0);
            Row row = sheet.getRow(mapDataSet.getTotalCount() + 2);
            Cell cell = row.getCell(0);
            cell.setCellValue("合计：");
            //添加会签人员
            addSignRow(workbook,sheet,mapDataSet.getTotalCount());
            ExcelExportUtils.doubleStypeBug(workbook);
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename="
                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
            OutputStream fos = response.getOutputStream();
            workbook.write(fos);
            fos.close();
        } catch (Exception e) {
            logger.error("导出数据异常" + e.getMessage());
        }
    }

    private void addSignRow(Workbook workbook,Sheet sheet,int size){
        //添加会签的人员数据
        List<String> signUser = Arrays.asList("制表人","人事部","财务部","人事分管领导","财务分管领导");
        Row signRow = sheet.createRow(size + 4);
        int cellIdx = 0;
        Cell signCell = null;
        CellStyle signCellStyle = null;
        for(String user : signUser) {
            signCell = signRow.createCell(cellIdx);
            signCellStyle = workbook.createCellStyle();
            signCellStyle.setAlignment(HorizontalAlignment.LEFT); //水平居中
            signCellStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直居中
            signCellStyle.setBorderBottom(BorderStyle.THIN); //下边框
            signCellStyle.setBorderLeft(BorderStyle.THIN); //左边框
            signCellStyle.setBorderTop(BorderStyle.THIN);//上边框
            signCellStyle.setBorderRight(BorderStyle.THIN);//右边框
            signCell.setCellStyle(signCellStyle);
            signCell.setCellValue(user+":");

            signCell = signRow.createCell(cellIdx+1);
            signCellStyle = workbook.createCellStyle();
            signCellStyle.setAlignment(HorizontalAlignment.LEFT); //水平居中
            signCellStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直居中
            signCellStyle.setBorderBottom(BorderStyle.THIN); //下边框
            signCellStyle.setBorderLeft(BorderStyle.THIN); //左边框
            signCellStyle.setBorderTop(BorderStyle.THIN);//上边框
            signCellStyle.setBorderRight(BorderStyle.THIN);//右边框
            signCell.setCellStyle(signCellStyle);
            signCell.setCellValue(" ");
            cellIdx += 2;
        }
    }


    @ApiOperation(value = "薪酬核算-查询核算状态", notes = "薪酬核算-查询核算状态")
    @GetMapping("/api/newSalaryOptionPayroll/getCalculationStatus")
    public PlatformResult<HrmsNewsalaryOptionPayroll> getCalculationStatus(HrmsNewsalaryOptionPayroll record) {
        try {
            HrmsNewsalaryOptionPayroll bean = hrmsNewsalaryOptionPayrollService.getCalculationStatus(record);
            return PlatformResult.success(bean);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

//    //薪酬核算 --开始计算
//    @ApiOperation(value = "薪酬核算-开始计算", notes = "薪酬核算-开始计算")
//    @GetMapping("/api/newSalaryOptionPayroll/startCalculation")
//    public PlatformResult startCalculation(HrmsNewsalaryOptionPayroll record) {
//        try {
//             hrmsNewsalaryOptionPayrollService.startCalculation(record);
//            return PlatformResult.success();
//        } catch (Exception e) {
//            logger.error(e.getMessage(), e);
//            return PlatformResult.failure(e.getMessage());
//        }
//    }

    /**
     * 薪酬核算 --开始计算 (sse 异步返回处理进度)
     * update ni.jiang
     * @param record
     * @return
     */
    @ApiOperation(value = "薪酬核算-开始计算", notes = "薪酬核算-开始计算")
    @GetMapping(path="/api/newSalaryOptionPayroll/startCalculation",produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter  startCalculation(HrmsNewsalaryOptionPayroll record,HttpServletResponse response) {
        response.setHeader("Cache-Control", "no-cache");
        SseEmitter sseEmitter = hrmsNewsalaryOptionPayrollService.startCalculation(record);
        return sseEmitter;
    }

    //修改工资项查询接口
    @ApiOperation(value = "薪酬核算-修改工资项查询接口", notes = "薪酬核算-修改工资项查询接口")
    @GetMapping("/api/newSalaryOptionPayroll/getSalaryChangesData")
    public PlatformResult<Map<String, Object>> getSalaryChangesData(HrmsNewsalaryOptionPayroll record) {
        try {
            return PlatformResult.success(hrmsNewsalaryOptionPayrollService.getSalaryChangesData(record));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "薪酬核算-修改之后重新计算", notes = "薪酬核算-修改之后重新计算")
    @PostMapping("/api/newSalaryOptionPayroll/reloadStartCalculation")
    public PlatformResult<String> reloadStartCalculation(@RequestBody HrmsNewsalaryOptionPayroll record) {
        try {
            hrmsNewsalaryOptionPayrollService.reloadStartCalculation(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    //
    @ApiOperation(value = "薪酬核算-检查是否存在未处理薪酬提醒数据", notes = "薪酬核算-检查是否存在未处理薪酬提醒数据")
    @GetMapping("/api/newSalaryOptionPayroll/checkIsExistsUnprocessedRemindData")
    public PlatformResult<Integer> checkIsExistsUnprocessedRemindData(HrmsNewsalaryOptionPayroll record) {
        try {
            Integer count =  hrmsNewsalaryOptionPayrollService.checkIsExistsUnprocessedRemindData(record.getOptionId(),record.getComputeDate());
            return PlatformResult.success(count);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    // 完成核算接口
    @ApiOperation(value = "薪酬核算-完成核算操作", notes = "薪酬核算-完成核算操作")
    @GetMapping("/api/newSalaryOptionPayroll/complete")
    public PlatformResult<String> complete(HrmsNewsalaryOptionPayroll record) {
        try {
            record.setComputeStatus("2");
            hrmsNewsalaryOptionPayrollService.complete(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "薪酬核算-重启核算操作", notes = "薪酬核算-重启核算操作")
    @GetMapping("/api/newSalaryOptionPayroll/reloadComplete")
    public PlatformResult<String> reloadComplete(HrmsNewsalaryOptionPayroll record) {
        try {
            record.setComputeStatus("1");
            hrmsNewsalaryOptionPayrollService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "薪酬核算-锁定工资结果", notes = "薪酬核算-锁定工资结果")
    @GetMapping("/api/newSalaryOptionPayroll/lockSalary")
    public PlatformResult<String> lockSalary(HrmsNewsalaryOptionPayroll record) {
        try {
            record.setComputeStatus("3");
            hrmsNewsalaryOptionPayrollService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "薪酬核算-批量锁定工资结果", notes = "薪酬核算-批量锁定工资结果")
    @PostMapping("/api/newSalaryOptionPayroll/batchLockSalary")
    public PlatformResult batchLockSalary(@RequestBody List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return PlatformResult.failure("ids不能为空!");
        }
        try {
            hrmsNewsalaryOptionPayrollService.batchLockSalary(ids);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "薪酬核算-发放工资条", notes = "薪酬核算-发放工资条")
    @GetMapping("/api/newSalaryOptionPayroll/paySlip")
    public PlatformResult<String> paySlip(HrmsNewsalaryOptionPayroll record) {
        try {
            record.setPaySlip("1");
            hrmsNewsalaryOptionPayrollService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "薪酬核算-批量发放工资条", notes = "薪酬核算-批量发放工资条")
    @PostMapping("/api/newSalaryOptionPayroll/batchPayslip")
    public PlatformResult batchPayslip(@RequestBody List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return PlatformResult.failure("ids不能为空!");
        }
        try {
            hrmsNewsalaryOptionPayrollService.batchPayslip(ids);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveHrmsNewsalaryOptionPayroll
     * @Description 新增
     * @date 2024��2��23�� ����11:39:46
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/newSalaryOptionPayroll/save")
    public PlatformResult<String> saveHrmsNewsalaryOptionPayroll(@RequestBody HrmsNewsalaryOptionPayroll record) {
        try {
            hrmsNewsalaryOptionPayrollService.save(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateHrmsNewsalaryOptionPayroll
     * @Description 编辑
     * @date 2024��2��23�� ����11:39:46
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/newSalaryOptionPayroll/update")
    public PlatformResult<String> updateHrmsNewsalaryOptionPayroll(@RequestBody HrmsNewsalaryOptionPayroll record) {
        try {
            hrmsNewsalaryOptionPayrollService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<HrmsNewsalaryOptionPayroll>
     * @Title selectHrmsNewsalaryOptionPayrollById
     * @Description 根据ID查询
     * @date 2024��2��23�� ����11:39:46
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/newSalaryOptionPayroll/{id}")
    public PlatformResult<HrmsNewsalaryOptionPayroll> selectHrmsNewsalaryOptionPayrollById(@PathVariable String id) {
        try {
            HrmsNewsalaryOptionPayroll record = hrmsNewsalaryOptionPayrollService.selectById(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteHrmsNewsalaryOptionPayrollById
     * @Description 根据ID删除
     * @date 2024��2��23�� ����11:39:46
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/newSalaryOptionPayroll/delete/{id}")
    public PlatformResult<String> deleteHrmsNewsalaryOptionPayrollById(@PathVariable String id) {
        try {
            hrmsNewsalaryOptionPayrollService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    //核算人员导出
    @ApiOperation(value = "核算人员导出", notes = "核算人员导出")
    @GetMapping("/api/newSalaryOptionPayroll/hsryExport")
    public void hsryExport(Page page, SearchListTable record, HttpServletRequest request, HttpServletResponse response) {
        hrmsNewsalaryOptionPayrollService.hsryExport(page, record, request, response);
    }

    @ApiOperation(value = "核算人员统计", notes = "核算人员统计")
    @GetMapping("/api/newSalaryOptionPayroll/checkPersonnelCount")
    public PlatformResult<Map<String, Integer>> checkPersonnelCount(SearchListTable record) {
        Map<String, Integer> map = hrmsNewsalaryOptionPayrollService.checkPersonnelCount(record);
        return PlatformResult.success(map);
    }

    @ApiOperation(value = "薪酬核算定薪调薪统计", notes = "薪酬核算定薪调薪统计")
    @GetMapping("/api/newSalaryOptionPayroll/makePayTableDataStatis")
    public PlatformResult<Map<String, Map<String, Object>>> makePayTableDataStatis(HrmsNewsalaryOptionPayroll record) {
        Map<String, Map<String, Object>> map = hrmsNewsalaryOptionPayrollService.makePayTableDataStatis(record);
        return PlatformResult.success(map);
    }

    @ApiOperation(value = "人员确认添加人员列表", notes = "人员确认添加人员列表")
    @GetMapping("/api/newSalaryOptionPayroll/getCheperList")
    public DataSet<CheckPersonnel> getCheperList(Page page, SearchListTable record) {
        return hrmsNewsalaryOptionPayrollService.getCheperList(page, record);
    }

    @ApiOperation(value = "薪酬核算-薪酬数据确认表头", notes = "薪酬核算-薪酬数据确认表头")
    @GetMapping("/api/newSalaryOptionPayroll/salaryConfirmTitle")
    public PlatformResult<List<VueTableEntity>> salaryConfirmTitle(HrmsNewsalaryOptionPayroll record) {
        try {
            List<VueTableEntity> list = hrmsNewsalaryOptionPayrollService.salaryConfirmTitle(record);
            return PlatformResult.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "薪酬核算-薪酬数据确认数据", notes = "薪酬核算-薪酬数据确认数据")
    @GetMapping("/api/newSalaryOptionPayroll/salaryConfirmData")
    public DataSet<Map<String, String>> salaryConfirmData(Page page, HrmsNewsalaryOptionPayroll record) {
        return hrmsNewsalaryOptionPayrollService.salaryConfirmData(page, record);
    }

    @ApiOperation(value = "薪酬计算-预览", notes = "薪酬计算-预览")
    @GetMapping("/api/newSalaryOptionPayroll/preview/{optionId}/{employeeId}/{computeDate}")
    public PlatformResult<Map<String, Object>> preview(@PathVariable String optionId, @PathVariable String employeeId, @PathVariable String computeDate) {
        Map<String, Object> map = hrmsNewsalaryOptionPayrollService.preview(optionId, employeeId, computeDate);
        return PlatformResult.success(map);
    }
}

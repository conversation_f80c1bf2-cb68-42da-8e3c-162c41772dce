package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionsRemindRecord;

/**
 * @ClassName HrmsNewsalaryOptionsRemindRecordService
 * @Description TODO
 * @date 2024��10��29�� ����6:29:36
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryOptionsRemindRecordService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��10��29�� ����6:29:36
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryOptionsRemindRecord record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��10��29�� ����6:29:36
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryOptionsRemindRecord record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��10��29�� ����6:29:36
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryOptionsRemindRecord
	 * @date 2024��10��29�� ����6:29:36
	 * <AUTHOR>
	 */
	HrmsNewsalaryOptionsRemindRecord selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryOptionsRemindRecord>
	 * @date 2024��10��29�� ����6:29:36
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryOptionsRemindRecord> getDataSetList(Page page, HrmsNewsalaryOptionsRemindRecord record);

	/**
	 * 一键处理
	 * @param record
	 * @return
	 */
	Integer batchAdjust(HrmsNewsalaryOptionsRemindRecord record);
	/**
	 * 定时查询薪酬方案异动数据
	 */
	void newsalaryOptionsChange();
}

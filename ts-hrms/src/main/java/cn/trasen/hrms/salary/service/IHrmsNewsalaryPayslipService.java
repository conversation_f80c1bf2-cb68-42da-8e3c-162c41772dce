package cn.trasen.hrms.salary.service;


import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayslip;
import java.util.List;

public interface IHrmsNewsalaryPayslipService {
    /**
     * 根据薪酬方案获取工资条
     * @param optionId
     * @return
     */
    HrmsNewsalaryPayslip getPayslip(String optionId);

    /**
     * 工资条编辑
     * @param payslip
     */
    void save(HrmsNewsalaryPayslip payslip);

    /**
     * 工资条模板列表
     * @param page
     * @param payslip
     * @return
     */
    DataSet<HrmsNewsalaryPayslip> getDataList(Page page, HrmsNewsalaryPayslip payslip);

    /**
     * 工资条模板启用
     * @param ids
     */
    void enable(List<String> ids);

    /**
     * 工资条模板禁用
     * @param ids
     */
    void disEnable(List<String> ids);

    /**
     *工资条模板新增
     * @param payslip
     */
    void add(HrmsNewsalaryPayslip payslip);

    /**
     *工资条模板删除
     * @param payslip
     */
    void delete(HrmsNewsalaryPayslip payslip);

    /**
     * 根据方案id获取启用工资条信息
     * @param optionId
     * @return
     */
    HrmsNewsalaryPayslip getPaySlipByOptionId(String optionId);

    /**
     * 同步工资条模板名称
     */
    void sysSlipName(String optionId,String optionName);
}

package cn.trasen.hrms.salary.controller;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemLibrary;
import cn.trasen.hrms.salary.service.IHrmsNewSalaryItemLibraryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Api(tags = "薪酬项目库Controller")
public class HrmsNewsalaryItemLibraryController {

    private static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryItemLibraryController.class);

    @Autowired
    private IHrmsNewSalaryItemLibraryService hrmsNewSalaryItemLibraryService;

    @ApiOperation(value = "薪酬项目库列表", notes = "薪酬项目库列表")
    @GetMapping("/api/salaryItemLibrary/list")
    public DataSet<HrmsNewsalaryItemLibrary> list(Page page, HrmsNewsalaryItemLibrary record) {
        return hrmsNewSalaryItemLibraryService.getDataList(page,record);
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/salaryItemLibrary/update")
    public PlatformResult update(@RequestBody HrmsNewsalaryItemLibrary record) {
        try {
            hrmsNewSalaryItemLibraryService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "详情", notes = "详情")
    @PostMapping("/api/salaryItemLibrary/getById/{id}")
    public PlatformResult<HrmsNewsalaryItemLibrary> getById(@PathVariable String id) {
        HrmsNewsalaryItemLibrary library = hrmsNewSalaryItemLibraryService.getById(id);
        return PlatformResult.success(library);
    }

    @ApiOperation(value = "启用", notes = "启用")
    @PostMapping("/api/salaryItemLibrary/enalbe")
    public PlatformResult enalbe(@RequestBody List<String> ids) {
        try {
            if(CollUtil.isEmpty(ids)){
                return PlatformResult.failure("ids不能为空!");
            }
            hrmsNewSalaryItemLibraryService.enalbe(ids);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "禁用", notes = "禁用")
    @PostMapping("/api/salaryItemLibrary/disEnalbe")
    public PlatformResult disEnalbe(@RequestBody List<String> ids) {
        try {
            if(CollUtil.isEmpty(ids)){
                return PlatformResult.failure("ids不能为空!");
            }
            hrmsNewSalaryItemLibraryService.disEnalbe(ids);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/salaryItemLibrary/delete")
    public PlatformResult delete(@RequestBody HrmsNewsalaryItemLibrary record) {
        try {
            hrmsNewSalaryItemLibraryService.delete(record);
            return PlatformResult.success();
        }catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "引用薪酬项目", notes = "引用薪酬项目")
    @PostMapping("/api/salaryItemLibrary/importItem")
    public PlatformResult importItem(@RequestBody List<HrmsNewsalaryItemLibrary> record) {
        if(CollUtil.isEmpty(record)){
            return PlatformResult.failure("薪酬项目不能为空!");
        }
        try {
            hrmsNewSalaryItemLibraryService.importItem(record);
            return PlatformResult.success();
        }catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "自定义薪酬项目", notes = "自定义薪酬项目")
    @PostMapping("/api/salaryItemLibrary/save")
    public PlatformResult save(@RequestBody HrmsNewsalaryItemLibrary record) {
        try {
            hrmsNewSalaryItemLibraryService.save(record);
            return PlatformResult.success();
        }catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "自定义公式薪酬表", notes = "自定义公式薪酬表")
    @PostMapping("/api/salaryItemLibrary/getCustomItem")
    public PlatformResult<List<HrmsNewsalaryItemLibrary>> getCustomItem() {
        List<HrmsNewsalaryItemLibrary> list = hrmsNewSalaryItemLibraryService.getCustomItem();
        return PlatformResult.success(list);
    }

    @ApiOperation(value = "薪酬项目库启用列表", notes = "薪酬项目库启用列表")
    @GetMapping("/api/salaryItemLibrary/getEnableDataList")
    public DataSet<HrmsNewsalaryItemLibrary> getEnableDataList(Page page,HrmsNewsalaryItemLibrary record) {
        return hrmsNewSalaryItemLibraryService.getEnableDataList(page,record);
    }

    @ApiOperation(value = "获取最新项目编码", notes = "获取最新项目编码")
    @GetMapping("/api/salaryItemLibrary/getLibraryCode/{itemSource}")
    public PlatformResult<String> getLibraryCode(@PathVariable String itemSource) {
        String libraryCode = hrmsNewSalaryItemLibraryService.getLibraryCode(itemSource);
        return PlatformResult.success(libraryCode);
    }
}

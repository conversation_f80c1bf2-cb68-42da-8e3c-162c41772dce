package cn.trasen.hrms.salary.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmpHistory;
import cn.trasen.hrms.salary.utils.VueTableEntity;

/**
 * @ClassName HrmsNewsalaryBasicitemEmpHistoryService
 * @Description TODO
 * @date 2023��11��11�� ����4:33:03
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryBasicitemEmpHistoryService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:33:03
	 * <AUTHOR>
	 */
	Integer save(List<HrmsNewsalaryBasicitemEmpHistory> records);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:33:03
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryBasicitemEmpHistory record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��11��11�� ����4:33:03
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryBasicitemEmpHistory
	 * @date 2023��11��11�� ����4:33:03
	 * <AUTHOR>
	 */
	HrmsNewsalaryBasicitemEmpHistory selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryBasicitemEmpHistory>
	 * @date 2023��11��11�� ����4:33:03
	 * <AUTHOR>
	 */
	List<Map<String,Object>>  getDataSetList(HrmsNewsalaryBasicitemEmpHistory record);

	/**
	 * 根据员工id批次号查询记录
	 * @param employeeId
	 * @param no
	 * @return
	 */
	List<HrmsNewsalaryBasicitemEmpHistory> getEmployeeIdAndNo(String employeeId, String no);

	List<VueTableEntity> headlist(String employeeId);


	//根据员工id修改 已生效的数据为过期数据
	Integer updateExpired (String employeeId,String basicItemId);

	HrmsNewsalaryBasicitemEmpHistory getFinallYByEmployeeId(String id);

	//查詢当前定薪记录
	Map<String,Object> currentSalary(HrmsNewsalaryBasicitemEmpHistory record);

	//撤销调薪
	Integer cancel(HrmsNewsalaryBasicitemEmpHistory record);

	List<HrmsNewsalaryBasicitemEmpHistory> getEmployeeIdAndEffectiveDate(String employeeId, String effectiveDate);

	Integer deleteByEmployeeIdAndDate(String employeeId, String effectiveDate);

	Integer updateDate(List<HrmsNewsalaryBasicitemEmpHistory> records);

	//获取所有要生效的人员
	List<HrmsNewsalaryBasicitemEmpHistory> getEmpTakeEffect(String takeEffect);

	Integer updateSetTakeEffect(List<HrmsNewsalaryBasicitemEmpHistory> historyList);

	/**
	 * 获取员工未生效定薪历史数据
	 * @param employeeId
	 * @param effectiveDate
	 * @return
	 */
	List<HrmsNewsalaryBasicitemEmpHistory> getEmpIneffectiveItemByEmpIdAndEffect(String employeeId, String effectiveDate);

	/**
	 * 更新本次人员定薪调薪人员表历史表数据
	 * @param employeeId
	 * @param basicItemId
	 * @param effectiveDate
	 * @param salaryAmount
	 * @return
	 */
	Integer updateEmpHisttorySalaryItemByEmpIdAndEffect(String employeeId, String basicItemId, String effectiveDate, BigDecimal salaryAmount);
}

package cn.trasen.hrms.salary.service.impl;

import java.util.Date;
import java.util.List;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.hrms.salary.dao.HrmsNewsalaryItemRemindSettingMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemRemindSetting;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemRemindSettingService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @ClassName HrmsNewsalaryItemRemindSettingServiceImpl
 * @Description TODO
 * @date 2024��10��29�� ����9:55:31
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryItemRemindSettingServiceImpl implements HrmsNewsalaryItemRemindSettingService {

	@Resource
	private HrmsNewsalaryItemRemindSettingMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryItemRemindSetting record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryItemRemindSetting record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryItemRemindSetting record = new HrmsNewsalaryItemRemindSetting();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteByRemindId(String remindId) {
		Assert.hasText(remindId, "提醒设置ID不能为空.");
		HrmsNewsalaryItemRemindSetting record = new HrmsNewsalaryItemRemindSetting();
		record.setRemindId(remindId);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		Example example = new Example(HrmsNewsalaryItemRemindSetting.class);
		Example.Criteria criteria = example.createCriteria();
		if(record!= null && record.getRemindId()!=null){
			criteria.andEqualTo("remindId",record.getRemindId());
		}
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return mapper.updateByExampleSelective(record,example);
	}

	@Override
	public HrmsNewsalaryItemRemindSetting selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalaryItemRemindSetting> getDataSetList(Page page, HrmsNewsalaryItemRemindSetting record) {
		List<HrmsNewsalaryItemRemindSetting> records = mapper.getList(page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsNewsalaryItemRemindSetting> getList(HrmsNewsalaryItemRemindSetting record) {
		List<HrmsNewsalaryItemRemindSetting> records = mapper.getList(record);
		return records;
	}


}

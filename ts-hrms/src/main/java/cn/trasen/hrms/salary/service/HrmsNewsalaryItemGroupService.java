package cn.trasen.hrms.salary.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemGroup;

/**
 * @ClassName HrmsNewsalaryItemGroupService
 * @Description TODO
 * @date 2023��11��11�� ����4:34:59
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryItemGroupService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:34:59
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryItemGroup record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:34:59
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryItemGroup record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��11��11�� ����4:34:59
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryItemGroup
	 * @date 2023��11��11�� ����4:34:59
	 * <AUTHOR>
	 */
	HrmsNewsalaryItemGroup selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryItemGroup>
	 * @date 2023��11��11�� ����4:34:59
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryItemGroup> getDataSetList(Page page, HrmsNewsalaryItemGroup record);

    Integer copy(HrmsNewsalaryItemGroup record);

	//根据薪酬组id查询分组和下面的栏目
	List<HrmsNewsalaryItemGroup> listByOptionId(HrmsNewsalaryItemGroup record);

	//导入工资项
	Integer importItem(String groupId,List<HrmsNewsalaryItem> record);

	List<HrmsNewsalaryItemGroup> listByOptionIdSel(HrmsNewsalaryItemGroup record);

	//修改工资条模板分组显示
	void updateArticle(HrmsNewsalaryItemGroup record);

	/**
	 * 根据方案id查询薪酬项分组
	 * @param optionId
	 * @return
	 */
	List<HrmsNewsalaryItemGroup> getSalaryGroupByOptionId(String optionId);
}

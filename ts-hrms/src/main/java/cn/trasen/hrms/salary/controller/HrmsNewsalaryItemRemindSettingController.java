package cn.trasen.hrms.salary.controller;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.hrms.salary.model.HrmsNewsalaryItemRemindSetting;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemRemindSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalaryItemRemindSettingController
 * @Description TODO
 * @date 2024��10��29�� ����9:55:31
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "hrmsNewsalaryItemRemindSettingController")
public class HrmsNewsalaryItemRemindSettingController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryItemRemindSettingController.class);

	@Autowired
	private HrmsNewsalaryItemRemindSettingService hrmsNewsalaryItemRemindSettingService;

	/**
	 * @Title saveHrmsNewsalaryItemRemindSetting
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/hrmsNewsalaryItemRemindSetting/save")
	public PlatformResult<String> saveHrmsNewsalaryItemRemindSetting(@RequestBody HrmsNewsalaryItemRemindSetting record) {
		try {
			hrmsNewsalaryItemRemindSettingService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryItemRemindSetting
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/hrmsNewsalaryItemRemindSetting/update")
	public PlatformResult<String> updateHrmsNewsalaryItemRemindSetting(@RequestBody HrmsNewsalaryItemRemindSetting record) {
		try {
			hrmsNewsalaryItemRemindSettingService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryItemRemindSettingById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryItemRemindSetting>
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/hrmsNewsalaryItemRemindSetting/{id}")
	public PlatformResult<HrmsNewsalaryItemRemindSetting> selectHrmsNewsalaryItemRemindSettingById(@PathVariable String id) {
		try {
			HrmsNewsalaryItemRemindSetting record = hrmsNewsalaryItemRemindSettingService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryItemRemindSettingById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/hrmsNewsalaryItemRemindSetting/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryItemRemindSettingById(@PathVariable String id) {
		try {
			hrmsNewsalaryItemRemindSettingService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryItemRemindSettingList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryItemRemindSetting>
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/hrmsNewsalaryItemRemindSetting/list")
	public DataSet<HrmsNewsalaryItemRemindSetting> selectHrmsNewsalaryItemRemindSettingList(Page page, HrmsNewsalaryItemRemindSetting record) {
		return hrmsNewsalaryItemRemindSettingService.getDataSetList(page, record);
	}
}

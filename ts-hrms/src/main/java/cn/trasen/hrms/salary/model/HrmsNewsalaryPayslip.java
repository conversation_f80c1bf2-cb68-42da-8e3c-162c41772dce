package cn.trasen.hrms.salary.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 工资条模版
 *
 */
@Table(name = "hrms_newsalary_payslip")
@Setter
@Getter
public class HrmsNewsalaryPayslip {

    @Id
    private String id;

    /**
     * 薪酬组id
     */
    @Column(name = "option_id")
    @ApiModelProperty(value = "薪酬组id")
    private String optionId;

    /**
     * 薪酬方案名称
     */
    @Column(name = "option_name")
    @ApiModelProperty(value = "薪酬方案名称")
    private String optionName;

    /**
     * 工资条名称
     */
    @Column(name = "slip_name")
    @ApiModelProperty(value = "工资条名称")
    private String slipName;

    /**
     * 提示语
     */
    @Column(name = "hint")
    @ApiModelProperty(value = "提示语")
    private String hint;

    /**
     * 是否启用 1是,2否
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用 1是,2否")
    private String isEnable;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    @Transient
    @ApiModelProperty(value = "工资项")
    List<HrmsNewsalaryItemGroup> salaryItemGroup;

    @Transient
    @ApiModelProperty(value = "隐藏空数据项")
    private List<String> hidden;

    @Transient
    @ApiModelProperty(value = "合计项列表")
    private List<HrmsNewsalaryItem> totalItems = new ArrayList<>();

    @Transient
    @ApiModelProperty(value = "应发工资")
    private String shSalary;

    @Transient
    @ApiModelProperty(value = "实发工资")
    private String show;

    @Transient
    @ApiModelProperty(value = "个税")
    private String personalTax;
}

package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.hrms.bean.RequestContent;
import cn.trasen.hrms.interfaceRegister.dao.CommInterfaceRegisterMapper;
import cn.trasen.hrms.interfaceRegister.model.CommInterfaceLogs;
import cn.trasen.hrms.interfaceRegister.model.CommInterfaceRegister;
import cn.trasen.hrms.interfaceRegister.service.CommInterfaceLogsService;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.salary.DTO.*;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryBasicColumnMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionPayrollMapper;
import cn.trasen.hrms.salary.enums.SalaryBaseColumnEnum;
import cn.trasen.hrms.salary.model.*;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicColumnService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionEmpService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionService;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.utils.IdUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrmsNewsalaryBasicColumnServiceImpl
 * @Description TODO
 * @date 2023��11��11�� ����4:28:57
 */
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryBasicColumnServiceImpl implements HrmsNewsalaryBasicColumnService {

	@Resource
	private HrmsNewsalaryBasicColumnMapper mapper;
	@Autowired
	DictItemFeignService dictItemFeignService;
	@Autowired
	HrmsNewsalaryItemService hrmsNewsalaryItemService;

	@Autowired
	HrmsNewsalaryOptionEmpService hrmsNewsalaryOptionEmpService;

	@Autowired
	HrmsEmployeeService hrmsEmployeeService;

	@Autowired
	HrmsNewsalaryOptionService hrmsNewsalaryOptionService;

	@Resource
	private HrmsNewsalaryOptionMapper optionMapper;

    @Resource
    private HrmsNewsalaryOptionPayrollMapper newsalaryOptionPayrollMapper;

	@Resource
	private CommInterfaceRegisterMapper commInterfaceRegisterMapper;

	@Autowired
	private CommInterfaceLogsService commInterfaceLogsService;

	@Autowired
	GlobalSettingsFeignService globalSettingsFeignService;

//    @Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryBasicColumn record) {

		// TODO 判断名称是不是存在
		Example example = new Example(HrmsNewsalaryBasicColumn.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("basicItemName", record.getBasicItemName());
		List<HrmsNewsalaryBasicColumn> records = mapper.selectByExample(example);

		if (records != null && records.size() > 0) {
			throw new BusinessException("项目不能重复添加");
		}
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

//    @Transactional(readOnly = false)
	@Override
	public Integer updateNumberSort(List<HrmsNewsalaryBasicColumn> records) {
		if (!records.isEmpty()) {
			records.forEach(item -> {
				// 防止偷袭 创建一个对象操作
				HrmsNewsalaryBasicColumn _bean = new HrmsNewsalaryBasicColumn();
				_bean.setId(item.getId());
				_bean.setNumberSort(item.getNumberSort());
				mapper.updateByPrimaryKeySelective(_bean);
			});
			return records.size();
		}
		return null;
	}

	@Override
	public List<HrmsNewsalaryBasicColumn> getBasicSalary() {

		Example example = new Example(HrmsNewsalaryBasicColumn.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("basicItemType", "2"); // 只查询工资项
		example.setOrderByClause(" number_sort asc ");
		List<HrmsNewsalaryBasicColumn> records = mapper.selectByExample(example);
		return records;
	}

	// @Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryBasicColumn record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	// @Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {

		// 判断是否有方案引用了这个
		boolean isUse = hrmsNewsalaryItemService.checkUse(id);
		if (isUse) {
			throw new BusinessException("项目已被引用，不能删除");
		}
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryBasicColumn record = new HrmsNewsalaryBasicColumn();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalaryBasicColumn selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalaryBasicColumn> getDataSetList(Page page, HrmsNewsalaryBasicColumn record) {
		Example example = new Example(HrmsNewsalaryBasicColumn.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		example.setOrderByClause(" number_sort asc ");
		List<HrmsNewsalaryBasicColumn> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsNewsalaryBasicColumn> getAllList() {
		Example example = new Example(HrmsNewsalaryBasicColumn.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		example.setOrderByClause(" number_sort ");
		return mapper.selectByExample(example);
	}

	@Override
	public List<HrmsNewsalaryBasicColumn> getAllListBaType() {
		Example example = new Example(HrmsNewsalaryBasicColumn.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("basicItemType", "2");
		example.setOrderByClause(" number_sort ");
		return mapper.selectByExample(example);
	}

	@Override
	public List<HrmsNewsalaryBasicColumn> getAllListBaType23() {
		List<String> basicItemType = new ArrayList<>();
		basicItemType.add("2");
		basicItemType.add("3");
		Example example = new Example(HrmsNewsalaryBasicColumn.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andIn("basicItemType", basicItemType);
		example.setOrderByClause(" number_sort ");
		return mapper.selectByExample(example);
	}

	/**
	 * 薪酬档案列表表头
	 *
	 * @return
	 */
	@Override
	public List<VueTableEntity> listTableTitle() {
		List<VueTableEntity> retVueTableEntity = new ArrayList<>();
		// 根据分组查询所有数据
		List<BasicItemVO> retmap = mapper.listTableTitle();
		if (!retmap.isEmpty() && retmap.size() > 0) {
			retmap.forEach(item -> {
				if ("姓名".equals(item.getBasicItemName())) {
					retVueTableEntity
							.add(new VueTableEntity(item.getBasicItemName(), "employee_name", null, null, null));
				} else if ("工号".equals(item.getBasicItemName())) {
					retVueTableEntity.add(new VueTableEntity(item.getBasicItemName(), "employee_no", null, null, null));
				} else if ("部门".equals(item.getBasicItemName())) {
					retVueTableEntity.add(new VueTableEntity(item.getBasicItemName(), "org_name", null, null, null));
				} else if ("员工状态".equals(item.getBasicItemName())) {
					retVueTableEntity
							.add(new VueTableEntity(item.getBasicItemName(), "employee_status", null, null, null));
				} else if ("岗位名称".equals(item.getBasicItemName())) {
					retVueTableEntity
							.add(new VueTableEntity(item.getBasicItemName(), "personal_identity", null, null, null));
				} else if ("编制类型".equals(item.getBasicItemName())) {
					retVueTableEntity
							.add(new VueTableEntity(item.getBasicItemName(), "establishment_type", null, null, null));
				} else if ("入职日期".equals(item.getBasicItemName())) {
					retVueTableEntity.add(new VueTableEntity(item.getBasicItemName(), "entry_date", null, null, null));
				} else if ("转正日期".equals(item.getBasicItemName())) {
					retVueTableEntity
							.add(new VueTableEntity(item.getBasicItemName(), "positive_time", null, null, null));
				} else if ("离退休日期".equals(item.getBasicItemName())) {
					retVueTableEntity
							.add(new VueTableEntity(item.getBasicItemName(), "retirement_time", null, 120, null));
				} else if ("身份证号".equals(item.getBasicItemName())) {
					retVueTableEntity
							.add(new VueTableEntity(item.getBasicItemName(), "identity_number", null, 120, null));
				} else if ("银行卡号".equals(item.getBasicItemName())) {
					retVueTableEntity.add(new VueTableEntity(item.getBasicItemName(), "bankcardno", null, 120, null));
				} else {
					int width = 20;
					if ("薪酬组".equals(item.getBasicItemName())) {
						retVueTableEntity.add(new VueTableEntity(item.getBasicItemName(), "option_name", null,
								item.getBasicItemName().length() == 2 ? 80 : item.getBasicItemName().length() * width,
								null));
					} else {
						if ("1".equals(item.getBasicItemType())) { // 需要数字格式
							retVueTableEntity.add(new VueTableEntity(item.getBasicItemName(), item.getBasicItemId(),
									null, item.getBasicItemName().length() == 2 ? 80
											: item.getBasicItemName().length() * width,
									null));
						} else {
							retVueTableEntity.add(new VueTableEntity(item.getBasicItemName(), item.getBasicItemId(),
									null, item.getBasicItemName().length() == 2 ? 80
											: item.getBasicItemName().length() * width,
									true, null,null));
						}
					}
				}
			});
		}
		retVueTableEntity.add(new VueTableEntity("最近修改人", "update_user_name", null, 90, null));
		retVueTableEntity.add(new VueTableEntity("最近修改日期", "update_date", null, 160, null));
		return retVueTableEntity;
	}

	/**
	 * 薪酬档案列表数据
	 *
	 * @param page
	 * @param record
	 * @return
	 */
	@Override
	public DataSet<Map<String, String>> listTableData(Page page, SearchListTable record) {
		long being = System.currentTimeMillis();
		Map<String, String> employeeStatusDictMap = convertDictMap("employee_status"); // 员工状态
		Map<String, String> personalIdentityDictMap = convertDictMap("personal_identity"); // 岗位名称
		Map<String, String> establishmentTypeDictMap = convertDictMap("establishment_type"); // 编制类型

		// 获取所有列 当参数传进去用于 case when
		List<HrmsNewsalaryBasicColumn> allList = getAllList();
		List<String> basicItemIds = allList.stream().map(item -> item.getId()).collect(Collectors.toList());
		record.setBasicItemIds(basicItemIds);
		System.err.println(System.currentTimeMillis() - being);
		List<Map<String, String>> rows = mapper.listTableData(page, record);
		System.err.println(System.currentTimeMillis() - being);
		// TODO 处理数据， 薪级 岗位等级 ，字典等值
		if (!rows.isEmpty() && !allList.isEmpty()) {
			for (int i = 0; i < rows.size(); i++) {
				if (!StringUtil.isEmpty(rows.get(i).get("employee_status"))) {
					rows.get(i).put("employee_status", employeeStatusDictMap.get(rows.get(i).get("employee_status")));
				}
				if (!StringUtil.isEmpty(rows.get(i).get("personal_identity"))) {
					rows.get(i).put("personal_identity",
							personalIdentityDictMap.get(rows.get(i).get("personal_identity")));
				}
				if (!StringUtil.isEmpty(rows.get(i).get("establishment_type"))) {
					rows.get(i).put("establishment_type",
							establishmentTypeDictMap.get(rows.get(i).get("establishment_type")));
				}
			}
		}
		System.err.println(System.currentTimeMillis() - being);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), rows);
	}

	/**
	 * @param @param  page
	 * @param @param  record
	 * @param @return 参数
	 * @return DataSet<Map < String, String>> 返回类型
	 * @throws
	 * @功能描述: 员工个人薪酬台帐
	 * @Title: EmpPayrollData
	 * <AUTHOR>
	 * @date 2024年6月21日 下午4:21:45
	 */
	@Override
	public DataSet<Map<String, String>> EmpPayrollData(Page page, SchEmpSalary record) {
		Map<String, String> employeeStatusDictMap = convertDictMap("employee_status"); // 员工状态
		Map<String, String> personalIdentityDictMap = convertDictMap("personal_identity"); // 岗位名称
		Map<String, String> establishmentTypeDictMap = convertDictMap("establishment_type"); // 编制类型
		// 获取所有列 当参数传进去用于 case when
		Example example = new Example(HrmsNewsalaryBasicColumn.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("basicItemType", "1");
		example.setOrderByClause(" number_sort ");
		List<HrmsNewsalaryBasicColumn> allList = mapper.selectByExample(example);

//        List<String> basicItemIds = allList.stream().map(item -> item.getId()).collect(Collectors.toList());
//        Map<String, String> basicItemIds = allList.stream().collect(Collectors.toMap(item->item.getId(),item->item.getBasicItemName()));
//        List<Map.Entry<String, String>> entryList = basicItemIds.entrySet().stream().collect(Collectors.toList());
		record.setBasicItemIds(allList);
		List<Map<String, String>> rows = mapper.listEmpSalaryData(page, record);
		// TODO 处理数据， 薪级 岗位等级 ，字典等值
		if (!rows.isEmpty() && !allList.isEmpty()) {
			for (int i = 0; i < rows.size(); i++) {
				if (!StringUtil.isEmpty(rows.get(i).get("employee_status"))) {
					rows.get(i).put("employee_status", employeeStatusDictMap.get(rows.get(i).get("employee_status")));
				}
				if (!StringUtil.isEmpty(rows.get(i).get("personal_identity"))) {
					rows.get(i).put("personal_identity",
							personalIdentityDictMap.get(rows.get(i).get("personal_identity")));
				}
				if (!StringUtil.isEmpty(rows.get(i).get("establishment_type"))) {
					rows.get(i).put("establishment_type",
							establishmentTypeDictMap.get(rows.get(i).get("establishment_type")));
				}
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), rows);
	}

	@Override
	public DataSet<SalaryLedgerListVo> listSalaryLedgerData(Page page, SearchSalaryLedgerVo record) {
		long being = System.currentTimeMillis();
		Map<String, String> personalIdentityDictMap = convertDictMap(SalaryBaseColumnEnum.ITEM_TYPE_10.getKey()); // 岗位名称
		System.err.println(System.currentTimeMillis() - being);
		String orgRang = UserInfoHolder.getCurrentUserInfo().getSysRoleCode();
		if (!orgRang.contains("TZ-ADMIN")) {
			ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
			record.setEmployeeName(currentUserInfo.getUsername());
		}
		List<SalaryLedgerListVo> rows = mapper.listSalaryLedgerData(page, record);
		System.err.println(System.currentTimeMillis() - being);
		// 处理数据， 薪级 岗位等级 ，字典等值
		if (CollectionUtils.isNotEmpty(rows)) {
			for (SalaryLedgerListVo ledgerListVo : rows) {
				if (Objects.nonNull(ledgerListVo.getPersonalIdentity())) {
					ledgerListVo.setPersonalIdentity(personalIdentityDictMap.get(ledgerListVo.getPersonalIdentity()));
				}
				if (Objects.nonNull(ledgerListVo.getPlgw())) {
					String salaryLevelName = mapper.getSalaryLevelName(ledgerListVo.getEmployeeId(),
							ledgerListVo.getPlgw(), SalaryBaseColumnEnum.ITEM_TYPE_11.getKey());
					ledgerListVo.setPlgw(salaryLevelName);
				}
				if (Objects.nonNull(ledgerListVo.getGwdj())) {
					String salaryLevelName = mapper.getSalaryLevelName(ledgerListVo.getEmployeeId(),
							ledgerListVo.getGwdj(), SalaryBaseColumnEnum.ITEM_TYPE_12.getKey());
					ledgerListVo.setGwdj(salaryLevelName);
				}
				if (Objects.nonNull(ledgerListVo.getSalaryLevelId())) {
					String salaryLevelName = mapper.getSalaryLevelName(ledgerListVo.getEmployeeId(),
							ledgerListVo.getSalaryLevelId(), SalaryBaseColumnEnum.ITEM_TYPE_14.getKey());
					ledgerListVo.setSalaryLevelId(salaryLevelName);
				}
				if (Objects.nonNull(ledgerListVo.getSalaryLevelType())) {
					String salaryLevelType = mapper.getSalaryLevelName(ledgerListVo.getEmployeeId(),
							ledgerListVo.getSalaryLevelType(), SalaryBaseColumnEnum.ITEM_TYPE_13.getKey());
					ledgerListVo.setSalaryLevelType(salaryLevelType);
				}
				ledgerListVo.setSalary(mapper.getSalary(ledgerListVo.getEmployeeId(), ledgerListVo.getPayrollDate()));
			}
		}
		System.err.println(System.currentTimeMillis() - being);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), rows);
	}

	public List<VueTableEntity> EmpPayrollTitle() {
		List<VueTableEntity> retVueTableEntity = new ArrayList<>();
		// 根据分组查询所有数据
		List<BasicItemVO> retmap = mapper.listTableTitle();
		if (!retmap.isEmpty() && retmap.size() > 0) {
			retmap.forEach(item -> {

				if ("姓名".equals(item.getBasicItemName())) {
					retVueTableEntity
							.add(new VueTableEntity(item.getBasicItemName(), "employee_name", null, null, null));
				} else if ("工号".equals(item.getBasicItemName())) {
					retVueTableEntity.add(new VueTableEntity(item.getBasicItemName(), "employee_no", null, null, null));
				} else if ("部门".equals(item.getBasicItemName())) {
					retVueTableEntity.add(new VueTableEntity(item.getBasicItemName(), "org_name", null, null, null));
				} else if ("员工状态".equals(item.getBasicItemName())) {
					retVueTableEntity
							.add(new VueTableEntity(item.getBasicItemName(), "employee_status", null, null, null));
				} else if ("岗位名称".equals(item.getBasicItemName())) {
					retVueTableEntity
							.add(new VueTableEntity(item.getBasicItemName(), "personal_identity", null, null, null));
				} else if ("编制类型".equals(item.getBasicItemName())) {
					retVueTableEntity
							.add(new VueTableEntity(item.getBasicItemName(), "establishment_type", null, null, null));
				} else if ("入职日期".equals(item.getBasicItemName())) {
					retVueTableEntity.add(new VueTableEntity(item.getBasicItemName(), "entry_date", null, null, null));
				} else if ("转正日期".equals(item.getBasicItemName())) {
					retVueTableEntity
							.add(new VueTableEntity(item.getBasicItemName(), "positive_time", null, null, null));
				} else if ("离退休日期".equals(item.getBasicItemName())) {
					retVueTableEntity
							.add(new VueTableEntity(item.getBasicItemName(), "retirement_time", null, 120, null));
				} else {
					if ("薪酬组".equals(item.getBasicItemName())) {
						retVueTableEntity
								.add(new VueTableEntity(item.getBasicItemName(), "option_name", null, null, null));
					} else {
						retVueTableEntity.add(
								new VueTableEntity(item.getBasicItemName(), item.getBasicItemId(), null, 120, null));
					}
				}
			});
		}
		retVueTableEntity.add(new VueTableEntity("最近修改人", "update_user_name", null, 120, null));
		retVueTableEntity.add(new VueTableEntity("最近修改日期", "update_date", null, 120, null));
		return retVueTableEntity;
	}

	@Override
	public List<Map<String, String>> makePayTableData(Page page, HrmsNewsalaryOptionPayroll record) {
		List<HrmsNewsalaryBasicColumn> allList = getAllList();
		List<String> basicItemIds = allList.stream().map(item -> item.getId()).collect(Collectors.toList());
		record.setBasicItemIds(basicItemIds);
		//传入当前登录账号机构编码过滤数据字典
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<Map<String, String>> rows = mapper.makePayTableData(page, record);
		return rows;
	}

	@Override
	public List<Map<String, Object>> makePayTableDataCount(Page page, HrmsNewsalaryOptionPayroll record) {
		Map<String, String> employeeStatusDictMap = convertDictMap("employee_status"); // 员工状态
		Map<String, String> personalIdentityDictMap = convertDictMap("personal_identity"); // 岗位名称
		Map<String, String> establishmentTypeDictMap = convertDictMap("establishment_type"); // 编制类型

		List<HrmsNewsalaryBasicColumn> allList = getAllList();
		List<String> basicItemIds = allList.stream().map(item -> item.getId()).collect(Collectors.toList());
		record.setBasicItemIds(basicItemIds);
		List<Map<String, Object>> rows = mapper.makePayTableDataCount(page, record);
		// TODO 处理数据， 薪级 岗位等级 ，字典等值
		if (!rows.isEmpty() && !allList.isEmpty()) {
			for (int i = 0; i < rows.size(); i++) {
				if (!StringUtil.isEmptyObj(rows.get(i).get("employee_status"))) {
					rows.get(i).put("employee_status", employeeStatusDictMap.get(rows.get(i).get("employee_status")));
				}
				if (!StringUtil.isEmptyObj(rows.get(i).get("personal_identity"))) {
					rows.get(i).put("personal_identity",
							personalIdentityDictMap.get(rows.get(i).get("personal_identity")));
				}
				if (!StringUtil.isEmptyObj(rows.get(i).get("establishment_type"))) {
					rows.get(i).put("establishment_type",
							establishmentTypeDictMap.get(rows.get(i).get("establishment_type")));
				}
			}
		}
		return rows;
	}

	@Override
	public List<HrmsNewsalaryBasicitemEmpHistory> getHistoryByEmp(String employeeId) {
		return mapper.getHistoryByEmp(employeeId);
	}

	private Map<String, String> convertDictMap(String dictType) {
		Assert.notNull(dictType, "dictType must not be null.");
		Map<String, String> map = Maps.newHashMap();
		List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
		if (CollectionUtils.isNotEmpty(dictItemList)) {
			for (DictItemResp d : dictItemList) {
				map.put(d.getItemNameValue(), d.getItemName());
			}
		}
		return map;
	}

	@Override
	public Integer getUncertain() {
		Integer uncertain = mapper.getUncertain();
		if (uncertain != null) {
			return uncertain;
		} else {
			return 0;
		}
	}

	@Override
	public List<Map<String, String>> selectEmpAll(SearchListTable record) {
		return mapper.selectEmpAll(record);
	}

	@Override
	public Map<String, Object> getUnoption() {
		Map<String, Object> map = new HashMap<>();
		List<String> list = mapper.getUnoption();
		map.put("count", list.size());
		map.put("data", list);
		List<String> tmpEmpList = mapper.getUnoptionByTmpEmp();
		map.put("countTmpEmp", tmpEmpList.size());
		map.put("dataTmpEmp", tmpEmpList);
		return map;
	}

	/**
	 * @param hrmsNewsalaryOptionEmp:
	 * @return void
	 * <AUTHOR>
	 * @description 批量绑定
	 * @date 2024/6/8 17:09
	 */
	@Override
//    @Transactional(readOnly = false)
	public void batchBindOption(HrmsNewsalaryOptionEmp hrmsNewsalaryOptionEmp) {

		String employeeId = hrmsNewsalaryOptionEmp.getEmployeeId();
		if (employeeId == null) {
			throw new BusinessException("参数错误");
		}
		// 按照逗号拆分 employeeId 变成一个 List
		List<String> employeeIdList = Arrays.asList(employeeId.split(","));
		for (String id : employeeIdList) {

			HrmsEmployee employee = hrmsEmployeeService.findDetailById(id);
			if (employee == null) {
				continue;
			}
			HrmsNewsalaryOptionEmp hrmsNewsalaryOptionEmpEo = hrmsNewsalaryOptionEmpService.destoryByEmployeeId(id);
			// 查询之前方案薪酬组数量
			if (Objects.nonNull(hrmsNewsalaryOptionEmpEo)) {
				HrmsNewsalaryOption record = optionMapper.selectByPrimaryKey(hrmsNewsalaryOptionEmpEo.getOptionId());
				List<HrmsNewsalaryOptionEmp> list = hrmsNewsalaryOptionEmpService
						.getAllByOptionId(hrmsNewsalaryOptionEmpEo.getOptionId(), null);
				record.setHeadCount(String.valueOf(list.size()));
				optionMapper.updateByPrimaryKeySelective(record);
			}

			// 新增薪酬组绑定数据

			HrmsNewsalaryOptionEmp hnoe = new HrmsNewsalaryOptionEmp();

			hnoe.setEmployeeId(id);
			hnoe.setEmployeeName(employee.getEmployeeName());
			hnoe.setEffectiveDate(hrmsNewsalaryOptionEmp.getEffectiveDate());
			hnoe.setRemark(hrmsNewsalaryOptionEmp.getRemark());
			hnoe.setOptionId(hrmsNewsalaryOptionEmp.getOptionId());

			hrmsNewsalaryOptionEmpService.save(hnoe);
		}

		// 更新方案维护的薪酬组数量
		hrmsNewsalaryOptionService.syncHeadCount(hrmsNewsalaryOptionEmp.getOptionId());

	}

	/**
	 * 获取员工基本薪酬项数据
	 * @param page
	 * @param requestContent
	 * @return
	 */
	@Override
	public Map<String,Object> findEmployeBaseSalaryItemList(Page page,  RequestContent requestContent){
        Assert.hasText(requestContent.getCallName(), "callName值不能为空");
        Assert.hasText(requestContent.getCipher(), "密钥缺失");
        Assert.hasText(requestContent.getPayrollDate(), "薪酬月份不能为空");
		long startTime = System.currentTimeMillis();    //获取开始时间
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("status", "1");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "人事系统-员工基础薪酬项");
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);
		if(CollUtil.isEmpty(registerList)) {
			Assert.isTrue(false, "接口未开放，请联系管理员");
		}
		CommInterfaceRegister commInterfaceRegister = registerList.get(0);
		if (commInterfaceRegister.getCipher().equals(requestContent.getCipher())) {
			Assert.isTrue(false, "密钥验证失败");
		}
		if(page.getPageSize()>1000){
			Assert.isTrue(false,"单次请求最多1000条数据");
		}
		PlatformResult<GlobalSetting> obj = globalSettingsFeignService.getSafeGlobalSetting("Y");
		GlobalSetting globalSetting = obj.getObject();
        List<HrmsNewsalaryItem> salaryList = new ArrayList<>();
		List<VueTableEntity> list = getEmployeBaseSalaryItemTitle(salaryList,globalSetting.getSsoOrgCode());
		if(CollUtil.isNotEmpty(list)) {
//			//获取薪酬项对应的字段id
			List<String> itemFieldId = list.stream().map(VueTableEntity::getProp).collect(Collectors.toList());
			Map<String,String> resultTitle = list.stream().collect(Collectors.toMap(VueTableEntity::getLabel,VueTableEntity::getProp,(entity1,entity2) -> entity1));
			if(resultTitle.keySet().contains("orgName")){
                resultTitle.put("部门id","org_id");
            }
            HrmsNewsalaryOptionPayroll payroll = new HrmsNewsalaryOptionPayroll();
            payroll.setComputeDate(requestContent.getPayrollDate());
            payroll.setComputeStatus("3");
            payroll.setSalaryList(salaryList);
            payroll.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            //获取所有薪酬项数据
            List<Map<String, String>> datas = newsalaryOptionPayrollMapper.getCalculateWagesDataCurrent(page,payroll);

			List<Map<String,String>> resultData = new ArrayList<>();
            datas.stream().forEach(vo->{
				Map<String,String> data = new HashMap<>();
				vo.keySet().stream().forEach(key->{
                    if(itemFieldId.contains(key)){
                        data.put(key,vo.get(key));
                        if(key.equals("personal_identity")){
                            data.put("personal_identity_text",vo.get("personal_identity_text"));
                        }
                        if(key.equals("establishment_type")){
                            data.put("establishment_type_text",vo.get("establishment_type_text"));
                        }
                        if(key.equals("employee_status")){
                            data.put("employee_status_text",vo.get("employee_status_text"));
                        }
                    }
				});
				if(CollUtil.isNotEmpty(data)){
					resultData.add(data);
				}
			});

			Map<String,Object> data = new HashMap<>();
			data.put("title",resultTitle);
			data.put("data",new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), resultData));

			long endTime = System.currentTimeMillis();
			CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
			commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
			commInterfaceLogs.setInterfaceName(commInterfaceRegister.getInterfaceName());
			commInterfaceLogs.setInterworkPlatform(requestContent.getCallName());
			commInterfaceLogs.setRequestUrl(requestContent.getIpAddress());
			commInterfaceLogs.setRequestParams(JSON.toJSONString(requestContent));
			commInterfaceLogs.setResponseParams(JSON.toJSONString(data));
			commInterfaceLogs.setResponseStatus("1");
			commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
			commInterfaceLogsService.save(commInterfaceLogs);
			return data;
		}
		return null;
	}

	/**
	 * 获取对外员工基本薪酬项表头
	 *
	 * @return
	 */
	private List<VueTableEntity> getEmployeBaseSalaryItemTitle(List<HrmsNewsalaryItem> salaryList,String ssoOrgCode) {
		List<VueTableEntity> retVueTableEntity = new ArrayList<>();
		// 根据分组查询所有数据
		List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode("outpush_salary_item").getObject();
		//获取方案所有薪酬项
        List<HrmsNewsalaryItem> salaryTitleList =  hrmsNewsalaryItemService.getAllOptionSalaryItem(ssoOrgCode);
		if (!dictItemList.isEmpty() && dictItemList.size() > 0) {
			dictItemList.forEach(item -> {
				if ("姓名".equals(item.getItemName())) {
                    retVueTableEntity
                            .add(new VueTableEntity(item.getItemName(), "employee_name", null, null, null));
                } else if ("工号".equals(item.getItemName())) {
                    retVueTableEntity.add(new VueTableEntity(item.getItemName(), "employee_no", null, null, null));
                } else if ("部门".equals(item.getItemName())) {
                    retVueTableEntity.add(new VueTableEntity(item.getItemName(), "orgName", null, null, null));
                } else if ("员工状态".equals(item.getItemName())) {
                    retVueTableEntity
                            .add(new VueTableEntity(item.getItemName(), "employee_status", null, null, null));
                } else if ("岗位名称".equals(item.getItemName())) {
                    retVueTableEntity
                            .add(new VueTableEntity(item.getItemName(), "personal_identity", null, null, null));
                } else if ("编制类型".equals(item.getItemName())) {
                    retVueTableEntity
                            .add(new VueTableEntity(item.getItemName(), "establishment_type", null, null, null));
                } else if ("入职日期".equals(item.getItemName())) {
                    retVueTableEntity.add(new VueTableEntity(item.getItemName(), "entry_date", null, null, null));
                } else if ("转正日期".equals(item.getItemName())) {
                    retVueTableEntity
                            .add(new VueTableEntity(item.getItemName(), "positive_time", null, null, null));
                } else if ("离退休日期".equals(item.getItemName())) {
                    retVueTableEntity
                            .add(new VueTableEntity(item.getItemName(), "retirement_time", null, 120, null));
                } else if ("身份证号".equals(item.getItemName())) {
                    retVueTableEntity
                            .add(new VueTableEntity(item.getItemName(), "identity_number", null, 120, null));
                } else if ("银行卡号".equals(item.getItemName())) {
                    retVueTableEntity.add(new VueTableEntity(item.getItemName(), "bankcardno", null, 120, null));
                } else if ("出生日期".equals(item.getItemName())) {
                    retVueTableEntity.add(new VueTableEntity(item.getItemName(), "birthday", null, 120, null));
                } else if ("手机号".equals(item.getItemName())) {
                    retVueTableEntity.add(new VueTableEntity(item.getItemName(), "phone_number", null, 120, null));
                }if ("薪酬组".equals(item.getItemName())) {
                    retVueTableEntity.add(new VueTableEntity(item.getItemName(), "option_name", null,120,null));
                }else {
                    if(CollUtil.isNotEmpty(salaryTitleList)){
                        List<HrmsNewsalaryItem> itemList = salaryTitleList.stream().filter(vo-> (","+item.getItemNameValue()+",").indexOf(vo.getItemName())>0).collect(Collectors.toList());
                        if(CollUtil.isNotEmpty(itemList)){
                            retVueTableEntity.add(new VueTableEntity(item.getItemName(), itemList.stream().map(HrmsNewsalaryItem::getItemId).collect(Collectors.joining(",")), null,120,null));
                            salaryList.addAll(itemList);
                        }
                    }
                }
			});
		}
		return retVueTableEntity;
	}
}

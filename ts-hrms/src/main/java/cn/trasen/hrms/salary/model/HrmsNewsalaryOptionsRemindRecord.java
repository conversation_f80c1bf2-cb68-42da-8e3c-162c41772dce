package cn.trasen.hrms.salary.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.*;

/**
 * 薪酬提醒-方案异动办理记录表 
 *
 */
@Table(name = "hrms_newsalary_options_remind_record")
@Setter
@Getter
public class HrmsNewsalaryOptionsRemindRecord {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 算薪周期
     */
    @Column(name = "compute_date")
    @ApiModelProperty(value = "算薪周期")
    private String computeDate;

    /**
     * 薪酬提醒ID
     */
    @Column(name = "remind_id")
    @ApiModelProperty(value = "薪酬提醒ID")
    private String remindId;

    /**
     * 员工id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    /**
     * 员工状态
     */
    @Column(name = "employee_status")
    @ApiModelProperty(value = "员工状态")
    private String employeeStatus;

    /**
     * 编制类型
     */
    @Column(name = "establishment_type")
    @ApiModelProperty(value = "编制类型")
    private String establishmentType;

    /**
     * 调整前方案id
     */
    @Column(name = "before_option_id")
    @ApiModelProperty(value = "调整前方案id")
    private String beforeOptionId;

    /**
     * 调整后方案id
     */
    @Column(name = "after_option_id")
    @ApiModelProperty(value = "调整后方案id")
    private String afterOptionId;

    /**
     * 处理状态 0-未处理 1-已处理
     */
    @Column(name = "handle_status")
    @ApiModelProperty(value = "处理状态 0-未处理 1-已处理")
    private String handleStatus;

    /**
     * 是否锁定  0-否 1-是
     */
    @Column(name = "is_lock")
    @ApiModelProperty(value = "是否锁定  0-否 1-是")
    private String isLock;



    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    /**
     * 生效时间
     */
    @Column(name = "effective_date")
    @ApiModelProperty(value = "生效时间")
    private String effectiveDate;

    /**
     * 处理结果
     */
    @Column(name = "handle_desc")
    @ApiModelProperty(value = "处理结果")
    private String handleDesc;


   @Transient
   @ApiModelProperty(value = "所在组织id")
    private String orgId;

    @Transient
    @ApiModelProperty(value = "所在组织名称")
    private String orgName;

    @Transient
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    @Transient
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    @Transient
    @ApiModelProperty(value = "岗位名称id")
    private String personalIdentity;

    @Transient
    @ApiModelProperty(value = "岗位名称")
    private String personalIdentityName;

    @Transient
    @ApiModelProperty(value = "编制类型名称")
    private String establishmentTypeName;

    @Transient
    @ApiModelProperty(value = "员工状态名称")
    private String employeeStatusName;

    @Transient
    @ApiModelProperty(value = "性别")
    private String gender;

    @Transient
    @ApiModelProperty(value = "调整前方案")
    private String beforeOptionName;

    @Transient
    @ApiModelProperty(value = "调整后方案")
    private String afterOptionName;

    @Transient
    @ApiModelProperty(value = "员工状态列表")
    private List<String> employeeStatuses;

    @Transient
    @ApiModelProperty(value = "员工编制类型列表")
    private List<String> establishmentTypes;
}
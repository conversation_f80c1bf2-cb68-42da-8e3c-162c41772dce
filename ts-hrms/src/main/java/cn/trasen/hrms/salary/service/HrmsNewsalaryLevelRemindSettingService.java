package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryLevelRemindSetting;

import java.util.List;

/**
 * @ClassName HrmsNewsalaryLevelRemindSettingService
 * @Description TODO
 * @date 2024��11��6�� ����10:32:10
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryLevelRemindSettingService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��11��6�� ����10:32:10
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryLevelRemindSetting record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��11��6�� ����10:32:10
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryLevelRemindSetting record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��11��6�� ����10:32:10
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 *
	 * @Title deleteById
	 * @Description 根据提醒设置id删除
	 * @param remindId
	 * @return Integer
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	Integer deleteByRemindId(String remindId);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryLevelRemindSetting
	 * @date 2024��11��6�� ����10:32:10
	 * <AUTHOR>
	 */
	HrmsNewsalaryLevelRemindSetting selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryLevelRemindSetting>
	 * @date 2024��11��6�� ����10:32:10
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryLevelRemindSetting> getDataSetList(Page page, HrmsNewsalaryLevelRemindSetting record);

	/**
	 * @Title getList
	 * @Description 查询薪级等级设置列表
	 * @param record
	 * @return DataSet<HrmsNewsalaryItemRemindSetting>
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	List<HrmsNewsalaryLevelRemindSetting> getList(HrmsNewsalaryLevelRemindSetting record);
}

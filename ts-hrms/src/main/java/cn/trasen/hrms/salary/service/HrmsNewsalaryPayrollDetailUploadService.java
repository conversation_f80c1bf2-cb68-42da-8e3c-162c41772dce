package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailUploadEo;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
*
*/
public interface HrmsNewsalaryPayrollDetailUploadService {

    PlatformResult<String> save(List<Map<String, Object>> record);

    PlatformResult<String> update(List<String> record);

    HrmsNewsalaryPayrollDetailUploadEo selectById(String id);

    Integer deleteById(List<String> ids);

    PlatformResult<List<Map<String, Object>>> getDataSetList(HrmsNewsalaryPayrollDetailUploadEo record);

    List<VueTableEntity> salaryCountTitle(String payrollDate);

    PlatformResult<String> rejectDetailUpload(List<String> record);

    DataSet<Map<String, Object>> getDataSetListPage(Page page, HrmsNewsalaryPayrollDetailUploadEo record);

    PlatformResult<String> updateDetailUpload(Map<String, Object> record);

    void exportUploadTemlateData(HttpServletResponse response,HrmsNewsalaryPayrollDetailUploadEo record);

    PlatformResult<String> importUploadTemplateData(MultipartFile file,String payrollDate);

    PlatformResult<String> updateUpload(Map<String, Object> record);

    PlatformResult<String> withdrawUpload(List<String> record);

    PlatformResult<List<HrmsNewsalaryPayrollDetailUploadEo>> queryUploadInfo(String payrollDate, String optionId);

    void uploadDataExport(HttpServletRequest request, HttpServletResponse response, HrmsNewsalaryPayrollDetailUploadEo record);
}

package cn.trasen.hrms.salary.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailImport;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicitemEmpService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionEmpService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryPayrollDetailImportService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName HrmsNewsalaryPayrollDetailImportController
 * @Description TODO
 * @date 2024��3��1�� ����4:35:33
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "手工导入工资项控制器ImportController")
public class HrmsNewsalaryPayrollDetailImportController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryPayrollDetailImportController.class);

	@Autowired
	private HrmsNewsalaryPayrollDetailImportService hrmsNewsalaryPayrollDetailImportService;
	@Autowired
	private HrmsNewsalaryOptionEmpService hrmsNewsalaryOptionEmpService;

	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;

	@Autowired
	private HrmsNewsalaryBasicitemEmpService hrmsNewsalaryBasicitemEmpService;

	/**
	 * @Title saveHrmsNewsalaryPayrollDetailImport
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��3��1�� ����4:35:33
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/newSalaryDetailImport/save")
	public PlatformResult<String> saveHrmsNewsalaryPayrollDetailImport(@RequestBody HrmsNewsalaryPayrollDetailImport record) {
		try {
			hrmsNewsalaryPayrollDetailImportService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "导入工资项导出模版", notes = "导入工资项导出模版")
	@GetMapping("/api/newSalaryDetailImport/exportTemplate")
	public void exportTemplate(String optionId, HttpServletResponse response) {

		try {

			//根据方案查询出人员列表
			//List<HrmsNewsalaryOptionEmp> listEmp = hrmsNewsalaryOptionEmpService.getEmployeeDetails(optionId);

			List<HrmsNewsalaryOptionEmp> listEmp = hrmsNewsalaryOptionEmpService.getAllEmployeeDetails(optionId);

			// 创建Excel文档
			XSSFWorkbook workbook = new XSSFWorkbook();
			XSSFSheet sheet = workbook.createSheet("Sheet1");

			//设置样式
			CellStyle textStyle = workbook.createCellStyle();
			textStyle.setDataFormat(workbook.getCreationHelper().createDataFormat().getFormat("text"));
			textStyle.setBorderLeft(CellStyle.BORDER_THIN);
			textStyle.setBorderRight(CellStyle.BORDER_THIN);
			textStyle.setBorderTop(CellStyle.BORDER_THIN);
			textStyle.setBorderBottom(CellStyle.BORDER_THIN);

			// 创建表头
			XSSFRow header = sheet.createRow(0);
			XSSFCell cell0 = header.createCell(0);
			cell0.setCellStyle(textStyle);
			cell0.setCellValue("工号");

			XSSFCell cell1 = header.createCell(1);
			cell1.setCellStyle(textStyle);
			cell1.setCellValue("姓名");

			// 添加  科室  和 薪酬组
			XSSFCell cell2 = header.createCell(2);
			cell2.setCellStyle(textStyle);
			cell2.setCellValue("科室");


			XSSFCell cell3 = header.createCell(3);
			cell3.setCellStyle(textStyle);
			cell3.setCellValue("薪酬组");

			XSSFRow rowsData = null;
			XSSFCell rowsCell0 = null;
			XSSFCell rowsCell1 = null;
			XSSFCell rowsCell2 = null;
			XSSFCell rowsCell3 = null;
			//改为所有手工导入的项 根据中文 不取重复的
			List<HrmsNewsalaryItem> itemList = hrmsNewsalaryPayrollDetailImportService.exportTemplateAll(optionId);
			int index = 4;
			for (HrmsNewsalaryItem item : itemList) {
				if("1".equals(item.getItemRule())){
					XSSFCell cellIndex = header.createCell(index);
					cellIndex.setCellValue(item.getItemName());
					cellIndex.setCellStyle(textStyle);
					index ++;
				}
			}
			if(!listEmp.isEmpty()){
				List<String> empIds = listEmp.stream().map(HrmsNewsalaryOptionEmp::getEmployeeId).collect(Collectors.toList());
				List<HrmsNewsalaryBasicitemEmp> basicItemList = hrmsNewsalaryBasicitemEmpService.getEmpByTypeAndIds("2", empIds);
				Map<String, Map<String, BigDecimal>> basicItemMap = basicItemList.stream()
						.collect(Collectors.groupingBy(HrmsNewsalaryBasicitemEmp::getEmployeeId,
								Collectors.toMap(
										HrmsNewsalaryBasicitemEmp::getEmpField,
										emp -> Optional.ofNullable(emp.getSalaryAmount())
												.orElse(BigDecimal.ZERO)
										,(oldValue,newValue)->newValue)
						));
				for (int i = 0; i < listEmp.size(); i++) {
					String employeeId = listEmp.get(i).getEmployeeId();
					rowsData = sheet.createRow(i+1);
					rowsCell0 = rowsData.createCell(0);
					rowsCell0.setCellStyle(textStyle);
					rowsCell0.setCellValue(listEmp.get(i).getEmployeeNo());
					rowsCell1 = rowsData.createCell(1);
					rowsCell1.setCellStyle(textStyle);
					rowsCell1.setCellValue(listEmp.get(i).getEmployeeName());

					rowsCell2 = rowsData.createCell(2);
					rowsCell2.setCellStyle(textStyle);
					rowsCell2.setCellValue(listEmp.get(i).getOrgName());

					rowsCell3 = rowsData.createCell(3);
					rowsCell3.setCellStyle(textStyle);
					rowsCell3.setCellValue(listEmp.get(i).getOptionName());
					for(int j = 0;j<itemList.size();j++){
						String cellName = header.getCell(j + 4).getStringCellValue();
						if(StrUtil.equals(cellName,itemList.get(j).getItemName())){
							XSSFCell cell = rowsData.createCell(j + 4);
							cell.setCellStyle(textStyle);
							Map<String, BigDecimal> salaryMap = basicItemMap.get(employeeId);
							if(MapUtil.isNotEmpty(salaryMap)) {
								String salary = String.valueOf(salaryMap.get(itemList.get(j).getId()));
								if (StrUtil.isNotBlank(salary) && !StrUtil.equals("null", salary)) {
									cell.setCellValue(salary);
								}
							}
						}
					}
				}
			}

			sheet.setColumnWidth(2, 15 * 256);
			sheet.setColumnWidth(3, 20 * 256);
			//遍历要添加的列
		//	List<HrmsNewsalaryItem> itemList = hrmsNewsalaryPayrollDetailImportService.exportTemplate(optionId);
			String encodeName = URLEncoder.encode("手工录入工资项导入模版.xlsx", StandardCharsets.UTF_8.toString());
			// 设置响应头信息
			response.setContentType("application/vnd.ms-excel");
			response.setHeader("Content-Disposition", "attachment; filename=\"" + encodeName + "\"; filename*=utf-8''" + encodeName);

			// 将Excel文档写入响应流中
			ServletOutputStream outputStream = response.getOutputStream();
			workbook.write(outputStream);
			outputStream.flush();
			outputStream.close();
		} catch (Exception e) {
			logger.error("导出模版异常"+e.getMessage(), e);
		}
	}

	@ApiOperation(value = "导入手工工资项", notes = "导入手工工资项")
	@PostMapping("/api/newSalaryDetailImport/importSave")
	public PlatformResult<String> importSave(@RequestParam("file") MultipartFile file,String optionId,String sendDate) {
		try {
			Workbook workbook = WorkbookFactory.create(file.getInputStream());
			Sheet sheet = workbook.getSheetAt(0);
			List<Map<String, String>> data = new ArrayList<>();
			for (Row row : sheet) { // 遍历每一行
				Map<String, String> rowData = new HashMap<>(); // 创建一个 Map 来存储每一行的值
				for (Cell cell : row) {  // 遍历每一列
					// 获取列名和值
					String columnName = sheet.getRow(0).getCell(cell.getColumnIndex()).getStringCellValue();
					String value = cell.toString();
					if(!StringUtil.isEmpty(columnName) && "工号".equals(columnName) && !StringUtil.isEmpty(value)){
						rowData.put(columnName.trim(), value.replaceAll("\\.0+|\\.00", "").trim());
					}else{
						if(!StringUtil.isEmpty(value)){
							rowData.put(columnName.trim(), value.trim());
						}
					}
				}
				if(!StringUtil.isEmpty(rowData.get("工号"))){  //工号不为空 就插入数据
					data.add(rowData);
				}
			}
			if(CollUtil.isNotEmpty(data) && data.size()>1){
				for(int i=1;i<data.size();i++) {
					String employeeNo = data.get(i).get("工号");
					Boolean isExistFlag = hrmsEmployeeService.isExistEmployeeByCode(employeeNo);
					if(!isExistFlag){
						throw new BusinessException(employeeNo+"员工信息不存在!");
					}
				}
			}
			Integer size = hrmsNewsalaryPayrollDetailImportService.importSave(data, optionId, sendDate);
			return PlatformResult.success("导入完成 "+size+" 条数据");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryPayrollDetailImport
	 * @Description 清除手工导入项数据
	 * @param optionId
	 * @param sendDate
	 * @return PlatformResult<String>
	 * @date 2024��3��1�� ����4:35:33
	 * <AUTHOR>
	 */
	@ApiOperation(value = "清除手工导入项数据", notes = "清除手工导入项数据")
	@PostMapping("/api/newSalaryDetailImport/cleanImportDataByOptionId")
	public PlatformResult<String> cleanImportDataByOptionId(String optionId,String sendDate) {
		try {
			hrmsNewsalaryPayrollDetailImportService.cleanImportDataByOptionId(optionId,sendDate);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryPayrollDetailImport
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��3��1�� ����4:35:33
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/newSalaryDetailImport/update")
	public PlatformResult<String> updateHrmsNewsalaryPayrollDetailImport(@RequestBody HrmsNewsalaryPayrollDetailImport record) {
		try {
			hrmsNewsalaryPayrollDetailImportService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryPayrollDetailImportById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryPayrollDetailImport>
	 * @date 2024��3��1�� ����4:35:33
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/newSalaryDetailImport/{id}")
	public PlatformResult<HrmsNewsalaryPayrollDetailImport> selectHrmsNewsalaryPayrollDetailImportById(@PathVariable String id) {
		try {
			HrmsNewsalaryPayrollDetailImport record = hrmsNewsalaryPayrollDetailImportService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryPayrollDetailImportById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��3��1�� ����4:35:33
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/newSalaryDetailImport/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryPayrollDetailImportById(@PathVariable String id) {
		try {
			hrmsNewsalaryPayrollDetailImportService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryPayrollDetailImportList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryPayrollDetailImport>
	 * @date 2024��3��1�� ����4:35:33
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/newSalaryDetailImport/list")
	public DataSet<HrmsNewsalaryPayrollDetailImport> selectHrmsNewsalaryPayrollDetailImportList(Page page, HrmsNewsalaryPayrollDetailImport record) {
		return hrmsNewsalaryPayrollDetailImportService.getDataSetList(page, record);
	}

	@ApiOperation(value = "导入修改工资项导出模版", notes = "导入修改工资项导出模版")
	@GetMapping("/api/newSalaryDetailImport/exportUpdateTemplate/{optionId}/{computeDate}")
	public void exportUpdateTemplate(@PathVariable String optionId,@PathVariable String computeDate, HttpServletResponse response) {
		try {
			List<HrmsNewsalaryOptionEmp> listEmp = hrmsNewsalaryOptionEmpService.getEmployeeDetailByOptionId(optionId);
			// 创建Excel文档
			XSSFWorkbook workbook = new XSSFWorkbook();
			XSSFSheet sheet = workbook.createSheet("Sheet1");
			//设置样式
			CellStyle textStyle = workbook.createCellStyle();
			textStyle.setDataFormat(workbook.getCreationHelper().createDataFormat().getFormat("text"));
			textStyle.setBorderLeft(CellStyle.BORDER_THIN);
			textStyle.setBorderRight(CellStyle.BORDER_THIN);
			textStyle.setBorderTop(CellStyle.BORDER_THIN);
			textStyle.setBorderBottom(CellStyle.BORDER_THIN);
			// 创建表头
			XSSFRow header = sheet.createRow(0);
			XSSFCell cell0 = header.createCell(0);
			cell0.setCellStyle(textStyle);
			cell0.setCellValue("工号");
			XSSFCell cell1 = header.createCell(1);
			cell1.setCellStyle(textStyle);
			cell1.setCellValue("姓名");
			// 添加  科室  和 薪酬组
			XSSFCell cell2 = header.createCell(2);
			cell2.setCellStyle(textStyle);
			cell2.setCellValue("科室");
			XSSFCell cell3 = header.createCell(3);
			cell3.setCellStyle(textStyle);
			cell3.setCellValue("薪酬组");
			XSSFRow rowsData = null;
			XSSFCell rowsCell0 = null;
			XSSFCell rowsCell1 = null;
			XSSFCell rowsCell2 = null;
			XSSFCell rowsCell3 = null;
			if(!listEmp.isEmpty()){
				for (int i = 0; i < listEmp.size(); i++) {
					rowsData = sheet.createRow(i+1);
					rowsCell0 = rowsData.createCell(0);
					rowsCell0.setCellStyle(textStyle);
					rowsCell0.setCellValue(listEmp.get(i).getEmployeeNo());
					rowsCell1 = rowsData.createCell(1);
					rowsCell1.setCellStyle(textStyle);
					rowsCell1.setCellValue(listEmp.get(i).getEmployeeName());
					rowsCell2 = rowsData.createCell(2);
					rowsCell2.setCellStyle(textStyle);
					rowsCell2.setCellValue(listEmp.get(i).getOrgName());
					rowsCell3 = rowsData.createCell(3);
					rowsCell3.setCellStyle(textStyle);
					rowsCell3.setCellValue(listEmp.get(i).getOptionName());
				}
			}
			sheet.setColumnWidth(2, 15 * 256);
			sheet.setColumnWidth(3, 20 * 256);
			//取方案下不是公式的所有薪资项
			List<HrmsNewsalaryItem> itemList = hrmsNewsalaryPayrollDetailImportService.exportSalaryItemByOptionId(optionId,computeDate);
			int index = 4;
			for (HrmsNewsalaryItem item : itemList) {
					XSSFCell cellIndex = header.createCell(index);
					cellIndex.setCellValue(item.getItemName());
					cellIndex.setCellStyle(textStyle);
					index ++;
			}
			String encodeName = URLEncoder.encode("导入修改工资项模版.xlsx", StandardCharsets.UTF_8.toString());
			// 设置响应头信息
			response.setContentType("application/vnd.ms-excel");
			response.setHeader("Content-Disposition", "attachment; filename=\"" + encodeName + "\"; filename*=utf-8''" + encodeName);
			// 将Excel文档写入响应流中
			ServletOutputStream outputStream = response.getOutputStream();
			workbook.write(outputStream);
			outputStream.flush();
			outputStream.close();
		} catch (Exception e) {
			logger.error("导出模版异常"+e.getMessage(), e);
		}
	}

	@ApiOperation(value = "判断薪酬方案是否导出手工录入工资项", notes = "判断薪酬方案是否导出手工录入工资项")
	@GetMapping("/api/newSalaryDetailImport/checkManualSalary/{optionId}/{computeDate}")
	public PlatformResult checkManualSalary(@PathVariable String optionId,@PathVariable String computeDate) {
		try {
			hrmsNewsalaryPayrollDetailImportService.checkManualSalary(optionId,computeDate);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}


	@ApiOperation(value = "导入全部手工工资项", notes = "导入手工工资项")
	@PostMapping("/api/newSalaryDetailImport/importAllSave")
	public PlatformResult<String> importAllSave(@RequestParam("file") MultipartFile file,String sendDate) {
		try {
			Workbook workbook = WorkbookFactory.create(file.getInputStream());
			Sheet sheet = workbook.getSheetAt(0);
			List<Map<String, String>> data = new ArrayList<>();
			for (Row row : sheet) { // 遍历每一行
				Map<String, String> rowData = new HashMap<>(); // 创建一个 Map 来存储每一行的值
				for (Cell cell : row) {  // 遍历每一列
					// 获取列名和值
					String columnName = sheet.getRow(0).getCell(cell.getColumnIndex()).getStringCellValue();
					String value = cell.toString();
					if(!StringUtil.isEmpty(columnName) && "工号".equals(columnName) && !StringUtil.isEmpty(value)){
						rowData.put(columnName.trim(), value.replaceAll("\\.0+|\\.00", "").trim());
					}else{
						if(!StringUtil.isEmpty(value)){
							rowData.put(columnName.trim(), value.trim());
						}
					}
				}
				if(!StringUtil.isEmpty(rowData.get("工号"))){  //工号不为空 就插入数据
					data.add(rowData);
				}
			}
			if(CollUtil.isNotEmpty(data) && data.size()>1){
				for(int i=1;i<data.size();i++) {
					String employeeNo = data.get(i).get("工号");
					HrmsEmployee employee = hrmsEmployeeService.findEmployeeByCode(employeeNo);
					if(ObjectUtil.isEmpty(employee)){
						throw new BusinessException(employeeNo+"员工信息不存在!");
					}
				}
			}
			return hrmsNewsalaryPayrollDetailImportService.importAllSave(data, sendDate);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}

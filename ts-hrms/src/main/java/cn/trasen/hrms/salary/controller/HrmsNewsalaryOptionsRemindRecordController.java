package cn.trasen.hrms.salary.controller;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionsRemindRecord;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionsRemindRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.Date;

/**
 * @ClassName HrmsNewsalaryOptionsRemindRecordController
 * @Description TODO
 * @date 2024��10��29�� ����6:29:36
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "hrmsNewsalaryOptionsRemindRecordController")
public class HrmsNewsalaryOptionsRemindRecordController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryOptionsRemindRecordController.class);

	@Autowired
	private HrmsNewsalaryOptionsRemindRecordService hrmsNewsalaryOptionsRemindRecordService;

	/**
	 * @Title saveHrmsNewsalaryOptionsRemindRecord
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��29�� ����6:29:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/hrmsNewsalaryOptionsRemindRecord/save")
	public PlatformResult<String> saveHrmsNewsalaryOptionsRemindRecord(@RequestBody HrmsNewsalaryOptionsRemindRecord record) {
		try {
			hrmsNewsalaryOptionsRemindRecordService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryOptionsRemindRecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��29�� ����6:29:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/hrmsNewsalaryOptionsRemindRecord/update")
	public PlatformResult<String> updateHrmsNewsalaryOptionsRemindRecord(@RequestBody HrmsNewsalaryOptionsRemindRecord record) {
		try {
			hrmsNewsalaryOptionsRemindRecordService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryOptionsRemindRecordById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryOptionsRemindRecord>
	 * @date 2024��10��29�� ����6:29:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/hrmsNewsalaryOptionsRemindRecord/{id}")
	public PlatformResult<HrmsNewsalaryOptionsRemindRecord> selectHrmsNewsalaryOptionsRemindRecordById(@PathVariable String id) {
		try {
			HrmsNewsalaryOptionsRemindRecord record = hrmsNewsalaryOptionsRemindRecordService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryOptionsRemindRecordById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��10��29�� ����6:29:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/hrmsNewsalaryOptionsRemindRecord/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryOptionsRemindRecordById(@PathVariable String id) {
		try {
			hrmsNewsalaryOptionsRemindRecordService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryOptionsRemindRecordList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryOptionsRemindRecord>
	 * @date 2024��10��29�� ����6:29:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/hrmsNewsalaryOptionsRemindRecord/list")
	public DataSet<HrmsNewsalaryOptionsRemindRecord> selectHrmsNewsalaryOptionsRemindRecordList(Page page, HrmsNewsalaryOptionsRemindRecord record) {
		return hrmsNewsalaryOptionsRemindRecordService.getDataSetList(page, record);
	}

	/**
	 * @Title selectHrmsNewsalaryOptionsRemindRecordList
	 * @Description 一键处理
	 * @param record
	 * @return DataSet<HrmsNewsalaryOptionsRemindRecord>
	 * @date 2024��10��29�� ����6:29:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "一键处理", notes = "一键处理")
	@PostMapping("/api/hrmsNewsalaryOptionsRemindRecord/batchAdjust")
	public PlatformResult<String> batchAdjust(@RequestBody HrmsNewsalaryOptionsRemindRecord record) {
		try {
			if(StringUtils.isBlank(record.getEffectiveDate())){
				record.setEffectiveDate(DateUtils.getStringDateShort(new Date()));
			}
            if(StringUtils.isBlank(record.getComputeDate())){
                record.setComputeDate(DateUtils.getStringDateShort(new Date()));
            }
			hrmsNewsalaryOptionsRemindRecordService.batchAdjust(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}

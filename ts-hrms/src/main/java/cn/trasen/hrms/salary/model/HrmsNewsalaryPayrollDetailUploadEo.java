package cn.trasen.hrms.salary.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 薪酬发放明细手工上报表
 * @TableName hrms_newsalary_payroll_detail_upload
 */
@Table(name = "hrms_newsalary_payroll_detail_upload")
@Setter
@Getter
public class HrmsNewsalaryPayrollDetailUploadEo implements Serializable {
    /**
     * id
     */
    @Id
    private String id;

    /**
     * 人员id
     */
    private String employeeNo;

    /**
     * 薪酬项目id
     */
    private String itemId;

    /**
     * 方案id
     */
    private String optionId;

    /**
     * 薪酬项目名称
     */
    private String itemName;

    /**
     * 工资金额
     */
    private BigDecimal salary;

    /**
     * 上报月份
     */
    private String payrollDate;

    /**
     * 是否审核(0.草稿 1.已提交核 2.已审核)
     */
    private Integer isExamine;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 修改时间
     */
    private Date updateDate;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改人名称
     */
    private String updateUserName;

    /**
     * 删除标识
     */
    private String isDeleted;

    /**
     * 机构编码
     */
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    private String ssoOrgName;

    /**
     * 排序号
     */
    private Integer sortNo;

    @Transient
    @ApiModelProperty("1.已提交 2.已驳回")
    private Integer type;

    @Transient
    private String userCode;

    @Transient
    private String employee_no;

    @Transient
    private String employee_name;

    @Transient
    private String employeeName;

    @Transient
    private String orgName;

    private static final long serialVersionUID = 1L;
}
package cn.trasen.hrms.salary.controller;

import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.hrms.salary.enums.EmployeeTemporaryOpTypeEnum;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.ExcelExportOfTemplateUtil;
import cn.trasen.hrms.utils.ExportExcelUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;

import cn.trasen.hrms.salary.model.HrmsEmployeeTemporary;
import cn.trasen.hrms.salary.service.HrmsEmployeeTemporaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName HrmsEmployeeTemporaryController
 * @Description TODO
 * @date 2024��10��8�� ����3:11:08
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@Api(tags = "临时员工管理Controller")
public class HrmsEmployeeTemporaryController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsEmployeeTemporaryController.class);

	@Autowired
	private HrmsEmployeeTemporaryService hrmsEmployeeTemporaryService;

	/**
	 * @Title saveHrmsEmployeeTemporary
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/employeeTemporary/save")
	public PlatformResult<String> saveHrmsEmployeeTemporary(@RequestBody HrmsEmployeeTemporary record) {
		try {
			hrmsEmployeeTemporaryService.save(record,EmployeeTemporaryOpTypeEnum.ADD);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsEmployeeTemporary
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/employeeTemporary/update")
	public PlatformResult<String> updateHrmsEmployeeTemporary(@RequestBody HrmsEmployeeTemporary record) {
		try {
			hrmsEmployeeTemporaryService.update(record,EmployeeTemporaryOpTypeEnum.UPDATE);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsEmployeeTemporaryById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsEmployeeTemporary>
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/employeeTemporary/{id}")
	public PlatformResult<HrmsEmployeeTemporary> selectHrmsEmployeeTemporaryById(@PathVariable String id) {
		try {
			HrmsEmployeeTemporary record = hrmsEmployeeTemporaryService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsEmployeeTemporaryById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/employeeTemporary/delete/{id}")
	public PlatformResult<String> deleteHrmsEmployeeTemporaryById(@PathVariable String id) {
		try {
			hrmsEmployeeTemporaryService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsEmployeeTemporaryList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsEmployeeTemporary>
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/employeeTemporary/list")
	public DataSet<HrmsEmployeeTemporary> selectHrmsEmployeeTemporaryList(Page page, HrmsEmployeeTemporary record) {
		List<HrmsEmployeeTemporary> records = hrmsEmployeeTemporaryService.getDataSetList(page, record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

    /**
     *
     * @Title deleteHrmsEmployeeTemporaryById
     * @Description j一键同步至薪酬方案
     * @return PlatformResult<String>
     * @date 2024��10��8�� ����3:11:08
     * <AUTHOR>
     */
    @ApiOperation(value = "同步", notes = "同步")
    @PostMapping("/api/employeeTemporary/sync2Salary")
    public PlatformResult<Integer> sync2Salary() {
        try {
           Integer count =  hrmsEmployeeTemporaryService.sync2Salary();
            return PlatformResult.success(count);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

	@GetMapping(value = "/api/employeeTemporary/downloadImportTemplate")
	@ApiOperation(value = "下载临时员工批量入职模板", notes = "下载临时员工批量入职模板")
	public void downloadImportTemplate(HttpServletResponse response) {
		try {
			ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
			String filename = "临时员工批量入职模板.xlsx";
			String template = "template/emptmp/importEmployeeTemporary.xlsx";
			ClassPathResource resource = new ClassPathResource(template);
			exportExcelUtil.downloadExportExcel(filename, response, resource);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	@ApiOperation(value = "批量入职", notes = "批量入职")
	@PostMapping(value = "/api/employeeTemporary/import")
	public PlatformResult importEmployeeTemporary(@RequestParam("file") MultipartFile file) {
		List<HrmsEmployeeTemporary> employeeImportList = (List<HrmsEmployeeTemporary>) ImportExcelUtil.getExcelDatas(file, HrmsEmployeeTemporary.class);
		List<HrmsEmployeeTemporary> datas = employeeImportList.stream().filter(vo-> StringUtils.isNotBlank(vo.getEmployeeNo()) && StringUtils.isNotBlank(vo.getIdentityNumber())).collect(Collectors.toList());
		Map<String,Integer> map = hrmsEmployeeTemporaryService.importEmployeeTemporary(datas);
		return PlatformResult.success(map);
	}

	@GetMapping(value = "/api/employeeTemporary/downloadAdjustSalaryTemplate")
	@ApiOperation(value = "下载临时员工调薪模板", notes = "下载临时员工调薪模板")
	public void downloadAdjustSalaryTemplate(HttpServletResponse response) {
		try {
			ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
			String filename = "临时员工批量调薪模板.xlsx";
			String template = "template/emptmp/txTemplate.xlsx";
			ClassPathResource resource = new ClassPathResource(template);
			exportExcelUtil.downloadExportExcel(filename, response, resource);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	@ApiOperation(value = "批量调薪", notes = "批量调薪")
	@PostMapping(value = "/api/employeeTemporary/batchAdjustSalary")
	public PlatformResult batchAdjustSalary(@RequestParam("file") MultipartFile file) {
		List<HrmsEmployeeTemporary> employeeImportList = (List<HrmsEmployeeTemporary>) ImportExcelUtil.getExcelDatas(file, HrmsEmployeeTemporary.class);
		Map<String,Integer> map = hrmsEmployeeTemporaryService.batchAdjustSalary(employeeImportList);
		return PlatformResult.success(map);
	}

	@ApiOperation(value = "批量离职", notes = "批量离职")
	@PostMapping("/api/employeeTemporary/batchdimission")
	public PlatformResult batchdimission(@RequestBody JSONObject jsonObject) {
    	if(jsonObject == null){
			return PlatformResult.failure("参数不能为空!");
		}
		String ids = jsonObject.getString("ids");
		String dimissionDate = jsonObject.getString("dimissionDate");
		String effectiveDate = jsonObject.getString("effectiveDate");
		String reason = jsonObject.getString("dimissionReason");
		try {
			if(StringUtils.isEmpty(ids)){
				return PlatformResult.failure("ids不能为空!");
			}
			if(StringUtil.isEmpty(dimissionDate)){
				return PlatformResult.failure("离职时间不能为空!");
			}
			if(StringUtil.isEmpty(effectiveDate)){
				return PlatformResult.failure("生效时间不能为空!");
			}
//			if(StringUtil.isEmpty(reason)){
//				return PlatformResult.failure("离职原因不能为空!");
//			}
			hrmsEmployeeTemporaryService.batchdimission(Arrays.asList(ids.split(",")),dimissionDate,effectiveDate,reason);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "临时员工导出数据", notes = "临时员工导出数据")
	@GetMapping(value = "/api/employeeTemporary/downLoad")
	public void downLoad(Page page, HrmsEmployeeTemporary entity,HttpServletRequest request,
						 HttpServletResponse response) {
		try {
			TemplateExportParams params = new TemplateExportParams("template/emptmp/export.xls");

			page.setPageSize(Integer.MAX_VALUE);

			String name = "临时员工记录.xls";
			name = new String(name.getBytes("UTF-8"), "ISO8859-1");


			List<HrmsEmployeeTemporary> list = hrmsEmployeeTemporaryService.getDataSetList(page, entity);
			if(list != null && list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					list.get(i).setNo(i+1);
					list.get(i).setCreateDateStr(DateUtils.getPresentTimeStr(list.get(i).getCreateDate()));
					list.get(i).setUpdateDateStr(DateUtils.getPresentTimeStr(list.get(i).getUpdateDate()));
					list.get(i).setTmpEmployeeStatus(list.get(i).getTmpEmployeeStatus().equals("1")?"在职":"离职");
					list.get(i).setIsSyncSalary(list.get(i).getIsSyncSalary().equals("Y")?"已同步":"未同步");
				}
			}

			Map<String,Object> resultMap = new HashMap<String, Object>();
			resultMap.put("list", list);
			Workbook workbook = new ExcelExportOfTemplateUtil().createExcleByTemplate(params, null, null, resultMap);
			response.setContentType("application/msword");
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-disposition", "attachment; filename=" + name);
			ServletOutputStream fos = response.getOutputStream();
			workbook.write(fos);
			fos.flush();
		} catch (Exception e) {
			log.error("临时员工导出异常"+e.getMessage(),e);
		}
	}
}

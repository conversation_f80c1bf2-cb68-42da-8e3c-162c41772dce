package cn.trasen.hrms.salary.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.model.HrmsEmployeeTemporary;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface HrmsEmployeeTemporaryMapper extends Mapper<HrmsEmployeeTemporary> {

    /**
     * @Title: getPageList
     * @Description: 分页查询临时员工列表
     * @param page
     * @param entity
     * @Return List<HrmsEmployeeTemporary>
     * <AUTHOR>
     * @date 2020年4月15日 下午2:45:21
     */
    List<HrmsEmployeeTemporary> getPageList(Page page, HrmsEmployeeTemporary entity);
}
package cn.trasen.hrms.salary.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.hrms.salary.enums.NewsalaryTemporaryAdjustOpTypeEnum;
import cn.trasen.hrms.salary.model.HrmsNewsalaryRemindSetting;
import cn.trasen.hrms.salary.model.HrmsNewsalaryTemporaryAdjust;
import cn.trasen.hrms.salary.service.HrmsNewsalaryRemindSettingService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryTemporaryAdjustService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.hrms.salary.dao.HrmsNewsalaryItemRemindRecordMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemRemindRecord;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemRemindRecordService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @ClassName HrmsNewsalaryItemRemindRecordServiceImpl
 * @Description TODO
 * @date 2024��10��29�� ����6:29:18
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryItemRemindRecordServiceImpl implements HrmsNewsalaryItemRemindRecordService {

	@Resource
	private HrmsNewsalaryItemRemindRecordMapper mapper;

	@Autowired
	private HrmsNewsalaryTemporaryAdjustService newsalaryTemporaryAdjustService;

	@Autowired
	private HrmsNewsalaryRemindSettingService remindSettingService;

	@Resource
	private InformationFeignService informationFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryItemRemindRecord record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer batchInsert(List<HrmsNewsalaryItemRemindRecord> list){
		int count = mapper.batchInsert(list);
		return count;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryItemRemindRecord record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer remindProcessing(HrmsNewsalaryItemRemindRecord record) {
		record.setUpdateDate(new Date());
		record.setHandleStatus("1");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//处理薪酬调整数据
		HrmsNewsalaryTemporaryAdjust temporaryAdjust = new HrmsNewsalaryTemporaryAdjust();
		temporaryAdjust.setEmployeeNo(record.getEmployeeNo());
		temporaryAdjust.setEmployeeId(record.getEmployeeId());
        temporaryAdjust.setEmployeeName(record.getEmployeeName());
		temporaryAdjust.setTmpItem(record.getTmpItem());
		temporaryAdjust.setOptionCycle(record.getComputeDate());
		BigDecimal totalAmount = record.getDifferenceAmount();
		if(StringUtils.isNotBlank(record.getBackPaymentMonth())){
			totalAmount = record.getDifferenceAmount().multiply(new BigDecimal(record.getBackPaymentMonth())).setScale(2, RoundingMode.HALF_UP);
		}
		temporaryAdjust.setSalaryItemAmount(totalAmount);
		temporaryAdjust.setRemark(record.getRemark());
		temporaryAdjust.setCountType("1");
		if ((totalAmount.compareTo(BigDecimal.ZERO) > 0 && "2".equals(record.getCountType())) ||
			(totalAmount.compareTo(BigDecimal.ZERO) < 0 && "1".equals(record.getCountType()))) {
			temporaryAdjust.setCountType("2");
		}
		//判断是否存在项目类型对应的调整记录
		HrmsNewsalaryTemporaryAdjust oldAjustData =  newsalaryTemporaryAdjustService.getEmployeeTemporaryAdjustByItem(record.getEmployeeId(),record.getTmpItem(),record.getComputeDate(),temporaryAdjust.getCountType());
		if(oldAjustData != null){
			oldAjustData.setSalaryItemAmount(oldAjustData.getSalaryItemAmount().add(totalAmount));
			oldAjustData.setRemark(oldAjustData.getRemark()+";" + temporaryAdjust.getRemark());
			newsalaryTemporaryAdjustService.update(oldAjustData, NewsalaryTemporaryAdjustOpTypeEnum.UPDATE);
		}else {
			newsalaryTemporaryAdjustService.save(temporaryAdjust, NewsalaryTemporaryAdjustOpTypeEnum.ADD);
		}
		record.setRemark(null);
		return mapper.updateByPrimaryKeySelective(record);
	}

	/**
	 * 批量处理
	 * @param record
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public Integer remindBatchProcessing(HrmsNewsalaryItemRemindRecord record){
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsNewsalaryItemRemindRecord> list = mapper.getList(record);
		if(CollectionUtil.isNotEmpty(list)){
			list.stream().forEach(vo->{
				vo.setTmpItem(record.getTmpItem());
				vo.setRemark("薪酬项["+vo.getItemName()+"]变动，从"+vo.getBeforeSalary()+"调整为"+vo.getAfterSalary()+",月差额："+vo.getDifferenceAmount()+",补扣月数："+vo.getBackPaymentMonth()+"个月");
				remindProcessing(vo);
			});
		}
		return 1;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryItemRemindRecord record = new HrmsNewsalaryItemRemindRecord();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	/**
	 *
	 * @Title batchNotAdjust
	 * @Description 批量不调整
	 * @param ids
	 * @return Integer
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	@Transactional(readOnly = false)
	@Override
	public  Integer batchNotAdjust(String ids){
		Assert.hasText(ids, "批量不调整项不能为空.");
		Example example = new Example(HrmsNewsalaryItemRemindRecord.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andIn("id", Arrays.asList(ids.split(",")));
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("handleStatus",0);
		HrmsNewsalaryItemRemindRecord itemRemindRecord = new  HrmsNewsalaryItemRemindRecord();
		itemRemindRecord.setHandleStatus("2");
		itemRemindRecord.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		itemRemindRecord.setUpdateDate(DateUtil.date());
		return mapper.updateByExampleSelective(itemRemindRecord,example);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteUnprocessedDataByEmployeeId(String employeeId,String computeDate){
		Assert.hasText(employeeId, "ID不能为空.");
		Assert.hasText(computeDate, "算薪周期不能为空.");
		Example example = new Example(HrmsNewsalaryItemRemindRecord.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N")
				.andEqualTo("employeeId",employeeId)
				.andEqualTo("computeDate",computeDate)
				.andEqualTo("handleStatus","0");

//		HrmsNewsalaryItemRemindRecord record = new HrmsNewsalaryItemRemindRecord();
//		record.setUpdateDate(new Date());
//		record.setIsDeleted("Y");
//		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
//		if (user != null) {
//			record.setUpdateUser(user.getUsercode());
//			record.setUpdateUserName(user.getUsername());
//		}
		return mapper.deleteByExample(example);
	}

	@Override
	public HrmsNewsalaryItemRemindRecord selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalaryItemRemindRecord> getDataSetList(Page page, HrmsNewsalaryItemRemindRecord record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsNewsalaryItemRemindRecord> records = mapper.getList(page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

    /**
     * 汇总统计员工薪酬异动数据
     * @param record
     * @return
     */
    @Override
    public DataSet<HrmsNewsalaryItemRemindRecord> getEmployeeSalaryRemindStatistics(Page page,HrmsNewsalaryItemRemindRecord record){
    	record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<HrmsNewsalaryItemRemindRecord> records = mapper.getEmployeeSalaryRemindStatistics(page,record);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

	/**
	 * 根据方案id和算薪周期查询是否存在未处理薪酬项提醒数据
	 * @param empIds 员工id列表
	 * @param computeDate
	 * @return
	 */
	public Integer checkIsExistsUnprocessedRemindDataByEmpIds(List<String> empIds, String computeDate){
		Assert.notNull(empIds, "员工集合不能为空.");
		Assert.hasText(computeDate, "算薪周期不能为空.");
		Example example = new Example(HrmsNewsalaryItemRemindRecord.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N")
				.andIn("employeeId",empIds)
				.andEqualTo("computeDate",computeDate)
				.andEqualTo("handleStatus","0");
		return mapper.selectCountByExample(example);
	}
}

package cn.trasen.hrms.salary.model;

import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;

/**
 * 员工薪酬方案关联表
 *
 */
@Table(name = "hrms_newsalary_option_emp")
@Setter
@Getter
public class HrmsNewsalaryOptionEmp {

    @Id
    private String id;

    /**
     * 人员id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "人员id")
    private String employeeId;

    @Column(name = "employee_name")
    @ApiModelProperty(value = "人员名字")
    private String employeeName;

    /**
     * 方案id
     */
    @Column(name = "option_id")
    @ApiModelProperty(value = "方案id")
    private String optionId;

    @Transient
    @ApiModelProperty(value = "方案名称")
    private String optionName;


    @Column(name = "effective_date")
    @ApiModelProperty(value = "生效时间")
    private String effectiveDate;

    @Column(name = "sort_num")
    @ApiModelProperty(value = "人员排序号")
    private Integer sortNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    @Transient
    @ApiModelProperty(value = "人员名字")
    private String employeeNo;

    @Transient
    @ApiModelProperty(value = "科室名称")
    private String orgName;
}
package cn.trasen.hrms.salary.controller;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryChangesDetailedEo;
import cn.trasen.hrms.salary.service.HrmsNewsalaryChangesDetailedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Title: HrmsPositionController.java
 * @Package cn.trasen.hrms.controller
 * @Description: 薪酬异动明细controller
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司
 * @date 2024年8月18日 上午11:48:38
 * @version V1.0
 */
@Slf4j
@Api(tags = "薪酬异动明细controller")
@RestController
public class HrmsNewsalaryChangesDetailedController {

	@Autowired
	HrmsNewsalaryChangesDetailedService detailedService;

	/**
	 * @Title: getDataList
	 * @Description: 薪酬异动明细列表
	 * @Param: page
	 * @param entity
	 * @Return: DataSet<HrmsAdvancementIncidentEo>
	 * <AUTHOR>
	 */
	@ApiOperation(value = "薪酬异动明细列表", notes = "薪酬异动明细列表")
	@GetMapping(value = "/changesDetailed/list")
	public DataSet<HrmsNewsalaryChangesDetailedEo> getDataList(Page page, HrmsNewsalaryChangesDetailedEo entity) {
		List<HrmsNewsalaryChangesDetailedEo> list = detailedService.getDataList(page, entity);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}

}

package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportMapVo;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportTotalVo;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryReportMapMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryReportTotalMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryReportMapEo;
import cn.trasen.hrms.salary.model.HrmsNewsalaryReportTotalEo;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryReportTotalService;
import cn.trasen.hrms.utils.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrmsNewsalaryOptionTotalTypeServiceImpl
 * @Description TODO
 * @date 2024  0723
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryReportTotalServiceImpl implements HrmsNewsalaryReportTotalService {

    @Autowired
    private HrmsNewsalaryReportTotalMapper mapper;
    @Autowired
    private HrmsNewsalaryReportMapMapper reportMapMapper;
    @Autowired
    private HrmsNewsalaryOptionService hrmsNewsalaryOptionService;

    @Override
    @Transactional(readOnly = false)
    public Integer save(HrmsNewsalaryReportTotalVo record) {
        //构建保存汇总配置数据
        record.setId(IdUtil.getId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getUsercode());
            record.setSsoOrgName(user.getUsername());
        }
        HrmsNewsalaryReportTotalEo hrmsNewsalaryReportTotalEo = copyObjectInfoEo(record);
        //生成一个随机colCode
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HHmmss");
        LocalDateTime dateTime = LocalDateTime.now();
        String currentYearMonth = dateTime.format(formatter);
        Random random = new Random();
        int randomIntBounded = random.nextInt(1000);
        hrmsNewsalaryReportTotalEo.setColCode(currentYearMonth + randomIntBounded);

        //构建保存汇总字段映射数据
        if (CollectionUtil.isNotEmpty(record.getReportMapVoList())) {
            record.getReportMapVoList().forEach(hrmsNewsalaryReportMapVo -> {
                hrmsNewsalaryReportMapVo.setId(IdUtil.getId());
                hrmsNewsalaryReportMapVo.setCreateDate(new Date());
                hrmsNewsalaryReportMapVo.setUpdateDate(new Date());
                hrmsNewsalaryReportMapVo.setIsDeleted("N");
                hrmsNewsalaryReportMapVo.setColCode(hrmsNewsalaryReportTotalEo.getColCode());
                hrmsNewsalaryReportMapVo.setColName(hrmsNewsalaryReportTotalEo.getColName());
                hrmsNewsalaryReportMapVo.setColId(hrmsNewsalaryReportTotalEo.getId());
                hrmsNewsalaryReportMapVo.setSsoOrgCode(user.getUsercode());
                hrmsNewsalaryReportMapVo.setSsoOrgName(user.getUsername());
                if (user != null) {
                    hrmsNewsalaryReportMapVo.setCreateUser(user.getUsercode());
                    hrmsNewsalaryReportMapVo.setCreateUserName(user.getUsername());
                    hrmsNewsalaryReportMapVo.setUpdateUser(user.getUsercode());
                    hrmsNewsalaryReportMapVo.setUpdateUserName(user.getUsername());
                }
                HrmsNewsalaryReportMapEo newsalaryOptionTotalTypeEo = copyReportInfoEo(hrmsNewsalaryReportMapVo);
                reportMapMapper.insertSelective(newsalaryOptionTotalTypeEo);
            });
        }
        return mapper.insertSelective(hrmsNewsalaryReportTotalEo);
    }

    private HrmsNewsalaryReportTotalEo copyObjectInfoEo(HrmsNewsalaryReportTotalVo record) {
        HrmsNewsalaryReportTotalEo HrmsNewsalaryReportTotalEo = new HrmsNewsalaryReportTotalEo();
        BeanUtils.copyProperties(record, HrmsNewsalaryReportTotalEo);
        return HrmsNewsalaryReportTotalEo;
    }

    @Override
    @Transactional(readOnly = false)
    public Integer update(HrmsNewsalaryReportTotalVo record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        HrmsNewsalaryReportTotalEo hrmsNewsalaryReportTotalEo = copyObjectInfoEo(record);
        mapper.updateByPrimaryKeySelective(hrmsNewsalaryReportTotalEo);

        Example example = new Example(HrmsNewsalaryReportMapEo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("colId",hrmsNewsalaryReportTotalEo.getId());
        HrmsNewsalaryReportMapEo hrmsNewsalaryReportMapEo = new HrmsNewsalaryReportMapEo();
        hrmsNewsalaryReportMapEo.setIsDeleted(Contants.IS_DELETED_TURE);
        reportMapMapper.updateByExampleSelective(hrmsNewsalaryReportMapEo,example);

        if (CollectionUtil.isNotEmpty(record.getReportMapVoList())) {
            record.getReportMapVoList().forEach(hrmsNewsalaryReportMapVo -> {
                hrmsNewsalaryReportMapVo.setId(IdUtil.getId());
                hrmsNewsalaryReportMapVo.setCreateDate(new Date());
                hrmsNewsalaryReportMapVo.setUpdateDate(new Date());
                hrmsNewsalaryReportMapVo.setIsDeleted("N");
                hrmsNewsalaryReportMapVo.setColCode(hrmsNewsalaryReportTotalEo.getColCode());
                hrmsNewsalaryReportMapVo.setColName(hrmsNewsalaryReportTotalEo.getColName());
                hrmsNewsalaryReportMapVo.setColId(hrmsNewsalaryReportTotalEo.getId());
                hrmsNewsalaryReportMapVo.setSsoOrgCode(user.getUsercode());
                hrmsNewsalaryReportMapVo.setSsoOrgName(user.getUsername());
                if (user != null) {
                    hrmsNewsalaryReportMapVo.setCreateUser(user.getUsercode());
                    hrmsNewsalaryReportMapVo.setCreateUserName(user.getUsername());
                    hrmsNewsalaryReportMapVo.setUpdateUser(user.getUsercode());
                    hrmsNewsalaryReportMapVo.setUpdateUserName(user.getUsername());
                }
                HrmsNewsalaryReportMapEo newsalaryOptionTotalTypeEo = copyReportInfoEo(hrmsNewsalaryReportMapVo);
                reportMapMapper.insertSelective(newsalaryOptionTotalTypeEo);
            });
        }
        return 1;
    }

    @Override
    @Transactional(readOnly = false)
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        HrmsNewsalaryReportTotalEo record = new HrmsNewsalaryReportTotalEo();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public PlatformResult<List<HrmsNewsalaryReportTotalVo>> getDataSetList(HrmsNewsalaryReportTotalVo record) {
        List<HrmsNewsalaryReportTotalVo> records = mapper.getDataSetList(record);
        if (CollectionUtils.isEmpty(records)){
            return PlatformResult.success(records);
        }
        records.forEach(reportTotalVo -> {
            List<HrmsNewsalaryReportMapVo> reportMapList = mapper.getReportMapList(reportTotalVo.getId());
            reportTotalVo.setReportMapVoList(reportMapList);
        });
        return PlatformResult.success(records);
    }

    @Override
    public PlatformResult<Map<String, Object>> getTotalData(HrmsNewsalaryReportTotalVo record) {
        List<String> reportMapList = mapper.getReportMapOptionList(record.getReportId());
        Map<String, Object> records = mapper.getTotalData(record,reportMapList);
        return PlatformResult.success(records);
    }

    @Override
    @Transactional(readOnly = false)
    public void sortNo(List<HrmsNewsalaryReportTotalVo> record) {
        if(!record.isEmpty()){
            for (int i = 0; i < record.size(); i++) {
                HrmsNewsalaryReportTotalEo reportMapEo = mapper.selectByPrimaryKey(record.get(i));
                reportMapEo.setUpdateDate(new Date());
                ThpsUser user = UserInfoHolder.getCurrentUserInfo();
                if (user != null) {
                    reportMapEo.setUpdateUser(user.getUsercode());
                    reportMapEo.setUpdateUserName(user.getUsername());
                }
                reportMapEo.setSortNo(record.get(i).getSortNo());
                mapper.updateByPrimaryKeySelective(reportMapEo);
            }
        }
    }

    private HrmsNewsalaryReportMapEo copyReportInfoEo(HrmsNewsalaryReportMapVo record) {
        HrmsNewsalaryReportMapEo newsalaryOptionTotalTypeEo = new HrmsNewsalaryReportMapEo();
        BeanUtils.copyProperties(record, newsalaryOptionTotalTypeEo);
        return newsalaryOptionTotalTypeEo;
    }
}

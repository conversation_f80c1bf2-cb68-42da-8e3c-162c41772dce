<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryPayrollMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryPayroll">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="option_id" jdbcType="VARCHAR" property="optionId" />
    <result column="option_payroll_id" jdbcType="VARCHAR" property="optionPayrollId" />
    <result column="payroll_date" jdbcType="VARCHAR" property="payrollDate" />
    <result column="send_status" jdbcType="CHAR" property="sendStatus" />
    <result column="send_time" jdbcType="VARCHAR" property="sendTime" />
    <result column="send_method" jdbcType="CHAR" property="sendMethod" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="employee_status" jdbcType="VARCHAR" property="employeeStatus" />
    <result column="establishment_type" jdbcType="VARCHAR" property="establishmentType" />
  </resultMap>


    <!-- 批量保存  -->
    <insert id="batchInsert">
      <![CDATA[
			INSERT INTO hrms_newsalary_payroll
			(
				id,
				employee_id,
				option_id,
				payroll_date,
                send_status,
			    create_user,
			    create_user_name,
				create_date,
			    update_date,
			    update_user,
				update_user_name,
				is_deleted,
			    is_view,
			    option_payroll_id,
			    employee_status,
			    establishment_type
			)
			VALUES
		]]>
      <foreach collection="list" index="index" item="item" separator=",">
        <![CDATA[
			(
				#{item.id},
				#{item.employeeId},
				#{item.optionId},
				#{item.payrollDate},
				#{item.sendStatus},
				#{item.createUser},
				#{item.createUserName},
				#{item.createDate},
				#{item.updateDate},
				#{item.updateUser},
				#{item.updateUserName},
				#{item.isDeleted},
			    #{item.isView},
			    #{item.optionPayrollId},
			    #{item.employeeStatus},
			    #{item.establishmentType},
			)
			]]>
      </foreach>
    </insert>


    <select id="getCount" resultType="cn.trasen.hrms.salary.DTO.SalarySendOut">
      SELECT COUNT(t1.employee_id) AS zrs,
             SUM(CASE WHEN t1.send_status='1' THEN 1 ELSE 0 END ) AS yf,
             SUM(CASE WHEN t1.send_status='0' THEN 1 ELSE 0 END ) AS wf,
             SUM(CASE WHEN t1.is_view = '1' THEN 1 ELSE 0 END ) AS yck,
             SUM(CASE WHEN t1.is_view = '0' THEN 1 ELSE 0 END ) AS wck
      FROM hrms_newsalary_payroll t1
             LEFT JOIN hrms_newsalary_option t2 ON t1.option_id=t2.id
      WHERE t1.is_deleted='N' AND  t1.payroll_date = #{computeDate}

    </select>

  <select id="getCountList" resultType="java.util.HashMap">
    SELECT
      t3.option_name AS optionName,
      t3.id as optionId,
      COUNT(t2.employee_id) AS zrs,
      SUM(CASE WHEN t2.send_status='1' THEN 1 ELSE 0 END ) AS yf,
      SUM(CASE WHEN t2.send_status='0' THEN 1 ELSE 0 END ) AS wf,
      SUM(CASE WHEN t2.is_view = '1' THEN 1 ELSE 0 END ) AS yck,
      SUM(CASE WHEN t2.is_view = '0' THEN 1 ELSE 0 END ) AS wck
    FROM hrms_newsalary_payroll t2
    LEFT JOIN hrms_newsalary_option t3 ON t2.`option_id` = t3.`id`
    WHERE t2.is_deleted='N' AND  t2.payroll_date = #{computeDate}
    GROUP BY t3.id,t3.option_name 
  </select>

  <select id="getRecordslist" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryPayroll">
      SELECT t2.employee_id,t2.employee_no,t2.employee_name,t3.name AS orgName,
             t2.personal_identity,
             t6.`ITEM_NAME` AS personal_identity_text,
             t2.establishment_type,
             t4.`ITEM_NAME` AS establishment_type_text,
             t2.employee_status,
             t5.ITEM_NAME AS employee_status_text,
             t1.send_status,t1.is_view,t1.send_time,
             t1.revocation_time,
             t1.id,
             t1.payroll_date,
             IF(t1.send_status ='1','已发送','未发送') AS  send_status_text,
             IF(t1.is_view ='1','已查看','未查看') AS  is_view_text
      FROM hrms_newsalary_payroll t1
               LEFT JOIN hrms_newsalary_option_payroll tt ON t1.`option_id`=tt.`option_id` AND tt.compute_date=#{payrollDate}
               INNER JOIN (
                  SELECT
                      employee_name,employee_no,org_id,employee_id,gender,birthday,
                      employee_status,positive_time,retirement_time,
                      position_id,year_work,bankcardname,emp_age,
                      establishment_type,salary_appoint,is_deleted,
                      personal_identity,identity_number,bankcardno,
                      entry_date,'N' as is_temp
                  FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
                  UNION ALL
                  SELECT
                      employee_name,employee_no,org_id,id as employee_id,gender,birthday,
                      (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
                      null as position_id,null as year_work,null as bankcardname,null as emp_age,
                      tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
                      tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
                      join_date as entry_date,'Y' as is_temp
                  FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
            ) t2 ON t1.employee_id=t2.employee_id AND t2.is_deleted='N'
               LEFT JOIN comm_organization t3 ON t2.org_id=t3.organization_id
               LEFT JOIN (
                  SELECT
                  A.*
                  FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                  WHERE
                  B.TYPE_CODE = 'establishment_type'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
               ) t4 ON t2.establishment_type = t4.item_code
               LEFT JOIN (
                  SELECT
                  A.*
                  FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                  WHERE
                  B.TYPE_CODE = 'employee_status'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
               ) t5 ON t2.employee_status = t5.item_code
               LEFT JOIN (
                  SELECT
                  A.*
                  FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                  WHERE
                  B.TYPE_CODE = 'personal_identity'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
               ) t6 ON t2.personal_identity = t6.item_code
      WHERE  t1.is_deleted='N' and  tt.pay_slip='1' AND t1.option_id=#{optionId}
      and t1.payroll_date = #{payrollDate}
      <if test="employeeName != null and employeeName != ''">
          and (( t2.employee_Name LIKE CONCAT('%', #{employeeName}, '%')) or (t2.employee_no LIKE CONCAT('%', #{employeeName}, '%')))
      </if>

      <if test="orgName != null and orgName != ''">
          AND t3.name like CONCAT('%', #{orgName}, '%')
      </if>
      <if test="sendStatus != null and (sendStatus == '0'.toString() or sendStatus == '1'.toString())">
          AND t1.send_status = #{sendStatus}
      </if>
      <if test="sendStatus != null and sendStatus == '2'.toString()">
          AND t1.is_view = '1'
      </if>
      <if test="sendStatus != null and sendStatus == '3'.toString()">
          AND t1.is_view = '0'
      </if>
      <if test="isView != null">
          AND t1.is_view = #{isView}
      </if>



  </select>

    <!-- 点击统计的明细 -->
    <select id="getCountTitleDetails" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryPayroll">
        SELECT t2.employee_id,t2.employee_no,t2.employee_name,t3.name AS orgName,
               t2.personal_identity,
               t6.`ITEM_NAME` AS personal_identity_text,
               t2.establishment_type,
               t4.`ITEM_NAME` AS establishment_type_text,
               t2.employee_status,
               t5.ITEM_NAME AS employee_status_text,
               t1.send_status,t1.is_view,t1.send_time,
               t1.revocation_time,
               t1.payroll_date,
               t1.id,
               IF(t1.send_status ='1','已发送','未发送') AS  send_status_text,
               IF(t1.is_view ='1','已查看','未查看') AS  is_view_text
        FROM hrms_newsalary_payroll t1
                 LEFT JOIN hrms_newsalary_option_payroll tt ON t1.`option_id`=tt.`option_id` AND tt.compute_date=#{payrollDate}
                 INNER JOIN (
                    SELECT
                        employee_name,employee_no,org_id,employee_id,gender,birthday,
                        employee_status,establishment_type,salary_appoint,is_deleted,
                        personal_identity,'N' as is_temp
                    FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
                    UNION ALL
                    SELECT
                        employee_name,employee_no,org_id,id as employee_id,gender,birthday,
                        (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,
                        tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
                        tmp_position as personal_identity,'Y' as is_temp
                    FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
                  ) t2 ON t1.employee_id=t2.employee_id AND t2.is_deleted='N'
                 LEFT JOIN comm_organization t3 ON t2.org_id=t3.organization_id
                 LEFT JOIN (
                        SELECT
                        A.*
                        FROM
                        COMM_DICT_ITEM A
                        LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                        WHERE
                        B.TYPE_CODE = 'establishment_type'
                        AND B.IS_DELETED = 'N'
                        AND A.IS_DELETED = 'N'
                        AND A.IS_ENABLE = '1'
                        AND A.sso_org_code = #{ssoOrgCode}
                 ) t4 ON t2.establishment_type = t4.item_code
                 LEFT JOIN (
                    SELECT
                    A.*
                    FROM
                    COMM_DICT_ITEM A
                    LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                    WHERE
                    B.TYPE_CODE = 'employee_status'
                    AND B.IS_DELETED = 'N'
                    AND A.IS_DELETED = 'N'
                    AND A.IS_ENABLE = '1'
                    AND A.sso_org_code = #{ssoOrgCode}
                 ) t5 ON t2.employee_status = t5.item_code
                 LEFT JOIN  (
                    SELECT
                    A.*
                    FROM
                    COMM_DICT_ITEM A
                    LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                    WHERE
                    B.TYPE_CODE = 'personal_identity'
                    AND B.IS_DELETED = 'N'
                    AND A.IS_DELETED = 'N'
                    AND A.IS_ENABLE = '1'
                    AND A.sso_org_code = #{ssoOrgCode}
                 ) t6 ON t2.personal_identity = t6.item_code
        WHERE  t1.is_deleted='N'   AND t1.payroll_date = #{payrollDate}
        <!-- 已发送 -->
        <if test='searchStatus =="1"'>
           and  t1.send_status ='1'
        </if>
        <!-- 未发送 -->
        <if test='searchStatus =="2"'>
            and  t1.send_status ='0'
        </if>
        <!-- 未查看  -->
        <if test='searchStatus =="3"'>
            and t1.is_view ='0'
        </if>
        <!-- 已查看-->
        <if test='searchStatus =="4"'>
            and t1.is_view ='1'
        </if>

        <if test="employeeName != null and employeeName != ''">
            and (( t2.employee_Name LIKE CONCAT('%', #{employeeName}, '%')) or (t2.employee_no LIKE CONCAT('%', #{employeeName}, '%')))
        </if>

        <if test="orgName != null and orgName != ''">
            AND t3.name like CONCAT('%', #{orgName}, '%')
        </if>

    </select>

    <update id="revocation" parameterType="cn.trasen.hrms.salary.model.HrmsNewsalaryPayroll">
        UPDATE hrms_newsalary_payroll SET send_status='0',
      send_time = NULL,is_view='0',
      revocation_time = NOW()
      WHERE option_id= #{optionId} and payroll_date = #{payrollDate}
      <if test="idList != null and idList.size() > 0">
        	AND id in
	        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
	            #{id,jdbcType=VARCHAR}
	        </foreach>
      </if>
      
    </update>
    <update id="singleRevocation" parameterType="cn.trasen.hrms.salary.model.HrmsNewsalaryPayroll">
        UPDATE hrms_newsalary_payroll SET send_status='0',
        send_time = NULL,is_view='0',
        revocation_time = NOW()
        WHERE id=#{id}
    </update>

    <update id="singleSend">
        UPDATE hrms_newsalary_payroll SET send_status='1',
        send_time = NOW(),is_view='0',
        revocation_time = NULL
        WHERE id=#{id}
    </update>
    
    <select id="selectNewsalaryPayrollList" parameterType="cn.trasen.hrms.salary.DTO.HrmsNewsalaryPayrollVo" resultType="cn.trasen.hrms.salary.DTO.HrmsNewsalaryPayrollVo">
    		SELECT
				t2.slip_name as slipName,
				t1.option_id as optionId,
				t1.payroll_date as payrollDate,
				count( 1 ) AS payrollNumber,
				sum(case when send_status = 0 then 1 else 0 end) as unsendNumber,
				sum(case when send_status = 1 then 1 else 0 end) as sendNumber,
				sum(case when is_view = 0 then 1 else 0 end) as unviewNumber,
				sum(case when is_view = 1 then 1 else 0 end) as viewNumber
			FROM
				hrms_newsalary_payroll t1
				LEFT JOIN hrms_newsalary_payslip t2 ON t1.option_id = t2.option_id 
				LEFT JOIN hrms_newsalary_option_payroll t3 on t1.option_id = t3.option_id and t1.payroll_date = t3.compute_date
			WHERE
				t1.is_deleted = 'N' 
				and t2.is_deleted = 'N'
				and t3.compute_status = '3'
				<if test="slipName != null and slipName != ''">
					and t2.slip_name like CONCAT('%', #{slipName}, '%')
				</if>
				<if test="payrollDate != null and payrollDate != ''">
					and t1.payroll_date = #{payrollDate}
				</if>
			GROUP BY t1.option_id,t1.payroll_date,t2.slip_name 
			ORDER BY
				t1.payroll_date DESC 
    </select>

	<!--<select id="summaryData" parameterType="cn.trasen.hrms.salary.DTO.HrmsNewsalaryPayrollVo" resultType="Map">-->
		<!--&lt;!&ndash;  select -->
			<!--(select ifnull(count(1),0) from hrms_newsalary_payroll-->
			<!--where option_id = #{optionId}-->
            <!--<if test="startDate != null and startDate != '' and endDate != null and endDate != '' and startDate != endDate">-->
                <!--<![CDATA[ and  payroll_date >= #{startDate} AND  payroll_date <= #{endDate} ]]>-->
            <!--</if>-->
            <!--<if test="startDate != null and startDate != '' and endDate != null and endDate != '' and startDate == endDate">-->
                <!--and  payroll_date = #{startDate}-->
            <!--</if>            -->
			 <!--and is_deleted = 'N') as numbers,-->
		<!---->
		 	<!--(select ifnull(sum(t1.salary),0) from hrms_newsalary_payroll_detail t1-->
			<!--LEFT JOIN hrms_newsalary_item t2 on t1.item_id = t2.id and t1.option_id = t2.option_id -->
			<!--where t1.option_id = #{optionId}-->
            <!--<if test="startDate != null and startDate != '' and endDate != null and endDate != '' and startDate != endDate">-->
                <!--<![CDATA[ and  t1.payroll_date >= #{startDate} AND  t1.payroll_date <= #{endDate} ]]>-->
            <!--</if>-->
            <!--<if test="startDate != null and startDate != '' and endDate != null and endDate != '' and startDate == endDate">-->
                <!--and  t1.payroll_date = #{startDate}-->
            <!--</if>-->
			<!--and t2.sh_salary = '1') as yfSalary,-->
		<!---->
			<!--(select ifnull(sum(t1.salary),0) from hrms_newsalary_payroll_detail t1-->
			<!--LEFT JOIN hrms_newsalary_item t2 on t1.item_id = t2.id and t1.option_id = t2.option_id -->
			<!--where t1.option_id = #{optionId}-->
			<!---->
            <!--<if test="startDate != null and startDate != '' and endDate != null and endDate != '' and startDate != endDate">-->
                <!--<![CDATA[ and  t1.payroll_date >= #{startDate} AND  t1.payroll_date <= #{endDate} ]]>-->
            <!--</if>-->
            <!--<if test="startDate != null and startDate != '' and endDate != null and endDate != '' and startDate == endDate">-->
                <!--and  t1.payroll_date = #{startDate}-->
            <!--</if>-->
			 <!--and t2.personal_tax = '1') as gsSalary,-->
		<!---->
			<!--(select ifnull(sum(t1.salary),0) from hrms_newsalary_payroll_detail t1-->
			<!--LEFT JOIN hrms_newsalary_item t2 on t1.item_id = t2.id and t1.option_id = t2.option_id -->
			<!--where t1.option_id = #{optionId}-->
            <!--<if test="startDate != null and startDate != '' and endDate != null and endDate != '' and startDate != endDate">-->
                <!--<![CDATA[ and  t1.payroll_date >= #{startDate} AND  t1.payroll_date <= #{endDate} ]]>-->
            <!--</if>-->
            <!--<if test="startDate != null and startDate != '' and endDate != null and endDate != '' and startDate == endDate">-->
                <!--and  t1.payroll_date = #{startDate}-->
            <!--</if>-->
			 <!--and t2.actual_salary = '1') as sfSalary-->
			  <!--&ndash;&gt;-->
			  <!--&lt;!&ndash; 重新优化此SQL逻辑 <NAME_EMAIL> 2024/09/13 &ndash;&gt;-->
			<!--SELECT-->
			    <!--COUNT(DISTINCT p.id) AS numbers,-->
			    <!--COALESCE(SUM(CASE WHEN si.sh_salary = '1' THEN pd.salary ELSE 0 END), 0) AS yfSalary,-->
			    <!--COALESCE(SUM(CASE WHEN si.personal_tax = '1' THEN pd.salary ELSE 0 END), 0) AS gsSalary,-->
			    <!--COALESCE(SUM(CASE WHEN si.actual_salary = '1' THEN pd.salary ELSE 0 END), 0) AS sfSalary-->
			<!--FROM-->
			    <!--hrms_newsalary_payroll p-->
			<!--LEFT JOIN-->
			    <!--hrms_newsalary_payroll_detail pd ON p.id = pd.payroll_id-->
			<!--LEFT JOIN-->
			    <!--hrms_newsalary_item si ON pd.item_id = si.id AND pd.option_id = si.option_id-->
			<!--WHERE-->
			    <!--p.option_id = #{optionId}-->
			    <!--<if test="startDate != null and startDate != '' and endDate != null and endDate != '' and startDate != endDate">-->
                	<!--<![CDATA[ and  p.payroll_date >= #{startDate} AND  p.payroll_date <= #{endDate} ]]>-->
	            <!--</if>-->
	            <!--<if test="startDate != null and startDate != '' and endDate != null and endDate != '' and startDate == endDate">-->
	                <!--and  p.payroll_date = #{startDate}-->
	            <!--</if>-->
			    <!--AND p.is_deleted = 'N'-->
	<!--</select>-->

    <select id="summaryData" parameterType="cn.trasen.hrms.salary.DTO.HrmsNewsalaryPayrollVo" resultType="Map">
        SELECT
        COUNT(DISTINCT p.id) AS numbers,
        COALESCE(SUM(pd.salary), 0) AS totalSalary,
        si.total_title_name as totalTileName
        FROM
        hrms_newsalary_payroll p
        LEFT JOIN
        hrms_newsalary_payroll_detail pd ON p.id = pd.payroll_id
        LEFT JOIN
        hrms_newsalary_item si ON pd.item_id = si.id AND pd.option_id = si.option_id
        WHERE si.is_total = '1'
        and p.option_id = #{optionId}
        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' and startDate != endDate">
            <![CDATA[ and  p.payroll_date >= #{startDate} AND  p.payroll_date <= #{endDate} ]]>
        </if>
        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' and startDate == endDate">
            and  p.payroll_date = #{startDate}
        </if>
        AND p.is_deleted = 'N'
        group by si.id
        order by si.total_sort_no
    </select>
	
	<select id="selectEmpPayroll" parameterType="String" resultType="String">
			select t2.employee_no from hrms_newsalary_payroll t1
			LEFT JOIN (
                SELECT
                  employee_name,employee_no,org_id,employee_id,is_deleted,emp_payroll
                FROM cust_emp_base
                UNION ALL
                SELECT
                  employee_name,employee_no,org_id,id as employee_id,is_deleted,employee_no as emp_payroll
                FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
            ) t2 on t1.employee_id = t2.employee_id AND t2.is_deleted='N'
			where option_id = #{optionId} and payroll_date = #{computeDate} and t1.is_deleted = 'N'
			<if test="employeeId != null and employeeId != ''">
				and t2.employee_id = #{employeeId}
			</if>
			<if test="employeeIds != null and employeeIds.size() > 0">
	        	and t2.employee_id in
		        <foreach collection="employeeIds" index="index" item="employeeId" open="(" separator="," close=")">
		            #{employeeId}
		        </foreach>
     		</if>
			
	</select>

	<select id="sendSalaryDeatilsSummaryData" parameterType="cn.trasen.hrms.salary.DTO.HrmsNewsalaryPayrollVo" resultType="Map">
		SELECT
			count( 1 ) AS payrollNumber,
			ifnull(sum(case when send_status = 1 then 1 else 0 end),0) as sendNumber,
			ifnull(sum(case when send_status = 0 then 1 else 0 end),0) as unsendNumber,
			ifnull(sum(case when is_view = 1 then 1 else 0 end),0) as viewNumber,
			ifnull(sum(case when is_view = 0 then 1 else 0 end),0) as unviewNumber,
			ifnull(sum(case when send_status = 0 then 1 else 0 end),0) as revocationNumber
		FROM
			hrms_newsalary_payroll t1
		WHERE
			t1.is_deleted = 'N' 
			and t1.option_id = #{optionId}
			and t1.payroll_date = #{payrollDate}
	</select>
	<select id="getPayrollByEmployeeId" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT m.item_name as itemName,p.salary as salary,m.group_id as groupId
         FROM hrms_newsalary_payroll r left join hrms_newsalary_payroll_detail p
        on r.id = p.payroll_id
        LEFT JOIN hrms_newsalary_item m on p.item_id = m.id and r.option_id = m.option_id 
         where r.employee_id = #{employeeId}
        and r.payroll_date = #{computeDate}
        order by m.group_id, m.library_type, m.sort_no 
    </select>
    <select id="getPayrollByEmpId" parameterType="java.lang.String" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryPayroll">
    	select option_payroll_id from hrms_newsalary_payroll 
    	where employee_id = #{employeeId} and payroll_date = #{payrollDate} and option_id = #{optionId} and is_deleted = 'N' 
    </select>
</mapper>
package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemRemindSetting;

import java.util.List;

/**
 * @ClassName HrmsNewsalaryItemRemindSettingService
 * @Description TODO
 * @date 2024��10��29�� ����9:55:31
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryItemRemindSettingService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryItemRemindSetting record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryItemRemindSetting record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 *
	 * @Title deleteById
	 * @Description 根据提醒设置id删除
	 * @param remindId
	 * @return Integer
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	Integer deleteByRemindId(String remindId);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryItemRemindSetting
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	HrmsNewsalaryItemRemindSetting selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryItemRemindSetting>
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryItemRemindSetting> getDataSetList(Page page, HrmsNewsalaryItemRemindSetting record);

	/**
	 * @Title getDataSetList
	 * @Description 查询薪酬薪资设置列表
	 * @param record
	 * @return DataSet<HrmsNewsalaryItemRemindSetting>
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	List<HrmsNewsalaryItemRemindSetting> getList(HrmsNewsalaryItemRemindSetting record);
}

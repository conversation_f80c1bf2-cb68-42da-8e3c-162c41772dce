package cn.trasen.hrms.salary.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportsVo;
import cn.trasen.hrms.salary.model.HrmsNewsalaryReportsEo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * @Entity generator.domain.HrmsNewsalaryReports
 */
public interface HrmsNewsalaryReportsMapper extends Mapper<HrmsNewsalaryReportsEo> {

    List<HrmsNewsalaryReportsVo> getDataSetList(@Param("reportsVo") HrmsNewsalaryReportsVo reportsVo, Page page);

}





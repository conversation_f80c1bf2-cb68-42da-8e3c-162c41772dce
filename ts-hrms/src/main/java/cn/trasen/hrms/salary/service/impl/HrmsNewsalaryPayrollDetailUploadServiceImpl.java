package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.dao.HrmsEmployeeMapper;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.salary.dao.*;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailImport;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailUploadEo;
import cn.trasen.hrms.salary.model.HrmsUploadRecordEo;
import cn.trasen.hrms.salary.service.HrmsNewsalaryPayrollDetailUploadService;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import cn.trasen.hrms.service.HrmsTimekeeperService;
import cn.trasen.hrms.utils.IdUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.beust.jcommander.internal.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryPayrollDetailUploadServiceImpl implements HrmsNewsalaryPayrollDetailUploadService {

    @Autowired
    private HrmsEmployeeMapper employeeMapper;
    @Autowired
    private HrmsNewsalaryPayrollDetailUploadMapper mapper;
    @Autowired
    DictItemFeignService dictItemFeignService;
    @Autowired
    private HrmsNewsalaryPayrollDetailImportMapper importMapper;
    @Autowired
    private HrmsTimekeeperService hrmsTimekeeperService;
    @Autowired
    private HrmsUploadRecordMapper recordMapper;
    @Autowired
    private HrmsNewsalaryOptionPayrollMapper payrollMapper;
    private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryPayrollDetailUploadServiceImpl.class);

    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> save(List<Map<String, Object>> record) {
        StringBuilder message = new StringBuilder();
        if (CollectionUtils.isNotEmpty(record)) {
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            for (Map<String, Object> stringObjectMap : record) {
                Object employeeNo = stringObjectMap.get("employee_no");
                Object employeeId = stringObjectMap.get("employee_id");
                Object effectiveDate = stringObjectMap.get("effective_date");
                if (Objects.isNull(effectiveDate)) {
                    return PlatformResult.failure("上报时间不为空");
                }
                Object id = stringObjectMap.get("id");
                //是否上报过
                Example example = new Example(HrmsNewsalaryPayrollDetailUploadEo.class);
                List<String> list = new ArrayList<>();
                list.add("1");
                list.add("2");
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
                criteria.andEqualTo("employeeNo", employeeNo.toString());
                criteria.andEqualTo("payrollDate", effectiveDate.toString());
                criteria.andIn("isExamine", list);
                int i = mapper.selectCountByExample(example);
                if (i > 0) {
                    Object employeeName = stringObjectMap.get("employee_name");
                    message.append(employeeName);
                    message.append(",");
                    continue;
                }

                if (Objects.nonNull(id)) {
                    HrmsNewsalaryPayrollDetailUploadEo newUploadEo = new HrmsNewsalaryPayrollDetailUploadEo();
                    newUploadEo.setEmployeeNo(employeeNo.toString());
                    newUploadEo.setPayrollDate(effectiveDate.toString());
                    mapper.delete(newUploadEo);
                }
                //保存上报数据列表
                List<HrmsNewsalaryPayrollDetailUploadEo> basicItemIds = mapper.getItemList(effectiveDate.toString());
                if (CollectionUtils.isEmpty(basicItemIds)){
                     basicItemIds = getAllList();
                }

                for (HrmsNewsalaryPayrollDetailUploadEo detailUploadEo : basicItemIds) {
                    HrmsNewsalaryPayrollDetailUploadEo newEo = new HrmsNewsalaryPayrollDetailUploadEo();
                    newEo.setItemId(detailUploadEo.getItemId());
                    newEo.setItemName(detailUploadEo.getItemName());
                    String salary = stringObjectMap.get(detailUploadEo.getItemId()).toString();
                    newEo.setSalary(new BigDecimal(Strings.isEmpty(salary) ? "0" : salary));
                    newEo.setEmployeeNo(employeeNo.toString());
                    newEo.setId(IdUtil.getId());
                    newEo.setCreateDate(new Date());
                    newEo.setUpdateDate(new Date());
                    newEo.setIsDeleted("N");
                    newEo.setIsExamine(1);
                    newEo.setPayrollDate(effectiveDate.toString());

                    List<String> opionList = mapper.queryOptionId(employeeNo.toString(), detailUploadEo.getItemId());
                    newEo.setOptionId(opionList.get(0));
                    if (user != null) {
                        newEo.setCreateUser(user.getUsercode());
                        newEo.setCreateUserName(user.getUsername());
                        newEo.setUpdateUser(user.getUsercode());
                        newEo.setUpdateUserName(user.getUsername());
                    }
                    mapper.insertSelective(newEo);
                }

                //查询审核人列表是否存在
                Example example1 = new Example(HrmsUploadRecordEo.class);
                Example.Criteria criteria1 = example1.createCriteria();
                criteria1.andEqualTo(Contants.IS_DELETED_FIELD, "N");
                criteria1.andEqualTo("employeeId", employeeId.toString());
                criteria1.andEqualTo("uploadDate", effectiveDate.toString());
                criteria1.andEqualTo("employeeNo", employeeNo.toString());
                HrmsUploadRecordEo recordEo = recordMapper.selectOneByExample(example1);
                if (Objects.isNull(recordEo)) {
                    HrmsUploadRecordEo hrmsUploadRecordEo = new HrmsUploadRecordEo();
                    hrmsUploadRecordEo.setRecordId(IdUtil.getId());
                    hrmsUploadRecordEo.setEmployeeId(employeeId.toString());
                    hrmsUploadRecordEo.setApprovalStatus("1"); // 审核状态 0审核
                    hrmsUploadRecordEo.setCreateDate(new Date());
                    hrmsUploadRecordEo.setBelongOrg("");
                    hrmsUploadRecordEo.setEmployeeNo(employeeNo.toString());
                    hrmsUploadRecordEo.setCreateUser(UserInfoHolder.getCurrentUserCode());
                    hrmsUploadRecordEo.setCreateUserName(UserInfoHolder.getCurrentUserName());
                    hrmsUploadRecordEo.setIsDeleted(Contants.IS_DELETED_FALSE);
                    hrmsUploadRecordEo.setUploadDate(effectiveDate.toString());
                    hrmsUploadRecordEo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                    if (UserInfoHolder.getCurrentUserInfo() != null) {
                        hrmsUploadRecordEo.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
                        hrmsUploadRecordEo.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
                    }
                    recordMapper.insertSelective(hrmsUploadRecordEo);
                } else {
                    recordEo.setApprovalStatus("1");
                    recordMapper.updateByPrimaryKey(recordEo);
                }

            }
        }
        if (StringUtils.isNotEmpty(message)) {
            message.append("已存在审批！");
            return PlatformResult.success(message.toString());
        }

        return PlatformResult.success();
    }

    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> update(List<String> record) {
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        for (String id : record) {
            HrmsNewsalaryPayrollDetailUploadEo hrmsNewsalaryPayrollDetailUploadEo = new HrmsNewsalaryPayrollDetailUploadEo();
            hrmsNewsalaryPayrollDetailUploadEo.setId(id);
            HrmsNewsalaryPayrollDetailUploadEo uploadEo = mapper.selectByPrimaryKey(id);
            if (Objects.isNull(uploadEo)) {
                continue;
            }
            //已审核不能修改
            if (Objects.equals(uploadEo.getIsExamine(), 2)) {
                return PlatformResult.failure("已审核不能修改");
            }
            Example example1 = new Example(HrmsNewsalaryPayrollDetailUploadEo.class);
            Example.Criteria criteria1 = example1.createCriteria();
            criteria1.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            criteria1.andEqualTo("employeeNo", uploadEo.getEmployeeNo());
            criteria1.andEqualTo("payrollDate", uploadEo.getPayrollDate());
            List<HrmsNewsalaryPayrollDetailUploadEo> uploadList = mapper.selectByExample(example1);
            for (HrmsNewsalaryPayrollDetailUploadEo hrmsNewsalaryPayroll : uploadList) {
                hrmsNewsalaryPayroll.setUpdateDate(new Date());
                if (user != null) {
                    hrmsNewsalaryPayroll.setUpdateUser(user.getUsercode());
                    hrmsNewsalaryPayroll.setUpdateUserName(user.getUsername());
                }
                hrmsNewsalaryPayroll.setIsExamine(2);
                mapper.updateByPrimaryKeySelective(hrmsNewsalaryPayroll);

                //保存到导入表
                Example example2 = new Example(HrmsNewsalaryPayrollDetailImport.class);
                Example.Criteria criteria2 = example2.createCriteria();
                criteria2.andEqualTo(Contants.IS_DELETED_FIELD, "N");
                criteria2.andEqualTo("employeeNo", hrmsNewsalaryPayroll.getEmployeeNo());
                criteria2.andEqualTo("optionId", hrmsNewsalaryPayroll.getOptionId());
                criteria2.andEqualTo("importDate", hrmsNewsalaryPayroll.getPayrollDate());
                criteria2.andEqualTo("itemId", hrmsNewsalaryPayroll.getItemId());
                HrmsNewsalaryPayrollDetailImport importEo = importMapper.selectOneByExample(example2);
                if (Objects.nonNull(importEo)) {
                    importEo.setUpdateDate(new Date());
                    importEo.setSalary(uploadEo.getSalary());
                    importMapper.updateByPrimaryKeySelective(importEo);
                } else {
                    HrmsNewsalaryPayrollDetailImport hrmsNewsalaryPayrollDetailImport = new HrmsNewsalaryPayrollDetailImport();
                    BeanUtils.copyProperties(hrmsNewsalaryPayroll, hrmsNewsalaryPayrollDetailImport);
                    hrmsNewsalaryPayrollDetailImport.setId(IdUtil.getId());
                    hrmsNewsalaryPayrollDetailImport.setImportDate(hrmsNewsalaryPayroll.getPayrollDate());
                    importMapper.insertSelective(hrmsNewsalaryPayrollDetailImport);
                }

            }

            //更新审核表状态
            Example recordExample = new Example(HrmsUploadRecordEo.class);
            Example.Criteria recordCriteria = recordExample.createCriteria();
            recordCriteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            recordCriteria.andEqualTo("employeeNo", uploadEo.getEmployeeNo());
            recordCriteria.andEqualTo("uploadDate", uploadEo.getPayrollDate());
            HrmsUploadRecordEo recordEo = recordMapper.selectOneByExample(recordExample);
            if (Objects.isNull(recordEo)) {
                continue;
            }
            recordEo.setApprovalStatus("2");
            recordEo.setUpdateDate(new Date());
            recordEo.setUpdateUser(user.getUsercode());
            //保存审核人
            recordEo.setDeptCheckUserid(user.getUsercode());
            recordMapper.updateByPrimaryKey(recordEo);
        }
        return PlatformResult.success();
    }

    @Override
    public HrmsNewsalaryPayrollDetailUploadEo selectById(String id) {
        return null;
    }

    @Override
    @Transactional(readOnly = false)
    public Integer deleteById(List<String> ids) {
        for (String id : ids) {
            HrmsNewsalaryPayrollDetailUploadEo record = new HrmsNewsalaryPayrollDetailUploadEo();
            record.setId(id);
            record.setUpdateDate(new Date());
            record.setIsDeleted("Y");
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user != null) {
                record.setUpdateUser(user.getUsercode());
                record.setUpdateUserName(user.getUsername());
            }
            return mapper.updateByPrimaryKeySelective(record);
        }
        return 1;
    }

    @Override
    public PlatformResult<List<Map<String, Object>>> getDataSetList(HrmsNewsalaryPayrollDetailUploadEo record) {
        ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
        List<HrmsNewsalaryPayrollDetailUploadEo> list = mapper.getItemList(record.getPayrollDate());

        List<Map<String, Object>> rows = null;
        List<String> emploeeyList = mapper.getEmployeeList(thpsUser.getId());

        //获取所有列 当参数传进去用于 case when(未上报数据查询项目配置列)
        if (CollectionUtils.isEmpty(emploeeyList) || Objects.isNull(record.getPayrollDate())) {
            return PlatformResult.success(rows);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> codeList = mapper.getEmploeeIdByCode(thpsUser.getId(), record.getPayrollDate());
            List<String> newList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(codeList)) {
                newList.addAll(emploeeyList);
                newList.removeAll(codeList);
            }
            //上报数据查询已经存在的列list
            rows = mapper.queryUploadList(record, list, emploeeyList, newList);
        } else {
            List<HrmsNewsalaryPayrollDetailUploadEo> basicItemIds = getAllList();
            rows = mapper.listTableData(record, basicItemIds, emploeeyList);
        }

        return PlatformResult.success(rows);
    }

    private List<HrmsNewsalaryPayrollDetailUploadEo> getAllList() {
        return mapper.getAllList();
    }

    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }

    @Override
    public List<VueTableEntity> salaryCountTitle(String payrollDate) {
        List<HrmsNewsalaryPayrollDetailUploadEo> uploadList = mapper.getItemList(payrollDate);

        List<VueTableEntity> retVueTableEntity = new ArrayList<>();
        retVueTableEntity.add(new VueTableEntity("工号", "employee_no", null, 80, null, true));
        retVueTableEntity.add(new VueTableEntity("姓名", "employee_name", null, 80, null, true));
        retVueTableEntity.add(new VueTableEntity("部门", "orgName", null, 80, null, true));
        retVueTableEntity.add(new VueTableEntity("状态", "text", null, 80, null, true));
        int width= 22;
        if (CollectionUtils.isNotEmpty(uploadList)) {
            uploadList.forEach(item -> {
                retVueTableEntity.add(new VueTableEntity(item.getItemName(), item.getItemId(), null, item.getItemName().length() == 2 ? 80 : item.getItemName().length() * width, null, false));
            });
        } else {
            List<HrmsNewsalaryPayrollDetailUploadEo> list = mapper.getAllList();
            if (!list.isEmpty() && list.size() > 0) {
                list.forEach(item -> {
                    retVueTableEntity.add(new VueTableEntity(item.getItemName(), item.getItemId(), null, item.getItemName().length() == 2 ? 80 : item.getItemName().length() * width, null, false));
                });
            }
        }
        retVueTableEntity.add(new VueTableEntity("处理人", "update_user_name", null, 100, null, true));
        retVueTableEntity.add(new VueTableEntity("最后处理日期", "update_date", null, 220, null, true));
        return retVueTableEntity;
    }

    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> rejectDetailUpload(List<String> record) {
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        for (String id : record) {
            HrmsNewsalaryPayrollDetailUploadEo uploadEo = mapper.selectByPrimaryKey(id);
            if (Objects.isNull(uploadEo)) {
                continue;
            }
            Example example3 = new Example(HrmsNewsalaryOptionPayroll.class);
            Example.Criteria criteria3 = example3.createCriteria();
            criteria3.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            criteria3.andEqualTo("optionId", uploadEo.getOptionId());
            criteria3.andEqualTo("computeDate", uploadEo.getPayrollDate());
            HrmsNewsalaryOptionPayroll payroll = payrollMapper.selectOneByExample(example3);
            if (Objects.nonNull(payroll) && Objects.equals(payroll.getComputeStatus(), "3")) {
                return PlatformResult.failure("已锁定，不能驳回！");
            }

            Example example1 = new Example(HrmsNewsalaryPayrollDetailUploadEo.class);
            Example.Criteria criteria1 = example1.createCriteria();
            criteria1.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            criteria1.andEqualTo("employeeNo", uploadEo.getEmployeeNo());
            criteria1.andEqualTo("payrollDate", uploadEo.getPayrollDate());
            List<HrmsNewsalaryPayrollDetailUploadEo> uploadList = mapper.selectByExample(example1);
            uploadList.forEach(hrmsNewsalaryPayrollDetailUploadEo -> {
                hrmsNewsalaryPayrollDetailUploadEo.setUpdateDate(new Date());
                hrmsNewsalaryPayrollDetailUploadEo.setIsExamine(0);
                hrmsNewsalaryPayrollDetailUploadEo.setIsDeleted("N");
                if (user != null) {
                    hrmsNewsalaryPayrollDetailUploadEo.setUpdateUser(user.getUsercode());
                    hrmsNewsalaryPayrollDetailUploadEo.setUpdateUserName(user.getUsername());
                }
                mapper.updateByPrimaryKeySelective(hrmsNewsalaryPayrollDetailUploadEo);
            });

            //修改导入表状态
            Example example2 = new Example(HrmsNewsalaryPayrollDetailImport.class);
            Example.Criteria criteria2 = example2.createCriteria();
            criteria2.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            criteria2.andEqualTo("importDate", uploadEo.getPayrollDate());
            criteria2.andEqualTo("employeeNo", uploadEo.getEmployeeNo());
            List<HrmsNewsalaryPayrollDetailImport> detailImportList = importMapper.selectByExample(example2);
            if (CollectionUtils.isNotEmpty(detailImportList)) {
                detailImportList.forEach(detailImport -> {
                    detailImport.setIsDeleted("Y");
                    importMapper.updateByPrimaryKeySelective(detailImport);
                });
            }

            //更新审核表状态
            Example recordExample = new Example(HrmsUploadRecordEo.class);
            Example.Criteria recordCriteria = recordExample.createCriteria();
            recordCriteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            recordCriteria.andEqualTo("employeeNo", uploadEo.getEmployeeNo());
            recordCriteria.andEqualTo("uploadDate", uploadEo.getPayrollDate());
            HrmsUploadRecordEo recordEo = recordMapper.selectOneByExample(recordExample);
            if (Objects.isNull(recordEo)) {
                continue;
            }
            recordEo.setApprovalStatus("0");
            recordEo.setUpdateDate(new Date());
            recordEo.setUpdateUser(user.getUsercode());
            recordMapper.updateByPrimaryKey(recordEo);
        }
        return PlatformResult.success();
    }

    @Override
    public DataSet<Map<String, Object>> getDataSetListPage(Page page, HrmsNewsalaryPayrollDetailUploadEo record) {
        ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
        String usercode = thpsUser.getUsercode();
        //获取所有列 当参数传进去用于 case when
        List<HrmsNewsalaryPayrollDetailUploadEo> list = mapper.getItemList(record.getPayrollDate());
        record.setUserCode(usercode);
        List<Map<String, Object>> rows = mapper.listTableDataPage(page, record, list, null);

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), rows);
    }

    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> updateDetailUpload(Map<String, Object> stringObjectMap) {
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        Object employeeNo = stringObjectMap.get("employee_no");
        Object id = stringObjectMap.get("id");
        Object effectiveDate = stringObjectMap.get("effective_date");

        //是否上报过
        HrmsNewsalaryPayrollDetailUploadEo uploadEo = mapper.selectByPrimaryKey(id);
        if (Objects.isNull(uploadEo)) {
            return PlatformResult.failure("审核通过状态不能修改");
        }
        HrmsNewsalaryPayrollDetailUploadEo newUploadEo = new HrmsNewsalaryPayrollDetailUploadEo();
        newUploadEo.setEmployeeNo(employeeNo.toString());
        newUploadEo.setPayrollDate(effectiveDate.toString());
        mapper.delete(newUploadEo);

        Example example = new Example(HrmsNewsalaryPayrollDetailUploadEo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("payrollDate", uploadEo.getPayrollDate());
        List<HrmsNewsalaryPayrollDetailUploadEo> list = mapper.selectByExample(example);

        for (HrmsNewsalaryPayrollDetailUploadEo detailUploadEo : list) {
            HrmsNewsalaryPayrollDetailUploadEo newEo = new HrmsNewsalaryPayrollDetailUploadEo();
            newEo.setItemId(detailUploadEo.getItemId());
            newEo.setItemName(detailUploadEo.getItemName());
            String salary = stringObjectMap.get(detailUploadEo.getItemId()).toString();
            newEo.setSalary(new BigDecimal(Strings.isEmpty(salary) ? "0" : salary));
            newEo.setEmployeeNo(employeeNo.toString());
            newEo.setId(IdUtil.getId());
            newEo.setCreateDate(new Date());
            newEo.setUpdateDate(new Date());
            newEo.setIsDeleted("N");
            newEo.setIsExamine(1);
            newEo.setPayrollDate(effectiveDate.toString());

            List<String> opionList = mapper.queryOptionId(employeeNo.toString(), detailUploadEo.getItemId());
            newEo.setOptionId(opionList.get(0));
            if (user != null) {
                newEo.setCreateUser(user.getUsercode());
                newEo.setCreateUserName(user.getUsername());
                newEo.setUpdateUser(user.getUsercode());
                newEo.setUpdateUserName(user.getUsername());
            }
            mapper.insertSelective(newEo);

        }
        return PlatformResult.success();
    }

    @Override
    public void exportUploadTemlateData(HttpServletResponse response, HrmsNewsalaryPayrollDetailUploadEo record) {
        try {
            String tempUrl = "template/dxsbTemplate.xlsx";
            // 创建Excel文档
            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath(tempUrl),
                    new Integer[0]);

            Workbook workbook = ExcelExportUtil.exportExcel(params, new HashMap());

            Sheet sheet1 = workbook.getSheet("薪资项上报模板");

            // 设置样式
            CellStyle textStyle = workbook.createCellStyle();
            // 四个边框
            textStyle.setBorderTop(BorderStyle.THIN);
            textStyle.setBorderBottom(BorderStyle.THIN);
            textStyle.setBorderLeft(BorderStyle.THIN);
            textStyle.setBorderRight(BorderStyle.THIN);
            // 上下左右居中
            textStyle.setAlignment(HorizontalAlignment.CENTER);
            textStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            textStyle.setDataFormat(workbook.getCreationHelper().createDataFormat().getFormat("text"));

            Row row0 = sheet1.getRow(0);
            Row row1 = sheet1.getRow(1);

            List<HrmsNewsalaryPayrollDetailUploadEo> uploadList = mapper.getItemList(record.getPayrollDate());
            if (CollectionUtils.isEmpty(uploadList)){
                uploadList = getAllList();
            }

            int index = 4;
            Cell cell = null;
            for (int i = 0; i < uploadList.size(); i++) {
                cell = row1.createCell(index);
                cell.setCellStyle(textStyle);
                cell.setCellValue(uploadList.get(i).getItemName());
                row0.createCell(index).setCellStyle(textStyle);
                index++;

            }

            for (int i = 0; i < sheet1.getNumMergedRegions(); i++) {
                CellRangeAddress mergedRegion = sheet1.getMergedRegion(i);
                if ("A1:D1".equals(mergedRegion.formatAsString())) {
                    // 如果找到A1:J1的合并区域，则移除它
                    sheet1.removeMergedRegion(i );
                }
            }

            // 把姓名工号带出来
            ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
            List<String> emploeeyList = mapper.getEmployeeList(thpsUser.getId());
            List<Map<String, String>> empList = null;
            if (CollectionUtils.isNotEmpty(emploeeyList)) {
                empList = mapper.selectEmpAll(emploeeyList, record);
            }
            Row _rowData;
            Cell _rowCell0;
            Cell _rowCell1;
            Cell _rowCell2;
            Cell _rowCell3;
            if (!empList.isEmpty()) {
                for (int i = 0; i < empList.size(); i++) {
                    _rowData = sheet1.createRow(i + 2);
                    _rowCell0 = _rowData.createCell(0);// 序号
                    _rowCell1 = _rowData.createCell(1);// 工号
                    _rowCell2 = _rowData.createCell(2);// 姓名
                    _rowCell3 = _rowData.createCell(3); //部门

                    _rowCell0.setCellStyle(textStyle);
                    _rowCell0.setCellValue(i + 1);

                    _rowCell1.setCellStyle(textStyle);
                    _rowCell1.setCellValue(empList.get(i).get("employee_no"));

                    _rowCell2.setCellStyle(textStyle);
                    _rowCell2.setCellValue(empList.get(i).get("employee_name"));
                    _rowCell3.setCellStyle(textStyle);
                    _rowCell3.setCellValue(empList.get(i).get("name"));
                }
            }

            sheet1.addMergedRegion(new CellRangeAddress(0, 0, 0, index - 1));
            String encodeName = URLEncoder.encode("薪资项上报模板.xlsx", StandardCharsets.UTF_8.toString());
            // 设置响应头信息
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=\"" + encodeName + "\"; filename*=utf-8''" + encodeName);

            // 将Excel文档写入响应流中
            ServletOutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            logger.error("导出数据异常" + e.getMessage());
            PlatformResult.failure("导出模版异常" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> importUploadTemplateData(MultipartFile file, String payrollDate) {
        try {
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            List<Map<String, String>> data = new ArrayList<>();
            for (Row row : sheet) { // 遍历每一行
                Map<String, String> rowData = new HashMap<>(); // 创建一个 Map 来存储每一行的值
                for (Cell cell : row) { // 遍历每一列
                    // 获取列名和值
                    String value = "";
                    String columnName = sheet.getRow(1).getCell(cell.getColumnIndex()).getStringCellValue(); // 获取第一列表头
//                    if (StringUtils.isAllEmpty(columnName)) {
//                        return PlatformResult.failure("导入excel出错: Excel模板中存在多余的列，请检查");
//                    }

                    value = cell.toString();
                    if (!StringUtil.isEmpty(columnName) && !StringUtil.isEmpty(value)) {
                        if ("工号".equals(columnName.trim())) {
                            rowData.put(columnName.trim(), value.replaceAll("\\.0+|\\.00", "").trim());
                        } else {
                            rowData.put(columnName.trim(), value.trim());
                        }
                    }
                }
                if (!StringUtil.isEmpty(rowData.get("工号"))) { // 工号不为空 就插入数据
                    data.add(rowData);
                }
            }
            Integer size = importEntryTemplateData(data, payrollDate);
            return PlatformResult.success("导入完成 " + size + " 条数据");
        } catch (Exception e) {
            return PlatformResult.failure("导入excel出错:" + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> updateUpload(Map<String, Object> stringObjectMap) {
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        Object employeeNo = stringObjectMap.get("employee_no");
        Object effectiveDate = stringObjectMap.get("effective_date");
        if (Objects.isNull(effectiveDate)) {
            return PlatformResult.failure("上报时间不为空");
        }
        Object id = stringObjectMap.get("id");
        //是否上报过
        Example example = new Example(HrmsNewsalaryPayrollDetailUploadEo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("employeeNo", employeeNo.toString());
        criteria.andEqualTo("payrollDate", effectiveDate.toString());
        criteria.andEqualTo("isExamine", 1);
        int i = mapper.selectCountByExample(example);
        if (i > 0) {
            return PlatformResult.failure("已存在上报审批不能修改！");
        }

        if (Objects.nonNull(id)) {
            HrmsNewsalaryPayrollDetailUploadEo newUploadEo = new HrmsNewsalaryPayrollDetailUploadEo();
            newUploadEo.setEmployeeNo(employeeNo.toString());
            newUploadEo.setPayrollDate(effectiveDate.toString());
            mapper.delete(newUploadEo);
        }
        //保存上报数据列表
        Example example2 = new Example(HrmsNewsalaryPayrollDetailUploadEo.class);
        Example.Criteria criteria2 = example2.createCriteria();
        criteria2.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria2.andEqualTo("payrollDate", effectiveDate);
        List<HrmsNewsalaryPayrollDetailUploadEo> uploadList = mapper.selectByExample(example2);
        if (CollectionUtils.isEmpty(uploadList)){
            uploadList = getAllList();
        }
        for (HrmsNewsalaryPayrollDetailUploadEo detailUploadEo : uploadList) {
            HrmsNewsalaryPayrollDetailUploadEo newEo = new HrmsNewsalaryPayrollDetailUploadEo();
            newEo.setItemId(detailUploadEo.getItemId());
            newEo.setItemName(detailUploadEo.getItemName());
            String salary = stringObjectMap.get(detailUploadEo.getItemId()).toString();
            newEo.setSalary(new BigDecimal(Strings.isEmpty(salary) ? "0" : salary));
            newEo.setEmployeeNo(employeeNo.toString());
            newEo.setId(IdUtil.getId());
            newEo.setCreateDate(new Date());
            newEo.setUpdateDate(new Date());
            newEo.setIsDeleted("N");
            newEo.setIsExamine(0);
            newEo.setPayrollDate(effectiveDate.toString());

            List<String> opionList = mapper.queryOptionId(employeeNo.toString(), detailUploadEo.getItemId());
            newEo.setOptionId(opionList.get(0));
            if (user != null) {
                newEo.setCreateUser(user.getUsercode());
                newEo.setCreateUserName(user.getUsername());
                newEo.setUpdateUser(user.getUsercode());
                newEo.setUpdateUserName(user.getUsername());
            }
            mapper.insertSelective(newEo);
        }
        return PlatformResult.success();
    }

    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> withdrawUpload(List<String> record) {
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        StringBuilder message = new StringBuilder();
        for (String id : record) {
            HrmsNewsalaryPayrollDetailUploadEo uploadEo = mapper.selectByPrimaryKey(id);
            if (Objects.isNull(uploadEo)) {
                message.append("未上报不能撤回!");
                continue;
            }
            if (Objects.equals(uploadEo.getIsExamine(), 2)) {
                Example example1 = new Example(HrmsEmployee.class);
                Example.Criteria criteria1 = example1.createCriteria();
                criteria1.andEqualTo(Contants.IS_DELETED_FIELD, "N");
                criteria1.andEqualTo("employeeNo", uploadEo.getEmployeeNo());
                HrmsEmployee employee = employeeMapper.selectOneByExample(example1);
                message.append(employee.getEmployeeName());
                message.append(",");
                continue;
            }

            Example example1 = new Example(HrmsNewsalaryPayrollDetailUploadEo.class);
            Example.Criteria criteria1 = example1.createCriteria();
            criteria1.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            criteria1.andEqualTo("employeeNo", uploadEo.getEmployeeNo());
            criteria1.andEqualTo("payrollDate", uploadEo.getPayrollDate());
            List<HrmsNewsalaryPayrollDetailUploadEo> uploadList = mapper.selectByExample(example1);
            uploadList.forEach(hrmsNewsalaryPayrollDetailUploadEo -> {
                hrmsNewsalaryPayrollDetailUploadEo.setUpdateDate(new Date());
                hrmsNewsalaryPayrollDetailUploadEo.setIsExamine(0);
                hrmsNewsalaryPayrollDetailUploadEo.setIsDeleted("N");
                if (user != null) {
                    hrmsNewsalaryPayrollDetailUploadEo.setUpdateUser(user.getUsercode());
                    hrmsNewsalaryPayrollDetailUploadEo.setUpdateUserName(user.getUsername());
                }
                mapper.updateByPrimaryKeySelective(hrmsNewsalaryPayrollDetailUploadEo);
            });

            //更新审核表状态
            Example recordExample = new Example(HrmsUploadRecordEo.class);
            Example.Criteria recordCriteria = recordExample.createCriteria();
            recordCriteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            recordCriteria.andEqualTo("employeeNo", uploadEo.getEmployeeNo());
            recordCriteria.andEqualTo("uploadDate", uploadEo.getPayrollDate());
            HrmsUploadRecordEo recordEo = recordMapper.selectOneByExample(recordExample);
            if (Objects.isNull(recordEo)) {
                continue;
            }
            recordEo.setApprovalStatus("0");
            recordEo.setUpdateDate(new Date());
            recordEo.setUpdateUser(user.getUsercode());
            recordMapper.updateByPrimaryKey(recordEo);
        }
        if (StringUtils.isNotEmpty(message)) {
            message.append("已审批不能撤回!");
            return PlatformResult.success(message.toString());
        }
        return PlatformResult.success();
    }

    @Override
    public PlatformResult<List<HrmsNewsalaryPayrollDetailUploadEo>> queryUploadInfo(String payrollDate, String optionId) {
        return PlatformResult.success(mapper.queryUploadInfo(payrollDate, optionId));
    }

    @Override
    public void uploadDataExport(HttpServletRequest request, HttpServletResponse response, HrmsNewsalaryPayrollDetailUploadEo record) {
        ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
        String usercode = thpsUser.getUsercode();
        List<HrmsNewsalaryPayrollDetailUploadEo> uploadList = mapper.getItemList(record.getPayrollDate());
        record.setUserCode(usercode);
        Page page = new Page();
        page.setPageSize(Integer.MAX_VALUE);
        List<Map<String, Object>> rows = mapper.listTableDataPage(page, record, uploadList, null);
        List<VueTableEntity> listTitle = salaryCountTitle(record.getPayrollDate());
        List<ExcelExportEntity> colList = new ArrayList<>();
        String filename = "薪资项上报数据";
        //时间格式转换
        if (CollUtil.isNotEmpty(rows)) {
            rows.forEach(item -> {
                String upDate = MapUtil.getStr(item, "update_date");
                if (StrUtil.isNotBlank(upDate)) {
                    LocalDateTime updateDate = LocalDateTime.parse(upDate);
                    String formatDate = updateDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    item.put("update_date", formatDate);
                }
            });
        }
        for (VueTableEntity vueTableEntity : listTitle) {
            colList.add(new ExcelExportEntity(vueTableEntity.getLabel(), vueTableEntity.getProp()));
        }
        try {
            Workbook workbook = ExcelExportUtil
                    .exportExcel(new ExportParams(filename, "薪资项上报数据", ExcelType.XSSF), colList, rows);
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename="
                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
            OutputStream fos = response.getOutputStream();
            workbook.write(fos);
            fos.close();
        } catch (Exception e) {
            logger.error("导出数据异常" + e.getMessage());
        }
    }

    private Integer importEntryTemplateData(List<Map<String, String>> data, String payrollDate) {
        Integer saveSize = 0;
        //先确定本次定调薪导入的项次有哪些内容，如果不在此批导入的项目中，则不做处理
        List<HrmsNewsalaryPayrollDetailUploadEo> list = mapper.getItemList(payrollDate);
        if (CollectionUtils.isEmpty(list)){
            list = getAllList();
        }
        Map<String, String> map = list.stream().collect(Collectors.toMap(HrmsNewsalaryPayrollDetailUploadEo::getItemName, HrmsNewsalaryPayrollDetailUploadEo::getItemId));
        for (int i = 1; i < data.size(); i++) { // 遍历每一个人
            // 遍历list
            String employeeNo = data.get(i).get("工号").trim();

            if (StringUtils.isEmpty(payrollDate)) {
                continue;
            }
            HrmsNewsalaryPayrollDetailUploadEo detailUpload = new HrmsNewsalaryPayrollDetailUploadEo();
            Example example = new Example(HrmsNewsalaryPayrollDetailUploadEo.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            criteria.andEqualTo("employeeNo", employeeNo);
            criteria.andEqualTo("payrollDate", payrollDate);
            criteria.andEqualTo("isExamine", 0);
            List<HrmsNewsalaryPayrollDetailUploadEo> uploadList = mapper.selectByExample(example);
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            //查询是否存在上报列表
            if (CollectionUtils.isNotEmpty(uploadList)) {
                uploadList.forEach(uploadEo -> {
                    uploadEo.setUpdateDate(new Date());
                    uploadEo.setIsDeleted("Y");
                    if (user != null) {
                        uploadEo.setUpdateUser(user.getUsercode());
                        uploadEo.setUpdateUserName(user.getUsername());
                    }
                    mapper.updateByPrimaryKeySelective(uploadEo);
                });
            }
            // 拿到人员的基本信息 根据工号
            for (HrmsNewsalaryPayrollDetailUploadEo detailUploadEo : list) {
                String salary = data.get(i).get(detailUploadEo.getItemName());
                detailUpload.setEmployeeNo(employeeNo);
                detailUpload.setItemId(map.get(detailUploadEo.getItemName()));
                detailUpload.setItemName(detailUploadEo.getItemName());
                if (StringUtils.isEmpty(salary) || Objects.isNull(salary)) {
                    detailUpload.setSalary(new BigDecimal(0));
                } else {
                    detailUpload.setSalary(new BigDecimal(salary));
                }
                detailUpload.setId(IdUtil.getId());
                detailUpload.setIsExamine(0);
                detailUpload.setCreateUserName(user.getUsername());
                detailUpload.setCreateDate(new Date());
                detailUpload.setUpdateUserName(user.getUsername());
                detailUpload.setUpdateDate(new Date());
                detailUpload.setPayrollDate(payrollDate);
                detailUpload.setIsDeleted("N");
                //查询是否有审核中的数据
                Example example1 = new Example(HrmsNewsalaryPayrollDetailUploadEo.class);
                Example.Criteria criteria1 = example1.createCriteria();
                criteria1.andEqualTo(Contants.IS_DELETED_FIELD, "N");
                criteria1.andEqualTo("employeeNo", employeeNo);
                criteria1.andEqualTo("payrollDate", payrollDate);
                criteria1.andEqualTo("isExamine", 1);
                int count = mapper.selectCountByExample(example1);
                if (0 == count) {
                    mapper.insertSelective(detailUpload);
                }

            }


        }

        saveSize = data.size() - 1;
        return saveSize;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsUploadRecordMapper">

    <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsUploadRecordEo">
            <id property="recordId" column="record_id" jdbcType="VARCHAR"/>
            <result property="employeeId" column="employee_id" jdbcType="VARCHAR"/>
            <result property="employeeNo" column="employee_no" jdbcType="VARCHAR"/>
            <result property="uploadDate" column="upload_date" jdbcType="VARCHAR"/>
            <result property="approvalStatus" column="approval_status" jdbcType="CHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="enterpriseId" column="enterprise_id" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="orgId" column="org_id" jdbcType="VARCHAR"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="is_deleted" jdbcType="CHAR"/>
            <result property="belongOrg" column="belong_org" jdbcType="VARCHAR"/>
            <result property="deptCheckDate" column="dept_check_date" jdbcType="TIMESTAMP"/>
            <result property="deptCheckUsername" column="dept_check_userName" jdbcType="VARCHAR"/>
            <result property="deptCheckUserid" column="dept_check_userId" jdbcType="VARCHAR"/>
            <result property="ssoOrgCode" column="sso_org_code" jdbcType="VARCHAR"/>
            <result property="ssoOrgName" column="sso_org_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        record_id,employee_id,employee_no,
        upload_date,approval_status,remark,
        enterprise_id,create_date,create_user,
        create_user_name,update_date,update_user,
        update_user_name,org_id,org_name,
        is_deleted,belong_org,dept_check_date,
        dept_check_userName,dept_check_userId,sso_org_code,
        sso_org_name
    </sql>
</mapper>

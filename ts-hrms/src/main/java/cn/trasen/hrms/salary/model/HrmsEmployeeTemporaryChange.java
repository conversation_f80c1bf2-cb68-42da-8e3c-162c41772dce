package cn.trasen.hrms.salary.model;

import cn.trasen.hrms.salary.enums.EmployeeTemporaryOpTypeEnum;
import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 临时员工操作记录表
 *
 */
@Table(name = "hrms_employee_temporary_change")
@Setter
@Getter
public class HrmsEmployeeTemporaryChange {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 员工id
     */
    @Column(name = "tmp_employee_id")
    @ApiModelProperty(value = "员工id")
    private String tmpEmployeeId;

    /**
     * 员工工号
     */
    @Column(name = "tmp_employee_no")
    @ApiModelProperty(value = "员工工号")
    private String tmpEmployeeNo;

    /**
     * 员工名称
     */
    @Column(name = "tmp_employee_name")
    @ApiModelProperty(value = "员工名称")
    private String tmpEmployeeName;

    /**
     * 操作类型:1-新增 2-编辑 3-同步 4-转档 5-离职 6-批量入职 7-批量离职 8-批量调薪 9-导出
     */
    @Column(name = "op_type")
    @ApiModelProperty(value = "操作类型:1-入职 2-编辑 3-同步 4-转档 5-离职 6-批量入职 7-批量离职 8-批量调薪 9-导出")
    private String opType;

    @Transient
    @ApiModelProperty(value = "操作功能名称")
    private String opTypeName;

    /**
     * 操作前数据
     */
    @Column(name = "op_before_value")
    @ApiModelProperty(value = "操作前数据")
    private String opBeforeValue;

    /**
     * 操作后数据
     */
    @Column(name = "op_after_value")
    @ApiModelProperty(value = "操作后数据")
    private String opAfterValue;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    @Transient
    @ApiModelProperty(value = "查询开始时间")
    private String searchStartTime;

    @Transient
    @ApiModelProperty(value = "查询结束时间")
    private String searchEndTime;

    public String getOpTypeName(){
        return EmployeeTemporaryOpTypeEnum.getValByKey(this.getOpType());
    }
}
package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.enums.EmployeeTemporaryOpTypeEnum;
import cn.trasen.hrms.salary.model.HrmsEmployeeTemporary;

import java.util.List;
import java.util.Map;

/**
 * @ClassName HrmsEmployeeTemporaryService
 * @Description TODO
 * @date 2024��10��8�� ����3:11:08
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsEmployeeTemporaryService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	Integer save(HrmsEmployeeTemporary record,EmployeeTemporaryOpTypeEnum opTypeEnum);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	Integer update(HrmsEmployeeTemporary record,EmployeeTemporaryOpTypeEnum opTypeEnum);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsEmployeeTemporary
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	HrmsEmployeeTemporary selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsEmployeeTemporary>
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	List<HrmsEmployeeTemporary> getDataSetList(Page page, HrmsEmployeeTemporary record);

	/**
	 *
	 * @Title sync2Salary
	 * @Description 一键同步至薪酬方案
	 * @return Integer
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	Integer sync2Salary();

	/**
	 *
	 * @Title importEmployeeTemporary
	 * @Description 批量入职
	 * @return Map
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	Map<String,Integer> importEmployeeTemporary(List<HrmsEmployeeTemporary> tmpEmpList);

	/**
	 *
	 * @Title batchAdjustSalary
	 * @Description 批量调薪
	 * @return Map
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	Map<String,Integer> batchAdjustSalary(List<HrmsEmployeeTemporary> tmpEmpList);


	/**
	 *
	 * @Title batchdimission
	 * @Description 批量离职
	 * @return Map
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	Integer batchdimission(List<String> ids,String dimissionDate,String effectiveDate,String reason);
}

package cn.trasen.hrms.salary.DTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 薪酬方案人员关联表
 *
 * <AUTHOR>
 * @TableName hrms_newsalary_option_total_type
 */
@Setter
@Getter
public class HrmsNewsalaryReportMapVo {

    /**
     *
     */
    @ApiModelProperty("id")
    private String id;
    /**
     * 薪酬项目id
     */
    @ApiModelProperty("薪酬项目id")
    private String itemId;

    /**
     * 薪酬项目名称
     */
    @ApiModelProperty("薪酬项目名称")
    private String itemName;
    /**
     * 方案id
     */
    @ApiModelProperty("方案id")
    private String optionId;

    /**
     * 薪酬项目名称
     */
    @ApiModelProperty("方案名称")
    private String optionName;

    @ApiModelProperty("汇总报表字段ID")
    private String colId;

    @ApiModelProperty("汇总报表字段编码")
    private String colCode;

    @ApiModelProperty("汇总报表字段名称")
    private String colName;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createDate;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createUser;
    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    private String createUserName;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateDate;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;
    /**
     * 修改人名称
     */
    @ApiModelProperty("修改人名称")
    private String updateUserName;
    /**
     * 删除标识
     */
    @ApiModelProperty("删除标识")
    private String isDeleted;
    /**
     * 机构编码
     */
    @ApiModelProperty("机构编码")
    private String ssoOrgCode;
    /**
     * 机构名称
     */
    @ApiModelProperty("机构名称")
    private String ssoOrgName;

    /**
     * 计算规则 1-关联薪酬项目 2-自定义公式
     */
    @ApiModelProperty(value = "计算规则 1-关联薪酬项目 2-自定义公式")
    private String itemRule;

    /**
     * 计算公式
     */
    @ApiModelProperty(value = "计算公式")
    private String countFormula;

    /**
     * 计算公式文本
     */
    @ApiModelProperty(value = "计算公式文本")
    private String countFormulaText;
}

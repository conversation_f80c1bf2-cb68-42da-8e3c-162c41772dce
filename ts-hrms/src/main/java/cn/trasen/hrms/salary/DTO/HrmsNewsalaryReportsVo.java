package cn.trasen.hrms.salary.DTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import java.util.Date;

/**
* 薪酬汇总返回对象
* @TableName hrms_newsalary_reports
*/
@Setter
@Getter
public class HrmsNewsalaryReportsVo {

    /**
    * ID
    */
    @ApiModelProperty("ID")
    @Id
    private String id;
    /**
    * 汇总报表id
    */
    @ApiModelProperty("汇总报表名称")
    private String reportName;
    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
    /**
    * 创建人ID
    */
    @ApiModelProperty("创建人ID")
    private String createUser;
    /**
    * 创建人姓名
    */
    @ApiModelProperty("创建人姓名")
    private String createUserName;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private Date createDate;
    /**
    * 更新人ID
    */
    @ApiModelProperty("更新人ID")
    private String updateUser;
    /**
    * 更新人姓名
    */
    @ApiModelProperty("更新人姓名")
    private String updateUserName;
    /**
    * 更新时间
    */
    @ApiModelProperty("更新时间")
    private Date updateDate;
    /**
    * 是否删除 N 正常   Y 删除
    */
    @ApiModelProperty("是否删除 N 正常   Y 删除")
    private String isDeleted;
    /**
    * 
    */
    @ApiModelProperty("")
    private String ssoOrgCode;
    /**
    * 
    */
    @ApiModelProperty("")
    private String ssoOrgName;

    /**
     * 报表类型 1-薪酬汇总 2-薪酬方案类 3-保险缴费类
     */
    @Column(name = "reports_type")
    @ApiModelProperty(value = "报表类型 1-薪酬汇总 2-薪酬方案类 3-保险缴费类")
    private String reportsType;

}

//package cn.trasen.hrms.salary.multi;
//
//
//import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
//import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;
//import cn.trasen.hrms.salary.model.HrmsNewsalaryPayroll;
//import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetail;
//import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionPayrollService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.math.BigDecimal;
//import java.util.List;
//import java.util.Map;
//
///**
// * 线程生成工资
// */
//@Slf4j
//@Component
//public class MultiThreadGrant implements Runnable {
//
//    private List<HrmsNewsalaryOptionEmp> empList;
//    private String computeDate;
//    private String optionId;
//    private List<HrmsNewsalaryPayroll> payrollRecords;
//    private List<HrmsNewsalaryPayrollDetail> payrollDetails;
//    private Map<String, Map<String, BigDecimal>> empBasicSalaryMap;
//    private List<HrmsNewsalaryItem> itemList;
//    private Map<String, Map<String, Object>> handWorkSalary;
//    private HrmsNewsalaryOptionPayrollService hrmsNewsalaryOptionPayrollService;
//    private String hnopId;
//
//    public MultiThreadGrant(HrmsNewsalaryOptionPayrollService hrmsNewsalaryOptionPayrollService, List<HrmsNewsalaryOptionEmp> empList, String computeDate, String optionId,
//                            List<HrmsNewsalaryPayroll> payrollRecords, List<HrmsNewsalaryPayrollDetail> payrollDetails,
//                            Map<String, Map<String, BigDecimal>> empBasicSalaryMap, List<HrmsNewsalaryItem> itemList,
//                            Map<String, Map<String, Object>> handWorkSalary,String hnopId) {
//        this.hrmsNewsalaryOptionPayrollService = hrmsNewsalaryOptionPayrollService;
//        this.empList = empList;
//        this.computeDate = computeDate;
//        this.optionId = optionId;
//        this.payrollRecords = payrollRecords;
//        this.payrollDetails = payrollDetails;
//        this.empBasicSalaryMap = empBasicSalaryMap;
//        this.itemList = itemList;
//        this.handWorkSalary = handWorkSalary;
//        this.hnopId = hnopId;
//    }
//    public MultiThreadGrant() {
//
//    }
//
//    public MultiThreadGrant(HrmsNewsalaryOptionPayrollService hrmsNewsalaryOptionPayrollService,
//        List<HrmsNewsalaryOptionEmp> empList, String computeDate, String optionId,
//        List<HrmsNewsalaryPayroll> payrollRecords, Map<String, Map<String, BigDecimal>> empBasicSalaryMap,
//        List<HrmsNewsalaryItem> itemList, Map<String, Map<String, Object>> handWorkSalary,String hnopId) {
//    }
//
//    @Override
//    public void run() {
//        hrmsNewsalaryOptionPayrollService.executeThread(empList,  computeDate,  optionId,
//                payrollRecords,payrollDetails, empBasicSalaryMap,
//               itemList, handWorkSalary,hnopId);
//    }
//}

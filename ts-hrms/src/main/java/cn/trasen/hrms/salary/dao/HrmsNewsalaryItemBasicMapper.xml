<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryItemBasicMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryItemBasic">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="item_type" jdbcType="VARCHAR" property="itemType" />
    <result column="item_digit" jdbcType="CHAR" property="itemDigit" />
    <result column="count_type" jdbcType="CHAR" property="countType" />
    <result column="item_rule" jdbcType="CHAR" property="itemRule" />
    <result column="count_formula" jdbcType="VARCHAR" property="countFormula" />
    <result column="count_formula_text" jdbcType="VARCHAR" property="countFormulaText" />
    <result column="salary_item_amount" jdbcType="DECIMAL" property="salaryItemAmount" />
    <result column="salary_remark" jdbcType="VARCHAR" property="salaryRemark" />
    <result column="is_hidden" jdbcType="VARCHAR" property="isHidden" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>
</mapper>
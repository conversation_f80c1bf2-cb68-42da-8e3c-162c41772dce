package cn.trasen.hrms.salary.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.DTO.UpdateSalaryVo;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetail;

/**
 * @ClassName HrmsNewsalaryPayrollDetailService
 * @Description TODO
 * @date 2023��11��11�� ����4:38:30
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryPayrollDetailService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:38:30
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryPayrollDetail record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:38:30
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryPayrollDetail record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��11��11�� ����4:38:30
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryPayrollDetail
	 * @date 2023��11��11�� ����4:38:30
	 * <AUTHOR>
	 */
	HrmsNewsalaryPayrollDetail selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryPayrollDetail>
	 * @date 2023��11��11�� ����4:38:30
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryPayrollDetail> getDataSetList(Page page, HrmsNewsalaryPayrollDetail record);

	//批量保存
    Integer batchInsert(List<HrmsNewsalaryPayrollDetail> payrollDetails);

	Integer deleteByExample(String optionId, String computeDate);

    List<UpdateSalaryVo> getSalaryChangesData(HrmsNewsalaryOptionPayroll record);

    Integer deleteByOptionId(String id);

    List<HrmsNewsalaryItem> getCalculateWagesHistoryTitle(HrmsNewsalaryOptionPayroll record);

	List<HrmsNewsalaryItem> getsalaryConfirmHistoryTitle(HrmsNewsalaryOptionPayroll record);

}

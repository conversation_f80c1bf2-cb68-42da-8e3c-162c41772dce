package cn.trasen.hrms.salary.controller;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOption;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionEmpService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName HrmsNewsalaryOptionController
 * @Description TODO
 * @date 2024��2��6�� ����10:03:32
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "薪酬方案Controller")
public class HrmsNewsalaryOptionController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryOptionController.class);

	@Autowired
	private HrmsNewsalaryOptionService hrmsNewsalaryOptionService;

	@Autowired
	private HrmsNewsalaryOptionEmpService hrmsNewsalaryOptionEmpService;

	/**
	 * @Title saveHrmsNewsalaryOption
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��2��6�� ����10:03:32
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/newSalaryOption/save")
	public PlatformResult<String> saveHrmsNewsalaryOption(@RequestBody HrmsNewsalaryOption record) {
		try {
			String id = hrmsNewsalaryOptionService.save(record);
			return PlatformResult.success(id);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryOption
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��2��6�� ����10:03:32
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/newSalaryOption/update")
	public PlatformResult<String> updateHrmsNewsalaryOption(@RequestBody HrmsNewsalaryOption record) {
		try {
			hrmsNewsalaryOptionService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "启用停用", notes = "启用停用")
	@PostMapping("/api/newSalaryOption/enable")
	public PlatformResult<String> status(@RequestBody HrmsNewsalaryOption record) {
		try {
			hrmsNewsalaryOptionService.enable(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "薪酬方案人员排序", notes = "薪酬方案人员排序")
	@PostMapping("/api/newSalaryOption/optionEmpSort")
	public PlatformResult<String> optionEmpSort(@RequestBody HrmsNewsalaryOptionEmp record) {
		try {
			hrmsNewsalaryOptionEmpService.optionEmpSort(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryOptionById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryOption>
	 * @date 2024��2��6�� ����10:03:32
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/newSalaryOption/{id}")
	public PlatformResult<HrmsNewsalaryOption> selectHrmsNewsalaryOptionById(@PathVariable String id) {
		try {
//			HrmsNewsalaryOption record = hrmsNewsalaryOptionService.selectById(id);
			HrmsNewsalaryOption record = hrmsNewsalaryOptionService.selectByOptionId(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title deleteHrmsNewsalaryOptionById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��2��6�� ����10:03:32
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/newSalaryOption/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryOptionById(@PathVariable String id) {
		try {
			hrmsNewsalaryOptionService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryOptionList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryOption>
	 * @date 2024��2��6�� ����10:03:32
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/newSalaryOption/list")
	public DataSet<HrmsNewsalaryOption> selectHrmsNewsalaryOptionList(Page page, HrmsNewsalaryOption record) {
		return hrmsNewsalaryOptionService.getDataSetList(page, record);
	}

	@ApiOperation(value = "所有方案", notes = "所有方案")
	@GetMapping("/api/newSalaryOption/allList")
	public PlatformResult<List<HrmsNewsalaryOption>> allList(Page page, HrmsNewsalaryOption record) {
		try {
			page.setPageSize(Integer.MAX_VALUE);
			DataSet<HrmsNewsalaryOption> dataSetList = hrmsNewsalaryOptionService.getDataSetList(page, record);
			List<HrmsNewsalaryOption> rows = dataSetList.getRows();
			return PlatformResult.success(rows);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "获取方案编号", notes = "获取方案编号")
	@GetMapping("/api/newSalaryOption/getOptionCode")
	public PlatformResult<String> getOptionCode(){
		String optionCode = hrmsNewsalaryOptionService.getOptionCode();
		return PlatformResult.success(optionCode);
	}

	@ApiOperation(value = "获取未关联工资条薪酬方案", notes = "获取未关联工资条薪酬方案")
	@GetMapping("/api/newSalaryOption/getPayslipOption")
	public DataSet<HrmsNewsalaryOption> getPayslipOption(HrmsNewsalaryOption record,Page page){
		return hrmsNewsalaryOptionService.getPaySlipOption(record,page);
	}

	@ApiOperation(value = "批量启用", notes = "批量启用")
	@PostMapping("/api/newSalaryOption/batchEnable")
	public PlatformResult batchEnable(@RequestBody List<String> ids) {
		try {
			if(CollUtil.isEmpty(ids)){
				return PlatformResult.failure("ids不能为空!");
			}
			hrmsNewsalaryOptionService.batchEnable(ids);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "批量停用", notes = "批量停用")
	@PostMapping("/api/newSalaryOption/batchdisEnable")
	public PlatformResult batchdisEnable(@RequestBody List<String> ids) {
		try {
			if(CollUtil.isEmpty(ids)){
				return PlatformResult.failure("ids不能为空!");
			}
			hrmsNewsalaryOptionService.batchdisEnable(ids);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 *
	 * @Title selectOptionByItemId
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryOption>
	 * @date 2024 0725
	 * <AUTHOR>
	 */
	@ApiOperation(value = "根据方案查项目", notes = "详情")
	@GetMapping("/api/newSalaryOptionItem/{id}")
	public PlatformResult<List<HrmsNewsalaryItem>> selectOptionByItemId(@PathVariable String id) {
		try {
			List<HrmsNewsalaryItem> record = hrmsNewsalaryOptionService.selectOptionByItemId(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}

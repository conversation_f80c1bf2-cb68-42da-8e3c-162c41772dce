package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemLibrary;
import java.util.List;

public interface IHrmsNewSalaryItemLibraryService {

    /**
     * 薪酬项目库列表
     * @param page
     * @param record
     * @return
     */
    DataSet<HrmsNewsalaryItemLibrary> getDataList(Page page, HrmsNewsalaryItemLibrary record);

    /**
     * 编辑
     * @param record
     */
    void update(HrmsNewsalaryItemLibrary record);

    /**
     * 启用
     * @param ids
     */
    void enalbe(List<String> ids);

    /**
     * 禁用
     * @param ids
     */
    void disEnalbe(List<String> ids);

    /**
     * 删除
     * @param record
     */
    void delete(HrmsNewsalaryItemLibrary record);

    /**
     * 引用薪酬项目
     * @param record
     */
    void importItem(List<HrmsNewsalaryItemLibrary> record);

    /**
     * 自定义薪酬项目
     * @param record
     */
    void save(HrmsNewsalaryItemLibrary record);

    /**
     * 查看详情
     * @param id
     */
    HrmsNewsalaryItemLibrary getById(String id);

    /**
     * 查询启用项目库列表
     * @return
     */
    List<HrmsNewsalaryItemLibrary> getEnableData();

    /**
     * 自定义公式薪酬表
     * @return
     */
    List<HrmsNewsalaryItemLibrary> getCustomItem();

    /**
     * 薪酬项目库启用列表
     * @param page
     * @param record
     * @return
     */
    DataSet<HrmsNewsalaryItemLibrary> getEnableDataList(Page page, HrmsNewsalaryItemLibrary record);

    /**
     * 获取项目编码
     * @return
     */
    String getLibraryCode(String itemSource);
}

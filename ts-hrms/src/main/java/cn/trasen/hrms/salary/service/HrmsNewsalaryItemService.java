package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.DTO.SalaryCountSearchReq;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemBasic;
import cn.trasen.hrms.salary.utils.VueTableEntity;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName HrmsNewsalaryItemService
 * @Description TODO
 * @date 2023��11��11�� ����4:34:26
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryItemService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryItem record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryItem record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	Integer deleteById(String uid,String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryItem
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	HrmsNewsalaryItem selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryItem>
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryItem> getDataSetList(Page page, HrmsNewsalaryItem record);

	/**
	 * @Title getDataSetList
	 * @Description 获取所有不重复的方案薪酬项列表
	 * @return DataSet<HrmsNewsalaryItem>
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	List<HrmsNewsalaryItem> getAllOptionSalaryItem(String ssoOrgCode);

	List<HrmsNewsalaryItemBasic> getDataItemBasicList(Page page, HrmsNewsalaryItemBasic record);

	//根据分组查询薪酬项
	List<HrmsNewsalaryItem> getDataByGroupId(String id);

	List<HrmsNewsalaryItem> getByOption(String optionId);

	Integer paySheet(List<HrmsNewsalaryItem> record);

	Integer sortNo(List<HrmsNewsalaryItem> record);

	/**
	 * 更具薪酬组id查询项目
	 * @param optionId
	 * @return
	 */
	List<HrmsNewsalaryItem> getItemByOptionId(String optionId);
	List<HrmsNewsalaryItem> getItemByOptionIdAll(String optionId);
	//根据人员查工资条（月）
	DataSet<HrmsNewsalaryItem> getPayrollByEmployee(Page page, HrmsNewsalaryItem record);

	Map<String, Object> getPayrollByEmployeeDetails(HrmsNewsalaryItem record);

	//根据方案iD和计算方式查询数据
	List<HrmsNewsalaryItem> getItemByOptionAndrule(String optionId, String c);

	List<VueTableEntity> salaryCountTitle(String optionId);

	List<HrmsNewsalaryItem> getListItemCode(String optionId);

    List<LinkedHashMap<String, String>> getExportColumns(String optionId);

	//判断薪酬档案项目是否被使用
	boolean checkUse(String id);

	//导出自定义列表头
	List<VueTableEntity> salaryCountTitleByDefinition(SalaryCountSearchReq record);

	List<HrmsNewsalaryItem> getALLByMap();

	Integer deleteByGroupId(String groupId);

	void updateArticle(HrmsNewsalaryItem record);

	/**
	 * 判断薪酬方案是否存在手工录入工资项
	 * @param optionId
	 * @return
	 */
	boolean checkManual(String optionId);

	/**
	 * 启用/禁用
	 * @param uid
	 * @param isEable
	 */
    void enable(String uid, String isEable);

	/**
	 * 判断是否引用
	 * @param id
	 * @return
	 */
	boolean checkQuote(String id);

	/**
	 * 根据薪酬方案id查询手工录入项目
	 * @param optionId
	 * @return
	 */
	List<HrmsNewsalaryItem> getManualByOptionId(String optionId);

	/**
	 * 根据方案id删除关联项目
	 * @param optionId
	 */
	void deleteByOptionId(String optionId);

	List<VueTableEntity> salaryTotalTitle(String reportId);

	List<LinkedHashMap<String, String>> getTotalExportColumns(String reportId);

	//导出自定义列表头
	List<VueTableEntity> salaryTotalCountTitleByDefinition(SalaryCountSearchReq record);

}

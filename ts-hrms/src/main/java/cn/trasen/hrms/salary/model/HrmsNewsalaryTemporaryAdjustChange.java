package cn.trasen.hrms.salary.model;

import cn.trasen.hrms.salary.enums.EmployeeTemporaryOpTypeEnum;
import cn.trasen.hrms.salary.enums.NewsalaryTemporaryAdjustOpTypeEnum;
import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 
 * 薪资调整表操作记录表
 *
 */
@Table(name = "hrms_newsalary_temporary_adjust_change")
@Setter
@Getter
public class HrmsNewsalaryTemporaryAdjustChange {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 员工id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    /**
     * 员工工号
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 员工姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    /**
     * 薪酬调整id
     */
    @Column(name = "adjust_id")
    @ApiModelProperty(value = "薪酬调整id")
    private String adjustId;

    /**
     * 操作类型:1-新增 2-编辑 3-删除 4-导入 5-导出 6-核算
     */
    @Column(name = "op_type")
    @ApiModelProperty(value = "操作类型:1-新增 2-编辑 3-删除 4-导入 5-导出 6-核算")
    private String opType;

    /**
     * 操作前数据
     */
    @Column(name = "op_before_value")
    @ApiModelProperty(value = "操作前数据")
    private String opBeforeValue;

    /**
     * 操作后数据
     */
    @Column(name = "op_after_value")
    @ApiModelProperty(value = "操作后数据")
    private String opAfterValue;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    @Transient
    @ApiModelProperty(value = "查询开始时间")
    private String searchStartTime;

    @Transient
    @ApiModelProperty(value = "查询结束时间")
    private String searchEndTime;

    public String getOpTypeName(){
        return NewsalaryTemporaryAdjustOpTypeEnum.getValByKey(this.getOpType());
    }
}
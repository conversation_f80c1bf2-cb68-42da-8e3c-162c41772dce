package cn.trasen.hrms.salary.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 工资记录表
 *
 */
@Table(name = "hrms_newsalary_payroll")
@Setter
@Getter
public class HrmsNewsalaryPayroll {



    /**
     * 薪酬发放id
     */
    @Id
    @ApiModelProperty(value = "薪酬发放id")
    private String id;

    /**
     * 员工id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    /**
     * 方案id
     */
    @Column(name = "option_id")
    @ApiModelProperty(value = "方案id")
    private String optionId;


    /**
     * 方案id
     */
    @Column(name = "option_payroll_id")
    @ApiModelProperty(value = "发放记录id")
    private String optionPayrollId;

    /**
     * 发放月份
     */
    @Column(name = "payroll_date")
    @ApiModelProperty(value = "发放月份")
    private String payrollDate;

    /**
     * 工资条发送状态
     */
    @Column(name = "send_status")
    @ApiModelProperty(value = "工资条发送状态 1 已发送")
    private String sendStatus;

    /**
     * 发送时间
     */
    @Column(name = "send_time")
    @ApiModelProperty(value = "发送时间")
    private String sendTime;

    @Column(name = "revocation_time")
    @ApiModelProperty(value = "撤回时间")
    private String revocationTime;

    /**
     * 发送方式
     */
    @Column(name = "send_method")
    @ApiModelProperty(value = "发送方式")
    private String sendMethod;

    /**
     * 1 已查看
     */
    @Column(name = "is_view")
    @ApiModelProperty(value = "1 已查看")
    private String isView;

    @Column(name = "sort_num")
    @ApiModelProperty(value = "人员排序号")
    private Integer sortNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 员工状态
     */
    @Column(name = "employee_status")
    @ApiModelProperty(value = "员工状态")
    private String employeeStatus;

    /**
     * 编制类型
     */
    @Column(name = "establishment_type")
    @ApiModelProperty(value = "编制类型")
    private String establishmentType;

    /**
     * 政策标准id
     */
    @Column(name = "policy_standard_id")
    @ApiModelProperty(value = "政策标准id")
    private String policyStandardId;

    @Transient
    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @Transient
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    @Transient
    @ApiModelProperty(value = "科室名称")
    private String orgName;

    @Transient
    @ApiModelProperty(value = "岗位")
    private String personalIdentity;

    @Transient
    @ApiModelProperty(value = "岗位中文")
    private String personalIdentityText;

    @Transient
    @ApiModelProperty(value = "编制类型中文")
    private String establishmentTypeText;

    @Transient
    @ApiModelProperty(value = "员工状态中文")
    private String employeeStatusText;

    @Transient
    @ApiModelProperty(value = "发送状态中文")
    private String sendStatusText;
    @Transient
    @ApiModelProperty(value = "查看状态中文")
    private String isViewText;

    @Transient
    @ApiModelProperty(value = "查询条件状态")
    private String searchStatus;

    @Transient
    private String ids;
    
    @Transient
    private List<String> idList;
    
    //导出序号
    @Transient
    private Integer no;


    public HrmsNewsalaryPayroll(String id,String employeeId ,String optionId){
        this.id = id;
        this.employeeId = employeeId;
        this.optionId = optionId;
        this.setCreateDate(new Date());
        this.setUpdateDate(new Date());
        this.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            this.setCreateUser(user.getUsercode());
            this.setCreateUserName(user.getUsername());
            this.setUpdateUser(user.getUsercode());
            this.setUpdateUserName(user.getUsername());
        }
    }

    public HrmsNewsalaryPayroll(){
    }
}
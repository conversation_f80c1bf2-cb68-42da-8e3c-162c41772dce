package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemOption;

/**
 * @ClassName HrmsNewsalaryItemOptionService
 * @Description TODO
 * @date 2023��11��11�� ����4:35:42
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryItemOptionService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:35:42
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryItemOption record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:35:42
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryItemOption record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��11��11�� ����4:35:42
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryItemOption
	 * @date 2023��11��11�� ����4:35:42
	 * <AUTHOR>
	 */
	HrmsNewsalaryItemOption selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryItemOption>
	 * @date 2023��11��11�� ����4:35:42
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryItemOption> getDataSetList(Page page, HrmsNewsalaryItemOption record);
}

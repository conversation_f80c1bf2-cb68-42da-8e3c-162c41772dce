package cn.trasen.hrms.salary.DTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class HrmsNewsalaryPayrollVo {
	
	private String slipName;  //工资单名称
	
	private String optionId; //薪酬组id
	
	private String payrollDate; //发放日期
	
	private Long payrollNumber; //发放人数
	
	private Long unsendNumber; //未发送人数
	
	private Long sendNumber; //已发送人数
	
	private Long unviewNumber;  //未查看人数
	 
	private Long viewNumber;  //已查看人数

	@ApiModelProperty("开始时间")
	private String startDate;

	@ApiModelProperty("结束时间")
	private String endDate;
}

package cn.trasen.hrms.salary.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.ExcelExportOfTemplateUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.trasen.hrms.salary.model.HrmsNewsalaryItemRemindRecord;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemRemindRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName HrmsNewsalaryItemRemindRecordController
 * @Description TODO
 * @date 2024��10��29�� ����6:29:18
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
@Api(tags = "hrmsNewsalaryItemRemindRecordController")
public class HrmsNewsalaryItemRemindRecordController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryItemRemindRecordController.class);

	@Autowired
	private HrmsNewsalaryItemRemindRecordService hrmsNewsalaryItemRemindRecordService;

	/**
	 * @Title saveHrmsNewsalaryItemRemindRecord
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/hrmsNewsalaryItemRemindRecord/save")
	public PlatformResult<String> saveHrmsNewsalaryItemRemindRecord(@RequestBody HrmsNewsalaryItemRemindRecord record) {
		try {
			hrmsNewsalaryItemRemindRecordService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryItemRemindRecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/hrmsNewsalaryItemRemindRecord/update")
	public PlatformResult<String> updateHrmsNewsalaryItemRemindRecord(@RequestBody HrmsNewsalaryItemRemindRecord record) {
		try {
			hrmsNewsalaryItemRemindRecordService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryItemRemindRecordById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryItemRemindRecord>
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/hrmsNewsalaryItemRemindRecord/{id}")
	public PlatformResult<HrmsNewsalaryItemRemindRecord> selectHrmsNewsalaryItemRemindRecordById(@PathVariable String id) {
		try {
			HrmsNewsalaryItemRemindRecord record = hrmsNewsalaryItemRemindRecordService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryItemRemindRecordById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/hrmsNewsalaryItemRemindRecord/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryItemRemindRecordById(@PathVariable String id) {
		try {
			hrmsNewsalaryItemRemindRecordService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 *
	 * @Title deleteHrmsNewsalaryItemRemindRecordById
	 * @Description 根据ID删除
	 * @param jsonObject
	 * @return PlatformResult<String>
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "批量不调整", notes = "批量不调整")
	@PostMapping("/api/hrmsNewsalaryItemRemindRecord/batchNotAdjust")
	public PlatformResult<String> batchNotAdjust(@RequestBody JSONObject jsonObject) {
		String ids = jsonObject.getString("ids");
		if(StringUtils.isBlank(ids)){
			return PlatformResult.failure("批量不处理的记录不能为空");
		}
		try {
			hrmsNewsalaryItemRemindRecordService.batchNotAdjust(ids);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryItemRemindRecordList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryItemRemindRecord>
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/hrmsNewsalaryItemRemindRecord/list")
	public DataSet<HrmsNewsalaryItemRemindRecord> selectHrmsNewsalaryItemRemindRecordList(Page page, HrmsNewsalaryItemRemindRecord record) {
		return hrmsNewsalaryItemRemindRecordService.getDataSetList(page, record);
	}

    /**
     * @Title selectHrmsNewsalaryItemRemindRecordList
     * @Description 查询列表
     * @param page
     * @param record
     * @return DataSet<HrmsNewsalaryItemRemindRecord>
     * @date 2024��10��29�� ����6:29:18
     * <AUTHOR>
     */
    @ApiOperation(value = "汇总统计员工核算月份薪资异动", notes = "汇总统计员工核算月份薪资异动")
    @GetMapping("/api/hrmsNewsalaryItemRemindRecord/getEmployeeSalaryRemindStatistics")
    public DataSet<HrmsNewsalaryItemRemindRecord> getEmployeeSalaryRemindStatistics(Page page, HrmsNewsalaryItemRemindRecord record) {
        return hrmsNewsalaryItemRemindRecordService.getEmployeeSalaryRemindStatistics(page, record);
    }

	/**
	 * @Title updateHrmsNewsalaryItemRemindRecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "提醒处理", notes = "提醒处理")
	@PostMapping("/api/hrmsNewsalaryItemRemindRecord/remindProcessing")
	public PlatformResult<String> remindProcessing(@RequestBody HrmsNewsalaryItemRemindRecord record) {
		try {
			hrmsNewsalaryItemRemindRecordService.remindProcessing(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryItemRemindRecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "提醒批量处理", notes = "提醒批量处理")
	@PostMapping("/api/hrmsNewsalaryItemRemindRecord/remindBatchProcessing")
	public PlatformResult<String> remindBatchProcessing(@RequestBody HrmsNewsalaryItemRemindRecord record) {
		try {
			if(CollectionUtil.isEmpty(record.getIds())){
				return PlatformResult.failure("批量处理的异动数据不能为空");
			}else if(StringUtils.isBlank(record.getTmpItem())){
				return PlatformResult.failure("批量处理的项目类型不能为空");
			}
			hrmsNewsalaryItemRemindRecordService.remindBatchProcessing(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping(value = "/api/hrmsNewsalaryItemRemindRecord/downLoad")
	public void downLoad(Page page, HrmsNewsalaryItemRemindRecord entity,HttpServletRequest request,
						 HttpServletResponse response) {
		page.setPageNo(1);
		page.setPageSize(Integer.MAX_VALUE);
		try {
			TemplateExportParams params = new TemplateExportParams("template/newsalaryremind/item_export.xls");
			String name = "薪资异动办理提醒记录.xls";
			String _val = entity.getComputeDate();
			if(!StringUtil.isEmpty(_val)) {
				name = _val + " 薪资异动办理提醒记录.xls";
			}
			name = new String(name.getBytes("UTF-8"), "ISO8859-1");


			List<HrmsNewsalaryItemRemindRecord> list = hrmsNewsalaryItemRemindRecordService.getDataSetList(page,entity).getRows();
			if(list != null && list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					list.get(i).setNo(i+1);
					list.get(i).setCreateDateStr(DateUtils.getPresentTimeStr(list.get(i).getCreateDate()));
					list.get(i).setUpdateDateStr(DateUtils.getPresentTimeStr(list.get(i).getUpdateDate()));
					list.get(i).setHandleStatus("1".equals(list.get(i).getHandleStatus())?"已处理":"未处理");
					list.get(i).setIsBackPayment("1".equals(list.get(i).getIsBackPayment())?"补":"扣");
					list.get(i).setTotalAmount(StringUtils.isNotEmpty(list.get(i).getBackPaymentMonth()) ?
							Convert.toBigDecimal(list.get(i).getDifferenceAmount()).multiply(Convert.toBigDecimal(list.get(i).getBackPaymentMonth())).setScale(2)
							:Convert.toBigDecimal(list.get(i).getDifferenceAmount()));
					if(!"1".equals(list.get(i).getHandleStatus())){
						list.get(i).setUpdateDateStr("");
						list.get(i).setUpdateUserName("");
					}
				}
			}
			Map<String,Object> resultMap = new HashMap<String, Object>();
			resultMap.put("list", list);
			resultMap.put("date", _val);
			Workbook workbook = new ExcelExportOfTemplateUtil().createExcleByTemplate(params, null, null, resultMap);
			response.setContentType("application/msword");
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-disposition", "attachment; filename=" + name);
			ServletOutputStream fos = response.getOutputStream();
			workbook.write(fos);
			fos.flush();
		} catch (Exception e) {
			log.error("薪资异动办理提醒记录导出异常"+e.getMessage(),e);
		}
	}

	@ApiOperation(value = "汇总统计导出", notes = "汇总统计导出")
	@GetMapping(value = "/api/hrmsNewsalaryItemRemindRecord/statisticsDownLoad")
	public void statisticsDownLoad(Page page, HrmsNewsalaryItemRemindRecord entity,HttpServletRequest request,
						 HttpServletResponse response) {
		page.setPageNo(1);
		page.setPageSize(Integer.MAX_VALUE);
		try {
			TemplateExportParams params = new TemplateExportParams("template/newsalaryremind/statistics_export.xls");
			String name = "薪资异动办理提醒-汇总统计.xls";
			String _val = entity.getComputeDate();
			if(!StringUtil.isEmpty(_val)) {
				name = _val + " 薪资异动办理提醒-汇总统计.xls";
			}
			name = new String(name.getBytes("UTF-8"), "ISO8859-1");


			List<HrmsNewsalaryItemRemindRecord> list = hrmsNewsalaryItemRemindRecordService.getEmployeeSalaryRemindStatistics(page,entity).getRows();
			if(list != null && list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					list.get(i).setNo(i+1);
					list.get(i).setCreateDateStr(DateUtils.getPresentTimeStr(list.get(i).getCreateDate()));
				}
				//添加合计
				HrmsNewsalaryItemRemindRecord totalRecord = new HrmsNewsalaryItemRemindRecord();
				totalRecord.setNo(list.size()+1);
				totalRecord.setEmployeeNo("合计");
				totalRecord.setTotalSupplementedAmount(list.stream().map(HrmsNewsalaryItemRemindRecord::getTotalSupplementedAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
				totalRecord.setTotalDifferenceAmount(list.stream().map(HrmsNewsalaryItemRemindRecord::getTotalDifferenceAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
				totalRecord.setTotalDeductedAmount(list.stream().map(HrmsNewsalaryItemRemindRecord::getTotalDeductedAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
				list.add(totalRecord);
			}
			Map<String,Object> resultMap = new HashMap<String, Object>();
			resultMap.put("list", list);
			resultMap.put("date", _val);
			Workbook workbook = new ExcelExportOfTemplateUtil().createExcleByTemplate(params, null, null, resultMap);
			response.setContentType("application/msword");
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-disposition", "attachment; filename=" + name);
			ServletOutputStream fos = response.getOutputStream();
			workbook.write(fos);
			fos.flush();
		} catch (Exception e) {
			log.error("薪资异动办理提醒-汇总统计导出异常"+e.getMessage(),e);
		}
	}
}

package cn.trasen.hrms.salary.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.*;

/**
 * 薪级定期调整提醒记录表
 *
 */
@Table(name = "hrms_newsalary_level_remind_record")
@Setter
@Getter
public class HrmsNewsalaryLevelRemindRecord {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 薪酬提醒ID
     */
    @Column(name = "remind_id")
    @ApiModelProperty(value = "薪酬提醒ID")
    private String remindId;

    /**
     * 调整时间
     */
    @Column(name = "adjust_date")
    @ApiModelProperty(value = "调整时间")
    private String adjustDate;

    /**
     * 员工id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    /**
     * 员工状态
     */
    @Column(name = "employee_status")
    @ApiModelProperty(value = "员工状态")
    private String employeeStatus;

    /**
     * 编制类型
     */
    @Column(name = "establishment_type")
    @ApiModelProperty(value = "编制类型")
    private String establishmentType;

    /**
     * 方案id
     */
    @Column(name = "option_id")
    @ApiModelProperty(value = "方案id")
    private String optionId;

    /**
     * 调整前薪级等级id
     */
    @Column(name = "before_salary_level")
    @ApiModelProperty(value = "调整前薪级等级id")
    private String beforeSalaryLevel;

    /**
     * 调整后薪级等级id
     */
    @Column(name = "after_salary_level")
    @ApiModelProperty(value = "调整后薪级等级id")
    private String afterSalaryLevel;

    /**
     * 调整前薪级等级金额
     */
    @Column(name = "before_amount")
    @ApiModelProperty(value = "调整前薪级等级金额")
    private BigDecimal beforeAmount;

    /**
     * 调整后薪级等级金额
     */
    @Column(name = "after_amount")
    @ApiModelProperty(value = "调整后薪级等级金额")
    private BigDecimal afterAmount;

    /**
     * 是否调整 1-是 0-否
     */
    @Column(name = "is_adjust")
    @ApiModelProperty(value = "是否调整 1-是 0-否")
    private String isAdjust;

    /**
     * 处理状态 0-未处理 1-已处理
     */
    @Column(name = "handle_status")
    @ApiModelProperty(value = "处理状态 0-未处理 1-已处理")
    private String handleStatus;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;


    @Transient
    @ApiModelProperty(value = "序号")
    private Integer no;

    @Transient
    @ApiModelProperty(value = "异动时间")
    private String createDateStr;

    @Transient
    @ApiModelProperty(value = "处理时间")
    private String updateDateStr;

    @Transient
    @ApiModelProperty(value = "所在组织id")
    private String orgId;

    @Transient
    @ApiModelProperty(value = "所在组织名称")
    private String orgName;

    @Transient
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    @Transient
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    @Transient
    @ApiModelProperty(value = "编制类型名称")
    private String establishmentTypeName;

    @Transient
    @ApiModelProperty(value = "员工状态名称")
    private String employeeStatusName;

    @Transient
    @ApiModelProperty(value = "方案名称")
    private String optionName;

    @Transient
    @ApiModelProperty(value = "调整前薪级等级名称")
    private String beforeSalaryLevelName;

    @Transient
    @ApiModelProperty(value = "调整后薪级等级名称")
    private String afterSalaryLevelName;

    @Transient
    @ApiModelProperty(value = "员工状态列表")
    private List<String> employeeStatuses;

    @Transient
    @ApiModelProperty(value = "员工编制类型列表")
    private List<String> establishmentTypes;

    @Transient
    @ApiModelProperty(value = "批量调整id集合")
    private List<String> ids;
}
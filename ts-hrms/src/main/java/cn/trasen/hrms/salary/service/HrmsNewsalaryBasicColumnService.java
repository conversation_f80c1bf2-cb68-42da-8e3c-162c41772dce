package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.bean.RequestContent;
import cn.trasen.hrms.salary.DTO.*;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicColumn;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmpHistory;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.utils.VueTableEntity;

import java.util.List;
import java.util.Map;

/**
 * @ClassName HrmsNewsalaryBasicColumnService
 * @Description TODO
 * @date 2023��11��11�� ����4:28:57
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryBasicColumnService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:28:57
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryBasicColumn record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:28:57
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryBasicColumn record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��11��11�� ����4:28:57
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryBasicColumn
	 * @date 2023��11��11�� ����4:28:57
	 * <AUTHOR>
	 */
	HrmsNewsalaryBasicColumn selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryBasicColumn>
	 * @date 2023��11��11�� ����4:28:57
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryBasicColumn> getDataSetList(Page page, HrmsNewsalaryBasicColumn record);

	/**
	 * 查询所有基础项
	 * @return
	 */
	public List<HrmsNewsalaryBasicColumn> getAllList();

	/**
	 * 薪酬档案列表表头
	 * @return
	 */
    List<VueTableEntity> listTableTitle();

	//薪酬档案列表数据
	DataSet<Map<String,String>> listTableData(Page page, SearchListTable record);

	Integer updateNumberSort(List<HrmsNewsalaryBasicColumn> records);

	List<HrmsNewsalaryBasicColumn> getBasicSalary();

	List<Map<String, String>> makePayTableData(Page page, HrmsNewsalaryOptionPayroll record);

	List<HrmsNewsalaryBasicitemEmpHistory> getHistoryByEmp(String employeeId);

	List<Map<String, Object>> makePayTableDataCount(Page page, HrmsNewsalaryOptionPayroll record);

    Integer getUncertain();  //统计未定薪

	List<HrmsNewsalaryBasicColumn> getAllListBaType();

	List<HrmsNewsalaryBasicColumn> getAllListBaType23();

	List<Map<String, String>> selectEmpAll(SearchListTable record);

	Map<String,Object> getUnoption();

	void batchBindOption(HrmsNewsalaryOptionEmp hrmsNewsalaryOptionEmp);

	/**
	* @功能描述: TODO 
	* @Title: EmpPayrollData
	* @param @param page
	* @param @param record
	* @param @return    参数
	* @return DataSet<Map<String,String>>    返回类型
	* @throws
	* <AUTHOR>
	* @date 2024年6月21日 下午4:30:40
	*/
	DataSet<Map<String, String>> EmpPayrollData(Page page, SchEmpSalary record);

	DataSet<SalaryLedgerListVo> listSalaryLedgerData(Page page, SearchSalaryLedgerVo record);

	/**
	 * 获取员工基本薪酬项数据
	 * @param page
	 * @param requestContent
	 * @return
	 */
	Map<String,Object> findEmployeBaseSalaryItemList(Page page,  RequestContent requestContent);
}

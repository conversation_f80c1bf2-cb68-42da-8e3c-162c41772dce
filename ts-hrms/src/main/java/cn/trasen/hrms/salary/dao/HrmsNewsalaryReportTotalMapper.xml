<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryReportTotalMapper">

    <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryReportTotalEo">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="colCode" column="col_code" jdbcType="VARCHAR"/>
            <result property="colName" column="col_name" jdbcType="VARCHAR"/>
           <result property="reportId" column="report_id" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="VARCHAR"/>
            <result property="ssoOrgCode" column="sso_org_code" jdbcType="VARCHAR"/>
            <result property="ssoOrgName" column="sso_org_name" jdbcType="VARCHAR"/>
            <result property="countType" column="count_type" jdbcType="CHAR"/>
            <result property="sortNo" column="sort_no" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,col_code,col_name,report_id,
        remark,create_user,create_user_name,
        create_date,update_user,update_user_name,
        update_date,is_deleted,sort_no,
        sso_org_code,sso_org_name,count_type
    </sql>

    <select id="getDataSetList" resultType="cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportTotalVo">
        select
        <include refid="Base_Column_List" />
        from hrms_newsalary_report_total  WHERE
        is_deleted = 'N'
        <if test="totalTypeVo.colName != null and totalTypeVo.colName != ''">
            and col_name like CONCAT('%', #{totalTypeVo.colName}, '%')
        </if>
        <if test="totalTypeVo.reportId != null and totalTypeVo.reportId != ''">
            and report_id = #{totalTypeVo.reportId}
        </if>
        order by SORT_NO ASC
    </select>
    <select id="getTotalData" resultType="Map">
        select (select ifnull(count(1),0) from hrms_newsalary_payroll
			where   is_deleted = 'N'
        <if test="reportTotalVo.startDate != null and reportTotalVo.startDate != '' and reportTotalVo.endDate != null and reportTotalVo.endDate != ''">
            <![CDATA[ and  payroll_date >= #{reportTotalVo.startDate} AND  payroll_date <= #{reportTotalVo.endDate} ]]>
        </if>
        <if test="reportTotalVo.payrollDate != null and reportTotalVo.payrollDate != ''">
            <![CDATA[ and  payroll_date = #{reportTotalVo.payrollDate}]]>
        </if>
        <if test="optionList != null and optionList.size() > 0">
			and option_id in
            <foreach collection="optionList" index="index" item="optionId" open="(" separator=","
                     close=")">
                #{optionId}
            </foreach>
        </if>
        ) as numbers,

		 	(select ifnull(sum(t1.salary),0) from hrms_newsalary_payroll_detail t1
			LEFT JOIN hrms_newsalary_item t2 on t1.item_id = t2.id and t1.option_id = t2.option_id
            LEFT JOIN hrms_newsalary_report_map t3 on t1.item_id = t3.item_id and t1.option_id = t3.option_id
			where  t2.sh_salary = 1
            <if test="reportTotalVo.startDate != null and reportTotalVo.startDate != '' and reportTotalVo.endDate != null and reportTotalVo.endDate != ''">
                <![CDATA[ and  t1.payroll_date >= #{reportTotalVo.startDate} AND  t1.payroll_date <= #{reportTotalVo.endDate} ]]>
            </if>
            <if test="reportTotalVo.payrollDate != null and reportTotalVo.payrollDate != ''">
                <![CDATA[ and  t1.payroll_date = #{reportTotalVo.payrollDate}]]>
            </if>
            <if test="optionList != null and optionList.size() > 0">
                and t1.option_id in
                <foreach collection="optionList" index="index" item="optionId" open="(" separator=","
                         close=")">
                    #{optionId}
                </foreach>
            </if>
        ) as yfSalary,

			(select ifnull(sum(t1.salary),0) from hrms_newsalary_payroll_detail t1
			LEFT JOIN hrms_newsalary_item t2 on t1.item_id = t2.id and t1.option_id = t2.option_id
            LEFT JOIN hrms_newsalary_report_map t3 on t1.item_id = t3.item_id and t1.option_id = t3.option_id
			where   t2.personal_tax = 1
            <if test="reportTotalVo.startDate != null and reportTotalVo.startDate != '' and reportTotalVo.endDate != null and reportTotalVo.endDate != ''">
                <![CDATA[ and  t1.payroll_date >= #{reportTotalVo.startDate} AND  t1.payroll_date <= #{reportTotalVo.endDate} ]]>
            </if>
            <if test="reportTotalVo.payrollDate != null and reportTotalVo.payrollDate != ''">
                <![CDATA[ and  t1.payroll_date = #{reportTotalVo.payrollDate}]]>
            </if>
             <if test="optionList != null and optionList.size() > 0">
                and t1.option_id in
                <foreach collection="optionList" index="index" item="optionId" open="(" separator=","
                         close=")">
                    #{optionId}
                </foreach>
            </if>) as gsSalary,

			(select ifnull(sum(t1.salary),0) from hrms_newsalary_payroll_detail t1
			LEFT JOIN hrms_newsalary_item t2 on t1.item_id = t2.id and t1.option_id = t2.option_id
            LEFT JOIN hrms_newsalary_report_map t3 on t1.item_id = t3.item_id and t1.option_id = t3.option_id
			where  t2.actual_salary = 1
             <if test="optionList != null and optionList.size() > 0">
                and t1.option_id in
                <foreach collection="optionList" index="index" item="optionId" open="(" separator=","
                         close=")">
                    #{optionId}
                </foreach>
            </if>
            <if test="reportTotalVo.startDate != null and reportTotalVo.startDate != '' and reportTotalVo.endDate != null and reportTotalVo.endDate != ''">
                <![CDATA[ and  t1.payroll_date >= #{reportTotalVo.startDate} AND  t1.payroll_date <= #{reportTotalVo.endDate} ]]>
            </if>
            <if test="reportTotalVo.payrollDate != null and reportTotalVo.payrollDate != ''">
                <![CDATA[ and  t1.payroll_date = #{reportTotalVo.payrollDate}]]>
            </if>
            ) as sfSalary
    </select>
    <select id="getReportMapList" resultType="cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportMapVo">
        select
         o.option_name, m.*
        from hrms_newsalary_report_total t
            LEFT JOIN hrms_newsalary_report_map m on t.id= m.col_id
            LEFT JOIN hrms_newsalary_option o on m.option_id = o.id
				WHERE
         t.is_deleted = 'N' and m.is_deleted = 'N' and t.id =#{colId}
    </select>
    <select id="getReportMapOptionList" resultType="java.lang.String">
        select
        DISTINCT
         m.option_id
        from hrms_newsalary_report_total t
            LEFT JOIN hrms_newsalary_report_map m on t.id= m.col_id
				WHERE
         t.is_deleted = 'N' and m.is_deleted = 'N' and t.report_id =#{reportId}
    </select>

</mapper>

package cn.trasen.hrms.salary.controller;

import cn.hutool.core.collection.CollUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalarySeniorityWageDisposition;
import cn.trasen.hrms.salary.service.HrmsNewsalarySeniorityWageDispositionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * @ClassName HrmsNewsalarySeniorityWageDispositionController
 * @Description TODO
 * @date 2024��4��12�� ����11:06:21
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "工龄工资配置Controller")
public class HrmsNewsalarySeniorityWageDispositionController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalarySeniorityWageDispositionController.class);

	@Autowired
	private HrmsNewsalarySeniorityWageDispositionService hrmsNewsalarySeniorityWageDispositionService;

	/**
	 * @Title saveHrmsNewsalarySeniorityWageDisposition
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��4��12�� ����11:06:21
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/newsalarySeniorityWageDisp/save")
	public PlatformResult<String> saveHrmsNewsalarySeniorityWageDisposition(@RequestBody HrmsNewsalarySeniorityWageDisposition record) {
		try {
			hrmsNewsalarySeniorityWageDispositionService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalarySeniorityWageDisposition
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��4��12�� ����11:06:21
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/newsalarySeniorityWageDisp/update")
	public PlatformResult<String> updateHrmsNewsalarySeniorityWageDisposition(@RequestBody HrmsNewsalarySeniorityWageDisposition record) {
		try {
			hrmsNewsalarySeniorityWageDispositionService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalarySeniorityWageDispositionById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalarySeniorityWageDisposition>
	 * @date 2024��4��12�� ����11:06:21
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/newsalarySeniorityWageDisp/{id}")
	public PlatformResult<HrmsNewsalarySeniorityWageDisposition> selectHrmsNewsalarySeniorityWageDispositionById(@PathVariable String id) {
		try {
			HrmsNewsalarySeniorityWageDisposition record = hrmsNewsalarySeniorityWageDispositionService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalarySeniorityWageDispositionById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��4��12�� ����11:06:21
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/newsalarySeniorityWageDisp/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalarySeniorityWageDispositionById(@PathVariable String id) {
		try {
			hrmsNewsalarySeniorityWageDispositionService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalarySeniorityWageDispositionList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalarySeniorityWageDisposition>
	 * @date 2024��4��12�� ����11:06:21
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/newsalarySeniorityWageDisp/list")
	public DataSet<HrmsNewsalarySeniorityWageDisposition> selectHrmsNewsalarySeniorityWageDispositionList(Page page, HrmsNewsalarySeniorityWageDisposition record) {
		return hrmsNewsalarySeniorityWageDispositionService.getDataSetList(page, record);
	}

	@ApiOperation(value = "启用", notes = "启用")
	@PostMapping("/api/newsalarySeniorityWageDisp/enable")
	public PlatformResult enable(@RequestBody List<String> ids) {
		 if(CollUtil.isEmpty(ids)){
		 	return PlatformResult.failure("ids不能为空!");
		 }
		 hrmsNewsalarySeniorityWageDispositionService.enable(ids);
		 return PlatformResult.success();
	}

	@ApiOperation(value = "禁用", notes = "禁用")
	@PostMapping("/api/newsalarySeniorityWageDisp/disEnable")
	public PlatformResult disEnable(@RequestBody List<String> ids) {
		if(CollUtil.isEmpty(ids)){
			return PlatformResult.failure("ids不能为空!");
		}
		hrmsNewsalarySeniorityWageDispositionService.disEnable(ids);
		return PlatformResult.success();
	}
}

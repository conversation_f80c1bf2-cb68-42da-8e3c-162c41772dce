package cn.trasen.hrms.salary.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalarySeniorityWageEf;
import cn.trasen.hrms.salary.service.HrmsNewsalarySeniorityWageEfService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalarySeniorityWageEfController
 * @Description TODO
 * @date 2024��4��17�� ����11:27:53
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsNewsalarySeniorityWageEfController")
public class HrmsNewsalarySeniorityWageEfController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalarySeniorityWageEfController.class);

	@Autowired
	private HrmsNewsalarySeniorityWageEfService hrmsNewsalarySeniorityWageEfService;

	/**
	 * @Title saveHrmsNewsalarySeniorityWageEf
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��4��17�� ����11:27:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/newsalarySeniorityWageEf/save")
	public PlatformResult<String> saveHrmsNewsalarySeniorityWageEf(@RequestBody HrmsNewsalarySeniorityWageEf record) {
		try {
			hrmsNewsalarySeniorityWageEfService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalarySeniorityWageEf
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��4��17�� ����11:27:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/newsalarySeniorityWageEf/update")
	public PlatformResult<String> updateHrmsNewsalarySeniorityWageEf(@RequestBody HrmsNewsalarySeniorityWageEf record) {
		try {
			hrmsNewsalarySeniorityWageEfService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalarySeniorityWageEfById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalarySeniorityWageEf>
	 * @date 2024��4��17�� ����11:27:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/newsalarySeniorityWageEf/{id}")
	public PlatformResult<HrmsNewsalarySeniorityWageEf> selectHrmsNewsalarySeniorityWageEfById(@PathVariable String id) {
		try {
			HrmsNewsalarySeniorityWageEf record = hrmsNewsalarySeniorityWageEfService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalarySeniorityWageEfById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��4��17�� ����11:27:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/newsalarySeniorityWageEf/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalarySeniorityWageEfById(@PathVariable String id) {
		try {
			hrmsNewsalarySeniorityWageEfService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalarySeniorityWageEfList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalarySeniorityWageEf>
	 * @date 2024��4��17�� ����11:27:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/newsalarySeniorityWageEf/list")
	public DataSet<HrmsNewsalarySeniorityWageEf> selectHrmsNewsalarySeniorityWageEfList(Page page, HrmsNewsalarySeniorityWageEf record) {
		return hrmsNewsalarySeniorityWageEfService.getDataSetList(page, record);
	}
}

package cn.trasen.hrms.salary.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.model.HrmsNewsalaryTemporaryAdjust;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface HrmsNewsalaryTemporaryAdjustMapper extends Mapper<HrmsNewsalaryTemporaryAdjust> {
    /**
     * @Title: getPageList
     * @Description: 分页查询薪酬调整数据
     * @param page
     * @param entity
     * @Return List<HrmsEmployeeTemporary>
     * <AUTHOR>
     * @date 2020年4月15日 下午2:45:21
     */
    List<HrmsNewsalaryTemporaryAdjust> getPageList(Page page, HrmsNewsalaryTemporaryAdjust entity);

    /**
     * @Title: getPageList
     * @Description: 查询薪酬调整数据
     * @param entity
     * @Return List<HrmsEmployeeTemporary>
     * <AUTHOR>
     * @date 2020年4月15日 下午2:45:21
     */
    List<HrmsNewsalaryTemporaryAdjust> getPageList(HrmsNewsalaryTemporaryAdjust entity);
}
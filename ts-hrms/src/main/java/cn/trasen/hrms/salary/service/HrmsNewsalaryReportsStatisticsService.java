package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportsVo;
import cn.trasen.hrms.salary.DTO.SalaryCountSearchReq;
import cn.trasen.hrms.salary.utils.VueTableEntity;

import java.util.List;
import java.util.Map;

/**
 * 薪酬报表统计
 * <AUTHOR>
 */
public interface HrmsNewsalaryReportsStatisticsService {
    /**
     * 方案汇总类表头
     * @param reportId
     * @return
     */
    List<VueTableEntity> salaryOptionTotalTitle(String reportId);

    /**
     * 方案汇总类列表数据
     * @param page
     * @param record
     * @return
     */
    DataSet<Map<String, Object>> salaryOptionStatisticsData(Page page, SalaryCountSearchReq record);

    /**
     * 保险缴费类表头
     * @param reportId
     * @return
     */
    List<VueTableEntity> salaryInsuranceTotalTitle(String reportId);

    /**
     * 保险缴费类列表数据
     * @param page
     * @param record
     * @return
     */
    DataSet<Map<String, Object>> salaryInsuranceStatisticsData(Page page, SalaryCountSearchReq record);

    /**
     * 薪酬报表公式计算
     * @param data
     * @param optionCountFormulaList
     */
    void reportFormulaCalculation(Map<String, Object> data, List<Map<String, String>> optionCountFormulaList);
}

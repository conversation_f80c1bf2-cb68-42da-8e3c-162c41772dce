package cn.trasen.hrms.salary.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.DTO.CheckPersonnel;
import cn.trasen.hrms.salary.DTO.SearchListTable;
import cn.trasen.hrms.salary.model.HrmsNewsalaryFirststepHistory;

/**
 * @ClassName HrmsNewsalaryFirststepHistoryService
 * @Description TODO
 * @date 2024��3��11�� ����5:03:18
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryFirststepHistoryService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��3��11�� ����5:03:18
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryFirststepHistory record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��3��11�� ����5:03:18
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryFirststepHistory record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��3��11�� ����5:03:18
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryFirststepHistory
	 * @date 2024��3��11�� ����5:03:18
	 * <AUTHOR>
	 */
	HrmsNewsalaryFirststepHistory selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryFirststepHistory>
	 * @date 2024��3��11�� ����5:03:18
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryFirststepHistory> getDataSetList(Page page, HrmsNewsalaryFirststepHistory record);

	//删除历史数据
	Integer deleteByOptionIdAndDate(String optionId, String computeDate);

	List<CheckPersonnel> getHistoryData(Page page, SearchListTable record);
}

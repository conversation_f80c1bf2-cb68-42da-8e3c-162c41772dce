package cn.trasen.hrms.salary.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.model.HrmsNewsalaryLevelRemindRecord;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface HrmsNewsalaryLevelRemindRecordMapper extends Mapper<HrmsNewsalaryLevelRemindRecord> {

    /**
     * @Title: getList
     * @Description: 查询薪级等级提醒列表
     * @param entity
     * @Return List<HrmsNewsalaryLevelRemindRecord>
     * <AUTHOR>
     * @date 2020年4月15日 下午2:45:21
     */
    List<HrmsNewsalaryLevelRemindRecord> getList(Page page, HrmsNewsalaryLevelRemindRecord entity);

    /**
     * @Title: getList
     * @Description: 查询薪级等级提醒列表
     * @param entity
     * @Return List<HrmsNewsalaryLevelRemindRecord>
     * <AUTHOR>
     * @date 2020年4月15日 下午2:45:21
     */
    List<HrmsNewsalaryLevelRemindRecord> getList(HrmsNewsalaryLevelRemindRecord entity);

    /**
     * 获取薪酬方案变动列表
     * @param remindCycle 预警周期 1-单次 2-每天 3-每周 4-每月 5-每年
     * @param advanceDays 提前预警天数
     * @param remindDate 提醒日期
     * @param remindTime 提醒时间
     * @return
     */
    List<HrmsNewsalaryLevelRemindRecord> getNewsalaryLevelChange(@Param(value = "remindCycle") String remindCycle, @Param(value = "advanceDays") String advanceDays,
                                                                 @Param(value = "remindDate") String remindDate, @Param(value = "remindTime") String remindTime);

}
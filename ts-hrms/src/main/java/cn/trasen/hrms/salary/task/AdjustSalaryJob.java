package cn.trasen.hrms.salary.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicitemEmpService;
import cn.trasen.hrms.salary.service.HrmsNewsalarySeniorityWageDispositionService;
import cn.trasen.hrms.salary.service.HrmsNewsalarySeniorityWageEfService;
import cn.trasen.hrms.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * 薪酬定时任务
 */
@Slf4j
@Component
public class AdjustSalaryJob {

	@Autowired
	HrmsNewsalaryBasicitemEmpService hrmsNewsalaryBasicitemEmpService;
	@Autowired
	HrmsNewsalarySeniorityWageDispositionService hrmsNewsalarySeniorityWageDispositionService;
	@Autowired
	HrmsNewsalarySeniorityWageEfService hrmsNewsalarySeniorityWageEfService;
	@Autowired
	GlobalSettingsFeignService globalSettingsFeignService;

	/**
	 * @Title: 每天凌晨2点执行一次定时任务 定薪调薪方案生效
	 * @Description: 
	 * @param       
	 * @return void      
	 * @throws
	 */
	@Scheduled(cron = "0 0 2 * * ?")
	public void updateAdjustSalary() {
		try {
			hrmsNewsalaryBasicitemEmpService.taskAdjustSalary();
			log.error("###########定时任务生效定薪调薪完成#############"+DateUtils.getPresentTimeStr());
		} catch (Exception e) {
			log.error("定时任务生效定薪调薪完成失败："+e.getMessage(),e);
		}
	}

	//每月一号执行一次   定时更新工龄工资
	@Scheduled(cron = "0 0 1 1 * ?")
	public void updateSeniorityWage() {
		try {
			hrmsNewsalarySeniorityWageDispositionService.taskUpdateSeniorityWage();
			log.error("###########定时生成工龄工资#############"+DateUtils.getPresentTimeStr());
		} catch (Exception e) {
			log.error("定时生成工龄工资失败："+e.getMessage(),e);
		}
	}

	//二福 每年1月1号调整一次
	@Scheduled(cron = "0 0 1 1 1 ? ")
	public void updateSeniorityWageEf() {
		try {
			//判断机构
			PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getGlobalSetting("Y");
			String orgCode = globalSetting.getObject().getOrgCode();
			if("cssdeshfly".equals(orgCode)){
				if("cssdeshfly".equals("cssdeshfly")){
					hrmsNewsalarySeniorityWageEfService.changgeSalary();
				}
			}
			log.error("###########二福每年1月1号调整#############"+DateUtils.getPresentTimeStr());
		} catch (Exception e) {
			log.error("定时生成工龄工资失败："+e.getMessage(),e);
		}
	}

	
}

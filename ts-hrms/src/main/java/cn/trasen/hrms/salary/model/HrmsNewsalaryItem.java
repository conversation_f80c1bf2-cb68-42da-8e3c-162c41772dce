package cn.trasen.hrms.salary.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 薪酬项目表
 *
 */
@Table(name = "hrms_newsalary_item")
@Setter
@Getter
public class HrmsNewsalaryItem {
    /**
     * 主键id
     */
    @Id
    @ApiModelProperty(value = "主键id")
    private String uid;

    /**
     * 计算id
     */
    @Column(name = "id")
    @ApiModelProperty(value = "计算id")
    private String id;

    /**
     * 项分组id
     */
    @Column(name = "group_id")
    @ApiModelProperty(value = "项分组id")
    private String groupId;

    /**
     * 薪酬组id
     */
    @Column(name = "option_id")
    @ApiModelProperty(value = "薪酬组id")
    private String optionId;

    /**
     * 薪酬档案定薪id
     */
    @Column(name = "basic_item_id")
    @ApiModelProperty(value = "薪酬档案定薪id")
    private String basicItemId;

    /**
     * 启用状态 1启用,2禁用
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "启用状态 1启用,2禁用")
    private String isEnable;

    /**
     * 1 下月引用当月数据
     */
    @Column(name = "next_month")
    @ApiModelProperty(value = "1 下月引用当月数据")
    private String nextMonth;



    /**
     * 薪酬项目名称
     */
    @Column(name = "item_name")
    @ApiModelProperty(value = "薪酬项目名称")
    private String itemName;

    /**
     * 薪酬项目编码
     */
    @Column(name = "item_code")
    @ApiModelProperty(value = "薪酬项目编码")
    private String itemCode;

    /**
     * 自定义项目
     */
    @Column(name = "custom_rule")
    @ApiModelProperty(value = "自定义项目")
    private String customRule;

    /**
     * 小数位
     */
    @Column(name = "item_digit")
    @ApiModelProperty(value = "小数位")
    private String itemDigit;

    /**
     * 1加项 2减项3不参与计算
     */
    @Column(name = "count_type")
    @ApiModelProperty(value = "1加项 2减项3不参与计算")
    private String countType;

    /**
     * 1手工录入2固定值3从薪资档案取4自定义公式
     */
    @Column(name = "item_rule")
    @ApiModelProperty(value = "1手工录入2固定值3从薪资档案取4自定义公式")
    private String itemRule;

    /**
     * 计算公式
     */
    @Column(name = "count_formula")
    @ApiModelProperty(value = "计算公式")
    private String countFormula;

    /**
     * 计算公式文本
     */
    @Column(name = "count_formula_text")
    @ApiModelProperty(value = "计算公式文本")
    private String countFormulaText;

    /**
     * 固定金额
     */
    @Column(name = "salary_item_amount")
    @ApiModelProperty(value = "固定金额")
    private BigDecimal salaryItemAmount;




    /**
     * 工资条备注
     */
    @Column(name = "salary_remark")
    @ApiModelProperty(value = "工资条备注")
    private String salaryRemark;

    /**
     * 温馨提示
     */
    @Column(name = "warm_reminder")
    @ApiModelProperty(value = "温馨提示")
    private String warmReminder;

    /**
     * 个税标识
     */
    @Column(name = "personal_tax")
    @ApiModelProperty(value = "个税标识")
    private String personalTax;

    /**
     * 应发工资标识
     */
    @Column(name = "sh_salary")
    @ApiModelProperty(value = "应发工资标识")
    private String shSalary;

    /**
     * 实发工资标识
     */
    @Column(name = "actual_salary")
    @ApiModelProperty(value = "实发工资标识")
    private String actualSalary;

    /**
     * 是否合计项 1-是
     */
    @Column(name = "is_total")
    @ApiModelProperty(value = "是否合计项")
    private String isTotal;

    /**
     * 合计标题名称
     */
    @Column(name = "total_title_name")
    @ApiModelProperty(value = "合计标题名称")
    private String totalTitleName;

    /**
     * 合计项排序
     */
    @Column(name = "total_sort_no")
    @ApiModelProperty(value = "合计项排序")
    private Integer totalSortNo;

    /**
     * 1空值隐藏
     */
    @Column(name = "is_hidden")
    @ApiModelProperty(value = "1空值隐藏")
    private String isHidden;

    @Column(name = "is_article")
    @ApiModelProperty(value = "1 工资条展示")
    private String isArticle;

    @Column(name = "carry_rule")
    @ApiModelProperty(value = "进位规则")
    private String carryRule;

    @Column(name = "library_type")
    @ApiModelProperty(value = "项目类型")
    private String libraryType;

    @Column(name = "status")
    @ApiModelProperty(value = "启用状态 1启用,2停用")
    private String status;

    @Column(name = "sort_no")
    @ApiModelProperty(value = "排序号")
    private Integer sortNo;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    @Transient
    private String employeeId;


    @Transient
    private String itemId;

    @Transient
    private String employeeNo;

    @Transient
    private String employeeName;

    @Transient
    private String payrollDate;

    @Transient
    private String personalIdentityText;

    @Transient
    private String orgName;

    @Transient
    @ApiModelProperty(value = "结果")
    private BigDecimal salary;

    @Transient
    @ApiModelProperty(value = "计薪规则自定义同步项目库")
    private String synFlag;

    @ApiModelProperty(value = "来源类型(1.修改项2.定薪项)")
    private Integer sourceType;

    @Override
    public String toString() {
        return "HrmsNewsalaryItem{" +
                "id='" + id + '\'' +
                ", groupId='" + groupId + '\'' +
                ", optionId='" + optionId + '\'' +
                ", basicItemId='" + basicItemId + '\'' +
                ", nextMonth='" + nextMonth + '\'' +
                ", itemName='" + itemName + '\'' +
                ", itemCode='" + itemCode + '\'' +
                ", itemDigit='" + itemDigit + '\'' +
                ", countType='" + countType + '\'' +
                ", itemRule='" + itemRule + '\'' +
                ", countFormula='" + countFormula + '\'' +
                ", countFormulaText='" + countFormulaText + '\'' +
                ", salaryItemAmount=" + salaryItemAmount +
                ", salaryRemark='" + salaryRemark + '\'' +
                ", warmReminder='" + warmReminder + '\'' +
                ", shSalary='" + shSalary + '\'' +
                ", actualSalary='" + actualSalary + '\'' +
                ", isHidden='" + isHidden + '\'' +
                ", isArticle='" + isArticle + '\'' +
                ", sortNo=" + sortNo +
                ", remark='" + remark + '\'' +
                ", createDate=" + createDate +
                ", createUser='" + createUser + '\'' +
                ", createUserName='" + createUserName + '\'' +
                ", updateDate=" + updateDate +
                ", updateUser='" + updateUser + '\'' +
                ", updateUserName='" + updateUserName + '\'' +
                ", isDeleted='" + isDeleted + '\'' +
                ", ssoOrgCode='" + ssoOrgCode + '\'' +
                ", ssoOrgName='" + ssoOrgName + '\'' +
                ", employeeId='" + employeeId + '\'' +
                ", itemId='" + itemId + '\'' +
                ", employeeNo='" + employeeNo + '\'' +
                ", employeeName='" + employeeName + '\'' +
                ", payrollDate='" + payrollDate + '\'' +
                ", personalIdentityText='" + personalIdentityText + '\'' +
                ", orgName='" + orgName + '\'' +
                ", salary=" + salary +
                '}';
    }
}
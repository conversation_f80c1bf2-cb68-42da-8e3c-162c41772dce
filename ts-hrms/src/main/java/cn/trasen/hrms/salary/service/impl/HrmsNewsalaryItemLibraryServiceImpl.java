package cn.trasen.hrms.salary.service.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.shiro.util.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryBasicColumnMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryItemLibraryMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicColumn;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemLibrary;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemService;
import cn.trasen.hrms.salary.service.IHrmsNewSalaryItemLibraryService;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;

@Service
public class HrmsNewsalaryItemLibraryServiceImpl implements IHrmsNewSalaryItemLibraryService {

    @Autowired
    private HrmsNewsalaryItemLibraryMapper mapper;

    @Autowired
    private HrmsNewsalaryItemService hrmsNewsalaryItemService;
    
    @Autowired
    private HrmsNewsalaryBasicColumnMapper hrmsNewsalaryBasicColumnMapper;

    @Override
    public DataSet<HrmsNewsalaryItemLibrary> getDataList(Page page, HrmsNewsalaryItemLibrary record) {
        Example example = new Example(HrmsNewsalaryItemLibrary.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        if(StrUtil.isNotBlank(record.getItemType())){
            criteria.andEqualTo("groupId",record.getGroupId());
        }
        if(StrUtil.isNotBlank(record.getStatus())){
            criteria.andEqualTo("status",record.getStatus());
        }
        if(StrUtil.isNotBlank(record.getItemName())){
            criteria.andLike("itemName","%"+record.getItemName()+"%");
        }
        if(StrUtil.isNotBlank(record.getLibraryType())){
            criteria.andEqualTo("libraryType",record.getLibraryType());
        }
        List<HrmsNewsalaryItemLibrary> records = mapper.selectByExampleAndRowBounds(example,page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public void update(HrmsNewsalaryItemLibrary record) {
        //项目名称校验
        Example example = new Example(HrmsNewsalaryItemLibrary.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("isDeleted","N");
        criteria.andEqualTo("itemName",record.getItemName());
        criteria.andNotEqualTo("id",record.getId());
        List<HrmsNewsalaryItemLibrary> list = mapper.selectByExample(example);
        if(CollUtil.isNotEmpty(list)){
            throw new BusinessException("项目名称已存在!");
        }
        ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();
        record.setUpdateDate(new Date());
        record.setUpdateUser(userInfo.getUsercode());
        record.setUpdateUserName(userInfo.getUsername());
		if (!"4".equals(record.getItemRule())){
			record.setCountFormula("");
			record.setCountFormulaText("");
		}
		if (!"2".equals(record.getItemRule())) {
			record.setSalaryItemAmount(null);
		}
		if (!"3".equals(record.getItemRule())) {
			record.setCustomRule(null);
		}
        mapper.updateByPrimaryKeySelective(record);
        //同步成功后，需要同步已经被引入到薪酬方案中的名称； hrms_newsalary_basic_column,如果有的话，就更新
        HrmsNewsalaryBasicColumn hnbc = new HrmsNewsalaryBasicColumn();
        hnbc.setEmpField(record.getId());
        hnbc.setBasicItemName(record.getItemName());
        Example exa = new Example(HrmsNewsalaryBasicColumn.class);
        Example.Criteria cri = exa.createCriteria();
        cri.andEqualTo("empField",record.getId());
        cri.andEqualTo("isDeleted", "N");
        hrmsNewsalaryBasicColumnMapper.updateByExampleSelective(hnbc, exa);
    }

    @Override
    public void enalbe(List<String> ids) {
        HrmsNewsalaryItemLibrary library = new HrmsNewsalaryItemLibrary();
        library.setStatus("1");
        Example example = new Example(HrmsNewsalaryItemLibrary.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id",ids);
        mapper.updateByExampleSelective(library,example);
    }

    @Override
    public void disEnalbe(List<String> ids) {
        for (String id : ids) {
            boolean check = hrmsNewsalaryItemService.checkQuote(id);
            if(check){
                throw new BusinessException("项目被引用,不能禁用!");
            }
        }
        HrmsNewsalaryItemLibrary library = new HrmsNewsalaryItemLibrary();
        library.setStatus("2");
        Example example = new Example(HrmsNewsalaryItemLibrary.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id",ids);
        mapper.updateByExampleSelective(library,example);
    }

    @Override
    public void delete(HrmsNewsalaryItemLibrary record) {
        if(StrUtil.equals("1",record.getStatus())){
            throw new BusinessException("项目启用中,不能删除!");
        }
        ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();
        record.setUpdateDate(new Date());
        record.setUpdateUser(userInfo.getUsercode());
        record.setUpdateUserName(userInfo.getUsername());
        record.setIsDeleted("Y");
        mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void importItem(List<HrmsNewsalaryItemLibrary> record) {
        Example example = new Example(HrmsNewsalaryItemLibrary.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("isDeleted","N");
        criteria.andEqualTo("itemSource","1");
        List<HrmsNewsalaryItemLibrary> list = mapper.selectByExample(example);
        String libraryCode = this.getLibraryCode("1");
        ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();
        if(CollUtil.isEmpty(list)){
            for (HrmsNewsalaryItemLibrary library : record) {
                library.setId(IdUtil.getId());
                library.setLibraryCode(libraryCode);
                library.setItemCode(IdUtil.getId());
                library.setItemSource("1");
                library.setIsDeleted("N");
                library.setStatus("1");
                library.setLibraryType(library.getGroupId());
                library.setCreateDate(new Date());
                library.setCreateUser(userInfo.getUsercode());
                library.setCreateUserName(userInfo.getUsername());
                library.setSsoOrgCode(userInfo.getCorpcode());
                library.setSsoOrgName(userInfo.getOrgName());
                mapper.insertSelective(library);
                libraryCode = "Y" + new BigInteger(libraryCode.substring(1)).add(BigInteger.ONE);
            }
        }else{
            //项目名称校验
            for (HrmsNewsalaryItemLibrary library : list) {
                for (HrmsNewsalaryItemLibrary item : record) {
                    if(StrUtil.equals(library.getItemName(),item.getItemName())){
                        throw new BusinessException(item.getItemName()+"已存在!");
                    }
                }
            }
            for (HrmsNewsalaryItemLibrary library : record) {
                library.setId(IdUtil.getId());
                library.setLibraryCode(libraryCode);
                library.setItemCode(IdUtil.getId());
                library.setItemSource("1");
                library.setIsDeleted("N");
                library.setStatus("1");
                library.setLibraryType(library.getGroupId());
                library.setCreateDate(new Date());
                library.setCreateUser(userInfo.getUsercode());
                library.setCreateUserName(userInfo.getUsername());
                library.setSsoOrgCode(userInfo.getCorpcode());
                library.setSsoOrgName(userInfo.getOrgName());
                mapper.insertSelective(library);
                libraryCode = "Y" + new BigInteger(libraryCode.substring(1)).add(BigInteger.ONE);
            }
        }  
    }

    @Override
    public void save(HrmsNewsalaryItemLibrary record) {
        Example example = new Example(HrmsNewsalaryItemLibrary.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("itemName", record.getItemName());
        List<HrmsNewsalaryItemLibrary> list = mapper.selectByExample(example);
        if(CollUtil.isNotEmpty(list)){
            throw new RuntimeException(record.getItemName()+"已存在!");
        }
        ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();
        String libraryCode = this.getLibraryCode("2");
        if(StrUtil.isBlank(record.getId())) {
            record.setId(IdUtil.getId());
        }
        //自定义项目
        if(StrUtil.isNotBlank(record.getCustomRule()) && record.getCustomRule().indexOf("=")>1){
            int index = record.getCustomRule().indexOf("=", record.getCustomRule().indexOf("=")+1);
            String customRule =  record.getCustomRule().substring(0,index);
            String salaryItemAmount = record.getCustomRule().substring(index+1);
            record.setCustomRule(customRule);
            record.setSalaryItemAmount(new BigDecimal(salaryItemAmount));
        }
        record.setLibraryCode(libraryCode);
        record.setItemCode(IdUtil.getId());
        record.setCreateDate(new Date());
        record.setCreateUser(userInfo.getUsercode());
        record.setCreateUserName(userInfo.getUsername());
        record.setIsDeleted("N");
        record.setItemSource("2");
        record.setStatus("1");
        record.setGroupId("8");
        record.setSsoOrgCode(userInfo.getCorpcode());
        record.setSsoOrgName(userInfo.getOrgName());
        mapper.insertSelective(record);
    }

    @Override
    public HrmsNewsalaryItemLibrary getById(String id) {
        Assert.hasText(id,"id不能为空!");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public List<HrmsNewsalaryItemLibrary> getEnableData() {
        Example example = new Example(HrmsNewsalaryItemLibrary.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("isDeleted","N");
        criteria.andEqualTo("status","1");
        List<HrmsNewsalaryItemLibrary> list = mapper.selectByExample(example);
        return list;
    }

    @Override
    public List<HrmsNewsalaryItemLibrary> getCustomItem() {
        Example example = new Example(HrmsNewsalaryItemLibrary.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("isDeleted","N");
        criteria.andEqualTo("status","1");
        List<HrmsNewsalaryItemLibrary> list = mapper.selectByExample(example);
        return list;
    }

    @Override
    public DataSet<HrmsNewsalaryItemLibrary> getEnableDataList(Page page, HrmsNewsalaryItemLibrary record) {
        Example example = new Example(HrmsNewsalaryItemLibrary.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("status","1");
        if(StrUtil.isNotBlank(record.getItemName())){
            criteria.andLike("itemName","%"+record.getItemName()+"%");
        }
        List<HrmsNewsalaryItemLibrary> records = mapper.selectByExampleAndRowBounds(example,page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public String getLibraryCode(String itemSource){
        String preFix = "Y";
        if(StrUtil.equals("2",itemSource)){
            preFix = "Z";
        }
        String libraryCode = "";
        Example example = new Example(HrmsNewsalaryItemLibrary.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("itemSource","itemSource");
        List<HrmsNewsalaryItemLibrary> list = mapper.selectByExample(example);
        if(CollUtil.isNotEmpty(list)){
            list = list.stream().sorted(Comparator.comparing(HrmsNewsalaryItemLibrary::getLibraryCode).reversed())
                    .collect(Collectors.toList());
            String code = list.get(0).getLibraryCode();
            BigInteger num = new BigInteger(code.substring(1));
            libraryCode = preFix + String.valueOf(num.add(BigInteger.ONE));
        }else{
            String dataTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            libraryCode = preFix + dataTime + "001";
        }
        return libraryCode;
    }
}

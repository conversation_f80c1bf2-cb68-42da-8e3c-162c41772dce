package cn.trasen.hrms.salary.enums;

import lombok.Getter;

/**
 * 启用禁用枚举类
 */
@Getter
public enum NewsalaryTemporaryAdjustOpTypeEnum {

    ADD("1", "新增"),
    UPDATE("2", "修改"),
    DELETE("3", "删除"),
    IMPORT("4", "导入"),
//    EXPORT("5", "导出"),
    CALCULATE("6", "核算");


    private final String key;
    private final String val;

    private NewsalaryTemporaryAdjustOpTypeEnum(String key, String val) {
        this.key = key;
        this.val = val;
    }

    /**
     * @Title: getValByKey
     * @Description: 根据key获得val值
     * @Param: key
     * @Return: String
     * <AUTHOR>
     */
    public static String getValByKey(String key) {
        for (NewsalaryTemporaryAdjustOpTypeEnum item : NewsalaryTemporaryAdjustOpTypeEnum.values()) {
            if (item.key.equals(key)) {
                return item.val;
            }
        }
        return "";
    }

    /**
     * @Title: getKeyByVal
     * @Description: 根据val获得key值
     * @param val
     * @Return String
     * <AUTHOR>
     * @date 2020年6月17日 下午5:30:51
     */
    public static String getKeyByVal(String val) {
        for (NewsalaryTemporaryAdjustOpTypeEnum item : NewsalaryTemporaryAdjustOpTypeEnum.values()) {
            if (item.val.equals(val)) {
                return item.key;
            }
        }
        return "";
    }
}

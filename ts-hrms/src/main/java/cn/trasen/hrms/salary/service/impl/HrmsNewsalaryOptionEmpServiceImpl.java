package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionEmpMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOption;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionEmpService;
import cn.trasen.hrms.utils.IdUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrmsNewsalaryOptionEmpServiceImpl
 * @Description TODO
 * @date 2023��11��11�� ����4:36:17
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryOptionEmpServiceImpl implements HrmsNewsalaryOptionEmpService {

    @Autowired
    private HrmsNewsalaryOptionEmpMapper mapper;
    @Autowired
    private HrmsNewsalaryOptionMapper optionMapper;
    @Autowired
    private HrmsNewsalaryOptionEmpService hrmsNewsalaryOptionEmpService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(HrmsNewsalaryOptionEmp record) {
        record.setId(IdUtil.getId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(HrmsNewsalaryOptionEmp record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        HrmsNewsalaryOptionEmp record = new HrmsNewsalaryOptionEmp();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Integer deleteByOptionId(String id) {
        HrmsNewsalaryOptionEmp record = new HrmsNewsalaryOptionEmp();
        record.setOptionId(id);
        return mapper.delete(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer optionEmpSort(HrmsNewsalaryOptionEmp record) {
        Assert.hasText(record.getId(),"方案人员id不能为空");
        Assert.notNull(record.getSortNum(),"排序号不能为空");
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public HrmsNewsalaryOptionEmp selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<HrmsNewsalaryOptionEmp> getDataSetList(Page page, HrmsNewsalaryOptionEmp record) {
        Example example = new Example(HrmsNewsalaryOptionEmp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        if (!StringUtil.isEmpty(record.getOptionId())) {
            criteria.andEqualTo("optionId", record.getOptionId());
        }
        List<HrmsNewsalaryOptionEmp> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<HrmsNewsalaryOptionEmp> getAllByOptionId(String id, String empId) {
        Example example = new Example(HrmsNewsalaryOptionEmp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("optionId", id);
        if(StringUtils.isNotBlank(empId)) {
            criteria.andEqualTo("employeeId", empId);
        }
        return mapper.selectByExample(example);
    }

    @Override
    public HrmsNewsalaryOptionEmp getEmpByOption(String id) {
        return mapper.getEmpByOption(id);
    }

    @Override
    public List<HrmsNewsalaryOptionEmp> getEmployeeDetails(String optionId) {
        return mapper.getEmployeeDetails(optionId);
    }

    @Override
    public List<HrmsNewsalaryOptionEmp> getAllEmployeeDetails(String optionId) {
        return mapper.getAllEmployeeDetails(optionId);
    }

    @Override
    public List<HrmsNewsalaryOptionEmp> getOprionIdByEmpNo(List<String> list) {
        return mapper.getOprionIdByEmpNo(list);
    }

    /**
     * @param employeeId:
     * @return void
     * <AUTHOR>
     * @description 清空用户薪酬组
     * @date 2024/6/8 17:30
     */
    @Override
    @Transactional(readOnly = false)
    public HrmsNewsalaryOptionEmp destoryByEmployeeId(String employeeId) {
        Assert.hasText(employeeId, "ID不能为空.");
        HrmsNewsalaryOptionEmp record = new HrmsNewsalaryOptionEmp();
        record.setIsDeleted("Y");
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        Example example = new Example(HrmsNewsalaryOptionEmp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("employeeId", employeeId);
        criteria.andEqualTo("isDeleted", "N");
        HrmsNewsalaryOptionEmp hrmsNewsalaryOptionEmp = mapper.selectOneByExample(example);
        mapper.updateByExampleSelective(record, example);
        return hrmsNewsalaryOptionEmp;
    }

    @Override
    @Transactional(readOnly = false)
    public void perSave(String optionId, List<String> employeeIds) {
        for (String employeeId : employeeIds) {
            HrmsNewsalaryOptionEmp optionEmp = this.getOptionByEmployeeId(employeeId);
            if(ObjectUtil.isNotEmpty(optionEmp)){
                this.deleteOptionByEmployeeId(employeeId);
            }
            HrmsNewsalaryOptionEmp emp = new HrmsNewsalaryOptionEmp();
            emp.setEmployeeId(employeeId);
            emp.setOptionId(optionId);
            this.save(emp);
        }
        //查询之前方案薪酬组数量
        HrmsNewsalaryOption record = optionMapper.selectByPrimaryKey(optionId);
        if (Objects.nonNull(record)) {
            List<HrmsNewsalaryOptionEmp> list = hrmsNewsalaryOptionEmpService.getAllByOptionId(optionId, null);
            record.setHeadCount(String.valueOf(list.size()));
            optionMapper.updateByPrimaryKeySelective(record);
        }
    }

    @Override
    public HrmsNewsalaryOptionEmp getOptionByEmployeeId(String employeeId) {
        Example example = new Example(HrmsNewsalaryOptionEmp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("employeeId",employeeId);
        criteria.andEqualTo("isDeleted","N");
        HrmsNewsalaryOptionEmp optionEmp = mapper.selectOneByExample(example);
        return optionEmp;
    }

    @Override
    @Transactional(readOnly = false)
    public void deleteOptionByEmployeeId(String employeeId) {
        Example example = new Example(HrmsNewsalaryOptionEmp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("employeeId",employeeId);
        criteria.andEqualTo("isDeleted","N");
        ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();
        HrmsNewsalaryOptionEmp optionEmp = new HrmsNewsalaryOptionEmp();
        optionEmp.setIsDeleted("Y");
        optionEmp.setUpdateDate(new Date());
        optionEmp.setUpdateUser(userInfo.getUsercode());
        optionEmp.setUpdateUserName(userInfo.getUsername());
        mapper.updateByExampleSelective(optionEmp,example);
    }

    @Override
    @Transactional(readOnly = false)
    public void deleteOption(String optionId, List<String> employeeIds) {
        Example example = new Example(HrmsNewsalaryOptionEmp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("employeeId",employeeIds);
        criteria.andEqualTo("optionId",optionId);
        criteria.andEqualTo("isDeleted","N");
        ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();
        HrmsNewsalaryOptionEmp optionEmp = new HrmsNewsalaryOptionEmp();
        optionEmp.setIsDeleted("Y");
        optionEmp.setUpdateDate(new Date());
        optionEmp.setUpdateUser(userInfo.getUsercode());
        optionEmp.setUpdateUserName(userInfo.getUsername());
        mapper.updateByExampleSelective(optionEmp,example);
        //查询之前方案薪酬组数量
        HrmsNewsalaryOption record = optionMapper.selectByPrimaryKey(optionId);
        if (Objects.nonNull(record)) {
            List<HrmsNewsalaryOptionEmp> list = hrmsNewsalaryOptionEmpService.getAllByOptionId(optionId, null);
            record.setHeadCount(String.valueOf(list.size()));
            optionMapper.updateByPrimaryKeySelective(record);
        }
    }

    @Override
    public List<HrmsNewsalaryOptionEmp> getEmployeeDetailByOptionId(String optionId) {
        return mapper.getEmployeeDetailByOptionId(optionId);
    }
}

package cn.trasen.hrms.salary.dao;

import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailImport;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface HrmsNewsalaryPayrollDetailImportMapper extends Mapper<HrmsNewsalaryPayrollDetailImport> {
    List<HrmsNewsalaryItem> exportTemplateAll(@Param("optionId") String optionId);

    List<HrmsNewsalaryItem> exportSalaryItemByOptionId(@Param("optionId") String optionId,@Param("computeDate") String computeDate);
}
package cn.trasen.hrms.salary.dao;

import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportMapVo;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportTotalVo;
import cn.trasen.hrms.salary.model.HrmsNewsalaryReportTotalEo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Entity generator.domain.HrmsNewsalaryTotalType
 */
public interface HrmsNewsalaryReportTotalMapper extends Mapper<HrmsNewsalaryReportTotalEo> {

    List<HrmsNewsalaryReportTotalVo> getDataSetList(@Param("totalTypeVo") HrmsNewsalaryReportTotalVo totalTypeVo);

    /**
     * @param reportTotalVo
     * @return
     */
    Map<String, Object> getTotalData(@Param("reportTotalVo") HrmsNewsalaryReportTotalVo reportTotalVo,@Param("optionList") List<String> optionList);

    List<HrmsNewsalaryReportMapVo> getReportMapList(@Param("colId") String colId);

    List<String> getReportMapOptionList(@Param("reportId") String reportId);
}





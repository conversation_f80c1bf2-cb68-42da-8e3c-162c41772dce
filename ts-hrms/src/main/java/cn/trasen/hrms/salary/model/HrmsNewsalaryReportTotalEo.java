package cn.trasen.hrms.salary.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;

import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
* 薪酬汇总类型配置表
* @TableName hrms_newsalary_report_total
*/
@Table(name = "hrms_newsalary_report_total")
@Setter
@Getter
public class HrmsNewsalaryReportTotalEo {

    /**
    * ID
    */
    @Id
    private String id;

    /**
     * 字段编码
     */
    @Column(name = "col_code")
    @ApiModelProperty(value = "字段编码")
    private String colCode;

    /**
     * 报表id
     */
    @Column(name = "report_id")
    @ApiModelProperty(value = "报表id")
    private String reportId;
    /**
     * 字段名称
     */
    @Column(name = "col_name")
    @ApiModelProperty(value = "字段名称")
    private String colName;
    /**
    * 备注
    */
    @Column(name = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
    * 创建人ID
    */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人ID")
    private String createUser;
    /**
    * 创建人姓名
    */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;
    /**
    * 创建时间
    */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;
    /**
    * 更新人ID
    */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;
    /**
    * 更新人姓名
    */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;
    /**
    * 更新时间
    */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;
    /**
    * 是否删除 N 正常   Y 删除
    */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;

    /**
    *
    */
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    /**
    * 
    */
    @Column(name = "sso_org_name")
    private String ssoOrgName;

    /**
     * 排序
     */
    @Column(name = "sort_no")
    @ApiModelProperty(value = "排序")
    private Integer sortNo;

    /**
     * 计算类型 1-增项 2-减项
     */
    @Column(name = "count_type")
    @ApiModelProperty(value = "计算类型 1-增项 2-减项")
    private String countType;

    /**
     * 用于计算公式类数据
     */
    @Transient
    private String sql;
}

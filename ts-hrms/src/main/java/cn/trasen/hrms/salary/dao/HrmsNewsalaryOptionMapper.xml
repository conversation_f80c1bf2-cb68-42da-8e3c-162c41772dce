<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryOption">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="option_name" jdbcType="VARCHAR" property="optionName" />
    <result column="option_code" jdbcType="VARCHAR" property="optionCode" />
    <result column="is_enable" jdbcType="VARCHAR" property="isEnable" />
    <result column="option_cycle" jdbcType="VARCHAR" property="optionCycle" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>
  <select id="getPaySlipOption" parameterType="cn.trasen.hrms.salary.model.HrmsNewsalaryOption" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryOption">
        select o.*
        from hrms_newsalary_option o where o.is_enable = '1'
        and o.is_deleted = 'N'
        <if test="optionName != null and optionName != ''">
            and o.option_name like CONCAT(CONCAT('%', #{optionName}), '%')
        </if>
        <!-- and not exists
        (select 1 from hrms_newsalary_payslip n where o.id = n.option_id and n.is_deleted = 'N') -->
  </select>
</mapper>
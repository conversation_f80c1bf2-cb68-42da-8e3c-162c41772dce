<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryPayrollDetailUploadMapper">

    <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailUploadEo">
        <id property="id" column="id" jdbcType="VARCHAR"/>

        <result property="employeeNo" column="employee_no" jdbcType="VARCHAR"/>
        <result property="itemId" column="item_id" jdbcType="VARCHAR"/>
        <result property="optionId" column="option_id" jdbcType="VARCHAR"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="salary" column="salary" jdbcType="DECIMAL"/>
        <result property="isExamine" column="is_examine" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="CHAR"/>
        <result property="ssoOrgCode" column="sso_org_code" jdbcType="VARCHAR"/>
        <result property="ssoOrgName" column="sso_org_name" jdbcType="VARCHAR"/>
        <result property="sortNo" column="sort_no" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,employee_no,item_id,
        option_id,item_code,item_name,
        salary,import_date,is_examine,
        remark,create_date,create_user,
        create_user_name,update_date,update_user,
        update_user_name,is_deleted,sso_org_code,
        sso_org_name,sort_no
    </sql>
    <select id="getAllList" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailUploadEo">
         SELECT DISTINCT id as itemId,item_name as itemName from   hrms_newsalary_item_library where source_type = 1 and status =1 and is_deleted ='N'
         union
         SELECT DISTINCT id as itemId,item_name as itemName  from   hrms_newsalary_item where source_type = 1 and status =1 and is_deleted ='N'
    </select>
    <select id="listTableData" resultType="java.util.Map">
        SELECT t1.employee_id, t1.employee_name ,t1.employee_no,t1.org_id,t1.employee_id,t3.name AS orgName,
        (0) as isExamine,
        ('未上报') as text,
        t5.update_date,
        t5.update_user_name,
        (#{record.payrollDate}) AS effective_date,
        t6.option_name
        <if test="allList != null and allList.size() > 0">
            ,
            <foreach collection="allList" index="index"
                     item="item" open="" separator="," close="">
                MAX(CASE
                WHEN t7.id=#{item.itemId}
                THEN ''
                ELSE ''
                END)
                AS '${item.itemId}'
            </foreach>
        </if>
        FROM cust_emp_base t1
        left JOIN hrms_newsalary_option_emp t5 on t1.employee_id=t5.employee_id and t5.is_deleted= t1.is_deleted
        Left join hrms_newsalary_item t7 on t7.option_id = t5.option_id
        LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
        left join hrms_newsalary_option t6 on t5.option_id = t6.id
        WHERE t1.is_deleted='N'
        <if test="employeeList != null and employeeList.size() > 0">
            and t1.employee_id in
            <foreach collection="employeeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="record.employee_no != null and record.employee_no != ''">
            and t1.employee_no = #{record.employee_no}
        </if>
        <if test="record.employee_name != null and record.employee_name != ''">
            and t1.employee_name LIKE CONCAT('%',#{record.employee_name},'%')
        </if>
        <if test="record.orgName != null and record.orgName != ''">
            and t3.name  LIKE CONCAT('%',#{record.orgName},'%')
        </if>
        GROUP BY t1.employee_id
    </select>
    <select id="getEmployeeList" resultType="java.lang.String">
        select DISTINCT employee_id from hrms_attendance_timekeeper where timekeeper_id =#{usercode}
    </select>
    <select id="queryOptionId" resultType="java.lang.String">
        select  DISTINCT t7.option_id FROM cust_emp_base t1
        left JOIN hrms_newsalary_option_emp t5 on t1.employee_id=t5.employee_id and t5.is_deleted= t1.is_deleted
        Left join hrms_newsalary_item t7 on t7.option_id = t5.option_id where t1.employee_no =#{employeeNo} and t1.is_deleted='N'
    </select>
    <select id="listTableDataPage" resultType="java.util.Map">
        SELECT pdu.id, t1.employee_name ,t1.employee_no,t1.org_id,t1.employee_id,t3.name AS orgName,
        du.approval_status as isExamine,
        (CASE WHEN pdu.is_examine = 0 THEN '未上报' WHEN pdu.is_examine = 1 THEN '审批中' WHEN pdu.is_examine = 2 THEN '已审批'
        ELSE '未上报' END) as text,
        pdu.update_date,
        pdu.update_user_name,
        nop.compute_status as computeStatus,
        (du.upload_date) AS effective_date,
        t6.option_name
        <if test="allList != null and allList.size() > 0">
            ,
            <foreach collection="allList" index="index"
                     item="item" open="" separator="," close="">
                MAX(CASE
                WHEN pdu.item_id=#{item.itemId}
                THEN pdu.salary
                ELSE ''
                END)
                AS '${item.itemId}'
            </foreach>
        </if>
        FROM cust_emp_base t1
        left JOIN hrms_newsalary_option_emp t5 on t1.employee_id=t5.employee_id and t5.is_deleted= t1.is_deleted
        Left join hrms_newsalary_item t7 on t7.option_id = t5.option_id
        left join hrms_upload_record du on du.employee_id = t1.employee_id
        LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
        left join hrms_newsalary_option t6 on t5.option_id = t6.id
        left join hrms_newsalary_payroll_detail_upload pdu on pdu.employee_no = t1.employee_no
        left join hrms_newsalary_option_payroll nop on t7.option_id =nop.option_id and nop.compute_date =pdu.payroll_date
        WHERE t1.is_deleted='N' AND du.is_deleted='N'
        and du.upload_date = #{record.payrollDate} AND du.approval_status in (1,2) and pdu.is_examine in(1,2)
        and pdu.payroll_date = #{record.payrollDate}
        <if test="employeeList != null and employeeList.size() > 0">
            and t1.employee_id in
            <foreach collection="employeeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="record.isExamine != null and record.isExamine != ''">
            and du.approval_status = #{record.isExamine}
        </if>
        <if test="record.employee_no != null and record.employee_no != ''">
            and t1.employee_no = #{record.employee_no}
        </if>
        <if test="record.employee_name != null and record.employee_name != ''">
            and t1.employee_name LIKE CONCAT('%',#{record.employee_name},'%')
        </if>
        <if test="record.orgName != null and record.orgName != ''">
            and t3.name  LIKE CONCAT('%',#{record.orgName},'%')
        </if>
        GROUP BY t1.employee_id
    </select>

    <select id="selectOrgInfo" resultType="String">
        select t1.employee_no from cust_emp_base t1
        LEFT JOIN comm_organization t2 on t1.org_id = t2.organization_id
        where t2.organization_id in ('445897456694112256','445897456484397056')
    </select>
    <select id="queryUploadList" resultType="java.util.Map">
        SELECT pdu.id, t1.employee_name ,t1.employee_no,t1.org_id,t1.employee_id,t3.name AS orgName,
        (CASE WHEN pdu.is_examine != '' THEN pdu.is_examine ELSE 0 END) as isExamine,
        (CASE WHEN pdu.is_examine = 0 THEN '未上报' WHEN pdu.is_examine = 1 THEN '审批中' WHEN pdu.is_examine = 2 THEN '已审批'
        ELSE '未上报' END) as text,
        pdu.update_date,
        pdu.update_user_name,
        (#{record.payrollDate}) AS effective_date,
        t6.option_name
        <if test="allList != null and allList.size() > 0">
            ,
            <foreach collection="allList" index="index"
                     item="item" open="" separator="," close="">
                MAX(CASE
                WHEN pdu.item_id=#{item.itemId}
                THEN pdu.salary
                ELSE ''
                END)
                AS '${item.itemId}'
            </foreach>
        </if>
        FROM cust_emp_base t1
        left JOIN hrms_newsalary_option_emp t5 on t1.employee_id=t5.employee_id and t5.is_deleted= t1.is_deleted
        Left join hrms_newsalary_item t7 on t7.option_id = t5.option_id
        LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
        left join hrms_newsalary_option t6 on t5.option_id = t6.id
        left join hrms_newsalary_payroll_detail_upload pdu on pdu.employee_no = t1.employee_no
        WHERE t1.is_deleted='N' AND pdu.is_deleted='N'
        and pdu.payroll_date = #{record.payrollDate}

        <if test="record.employee_no != null and record.employee_no != ''">
            and t1.employee_no = #{record.employee_no}
        </if>
        <if test="record.employee_name != null and record.employee_name != ''">
            and t1.employee_name LIKE CONCAT('%',#{record.employee_name},'%')
        </if>
        <if test="record.orgName != null and record.orgName != ''">
            and t3.name  LIKE CONCAT('%',#{record.orgName},'%')
        </if>
        <if test="record.isExamine != null">
            and pdu.is_examine = #{record.isExamine}
        </if>
        GROUP BY t1.employee_id
        <if test="codeList != null and codeList.size() > 0">
            union all
            SELECT t5.id, t1.employee_name ,t1.employee_no,t1.org_id,t1.employee_id,t3.name AS orgName,
            (0) as isExamine,
            ('未上报') as text,
            t5.update_date,
            t5.update_user_name,
            (#{record.payrollDate}) AS effective_date,
            t6.option_name
            <if test="allList != null and allList.size() > 0">
                ,
                <foreach collection="allList" index="index"
                         item="item" open="" separator="," close="">
                    MAX(CASE
                    WHEN t7.id=#{item.itemId}
                    THEN ''
                    ELSE ''
                    END)
                    AS '${item.itemId}'
                </foreach>
            </if>
            FROM cust_emp_base t1
            left JOIN hrms_newsalary_option_emp t5 on t1.employee_id=t5.employee_id and t5.is_deleted= t1.is_deleted
            Left join hrms_newsalary_item t7 on t7.option_id = t5.option_id
            LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
            left join hrms_newsalary_option t6 on t5.option_id = t6.id
            WHERE t1.is_deleted='N'
            <if test="codeList != null and codeList.size() > 0">
                and t1.employee_id in
                <foreach collection="codeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="record.employee_no != null and record.employee_no != ''">
                and t1.employee_no = #{record.employee_no}
            </if>
            <if test="record.employee_name != null and record.employee_name != ''">
                and t1.employee_name LIKE CONCAT('%',#{record.employee_name},'%')
            </if>
            <if test="record.orgName != null and record.orgName != ''">
                and t3.name  LIKE CONCAT('%',#{record.orgName},'%')
            </if>
            GROUP BY t1.employee_id
        </if>
    </select>
    <select id="selectEmpAll" resultType="java.util.Map">
        SELECT DISTINCT t1.employee_no,t1.employee_name, t3.name FROM cust_emp_base t1
        LEFT JOIN hrms_newsalary_basicitem_emp t2 ON t1.employee_id = t2.employee_id
        LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
        LEFT JOIN hrms_newsalary_option_emp t5 ON t1.employee_id=t5.employee_id AND t5.is_deleted='N'
        WHERE t1.employee_status IN(1,6,8,12,99,9)
        AND t1.is_deleted='N'
        <if test='emploeeyList != null and emploeeyList.size() > 0'>
            and t1.employee_id in
            <foreach collection="emploeeyList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="record.employee_no != null and record.employee_no != ''">
            and t1.employee_no = #{record.employee_no}
        </if>
        <if test="record.employee_name != null and record.employee_name != ''">
            and t1.employee_name LIKE CONCAT('%',#{record.employee_name},'%')
        </if>
        <if test="record.orgName != null and record.orgName != ''">
            and t3.name  LIKE CONCAT('%',#{record.orgName},'%')
        </if>
    </select>
    <select id="getEmploeeIdByCode" resultType="java.lang.String">
       SELECT DISTINCT t1.employee_id FROM hrms_newsalary_payroll_detail_upload du
        LEFT JOIN cust_emp_base t1  on du.employee_no = t1.employee_no
        where t1.employee_id in(select DISTINCT employee_id from hrms_attendance_timekeeper where timekeeper_id =#{usercode}) AND du.is_deleted='N'
        and du.payroll_date = #{payrollDate}
    </select>
    <select id="queryUploadInfo" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailUploadEo">
        SELECT DISTINCT t1.employee_no ,t1.employee_name  FROM hrms_newsalary_payroll_detail_upload du
        LEFT JOIN (
             SELECT
                employee_name,employee_no,org_id,employee_id,is_deleted
            FROM cust_emp_base
            UNION ALL
            SELECT
                employee_name,employee_no,org_id,id as employee_id,is_deleted
            FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
        ) t1  on du.employee_no = t1.employee_no AND t1.is_deleted='N'
        where du.payroll_date = #{payrollDate} and du.option_id = #{optionId} and du.is_examine in (0,1) and du.is_deleted='N'
    </select>
    <select id="getItemList" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailUploadEo">
        SELECT DISTINCT item_id as itemId,item_name as itemName from hrms_newsalary_payroll_detail_upload where payroll_date = #{payrollDate}
        and is_deleted='N'
    </select>

</mapper>

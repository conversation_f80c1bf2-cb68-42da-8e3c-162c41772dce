package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalarySecondstepTitleHistory;

/**
 * @ClassName HrmsNewsalarySecondstepTitleHistoryService
 * @Description TODO
 * @date 2024��3��11�� ����5:38:28
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalarySecondstepTitleHistoryService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��3��11�� ����5:38:28
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalarySecondstepTitleHistory record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��3��11�� ����5:38:28
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalarySecondstepTitleHistory record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��3��11�� ����5:38:28
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalarySecondstepTitleHistory
	 * @date 2024��3��11�� ����5:38:28
	 * <AUTHOR>
	 */
	HrmsNewsalarySecondstepTitleHistory selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalarySecondstepTitleHistory>
	 * @date 2024��3��11�� ����5:38:28
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalarySecondstepTitleHistory> getDataSetList(Page page, HrmsNewsalarySecondstepTitleHistory record);

	//删除历史数据
	Integer deleteByOptionIdAndDate(String optionId, String computeDate);

	HrmsNewsalarySecondstepTitleHistory getData(String optionId, String computeDate);
}

package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.enums.EmployeeTemporaryOpTypeEnum;
import cn.trasen.hrms.salary.model.HrmsEmployeeTemporary;
import cn.trasen.hrms.salary.model.HrmsEmployeeTemporaryChange;

/**
 * @ClassName HrmsEmployeeTemporaryChangeService
 * @Description TODO
 * @date 2024��10��8�� ����3:12:57
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsEmployeeTemporaryChangeService {

	/**
	 * @Title saveChanges
	 * @Description 保存操作记录
	 * @param oldObj 原数据
	 * @param newObj 新数据
	 * @param opTypeEnum 操作类型
	 * @return Integer
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	Integer saveChanges(HrmsEmployeeTemporary oldObj,HrmsEmployeeTemporary newObj,EmployeeTemporaryOpTypeEnum opTypeEnum);

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	Integer save(HrmsEmployeeTemporaryChange record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	Integer update(HrmsEmployeeTemporaryChange record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsEmployeeTemporaryChange
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	HrmsEmployeeTemporaryChange selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsEmployeeTemporaryChange>
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	DataSet<HrmsEmployeeTemporaryChange> getDataSetList(Page page, HrmsEmployeeTemporaryChange record);
}

package cn.trasen.hrms.salary.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 手工导入工资表
 *
 */
@Table(name = "hrms_newsalary_payroll_detail_import")
@Setter
@Getter
public class HrmsNewsalaryPayrollDetailImport {
    /**
     * 发放明细id
     */
    @Id
    @ApiModelProperty(value = "发放明细id")
    private String id;

    /**
     * 人员id
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "人员id")
    private String employeeNo;

    /**
     * 薪酬醒目id
     */
    @Column(name = "item_id")
    @ApiModelProperty(value = "薪酬醒目id")
    private String itemId;

    /**
     * 薪酬醒目id
     */
    @Column(name = "option_id")
    @ApiModelProperty(value = "薪酬醒目id")
    private String optionId;

    /**
     * 薪酬项目code
     */
    @Column(name = "item_code")
    @ApiModelProperty(value = "薪酬项目code")
    private String itemCode;

    /**
     * 薪酬项目名称
     */
    @Column(name = "item_name")
    @ApiModelProperty(value = "薪酬项目名称")
    private String itemName;

    /**
     * 工资金额
     */
    @ApiModelProperty(value = "工资金额")
    private BigDecimal salary;

    /**
     * 导入月份
     */
    @Column(name = "import_date")
    @ApiModelProperty(value = "导入月份")
    private String importDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
}
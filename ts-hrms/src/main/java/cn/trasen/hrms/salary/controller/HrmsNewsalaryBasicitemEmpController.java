package cn.trasen.hrms.salary.controller;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.hrms.salary.DTO.NewSalaryBasicItemBatchAdjustReq;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.model.HrmsPersonnelChange;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryBasicitemEmpMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicColumn;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicitemEmpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalaryBasicitemEmpController
 * @Description 人员定薪
 * @date 2023��11��11�� ����4:32:36
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "人员定薪Controller")
public class HrmsNewsalaryBasicitemEmpController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryBasicitemEmpController.class);

	@Autowired
	private HrmsNewsalaryBasicitemEmpService hrmsNewsalaryBasicitemEmpService;

	@Resource
	private HrmsNewsalaryBasicitemEmpMapper mapper;
	/**
	 * @Title saveHrmsNewsalaryBasicitemEmp
	 * @Description 新增
	 * @param records
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:32:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "定薪保存", notes = "定薪保存")
	@PostMapping("/api/salaryBasicItemEmp/save")
	public PlatformResult<String> saveHrmsNewsalaryBasicitemEmp(@RequestBody List<HrmsNewsalaryBasicitemEmp> records) {
		try {
			hrmsNewsalaryBasicitemEmpService.save(records);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "调薪保存", notes = "调薪保存")
	@PostMapping("/api/salaryBasicItemEmp/adjustSave")
	public PlatformResult<String> adjustSave(@RequestBody List<HrmsNewsalaryBasicitemEmp> records) {
		try {
			hrmsNewsalaryBasicitemEmpService.adjustSave(records);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "批量调薪", notes = "批量调薪")
	@PostMapping("/api/salaryBasicColumn/batchAdjustSave")
	public PlatformResult<String> batchAdjustSave(@RequestBody NewSalaryBasicItemBatchAdjustReq record) {
		if(record == null || StringUtils.isBlank(record.getEffectiveDate())){
			return PlatformResult.failure("生效时间不能为空");
		}
		if(CollUtil.isEmpty(record.getEmployeeIds())){
			return PlatformResult.failure("批量调薪的员工不能为空");
		}
		if(StringUtils.isEmpty(record.getPolicyStandardId()) && CollUtil.isEmpty(record.getBasicColumnsList())){
			return PlatformResult.failure("批量调薪项不能为空");
		}
		try {
			String msg = hrmsNewsalaryBasicitemEmpService.batchAdjustSave(record);
			if(StringUtils.isNotBlank(msg)){
                return PlatformResult.failure("批量调薪失败，其中："+msg);
            }
            return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@GetMapping("/api/salary/singcalc/{optionId}")
	public PlatformResult<String>callAllSingcalc(@PathVariable String optionId){
		List<String> dd = mapper.getEmpIds();
		for (String empId : dd) {
			hrmsNewsalaryBasicitemEmpService.CalculatSingle(empId);
		}
		return PlatformResult.success();
	}
	//导出定薪调薪模版
	@GetMapping("/api/salaryBasicItemEmp/export")
	public void projectInfoExport(Page page, HttpServletRequest request, HttpServletResponse response, HrmsPersonnelChange hrmsPersonnelChange) {
		// 导出文件名称
		String name = "人员定薪调薪模版.xlsx";
		// 模板位置
		String templateUrl = "template/hrmsTransfer.xls";
		// 导出数据列表
		try {
			//查询出定薪调薪的列

		//	List<HrmsPersonnelChange> list = hrmsPersonnelChangeService.getList(hrmsPersonnelChange);
			// 调用导出方法
	//		ExportUtil.export(request, response, list, name, templateUrl);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}

	}



	/**
	 * @Title updateHrmsNewsalaryBasicitemEmp
	 * @Description 编辑
	 * @param records
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:32:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryBasicItemEmp/update")
	public PlatformResult<String> updateHrmsNewsalaryBasicitemEmp(@RequestBody List<HrmsNewsalaryBasicitemEmp> records) {
		try {
			hrmsNewsalaryBasicitemEmpService.update(records);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "根据员工id查询定薪信息", notes = "根据员工id查询定薪信息")
	@GetMapping("/api/salaryBasicItemEmp/getDataByEmployeeId/{employeeId}")
	public PlatformResult<List<HrmsNewsalaryBasicitemEmp>> getDataEmployeeId(@PathVariable String employeeId) {
		try {
			List<HrmsNewsalaryBasicitemEmp> list = hrmsNewsalaryBasicitemEmpService.getDataByEmployeeId(employeeId);
			return PlatformResult.success(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryBasicitemEmpById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryBasicitemEmp>
	 * @date 2023��11��11�� ����4:32:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/salaryBasicItemEmp/{id}")
	public PlatformResult<HrmsNewsalaryBasicitemEmp> selectHrmsNewsalaryBasicitemEmpById(@PathVariable String id) {
		try {
			HrmsNewsalaryBasicitemEmp record = hrmsNewsalaryBasicitemEmpService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryBasicitemEmpById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:32:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/salaryBasicItemEmp/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryBasicitemEmpById(@PathVariable String id) {
		try {
			hrmsNewsalaryBasicitemEmpService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryBasicitemEmpList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryBasicitemEmp>
	 * @date 2023��11��11�� ����4:32:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/salaryBasicItemEmp/list")
	public DataSet<HrmsNewsalaryBasicitemEmp> selectHrmsNewsalaryBasicitemEmpList(Page page, HrmsNewsalaryBasicitemEmp record) {
		return hrmsNewsalaryBasicitemEmpService.getDataSetList(page, record);
	}

	@ApiOperation(value = "定薪页面进来查询", notes = "定薪页面进来查询")
	@GetMapping("/api/salaryBasicItemEmp/getAllData")
	public PlatformResult<List<HrmsNewsalaryBasicColumn>> getAllData(HrmsNewsalaryBasicColumn record) {
		try {
			List<HrmsNewsalaryBasicColumn> list = hrmsNewsalaryBasicitemEmpService.getAllData(record);
			return PlatformResult.success(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "薪酬档案上面的个人信息", notes = "薪酬档案上面的个人信息")
	@GetMapping("/api/salaryBasicItemEmp/getBaseData/{id}")
	public PlatformResult<Map<String,Object>> getBaseData(@PathVariable String id) {
		try {
			Map<String,Object> data = hrmsNewsalaryBasicitemEmpService.getBaseData(id);
			return PlatformResult.success(data);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}

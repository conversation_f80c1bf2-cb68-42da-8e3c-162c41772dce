package cn.trasen.hrms.salary.controller;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmpHistory;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicitemEmpHistoryService;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalaryBasicitemEmpHistoryController
 * @Description 人员定薪历史表
 * @date 2023��11��11�� ����4:33:03
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "人员定薪历史Controller")
public class HrmsNewsalaryBasicitemEmpHistoryController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryBasicitemEmpHistoryController.class);

	@Autowired
	private HrmsNewsalaryBasicitemEmpHistoryService hrmsNewsalaryBasicitemEmpHistoryService;



	/**
	 * @Title updateHrmsNewsalaryBasicitemEmpHistory
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:33:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryBasicItemEmpHistory/update")
	public PlatformResult<String> updateHrmsNewsalaryBasicitemEmpHistory(@RequestBody HrmsNewsalaryBasicitemEmpHistory record) {
		try {
			hrmsNewsalaryBasicitemEmpHistoryService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryBasicItemEmpHistory/updateDate")
	public PlatformResult<String> updateDate(@RequestBody List<HrmsNewsalaryBasicitemEmpHistory> records) {
		try {
			hrmsNewsalaryBasicitemEmpHistoryService.updateDate(records);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryBasicitemEmpHistoryById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryBasicitemEmpHistory>
	 * @date 2023��11��11�� ����4:33:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/salaryBasicItemEmpHistory/{id}")
	public PlatformResult<HrmsNewsalaryBasicitemEmpHistory> selectHrmsNewsalaryBasicitemEmpHistoryById(@PathVariable String id) {
		try {
			HrmsNewsalaryBasicitemEmpHistory record = hrmsNewsalaryBasicitemEmpHistoryService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "根据人员id和批次号查询记录", notes = "根据人员id和批次号查询记录")
	@GetMapping("/api/salaryBasicItemEmpHistory/getEmployeeIdAndNo")
	public PlatformResult<List<HrmsNewsalaryBasicitemEmpHistory>> getEmployeeIdAndNo(String employeeId, String no) {
		try {
			List<HrmsNewsalaryBasicitemEmpHistory> records = hrmsNewsalaryBasicitemEmpHistoryService.getEmployeeIdAndNo(employeeId,no);
			return PlatformResult.success(records);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "根据人员id和批次号查询记录", notes = "根据人员id和批次号查询记录")
	@GetMapping("/api/salaryBasicItemEmpHistory/getEmployeeIdAndEffectiveDate")
	public PlatformResult<List<HrmsNewsalaryBasicitemEmpHistory>> getEmployeeIdAndEffectiveDate(String employeeId, String effectiveDate) {
		try {
			List<HrmsNewsalaryBasicitemEmpHistory> records = hrmsNewsalaryBasicitemEmpHistoryService.getEmployeeIdAndEffectiveDate(employeeId,effectiveDate);
			return PlatformResult.success(records);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryBasicitemEmpHistoryById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:33:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/salaryBasicItemEmpHistory/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryBasicitemEmpHistoryById(@PathVariable String id) {
		try {
			hrmsNewsalaryBasicitemEmpHistoryService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "撤销调薪", notes = "撤销调薪")
	@PostMapping("/api/salaryBasicItemEmpHistory/cancel")
	public PlatformResult<String> cancel(@RequestBody HrmsNewsalaryBasicitemEmpHistory record) {
		try {
			hrmsNewsalaryBasicitemEmpHistoryService.cancel(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}


	@ApiOperation(value = "查询调薪记录表头", notes = "查询调薪记录表头")
	@GetMapping("/api/salaryBasicItemEmpHistory/headlist/{employeeId}")
	public PlatformResult<List<VueTableEntity>>  headlist(@PathVariable String employeeId) {
		try {
			List<VueTableEntity> list = hrmsNewsalaryBasicitemEmpHistoryService.headlist(employeeId);
			return PlatformResult.success(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}

	}

	/**
	 * @Title selectHrmsNewsalaryBasicitemEmpHistoryList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryBasicitemEmpHistory>
	 * @date 2023��11��11�� ����4:33:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询调薪记录数据", notes = "查询调薪记录数据")
	@GetMapping("/api/salaryBasicItemEmpHistory/list")
	public PlatformResult<List<Map<String,Object>>>  selectHrmsNewsalaryBasicitemEmpHistoryList(HrmsNewsalaryBasicitemEmpHistory record) {
		try {
			List<Map<String,Object>> list  = hrmsNewsalaryBasicitemEmpHistoryService.getDataSetList(record);
			return PlatformResult.success(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}

	}

	//查询当前定薪数据
	@ApiOperation(value = "查询当前定薪数据", notes = "查询当前定薪数据")
	@GetMapping("/api/salaryBasicItemEmpHistory/currentSalary")
	public PlatformResult<Map<String,Object>>  currentSalary(HrmsNewsalaryBasicitemEmpHistory record) {
		try {
			Map<String,Object> map  = hrmsNewsalaryBasicitemEmpHistoryService.currentSalary(record);
			return PlatformResult.success(map);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}

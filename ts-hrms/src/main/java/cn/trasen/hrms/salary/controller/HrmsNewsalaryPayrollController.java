package cn.trasen.hrms.salary.controller;

import cn.trasen.hrms.salary.utils.VueTableEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryPayrollVo;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayroll;
import cn.trasen.hrms.salary.service.HrmsNewsalaryPayrollService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.util.Map;

/**
 * @ClassName HrmsNewsalaryPayrollController
 * @Description 发放记录表
 * @date 2023��11��11�� ����4:37:44
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "发放记录Controller")
public class HrmsNewsalaryPayrollController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryPayrollController.class);

	@Autowired
	private HrmsNewsalaryPayrollService hrmsNewsalaryPayrollService;

	/**
	 * @Title saveHrmsNewsalaryPayroll
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:37:44
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/salaryPayroll/save")
	public PlatformResult<String> saveHrmsNewsalaryPayroll(@RequestBody HrmsNewsalaryPayroll record) {
		try {
			hrmsNewsalaryPayrollService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryPayroll
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:37:44
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryPayroll/update")
	public PlatformResult<String> updateHrmsNewsalaryPayroll(@RequestBody HrmsNewsalaryPayroll record) {
		try {
			hrmsNewsalaryPayrollService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryPayrollById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryPayroll>
	 * @date 2023��11��11�� ����4:37:44
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/salaryPayroll/{id}")
	public PlatformResult<HrmsNewsalaryPayroll> selectHrmsNewsalaryPayrollById(@PathVariable String id) {
		try {
			HrmsNewsalaryPayroll record = hrmsNewsalaryPayrollService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryPayrollById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:37:44
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/salaryPayroll/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryPayrollById(@PathVariable String id) {
		try {
			hrmsNewsalaryPayrollService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryPayrollList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryPayroll>
	 * @date 2023��11��11�� ����4:37:44
	 * <AUTHOR>
	 */
	@ApiOperation(value = "薪酬组发送记录", notes = "薪酬组发送记录")
	@GetMapping("/api/salaryPayroll/list")
	public DataSet<HrmsNewsalaryPayroll> selectHrmsNewsalaryPayrollList(Page page, HrmsNewsalaryPayroll record) {
		return hrmsNewsalaryPayrollService.getDataSetList(page, record);
	}

	@ApiOperation(value = "档案详情月度工资记录表头", notes = "档案详情月度工资记录表头")
	@GetMapping("/api/salaryPayroll/getSalaryMonthRecordHeadList/{employeeId}")
	public PlatformResult<List<VueTableEntity>> getSalaryMonthRecordHeadList(@PathVariable String employeeId){
		List<VueTableEntity> list = hrmsNewsalaryPayrollService.getSalaryMonthRecordHeadList(employeeId);
		return PlatformResult.success(list);
	}
	
	
	@ApiOperation(value = "工资发放列表", notes = "工资发放列表")
	@GetMapping("/api/salaryPayroll/selectNewsalaryPayrollList")
	public DataSet<HrmsNewsalaryPayrollVo> selectNewsalaryPayrollList(Page page, HrmsNewsalaryPayrollVo record) {
		return hrmsNewsalaryPayrollService.selectNewsalaryPayrollList(page, record);
	}
	
//	@ApiOperation(value = "发送工资条页面汇总", notes = "发送工资条页面汇总")
//	@GetMapping("/api/salaryPayroll/sendSalarySummaryData")
//	public PlatformResult<Map<String,Object>> sendSalarySummaryData(HrmsNewsalaryPayrollVo record) {
//		try {
//			return PlatformResult.success(hrmsNewsalaryPayrollService.summaryData(record));
//		} catch (Exception e) {
//			logger.error(e.getMessage(), e);
//			return PlatformResult.failure(e.getMessage());
//		}
//	}

	@ApiOperation(value = "发送工资条页面汇总(新版)", notes = "发送工资条页面汇总(新版)")
	@GetMapping("/api/salaryPayroll/sendSalarySummaryData")
	public PlatformResult<List<Map<String,Object>>> sendSalarySummaryData(HrmsNewsalaryPayrollVo record) {
		try {
			return PlatformResult.success(hrmsNewsalaryPayrollService.summaryData(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "发放详情页面汇总", notes = "发放详情页面汇总")
	@GetMapping("/api/salaryPayroll/sendSalaryDeatilsSummaryData")
	public PlatformResult<Map<String,Object>> sendSalaryDeatilsSummaryData(HrmsNewsalaryPayrollVo record) {
		try {
			return PlatformResult.success(hrmsNewsalaryPayrollService.sendSalaryDeatilsSummaryData(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}

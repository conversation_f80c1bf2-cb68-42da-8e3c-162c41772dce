package cn.trasen.hrms.salary.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 薪酬项目提醒设置表
 *
 */
@Table(name = "hrms_newsalary_item_remind_setting")
@Setter
@Getter
public class HrmsNewsalaryItemRemindSetting {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 薪酬提醒ID
     */
    @Column(name = "remind_id")
    @ApiModelProperty(value = "薪酬提醒ID")
    private String remindId;

    /**
     * 薪酬项目ID
     */
    @Column(name = "item_id")
    @ApiModelProperty(value = "薪酬项目ID")
    private String itemId;

    /**
     * 薪酬项目名称
     */
    @Transient
    @ApiModelProperty(value = "薪酬项目名称")
    private String itemName;

    /**
     * 是否补缴 1-是 0-否
     */
    @Column(name = "is_back_payment")
    @ApiModelProperty(value = "是否补缴 1-是 0-否")
    private String isBackPayment;

    /**
     * 补缴开始月份
     */
    @Column(name = "back_payment_date")
    @ApiModelProperty(value = "补缴开始月份")
    private String backPaymentDate;

    /**
     * 是否启用  1正常  0停用
     */
    @Column(name = "is_enabled")
    @ApiModelProperty(value = "是否启用  1正常  0停用")
    private String isEnabled;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    @Transient
    @ApiModelProperty(value = "加减项 1加项 2减项3不参与计算")
    private String countType;

    @Transient
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    @Transient
    @ApiModelProperty(value = "薪酬项金额")
    private String salary;
}
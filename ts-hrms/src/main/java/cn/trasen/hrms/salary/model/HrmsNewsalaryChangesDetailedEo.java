package cn.trasen.hrms.salary.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 员工异动明细表
 * <AUTHOR>
 * @TableName hrms_newsalary_changes_detailed
 */
@Table(name = "hrms_newsalary_changes_detailed")
@Setter
@Getter
public class HrmsNewsalaryChangesDetailedEo {
    /**
     * 主键ID
     */
    @Id
    private String id;

    /**
     * 员工工号
     */
    private String employeeNo;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 薪酬组
     */
    @Transient
    private String optionName;

    /**
     * 调整之前的值
     */
    private String adjustValue;

    /**
     * 现有值
     */
    private String nowValue;

    /**
     * 异动项
     */
    private String abnormalItems;

    /**
     * 薪酬类别 (1.岗位类别2.薪级类别3.薪酬项目4.员工状态)
     */
    private Integer salaryCategory;

    /**
     * 编制类型
     */
    private String establishmentType;

    /**
     * 岗位名称
     */
    private String personalIdentity;

    /**
     * 科室
     */
    @Transient
    private String orgName;

    /**
     * 员工名字
     */
    private String employeeName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建者ID
     */
    private String createUser;

    /**
     * 创建者姓名
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新者ID
     */
    private String updateUser;

    /**
     * 更新者姓名
     */
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    private String isDeleted;

    /**
     * 
     */
    private String ssoOrgCode;

    /**
     * 
     */
    private String ssoOrgName;

    /**
     * 薪酬类别 (1.岗位类别2.薪级类别3.薪酬项目4.员工状态)
     */
    @Transient
    @ApiModelProperty(value = "薪酬类别文本")
    private String salaryCategoryText;

    @Transient
    @ApiModelProperty(value = "编制类型文本")
    private String establishmentTypeText;

    @Transient
    @ApiModelProperty(value = "薪酬名称")
    private String  salaryName;

    /**
     * 生效日期
     */
    private String effectiveDate;

    /**
     * 变动原因
     */
    private String reason;

    /**
     * 0.档案修改 1.晋升修改 2.薪酬修改
     */
    private Integer sourceType;

    @Transient
    private String sourceTypeText;


    private static final long serialVersionUID = 1L;
}
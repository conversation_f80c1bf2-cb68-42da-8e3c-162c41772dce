/************************************************
* @功能描述: TODO
* @Title: SchEmpSalary.java
* @Package cn.trasen.hrms.salary.DTO
* <AUTHOR>
* @date 2024年6月21日 下午5:39:17
* @version V1.0
*************************************************/
package cn.trasen.hrms.salary.DTO;

import java.util.List;

import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicColumn;
import lombok.Data;

/**
* @ClassName: SchEmpSalary
* @Description: TODO 
* <AUTHOR>
* @date 2024年6月21日 下午5:39:17
*
*/
@Data
public class SchEmpSalary {

    private List<HrmsNewsalaryBasicColumn> basicItemIds; //字段列表
    private String beginDate;
    private String endDate;
    private String employeeName;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryReportMapMapper">

    <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryReportMapEo">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="itemId" column="item_id" jdbcType="VARCHAR"/>
            <result property="optionId" column="option_id" jdbcType="VARCHAR"/>
            <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="is_deleted" jdbcType="CHAR"/>
            <result property="ssoOrgCode" column="sso_org_code" jdbcType="VARCHAR"/>
            <result property="ssoOrgName" column="sso_org_name" jdbcType="VARCHAR"/>
            <result property="itemRule" column="item_rule" jdbcType="CHAR"/>
            <result property="countFormula" column="count_formula" jdbcType="VARCHAR"/>
            <result property="countFormulaText" column="count_formula_text" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,item_id,option_id,col_id,col_name,col_code,
        item_name,create_date,
        create_user,create_user_name,update_date,
        update_user,update_user_name,is_deleted,
        sso_org_code,sso_org_name,item_rule,count_formula,count_formula_text
    </sql>

    <select id="getDataSetList" resultType="cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportMapVo">
        select
        <include refid="Base_Column_List" />
        from hrms_newsalary_report_map where
        is_deleted = 'N'
        <if test="reportMapVo.colName != null and reportMapVo.colName != ''">
            and col_name like CONCAT('%', #{reportMapVo.colName}, '%')
        </if>
    </select>
    <select id="queryItem" resultType="java.lang.String">
        SELECT DISTINCT m.item_id FROM hrms_newsalary_report_total t left join hrms_newsalary_report_map m on m.col_id = t.id
        WHERE t.is_deleted='N' and m.is_deleted='N' and m.item_id is not null and m.item_id != ''
        and t.report_id =#{reportId}
    </select>
    <select id="queryCountFormulaItem" resultType="map">
        SELECT DISTINCT m.option_id,m.col_code,m.count_formula FROM hrms_newsalary_report_total t left join hrms_newsalary_report_map m on m.col_id = t.id
        WHERE t.is_deleted='N' and m.is_deleted='N'
        and t.report_id =#{reportId} and m.count_formula is not null and m.count_formula != ''
    </select>

</mapper>

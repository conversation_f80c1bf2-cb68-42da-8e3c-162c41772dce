package cn.trasen.hrms.salary.service.impl;

import java.util.Date;
import java.util.List;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryLevelRemindSettingMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryLevelRemindSetting;
import cn.trasen.hrms.salary.service.HrmsNewsalaryLevelRemindSettingService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @ClassName HrmsNewsalaryLevelRemindSettingServiceImpl
 * @Description TODO
 * @date 2024��11��6�� ����10:32:10
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryLevelRemindSettingServiceImpl implements HrmsNewsalaryLevelRemindSettingService {

	@Resource
	private HrmsNewsalaryLevelRemindSettingMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryLevelRemindSetting record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		record.setIsEnabled("1");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryLevelRemindSetting record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryLevelRemindSetting record = new HrmsNewsalaryLevelRemindSetting();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteByRemindId(String remindId) {
		Assert.hasText(remindId, "提醒设置ID不能为空.");
		HrmsNewsalaryLevelRemindSetting record = new HrmsNewsalaryLevelRemindSetting();
		record.setRemindId(remindId);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		Example example = new Example(HrmsNewsalaryLevelRemindSetting.class);
		Example.Criteria criteria = example.createCriteria();
		if(record!= null && record.getRemindId()!=null){
			criteria.andEqualTo("remindId",record.getRemindId());
		}
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return mapper.updateByExampleSelective(record,example);
	}

	@Override
	public HrmsNewsalaryLevelRemindSetting selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalaryLevelRemindSetting> getDataSetList(Page page, HrmsNewsalaryLevelRemindSetting record) {
		Example example = new Example(HrmsNewsalaryLevelRemindSetting.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalaryLevelRemindSetting> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	/**
	 * @Title getList
	 * @Description 查询薪级等级设置列表
	 * @param record
	 * @return DataSet<HrmsNewsalaryItemRemindSetting>
	 * @date 2024��10��29�� ����9:55:31
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsNewsalaryLevelRemindSetting> getList(HrmsNewsalaryLevelRemindSetting record){
		Example example = new Example(HrmsNewsalaryLevelRemindSetting.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StringUtils.isNotBlank(record.getRemindId())) {
			criteria.andEqualTo("remindId", record.getRemindId());
		}
		if(StringUtils.isNotBlank(record.getIsEnabled())) {
			criteria.andEqualTo("isEnabled", record.getIsEnabled());
		}
		if(StringUtils.isNotBlank(record.getEmployeeStatus())) {
			criteria.andEqualTo("employeeStatus", record.getEmployeeStatus());
		}
		if(StringUtils.isNotBlank(record.getEstablishmentType())) {
			criteria.andEqualTo("establishmentType", record.getEstablishmentType());
		}
		return mapper.selectByExample(example);
	}
}

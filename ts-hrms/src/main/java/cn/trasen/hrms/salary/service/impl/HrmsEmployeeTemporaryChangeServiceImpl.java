package cn.trasen.hrms.salary.service.impl;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.hutool.core.bean.BeanUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.salary.enums.EmployeeTemporaryOpTypeEnum;
import cn.trasen.hrms.salary.model.HrmsEmployeeTemporary;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.hrms.salary.dao.HrmsEmployeeTemporaryChangeMapper;
import cn.trasen.hrms.salary.model.HrmsEmployeeTemporaryChange;
import cn.trasen.hrms.salary.service.HrmsEmployeeTemporaryChangeService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.persistence.Transient;

/**
 * @ClassName HrmsEmployeeTemporaryChangeServiceImpl
 * @Description TODO
 * @date 2024��10��8�� ����3:12:57
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsEmployeeTemporaryChangeServiceImpl implements HrmsEmployeeTemporaryChangeService {

	@Resource
	private HrmsEmployeeTemporaryChangeMapper mapper;

	@Resource
	DictItemFeignService dictItemFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer saveChanges(HrmsEmployeeTemporary oldObj, HrmsEmployeeTemporary newObj,EmployeeTemporaryOpTypeEnum opTypeEnum){
		if(opTypeEnum.getKey().equals(EmployeeTemporaryOpTypeEnum.ADD.getKey())
				|| opTypeEnum.getKey().equals(EmployeeTemporaryOpTypeEnum.BATCHENTRY.getKey())
				|| opTypeEnum.getKey().equals(EmployeeTemporaryOpTypeEnum.EXPORT.getKey())){
			HrmsEmployeeTemporaryChange record = new HrmsEmployeeTemporaryChange();
			record.setOpType(opTypeEnum.getKey());
			record.setTmpEmployeeId(newObj.getId());
			record.setTmpEmployeeNo(newObj.getEmployeeNo());
			record.setTmpEmployeeName(newObj.getEmployeeName());
			return save(record);
		}else {
			//对比两个对象的属性差值，保存操作
			Map<String, Object> oldMap = BeanUtil.beanToMap(oldObj);
			Map<String, Object> newMap = BeanUtil.beanToMap(newObj);
			Field[] fields = HrmsEmployeeTemporary.class.getDeclaredFields();
			StringBuffer beforeValues = new StringBuffer();
			StringBuffer afterValues = new StringBuffer();
			Map<String, String> genderDictMap = convertDictMap("SEX"); // 岗位名称
			Map<String, String> educationConvertDictMap = convertDictMap("education_type");  //学历
			Map<String, String> positionConvertDictMap = convertDictMap("personal_identity");  //岗位名称
			Map<String, String> establishmentConvertDictMap = convertDictMap("establishment_type");  //编制类型

			String oldValue = "";
			String newalue = "";
			for (Field f: fields){
				if("createDate".equals(f.getName())
						||"createUser".equals(f.getName())
						||"createUserName".equals(f.getName())
						||"updateDate".equals(f.getName())
						||"updateUser".equals(f.getName())
						||"updateUserName".equals(f.getName())
						|| f.getAnnotatedType().equals(Transient.class)){
					continue;
				}
				if("gender".equals(f.getName())){
					if(oldMap.containsKey(f.getName())  && oldMap.get(f.getName()) !=null) {
						oldValue = genderDictMap.get(oldMap.get(f.getName()).toString());
					}
					if(newMap.containsKey(f.getName())&& newMap.get(f.getName()) !=null) {
						newalue = genderDictMap.get(newMap.get(f.getName()).toString());
					}
				}else if("tmpEducation".equals(f.getName())){
					if(oldMap.containsKey(f.getName())&& oldMap.get(f.getName()) !=null) {
						oldValue = educationConvertDictMap.get(oldMap.get(f.getName()).toString());
					}
					if(newMap.containsKey(f.getName())&& newMap.get(f.getName()) !=null) {
						newalue = educationConvertDictMap.get(newMap.get(f.getName()).toString());
					}
				}else if("tmpPosition".equals(f.getName())){
					if(oldMap.containsKey(f.getName())&& oldMap.get(f.getName()) !=null) {
						oldValue = positionConvertDictMap.get(oldMap.get(f.getName()).toString());
					}
					if(newMap.containsKey(f.getName())&& newMap.get(f.getName()) !=null) {
						newalue = positionConvertDictMap.get(newMap.get(f.getName()).toString());
					}
				}else if("tmpEstablishment".equals(f.getName())){
					if(oldMap.containsKey(f.getName())&& oldMap.get(f.getName()) !=null) {
						oldValue = establishmentConvertDictMap.get(oldMap.get(f.getName()).toString());
					}
					if(newMap.containsKey(f.getName())&& newMap.get(f.getName()) !=null) {
						newalue = establishmentConvertDictMap.get(newMap.get(f.getName()).toString());
					}
				}else if("tmpEmployeeStatus".equals(f.getName())){
					if(oldMap.containsKey(f.getName())&& oldMap.get(f.getName()) !=null) {
						oldValue = "1".equals(oldMap.get(f.getName()).toString()) ? "在职" : "离职";
					}
					if(newMap.containsKey(f.getName())&& newMap.get(f.getName()) !=null) {
						newalue = "1".equals(newMap.get(f.getName()).toString()) ? "在职" : "离职";
					}
				}else if("isSyncSalary".equals(f.getName())){
					if(oldMap.containsKey(f.getName())&& oldMap.get(f.getName()) !=null) {
						oldValue = "Y".equals(oldMap.get(f.getName()).toString()) ? "已同步" : "未同步";
					}
					if(newMap.containsKey(f.getName())&& newMap.get(f.getName()) !=null) {
						newalue = "Y".equals(newMap.get(f.getName()).toString()) ? "已同步" : "未同步";
					}
				}else{
					if(oldMap.containsKey(f.getName())&& oldMap.get(f.getName()) !=null) {
						oldValue =oldMap.get(f.getName()).toString();
					}
					if(newMap.containsKey(f.getName()) && newMap.get(f.getName()) !=null) {
						newalue = newMap.get(f.getName()).toString();
					}
				}

				if(oldMap.containsKey(f.getName()) && oldMap.get(f.getName()) != null && !newMap.containsKey(f.getName()) ){
					beforeValues.append(f.getAnnotation(ApiModelProperty.class).value()).append(":").append(oldValue).append(";  ");
					afterValues.append(f.getAnnotation(ApiModelProperty.class).value()).append(":").append("空 ;   ");
				}else if(!oldMap.containsKey(f.getName()) && newMap.containsKey(f.getName()) && newMap.get(f.getName()) != null){
					beforeValues.append(f.getAnnotation(ApiModelProperty.class).value()).append(":").append("空 ;  ");
					afterValues.append(f.getAnnotation(ApiModelProperty.class).value()).append(":").append(newalue).append(";   ");
				}else if(oldMap.containsKey(f.getName()) && newMap.containsKey(f.getName())
						&& oldMap.get(f.getName()) != null && newMap.get(f.getName()) != null
						&& !oldMap.get(f.getName()).equals(newMap.get(f.getName()))){
					 beforeValues.append(f.getAnnotation(ApiModelProperty.class).value()).append(":").append(oldValue).append(";  ");
					 afterValues.append(f.getAnnotation(ApiModelProperty.class).value()).append(":").append(newalue).append(";   ");
				 }
			}

			HrmsEmployeeTemporaryChange record = new HrmsEmployeeTemporaryChange();
			record.setOpType(opTypeEnum.getKey());
			record.setTmpEmployeeId(newObj.getId());
			record.setTmpEmployeeNo(newObj.getEmployeeNo());
			record.setTmpEmployeeName(newObj.getEmployeeName());
			record.setOpBeforeValue(beforeValues.toString());
			record.setOpAfterValue(afterValues.toString());
			return save(record);
		}
	}

	private Map<String, String> convertDictMap(String dictType) {
		Assert.notNull(dictType, "dictType must not be null.");
		Map<String, String> map = Maps.newHashMap();
		List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
		if (CollectionUtils.isNotEmpty(dictItemList)) {
			for (DictItemResp d : dictItemList) {
				map.put(d.getItemNameValue(), d.getItemName());
			}
		}
		return map;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsEmployeeTemporaryChange record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsEmployeeTemporaryChange record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsEmployeeTemporaryChange record = new HrmsEmployeeTemporaryChange();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsEmployeeTemporaryChange selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsEmployeeTemporaryChange> getDataSetList(Page page, HrmsEmployeeTemporaryChange record) {
		Example example = new Example(HrmsEmployeeTemporaryChange.class);
		Example.Criteria criteria = example.createCriteria();
		Example.Criteria employeeCriteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(record!=null){
			if(StringUtils.isNotBlank(record.getTmpEmployeeNo())){
				employeeCriteria.andLike("tmpEmployeeNo","%"+record.getTmpEmployeeNo()+"%").orLike("tmpEmployeeName","%"+record.getTmpEmployeeNo()+"%");
			}
			if(StringUtils.isNotBlank(record.getSearchStartTime())){
				criteria.andCondition("DATE_FORMAT(create_date, '%Y-%m-%d') >= DATE_FORMAT('"+record.getSearchStartTime()+"','%Y-%m-%d')");
			}
			if(StringUtils.isNotBlank(record.getSearchEndTime())){
				criteria.andCondition("DATE_FORMAT(create_date, '%Y-%m-%d') <= DATE_FORMAT('"+record.getSearchEndTime()+"','%Y-%m-%d')");
			}
			if(StringUtils.isNotBlank(record.getOpType())){
				criteria.andEqualTo("opType",record.getOpType());
			}
			example.and(employeeCriteria);
		}
		example.setOrderByClause("create_date desc");
		List<HrmsEmployeeTemporaryChange> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

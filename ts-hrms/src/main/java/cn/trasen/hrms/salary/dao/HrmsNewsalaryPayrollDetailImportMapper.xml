<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryPayrollDetailImportMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailImport">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="item_id" jdbcType="VARCHAR" property="itemId" />
    <result column="option_id" jdbcType="VARCHAR" property="optionId" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="salary" jdbcType="DECIMAL" property="salary" />
    <result column="import_date" jdbcType="VARCHAR" property="importDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>

    <select id="exportTemplateAll" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
      SELECT t2.id,t2.item_name,t2.item_rule FROM hrms_newsalary_item_group t1
      LEFT JOIN hrms_newsalary_item  t2 ON t1.id=t2.group_id
      LEFT JOIN hrms_newsalary_option hno ON t2.option_id=hno.id
      WHERE t1.is_deleted='N' AND t2.is_deleted='N'
        AND hno.is_enable = '1' and t2.is_enable = '1'
        <if test="optionId != null and optionId !=''">
            and hno.id = #{optionId}
        </if>
        AND t2.item_rule ='1'
      GROUP BY t2.item_name
      ORDER BY t1.seq_no , t2.create_date
    </select>
  <select id="exportSalaryItemByOptionId" parameterType="java.lang.String" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
       SELECT t2.item_id,t3.item_name
      FROM hrms_newsalary_payroll t1
      LEFT JOIN hrms_newsalary_payroll_detail t2 ON t1.id=t2.payroll_id
      LEFT JOIN hrms_newsalary_item t3 ON t2.item_id = t3.id
      LEFT JOIN hrms_newsalary_item_group t4 ON t3.group_id = t4.id
      WHERE t1.is_deleted = 'N' AND t2.is_deleted='N'
      AND t3.item_rule != '4'
        AND t1.payroll_date = #{computeDate}
        AND t1.option_id=#{optionId}
      GROUP BY t2.item_id
      ORDER BY t4.seq_no,t3.sort_no,t3.id
    </select>

</mapper>
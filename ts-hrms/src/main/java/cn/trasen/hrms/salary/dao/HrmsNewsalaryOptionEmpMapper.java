package cn.trasen.hrms.salary.dao;

import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface HrmsNewsalaryOptionEmpMapper extends Mapper<HrmsNewsalaryOptionEmp> {
    HrmsNewsalaryOptionEmp getEmpByOption(String id);

    List<HrmsNewsalaryOptionEmp> getEmployeeDetails(String optionId);

    List<HrmsNewsalaryOptionEmp> getAllEmployeeDetails(@Param("optionId") String optionId);

    List<HrmsNewsalaryOptionEmp> getOprionIdByEmpNo(@Param("list") List<String> list);

    List<HrmsNewsalaryOptionEmp> getEmployeeDetailByOptionId(String optionId);
    
    List<String> getIsSalary(String optionId);
    

}
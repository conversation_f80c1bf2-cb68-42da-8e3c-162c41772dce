package cn.trasen.hrms.salary.service;

import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemBasic;

/**
 * @ClassName HrmsNewsalaryItemBasicService
 * @Description TODO
 * @date 2023��11��11�� ����4:34:26
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryItemBasicService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryItemBasic record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryItemBasic record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryItem
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	HrmsNewsalaryItemBasic selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryItemBasic>
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryItemBasic> getDataSetList(Page page, HrmsNewsalaryItemBasic record);

	/**
	 * 获取导入工资项数据
	 * @return
	 */
	Map<String, Object> getItemBasicData();

	/**
	 * 获取导入工资项项目库数据
	 * @return
	 */
	Map<String, Object> getItemLibraryData();
}

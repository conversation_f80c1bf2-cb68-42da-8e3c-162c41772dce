package cn.trasen.hrms.salary.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.ExcelExportOfTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.hrms.salary.model.HrmsNewsalaryLevelRemindRecord;
import cn.trasen.hrms.salary.service.HrmsNewsalaryLevelRemindRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName HrmsNewsalaryLevelRemindRecordController
 * @Description TODO
 * @date 2024��11��6�� ����10:33:38
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
@Api(tags = "hrmsNewsalaryLevelRemindRecordController")
public class HrmsNewsalaryLevelRemindRecordController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryLevelRemindRecordController.class);

	@Autowired
	private HrmsNewsalaryLevelRemindRecordService hrmsNewsalaryLevelRemindRecordService;

	/**
	 * @Title saveHrmsNewsalaryLevelRemindRecord
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��11��6�� ����10:33:38
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/hrmsNewsalaryLevelRemindRecord/save")
	public PlatformResult<String> saveHrmsNewsalaryLevelRemindRecord(@RequestBody HrmsNewsalaryLevelRemindRecord record) {
		try {
			hrmsNewsalaryLevelRemindRecordService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryLevelRemindRecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��11��6�� ����10:33:38
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/hrmsNewsalaryLevelRemindRecord/update")
	public PlatformResult<String> updateHrmsNewsalaryLevelRemindRecord(@RequestBody HrmsNewsalaryLevelRemindRecord record) {
		try {
			hrmsNewsalaryLevelRemindRecordService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryLevelRemindRecordById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryLevelRemindRecord>
	 * @date 2024��11��6�� ����10:33:38
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/hrmsNewsalaryLevelRemindRecord/{id}")
	public PlatformResult<HrmsNewsalaryLevelRemindRecord> selectHrmsNewsalaryLevelRemindRecordById(@PathVariable String id) {
		try {
			HrmsNewsalaryLevelRemindRecord record = hrmsNewsalaryLevelRemindRecordService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryLevelRemindRecordById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��11��6�� ����10:33:38
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/hrmsNewsalaryLevelRemindRecord/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryLevelRemindRecordById(@PathVariable String id) {
		try {
			hrmsNewsalaryLevelRemindRecordService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryLevelRemindRecordList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryLevelRemindRecord>
	 * @date 2024��11��6�� ����10:33:38
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/hrmsNewsalaryLevelRemindRecord/list")
	public DataSet<HrmsNewsalaryLevelRemindRecord> selectHrmsNewsalaryLevelRemindRecordList(Page page, HrmsNewsalaryLevelRemindRecord record) {
		return hrmsNewsalaryLevelRemindRecordService.getDataSetList(page, record);
	}

	/**
	 * @Title selectHrmsNewsalaryOptionsRemindRecordList
	 * @Description 批量确认调整
	 * @param isAdjust
	 * @param ids
	 * @return DataSet<HrmsNewsalaryOptionsRemindRecord>
	 * @date 2024��10��29�� ����6:29:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "批量确认调整", notes = "批量确认调整")
	@GetMapping(path="/api/hrmsNewsalaryLevelRemindRecord/batchAdjust",produces = MediaType.TEXT_EVENT_STREAM_VALUE)
	public SseEmitter batchAdjust(String isAdjust,String ids) {
		return hrmsNewsalaryLevelRemindRecordService.batchSalaryLevelAdjust(isAdjust,ids);
	}

	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping(value = "/api/hrmsNewsalaryLevelRemindRecord/downLoad")
	public void downLoad(Page page, HrmsNewsalaryLevelRemindRecord entity,HttpServletRequest request,
						 HttpServletResponse response) {
		page.setPageNo(1);
		page.setPageSize(Integer.MAX_VALUE);
		try {
			TemplateExportParams params = new TemplateExportParams("template/newsalaryremind/level_export.xls");
			String name = "薪级等级调整提醒记录.xls";
			String _val = entity.getAdjustDate();
			if(!StringUtil.isEmpty(_val)) {
				name = _val + " 薪级等级调整提醒记录.xls";
			}
			name = new String(name.getBytes("UTF-8"), "ISO8859-1");


			List<HrmsNewsalaryLevelRemindRecord> list = hrmsNewsalaryLevelRemindRecordService.getDataSetList(page,entity).getRows();
			if(list != null && list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					list.get(i).setNo(i+1);
					list.get(i).setCreateDateStr(DateUtils.getPresentTimeStr(list.get(i).getCreateDate()));
					list.get(i).setUpdateDateStr(DateUtils.getPresentTimeStr(list.get(i).getUpdateDate()));
					list.get(i).setHandleStatus("1".equals(list.get(i).getHandleStatus())?"已处理":"未处理");
					list.get(i).setIsAdjust("1".equals(list.get(i).getIsAdjust())?"是":"否");
				}
			}
			Map<String,Object> resultMap = new HashMap<String, Object>();
			resultMap.put("list", list);
			resultMap.put("date", _val);
			Workbook workbook = new ExcelExportOfTemplateUtil().createExcleByTemplate(params, null, null, resultMap);
			response.setContentType("application/msword");
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-disposition", "attachment; filename=" + name);
			ServletOutputStream fos = response.getOutputStream();
			workbook.write(fos);
			fos.flush();
		} catch (Exception e) {
			log.error("薪级等级调整提醒记录导出异常"+e.getMessage(),e);
		}
	}
}

package cn.trasen.hrms.salary.controller;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.enums.EmployeeTemporaryOpTypeEnum;
import cn.trasen.hrms.salary.enums.NewsalaryTemporaryAdjustOpTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.hrms.salary.model.HrmsNewsalaryTemporaryAdjustChange;
import cn.trasen.hrms.salary.service.HrmsNewsalaryTemporaryAdjustChangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName HrmsNewsalaryTemporaryAdjustChangeController
 * @Description TODO
 * @date 2024��10��8�� ����3:13:33
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "薪酬月度调整变动记录Controller")
public class HrmsNewsalaryTemporaryAdjustChangeController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryTemporaryAdjustChangeController.class);

	@Autowired
	private HrmsNewsalaryTemporaryAdjustChangeService hrmsNewsalaryTemporaryAdjustChangeService;

	/**
	 * @Title saveHrmsNewsalaryTemporaryAdjustChange
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��8�� ����3:13:33
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/newsalaryTemporaryAdjustChange/save")
	public PlatformResult<String> saveHrmsNewsalaryTemporaryAdjustChange(@RequestBody HrmsNewsalaryTemporaryAdjustChange record) {
		try {
			hrmsNewsalaryTemporaryAdjustChangeService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryTemporaryAdjustChange
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��8�� ����3:13:33
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/newsalaryTemporaryAdjustChange/update")
	public PlatformResult<String> updateHrmsNewsalaryTemporaryAdjustChange(@RequestBody HrmsNewsalaryTemporaryAdjustChange record) {
		try {
			hrmsNewsalaryTemporaryAdjustChangeService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryTemporaryAdjustChangeById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryTemporaryAdjustChange>
	 * @date 2024��10��8�� ����3:13:33
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/newsalaryTemporaryAdjustChange/{id}")
	public PlatformResult<HrmsNewsalaryTemporaryAdjustChange> selectHrmsNewsalaryTemporaryAdjustChangeById(@PathVariable String id) {
		try {
			HrmsNewsalaryTemporaryAdjustChange record = hrmsNewsalaryTemporaryAdjustChangeService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryTemporaryAdjustChangeById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��10��8�� ����3:13:33
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/newsalaryTemporaryAdjustChange/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryTemporaryAdjustChangeById(@PathVariable String id) {
		try {
			hrmsNewsalaryTemporaryAdjustChangeService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryTemporaryAdjustChangeList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryTemporaryAdjustChange>
	 * @date 2024��10��8�� ����3:13:33
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/newsalaryTemporaryAdjustChange/list")
	public DataSet<HrmsNewsalaryTemporaryAdjustChange> selectHrmsNewsalaryTemporaryAdjustChangeList(Page page, HrmsNewsalaryTemporaryAdjustChange record) {
		return hrmsNewsalaryTemporaryAdjustChangeService.getDataSetList(page, record);
	}

	/**
	 * @Title selectHrmsEmployeeTemporaryChangeList
	 * @Description 查询操作类别列表
	 * @return DataSet<HrmsEmployeeTemporaryChange>
	 * @date 2024��10��8�� ����3:12:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "操作类型", notes = "操作类型")
	@GetMapping("/api/newsalaryTemporaryAdjustChange/getOpTypeDatas")
	public PlatformResult getOpTypeDatas() {
		NewsalaryTemporaryAdjustOpTypeEnum[] enums = NewsalaryTemporaryAdjustOpTypeEnum.values();
		Map<String,String> map = new HashMap<>();
		List<Map<String,String>> list = new ArrayList<>(enums.length);
		for(NewsalaryTemporaryAdjustOpTypeEnum en : enums){
			map = new HashMap<>();
			map.put("key",en.getKey());
			map.put("val",en.getVal());
			list.add(map);
		}
		return PlatformResult.success(list);
	}
}

package cn.trasen.hrms.salary.model;

import cn.trasen.hrms.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * 临时员工表
 *
 */
@Table(name = "hrms_employee_temporary")
@Setter
@Getter
public class HrmsEmployeeTemporary {

    /**
     * 序号
     */
    @Transient
    @ApiModelProperty(value = "序号")
    private Integer no;
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 员工工号
     */
    @Excel(name = "工号")
    @Column(name = "employee_no")
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 员工名字
     */
    @Excel(name = "姓名")
    @Column(name = "employee_name")
    @ApiModelProperty(value = "员工名字")
    private String employeeName;

    /**
     * 组织机构
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Excel(name = "所属组织")
    @Transient
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    @Column(name = "identity_number")
    @ApiModelProperty(value = "身份证号")
    private String identityNumber;

    /**
     * 性别
     */
    @Column(name = "gender")
    @ApiModelProperty(value = "性别")
    private String gender;

    /**
     * 性别
     */
    @Transient
    @Excel(name = "性别")
    @ApiModelProperty(value = "性别")
    private String genderName;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    /**
     * 年龄
     */
    @Transient
    @ApiModelProperty(value = "年龄")
    private Integer age;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @Column(name = "phone_number")
    @ApiModelProperty(value = "联系电话")
    private String phoneNumber;

    /**
     * 学历(字典表)
     */
    @Column(name = "tmp_education")
    @ApiModelProperty(value = "学历")
    private String tmpEducation;

    @Excel(name = "学历")
    @Transient
    @ApiModelProperty(value = "学历名称")
    private String tmpEducationName;

    /**
     * 岗位(字典表)
     */
    @Column(name = "tmp_position")
    @ApiModelProperty(value = "岗位")
    private String tmpPosition;

    @Excel(name = "岗位名称")
    @Transient
    @ApiModelProperty(value = "岗位名称")
    private String tmpPositionName;

    /**
     * 编制类型(字典表)
     */
    @Column(name = "tmp_establishment")
    @ApiModelProperty(value = "编制类型")
    private String tmpEstablishment;

    @Excel(name = "编制类型")
    @Transient
    @ApiModelProperty(value = "编制类型名称")
    private String tmpEstablishmentName;

    /**
     * 岗位薪资
     */
    @Excel(name = "岗位工资")
    @Column(name = "tmp_position_salary")
    @ApiModelProperty(value = "岗位薪资")
    private BigDecimal tmpPositionSalary;

    /**
     * 银行卡号
     */
    @Excel(name = "银行卡号")
    @Column(name = "bank_card_number")
    @ApiModelProperty(value = "银行卡号")
    private String bankCardNumber;

    /**
     * 员工状态 1-在职 2-离职
     */
    @Column(name = "tmp_employee_status")
    @ApiModelProperty(value = "员工状态")
    private String tmpEmployeeStatus;

    /**
     * 入职日期
     */
    @Excel(name = "入职日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column(name = "join_date")
    @ApiModelProperty(value = "入职日期")
    private String joinDate;

    /**
     * 离职日期
     */
    @Column(name = "dimission_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "离职日期")
    private String dimissionDate;

    /**
     * 离职原因
     */
    @Column(name = "dimission_reason")
    @ApiModelProperty(value = "离职日期")
    private String dimissionReason;

    /**
     * 生效日期
     */
    @Excel(name = "生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column(name = "effective_date")
    @ApiModelProperty(value = "生效日期")
    private String effectiveDate;

    /**
     * 生效金额
     */
    @Column(name = "effective_salary")
    @ApiModelProperty(value = "生效金额")
    private BigDecimal effectiveSalary;

    /**
     * 是否已同步薪酬: Y=是; N=否;
     */
    @Column(name = "is_sync_salary")
    @ApiModelProperty(value = "是否同步")
    private String isSyncSalary;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建时间字符串
     */
    @Transient
    @ApiModelProperty(value = "创建时间字符串")
    private String createDateStr;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新时间字符串
     */
    @Transient
    @ApiModelProperty(value = "更新时间字符串")
    private String updateDateStr;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    @Transient
    @ApiModelProperty(value = "查询开始时间")
    private String searchStartTime;

    @Transient
    @ApiModelProperty(value = "查询结束时间")
    private String searchEndTime;

    public Integer getAge(){
        if(this.getBirthday()!=null){
            try {
                return DateUtils.getDifferYears(this.getBirthday(), new Date());
            }catch (Exception e){
                return 0;
            }
        }
        return 0;
    }
}
package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportsVo;
import cn.trasen.hrms.salary.model.HrmsNewsalaryReportsEo;

/**
 * 薪酬报表设置
 * <AUTHOR>
 */
public interface HrmsNewsalaryReportsService {

    Integer save(HrmsNewsalaryReportsVo record);

    Integer update(HrmsNewsalaryReportsVo record);

    Integer deleteById(String id);

    DataSet<HrmsNewsalaryReportsVo> getDataSetList(Page page, HrmsNewsalaryReportsVo record);

    HrmsNewsalaryReportsEo getById(String report);
}

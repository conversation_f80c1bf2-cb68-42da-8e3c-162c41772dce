package cn.trasen.hrms.salary.model;

import io.swagger.annotations.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.*;

/**
 * 薪酬方案表
 *
 */
@Table(name = "hrms_newsalary_option")
@Setter
@Getter
public class HrmsNewsalaryOption {

    @Id
    private String id;

    /**
     * 方案名称
     */
    @Column(name = "option_name")
    @ApiModelProperty(value = "方案名称")
    private String optionName;

    /**
     * 方案编码
     */
    @Column(name = "option_code")
    @ApiModelProperty(value = "方案编码")
    private String optionCode;

    /**
     * 小数位
     */
    @Column(name = "item_digit")
    @ApiModelProperty(value = "小数位")
    private Integer itemDigit;


    /**
     * 是否启用: 1=是; 2=否;  
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;  ")
    private String isEnable;

    /**
     * 算薪周期
     */
    @Column(name = "option_cycle")
    @ApiModelProperty(value = "算薪周期")
    private String optionCycle;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    @Column(name = "head_count")
    @ApiModelProperty(value = "算薪人数")
    private String headCount;


    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 进位规则
     */
    @Column(name = "carry_rule")
    @ApiModelProperty(value = "进位规则 1四舍五入,2向下取数,3向上取数")
    private String carryRule;

    /**
     * 是否创建工资条 1是,2否
     */
    @Column(name = "pay_slip")
    @ApiModelProperty(value = "是否创建工资条 1是,2否")
    private String paySlip;

    /**
     * 算薪日期
     */
    @Column(name = "compute_day")
    @ApiModelProperty(value = "算薪日期")
    private Integer computeDay;

    /**
     * 算薪类型 1-上月 2-本月
     */
    @Column(name = "compute_type")
    @ApiModelProperty(value = "算薪类型")
    private String computeType;

    /**
     * 是否临时员工: Y=是; N=否
     */
    @Column(name = "is_tmp_employee")
    @ApiModelProperty(value = "是否临时员工: Y=是; N=否")
    private String isTmpEmployee;

    /**
     * 编制类型，多个类型之间用英文逗号连接
     */
    @Column(name = "establishment_types")
    @ApiModelProperty(value = "编制类型")
    private String establishmentTypes;

    /**
     * 员工状态，多个状态之间用英文逗号连接
     */
    @Column(name = "employee_status")
    @ApiModelProperty(value = "员工状态")
    private String employeeStatus;

    @Transient
    @ApiModelProperty(value = "薪酬方案包含的人员")
    private List<HrmsNewsalaryOptionEmp> optionEmp;

    @Transient
    @ApiModelProperty(value = "人员名称")
    private String employeeName;

    @Transient
    @ApiModelProperty(value = "人员id")
    private String employeeId;

    @Transient
    @ApiModelProperty(value = "人员工号")
    private String employeeNo;

    @Transient
    @ApiModelProperty(value = "定薪人数")
    private Integer setCount;

    @Transient
    @ApiModelProperty(value = "调薪人数")
    private Integer updateCount;


    @Transient
    @ApiModelProperty(value = "工资条状态")
    private String  paySheet;

    @Transient
    @ApiModelProperty(value = "工资发放月份")
    private String  calculateDate;

    @Transient
    @ApiModelProperty(value = "薪酬方案人员列表")
    private List<HrmsNewsalaryOptionEmp> empList = new ArrayList<>();

}
package cn.trasen.hrms.salary.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.dao.HrmsNewsalarySecondstepTitleHistoryMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalarySecondstepTitleHistory;
import cn.trasen.hrms.salary.service.HrmsNewsalarySecondstepTitleHistoryService;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsNewsalarySecondstepTitleHistoryServiceImpl
 * @Description TODO
 * @date 2024��3��11�� ����5:38:28
 * <AUTHOR>
 * @version 1.0
 */
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalarySecondstepTitleHistoryServiceImpl implements HrmsNewsalarySecondstepTitleHistoryService {

	@Autowired
	private HrmsNewsalarySecondstepTitleHistoryMapper mapper;

//	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalarySecondstepTitleHistory record) {
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalarySecondstepTitleHistory record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalarySecondstepTitleHistory record = new HrmsNewsalarySecondstepTitleHistory();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalarySecondstepTitleHistory selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalarySecondstepTitleHistory> getDataSetList(Page page, HrmsNewsalarySecondstepTitleHistory record) {
		Example example = new Example(HrmsNewsalarySecondstepTitleHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalarySecondstepTitleHistory> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public HrmsNewsalarySecondstepTitleHistory getData(String optionId, String computeDate) {
		Example example = new Example(HrmsNewsalarySecondstepTitleHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("payrollDate", computeDate);
		criteria.andEqualTo("optionId", optionId);
		List<HrmsNewsalarySecondstepTitleHistory> records = mapper.selectByExample(example);
		if(!records.isEmpty()){
			return records.get(0);
		}
		return null;
	}

	@Override
	public Integer deleteByOptionIdAndDate(String optionId, String computeDate) {
		Example example = new Example(HrmsNewsalarySecondstepTitleHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("payrollDate", computeDate);
		criteria.andEqualTo("optionId", optionId);
		return mapper.deleteByExample(example);
	}
}

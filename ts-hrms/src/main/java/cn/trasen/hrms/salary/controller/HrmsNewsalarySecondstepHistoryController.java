package cn.trasen.hrms.salary.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalarySecondstepHistory;
import cn.trasen.hrms.salary.service.HrmsNewsalarySecondstepHistoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalarySecondstepHistoryController
 * @Description TODO
 * @date 2024��3��11�� ����5:53:01
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsNewsalarySecondstepHistoryController")
public class HrmsNewsalarySecondstepHistoryController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalarySecondstepHistoryController.class);

	@Autowired
	private HrmsNewsalarySecondstepHistoryService hrmsNewsalarySecondstepHistoryService;

	/**
	 * @Title saveHrmsNewsalarySecondstepHistory
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��3��11�� ����5:53:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/newsalarysecondstephistory/save")
	public PlatformResult<String> saveHrmsNewsalarySecondstepHistory(@RequestBody HrmsNewsalarySecondstepHistory record) {
		try {
			hrmsNewsalarySecondstepHistoryService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalarySecondstepHistory
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��3��11�� ����5:53:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/newsalarysecondstephistory/update")
	public PlatformResult<String> updateHrmsNewsalarySecondstepHistory(@RequestBody HrmsNewsalarySecondstepHistory record) {
		try {
			hrmsNewsalarySecondstepHistoryService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalarySecondstepHistoryById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalarySecondstepHistory>
	 * @date 2024��3��11�� ����5:53:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/newsalarysecondstephistory/{id}")
	public PlatformResult<HrmsNewsalarySecondstepHistory> selectHrmsNewsalarySecondstepHistoryById(@PathVariable String id) {
		try {
			HrmsNewsalarySecondstepHistory record = hrmsNewsalarySecondstepHistoryService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalarySecondstepHistoryById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��3��11�� ����5:53:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/newsalarysecondstephistory/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalarySecondstepHistoryById(@PathVariable String id) {
		try {
			hrmsNewsalarySecondstepHistoryService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalarySecondstepHistoryList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalarySecondstepHistory>
	 * @date 2024��3��11�� ����5:53:01
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/newsalarysecondstephistory/list")
	public DataSet<HrmsNewsalarySecondstepHistory> selectHrmsNewsalarySecondstepHistoryList(Page page, HrmsNewsalarySecondstepHistory record) {
		return hrmsNewsalarySecondstepHistoryService.getDataSetList(page, record);
	}
}

package cn.trasen.hrms.salary.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 工资明细表
 *
 */
@Table(name = "hrms_newsalary_payroll_detail")
@Setter
@Getter
public class HrmsNewsalaryPayrollDetail {
    /**
     * 发放明细id
     */
    @Id
    @ApiModelProperty(value = "发放明细id")
    private String id;

    /**
     * 薪酬发放id
     */
    @Column(name = "payroll_id")
    @ApiModelProperty(value = "薪酬发放id")
    private String payrollId;

    /**
     * 薪酬醒目id
     */
    @Column(name = "item_id")
    @ApiModelProperty(value = "薪酬醒目id")
    private String itemId;

    /**
     * 薪酬项目code
     */
    @Column(name = "option_id")
    @ApiModelProperty(value = "方案id")
    private String optionId;

    @Column(name = "payroll_date")
    @ApiModelProperty(value = "月份")
    private String payrollDate;

    /**
     * 薪酬项目名称
     */
    @Column(name = "item_name")
    @ApiModelProperty(value = "薪酬项目名称")
    private String itemName;

    /**
     * 工资金额
     */
    @ApiModelProperty(value = "工资金额")
    private BigDecimal salary;

    /**
     * 工资条空值是否隐藏
     */
    @Column(name = "is_hidden")
    @ApiModelProperty(value = "工资条空值是否隐藏")
    private String isHidden;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    public HrmsNewsalaryPayrollDetail(String id,String payrollId){
        this.id = id;
        this.payrollId = payrollId;
        this.setCreateDate(new Date());
        this.setUpdateDate(new Date());
        this.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            this.setCreateUser(user.getUsercode());
            this.setCreateUserName(user.getUsername());
            this.setUpdateUser(user.getUsercode());
            this.setUpdateUserName(user.getUsername());
        }
    }

    public HrmsNewsalaryPayrollDetail(){
    }
}
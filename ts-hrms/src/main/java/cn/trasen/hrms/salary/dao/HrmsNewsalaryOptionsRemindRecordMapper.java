package cn.trasen.hrms.salary.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionsRemindRecord;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface HrmsNewsalaryOptionsRemindRecordMapper extends Mapper<HrmsNewsalaryOptionsRemindRecord> {
    /**
     * @Title: getList
     * @Description: 查询薪酬方案提醒列表
     * @param entity
     * @Return List<HrmsEmployeeTemporary>
     * <AUTHOR>
     * @date 2020年4月15日 下午2:45:21
     */
    List<HrmsNewsalaryOptionsRemindRecord> getList(Page page, HrmsNewsalaryOptionsRemindRecord entity);

    /**
     * @Title: getList
     * @Description: 查询薪酬方案提醒列表
     * @param entity
     * @Return List<HrmsEmployeeTemporary>
     * <AUTHOR>
     * @date 2020年4月15日 下午2:45:21
     */
    List<HrmsNewsalaryOptionsRemindRecord> getList(HrmsNewsalaryOptionsRemindRecord entity);

    /**
     * 获取薪酬方案变动列表
     * @param remindCycle 预警周期 1-单次 2-每天 3-每周 4-每月 5-每年
     * @param advanceDays 提前预警天数
     * @param remindDate 提醒日期
     * @param remindTime 提醒时间
     * @return
     */
    List<HrmsNewsalaryOptionsRemindRecord> getNewsalaryOptionsChange(@Param(value = "remindCycle") String remindCycle,@Param(value = "advanceDays") String advanceDays,
                                                                     @Param(value = "remindDate") String remindDate,@Param(value = "remindTime") String remindTime);

    /**
     * 查询需要批量锁定的方案提醒记录
     * @return
     */
    List<HrmsNewsalaryOptionsRemindRecord> selectNeedLockOptionsRemindData();
}
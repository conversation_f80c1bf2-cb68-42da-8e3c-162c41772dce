package cn.trasen.hrms.salary.utils;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class VueTableEntity {

    /**
     * 显示文本
     */
    @ApiModelProperty(value = "显示文本")
    private String label;
    /**
     * 名称
     */
    @ApiModelProperty(value = "绑定的key")
    private String prop;
    /**
     * 排序
     */
    @ApiModelProperty(value = "显示位置")
    private String   align = "center";
    /**
     * 宽度
     */
    @ApiModelProperty(value = "宽度")
    private Integer width = 80;

    @ApiModelProperty(value = "固定")
    private String fixed ;

    @ApiModelProperty(value = "true不能编辑")
    private Boolean isBasic;
    
    @ApiModelProperty(value = "是否数字格式")
    private Boolean isNum = false;

    @ApiModelProperty(value = "是否薪酬加减项 1-加项 2-减项")
    private String isAdjust = "";


    public VueTableEntity(String label, String prop, String align, Integer width, String fixed) {
        this.label = label;
        this.prop = prop;
        if(align != null){
            this.align = align;
        }
        if(width != null){
            this.width = width;
        }
        if(fixed != null){
            this.fixed = fixed;
        }
    }
    
    public VueTableEntity(String label, String prop, String align, Integer width, Boolean isNum, String fixed,String isAdjust) {
        this.label = label;
        this.prop = prop;
        if(align != null){
            this.align = align;
        }
        if(width != null){
            this.width = width;
        }
        if(isNum != null){
            this.isNum = isNum;
        }
        if(fixed != null){
            this.fixed = fixed;
        }
        if(StringUtils.isNotBlank(isAdjust)){
            this.isAdjust = isAdjust;
        }
    }

    public VueTableEntity() {

    }

    public VueTableEntity(String label, String prop, String align, Integer width, String fixed,Boolean isBasic) {
        this.label = label;
        this.prop = prop;
        if(align != null){
            this.align = align;
        }
        if(width != null){
            this.width = width;
        }
        if(fixed != null){
            this.fixed = fixed;
        }
        if(isBasic != null){
            this.isBasic = isBasic;
        }
    }
}

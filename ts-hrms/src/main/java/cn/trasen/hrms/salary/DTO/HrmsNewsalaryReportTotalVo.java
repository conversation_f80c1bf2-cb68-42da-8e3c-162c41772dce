package cn.trasen.hrms.salary.DTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import java.util.Date;
import java.util.List;

/**
* 薪酬汇总类型返回类
*/
@Setter
@Getter
public class HrmsNewsalaryReportTotalVo {

    /**
    * ID
    */
    @ApiModelProperty("ID")
    @Id
    private String id;

    @ApiModelProperty("报表id")
    private String reportId;

    @ApiModelProperty("报表名称")
    private String reportName;
    /**
    * 字段名称
    */
    @ApiModelProperty("字段名称")
    private String colName;

    /**
     * 字段名称
     */
    @ApiModelProperty("字段名称")
    private String colCode;
    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
    /**
    * 创建人ID
    */
    @ApiModelProperty("创建人ID")
    private String createUser;
    /**
    * 创建人姓名
    */
    @ApiModelProperty("创建人姓名")
    private String createUserName;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private Date createDate;
    /**
    * 更新人ID
    */
    @ApiModelProperty("更新人ID")
    private String updateUser;
    /**
    * 更新人姓名
    */
    @ApiModelProperty("更新人姓名")
    private String updateUserName;
    /**
    * 更新时间
    */
    @ApiModelProperty("更新时间")
    private Date updateDate;
    /**
    * 是否删除 N 正常   Y 删除
    */
    @ApiModelProperty("是否删除 N 正常   Y 删除")
    private String isDeleted;

    /**
    * 
    */
    @ApiModelProperty("机构编码")
    private String ssoOrgCode;
    /**
    * 
    */
    @ApiModelProperty("编码长度")
    private String ssoOrgName;

    @ApiModelProperty("汇总报表和方案关联的列表")
    private List<HrmsNewsalaryReportMapVo> reportMapVoList;

    @ApiModelProperty("时间")
    private String payrollDate;

    /**
     * 排序
     */
    private Integer sortNo;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    /**
     * 计算类型 1-增项 2-减项
     */
    @ApiModelProperty(value = "计算类型 1-增项 2-减项")
    private String countType;
}

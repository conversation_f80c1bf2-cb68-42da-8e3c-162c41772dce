package cn.trasen.hrms.salary.DTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 薪酬档案列表查询条件对象
 */
@Data
public class SearchListTable {

    @ApiModelProperty(value = "工号姓名")
    private String employeeName; //员工姓名
    @ApiModelProperty(value = "员工状态")
    private List<String> employeeStatus;
    @ApiModelProperty(value = "科室名称")
    private String orgName;
    @ApiModelProperty(value = "字段列表")
    private List<String> basicItemIds; //字段列表

    @ApiModelProperty(value = "定薪状态 1 已定薪 0 或者空 未定薪")
    private String salaryAppoint;

    @ApiModelProperty(value = "是否加入薪酬组织 1 已加入 0 未加入")
    private String hasJoinOption;

    @ApiModelProperty(value = "异动情况")
    private String chageStatus;

    @ApiModelProperty(value = "核算月份")
    private String payrollDate;
    @ApiModelProperty(value = "方案id")
    private String optionId;

    @ApiModelProperty(value = "入职开始时间")
    private String entryDateStartTime;

    @ApiModelProperty(value = "入职结束时间")
    private String entryDateEndTime;

    @ApiModelProperty(value = "编制类型")
    private List<String> establishmentTypes;


    @ApiModelProperty(value = "转正开始时间")
    private String positiveTimeStartTime;

    @ApiModelProperty(value = "转正结束时间")
    private String positiveTimeEndTime;

    @ApiModelProperty(value = "离退休开始时间")
    private String retirementTimeStartTime;

    @ApiModelProperty(value = "离退休结束时间")
    private String retirementTimeEndTime;

    @ApiModelProperty(value = "员工id集合")
    private List<String> empList;

    @ApiModelProperty(value = "菜单筛选 1全部人员,2在职未定薪人员,3离/退休人员,4调薪/定薪人员")
    private String menu;

    @ApiModelProperty(value = "机构id")
    private String orgId;

    @ApiModelProperty(value = "机构id集合")
    private List<String> orgList;

    @ApiModelProperty(value = "查询百分比薪酬项")
    private String empField;

    @ApiModelProperty(value = "查询百分比")
    private String empFieldValue;

    @ApiModelProperty(value = "岗位等级")
    private String gwdj;

    @ApiModelProperty(value = "薪级等级")
    private String salaryLevelId;

    @ApiModelProperty(value = "是否临时员工 Y-是")
    private String isTemp;

    @ApiModelProperty(value = "员工id字符串")
    private List<String> employeeIds;

    @ApiModelProperty(value = "政策标准id")
    private String policyStandardId;
}

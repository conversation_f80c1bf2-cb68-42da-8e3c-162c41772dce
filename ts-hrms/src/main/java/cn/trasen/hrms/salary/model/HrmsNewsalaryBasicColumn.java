package cn.trasen.hrms.salary.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 薪酬档案列管理
 *
 */
@Table(name = "hrms_newsalary_basic_column")
@Setter
@Getter
public class HrmsNewsalaryBasicColumn {
    /**
     * 主键id
     */
    @Id
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 基本定薪项名称
     */
    @Column(name = "basic_item_name")
    @ApiModelProperty(value = "基本定薪项名称")
    private String basicItemName;

    /**
     * 项目类型1基础信息2工资项
     */
    @Column(name = "basic_item_type")
    @ApiModelProperty(value = "项目类型1基础信息2工资项3其它")
    private String basicItemType;

    /**
     * 员工档案字段
     */
    @Column(name = "emp_field")
    @ApiModelProperty(value = "员工档案字段")
    private String empField;

    /**
     * 小数位
     */
    @Column(name = "POSITION")
    @ApiModelProperty(value = "小数位")
    private String position;

    /**
     * 自定义项目
     */
    @Column(name = "custom_rule")
    @ApiModelProperty(value = "自定义项目")
    private String customRule;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;


    @Column(name = "number_sort")
    @ApiModelProperty(value = "排序号")
    private Integer numberSort;

    @Column(name = "compare")
    @ApiModelProperty(value = "1 需要对比的列")
    private String compare;


    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;




    @Transient
    @ApiModelProperty(value = "人员id")
    private String employeeId;

    /**
     * 基本定薪项名称
     */
    @Transient
    @ApiModelProperty(value = "基本定薪项值")
    private String basicItemValue;

    @Transient
    @ApiModelProperty(value = "定薪金额")
    private String salaryAmount;


    @Transient
    @ApiModelProperty(value = "字段值")
    private String empFieldValue;

    @Transient
    @ApiModelProperty(value = "字段规则")
    private String itemRule;
    
}
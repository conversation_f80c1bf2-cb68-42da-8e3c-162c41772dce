package cn.trasen.hrms.salary.service.impl;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemLibrary;
import cn.trasen.hrms.salary.service.IHrmsNewSalaryItemLibraryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryItemBasicMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryItemGroupBasicMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemBasic;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemGroupBasic;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemBasicService;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsNewsalaryItemServiceImpl
 * @Description TODO
 * @date 2023��11��11�� ����4:34:26
 * <AUTHOR>
 * @version 1.0
 */
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryItemBasicServiceImpl implements HrmsNewsalaryItemBasicService {

	@Autowired
	private HrmsNewsalaryItemBasicMapper mapper;

	@Autowired
	HrmsNewsalaryItemGroupBasicMapper groupBasicMapper;

	@Autowired
	private IHrmsNewSalaryItemLibraryService hrmsNewSalaryItemLibraryService;

//	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryItemBasic record) {
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryItemBasic record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryItemBasic record = new HrmsNewsalaryItemBasic();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalaryItemBasic selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalaryItemBasic> getDataSetList(Page page, HrmsNewsalaryItemBasic record) {
		Example example = new Example(HrmsNewsalaryItemBasic.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalaryItemBasic> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public Map<String, Object> getItemBasicData() {
		//获取所有类型
		List<HrmsNewsalaryItemGroupBasic> hrmsNewsalaryItemGroupBasics = groupBasicMapper.selectAll();
		//获取所有内容
		List<HrmsNewsalaryItemBasic> list = mapper.selectAll();
		Map<String, List<HrmsNewsalaryItemBasic>> groupedMap = list.stream().collect(Collectors.groupingBy(dto -> dto.getGroupId()));
		Map<String,Object> retMap = new LinkedHashMap<>();
		for (int i = 0; i < hrmsNewsalaryItemGroupBasics.size(); i++) {
			retMap.put(hrmsNewsalaryItemGroupBasics.get(i).getItemGroup(),groupedMap.get(hrmsNewsalaryItemGroupBasics.get(i).getId()));
		}
		return retMap;
	}

	@Override
	public Map<String, Object> getItemLibraryData() {
		//获取所有类型
		List<HrmsNewsalaryItemGroupBasic> hrmsNewsalaryItemGroupBasics = groupBasicMapper.selectAll();
		//获取所有内容
		List<HrmsNewsalaryItemLibrary> list = hrmsNewSalaryItemLibraryService.getEnableData();
		if(CollUtil.isEmpty(list)){
			throw new BusinessException("没有可用薪资项目!");
		}
		Map<String, List<HrmsNewsalaryItemLibrary>> groupedMap = list.stream().collect(Collectors.groupingBy(dto -> dto.getLibraryType()));
		Map<String,Object> retMap = new LinkedHashMap<>();
		for (int i = 0; i < hrmsNewsalaryItemGroupBasics.size(); i++) {
			retMap.put(hrmsNewsalaryItemGroupBasics.get(i).getItemGroup(),groupedMap.get(hrmsNewsalaryItemGroupBasics.get(i).getId()));
		}
		return retMap;
	}
}

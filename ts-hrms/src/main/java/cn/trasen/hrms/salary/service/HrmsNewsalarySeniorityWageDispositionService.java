package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalarySeniorityWageDisposition;

import java.util.List;

/**
 * @ClassName HrmsNewsalarySeniorityWageDispositionService
 * @Description 工龄工资规则
 * @date 2024��4��12�� ����11:06:21
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalarySeniorityWageDispositionService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��4��12�� ����11:06:21
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalarySeniorityWageDisposition record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��4��12�� ����11:06:21
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalarySeniorityWageDisposition record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��4��12�� ����11:06:21
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalarySeniorityWageDisposition
	 * @date 2024��4��12�� ����11:06:21
	 * <AUTHOR>
	 */
	HrmsNewsalarySeniorityWageDisposition selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalarySeniorityWageDisposition>
	 * @date 2024��4��12�� ����11:06:21
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalarySeniorityWageDisposition> getDataSetList(Page page, HrmsNewsalarySeniorityWageDisposition record);

	/**
	 * 更新工龄工资的方法
	 */
	Integer taskUpdateSeniorityWage();

	/**启用
	 * @param ids
	 */
	void enable(List<String> ids);

	/**
	 * 禁用
	 * @param ids
	 */
	void disEnable(List<String> ids);
}

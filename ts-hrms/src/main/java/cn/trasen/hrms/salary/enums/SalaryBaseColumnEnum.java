package cn.trasen.hrms.salary.enums;

import cn.trasen.hrms.common.KeyValue;
import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * 薪酬档案基础列
 */
@Getter
public enum SalaryBaseColumnEnum {


  /*  ITEM_TYPE_1("employee_no", "工号"),
    ITEM_TYPE_2("employee_name", "姓名"),

    ITEM_TYPE_4("org_name", "部门"),*/
    ITEM_TYPE_xcz("option_name", "薪酬组"),
//    ITEM_TYPE_3("employee_status", "员工状态"),
    ITEM_TYPE_10("personal_identity", "岗位名称"),
//    ITEM_TYPE_15("establishment_type", "编制类型"),
//    ITEM_TYPE_21("policy_standard_id", "政策标准"),

    ITEM_TYPE_11("plgw", "岗位类别"),
    ITEM_TYPE_12("gwdj", "岗位等级"),
    ITEM_TYPE_13("salary_level_type", "薪级类别"),
    ITEM_TYPE_14("salary_level_id", "薪级等级"),

    ITEM_TYPE_16("entry_date", "入职日期"),
    ITEM_TYPE_17("positive_time", "转正日期"),
    ITEM_TYPE_18("retirement_time", "离退休日期"),
    ITEM_TYPE_19("identity_number", "身份证号"),
    ITEM_TYPE_20("bankcardno", "银行卡号");

    private final String key;
    private final String val;

    private SalaryBaseColumnEnum(String key, String val) {
        this.key = key;
        this.val = val;
    }

    /**
     * @Title: getValByKey
     * @Description: 根据Key获得val值
     * @Param: key
     * @Return: String
     * <AUTHOR>
     */
    public static String getValByKey(String key) {
        for (SalaryBaseColumnEnum item : SalaryBaseColumnEnum.values()) {
            if (item.key.equals(key)) {
                return item.val;
            }
        }
        return "";
    }

    public static String getKeyByVal(String val) {
        for (SalaryBaseColumnEnum item : SalaryBaseColumnEnum.values()) {
            if (item.val.equals(val)) {
                return item.key;
            }
        }
        return "";
    }

    public static List<KeyValue> getBaseColumnEnumList() {
        List<KeyValue> list = Lists.newArrayList();
        for (SalaryBaseColumnEnum item : SalaryBaseColumnEnum.values()) {
            KeyValue kv = new KeyValue();
            kv.setCode(item.getKey());
            kv.setText(item.getVal());
            list.add(kv);
        }
        return list;
    }

}

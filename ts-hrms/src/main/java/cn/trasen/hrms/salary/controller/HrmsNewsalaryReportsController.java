package cn.trasen.hrms.salary.controller;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportsVo;
import cn.trasen.hrms.salary.service.HrmsNewsalaryReportsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName HrmsNewsalarysTypeController
 * @Description 薪酬汇总报表Controller
 * @date 2024 0723
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "薪酬汇总报表Controller")
public class HrmsNewsalaryReportsController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryReportsController.class);

	@Autowired
	private HrmsNewsalaryReportsService hrmsNewsalaryReportsService;

	/**
	 * @Title saveHrmsNewsalaryReportsVo
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024 0723
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/reports/save")
	public PlatformResult<String> saveHrmsNewsalaryReportsVo(@RequestBody HrmsNewsalaryReportsVo record) {
		try {
			hrmsNewsalaryReportsService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryReportsVo
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024 0723
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/reports/update")
	public PlatformResult<String> updateHrmsNewsalaryReportsVo(@RequestBody HrmsNewsalaryReportsVo record) {
		try {
			hrmsNewsalaryReportsService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}


	/**
	 *
	 * @Title deleteHrmsNewsalaryReportsVoById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024 0723
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/reports/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryReportsVoById(@PathVariable String id) {
		try {
			hrmsNewsalaryReportsService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryReportsVoList
	 * @Description 汇总方案列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryReportsVo>
	 * @date 2024 0723
	 * <AUTHOR>
	 */
	@ApiOperation(value = "汇总方案列表", notes = "列表")
	@GetMapping("/api/reports/list")
	public DataSet<HrmsNewsalaryReportsVo> selectHrmsNewsalaryReportsVoList(Page page, HrmsNewsalaryReportsVo record) {
		return hrmsNewsalaryReportsService.getDataSetList(page, record);
	}
}

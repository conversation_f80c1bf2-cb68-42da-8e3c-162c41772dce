package cn.trasen.hrms.salary.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.dao.HrmsNewsalarySeniorityWageEfMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalarySeniorityWageEf;
import cn.trasen.hrms.salary.service.HrmsNewsalarySeniorityWageEfService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsNewsalarySeniorityWageEfServiceImpl
 * @Description TODO
 * @date 2024��4��17�� ����11:27:53
 * <AUTHOR>
 * @version 1.0
 */
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalarySeniorityWageEfServiceImpl implements HrmsNewsalarySeniorityWageEfService {

	@Autowired
	private HrmsNewsalarySeniorityWageEfMapper mapper;

//	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalarySeniorityWageEf record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalarySeniorityWageEf record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalarySeniorityWageEf record = new HrmsNewsalarySeniorityWageEf();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalarySeniorityWageEf selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalarySeniorityWageEf> getDataSetList(Page page, HrmsNewsalarySeniorityWageEf record) {
		Example example = new Example(HrmsNewsalarySeniorityWageEf.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalarySeniorityWageEf> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsNewsalarySeniorityWageEf> getAllData() {
		Example example = new Example(HrmsNewsalarySeniorityWageEf.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return mapper.selectByExample(example);
	}

	@Override
	public void changgeSalary() {
		//查询出所有人员数据
		//先根据人员删除数据
		Example example = new Example(HrmsNewsalarySeniorityWageEf.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalarySeniorityWageEf> list = mapper.selectByExample(example);
		if(!list.isEmpty()){
			for (int i = 0; i < list.size(); i++) {
				HrmsNewsalarySeniorityWageEf _bean = list.get(i);
				BigDecimal _oldSalary = _bean.getSalary();
				Date updateDate = _bean.getUpdateDate();
				_bean.setUpdateDate(new Date());
				_bean.setLastUpdateDate(updateDate);
				_bean.setLastSalary(_oldSalary);
				_bean.setSalary(_oldSalary.add(new BigDecimal(30)));
				mapper.updateByPrimaryKeySelective(_bean);
			}
		}


	}
}

package cn.trasen.hrms.salary.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 工龄工资表
 *
 */
@Table(name = "hrms_newsalary_seniority_wage_disposition")
@Setter
@Getter
public class HrmsNewsalarySeniorityWageDisposition {
    /**
     * id
     */
    @Id
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
    private String years;

    /**
     * 工资金额
     */
    @ApiModelProperty(value = "工资金额")
    private BigDecimal salary;

    /**
     * 状态 1启用,2禁用
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "状态 1启用,2禁用")
    private String isEnable;

    /**
     * 启用状态文本
     */
    @Transient
    @ApiModelProperty(value = "启用状态文本")
    private String isEnableLabel;

    /**
     * 备注
     */
    @Column(name = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    @ApiModelProperty(value = "员工id")
    @Transient
    private String employeeId;
}
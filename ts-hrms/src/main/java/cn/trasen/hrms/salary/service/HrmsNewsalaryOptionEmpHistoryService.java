package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmpHistory;

/**
 * @ClassName HrmsNewsalaryOptionEmpHistoryService
 * @Description TODO
 * @date 2023��11��11�� ����4:36:51
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryOptionEmpHistoryService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:36:51
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryOptionEmpHistory record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:36:51
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryOptionEmpHistory record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��11��11�� ����4:36:51
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryOptionEmpHistory
	 * @date 2023��11��11�� ����4:36:51
	 * <AUTHOR>
	 */
	HrmsNewsalaryOptionEmpHistory selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryOptionEmpHistory>
	 * @date 2023��11��11�� ����4:36:51
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryOptionEmpHistory> getDataSetList(Page page, HrmsNewsalaryOptionEmpHistory record);
}

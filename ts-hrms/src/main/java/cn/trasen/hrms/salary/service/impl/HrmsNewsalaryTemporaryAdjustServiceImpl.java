package cn.trasen.hrms.salary.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.salary.enums.NewsalaryTemporaryAdjustOpTypeEnum;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionPayrollService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryTemporaryAdjustChangeService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.hrms.salary.dao.HrmsNewsalaryTemporaryAdjustMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryTemporaryAdjust;
import cn.trasen.hrms.salary.service.HrmsNewsalaryTemporaryAdjustService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @ClassName HrmsNewsalaryTemporaryAdjustServiceImpl
 * @Description TODO
 * @date 2024��10��8�� ����3:13:22
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryTemporaryAdjustServiceImpl implements HrmsNewsalaryTemporaryAdjustService {

	@Resource
	private HrmsNewsalaryTemporaryAdjustMapper mapper;

	@Resource
	private DictItemFeignService dictItemFeignService;

	@Autowired
	private HrmsNewsalaryTemporaryAdjustChangeService newsalaryTemporaryAdjustChangeService;

	@Autowired
	private HrmsNewsalaryOptionPayrollService newsalaryOptionPayrollService;

	@Autowired
    private HrmsEmployeeService employeeService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryTemporaryAdjust record,NewsalaryTemporaryAdjustOpTypeEnum opTypeEnum) {
		//根据工号+身份证号确定一条数据
		Assert.hasText(record.getEmployeeNo(), "工号不能为空.");
		Assert.hasText(record.getOptionCycle(), "算薪周期不能为空.");
		Assert.hasText(record.getTmpItem(), "项目类型不能为空.");
		Assert.notNull(record.getSalaryItemAmount(), "金额不能为空.");
        //查询,主要针对类似导入时没有员工id的情况
        if(StringUtils.isBlank(record.getEmployeeId())){
            HrmsEmployee employee = employeeService.findByEmployeeNo(record.getEmployeeNo());
            if(employee == null){
                throw new BusinessException("未找到员工数据");
            }
            record.setEmployeeName(employee.getEmployeeName());
            record.setEmployeeId(employee.getEmployeeId());
        }

		Example example = new Example(HrmsNewsalaryTemporaryAdjust.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeNo", record.getEmployeeNo())
				.andEqualTo("optionCycle",record.getOptionCycle())
				.andEqualTo("tmpItem",record.getTmpItem())
				.andEqualTo("countType",record.getCountType())
				.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalaryTemporaryAdjust> records = mapper.selectByExample(example);
		if(CollectionUtil.isNotEmpty(records)){
			throw new BusinessException("["+record.getEmployeeNo()+"]当前算薪周期已存在相同项目类型的数据，请勿重复添加");
		}
		//判断员工所在方案在薪酬方案是否已锁定 1-已锁定 0-未锁定 -1 -未绑定方案
		Integer num = newsalaryOptionPayrollService.getIsLockByEmpComputeDate(record.getOptionCycle(),record.getEmployeeId());
		if(num == null || num  == -1){
			throw new BusinessException("["+record.getEmployeeNo()+"]尚未加入薪酬方案");
		}else if(num == 1){
			throw new BusinessException("["+record.getEmployeeNo()+"]所在算薪周期的方案已核算锁定，不支持再次进行薪酬调整");
		}else if(num == -2){
			throw new BusinessException("["+record.getEmployeeNo()+"]所在方案已禁用,请先启用薪酬方案后再进行操作");
		}

		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		record.setIsUse("2");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//保存操作记录
		newsalaryTemporaryAdjustChangeService.saveChanges(null,record,opTypeEnum);
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryTemporaryAdjust record,NewsalaryTemporaryAdjustOpTypeEnum opTypeEnum) {
		//根据工号+身份证号确定一条数据
		Assert.hasText(record.getId(), "id不能为空.");
		Assert.hasText(record.getEmployeeNo(), "工号不能为空.");
		Assert.hasText(record.getOptionCycle(), "算薪周期不能为空.");
		Assert.hasText(record.getTmpItem(), "项目类型不能为空.");
		Assert.notNull(record.getSalaryItemAmount(), "金额不能为空.");
		//判断当员工是否已离职，离职以后得数据不能进行编辑操作
		HrmsNewsalaryTemporaryAdjust entity =mapper.selectByPrimaryKey(record.getId());
		if(entity == null || "Y".equals(entity.getIsDeleted())){
			throw new BusinessException("未找到可以编辑的数据");
		}else if("1".equals(entity.getIsUse())){
			throw new BusinessException("当前方案已锁定不能再进行修改操作");
		}

		Example example = new Example(HrmsNewsalaryTemporaryAdjust.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeNo", record.getEmployeeNo())
				.andEqualTo("optionCycle",record.getOptionCycle())
				.andEqualTo("tmpItem",record.getTmpItem())
				.andEqualTo("countType",record.getCountType())
				.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalaryTemporaryAdjust> records = mapper.selectByExample(example);
		if(CollectionUtil.isNotEmpty(records)){
			if(records.size()>1) {
				throw new BusinessException("["+record.getEmployeeNo()+"]当前算薪周期已存在相同项目类型的数据，请重新操作");
			}else if(records.size()==1 && !record.getId().equals(records.get(0).getId())){
				throw new BusinessException("["+record.getEmployeeNo()+"]当前算薪周期已存在相同项目类型的数据，请重新操作");
			}
		}

		//判断员工所在方案在薪酬方案是否已锁定 1-已锁定 0-未锁定 -1 -未绑定方案
		Integer num = newsalaryOptionPayrollService.getIsLockByEmpComputeDate(record.getOptionCycle(),record.getEmployeeId());
		if(num == null || num  == -1){
			throw new BusinessException("["+record.getEmployeeNo()+"]尚未加入薪酬方案");
		}else if(num == 1){
			throw new BusinessException("["+record.getEmployeeNo()+"]所在算薪周期的方案已核算锁定，不支持再次进行薪酬调整");
		}else if(num == -2){
			throw new BusinessException("["+record.getEmployeeNo()+"]所在方案已禁用,请先启用薪酬方案后再进行操作");
		}
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}

		//保存操作记录
		newsalaryTemporaryAdjustChangeService.saveChanges(entity,record,opTypeEnum);
		return mapper.updateByPrimaryKeySelective(record);
	}

	/**
	 * 根据薪酬调整项目类型和计算类型查询员工调整记录
	 * @param employeeId
	 * @param tmpItem
	 * @param optionCycle
	 * @param countType
	 * @return
	 */
	@Override
	public HrmsNewsalaryTemporaryAdjust getEmployeeTemporaryAdjustByItem(String employeeId,String tmpItem,String optionCycle,String countType){
		//根据工号+身份证号确定一条数据
		Assert.hasText(employeeId, "员工id不能为空.");
		Assert.hasText(optionCycle, "算薪周期不能为空.");
		Assert.hasText(tmpItem, "项目类型不能为空.");
		Assert.notNull(countType, "计算类型不能为空.");
		Example example = new Example(HrmsNewsalaryTemporaryAdjust.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeId", employeeId)
				.andEqualTo("optionCycle",optionCycle)
				.andEqualTo("countType",countType)
				.andEqualTo("tmpItem",tmpItem)
				.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalaryTemporaryAdjust> records = mapper.selectByExample(example);
		if(CollectionUtil.isNotEmpty(records)){
			return records.get(0);
		}
		return null;
	}

	 /**
	 * 根据员工id列表和算薪周期锁定薪酬调整
	 * @param employeeids
	 * @param computeDate
	 * @return
	 */
	 @Transactional(readOnly = false)
	 @Override
	 public Integer lockAdjuct(List<String> employeeids,String computeDate){
	 	if(CollectionUtil.isEmpty(employeeids) || StringUtils.isEmpty(computeDate)){
	 		throw new BusinessException("算薪周期和员工id不能为空");
		}
		 Example example = new Example(HrmsNewsalaryTemporaryAdjust.class);
		 Example.Criteria criteria = example.createCriteria();
		 criteria.andIn("employeeId", employeeids)
				 .andEqualTo("optionCycle",computeDate)
				 .andEqualTo(Contants.IS_DELETED_FIELD, "N");
		 List<HrmsNewsalaryTemporaryAdjust> records = mapper.selectByExample(example);
		 HrmsNewsalaryTemporaryAdjust newEntity = null;
		 int count= 0;
		 for(HrmsNewsalaryTemporaryAdjust entity : records){
			 newEntity = new HrmsNewsalaryTemporaryAdjust();
			 BeanUtil.copyProperties(entity,newEntity);
			 newEntity.setIsUse("1");
			 newEntity.setUpdateDate(new Date());
			 ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			 if (user != null) {
				 newEntity.setUpdateUser(user.getUsercode());
				 newEntity.setUpdateUserName(user.getUsername());
			 }
			 count += mapper.updateByPrimaryKey(newEntity);
			 //保存操作记录
			 newsalaryTemporaryAdjustChangeService.saveChanges(entity,newEntity,NewsalaryTemporaryAdjustOpTypeEnum.CALCULATE);
		 }
		 return count;
	 }

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryTemporaryAdjust record = selectById(id);
		if(record==null){
			throw new BusinessException("未找到需要删除的数据");
		}else {
			if("1".equals(record.getIsUse())){
				throw new BusinessException("人员所在方案已被锁定，无法进行删除操作");
			}
			HrmsEmployee employee = employeeService.findByEmployeeNo(record.getEmployeeNo());
			if(employee == null){
				throw new BusinessException("未找到员工数据");
			}
			record.setEmployeeName(employee.getEmployeeName());
			record.setEmployeeId(employee.getEmployeeId());
            HrmsNewsalaryTemporaryAdjust oldEntity = new HrmsNewsalaryTemporaryAdjust();
			BeanUtils.copyProperties(record,oldEntity);

			record.setUpdateDate(new Date());
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			record.setIsDeleted("Y");
            //保存操作记录
            newsalaryTemporaryAdjustChangeService.saveChanges(oldEntity,record,NewsalaryTemporaryAdjustOpTypeEnum.DELETE);
			return mapper.updateByPrimaryKey(record);
		}
	}

	@Override
	public HrmsNewsalaryTemporaryAdjust selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public List<HrmsNewsalaryTemporaryAdjust> getDataSetList(Page page, HrmsNewsalaryTemporaryAdjust record) {
	 	record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsNewsalaryTemporaryAdjust> records = mapper.getPageList(page,record);
		return records;
	}

	@Override
	public List<HrmsNewsalaryTemporaryAdjust> getList(HrmsNewsalaryTemporaryAdjust record) {
	 	record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsNewsalaryTemporaryAdjust> records = mapper.getPageList(record);
		return records;
	}


	/**
	 *
	 * @Title importEmployeeTemporary
	 * @Description 导入
	 * @return Map
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	@Transactional(readOnly = false)
	@Override
	public Map<String,Integer> importData(List<HrmsNewsalaryTemporaryAdjust> list){
		log.info("导入的数据:" + list);
		if(CollectionUtil.isEmpty(list)){
			throw new BusinessException("请至少添加一条数据");
		}
		//处理工作年限  性别 学历
		Map<String, String> tmpAdjustItemConvertDictMap = convertDictMap("tmp_adjust_item");  //性别
		int sucessNum = 0;
		for (HrmsNewsalaryTemporaryAdjust adjust : list) {

			if (StringUtils.isBlank(adjust.getTmpItemName()) == false) {//项目类型
				adjust.setTmpItem(tmpAdjustItemConvertDictMap.get(adjust.getTmpItemName().trim()));
			}
			if (StringUtils.isBlank(adjust.getCountType()) == false) {//计算类型
				adjust.setCountType("加项".equals(adjust.getCountType())?"1":"2");
			}
			adjust.setIsDeleted("N");
			adjust.setIsUse("2");
			try {
                sucessNum += save(adjust, NewsalaryTemporaryAdjustOpTypeEnum.IMPORT);
            }catch (Exception e){
			    e.printStackTrace();
            }
		}
		Map<String,Integer> map = new HashMap<>();
		map.put("successNum",sucessNum);
		map.put("errorNum",list.size() - sucessNum);
		return map;
	}

	private Map<String, String> convertDictMap(String dictType) {
		Assert.notNull(dictType, "dictType must not be null.");
		Map<String, String> map = Maps.newHashMap();
		List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
		if (CollectionUtils.isNotEmpty(dictItemList)) {
			for (DictItemResp d : dictItemList) {
				map.put(d.getItemName(),d.getItemNameValue());
			}
		}
		return map;
	}
}

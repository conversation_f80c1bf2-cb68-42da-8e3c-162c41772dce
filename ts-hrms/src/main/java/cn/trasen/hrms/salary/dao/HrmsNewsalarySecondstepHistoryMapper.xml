<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalarySecondstepHistoryMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalarySecondstepHistory">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="payroll_date" jdbcType="VARCHAR" property="payrollDate" />
    <result column="option_id" jdbcType="VARCHAR" property="optionId" />
    <result column="ydqk" jdbcType="VARCHAR" property="ydqk" />
    <result column="employee_no" jdbcType="VARCHAR" property="employeeNo" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="employee_status" jdbcType="VARCHAR" property="employeeStatus" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="val" jdbcType="VARCHAR" property="val" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>

    <select id="getData" resultType="cn.trasen.hrms.salary.model.HrmsNewsalarySecondstepHistory">
      SELECT * FROM hrms_newsalary_secondstep_history
      WHERE is_deleted='N' and option_id = #{optionId} and payroll_date = #{computeDate}
      <if test="employeeName != null and employeeName !=''">
        and ( employee_name like CONCAT('%',#{employeeName},'%') or
        employee_no like CONCAT('%',#{employeeName},'%') )
      </if>

      <if test='employeeStatus != null and employeeStatus.size() > 0'>
        and employee_status in
        <foreach collection="employeeStatus" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>

      <if test="orgName != null and orgName !=''">
        and  org_name like CONCAT('%',#{orgName},'%')
      </if>

      <if test="reason != null and reason !=''">
        and  ydqk like CONCAT('%',#{reason},'%')
      </if>

    </select>
</mapper>
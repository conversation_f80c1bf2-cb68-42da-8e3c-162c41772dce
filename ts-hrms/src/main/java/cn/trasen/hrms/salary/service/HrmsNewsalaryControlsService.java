package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryControls;

/**
 * @ClassName HrmsNewsalaryControlsService
 * @Description TODO
 * @date 2023��11��11�� ����4:33:46
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryControlsService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:33:46
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryControls record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:33:46
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryControls record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��11��11�� ����4:33:46
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryControls
	 * @date 2023��11��11�� ����4:33:46
	 * <AUTHOR>
	 */
	HrmsNewsalaryControls selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryControls>
	 * @date 2023��11��11�� ����4:33:46
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryControls> getDataSetList(Page page, HrmsNewsalaryControls record);
}

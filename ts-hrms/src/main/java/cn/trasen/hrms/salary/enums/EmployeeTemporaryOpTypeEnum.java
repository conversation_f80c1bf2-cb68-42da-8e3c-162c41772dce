package cn.trasen.hrms.salary.enums;

import lombok.Getter;

/**
 * 启用禁用枚举类
 */
@Getter
public enum EmployeeTemporaryOpTypeEnum {

    ADD("1", "新增"),
    UPDATE("2", "修改"),
    SYNC("3", "同步"),
    TRANSFER("4", "转档"),
    DIMISSION("5", "离职"),
    BATCHENTRY("6", "批量入职"),
    BATCHDIMISSION("7", "批量离职"),
    SALARYADJUSTMENT("8", "批量调薪"),
    EXPORT("9", "导出");


    private final String key;
    private final String val;

    private EmployeeTemporaryOpTypeEnum(String key, String val) {
        this.key = key;
        this.val = val;
    }

    /**
     * @Title: getValByKey
     * @Description: 根据key获得val值
     * @Param: key
     * @Return: String
     * <AUTHOR>
     */
    public static String getValByKey(String key) {
        for (EmployeeTemporaryOpTypeEnum item : EmployeeTemporaryOpTypeEnum.values()) {
            if (item.key.equals(key)) {
                return item.val;
            }
        }
        return "";
    }

    /**
     * @Title: getKeyByVal
     * @Description: 根据val获得key值
     * @param val
     * @Return String
     * <AUTHOR>
     * @date 2020年6月17日 下午5:30:51
     */
    public static String getKeyByVal(String val) {
        for (EmployeeTemporaryOpTypeEnum item : EmployeeTemporaryOpTypeEnum.values()) {
            if (item.val.equals(val)) {
                return item.key;
            }
        }
        return "";
    }
}

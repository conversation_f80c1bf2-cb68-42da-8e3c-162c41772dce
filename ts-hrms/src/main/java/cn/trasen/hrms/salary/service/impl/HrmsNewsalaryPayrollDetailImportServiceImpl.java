package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.DTO.UpdateSalaryVo;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryOptionPayrollMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryPayrollDetailImportMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmp;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailImport;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionEmpService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionPayrollService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryPayrollDetailImportService;
import cn.trasen.hrms.utils.IdUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName HrmsNewsalaryPayrollDetailImportServiceImpl
 * @Description TODO
 * @date 2024��3��1�� ����4:35:33
 * <AUTHOR>
 * @version 1.0
 */
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryPayrollDetailImportServiceImpl implements HrmsNewsalaryPayrollDetailImportService {

	@Autowired
	private HrmsNewsalaryPayrollDetailImportMapper mapper;
	@Autowired
	HrmsNewsalaryItemService hrmsNewsalaryItemService;
	@Autowired
	HrmsNewsalaryOptionEmpService hrmsNewsalaryOptionEmpService;

	@Autowired
	private HrmsNewsalaryOptionPayrollService hrmsNewsalaryOptionPayrollService;
	@Autowired
	private HrmsNewsalaryOptionPayrollMapper payrollMapper;

//	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryPayrollDetailImport record) {
		if(StringUtil.isEmpty(record.getId())){
			record.setId(IdUtil.getId());
		}
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	//导入薪酬数据
//	@Transactional(readOnly = false)
	@Override
	public Integer importSave(List<Map<String, String>> data,String optionId,String sendDate) {
		Assert.hasText(optionId, "薪酬方案id不能为空.");
		Assert.hasText(sendDate, "导入月份不能为空");
		Map<String,String> importTitleMap = new HashMap<>();
		importTitleMap = data.get(0);
		if(!importTitleMap.containsKey("工号")){
			throw new BusinessException("导入文件必须包含工号列");
		}


		//拿到所有的导入项目
		List<HrmsNewsalaryItem> itemByOptionId = hrmsNewsalaryItemService.getItemByOptionId(optionId);
		//验证表头完整性
	/*	Map<String, String> itemSalaryMap = new HashMap<>();
		for (HrmsNewsalaryItem item : itemByOptionId) {
			if("1".equals(item.getItemRule())){
				itemSalaryMap.put(item.getItemName(), item.getId());
			}
		}*/

		//拿到人所在的薪酬组
		List<String> empNo = new ArrayList<>();
		for (int i = 0; i < data.size(); i++) {
			empNo.add(data.get(i).get("工号"));
		}

		List<HrmsNewsalaryOptionEmp> oprionIdByEmpNo = hrmsNewsalaryOptionEmpService.getOprionIdByEmpNo(empNo);
		Map<String, String> empMap = oprionIdByEmpNo.stream()
				.collect(Collectors.toMap(
						HrmsNewsalaryOptionEmp::getEmployeeNo, // 作为键的函数
						HrmsNewsalaryOptionEmp::getOptionId, // 作为值的函数，这里直接使用元素本身
						(existingValue, newValue) -> newValue // 如果有重复的键，则使用旧的值
				));

		// 取到 所有的薪酬项目 转成 map
		List<HrmsNewsalaryItem> itemMap = hrmsNewsalaryItemService.getALLByMap();

		Map<String, Map<String, String>> groupedMap = itemMap.stream()
				.collect(Collectors.groupingBy(
						HrmsNewsalaryItem::getOptionId, // Group by employee number
						Collectors.toMap( // For each employee, create a map of salary items
								HrmsNewsalaryItem::getItemName, // The salary item name as the key
								HrmsNewsalaryItem::getId, // The value of the salary item as the value
								(existingValue, newValue) -> existingValue // If there's a duplicate key, keep the existing value
						)
				));


		//表头没问题插入数据
		for (int i = 1; i < data.size(); i++) {
			HrmsNewsalaryPayrollDetailImport record = new HrmsNewsalaryPayrollDetailImport();
			Map<String, String> _empData = data.get(i);
			record.setOptionId(empMap.get(_empData.get("工号")));  //方案id
			record.setImportDate(sendDate);
			record.setEmployeeNo(_empData.get("工号"));
			_empData.remove("工号");
			record.setRemark(_empData.get("姓名"));
			_empData.remove("姓名");
			_empData.forEach((key, value) -> {
				if(!StringUtil.isEmpty(groupedMap.get(record.getOptionId()).get(key))){
					record.setId(IdUtil.getId());
					// record.setItemId(itemSalaryMap.get(key));
					record.setItemId(groupedMap.get(record.getOptionId()).get(key));
					record.setItemName(key);
					if(StringUtils.isNotBlank(value)) {
						record.setSalary(new BigDecimal(value));
					}else{
						record.setSalary(BigDecimal.ZERO);
					}
					//加一个先删除数据的方案
					deleteByObj(record);
					save(record);
				}
			});
		}
		//计算过重新导入时直接计算反显数据
		/*HrmsNewsalaryOptionPayroll hnop = new HrmsNewsalaryOptionPayroll();
		hnop.setOptionId(optionId);
		hnop.setComputeDate(sendDate);
		HrmsNewsalaryOptionPayroll completeInfo = hrmsNewsalaryOptionPayrollService.getCalculationStatus(hnop);
		String computeStatus = completeInfo.getComputeStatus();
		if(StrUtil.equals("1",computeStatus)){
			hrmsNewsalaryOptionPayrollService.startCalculation(hnop);
		}*/
		return data.size() - 1;
	}

	private Integer deleteByObj(HrmsNewsalaryPayrollDetailImport record){
		Example example = new Example(HrmsNewsalaryPayrollDetailImport.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeNo", record.getEmployeeNo());
		criteria.andEqualTo("itemId", record.getItemId());
		criteria.andEqualTo("importDate", record.getImportDate());
		return mapper.deleteByExample(example);
	}

	//导出手工录入模版
	@Override
	public List<HrmsNewsalaryItem> exportTemplate(String optionId) {
		return hrmsNewsalaryItemService.getItemByOptionId(optionId);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryPayrollDetailImport record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryPayrollDetailImport record = new HrmsNewsalaryPayrollDetailImport();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	/**
	 * 根据方案id和核算月份清除导入项数据
	 * @param optionId
	 * @param sendDate
	 * @return
	 */
	@Override
	public Integer cleanImportDataByOptionId(String optionId,String sendDate){
		Example example = new Example(HrmsNewsalaryPayrollDetailImport.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("optionId", optionId);
		criteria.andEqualTo("importDate",sendDate);
		return mapper.deleteByExample(example);
	}

	@Override
	public HrmsNewsalaryPayrollDetailImport selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	//修改手动导入的工资项
	@Override
//	@Transactional(readOnly = false)
	public Integer updateSalaryList(List<UpdateSalaryVo> updateSalaryList) {
		if(null != updateSalaryList && !updateSalaryList.isEmpty()){
			for (int i = 0; i < updateSalaryList.size(); i++) {
				if("1".equals(updateSalaryList.get(i).getItemRule())){
					String newSalary = updateSalaryList.get(i).getSalary();

					Example example = new Example(HrmsNewsalaryPayrollDetailImport.class);
					Example.Criteria criteria = example.createCriteria();
					criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
					criteria.andEqualTo("employeeNo", updateSalaryList.get(i).getEmployeeNo());
					criteria.andEqualTo("itemId", updateSalaryList.get(i).getItemId());
					criteria.andEqualTo("optionId", updateSalaryList.get(i).getOptionId());
					criteria.andEqualTo("importDate", updateSalaryList.get(i).getPayrollDate());

					List<HrmsNewsalaryPayrollDetailImport> getList = mapper.selectByExample(example);
					if(null != getList && getList.size() > 0){
						HrmsNewsalaryPayrollDetailImport oldBean = getList.get(0);
						oldBean.setSalary(new BigDecimal(updateSalaryList.get(i).getSalary()));
						mapper.updateByPrimaryKeySelective(oldBean);
					}else{
						HrmsNewsalaryPayrollDetailImport inserBean = new HrmsNewsalaryPayrollDetailImport();
						inserBean.setEmployeeNo(updateSalaryList.get(i).getEmployeeNo());
						inserBean.setOptionId(updateSalaryList.get(i).getOptionId());
						inserBean.setImportDate(updateSalaryList.get(i).getPayrollDate());
						inserBean.setItemId(updateSalaryList.get(i).getItemId());
						inserBean.setItemName(updateSalaryList.get(i).getItemName());
						inserBean.setSalary(new BigDecimal(updateSalaryList.get(i).getSalary()));
						save(inserBean);
					}
				}
			}
		}
		return 1;
	}

	@Override
	public DataSet<HrmsNewsalaryPayrollDetailImport> getDataSetList(Page page, HrmsNewsalaryPayrollDetailImport record) {
		Example example = new Example(HrmsNewsalaryPayrollDetailImport.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalaryPayrollDetailImport> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsNewsalaryItem> exportTemplateAll(String optionId) {
		return mapper.exportTemplateAll(optionId);
	}

	@Override
	public List<HrmsNewsalaryItem> exportSalaryItemByOptionId(String optionId,String computeDate) {
		return mapper.exportSalaryItemByOptionId(optionId,computeDate);
	}

	@Override
	public void checkManualSalary(String optionId, String computeDate) {
		boolean manual = hrmsNewsalaryItemService.checkManual(optionId);
		if(manual){
			Example example = new Example(HrmsNewsalaryPayrollDetailImport.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo("optionId",optionId);
			criteria.andEqualTo(Contants.IS_DELETED_FIELD,Contants.IS_DELETED_FALSE);
			criteria.andEqualTo("importDate",computeDate);
			List<HrmsNewsalaryPayrollDetailImport> list = mapper.selectByExample(example);
			if(CollUtil.isEmpty(list)){
				throw new BusinessException("未导入手工录入工资项!");
			}
		}
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public PlatformResult<String> importAllSave(List<Map<String, String>> data, String sendDate) {
		Assert.hasText(sendDate, "导入月份不能为空");
		Map<String,String> importTitleMap = new HashMap<>();
		importTitleMap = data.get(0);
		if(!importTitleMap.containsKey("工号")){
			throw new BusinessException("导入文件必须包含工号列");
		}

		StringBuilder message = new StringBuilder();
		//拿到人所在的薪酬组
		List<String> empNo = new ArrayList<>();
		for (int i = 0; i < data.size(); i++) {
			empNo.add(data.get(i).get("工号"));
		}

		List<HrmsNewsalaryOptionEmp> oprionIdByEmpNo = hrmsNewsalaryOptionEmpService.getOprionIdByEmpNo(empNo);
		Map<String, String> empMap = oprionIdByEmpNo.stream()
				.collect(Collectors.toMap(
						HrmsNewsalaryOptionEmp::getEmployeeNo, // 作为键的函数
						HrmsNewsalaryOptionEmp::getOptionId, // 作为值的函数，这里直接使用元素本身
						(existingValue, newValue) -> newValue // 如果有重复的键，则使用旧的值
				));

		// 取到 所有的薪酬项目 转成 map
		List<HrmsNewsalaryItem> itemMap = hrmsNewsalaryItemService.getALLByMap();

		Map<String, Map<String, String>> groupedMap = itemMap.stream()
				.collect(Collectors.groupingBy(
						HrmsNewsalaryItem::getOptionId, // Group by employee number
						Collectors.toMap( // For each employee, create a map of salary items
								HrmsNewsalaryItem::getItemName, // The salary item name as the key
								HrmsNewsalaryItem::getId, // The value of the salary item as the value
								(existingValue, newValue) -> existingValue // If there's a duplicate key, keep the existing value
						)
				));


		//表头没问题插入数据
		for (int i = 1; i < data.size(); i++) {
			HrmsNewsalaryPayrollDetailImport record = new HrmsNewsalaryPayrollDetailImport();
			Map<String, String> _empData = data.get(i);
			record.setOptionId(empMap.get(_empData.get("工号")));  //方案id
			record.setImportDate(sendDate);
			record.setEmployeeNo(_empData.get("工号"));
			_empData.remove("工号");
			record.setRemark(_empData.get("姓名"));
			_empData.remove("姓名");
			for (Map.Entry entry:_empData.entrySet()){
				if(Objects.nonNull(groupedMap.get(record.getOptionId())) && !StringUtil.isEmpty(groupedMap.get(record.getOptionId()).get(entry.getKey()))){
					Example example3 = new Example(HrmsNewsalaryOptionPayroll.class);
					Example.Criteria criteria3 = example3.createCriteria();
					criteria3.andEqualTo(Contants.IS_DELETED_FIELD, "N");
					criteria3.andEqualTo("optionId", record.getOptionId());
					criteria3.andEqualTo("computeDate", record.getImportDate());
					HrmsNewsalaryOptionPayroll payroll = payrollMapper.selectOneByExample(example3);
					if (Objects.nonNull(payroll) && Objects.equals(payroll.getComputeStatus(),"3")){
						if (message.toString().contains(record.getRemark())){
							continue;
						}
						message.append(record.getRemark());
						message.append(",");
						continue;
					}
					record.setId(IdUtil.getId());
					record.setItemId(groupedMap.get(record.getOptionId()).get(entry.getKey()));
					record.setItemName(entry.getKey().toString());
					record.setSalary(new BigDecimal(entry.getValue().toString()));
					//加一个先删除数据的方案
					deleteByObj(record);
					save(record);
				}
			}

		}
		if (Strings.isNotEmpty(message)){
			message.append("已经锁定无法导入！(其他人员导入)");
			String concat = message.toString().substring(0, message.toString().lastIndexOf(",")).concat(message.toString().substring(message.toString().lastIndexOf(",") + 1));
			return PlatformResult.success(concat);
		}
		return PlatformResult.success();
	}
}

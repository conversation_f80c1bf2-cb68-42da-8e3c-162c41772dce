package cn.trasen.hrms.salary.controller;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayslip;
import cn.trasen.hrms.salary.service.IHrmsNewsalaryPayslipService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Api(tags = "工资条模板Controller")
public class HrmsNewsalaryPayslipController {

    private static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryPayslipController.class);

    @Autowired
    private IHrmsNewsalaryPayslipService hrmsNewsalaryPayslipService;

    @ApiOperation(value = "根据薪酬方案获取工资条模板", notes = "根据薪酬方案获取工资条模板")
    @GetMapping("/api/salaryPayslip/getPayslip/{optionId}")
    public PlatformResult<HrmsNewsalaryPayslip> getPayslip(@PathVariable String optionId) {
        try {
            HrmsNewsalaryPayslip paySlip = hrmsNewsalaryPayslipService.getPayslip(optionId);
            return PlatformResult.success(paySlip);
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "工资条模板编辑", notes = "工资条模板编辑")
    @PostMapping("/api/salaryPayslip/save")
    public PlatformResult save(@RequestBody HrmsNewsalaryPayslip payslip) {
        try {
            hrmsNewsalaryPayslipService.save(payslip);
            return PlatformResult.success();
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "工资条模板列表", notes = "工资条模板列表")
    @GetMapping("/api/salaryPayslip/list")
    public DataSet<HrmsNewsalaryPayslip> list(Page page, HrmsNewsalaryPayslip payslip) {
            return hrmsNewsalaryPayslipService.getDataList(page,payslip);
        }

    @ApiOperation(value = "工资条模板启用", notes = "工资条模板启用")
    @PostMapping("/api/salaryPayslip/enable")
    public PlatformResult enable(@RequestBody List<String> ids) {
        if(CollUtil.isEmpty(ids)){
            return PlatformResult.failure("ids不能为空!");
        }
         hrmsNewsalaryPayslipService.enable(ids);
         return PlatformResult.success();
     }

    @ApiOperation(value = "工资条模板禁用", notes = "工资条模板禁用")
    @PostMapping("/api/salaryPayslip/disEnable")
    public PlatformResult disEnable(@RequestBody List<String> ids) {
        if(CollUtil.isEmpty(ids)){
            return PlatformResult.failure("ids不能为空!");
        }
        hrmsNewsalaryPayslipService.disEnable(ids);
        return PlatformResult.success();
    }

    @ApiOperation(value = "工资条模板新增（废弃）", notes = "工资条模板新增")
    @PostMapping("/api/salaryPayslip/add")
    public PlatformResult add(@RequestBody HrmsNewsalaryPayslip payslip) {
        try {
            hrmsNewsalaryPayslipService.add(payslip);
            return PlatformResult.success();
        }catch (Exception e){
        logger.error(e.getMessage(), e);
        return PlatformResult.failure(e.getMessage());
     }
    }

    @ApiOperation(value = "工资条模板删除", notes = "工资条模板删除")
    @PostMapping("/api/salaryPayslip/delete")
    public PlatformResult delete(@RequestBody HrmsNewsalaryPayslip payslip) {
        hrmsNewsalaryPayslipService.delete(payslip);
        return PlatformResult.success();
      }
    }

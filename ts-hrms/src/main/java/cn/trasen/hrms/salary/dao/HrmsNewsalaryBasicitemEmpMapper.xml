<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryBasicitemEmpMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="basic_item_id" jdbcType="VARCHAR" property="basicItemId" />
    <result column="emp_field" jdbcType="VARCHAR" property="empField" />
    <result column="salary_amount" jdbcType="DECIMAL" property="salaryAmount" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="effective_date" jdbcType="VARCHAR" property="effectiveDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />

    <result column="count_formula" jdbcType="VARCHAR" property="countFormula" />
    <result column="custom_rule" jdbcType="VARCHAR" property="customRule" />
    <result column="item_rule" jdbcType="VARCHAR" property="itemRule" />
    <result column="employee_name" jdbcType="TIMESTAMP" property="employeeName" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="count_formula_text" jdbcType="VARCHAR" property="countFormulaText" />
    <result column="next_month" jdbcType="CHAR" property="nextMonth" />
    <result column="count_type" jdbcType="VARCHAR" property="countType" />

  </resultMap>

    <!-- 设置人员已定薪 -->
    <update id="updateSalaryAppoint">
        UPDATE cust_emp_info SET salary_appoint = 1
        WHERE  info_id=#{employeeId}
    </update>
    <update id="updateEmployeeSalary">
        UPDATE cust_emp_info
        <set>
            <if test="plgw != null">
                plgw = #{plgw},
            </if>
            <if test="gwdj != null">
               gwdj = #{gwdj},
            </if>
            <if test="salaryLevelType != null">
                salary_level_type = #{salaryLevelType},
            </if>
            <if test="salaryLevelId != null">
                salary_level_id = #{salaryLevelId},
            </if>
        </set>
        WHERE info_id = #{employeeId}
    </update>
	<insert id="upsertSalaryBasicEmp">
		INSERT INTO hrms_newsalary_basicitem_emp (id, employee_id,basic_item_id, emp_field, salary_amount,reason,effective_date,remark,create_date, create_user,
		create_user_name,update_date, update_user, update_user_name, is_deleted, sso_org_code, sso_org_name,basic_item_type
		,emp_field_value,emp_field_value_text)
        VALUES (#{id}, #{employeeId}, #{basicItemId}, #{empField},#{salaryAmount},#{reason},#{effectiveDate},#{remark},#{createDate},#{createUser},#{createUserName},
        #{updateDate},#{updateUser},#{updateUserName},#{isDeleted},#{ssoOrgCode},#{ssoOrgName},#{basicItemType},#{empFieldValue}
		,#{empFieldValueText})
        ON DUPLICATE KEY UPDATE
        emp_field = VALUES(emp_field),
        salary_amount = VALUES(salary_amount),
        reason = VALUES(reason),
        effective_date = VALUES(effective_date),
        remark = VALUES(remark),
        update_user = VALUES(update_user),
        update_date = VALUES(update_date),
        update_user_name = VALUES(update_user_name),
        is_deleted = VALUES(is_deleted),
        sso_org_code = VALUES(sso_org_code),
        sso_org_name = VALUES(sso_org_name),
        basic_item_type = VALUES(basic_item_type),
        emp_field_value = VALUES(emp_field_value),
        emp_field_value_text = VALUES(emp_field_value_text)
	</insert>

    <select id="getBaseDataByEmpNo" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT DISTINCT t1.employee_id,t1.employee_no,t1.employee_name,
               IF(t1.gender =0,'男','女') AS gender,
               t1.birthday,t1.emp_age,t1.identity_number,
               t2.name AS orgName,
               t2.name AS org_name,
               t1.personal_identity ,
               t1.position_id,
               t1.establishment_type,
               t1.employee_status,
               t1.year_work,t1.positive_time,
               DATE_FORMAT(t1.entry_date, '%Y-%m-%d') as  entry_date,
               a5.position_name AS positionName,
               t3.ITEM_NAME AS establishmentTypeName,
               t4.ITEM_NAME AS employeeStatusName,
               t5.ITEM_NAME AS personalIdentityName,
               t7.ITEM_NAME AS educationTypeName,
               t8.ITEM_NAME AS genderText,
               t1.retirement_time,
               t1.bankcardname,
               t1.bankcardno,
               a4.education_type_name as educationTypeName,
               t1.salary_appoint,
               t1.is_temp
        FROM (
                SELECT
                  employee_name,employee_no,org_id,employee_id,gender,birthday,
                  employee_status,positive_time,retirement_time,
                  position_id,year_work,bankcardname,emp_age,
                  establishment_type,salary_appoint,is_deleted,
                  personal_identity,identity_number,bankcardno,
                  entry_date,'N' as is_temp
                FROM
                    cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
                UNION ALL
                SELECT
                    employee_name,employee_no,org_id,id as employee_id,gender,birthday,
                    (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
                    null as position_id,null as year_work,null as bankcardname,null as emp_age,
                    tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
                    tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
                    join_date as entry_date,'Y' as is_temp
                FROM
                    hrms_employee_temporary et where et.is_sync_salary = 'Y'
            ) t1
             LEFT JOIN comm_organization t2 ON t1.`org_id`=t2.`organization_id`
             LEFT JOIN comm_position AS a5  ON t1.position_id=a5.position_id
             LEFT JOIN (
                SELECT
                  A.*
                FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                WHERE
                  B.TYPE_CODE = 'establishment_type'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
             ) t3 ON t1.establishment_type = t3.ITEM_CODE
             LEFT JOIN (
                SELECT
                  A.*
                FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                WHERE
                  B.TYPE_CODE = 'employee_status'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
             ) t4 ON t1.employee_status = t4.ITEM_CODE
             LEFT JOIN (
                SELECT
                  A.*
                FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                WHERE
                  B.TYPE_CODE = 'personal_identity'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
             ) t5 ON t1.personal_identity = t5.ITEM_CODE
             LEFT JOIN  hrms_education_info t6 ON t6.employee_id=t1.employee_id AND t6.highest_level='1' AND t6.is_deleted='N'
             LEFT JOIN (
                SELECT
                  A.*
                FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                WHERE
                  B.TYPE_CODE = 'education_type'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
             ) t7 ON t6.education_type = t7.ITEM_CODE
             LEFT JOIN (
                SELECT
                  A.*
                FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                WHERE
                  B.TYPE_CODE = 'SEX'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
             ) t8 ON  t1.gender = t8.ITEM_CODE
             LEFT JOIN (
            SELECT ed.employee_id,
                   ed.school_name,ed.education_type,dict.ITEM_NAME AS education_type_name ,ed.start_time,ed.end_time,ed.professional,
                   ed.degree_number,ed.certificate_number
            FROM hrms_education_info ed
                     LEFT JOIN (
                SELECT
                  A.*
                FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                WHERE
                  B.TYPE_CODE = 'education_type'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
             ) dict ON ed.education_type = dict.item_code
            WHERE ed.is_deleted = 'N'   AND ed.highest_level=1  GROUP BY ed.employee_id
        )a4 ON a4.employee_id = t1.employee_id
        WHERE t1.is_deleted='N' and t1.employee_no=#{employeeNo} LIMIT 1

    </select>

    <select id="getBaseData" resultType="java.util.Map" parameterType="java.lang.String">

    SELECT DISTINCT t1.employee_id,t1.employee_no,t1.employee_name,
           IF(t1.gender =0,'男','女') AS gender,
            t1.birthday,t1.emp_age,t1.identity_number,
           t2.name AS orgName,
           t2.name AS org_name,
           t1.personal_identity ,
           t1.position_id,
           t1.establishment_type,
           t1.employee_status,
           t1.year_work,t1.positive_time,
           DATE_FORMAT(t1.entry_date, '%Y-%m-%d') as  entry_date,
           a5.position_name AS positionName,
           t3.ITEM_NAME AS establishmentTypeName,
           t4.ITEM_NAME as employeeStatusName,
           t5.ITEM_NAME AS personalIdentityName,
           t7.ITEM_NAME AS educationTypeName,
           t8.ITEM_NAME AS genderText,
           t1.retirement_time,
           t1.bankcardname,
           t1.bankcardno,
           a4.education_type_name as educationTypeName,
           t1.is_temp as isTemp
    FROM (
       SELECT
          employee_name,employee_no,org_id,employee_id,gender,birthday,
          employee_status,positive_time,retirement_time,
          position_id,year_work,bankcardname,emp_age,
          establishment_type,salary_appoint,is_deleted,
          personal_identity,identity_number,bankcardno,
          entry_date,'N' as is_temp
        FROM
            cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
        UNION ALL
        SELECT
            employee_name,employee_no,org_id,id as employee_id,gender,birthday,
            (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
            null as position_id,null as year_work,null as bankcardname,null as emp_age,
            tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
            tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
            join_date as entry_date,'Y' as is_temp
        FROM
            hrms_employee_temporary et where et.is_sync_salary = 'Y'
            ) t1
           LEFT JOIN comm_organization t2 ON t1.`org_id`=t2.`organization_id`
           LEFT JOIN comm_position AS a5  ON t1.position_id=a5.position_id
           LEFT JOIN (
                SELECT
                  A.*
                FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                WHERE
                  B.TYPE_CODE = 'establishment_type'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
             ) t3 ON t1.establishment_type = t3.ITEM_CODE
           LEFT JOIN (
                SELECT
                  A.*
                FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                WHERE
                  B.TYPE_CODE = 'employee_status'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
             ) t4 ON t1.employee_status = t4.ITEM_CODE
           LEFT JOIN (
                SELECT
                  A.*
                FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                WHERE
                  B.TYPE_CODE = 'personal_identity'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
             ) t5 ON t1.personal_identity = t5.ITEM_CODE
           LEFT JOIN  hrms_education_info t6 ON t6.employee_id=t1.employee_id AND t6.highest_level='1' AND t6.is_deleted='N'
           LEFT JOIN (
                SELECT
                  A.*
                FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                WHERE
                  B.TYPE_CODE = 'education_type'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
             ) t7 ON  t6.education_type = t7.ITEM_CODE
           LEFT JOIN (
                SELECT
                  A.*
                FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                WHERE
                  B.TYPE_CODE = 'SEX'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
             ) t8 ON  t1.gender = t8.ITEM_CODE
           LEFT JOIN (
             SELECT ed.employee_id,
                dict.ITEM_NAME AS education_type_name
                FROM hrms_education_info ed
                LEFT JOIN (
                SELECT
                  A.*
                FROM
                  COMM_DICT_ITEM A
                  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                WHERE
                  B.TYPE_CODE = 'education_type'
                  AND B.IS_DELETED = 'N'
                  AND A.IS_DELETED = 'N'
                  AND A.IS_ENABLE = '1'
                  AND A.sso_org_code = #{ssoOrgCode}
             ) dict ON ed.education_type = dict.item_code
                WHERE ed.is_deleted = 'N'   AND ed.highest_level=1  GROUP BY ed.employee_id,dict.ITEM_NAME
            )a4 ON a4.employee_id = t1.employee_id
    WHERE t1.is_deleted='N' and t1.employee_id=#{id} LIMIT 1

  </select>


    <select id="getDataByEmployeeId" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp" parameterType="java.lang.String">
        SELECT t1.*,t2.basic_item_name,t2.basic_item_type as basicItemType,t2.custom_rule as customRule   FROM hrms_newsalary_basicitem_emp t1
        LEFT JOIN hrms_newsalary_basic_column t2 ON t1.basic_item_id = t2.id
        WHERE t1.is_deleted='N' AND  t1.employee_id=#{employeeId}
        order by t2.number_sort asc
    </select>

    <!-- 根据岗位等级id查询岗位岗位 -->
    <select id="getGwdjText" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT
            distinct post.post_name
        FROM comm_post_wage wage
                 LEFT JOIN comm_post post ON post.post_id = wage.post_id
        WHERE wage.is_deleted = 'N'
          AND wage.post_id = #{id}
    </select>
    <!-- 根据薪级等级id查询薪级等级 -->
    <select id="getSalaryLeavelText" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT
           distinct sl.salary_level_name
        FROM hrms_salary_level_wage wage
                 LEFT JOIN hrms_salary_level sl ON sl.salary_level_id = wage.salary_level_id
        WHERE wage.is_deleted = 'N'
          AND wage.salary_level_id  = #{id}
    </select>
	<!-- 岗位等级及工资 -->
	<select id="selectPostWage" resultType="cn.trasen.hrms.model.HrmsPostWageVo">
		select post_wage_id, a.post_id, post_name, post_category , post_wage, performance_wage,award_wage,b.policy_standard_id
		from comm_post A left join comm_post_wage B on A.post_id = B.post_id and A.is_enable=b.is_enable 
		and a.is_deleted = b.is_deleted   
		where a.is_enable = 1 and a.is_deleted = 'N'
	</select>
	<!-- 薪资等级及工资 -->
	<select id="selectSalaryLevelWage" resultType="cn.trasen.hrms.model.HrmsSalaryLevelWageVo">
		select salary_level_wage_id, a.salary_level_id, salary_level_wage, b.salary_level_name, salary_level_category, grade ,a.policy_standard_id
		from  hrms_salary_level b left join hrms_salary_level_wage a on a.salary_level_id = b.salary_level_id 
		and a.is_enable = b.is_enable and a.is_deleted = b.is_deleted 
		where a.is_enable = 1 and a.is_deleted = 'N'
	</select>
    <select id="getGwdjCode"  resultType="java.lang.String">
        SELECT
            post_id
        FROM
            comm_post
        WHERE
            is_deleted = 'N' AND is_enable = '1'
          AND post_category =#{plgwCode}
          AND post_name = #{gwdjText}
    </select>
    <select id="getSalaryLevelId" resultType="java.lang.String">
        SELECT salary_level_id FROM hrms_salary_level WHERE
            is_deleted = 'N'
            AND salary_level_category =#{salaryLevelTypeCode}
            AND salary_level_name=#{salaryLevelIdText}

    </select>

    <select id="getGwSalary" resultType="java.lang.String">
        SELECT
            wage.post_wage
        FROM comm_post_wage wage
                 LEFT JOIN comm_post post ON post.post_id = wage.post_id
        WHERE wage.is_deleted = 'N'
          AND  post.post_category =#{plgwCode}
          AND wage.post_id = #{gwdjCode}
    </select>
    <select id="getJxSalary" resultType="java.lang.String">
        SELECT
            wage.performance_wage
        FROM comm_post_wage wage
                 LEFT JOIN comm_post post ON post.post_id = wage.post_id
        WHERE wage.is_deleted = 'N'
          AND  post.post_category =#{plgwCode}
          AND wage.post_id = #{gwdjCode}

    </select>
    <select id="getSalaryItemByOptionId" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp" parameterType="java.lang.String">
    	select a.* from hrms_newsalary_basicitem_emp a 
			inner join hrms_newsalary_option_emp b on a.employee_id = b.employee_id and b.option_id = #{optionId} and is_deleted='N';
    </select>

	<select id="getSalaryOptionEmp" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp" parameterType="java.lang.String">
		select a.*  ,c.count_formula,c.custom_rule, c.item_name,
			c.count_Formula_Text,c.item_rule,c.next_month,c.count_type, c.carry_rule,c.item_digit
		from hrms_newsalary_basicitem_emp a
		<if test="optionId !=null and optionId != ''">
		left join hrms_newsalary_option_emp b on a.employee_id = b.employee_id and a.is_deleted=b.is_deleted
		</if>
		left join hrms_newsalary_item_library c on a.emp_field = c.id and a.is_deleted = c.is_deleted and c.status = 1
		where  a.employee_id = #{employeeId}
		<if test="optionId !=null and optionId != ''">
			and b.option_id = #{optionId}
		</if>
		and a.is_deleted='N'  and (c.item_rule is not null or a.emp_field = 'policy_standard_id')
        order by a.effective_date desc
	</select>

	<select id="getSalaryOptionEmpEffDate" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp" parameterType="java.lang.String">
		select a.*  ,c.count_formula,c.custom_rule, c.item_name,
			c.count_Formula_Text,c.item_rule,c.next_month,c.count_type, c.carry_rule,c.item_digit
		from (select * from hrms_newsalary_basicitem_emp_history where employee_id =  #{employeeId}
            and (employee_id,emp_field, create_date) in
                (select employee_id,emp_field,max(create_date) as create_date from hrms_newsalary_basicitem_emp_history
                    where effective_Date <![CDATA[ <= ]]> #{effectiveDate} group by employee_id,emp_field)) a
		<if test="optionId !=null and optionId != ''">
		left join hrms_newsalary_option_emp b on a.employee_id = b.employee_id and a.is_deleted=b.is_deleted
		</if>
		left join hrms_newsalary_item_library c on a.emp_field = c.id and a.is_deleted = c.is_deleted and c.status = 1
		where  a.employee_id = #{employeeId} and a.effective_Date <![CDATA[ <= ]]> #{effectiveDate}
		<if test="optionId !=null and optionId != ''">
			and b.option_id = #{optionId}
		</if>
		and a.is_deleted='N'  and (c.item_rule is not null or a.emp_field = 'policy_standard_id')
	</select>
	<select id = "getEffectiveDate" resultType = "java.lang.String" parameterType="java.lang.String">
		select max(t.effective_date) from hrms_newsalary_basicitem_emp_history t
			where employee_id = #{employeeId}
			<![CDATA[ 
			and effective_date <= #{effectiveDate}
			]]>
	</select>

	<select id="getSalaryItemLibrary" resultType = "cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
		select * from hrms_newsalary_item_library 
	</select>
    <select id="getNewsalaryDataByEmployeeId"
            resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp">
        SELECT t1.*,t2.basic_item_name,t2.basic_item_type as basicItemType,t2.custom_rule as customRule FROM hrms_newsalary_basicitem_emp t1
        LEFT JOIN hrms_newsalary_basic_column t2 ON t1.basic_item_id = t2.id
        WHERE t1.is_deleted='N' AND  t1.employee_id=#{employeeId} and t1.emp_field in('plgw','salary_level_type','salary_level_id','gwdj','policy_standard_id')
        order by t2.number_sort asc
    </select>
    <select id="getEmpIds" resultType="java.lang.String">
    	select distinct employee_id from hrms_newsalary_basicitem_emp
    </select>
</mapper>
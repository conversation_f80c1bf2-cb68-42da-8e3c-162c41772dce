package cn.trasen.hrms.salary.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 考勤记录表
 * @TableName hrms_upload_record
 */
@Table(name = "hrms_upload_record")
@Setter
@Getter
public class HrmsUploadRecordEo {
    /**
     * 主键ID
     */
    @Id
    private String recordId;

    /**
     * 员工ID
     */
    private String employeeId;

    /**
     * 员工工号
     */
    private String employeeNo;

    /**
     * 上报月份
     */
    private String uploadDate;

    /**
     * 审核状态(0待审核)
     */
    private String approvalStatus;

    /**
     * 备注
     */
    private String remark;
    

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建者ID
     */
    private String createUser;

    /**
     * 创建者姓名
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新者ID
     */
    private String updateUser;

    /**
     * 更新者姓名
     */
    private String updateUserName;

    /**
     * 组织机构ID
     */
    private String orgId;

    /**
     * 组织机构名称
     */
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    private String isDeleted;

    /**
     * 属于哪个科室
     */
    private String belongOrg;

    /**
     * 科室审核时间
     */
    private Date deptCheckDate;

    /**
     * 科室审核人
     */
    private String deptCheckUsername;

    /**
     * 科室审核人id
     */
    private String deptCheckUserid;

    /**
     * 
     */
    private String ssoOrgCode;

    /**
     * 
     */
    private String ssoOrgName;

    private static final long serialVersionUID = 1L;
}
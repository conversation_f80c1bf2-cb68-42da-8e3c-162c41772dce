package cn.trasen.hrms.salary.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.CommTableSnapshotSaveReq;
import cn.trasen.homs.feign.base.CommTableSnapshotFeignService;
import cn.trasen.hrms.model.HrmsSalaryLevel;
import cn.trasen.hrms.salary.enums.EnableEnum;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayslip;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.dao.HrmsNewsalarySeniorityWageDispositionMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalarySeniorityWage;
import cn.trasen.hrms.salary.model.HrmsNewsalarySeniorityWageDisposition;
import cn.trasen.hrms.salary.service.HrmsNewsalarySeniorityWageDispositionService;
import cn.trasen.hrms.salary.service.HrmsNewsalarySeniorityWageService;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsNewsalarySeniorityWageDispositionServiceImpl
 * @Description TODO
 * @date 2024��4��12�� ����11:06:21
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalarySeniorityWageDispositionServiceImpl implements HrmsNewsalarySeniorityWageDispositionService {

	@Autowired
	private HrmsNewsalarySeniorityWageDispositionMapper mapper;

	@Autowired
	HrmsNewsalarySeniorityWageService hrmsNewsalarySeniorityWageService;

	@Autowired
	private CommTableSnapshotFeignService commTableSnapshotFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalarySeniorityWageDisposition record) {

		//根据工龄查询 看数据是否存在
		Example example = new Example(HrmsNewsalarySeniorityWageDisposition.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("years", record.getYears());
		List<HrmsNewsalarySeniorityWageDisposition> list = mapper.selectByExample(example);
		if(!list.isEmpty()){
			throw new RuntimeException("工龄已配置,无需重复添加");
		}

		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		mapper.insertSelective(record);
		// 增加处理快照
		CommTableSnapshotSaveReq commTableSnapshotSaveReq = new CommTableSnapshotSaveReq();
		commTableSnapshotSaveReq.setTableName("hrms_newsalary_seniority_wage_disposition");
		commTableSnapshotSaveReq.setRowPkValue(record.getId());
		fillDescField(record);
		commTableSnapshotSaveReq.setNow(record);
		commTableSnapshotFeignService.save(commTableSnapshotSaveReq);
		return 1;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalarySeniorityWageDisposition record) {
		HrmsNewsalarySeniorityWageDisposition oldDisposition = mapper.selectByPrimaryKey(record.getId());
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		mapper.updateByPrimaryKeySelective(record);
		// 增加处理快照
		CommTableSnapshotSaveReq commTableSnapshotSaveReq = new CommTableSnapshotSaveReq();
		commTableSnapshotSaveReq.setTableName("hrms_newsalary_seniority_wage_disposition");
		commTableSnapshotSaveReq.setRowPkValue(record.getId());
		this.fillDescField(oldDisposition);
		this.fillDescField(record);
		commTableSnapshotSaveReq.setOld(oldDisposition);
		commTableSnapshotSaveReq.setNow(record);
		commTableSnapshotFeignService.save(commTableSnapshotSaveReq);
		return 1;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalarySeniorityWageDisposition record = new HrmsNewsalarySeniorityWageDisposition();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalarySeniorityWageDisposition selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalarySeniorityWageDisposition> getDataSetList(Page page, HrmsNewsalarySeniorityWageDisposition record) {
		Example example = new Example(HrmsNewsalarySeniorityWageDisposition.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StrUtil.isNotBlank(record.getYears())){
			criteria.andEqualTo("years",record.getYears());
		}
		if(StrUtil.isNotBlank(record.getIsEnable())){
			criteria.andEqualTo("isEnable",record.getIsEnable());
		}
		List<HrmsNewsalarySeniorityWageDisposition> records = mapper.selectByExampleAndRowBounds(example, page);
		if(CollUtil.isNotEmpty(records)){
			for (HrmsNewsalarySeniorityWageDisposition disposition : records) {
				disposition.setIsEnableLabel(EnableEnum.getValByKey(disposition.getIsEnable()));
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}



	//更新工龄工资的方法
	@Override
	@Transactional(readOnly = false)
	public Integer taskUpdateSeniorityWage(){

		//查询出来规培
		Example example = new Example(HrmsNewsalarySeniorityWageDisposition.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalarySeniorityWageDisposition> records = mapper.selectByExample(example);

		Map<String, Object> disMap = records.stream()
				.collect(Collectors.toMap(HrmsNewsalarySeniorityWageDisposition::getYears, HrmsNewsalarySeniorityWageDisposition::getSalary,
						(existingValue, newValue) -> newValue)); // Keep the newest value in case of duplicates
		//查询出人员工龄
		List<HrmsNewsalarySeniorityWageDisposition> empList = mapper.getYearsAll();
		//人员工龄匹配上对应规则插入数据库中
		if(null != empList && !empList.isEmpty()){
			empList.forEach(item->{
				HrmsNewsalarySeniorityWage saveData = new  HrmsNewsalarySeniorityWage();
				saveData.setId(IdUtil.getId());
				saveData.setIsDeleted("N");
				saveData.setEmployeeId(item.getEmployeeId());
				saveData.setWorkingYear(item.getYears());
				Object _salary = disMap.get(item.getYears());
				if(null != _salary){
					saveData.setSalary(new BigDecimal(_salary.toString()));
				}else{
					saveData.setSalary(new BigDecimal(0));
				}
				hrmsNewsalarySeniorityWageService.tsakSave(saveData);

			});
		}
		return empList.size();
	}

	@Override
	@Transactional(readOnly = false)
	public void enable(List<String> ids) {
		HrmsNewsalarySeniorityWageDisposition wage = new HrmsNewsalarySeniorityWageDisposition();
		wage.setIsEnable("1");
		Example example = new Example(HrmsNewsalarySeniorityWageDisposition.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andIn("id",ids);
		mapper.updateByExampleSelective(wage,example);
	}

	@Override
	@Transactional(readOnly = false)
	public void disEnable(List<String> ids) {
		HrmsNewsalarySeniorityWageDisposition wage = new HrmsNewsalarySeniorityWageDisposition();
		wage.setIsEnable("2");
		Example example = new Example(HrmsNewsalarySeniorityWageDisposition.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andIn("id",ids);
		mapper.updateByExampleSelective(wage,example);
	}

	private void fillDescField(HrmsNewsalarySeniorityWageDisposition record) {
		// 填充isEnable
		if (StringUtils.isNotBlank(record.getIsEnable())) {
			record.setIsEnable(EnableEnum.getValByKey(record.getIsEnable()));
		}
	}
}

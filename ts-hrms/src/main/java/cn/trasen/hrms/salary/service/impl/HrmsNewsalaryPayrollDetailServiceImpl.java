package cn.trasen.hrms.salary.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.DTO.UpdateSalaryVo;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryPayrollDetailMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetail;
import cn.trasen.hrms.salary.service.HrmsNewsalaryPayrollDetailService;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsNewsalaryPayrollDetailServiceImpl
 * @Description TODO
 * @date 2023��11��11�� ����4:38:30
 * <AUTHOR>
 * @version 1.0
 */
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryPayrollDetailServiceImpl implements HrmsNewsalaryPayrollDetailService {

	@Autowired
	private HrmsNewsalaryPayrollDetailMapper mapper;

//	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryPayrollDetail record) {
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryPayrollDetail record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

//	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryPayrollDetail record = new HrmsNewsalaryPayrollDetail();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalaryPayrollDetail selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalaryPayrollDetail> getDataSetList(Page page, HrmsNewsalaryPayrollDetail record) {
		Example example = new Example(HrmsNewsalaryPayrollDetail.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalaryPayrollDetail> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	//批量保存数据
	@Override
//	@Transactional(readOnly = false)
	public Integer batchInsert(List<HrmsNewsalaryPayrollDetail> payrollDetails) {
		int totalSize = payrollDetails.size(); // 总记录数
		int pageSize = 1000; // 每次新增的记录数
		int totalPage = totalSize / pageSize; // 总页数
		if (totalSize % pageSize != 0) {
			totalPage += 1;
			if (totalSize < pageSize) {
				pageSize = totalSize;
			}
		}
		for (int i = 1; i < totalPage + 1; i++) {
			int start = (i - 1) * pageSize;
			int end = i * pageSize > totalSize ? (totalSize) : i * pageSize;
			mapper.batchInsert(payrollDetails.subList(start, end));
		}
		return payrollDetails.size();
	}

	@Override
//	@Transactional(readOnly = false)
	public Integer deleteByExample(String optionId, String payrollDate) {
		Example example = new Example(HrmsNewsalaryPayrollDetail.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("optionId", optionId);
		criteria.andEqualTo("payrollDate", payrollDate);
		return mapper.deleteByExample(example);
	}

	//修改的时候查询工资项
	@Override
	public List<UpdateSalaryVo> getSalaryChangesData(HrmsNewsalaryOptionPayroll record) {
		return mapper.getSalaryChangesData(record);
	}

	@Override
	public Integer deleteByOptionId(String id) {
		Example example = new Example(HrmsNewsalaryPayrollDetail.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("payrollId", id);
		return mapper.deleteByExample(example);
	}

	@Override
	public List<HrmsNewsalaryItem> getCalculateWagesHistoryTitle(HrmsNewsalaryOptionPayroll record) {
		return mapper.getCalculateWagesHistoryTitle(record);
	}

	@Override
	public List<HrmsNewsalaryItem> getsalaryConfirmHistoryTitle(HrmsNewsalaryOptionPayroll record) {
		return mapper.getsalaryConfirmHistoryTitle(record);
	}
}

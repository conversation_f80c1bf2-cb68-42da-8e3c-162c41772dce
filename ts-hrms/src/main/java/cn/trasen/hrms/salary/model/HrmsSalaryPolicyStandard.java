package cn.trasen.hrms.salary.model;

import cn.trasen.homs.bean.base.FileAttachmentResp;
import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.*;

/**
 * 政策标准表
 *
 */
@Table(name = "hrms_salary_policy_standard")
@Setter
@Getter
public class HrmsSalaryPolicyStandard {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "policy_standard_id")
    @ApiModelProperty(value = "主键ID")
    private String policyStandardId;

    /**
     * 政策标准名称
     */
    @Column(name = "policy_standard_name")
    @ApiModelProperty(value = "政策标准名称")
    private String policyStandardName;

    /**
     * 政策文件id
     */
    @Column(name = "file_id")
    @ApiModelProperty(value = "政策文件id")
    private String fileId;

    /**
     * 排序号
     */
    @Column(name = "sort_no")
    @ApiModelProperty(value = "排序号")
    private Integer sortNo;

    /**
     * 是否启用: 1=是; 2=否;
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String isEnable;

    /**
     * 是否启用:
     */
    @Transient
    @ApiModelProperty(value = "是否启用文字描述")
    private String isEnableLable;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    @ApiModelProperty(value = "附件")
    List<FileAttachmentResp> fileList;
}
package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportTotalVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface HrmsNewsalaryReportTotalService {

    Integer save(HrmsNewsalaryReportTotalVo record);

    Integer update(HrmsNewsalaryReportTotalVo record);

    Integer deleteById(String id);

    PlatformResult<List<HrmsNewsalaryReportTotalVo>> getDataSetList(HrmsNewsalaryReportTotalVo record);

    PlatformResult<Map<String, Object>> getTotalData(HrmsNewsalaryReportTotalVo record);

    void sortNo(List<HrmsNewsalaryReportTotalVo> record);
}

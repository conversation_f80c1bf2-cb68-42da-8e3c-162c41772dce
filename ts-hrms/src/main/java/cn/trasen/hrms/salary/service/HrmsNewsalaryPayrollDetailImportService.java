package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.DTO.UpdateSalaryVo;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailImport;

import java.util.List;
import java.util.Map;

/**
 * @ClassName HrmsNewsalaryPayrollDetailImportService
 * @Description TODO
 * @date 2024��3��1�� ����4:35:33
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryPayrollDetailImportService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��3��1�� ����4:35:33
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryPayrollDetailImport record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��3��1�� ����4:35:33
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryPayrollDetailImport record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��3��1�� ����4:35:33
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * 根据方案id和核算月份清除导入项数据
	 * @param optionId
	 * @param sendDate
	 * @return
	 */
	Integer cleanImportDataByOptionId(String optionId,String sendDate);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryPayrollDetailImport
	 * @date 2024��3��1�� ����4:35:33
	 * <AUTHOR>
	 */
	HrmsNewsalaryPayrollDetailImport selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryPayrollDetailImport>
	 * @date 2024��3��1�� ����4:35:33
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryPayrollDetailImport> getDataSetList(Page page, HrmsNewsalaryPayrollDetailImport record);

	/**
	 * 导入薪酬数据
	 * @param data
	 * @return
	 */
	Integer importSave(List<Map<String, String>> data,String optionId,String sendDate);

	List<HrmsNewsalaryItem> exportTemplate(String optionId);
	Integer updateSalaryList(List<UpdateSalaryVo> updateSalaryList);

	List<HrmsNewsalaryItem> exportTemplateAll(String optionId);

	/**
	 * 导入修改方案工资项
	 * @param optionId
	 * @return
	 */
	List<HrmsNewsalaryItem> exportSalaryItemByOptionId(String optionId,String computeDate);

	/**
	 * 判断薪酬方案是否导出手工录入工资项
	 * @param optionId
	 * @param computeDate
	 */
    void checkManualSalary(String optionId, String computeDate);

	/**
	 * 导入薪酬数据
	 * @param data
	 * @return
	 */
	PlatformResult<String> importAllSave(List<Map<String, String>> data, String sendDate);
}

package cn.trasen.hrms.salary.controller;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetailUploadEo;
import cn.trasen.hrms.salary.service.HrmsNewsalaryPayrollDetailUploadService;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrmsNewsalaryPayrollDetailImportController
 * @Description TODO
 * @date 20240802
 */
@RestController
@Api(tags = "手工上报工资项控制器UploadController")
public class HrmsNewsalaryPayrollDetailUploudController {

    private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryPayrollDetailUploudController.class);

    @Autowired
    private HrmsNewsalaryPayrollDetailUploadService uploadService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveHrmsNewsalaryPayrollDetailUpload
     * @Description 新增
     * @date 2024��3��1�� ����4:35:33
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/newSalaryDetailUpload/save")
    public PlatformResult<String> saveHrmsNewsalaryPayrollDetailUpload(@RequestBody List<Map<String, Object>> record) {
        return uploadService.save(record);

    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveHrmsNewsalaryPayrollDetailUpload
     * @Description 新增
     * @date 2024��3��1�� ����4:35:33
     * <AUTHOR>
     */
    @ApiOperation(value = "驳回上报", notes = "驳回上报")
    @PostMapping("/api/rejectDetailUpload/update")
    public PlatformResult<String> rejectDetailUpload(@RequestBody List<String> record) {
        return uploadService.rejectDetailUpload(record);
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateHrmsNewsalaryPayrollDetailUpload
     * @Description 审核上报
     * @date 20240803
     * <AUTHOR>
     */
    @ApiOperation(value = "审核通过上报", notes = "审核上报")
    @PostMapping("/api/newSalaryDetailUpload/toExamine")
    public PlatformResult<String> toExamineDetailUpload(@RequestBody List<String> record) {
        return uploadService.update(record);
    }

    /**
     * @param id
     * @return PlatformResult<HrmsNewsalaryPayrollDetailUpload>
     * @Title selectHrmsNewsalaryPayrollDetailUploadById
     * @Description 根据ID查询
     * @date 20240803
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/newSalaryDetailUpload/{id}")
    public PlatformResult<HrmsNewsalaryPayrollDetailUploadEo> selectHrmsNewsalaryPayrollDetailUploadById(@PathVariable String id) {
        try {
            HrmsNewsalaryPayrollDetailUploadEo record = uploadService.selectById(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param ids
     * @return PlatformResult<String>
     * @Title deleteHrmsNewsalaryPayrollDetailUploadById
     * @Description 根据ID删除
     * @date 20240803
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/newSalaryDetailUpload/delete")
    public PlatformResult<String> deleteHrmsNewsalaryPayrollDetailUploadById(@RequestParam List<String> ids) {
        try {
            uploadService.deleteById(ids);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return DataSet<HrmsNewsalaryPayrollDetailUpload>
     * @Title selectHrmsNewsalaryPayrollDetailUploadList
     * @Description 查询列表
     * @date 2024 0802
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/newSalaryDetailUpload/list")
    public PlatformResult<List<Map<String, Object>>> selectHrmsNewsalaryPayrollDetailUploadList(HrmsNewsalaryPayrollDetailUploadEo record) {
        return uploadService.getDataSetList(record);
    }

    @ApiOperation(value = "薪酬上报列表表头", notes = "薪酬上报列表表头")
    @GetMapping("/api/newSalaryDetailUpload/salaryCountTitle")
    public PlatformResult<List<VueTableEntity>> salaryCountTitle(String payrollDate) {
        try {
            List<VueTableEntity> list = uploadService.salaryCountTitle(payrollDate);
            return PlatformResult.success(list);
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return DataSet<HrmsNewsalaryPayrollDetailUpload>
     * @Title selectHrmsNewsalaryPayrollDetailUploadList
     * @Description 查询列表
     * @date 2024 0802
     * <AUTHOR>
     */
    @ApiOperation(value = "审批未审批列表", notes = "列表")
    @GetMapping("/api/selectApproval/list")
    public DataSet<Map<String, Object>> selectApprovalList(Page page, HrmsNewsalaryPayrollDetailUploadEo record) {
        return uploadService.getDataSetListPage(page, record);
    }


    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveHrmsNewsalaryPayrollDetailUpload
     * @Description 修改上报数据
     * @date 2024��3��1�� ����4:35:33
     * <AUTHOR>
     */
    @ApiOperation(value = "修改上报数据", notes = "修改上报数据")
    @PostMapping("/api/updateDetailUpload")
    public PlatformResult<String> updateDetailUpload(@RequestBody Map<String, Object> record) {
        return uploadService.updateDetailUpload(record);
    }

    @ApiOperation(value = "导出定薪调薪模版", notes = "导出定薪调薪模版")
    @GetMapping("/api/salaryBasicColumn/exportUploadTemplateData")
    public void exportUploadTemplateData(HttpServletResponse response, HrmsNewsalaryPayrollDetailUploadEo record) {
        uploadService.exportUploadTemlateData(response, record);
    }

    @ApiOperation(value = "导入入职定薪模版数据", notes = "导入入职定薪模版数据")
    @PostMapping("/api/salaryBasicColumn/importUploadTemplateData")
    public PlatformResult<String> importEntryTemplateData(@RequestParam("file") MultipartFile file, String payrollDate) {
        return uploadService.importUploadTemplateData(file, payrollDate);
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveHrmsNewsalaryPayrollDetailUpload
     * @Description 修改上报数据
     * @date 2024��3��1�� ����4:35:33
     * <AUTHOR>
     */
    @ApiOperation(value = "修改草稿数据", notes = "修改草稿数据")
    @PostMapping("/api/updateUpload")
    public PlatformResult<String> updateUpload(@RequestBody Map<String, Object> record) {
        return uploadService.updateUpload(record);
    }

    @ApiOperation(value = "撤回上报数据", notes = "撤回上报数据")
    @PostMapping("/api/withdrawUpload")
    public PlatformResult<String> withdrawUpload(@RequestBody List<String> record) {
        return uploadService.withdrawUpload(record);
    }

    @ApiOperation(value = "查询是否有未上报的数据", notes = "查询是否有未上报的数据")
    @GetMapping("/api/queryUploadInfo")
    public PlatformResult<List<HrmsNewsalaryPayrollDetailUploadEo>> queryUploadInfo(String payrollDate, String optionId) {
        return uploadService.queryUploadInfo(payrollDate, optionId);
    }

    @ApiOperation(value = "上报数据导出", notes = "上报数据导出")
    @GetMapping("/api/uploadDataExport")
    public void uploadDataExport(HttpServletRequest request, HttpServletResponse response, HrmsNewsalaryPayrollDetailUploadEo record) {
        uploadService.uploadDataExport(request,response,record);
    }
}

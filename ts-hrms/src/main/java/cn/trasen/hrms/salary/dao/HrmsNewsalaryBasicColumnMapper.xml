<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryBasicColumnMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryBasicColumn">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="basic_item_name" jdbcType="VARCHAR" property="basicItemName" />
        <result column="basic_item_type" jdbcType="CHAR" property="basicItemType" />
        <result column="emp_field" jdbcType="VARCHAR" property="empField" />
        <result column="POSITION" jdbcType="CHAR" property="position" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    </resultMap>


    <select id="listTableTitle" resultType="cn.trasen.hrms.salary.DTO.BasicItemVO">
      SELECT
        basic_item_name,basic_item_type,id AS basicItemId
      FROM hrms_newsalary_basic_column
      WHERE is_deleted = 'N'
      ORDER BY number_sort ASC
    </select>

    <!-- 薪酬档案列表数据 -->
    <select id="listTableData" resultType="java.util.Map" parameterType="cn.trasen.hrms.salary.DTO.SearchListTable">
        SELECT t1.employee_name,t1.employee_no,t1.org_id,t1.employee_id,t3.name AS org_name,
        t1.employee_status,t1.positive_time,t1.retirement_time,
        t1.establishment_type,t1.is_temp,
        t1.personal_identity,t1.identity_number,t1.bankcardno,
        <if test="basicItemIds != null and basicItemIds.size() > 0">
            <foreach collection="basicItemIds" index="index"
                     item="item" open="" separator="," close="">
                MAX(CONVERT(CASE
                WHEN t4.basic_item_type ='1' AND t2.basic_item_id=#{item} THEN t2.emp_field_value_text
                WHEN t4.basic_item_type ='2'  AND t2.basic_item_id=#{item} THEN
                case when t7.carry_rule = '1' then CAST(round(CAST(t2.salary_amount AS UNSIGNED) * COALESCE(t2.emp_field_value, 100) / 100,COALESCE(CAST(t7.item_digit AS UNSIGNED),0)) AS CHAR)
                when t7.carry_rule = '2' then
                CAST(round(FLOOR(CAST(t2.salary_amount AS UNSIGNED) * COALESCE(t2.emp_field_value, 100) * pow(10,COALESCE(CAST(t7.item_digit AS UNSIGNED),0)) / 100) /pow(10,COALESCE(CAST(t7.item_digit AS UNSIGNED),0)), COALESCE(CAST(t7.item_digit AS UNSIGNED),0)) AS CHAR)
                when t7.carry_rule = '3' then
                CAST(round(CEIL(CAST(t2.salary_amount AS UNSIGNED) * COALESCE(t2.emp_field_value, 100) * pow(10,COALESCE(CAST(t7.item_digit AS UNSIGNED),0)) / 100) /pow(10,COALESCE(CAST(t7.item_digit AS UNSIGNED),0)),COALESCE(CAST(t7.item_digit AS UNSIGNED),0)) 	AS CHAR)
                else 0 end
                WHEN t4.basic_item_type ='3' AND t2.basic_item_id=#{item} AND t7.item_digit is not null  THEN round(ifnull(t2.salary_amount,0),COALESCE(CAST(t7.item_digit AS UNSIGNED),0))
                WHEN t4.basic_item_type ='3' AND t2.basic_item_id=#{item} AND t7.item_digit is null THEN ifnull(t2.salary_amount,0)
                ELSE ''
                END USING utf8mb4) COLLATE utf8mb4_bin) AS '${item}'
            </foreach>
            ,
        </if>
        <if test="basicItemIds != null and basicItemIds.size() > 0">
            <foreach collection="basicItemIds" index="index"
                     item="item" open="" separator="," close="">
                MAX(CASE
                WHEN t4.basic_item_type ='2'  AND t2.basic_item_id=#{item} THEN ifnull(t2.emp_field_value,100)
                WHEN t4.basic_item_type ='3' AND t2.basic_item_id=#{item} and t2.emp_field_value ='1' THEN '是'
                WHEN t4.basic_item_type ='3' AND t2.basic_item_id=#{item} and (t2.emp_field_value != '1' or t2.emp_field_value is null ) THEN '否'
                ELSE ''
                END) AS '${item}-percent'
            </foreach>
            ,
        </if>
        MAX(CASE WHEN t2.reason != NULL THEN t2.reason ELSE '' END) AS reason,
        MAX(CASE WHEN t2.effective_date != NULL THEN t2.salary_amount ELSE '' END) AS effective_date,
        t1.salary_appoint,
        t2.update_date,
        t2.update_user_name,
        t6.option_name
        FROM (
        SELECT
        employee_name,employee_no,org_id,employee_id,employee_status,positive_time,retirement_time,
        establishment_type,salary_appoint,is_deleted, personal_identity,identity_number,bankcardno,
        entry_date,'N' as is_temp
        FROM
        cust_emp_base base left join cust_emp_info inf on base.employee_id = inf.info_id
        UNION ALL
        SELECT
        employee_name,employee_no,org_id,id as employee_id,
        (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
        tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
        tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
        join_date as entry_date,'Y' as is_temp
        FROM
        hrms_employee_temporary et where is_sync_salary = 'Y'
        ) t1
        left JOIN hrms_newsalary_basicitem_emp t2 ON t1.employee_id = t2.employee_id
        left JOIN hrms_newsalary_option_emp t5 on t1.employee_id=t5.employee_id and t5.is_deleted= t1.is_deleted
        Left join hrms_newsalary_item t7 on t7.option_id = t5.option_id and t7.id = t2.emp_field
        LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
        LEFT JOIN hrms_newsalary_basic_column t4 ON t2.basic_item_id = t4.id
        left join hrms_newsalary_option t6 on t5.option_id = t6.id
        WHERE t1.is_deleted='N'
        AND t1.employee_no != 'admin'
        <if test="employeeName !=null and employeeName !='' ">
            and (
            t1.employee_name like concat('%',#{employeeName},'%')
            or t1.employee_no like concat('%',#{employeeName},'%')
            )
        </if>

        <if test='employeeIds != null and employeeIds.size() > 0'>
            and t1.employee_id in
            <foreach collection="employeeIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <choose>
            <when test='employeeStatus != null and employeeStatus.size() > 0'>
                and t1.employee_status in
                <foreach collection="employeeStatus" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>

            <otherwise>
                and t1.employee_status IN(1,6,12,99,9)
            </otherwise>

        </choose>

        <if test="salaryAppoint !=null and salaryAppoint !='' ">
            <choose>
                <when test='salaryAppoint == "1"'>
                    and t1.salary_appoint = '1'
                </when>
                <otherwise>
                    and (t1.salary_appoint = '0' or t1.salary_appoint is null)
                </otherwise>
            </choose>
        </if>

        <if test="entryDateStartTime!=null and entryDateStartTime!=''">
            and t1.entry_date >= #{entryDateStartTime}
        </if>

        <if test="entryDateEndTime!=null and entryDateEndTime!=''">
            and #{entryDateEndTime} >=t1.entry_date
        </if>

        <if test="positiveTimeStartTime!=null and positiveTimeStartTime!=''">
            and t1.positive_time >= #{positiveTimeStartTime}
        </if>

        <if test="positiveTimeEndTime!=null and positiveTimeEndTime!=''">
            and #{positiveTimeEndTime} >=t1.positive_time
        </if>

        <if test="retirementTimeStartTime!=null and retirementTimeStartTime!=''">
            and t1.retirement_time >= #{retirementTimeStartTime}
        </if>

        <if test="retirementTimeEndTime!=null and retirementTimeEndTime!=''">
            and #{retirementTimeEndTime} >=t1.retirement_time
        </if>

        <if test="orgName !=null and orgName!=''">
            and t3.name like concat('%',#{orgName},'%')
        </if>

        <if test="optionId !=null and optionId!=''">
            and t5.option_id = #{optionId}
        </if>

        <if test="establishmentTypes != null and establishmentTypes.size() > 0">
            and (t1.establishment_type in
            <foreach collection="establishmentTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test='hasJoinOption != null'>
            <choose>
                <when test='hasJoinOption =="0"'>
                    and t5.id is null
                </when>
                <when test='hasJoinOption =="1"'>
                    and t5.id is not null
                </when>
            </choose>
        </if>

        <if test="empFieldValue !=null and empFieldValue!=''">
            and t1.employee_id in (select employee_id from hrms_newsalary_basicitem_emp where basic_item_type = '2' and emp_field_value = #{empFieldValue}
            <if test="empField  !=null and empField!=''">
                and emp_field= #{empField}
            </if>
            )
        </if>

        <if test="gwdj !=null and gwdj !=''">
            and t1.employee_id in (select employee_id from hrms_newsalary_basicitem_emp where emp_field='gwdj' and emp_field_value = #{gwdj})
        </if>

        <if test="salaryLevelId !=null and salaryLevelId!=''">
            and t1.employee_id in (select employee_id from hrms_newsalary_basicitem_emp where emp_field='salary_level_id' and emp_field_value = #{salaryLevelId})
        </if>

        <if test="policyStandardId !=null and policyStandardId!=''">
            and t1.employee_id in (select employee_id from hrms_newsalary_basicitem_emp where emp_field='policy_standard_id' and emp_field_value = #{policyStandardId})
        </if>
        <choose>
            <when test="_databaseId=='postgresql' or _databaseId=='kingbase'">
                GROUP BY t1.employee_id,t1.employee_name,t1.employee_no,t1.org_id,t3.name,
                t1.employee_status,t1.positive_time,t1.retirement_time,t1.establishment_type,t1.personal_identity,
                t1.identity_number,t1.bankcardno,t1.salary_appoint,t2.update_date,t2.update_user_name,t6.option_name
            </when>
            <otherwise>
                Group by t1.employee_id
            </otherwise>
        </choose>
        Order by COALESCE(CAST(t1.employee_no AS UNSIGNED),9999)

    </select>

    <select id="listEmpSalaryData" resultType="java.util.Map" parameterType="cn.trasen.hrms.salary.DTO.SchEmpSalary">
        SELECT t1.employee_name,t1.employee_no,t1.org_id,t1.employee_id,t3.name AS org_name,
        t1.employee_status,t1.positive_time,t1.retirement_time,
        t1.establishment_type,
        t1.personal_identity,
        <if test="basicItemIds != null and basicItemIds.size() > 0">
            <!-- <foreach collection="basicItemIds" index="index"
                     item="item" open="" separator="," close=""> -->
            <foreach collection="basicItemIds" index="index" item="item" separator=",">
                MAX(CASE
                WHEN t4.basic_item_type ='1' AND t2.basic_item_id=#{item.id} THEN t2.emp_field_value_text
                WHEN t4.basic_item_type ='2' AND t2.basic_item_id=#{item.id} THEN round(t2.salary_amount * COALESCE(t2.emp_field_value, 100) / 100,2)
                WHEN t4.basic_item_type ='3' AND t2.basic_item_id=#{item.id} and t2.emp_field_value ='1' THEN '是'
                WHEN t4.basic_item_type ='3' AND t2.basic_item_id=#{item.id} and (t2.emp_field_value = '0' or
                t2.emp_field_value is null ) THEN '否'
                ELSE ''
                END) AS #{item.basicItemName}
            </foreach>
            ,
        </if>
        MAX(CASE WHEN t2.reason != NULL THEN t2.reason ELSE '' END) AS reason,
        MAX(CASE WHEN t2.effective_date != NULL THEN t2.salary_amount ELSE '' END) AS effective_date,
        t1.salary_appoint,
        t2.update_date,
        t2.update_user_name,
        t6.option_name, hnp.payroll_date
        FROM hrms_newsalary_payroll hnp
        inner join (
        SELECT
        employee_name,employee_no,org_id,employee_id,gender,birthday,
        employee_status,positive_time,retirement_time,
        position_id,year_work,bankcardname,emp_age,
        establishment_type,salary_appoint,is_deleted,
        personal_identity,identity_number,bankcardno,
        entry_date,phone_number,emp_payroll,name_spell
        FROM
        cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
        ) t1 on hnp.employee_id = t1.employee_id
        LEFT JOIN hrms_newsalary_basicitem_emp t2 ON t1.employee_id = t2.employee_id
        LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
        LEFT JOIN hrms_newsalary_basic_column t4 ON t2.basic_item_id = t4.id
        LEFT JOIN hrms_newsalary_option_emp t5 on t1.employee_id=t5.employee_id and t5.is_deleted='N'
        left join hrms_newsalary_option t6 on t5.option_id = t6.id
        WHERE t1.is_deleted='N' AND t1.employee_no != 'admin'
        <if test="employeeName !=null and employeeName !='' ">
            and (
            t1.identity_number like concat('%',#{employeeName},'%')
            or t1.phone_number like concat('%',#{employeeName},'%')
            or t1.emp_payroll like concat('%',#{employeeName},'%')
            or t1.employee_name like concat('%',#{employeeName},'%')
            or t1.employee_no like concat('%',#{employeeName},'%')
            or t1.name_spell like concat('%',#{employeeName},'%')
            )
        </if>
        <if test="beginDate !=null and beginDate !=''">
            and hnp.payroll_date &gt;= #{beginDate}
        </if>
        <if test="endDate !=null and endDate !=''">
            and hnp.payroll_date &lt;= #{endDate}
        </if>
        <choose>
            <when test="_databaseId=='postgresql' or _databaseId=='kingbase'">
                GROUP BY t1.employee_name,t1.employee_no,t1.org_id,t1.employee_id,t3.name AS org_name,
                t1.employee_status,t1.positive_time,t1.retirement_time,
                t1.establishment_type,t1.personal_identity,t1.salary_appoint,
                t2.update_date,t2.update_user_name,t6.option_name, hnp.payroll_date
            </when>
            <otherwise>
                GROUP BY hnp.payroll_date
            </otherwise>
        </choose>
        order by hnp.payroll_date
    </select>
    <select id="makePayTableData" resultType="java.util.Map" >
        SELECT t1.employee_name,t1.employee_no,t1.org_id,t1.employee_id,
        t1.positive_time,t1.retirement_time,t1.establishment_type,t1.personal_identity,t3.name as org_name,
        <if test="basicItemIds != null and basicItemIds.size() > 0">
            <foreach collection="basicItemIds" index="index"
                     item="item" open="" separator="," close="">
                MAX(CASE
                WHEN  t4.basic_item_type ='1' AND t2.basic_item_id=#{item} THEN t2.emp_field_value_text
                WHEN  t4.basic_item_type ='2' AND t2.basic_item_id=#{item} THEN round(t2.salary_amount * COALESCE(t2.emp_field_value, 100) / 100,2)
                WHEN  t4.basic_item_type ='3' AND t2.basic_item_id=#{item} THEN t2.emp_field_value
                ELSE ''
                END) AS #{item}
            </foreach>
            ,
        </if>
        MAX(CASE  WHEN   t2.reason != NULL  THEN t2.reason ELSE '' END) AS reason,
        t2.effective_date,
        t1.salary_appoint,
        t2.update_date,
        t2.update_user_name,
        t7.item_name AS employee_status
        FROM  (
        SELECT
        employee_name,employee_no,org_id,employee_id,gender,birthday,
        employee_status,positive_time,retirement_time,
        position_id,year_work,bankcardname,emp_age,
        establishment_type,salary_appoint,is_deleted,
        personal_identity,identity_number,bankcardno,
        entry_date,phone_number,emp_payroll,name_spell,
        salary_level_id,salary_level_type,gwdj,plgw
        FROM
        cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
        ) t1
        LEFT JOIN hrms_newsalary_basicitem_emp t2 ON t1.employee_id = t2.employee_id
        LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
        LEFT JOIN hrms_newsalary_basic_column t4 ON t2.basic_item_id = t4.id
        LEFT JOIN (
            SELECT
            A.*
            FROM
            COMM_DICT_ITEM A
            LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
            WHERE
            B.TYPE_CODE = 'employee_status'
            AND B.IS_DELETED = 'N'
            AND A.IS_DELETED = 'N'
            AND A.IS_ENABLE = '1'
            AND A.sso_org_code = #{ssoOrgCode}
        ) t7 ON t1.employee_status =t7.item_code
        WHERE t2.effective_date like CONCAT('%',#{computeDate},'%')
        and t1.employee_id in
        (
        SELECT employee_id FROM hrms_newsalary_option_emp WHERE option_id =#{optionId} and is_deleted = 'N'
        )
        <if test="employeeName != null and employeeName !=''">
            and ( t1.employee_name like CONCAT('%',#{employeeName},'%') or
            t1.employee_no like CONCAT('%',#{employeeName},'%') )
        </if>

        <if test='employeeStatus != null and employeeStatus.size() > 0'>
            and t1.employee_status in
            <foreach collection="employeeStatus" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="orgName != null and orgName !=''">
            and  t3.name like CONCAT('%',#{orgName},'%')
        </if>

        <if test="reason != null and reason !=''">
            and  t2.reason like CONCAT('%',#{reason},'%')
        </if>
        <choose>
            <when test="_databaseId=='postgresql' or _databaseId=='kingbase'">
                GROUP BY t1.employee_name,t1.employee_no,t1.org_id,t1.employee_id,
                t1.positive_time,t1.retirement_time,t1.establishment_type,t1.personal_identity,t3.name
            </when>
            <otherwise>
                Group by t1.employee_id
            </otherwise>
        </choose>

        Order by COALESCE(CAST(t1.employee_no AS UNSIGNED),9999)
    </select>

    <select id="makePayTableDataCount" resultType="java.util.Map" parameterType="cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll">
        SELECT t1.employee_name,t1.employee_no,t1.org_id,t1.employee_id,t3.name as org_name,
        t1.employee_status,t1.positive_time,t1.retirement_time,t1.establishment_type, t1.personal_identity,
        <if test="basicItemIds != null and basicItemIds.size() > 0">
            <foreach collection="basicItemIds" index="index"
                     item="item" open="" separator="," close="">
                MAX(CASE
                WHEN  t4.basic_item_type ='1' AND t2.basic_item_id=#{item} THEN t2.emp_field_value_text
                WHEN  t4.basic_item_type ='2' AND t2.basic_item_id=#{item} THEN round(t2.salary_amount * COALESCE(t2.emp_field_value, 100) / 100,2)
                WHEN  t4.basic_item_type ='3' AND t2.basic_item_id=#{item} THEN t2.emp_field_value
                ELSE ''
                END) AS '${item}'
            </foreach>
            ,
        </if>
        MAX(CASE  WHEN   t2.reason != NULL  THEN t2.reason ELSE '' END) AS reason,
        MAX(CASE  WHEN   t2.effective_date != NULL THEN t2.salary_amount ELSE '' END) AS effective_date,
        t1.salary_appoint,
        t2.update_date,
        t2.update_user_name,
        t1.is_temp
        FROM  (
        SELECT
        employee_name,employee_no,org_id,employee_id,gender,birthday,
        employee_status,positive_time,retirement_time,
        position_id,year_work,bankcardname,emp_age,
        establishment_type,salary_appoint,is_deleted,
        personal_identity,identity_number,bankcardno,
        entry_date,'N' as is_temp
        FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
        UNION ALL
        SELECT
        employee_name,employee_no,org_id,id as employee_id,gender,birthday,
        (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
        null as position_id,null as year_work,null as bankcardname,null as emp_age,
        tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
        tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
        join_date as entry_date,'Y' as is_temp
        FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
        ) t1
        LEFT JOIN hrms_newsalary_basicitem_emp t2 ON t1.employee_id = t2.employee_id
        LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
        LEFT JOIN hrms_newsalary_basic_column t4 ON t2.basic_item_id = t4.id
        WHERE t1.is_deleted ='N' AND t2.is_deleted='N'
        <choose>
            <when test="_databaseId=='postgresql' or _databaseId=='kingbase'">
                GROUP BY t1.employee_id,t1.employee_name,t1.employee_no,t1.org_id,t1.employee_id,t3.name,
                t1.employee_status,t1.positive_time,t1.retirement_time,t1.establishment_type, t1.personal_identity,
                t1.salary_appoint,t2.update_date,t2.update_user_name
            </when>
            <otherwise>
                Group by t1.employee_id
            </otherwise>
        </choose>
        Order by COALESCE(CAST(t1.employee_no AS UNSIGNED),9999)
    </select>


    <select id="getEmployeeBase" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT * FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id and employee_id=#{employeeId}
  </select>

    <select id="getHistoryByEmp" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmpHistory">
    SELECT t1.*,t2.basic_item_name,t2.basic_item_type,t2.compare FROM hrms_newsalary_basicitem_emp_history t1
     LEFT JOIN hrms_newsalary_basic_column  t2 ON t1.basic_item_id = t2.id
    WHERE t1.is_deleted='N'
      AND t1.v_number = (   SELECT v_number FROM hrms_newsalary_basicitem_emp_history
                            WHERE is_deleted='N' AND employee_id=#{employeeId}
                               GROUP BY v_number
                            ORDER BY v_number DESC LIMIT 1)
  </select>


    <select id="getPostCategoryType" resultType="java.lang.String">
        SELECT A.item_name FROM COMM_DICT_ITEM A
        LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
        WHERE B.TYPE_CODE = 'post_category'
        AND A.item_code=#{val}
        AND B.IS_DELETED = 'N'
        AND A.IS_DELETED = 'N'
        AND A.IS_ENABLE='1'
        AND A.sso_org_code = '*PUBLIC*'
  </select>
    <select id="getPostCategoryLevel" resultType="java.lang.String">
    select post_name from comm_post where post_id = #{val}
    and is_deleted = 'N' and is_enable = '1'
  </select>
  <select id="getSalaryLevelCategoryType" resultType="java.lang.String">
        SELECT
          A.item_name
        FROM
          COMM_DICT_ITEM A
          LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
        WHERE
          B.TYPE_CODE = 'salary_level_category'
          AND item_code=#{val}
          AND B.IS_DELETED = 'N'
          AND A.IS_DELETED = 'N'
          AND A.IS_ENABLE = '1'
          AND A.sso_org_code = #{ssoOrgCode}
  </select>
    <select id="getSalaryLevelCategoryLevel" resultType="java.lang.String">
    SELECT
     distinct sl.salary_level_name
    FROM hrms_salary_level_wage wage
           LEFT JOIN hrms_salary_level sl ON sl.salary_level_id = wage.salary_level_id
    WHERE wage.is_deleted = 'N' AND wage.salary_level_id=#{val} limit 1
  </select>

    <!-- 查询未定薪人员 -->
    <select id="getUncertain" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM  cust_emp_base base,cust_emp_info inf WHERE base.employee_id = inf.info_id and is_deleted='N' AND employee_status IN(1,6,12,99,9)
          AND employee_no != 'admin'
         AND   (salary_appoint = '0' OR salary_appoint IS NULL OR salary_appoint='') 
  </select>

    <select id="selectEmpAll" resultType="java.util.Map">
        SELECT t1.employee_no,t1.employee_name, t6.option_name FROM (
        SELECT
        employee_name,employee_no,org_id,employee_id,gender,birthday,
        employee_status,positive_time,retirement_time,
        position_id,year_work,bankcardname,emp_age,
        establishment_type,salary_appoint,is_deleted,
        personal_identity,identity_number,bankcardno,
        entry_date,'N' as is_temp
        FROM
        cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
        UNION ALL
        SELECT
        employee_name,employee_no,org_id,id as employee_id,gender,birthday,
        (case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
        null as position_id,null as year_work,null as bankcardname,null as emp_age,
        tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
        tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
        join_date as entry_date,'Y' as is_temp
        FROM
        hrms_employee_temporary et where et.is_sync_salary = 'Y'
        ) t1
        LEFT JOIN hrms_newsalary_basicitem_emp t2 ON t1.employee_id = t2.employee_id
        LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
        LEFT JOIN hrms_newsalary_option_emp t5 ON t1.employee_id=t5.employee_id AND t5.is_deleted='N'
        LEFT JOIN hrms_newsalary_option t6 on t5.option_id = t6.id
        WHERE  t1.employee_status IN(1,6,8,12,99,9)
        AND t1.is_deleted='N'
        AND t1.employee_no != 'admin'
        <if test="employeeName !=null and employeeName !='' ">
            and (
            t1.identity_number like concat('%',#{employeeName},'%')
            or t1.phone_number like concat('%',#{employeeName},'%')
            or t1.emp_payroll like concat('%',#{employeeName},'%')
            or t1.employee_name  like concat('%',#{employeeName},'%')
            or t1.employee_no  like concat('%',#{employeeName},'%')
            or t1.name_spell  like concat('%',#{employeeName},'%')
            or t1.name_stroke  like concat('%',#{employeeName},'%')
            )
        </if>

        <choose>
            <when test='employeeStatus != null and employeeStatus.size() > 0'>
                and t1.employee_status in
                <foreach collection="employeeStatus" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>

            <otherwise>
                and t1.employee_status IN(1,6,8,12,99,9)
            </otherwise>

        </choose>

        <if test="salaryAppoint !=null and salaryAppoint !='' ">
            <choose>
                <when test='salaryAppoint == "1"'>
                    and t1.salary_appoint = '1'
                </when>
                <otherwise>
                    and (t1.salary_appoint = '0' or t1.salary_appoint is null)
                </otherwise>
            </choose>
        </if>

        <if test="entryDateStartTime!=null and entryDateStartTime!=''">
            and t1.entry_date >=  #{entryDateEndTime}
        </if>

        <if test="entryDateEndTime!=null and entryDateEndTime!=''">
            and #{entryDateEndTime} >=t1.entry_date
        </if>

        <if test="positiveTimeStartTime!=null and positiveTimeStartTime!=''">
            and t1.positive_time >=  #{positiveTimeStartTime}
        </if>

        <if test="positiveTimeEndTime!=null and positiveTimeEndTime!=''">
            and #{positiveTimeEndTime} >=t1.positive_time
        </if>

        <if test="retirementTimeStartTime!=null and retirementTimeStartTime!=''">
            and t1.retirement_time >=  #{retirementTimeStartTime}
        </if>

        <if test="retirementTimeEndTime!=null and retirementTimeEndTime!=''">
            and #{retirementTimeEndTime} >=t1.retirement_time
        </if>

        <if test="orgName !=null and orgName!=''">
            and t3.name  like concat('%',#{orgName},'%')
        </if>

        <if test="optionId !=null and optionId!=''">
            and t5.option_id  = #{optionId}
        </if>

        <if test="establishmentTypes != null and establishmentTypes.size() > 0">
            and (t1.establishment_type in
            <foreach collection="establishmentTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <choose>
            <when test="_databaseId=='postgresql' or _databaseId=='kingbase'">
            </when>
            <otherwise>
                GROUP BY t1.employee_id
            </otherwise>
        </choose>
        ORDER BY t1.org_id ,COALESCE(CAST(t1.employee_no AS UNSIGNED),9999)
    </select>
    <select id="getUnoption" resultType="java.lang.String">
    select h.employee_name FROM cust_emp_base h where h.employee_id NOT IN (
      select employee_id e from hrms_newsalary_option_emp e where e.IS_DELETED = 'N'
    ) and h.IS_DELETED = 'N' and h.employee_status IN(1,6,12,99,9)
    AND h.employee_no != 'admin'
  </select>

    <select id="getUnoptionByTmpEmp" resultType="java.lang.String">
    select h.employee_name FROM hrms_employee_temporary h where h.id NOT IN (
      select employee_id e from hrms_newsalary_option_emp e where e.IS_DELETED = 'N'
    ) and h.IS_DELETED = 'N' and h.is_sync_salary = 'Y' and h.tmp_employee_status = '1'
    AND h.employee_no != 'admin' AND h.is_deleted='N'
  </select>

    <select id="selectBasColumn" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryBasicColumn">
		select a.*, b.item_rule   
		from hrms_newsalary_basic_column a 
		left join hrms_newsalary_item_library b on a.emp_field = b.id 
		where a.is_deleted = 'N' order by   number_sort 
	</select>
    <select id="listSalaryLedgerData" resultType="cn.trasen.hrms.salary.DTO.SalaryLedgerListVo">
        SELECT DISTINCT t1.employee_name AS employeeName,t1.employee_no AS employeeNo,t1.org_id,t1.employee_id employeeId,t3.name AS orgName,t6.option_name as optionName,
        t1.establishment_type as establishmentType,
        t1.salary_level_id as salaryLevelId,
        t1.salary_level_type as salaryLevelType,
        t1.personal_identity as personalIdentity,
        t1.gwdj,
        t1.plgw,
        nsp.option_id as optionId,
        nsp.payroll_date AS payrollDate,
        nsp.update_date AS updateDate,
        nsp.update_user_name AS updateUserName
        FROM
        hrms_newsalary_payroll nsp
        LEFT JOIN hrms_newsalary_payroll_detail npd ON nsp.id= npd.payroll_id and nsp.is_deleted = npd.is_deleted
        left join (
        SELECT
        employee_name,employee_no,org_id,employee_id,gender,birthday,
        employee_status,positive_time,retirement_time,
        position_id,year_work,bankcardname,emp_age,
        establishment_type,salary_appoint,is_deleted,
        personal_identity,identity_number,bankcardno,
        entry_date,phone_number,emp_payroll,name_spell,
        salary_level_id,salary_level_type,gwdj,plgw
        FROM
        cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
        ) t1 on nsp.employee_id =t1.employee_id
        Left join hrms_newsalary_item t7 on npd.item_id = t7.id and npd.option_id = t7.option_id
        LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
        left join hrms_newsalary_option t6 on nsp.option_id = t6.id
        WHERE t1.is_deleted='N'
        <if test="employeeName !=null and employeeName !='' ">
            and  t1.employee_name like concat('%',#{employeeName},'%')

        </if>

        <![CDATA[ and  nsp.payroll_date >= #{monthStartTime} AND  nsp.payroll_date <= #{monthEndTime} ]]>

        Order by nsp.payroll_date ASC
    </select>
    <select id="getSalaryLevelName" resultType="java.lang.String">
        SELECT distinct emp_field_value_text from hrms_newsalary_basicitem_emp WHERE employee_id = #{employeeId}  and emp_field_value = #{salaryLevelId}
        and emp_field =#{empField}
    </select>
    <select id="getSalary" resultType="java.lang.String">
        SELECT max(t2.salary)
        FROM hrms_newsalary_payroll t1
        LEFT JOIN hrms_newsalary_payroll_detail t2 ON t1.id=t2.payroll_id
        LEFT JOIN hrms_newsalary_item t3 ON t2.item_id = t3.id and t1.option_id = t3.option_id
        LEFT JOIN hrms_newsalary_item_group t4 on t3.group_id = t4.id
        WHERE  t1.is_deleted='N'
        AND t4.is_deleted = 'N'
        AND t1.employee_id = #{employeeId}
        AND t3.is_article='1'
        AND t1.payroll_date= #{payrollDate} and t3.actual_salary = '1'
        order by t4.seq_no,t3.sort_no,t3.id
    </select>


</mapper>
package cn.trasen.hrms.salary.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.DTO.*;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicColumn;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmpHistory;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface HrmsNewsalaryBasicColumnMapper extends Mapper<HrmsNewsalaryBasicColumn> {
    /**
     * 获取薪酬档案列表表头
     * @return
     */
    List<BasicItemVO> listTableTitle();

    List<Map<String, String>> listTableData(Page page, SearchListTable record);

    //查询人员档案基本信息
    Map<String, String> getEmployeeBase(String employeeId);

    List<Map<String, String>> makePayTableData(Page page, HrmsNewsalaryOptionPayroll record);

    List<HrmsNewsalaryBasicitemEmpHistory> getHistoryByEmp(String employeeId);

    //获取岗位类别中文名称
    String getPostCategoryType(@Param("val") String val, @Param("ssoOrgCode") String ssoOrgCode);
    //获取岗位等级中文名称
    String getPostCategoryLevel(String val);
    //获取薪级类别中文
    String getSalaryLevelCategoryType(@Param("val") String val, @Param("ssoOrgCode") String ssoOrgCode);
    //获取薪级名称中文
    String getSalaryLevelCategoryLevel(String val);

    List<Map<String, Object>> makePayTableDataCount(Page page, HrmsNewsalaryOptionPayroll record);

    Integer getUncertain(); //查询未定薪人员

    List<Map<String, String>> selectEmpAll(SearchListTable record);

    List<String> getUnoption();

    List<String> getUnoptionByTmpEmp();

	/**
	* @功能描述: TODO 
	* @Title: listEmpSalaryData
	* @param @param page
	* @param @param record
	* @param @return    参数
	* @return List<Map<String,String>>    返回类型
	* @throws
	* <AUTHOR>
	* @date 2024年6月21日 下午5:42:07
	*/
	List<Map<String, String>> listEmpSalaryData(Page page, SchEmpSalary record);
	
	List<HrmsNewsalaryBasicColumn> selectBasColumn();

    List<SalaryLedgerListVo> listSalaryLedgerData(Page page, SearchSalaryLedgerVo record);

    String getSalaryLevelName(@Param("employeeId") String employeeId, @Param("salaryLevelId") String salaryLevelId,@Param("empField") String empField);

    String getSalary(@Param("employeeId") String employeeId,@Param("payrollDate") String payrollDate);
}
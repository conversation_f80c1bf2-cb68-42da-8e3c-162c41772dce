package cn.trasen.hrms.salary.dao;

import java.util.List;
import java.util.Map;

import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmpHistory;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsNewsalaryBasicitemEmpHistoryMapper extends Mapper<HrmsNewsalaryBasicitemEmpHistory> {
    List<Map<String,Object>>  getDataSetList(HrmsNewsalaryBasicitemEmpHistory record);

    List<HrmsNewsalaryBasicitemEmp> currentSalary(HrmsNewsalaryBasicitemEmpHistory record);

    List<HrmsNewsalaryBasicitemEmpHistory> getEmpTakeEffect(String takeEffect);
}
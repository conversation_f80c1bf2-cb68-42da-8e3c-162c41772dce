package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemRemindRecord;

import java.util.List;

/**
 * @ClassName HrmsNewsalaryItemRemindRecordService
 * @Description TODO
 * @date 2024��10��29�� ����6:29:18
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryItemRemindRecordService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryItemRemindRecord record);

	/**
	 * @Title save
	 * @Description 新增
	 * @param list
	 * @return Integer
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	Integer batchInsert(List<HrmsNewsalaryItemRemindRecord> list);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryItemRemindRecord record);

	/**
	 * @Title update
	 * @Description 提醒处理
	 * @param record
	 * @return Integer
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	Integer remindProcessing(HrmsNewsalaryItemRemindRecord record);

	/**
	 * 批量处理
	 * @param record
	 * @return
	 */
	Integer remindBatchProcessing(HrmsNewsalaryItemRemindRecord record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 *
	 * @Title batchNotAdjust
	 * @Description 批量不调整
	 * @param ids
	 * @return Integer
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	Integer batchNotAdjust(String ids);

	/**
	 * 删除员工指定周期内未处理的薪酬项提醒记录
	 * @param employeeId
	 * @param computeDate 算薪周期
	 * @return
	 */
	Integer deleteUnprocessedDataByEmployeeId(String employeeId,String computeDate);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryItemRemindRecord
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	HrmsNewsalaryItemRemindRecord selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryItemRemindRecord>
	 * @date 2024��10��29�� ����6:29:18
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryItemRemindRecord> getDataSetList(Page page, HrmsNewsalaryItemRemindRecord record);

    /**
     * 汇总统计员工薪酬异动数据
     * @param record
     * @return
     */
    DataSet<HrmsNewsalaryItemRemindRecord> getEmployeeSalaryRemindStatistics(Page page,HrmsNewsalaryItemRemindRecord record);
	/**
	 * 根据方案id和算薪周期查询是否存在未处理薪酬项提醒数据
	 * @param empIds 员工id列表
	 * @param computeDate
	 * @return
	 */
	Integer checkIsExistsUnprocessedRemindDataByEmpIds(List<String> empIds, String computeDate);
}

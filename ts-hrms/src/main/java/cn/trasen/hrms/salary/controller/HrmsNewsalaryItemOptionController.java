package cn.trasen.hrms.salary.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemOption;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemOptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalaryItemOptionController
 * @Description 薪酬方案项目中间表
 * @date 2023��11��11�� ����4:35:42
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "薪酬方案项目中间表Controller")
public class HrmsNewsalaryItemOptionController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryItemOptionController.class);

	@Autowired
	private HrmsNewsalaryItemOptionService hrmsNewsalaryItemOptionService;

	/**
	 * @Title saveHrmsNewsalaryItemOption
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:35:42
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/salaryItemOption/save")
	public PlatformResult<String> saveHrmsNewsalaryItemOption(@RequestBody HrmsNewsalaryItemOption record) {
		try {
			hrmsNewsalaryItemOptionService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryItemOption
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:35:42
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryItemOption/update")
	public PlatformResult<String> updateHrmsNewsalaryItemOption(@RequestBody HrmsNewsalaryItemOption record) {
		try {
			hrmsNewsalaryItemOptionService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryItemOptionById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryItemOption>
	 * @date 2023��11��11�� ����4:35:42
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/salaryItemOption/{id}")
	public PlatformResult<HrmsNewsalaryItemOption> selectHrmsNewsalaryItemOptionById(@PathVariable String id) {
		try {
			HrmsNewsalaryItemOption record = hrmsNewsalaryItemOptionService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryItemOptionById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:35:42
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/salaryItemOption/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryItemOptionById(@PathVariable String id) {
		try {
			hrmsNewsalaryItemOptionService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryItemOptionList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryItemOption>
	 * @date 2023��11��11�� ����4:35:42
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/salaryItemOption/list")
	public DataSet<HrmsNewsalaryItemOption> selectHrmsNewsalaryItemOptionList(Page page, HrmsNewsalaryItemOption record) {
		return hrmsNewsalaryItemOptionService.getDataSetList(page, record);
	}
}

package cn.trasen.hrms.salary.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 薪酬基本项，已废弃？和hrms_newsalary_item_library冲突？
 *
 */
@Table(name = "hrms_newsalary_item_basic")
@Setter
@Getter
public class HrmsNewsalaryItemBasic {
    /**
     * 薪酬项目id
     */
    @Id
    @ApiModelProperty(value = "薪酬项目id")
    private String id;

    /**
     * 项分组id
     */
    @Column(name = "group_id")
    @ApiModelProperty(value = "项分组id")
    private String groupId;

    /**
     * 薪酬项目名称
     */
    @Column(name = "item_name")
    @ApiModelProperty(value = "薪酬项目名称")
    private String itemName;

    /**
     * 薪酬项目编码
     */
    @Column(name = "item_code")
    @ApiModelProperty(value = "薪酬项目编码")
    private String itemCode;

    /**
     * 1基础信息2工资项
     */
    @Column(name = "item_type")
    @ApiModelProperty(value = "1基础信息2工资项")
    private String itemType;

    /**
     * 小数位
     */
    @Column(name = "item_digit")
    @ApiModelProperty(value = "小数位")
    private String itemDigit;

    /**
     * 1加项 2减项3不参与计算
     */
    @Column(name = "count_type")
    @ApiModelProperty(value = "1加项 2减项3不参与计算")
    private String countType;

    /**
     * 1手工录入2固定值3从薪资档案取4自定义公式
     */
    @Column(name = "item_rule")
    @ApiModelProperty(value = "1手工录入2固定值3从薪资档案取4自定义公式")
    private String itemRule;

    /**
     * 计算公式
     */
    @Column(name = "count_formula")
    @ApiModelProperty(value = "计算公式")
    private String countFormula;

    /**
     * 计算公式文本
     */
    @Column(name = "count_formula_text")
    @ApiModelProperty(value = "计算公式文本")
    private String countFormulaText;

    /**
     * 固定金额
     */
    @Column(name = "salary_item_amount")
    @ApiModelProperty(value = "固定金额")
    private BigDecimal salaryItemAmount;

    /**
     * 工资条备注
     */
    @Column(name = "salary_remark")
    @ApiModelProperty(value = "工资条备注")
    private String salaryRemark;

    /**
     * 1空值隐藏
     */
    @Column(name = "is_hidden")
    @ApiModelProperty(value = "1空值隐藏")
    private String isHidden;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
}
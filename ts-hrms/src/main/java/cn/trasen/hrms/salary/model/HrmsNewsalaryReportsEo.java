package cn.trasen.hrms.salary.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;

import javax.persistence.Table;
import java.util.Date;

/**
* 薪酬汇总报表
* @TableName hrms_newsalary_reports
*/
@Table(name = "hrms_newsalary_reports")
@Setter
@Getter
public class HrmsNewsalaryReportsEo {

    /**
    * ID
    */
    @Id
    private String id;
    /**
    * 汇总报表名称
    */
    @Column(name = "report_name")
    @ApiModelProperty(value = "汇总报表名称")
    private String reportName;
    /**
    * 备注
    */
    @Column(name = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
    * 创建人ID
    */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人ID")
    private String createUser;
    /**
    * 创建人姓名
    */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;
    /**
    * 创建时间
    */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;
    /**
    * 更新人ID
    */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;
    /**
    * 更新人姓名
    */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;
    /**
    * 更新时间
    */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;
    /**
    * 是否删除 N 正常   Y 删除
    */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;
    /**
    * 
    */
    private String ssoOrgCode;
    /**
    * 
    */
    private String ssoOrgName;

    /**
     * 报表类型 1-薪酬汇总 2-薪酬方案类 3-保险缴费类
     */
    @Column(name = "reports_type")
    @ApiModelProperty(value = "报表类型 1-薪酬汇总 2-薪酬方案类 3-保险缴费类")
    private String reportsType;

}

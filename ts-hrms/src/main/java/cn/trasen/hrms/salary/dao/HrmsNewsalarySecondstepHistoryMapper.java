package cn.trasen.hrms.salary.dao;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.model.HrmsNewsalarySecondstepHistory;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsNewsalarySecondstepHistoryMapper extends Mapper<HrmsNewsalarySecondstepHistory> {
    List<HrmsNewsalarySecondstepHistory> getData(Page page, HrmsNewsalaryOptionPayroll record);
}
package cn.trasen.hrms.salary.controller;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.hrms.salary.model.HrmsNewsalaryRemindSetting;
import cn.trasen.hrms.salary.service.HrmsNewsalaryRemindSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalaryRemindSettingController
 * @Description TODO
 * @date 2024��10��25�� ����3:46:52
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "hrmsNewsalaryRemindSettingController")
public class HrmsNewsalaryRemindSettingController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryRemindSettingController.class);

	@Autowired
	private HrmsNewsalaryRemindSettingService hrmsNewsalaryRemindSettingService;

	/**
	 * @Title saveHrmsNewsalaryRemindSetting
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��25�� ����3:46:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/hrmsNewsalaryRemindSetting/save")
	public PlatformResult<String> saveHrmsNewsalaryRemindSetting(@RequestBody HrmsNewsalaryRemindSetting record) {
		try {
			hrmsNewsalaryRemindSettingService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryRemindSetting
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��25�� ����3:46:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/hrmsNewsalaryRemindSetting/update")
	public PlatformResult<String> updateHrmsNewsalaryRemindSetting(@RequestBody HrmsNewsalaryRemindSetting record) {
		try {
			hrmsNewsalaryRemindSettingService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryRemindSettingById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryRemindSetting>
	 * @date 2024��10��25�� ����3:46:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/hrmsNewsalaryRemindSetting/{id}")
	public PlatformResult<HrmsNewsalaryRemindSetting> selectHrmsNewsalaryRemindSettingById(@PathVariable String id) {
		try {
			HrmsNewsalaryRemindSetting record = hrmsNewsalaryRemindSettingService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 *
	 * @Title selectHrmsNewsalaryRemindSettingById
	 * @Description 根据类型查询设置详情
	 * @param remindType
	 * @return PlatformResult<HrmsNewsalaryRemindSetting>
	 * @date 2024��10��25�� ����3:46:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "根据类型查询设置详情", notes = "根据类型查询设置详情")
	@GetMapping("/api/hrmsNewsalaryRemindSetting/getSettingByType")
	public PlatformResult<HrmsNewsalaryRemindSetting> selectHrmsNewsalaryRemindSettingByType(String remindType) {
		try {
			Integer type = Integer.valueOf(remindType);
			HrmsNewsalaryRemindSetting record = hrmsNewsalaryRemindSettingService.selectByType(type);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryRemindSettingById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��10��25�� ����3:46:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/hrmsNewsalaryRemindSetting/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryRemindSettingById(@PathVariable String id) {
		try {
			hrmsNewsalaryRemindSettingService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryRemindSettingList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryRemindSetting>
	 * @date 2024��10��25�� ����3:46:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/hrmsNewsalaryRemindSetting/list")
	public DataSet<HrmsNewsalaryRemindSetting> selectHrmsNewsalaryRemindSettingList(Page page, HrmsNewsalaryRemindSetting record) {
		return hrmsNewsalaryRemindSettingService.getDataSetList(page, record);
	}
}

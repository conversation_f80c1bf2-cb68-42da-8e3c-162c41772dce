package cn.trasen.hrms.salary.DTO;


import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicColumn;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 薪酬批量调整Vo
 */
@Data
public class NewSalaryBasicItemBatchAdjustReq {
    /**
     * 调薪原因
     */
    @ApiModelProperty(value = "调薪原因")
    private String reason;
    /**
     * 生效日期
     */
    @ApiModelProperty(value = "生效日期")
    private String effectiveDate;

    @ApiModelProperty(value = "员工id字符串")
    private List<String> employeeIds;

    /**
     * 政策标准id
     */
    @ApiModelProperty(value = "政策标准id")
    private String policyStandardId;

    /**
     * 普调薪酬项列表 金额为正数则表示在原基础+对应金额， 金额为负数则表示在原金额 - 对应金额
     */
    @ApiModelProperty(value = "政策标准id")
    private List<HrmsNewsalaryBasicColumn> basicColumnsList;
}

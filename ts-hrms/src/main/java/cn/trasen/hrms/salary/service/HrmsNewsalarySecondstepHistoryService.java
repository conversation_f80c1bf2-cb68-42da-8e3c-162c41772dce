package cn.trasen.hrms.salary.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.model.HrmsNewsalarySecondstepHistory;

/**
 * @ClassName HrmsNewsalarySecondstepHistoryService
 * @Description TODO
 * @date 2024��3��11�� ����5:53:01
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalarySecondstepHistoryService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��3��11�� ����5:53:01
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalarySecondstepHistory record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��3��11�� ����5:53:01
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalarySecondstepHistory record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��3��11�� ����5:53:01
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalarySecondstepHistory
	 * @date 2024��3��11�� ����5:53:01
	 * <AUTHOR>
	 */
	HrmsNewsalarySecondstepHistory selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalarySecondstepHistory>
	 * @date 2024��3��11�� ����5:53:01
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalarySecondstepHistory> getDataSetList(Page page, HrmsNewsalarySecondstepHistory record);

	//删除历史数据
	Integer deleteByOptionIdAndDate(String optionId, String computeDate);

	List<HrmsNewsalarySecondstepHistory> getData(Page page, HrmsNewsalaryOptionPayroll record);
}

package cn.trasen.hrms.salary.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalarySeniorityWageEf;

/**
 * @ClassName HrmsNewsalarySeniorityWageEfService
 * @Description TODO
 * @date 2024��4��17�� ����11:27:53
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalarySeniorityWageEfService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��4��17�� ����11:27:53
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalarySeniorityWageEf record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��4��17�� ����11:27:53
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalarySeniorityWageEf record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��4��17�� ����11:27:53
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalarySeniorityWageEf
	 * @date 2024��4��17�� ����11:27:53
	 * <AUTHOR>
	 */
	HrmsNewsalarySeniorityWageEf selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalarySeniorityWageEf>
	 * @date 2024��4��17�� ����11:27:53
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalarySeniorityWageEf> getDataSetList(Page page, HrmsNewsalarySeniorityWageEf record);

	//二福定时任务 在基础数据的基础上每年加三十
	public void changgeSalary();
	/**
	 * @Title getAllData
	 * @Description 获取所有数据
	 * @return List<HrmsNewsalarySeniorityWageEf>
	 * @date 2024��4��17�� ����11:27:53
	 * <AUTHOR>
	 */
	public List<HrmsNewsalarySeniorityWageEf> getAllData();

}

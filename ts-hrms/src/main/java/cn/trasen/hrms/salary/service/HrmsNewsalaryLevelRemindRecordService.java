package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.model.HrmsNewsalaryLevelRemindRecord;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

/**
 * @ClassName HrmsNewsalaryLevelRemindRecordService
 * @Description TODO
 * @date 2024��11��6�� ����10:33:38
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryLevelRemindRecordService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��11��6�� ����10:33:38
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryLevelRemindRecord record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��11��6�� ����10:33:38
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryLevelRemindRecord record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��11��6�� ����10:33:38
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryLevelRemindRecord
	 * @date 2024��11��6�� ����10:33:38
	 * <AUTHOR>
	 */
	HrmsNewsalaryLevelRemindRecord selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryLevelRemindRecord>
	 * @date 2024��11��6�� ����10:33:38
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryLevelRemindRecord> getDataSetList(Page page, HrmsNewsalaryLevelRemindRecord record);

	/**
	 * 薪级等级批量调整(异步)
	 * @param isAdjust 调整类型
	 * @param ids id
	 * @return
	 */
	SseEmitter batchSalaryLevelAdjust(String isAdjust,String ids);

	/**
	 * 薪级等级调整
	 * @param records 提醒记录对象集合
	 * @return
	 */
	Integer saveSalaryLevelAdjust(HrmsNewsalaryLevelRemindRecord records);

	/**
	 * 定时查询薪级等级异动数据
	 */
	void newsalaryLevelChange();
}

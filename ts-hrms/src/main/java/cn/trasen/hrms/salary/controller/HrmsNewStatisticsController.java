package cn.trasen.hrms.salary.controller;

import cn.trasen.BootComm.utils.RedisUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportTotalVo;
import cn.trasen.hrms.salary.DTO.SalaryCountSearchReq;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryItemMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.model.HrmsNewsalaryReportTotalEo;
import cn.trasen.hrms.salary.model.HrmsNewsalaryReportsEo;
import cn.trasen.hrms.salary.service.*;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.ExcelExportUtils;
import cn.trasen.hrms.utils.ExcelStyleUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.*;


@Slf4j
@RestController
@Api(tags = "薪酬管理-汇总统计controller")
public class HrmsNewStatisticsController {


    @Autowired
    private HrmsNewsalaryOptionPayrollService hrmsNewsalaryOptionPayrollService;

    @Autowired
    private HrmsNewsalaryItemService hrmsNewsalaryItemService;

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private HrmsNewsalaryReportTotalService hrmsNewsalaryReportTotalService;
    @Autowired
    private HrmsNewsalaryReportsStatisticsService newsalaryReportsStatisticsService;
    @Resource
    private HrmsNewsalaryItemMapper hrmsNewsalaryItemMapper;
    @Autowired
    private HrmsNewsalaryReportsService hrmsNewsalaryReportsService;

    //定薪调薪统计汇总
    @ApiOperation(value = "查询调薪记录表头", notes = "查询调薪记录表头")
    @GetMapping("/api/selectupdateSalaryHistory/statisticsListTitle")
    public PlatformResult<List<VueTableEntity>> statisticsListTitle(HrmsNewsalaryOptionPayroll record) {
        try {
            List<VueTableEntity> list =  hrmsNewsalaryOptionPayrollService.makePayTableTitleCount( record);
            return PlatformResult.success(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "查询调薪记录数据", notes = "查询调薪记录数据")
    @GetMapping("/api/selectupdateSalaryHistory/statisticsListData")
    public DataSet<Map<String, Object>> statisticsListData(Page page, HrmsNewsalaryOptionPayroll record) {
        return hrmsNewsalaryOptionPayrollService.makePayTableDataCount(page,record);
    }


    //导出excel
    @ApiOperation(value = "调薪记录导出汇总统计", notes = "调薪记录导出汇总统计")
    @GetMapping("/api/selectupdateSalaryHistory/export")
    public void exportUpdate(HttpServletRequest request, HttpServletResponse response, Page page, HrmsNewsalaryOptionPayroll record) {
        String filename = "员工调薪记录";
        List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
        List<VueTableEntity> listTitle = hrmsNewsalaryOptionPayrollService.makePayTableTitleCount(record);
        DataSet<Map<String, Object>> mapDataSet = hrmsNewsalaryOptionPayrollService.makePayTableDataCount(page, record);
        List<Map<String, Object>> rows = mapDataSet.getRows();
        for (VueTableEntity vueTableEntity : listTitle) {
        	ExcelExportEntity eee = new ExcelExportEntity(vueTableEntity.getLabel(), vueTableEntity.getProp());
        	if (vueTableEntity.getIsNum()) {
        		eee.setType(10); //数字格式；
        }
            colList.add(eee);
        }
        try {
            Workbook workbook = ExcelExportUtil
                    .exportExcel(new ExportParams(filename, "数据", ExcelType.XSSF), colList, rows);
            ExcelExportUtils.doubleStypeBug(workbook);
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename="
                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
            OutputStream fos = response.getOutputStream();
            workbook.write(fos);
            fos.close();
        } catch (Exception e) {
            log.error("导出数据异常"+e.getMessage());
        }
    }

    //月度汇总统计表头
    @ApiOperation(value = "薪酬方案明细表头", notes = "薪酬方案明细表头")
    @GetMapping("/api/selectMonthlySalary/salaryCountTitle/{optionId}")
    public PlatformResult<List<VueTableEntity>> salaryCountTitle(@PathVariable String optionId) {
        try {
            List<VueTableEntity> list =  hrmsNewsalaryItemService.salaryCountTitle(optionId);
            return PlatformResult.success(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
    //于都汇总统计数据
    @ApiOperation(value = "薪酬月度方案明细数据", notes = "薪酬月度方案明细数据")
    @GetMapping("/api/selectMonthlySalary/salaryCountData")
    public DataSet<Map<String, Object>> salaryCountData(Page page, SalaryCountSearchReq  record) {
        return hrmsNewsalaryOptionPayrollService.salaryCountData(page,record);
    }


    //选择导出字段
    @ApiOperation(value = "选择导出字段", notes = "选择导出字段")
    @GetMapping("/api/selectMonthlySalary/getExportColumns")
    public PlatformResult<Map<String,Object>> getExportColumns(String optionId) {
        try {
            //导出字段
            Map<String,Object> retMap = new LinkedHashMap<>();
            Map<String,String> rsxxMap = new LinkedHashMap<>();
            rsxxMap.put("employee_name","姓名");
            rsxxMap.put("employee_no","工号");
            rsxxMap.put("orgName","部门");
            rsxxMap.put("employee_status_text","员工状态");
            rsxxMap.put("personal_identity_text","岗位名称");
            rsxxMap.put("establishment_type_text","编制类型");
            rsxxMap.put("entry_date","入职日期");
            rsxxMap.put("positive_time","转正日期");
            rsxxMap.put("identity_number","身份证");
            rsxxMap.put("bankcardname","开户行");
            rsxxMap.put("bankcardno","银行账号");
            rsxxMap.put("optionName","薪酬组");
            rsxxMap.put("payroll_date","核算月份");

           List<LinkedHashMap<String,String>> list =  hrmsNewsalaryItemService.getExportColumns(optionId);
            Map<String, String> listMap = new LinkedHashMap<>();
            for (Map<String, String> map : list) {
                listMap.put(map.get("item_code"),map.get("item_name"));
            }
            retMap.put("rsxx",rsxxMap);
            retMap.put("gzx",listMap);
            return PlatformResult.success(retMap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }



    //月度汇总统计导出
    @ApiOperation(value = "月度薪酬方案明细导出", notes = "月度薪酬方案明细导出")
    @GetMapping("/api/selectMonthlySalary/export")
    public void selectMonthlySalaryExport(HttpServletRequest request, HttpServletResponse response, Page page, SalaryCountSearchReq record) {
        String filename = "月度方案明细记录";
        page.setPageSize(Integer.MAX_VALUE);
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        //拿出请求参数
        try {
            String json = (String) redisUtil.get("hzqqcs" + user.getUsercode());
            ObjectMapper objectMapper = new ObjectMapper();
            record = objectMapper.readValue(json, SalaryCountSearchReq.class);
        }catch (Exception e){
            log.error("获取请求参数异常"+e.getMessage(),e);
        }

        List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();

        List<VueTableEntity> listTitle =  hrmsNewsalaryItemService.salaryCountTitleByDefinition(record);

        DataSet<Map<String, Object>> mapDataSet = hrmsNewsalaryOptionPayrollService.salaryCountData(page,record);

        List<Map<String, Object>> rows = mapDataSet.getRows();
        int size = rows.size();
        int count = 0; //获取导出的报表薪酬项数量
        //人事信息字段
        List<String> basicTitle = Arrays.asList("no","payroll_date","employee_name","employee_no","orgName","optionName","employee_status_text","personal_identity_text","establishment_type_text","entry_date","positive_time","identity_number","bankcardname","bankcardno");
        for (VueTableEntity vueTableEntity : listTitle) {
        	ExcelExportEntity eee = new ExcelExportEntity(vueTableEntity.getLabel(), vueTableEntity.getProp());
        	if (vueTableEntity.getIsNum()) {
        		eee.setType(10);
            }
            colList.add(eee);
            if(rows.get(0).containsKey(vueTableEntity.getProp())
                    && !basicTitle.contains(vueTableEntity.getProp())
                    && rows.get(0).get(vueTableEntity.getProp()) instanceof Number){
                count ++;
            }
        }
        try {
            ExportParams exportParams = new ExportParams(filename, "数据", ExcelType.XSSF);
            exportParams.setStyle(ExcelStyleUtil.class);
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, rows);
            // 最后一行的合并单元格
            Sheet sheet = workbook.getSheetAt(0);
            Row row = sheet.getRow(size + 1);
            Cell cell = row.getCell(0);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(size + 1, size + 1, 0, (listTitle.size()-count - 3))); // 合并最后一行的第一列到第三列
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER); //水平居中
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直居中
            cellStyle.setBorderBottom(BorderStyle.THIN);//下边框
            cellStyle.setBorderLeft(BorderStyle.THIN);//左边框
            cellStyle.setBorderTop(BorderStyle.THIN);//上边框
            cellStyle.setBorderRight(BorderStyle.THIN); //右边框
            cell.setCellStyle(cellStyle);
            cell.setCellValue("合计");

            ExcelExportUtils.doubleStypeBug(workbook);
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename="
                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
            OutputStream fos = response.getOutputStream();
            workbook.write(fos);
            fos.close();
        } catch (Exception e) {
            log.error("导出数据异常"+e.getMessage());
        }
    }

    @ApiOperation(value = "月度薪酬方案明细导出请求参数", notes = "月度方案明细导出请求参数")
    @PostMapping("/api/selectMonthlySalary/exportSubmitData")
    public PlatformResult<String> handlePostRequest(@RequestBody  SalaryCountSearchReq data ) {
        // 处理POST请求中的参数
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 将Java对象转换为JSON字符串
            String json = objectMapper.writeValueAsString(data);
            redisUtil.set("hzqqcs"+user.getUsercode(),json,30);  //有效期30秒
            log.error("r月度薪酬汇总导出请求参数"+json);
            return PlatformResult.success("api/selectMonthlySalary/export");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure("请求参数接收异常"+e.getMessage());
        }

    }

    @ApiOperation(value = "薪酬月度方案汇总数据", notes = "薪酬月度方案汇总数据")
    @GetMapping("/api/selectSalary/salaryTotal")
    public DataSet<Map<String, Object>> salaryTotalCountData(HttpServletRequest request, HttpServletResponse response, Page page, SalaryCountSearchReq record) {
        return hrmsNewsalaryOptionPayrollService.salaryTotalCountData(page,record);
    }

    //月度汇总统计表头
    @ApiOperation(value = "薪酬月度汇总表头", notes = "薪酬月度汇总表头")
    @GetMapping("/api/selectMonthlySalary/salaryTotalTitle/{reportId}")
    public PlatformResult<List<VueTableEntity>> salaryTotalTitle(@PathVariable String reportId) {
        try {
            List<VueTableEntity> list =  hrmsNewsalaryItemService.salaryTotalTitle(reportId);
            return PlatformResult.success(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "月度薪酬汇总导出请求参数", notes = "月度薪酬汇总导出请求参数")
    @PostMapping("/api/selectMonthlySalary/exportTotalSubmitData")
    public PlatformResult<String> exportTotalSubmitData(@RequestBody  SalaryCountSearchReq data ) {
        // 处理POST请求中的参数
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 将Java对象转换为JSON字符串
            String json = objectMapper.writeValueAsString(data);
            redisUtil.set("hzqqcshz"+user.getUsercode(),json,30);  //有效期30秒
            log.error("月度薪酬汇总导出请求参数"+json);
            return PlatformResult.success("api/selectMonthlySalary/totalExport");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure("请求参数接收异常"+e.getMessage());
        }

    }

    @ApiOperation(value = "月度薪酬汇总导出", notes = "月度薪酬汇总导出")
    @GetMapping("/api/selectMonthlySalary/totalExport")
    public void totalExport(HttpServletRequest request, HttpServletResponse response, Page page, SalaryCountSearchReq record) {
        String filename = "月度汇总记录表";
        page.setPageSize(Integer.MAX_VALUE);
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        //拿出请求参数
        try {
            String json = (String) redisUtil.get("hzqqcshz" + user.getUsercode());
            ObjectMapper objectMapper = new ObjectMapper();
            record = objectMapper.readValue(json, SalaryCountSearchReq.class);
        }catch (Exception e){
            log.error("获取请求参数异常"+e.getMessage(),e);
        }

        List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();

        List<VueTableEntity> listTitle =  hrmsNewsalaryItemService.salaryTotalCountTitleByDefinition(record);

        DataSet<Map<String, Object>> mapDataSet = hrmsNewsalaryOptionPayrollService.salaryTotalCountData(page,record);
        int size = mapDataSet.getRows().size();

        HrmsNewsalaryReportTotalVo hrmsNewsalaryReportTotalVo = new HrmsNewsalaryReportTotalVo();
        hrmsNewsalaryReportTotalVo.setReportId(record.getReportId());
        hrmsNewsalaryReportTotalVo.setPayrollDate(record.getPayrollDate());
        PlatformResult<Map<String, Object>> totalData = hrmsNewsalaryReportTotalService.getTotalData(hrmsNewsalaryReportTotalVo);
        StringBuilder sb = new StringBuilder();
        sb.append("汇总：发送人数：");
        sb.append(totalData.getObject().get("numbers"));
        sb.append(" |应发合计：￥");
        sb.append(totalData.getObject().get("yfSalary"));
        sb.append(" |个税扣除：￥");
        sb.append(totalData.getObject().get("gsSalary"));
        sb.append(" |实发合计：￥");
        sb.append(totalData.getObject().get("sfSalary"));
        sb.append("    导出人：");
        sb.append(user.getUsername());
        sb.append("    导出日期：");
        sb.append(DateUtils.getDateString());

        List<Map<String, Object>> rows = mapDataSet.getRows();
        List<HrmsNewsalaryReportTotalEo> list = hrmsNewsalaryItemMapper.salaryTotalCountTitle(record.getReportId());

        int count = 0; //获取导出的报表薪酬项数量
        for (VueTableEntity vueTableEntity : listTitle) {
        	ExcelExportEntity eee = new ExcelExportEntity(vueTableEntity.getLabel(), vueTableEntity.getProp());
        	if (vueTableEntity.getIsNum()) {
//				eee.setFormat("#,##0.00");
        		eee.setType(10);
            }
            colList.add(eee);
            count += list.stream().filter(vo->vo.getColCode().equalsIgnoreCase(vueTableEntity.getProp())).count();
        }
        //为空的数据保存为0
        if (CollectionUtils.isNotEmpty(list)){
            for (HrmsNewsalaryReportTotalEo hrmsNewsalaryReportTotalEo:list){
                for (Map<String, Object> map:rows){
                    if (!map.containsKey(hrmsNewsalaryReportTotalEo.getColCode())){
                        map.put(hrmsNewsalaryReportTotalEo.getColCode(),"0.00");
                    }
                }
            }
        }
        try {
            ExportParams exportParams = new ExportParams(filename, "数据", ExcelType.XSSF);
            exportParams.setStyle(ExcelStyleUtil.class);
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, rows);
            // 最后一行的合并单元格
            Sheet sheet = workbook.getSheetAt(0);
            Row row = sheet.getRow(size + 1);
            Cell cell = row.getCell(0);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(size + 1, size + 1, 0, (listTitle.size()-count - 3))); // 合并最后一行的第一列到第三列
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER); //水平居中
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直居中
            cellStyle.setBorderBottom(BorderStyle.THIN);//下边框
            cellStyle.setBorderLeft(BorderStyle.THIN);//左边框
            cellStyle.setBorderTop(BorderStyle.THIN);//上边框
            cellStyle.setBorderRight(BorderStyle.THIN); //右边框
            cell.setCellStyle(cellStyle);
            cell.setCellValue("合计");

            Row row2 = sheet.createRow(size + 3);
            Cell cell2 = row2.createCell(0);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(size + 2, size + 2, 0, listTitle.size())); // 合并最后一行的第一列到第三列
            cell2.setCellValue(sb.toString());
            CellStyle cellStyle2 = workbook.createCellStyle();
            cellStyle2.setAlignment(HorizontalAlignment.CENTER);
            cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER); //垂直居中
            cellStyle2.setBorderBottom(BorderStyle.THIN);//下边框
            cellStyle2.setBorderLeft(BorderStyle.THIN);//左边框
            cellStyle2.setBorderTop(BorderStyle.THIN);//上边框
            cellStyle2.setBorderRight(BorderStyle.THIN); //右边框
            cell2.setCellStyle(cellStyle2);
            ExcelExportUtils.doubleStypeBug(workbook);
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename="
                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
            OutputStream fos = response.getOutputStream();
            workbook.write(fos);
            fos.close();
        } catch (Exception e) {
            log.error("导出数据异常"+e.getMessage());
        }

    }

    //选择导出字段
    @ApiOperation(value = "选择汇总导出字段", notes = "选择导出字段")
    @GetMapping(value = "/api/selectMonthlySalary/getTotalExportColumns/{reportId}")
    public PlatformResult<Map<String,Object>> getTotalExportColumns(@PathVariable String reportId) {
        try {
            //导出字段
            Map<String,Object> retMap = new LinkedHashMap<>();
            Map<String,String> rsxxMap = new LinkedHashMap<>();
            rsxxMap.put("employee_name","姓名");
            rsxxMap.put("employee_no","工号");
            rsxxMap.put("orgName","部门");
            rsxxMap.put("employee_status_text","员工状态");
            rsxxMap.put("personal_identity_text","岗位名称");
            rsxxMap.put("establishment_type_text","编制类型");
            rsxxMap.put("entry_date","入职日期");
            rsxxMap.put("positive_time","转正日期");
            rsxxMap.put("identity_number","身份证");
            rsxxMap.put("bankcardname","开户行");
            rsxxMap.put("bankcardno","银行账号");
            rsxxMap.put("optionName","薪酬组");
            rsxxMap.put("payroll_date","核算月份");

            List<LinkedHashMap<String,String>> list =  hrmsNewsalaryItemService.getTotalExportColumns(reportId);
            Map<String, String> listMap = new LinkedHashMap<>();
            for (Map<String, String> map : list) {
                listMap.put(map.get("col_code"),map.get("col_name"));
            }
            retMap.put("rsxx",rsxxMap);
            retMap.put("gzx",listMap);
            return PlatformResult.success(retMap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @TODO 方案汇总类接口 add ni.jiang
     * @param reportId
     * @return
     */
    @ApiOperation(value = "薪酬方案汇总表头", notes = "薪酬方案汇总表头")
    @GetMapping("/api/newsalaryReportsStatistics/salaryOptionTotalTitle/{reportId}")
    public PlatformResult<List<VueTableEntity>> salaryOptionTotalTitle(@PathVariable String reportId) {
        try {
            List<VueTableEntity> list =  newsalaryReportsStatisticsService.salaryOptionTotalTitle(reportId);
            return PlatformResult.success(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "薪酬方案汇总数据", notes = "薪酬方案汇总数据")
    @GetMapping("/api/newsalaryReportsStatistics/salaryOptionStatisticsData")
    public DataSet<Map<String, Object>> salaryOptionStatisticsData(HttpServletRequest request, HttpServletResponse response, Page page, SalaryCountSearchReq record) {
        return newsalaryReportsStatisticsService.salaryOptionStatisticsData(page,record);
    }

    @ApiOperation(value = "薪酬方案汇总导出", notes = "薪酬方案汇总导出")
    @GetMapping("/api/newsalaryReportsStatistics/salaryOptionExport")
    public void salaryOptionExport(HttpServletRequest request, HttpServletResponse response, Page page, SalaryCountSearchReq record) {
        Assert.hasText(record.getReportId(),"报表id不能为空");
        Assert.hasText(record.getPayrollDate(),"算薪周期不能为空");
        String filename = "薪酬方案汇总表";
        page.setPageSize(Integer.MAX_VALUE);

        List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();

        //获取报表名称
        HrmsNewsalaryReportsEo reportsEo = hrmsNewsalaryReportsService.getById(record.getReportId());
        filename = filename + '-' + reportsEo.getReportName() +"[" + record.getPayrollDate()+"]";
        //获取表头
        List<VueTableEntity> listTitle =  newsalaryReportsStatisticsService.salaryOptionTotalTitle(record.getReportId());

        //获取导出数据
        DataSet<Map<String, Object>> mapDataSet = newsalaryReportsStatisticsService.salaryOptionStatisticsData(page,record);
        int size = mapDataSet.getRows().size();
        List<Map<String, Object>> rows = mapDataSet.getRows();
        //获取薪酬配置的字段列表
        List<HrmsNewsalaryReportTotalEo> list = hrmsNewsalaryItemMapper.salaryTotalCountTitle(record.getReportId());
        for (VueTableEntity vueTableEntity : listTitle) {
            ExcelExportEntity eee = new ExcelExportEntity(vueTableEntity.getLabel(), vueTableEntity.getProp());
            if("option_name".equalsIgnoreCase(vueTableEntity.getProp())) {
                eee = new ExcelExportEntity(vueTableEntity.getLabel(), vueTableEntity.getProp(),25);
            }else if("establishment_types".equalsIgnoreCase(vueTableEntity.getProp())){
                eee = new ExcelExportEntity(vueTableEntity.getLabel(), vueTableEntity.getProp(),20);
            }
            if (vueTableEntity.getIsNum()) {
                eee.setType(10);
            }
            colList.add(eee);
        }
        //为空的数据保存为0
        if (CollectionUtils.isNotEmpty(list)){
            for (HrmsNewsalaryReportTotalEo hrmsNewsalaryReportTotalEo:list){
                for (Map<String, Object> map:rows){
                    if (!map.containsKey(hrmsNewsalaryReportTotalEo.getColCode())){
                        map.put(hrmsNewsalaryReportTotalEo.getColCode(),"0.00");
                    }
                }
            }
        }
        try {
            ExportParams exportParams = new ExportParams(filename, "数据", ExcelType.XSSF);
            exportParams.setStyle(ExcelStyleUtil.class);
            Workbook workbook = ExcelExportUtil.exportExcel( exportParams, colList, rows);
            // 最后一行的合并单元格
            Sheet sheet = workbook.getSheetAt(0);
            Row row = sheet.getRow(size + 1);
            Cell cell = row.getCell(0);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(size + 1, size + 1, 0, 2)); // 合并最后一行的第一列到第三列
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER); //水平居中
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直居中
            cellStyle.setBorderBottom(BorderStyle.THIN);//下边框
            cellStyle.setBorderLeft(BorderStyle.THIN);//左边框
            cellStyle.setBorderTop(BorderStyle.THIN);//上边框
            cellStyle.setBorderRight(BorderStyle.THIN); //右边框
            cell.setCellStyle(cellStyle);
            //添加会签人员
            addSignRow(workbook,sheet,size);
            ExcelExportUtils.doubleStypeBug(workbook);
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename="
                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
            OutputStream fos = response.getOutputStream();
            workbook.write(fos);
            fos.close();
        } catch (Exception e) {
            log.error("导出数据异常"+e.getMessage());
            e.printStackTrace();
        }
    }

    private void addSignRow(Workbook workbook,Sheet sheet,int size){
        //添加会签的人员数据
        List<String> signUser = Arrays.asList("制表人","人事部","财务部","人事分管领导","院长");
        Row signRow = sheet.createRow(size + 3);
        int cellIdx = 0;
        Cell signCell = null;
        CellStyle signCellStyle = null;
        for(String user : signUser) {
            signCell = signRow.createCell(cellIdx);
            signCellStyle = workbook.createCellStyle();
            signCellStyle.setAlignment(HorizontalAlignment.LEFT); //水平居中
            signCellStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直居中
            signCellStyle.setBorderBottom(BorderStyle.THIN); //下边框
            signCellStyle.setBorderLeft(BorderStyle.THIN); //左边框
            signCellStyle.setBorderTop(BorderStyle.THIN);//上边框
            signCellStyle.setBorderRight(BorderStyle.THIN);//右边框
            signCell.setCellStyle(signCellStyle);
            signCell.setCellValue(user+":");

            signCell = signRow.createCell(cellIdx+1);
            signCellStyle = workbook.createCellStyle();
            signCellStyle.setAlignment(HorizontalAlignment.LEFT); //水平居中
            signCellStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直居中
            signCellStyle.setBorderBottom(BorderStyle.THIN); //下边框
            signCellStyle.setBorderLeft(BorderStyle.THIN); //左边框
            signCellStyle.setBorderTop(BorderStyle.THIN);//上边框
            signCellStyle.setBorderRight(BorderStyle.THIN);//右边框
            signCell.setCellStyle(signCellStyle);
            signCell.setCellValue(" ");
            cellIdx += 2;
        }
    }

    @ApiOperation(value = "保险缴费报表表头", notes = "保险缴费报表表头")
    @GetMapping("/api/newsalaryReportsStatistics/salaryInsuranceTotalTitle/{reportId}")
    public PlatformResult<List<VueTableEntity>> salaryInsuranceTotalTitle(@PathVariable String reportId) {
        try {
            List<VueTableEntity> list =  newsalaryReportsStatisticsService.salaryInsuranceTotalTitle(reportId);
            return PlatformResult.success(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "保险缴费报表数据", notes = "保险缴费报表数据")
    @GetMapping("/api/newsalaryReportsStatistics/salaryInsuranceStatisticsData")
    public DataSet<Map<String, Object>> salaryInsuranceStatisticsData(HttpServletRequest request, HttpServletResponse response, Page page, SalaryCountSearchReq record) {
        return newsalaryReportsStatisticsService.salaryInsuranceStatisticsData(page,record);
    }

    @ApiOperation(value = "保险缴费报表导出", notes = "保险缴费报表导出")
    @GetMapping("/api/newsalaryReportsStatistics/salaryInsuranceExport")
    public void salaryInsuranceExport(HttpServletRequest request, HttpServletResponse response, Page page, SalaryCountSearchReq record) {
        Assert.hasText(record.getReportId(),"报表id不能为空");
        Assert.hasText(record.getPayrollDate(),"算薪周期不能为空");
        String filename = "保险缴费报表";
        page.setPageSize(Integer.MAX_VALUE);

        List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();

        //获取报表名称
        HrmsNewsalaryReportsEo reportsEo = hrmsNewsalaryReportsService.getById(record.getReportId());
        filename = filename + '-' + reportsEo.getReportName() +"[" + record.getPayrollDate()+"]";
        //获取表头
        List<VueTableEntity> listTitle =  newsalaryReportsStatisticsService.salaryInsuranceTotalTitle(record.getReportId());

        //获取导出数据
        DataSet<Map<String, Object>> mapDataSet = newsalaryReportsStatisticsService.salaryInsuranceStatisticsData(page,record);
        int size = mapDataSet.getRows().size();
        List<Map<String, Object>> rows = mapDataSet.getRows();
        //获取薪酬配置的字段列表
        List<HrmsNewsalaryReportTotalEo> list = hrmsNewsalaryItemMapper.salaryTotalCountTitle(record.getReportId());

        for (VueTableEntity vueTableEntity : listTitle) {
            ExcelExportEntity eee = new ExcelExportEntity(vueTableEntity.getLabel(), vueTableEntity.getProp());
            if (vueTableEntity.getIsNum()) {
                eee.setType(10);
            }
            colList.add(eee);
        }
        //为空的数据保存为0
        if (CollectionUtils.isNotEmpty(list)){
            for (HrmsNewsalaryReportTotalEo hrmsNewsalaryReportTotalEo:list){
                for (Map<String, Object> map:rows){
                    if (!map.containsKey(hrmsNewsalaryReportTotalEo.getColCode())){
                        map.put(hrmsNewsalaryReportTotalEo.getColCode(),"0.00");
                    }
                }
            }
        }
        try {
            ExportParams exportParams = new ExportParams(filename, "数据", ExcelType.XSSF);
            exportParams.setStyle(ExcelStyleUtil.class);
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, rows);

            ExcelExportUtils.doubleStypeBug(workbook);
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename="
                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
            OutputStream fos = response.getOutputStream();
            workbook.write(fos);
            fos.close();
        } catch (Exception e) {
            log.error("导出数据异常"+e.getMessage());
            e.printStackTrace();
        }
    }
}

package cn.trasen.hrms.salary.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryPayrollVo;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayroll;
import cn.trasen.hrms.salary.utils.VueTableEntity;

/**
 * @ClassName HrmsNewsalaryPayrollService
 * @Description TODO
 * @date 2023��11��11�� ����4:37:44
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryPayrollService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:37:44
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryPayroll record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��11��11�� ����4:37:44
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryPayroll record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��11��11�� ����4:37:44
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryPayroll
	 * @date 2023��11��11�� ����4:37:44
	 * <AUTHOR>
	 */
	HrmsNewsalaryPayroll selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryPayroll>
	 * @date 2023��11��11�� ����4:37:44
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryPayroll> getDataSetList(Page page, HrmsNewsalaryPayroll record);

    Integer batchInsert(List<HrmsNewsalaryPayroll> payrollRecords);

	Integer deleteByExample(String optionId, String computeDate);

	//发送工资条统计
	Map<String, String> getCount(String computeDate);
	//发送工资条统计列表
	List<Map<String,String>> getCountList(String computeDate);

	//批量发送工资条
	Integer batchSend(String computeDate, List<String> optionId,String ids);

	DataSet<HrmsNewsalaryPayroll> getRecordslist(Page page, HrmsNewsalaryPayroll record);

	//全部撤回接口
	Integer revocation(HrmsNewsalaryPayroll record);

	Integer singleRevocation(HrmsNewsalaryPayroll record);

	Integer singleSend(HrmsNewsalaryPayroll record);


	DataSet<HrmsNewsalaryPayroll> getCountTitleDetails(Page page, HrmsNewsalaryPayroll record);

	Integer setIsView(String employeeId, String payrollDate);

    Integer deleteByEmployeeIdAndDate(String employeeId, String computeDate);

	List<VueTableEntity> getSalaryMonthRecordHeadList(String employeeId);

	DataSet<HrmsNewsalaryPayrollVo> selectNewsalaryPayrollList(Page page, HrmsNewsalaryPayrollVo record);

//	Map<String,Object> summaryData(HrmsNewsalaryPayrollVo record);

	List<Map<String,Object>> summaryData(HrmsNewsalaryPayrollVo record);

	Map<String,Object> sendSalaryDeatilsSummaryData(HrmsNewsalaryPayrollVo record);

	/**
	 * 获取员工月份所有薪资发放项目
	 * @param employeeId,computeDate
	 * @return
	 */
	List<Map<String,String>> getPayrollByEmployeeId(String employeeId,String computeDate);
	
//	HrmsNewsalaryPayroll getPayrollByEmpId(String employeeId, String computeDate);
	
	HrmsNewsalaryPayroll selectByEmpId(String employeeId, String computeDate);
}

package cn.trasen.hrms.ywgl.server;

import java.util.List;
import java.util.Map;

/**   
 * @ClassName:  CallHlglService   
 * @Description:调用护理管理数据   
 * @author: WZH
 * @date:   2021年11月15日 下午3:31:31      
 * @Copyright:  
 */
public interface CallYwglService {
	
	
	/**
	 * @param parMap    
	 * @Title: findAllDate   
	 * @Description: 同步护理数据 
	 * @param:       
	 * @return: void      
	 * @throws   
	 */
	public void findAllDate(Map<String, String> parMap);
	
	/**
	 * 
	 * @Title: selectAllPersonJobDescription
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @return 参数
	 * @return List<Map<String,Object>> 返回类型
	 * 2021年12月27日
	 * ADMIN
	 * @throws
	 */
	public List<Map<String,Object>> selectAllPersonJobDescription();
}

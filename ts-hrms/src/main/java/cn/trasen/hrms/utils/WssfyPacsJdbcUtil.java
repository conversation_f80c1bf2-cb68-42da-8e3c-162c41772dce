package cn.trasen.hrms.utils;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;

import cn.trasen.hrms.med.risk.model.RiskOperationDiscuss;
import lombok.extern.log4j.Log4j2;

/**
 * 湖南省人民医院EMR正式库
 * <AUTHOR> 
 */
@Log4j2
public class WssfyPacsJdbcUtil {

	 /**
     * 获取一个连接
     * @return
     * @throws Exception
     */
    public static Connection getConnection() throws Exception {
    	
        String user = "cxpacsyw";
        String password = "Wsfy@trasen2025";
        String url = "***************************************************";
        String driverClass = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
     
        // 2. 加载驱动
        Class.forName(driverClass);

        Connection connection = DriverManager.getConnection(url, user, password);
        return connection;
    }

    /**
     * 关闭连接
     * @param connection
     * @param statement
     */
    public static void closeConnection(Connection connection, Statement statement) {
        if (connection !=  null){
            try {
                connection.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (statement != null){
            try {
                statement.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
    }

    /**
     * 关闭连接
     * @param connection
     * @param statement
     */
    public static void closeConnection(Connection connection, Statement statement, ResultSet resultSet) {
        if (resultSet != null){
            try {
                resultSet.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (connection !=  null){
            try {
                connection.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (statement != null){
            try {
                statement.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
    }


    /**
     * <AUTHOR>
     * @date: 21/1/12 11:49
     * @Description: query
     * @param sql 执行的sql
     * @param clazz 查询的结果的类型
     * @param args sql的参数
     * @return: List<T>
     */
    public static  <T> List<T> query(String sql, Class<T> clazz, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<T> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
                T t = clazz.newInstance();
                for (int i = 0; i < columnCount; i++) {
                    // 7.1获取列值
                    Object object = resultSet.getObject(i + 1);
                    // 7.2获取列别名
                    String columnLabel = metaData.getColumnLabel(i + 1);
                    // 7.3获取属性并设置属性的值
                    Field field = clazz.getDeclaredField(columnLabel.toLowerCase());
                    field.setAccessible(true);
                    field.set(t, object);
                }
                list.add(t);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }

        return list;
    }

    /**
     * @auther: ADMIN
     * @date: 21/1/12 11:52
     * @Description: update 更新
     * @param sql 执行的sql
     * @param obj 可变参数
     * @return: int 改变的条数
     */
    public static int update(String sql, Object ...obj){
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        try {
            // 1.获取连接
            connection = getConnection();
            // 2.预编译sql，返回一个PrepareStatement实例
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < obj.length; i++) {
                preparedStatement.setObject(i+1, obj[i]);
            }
            // 4.执行
            return preparedStatement.executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 5.关闭资源
            closeConnection(connection, preparedStatement);
        }
        return 0;
    }

	public static List<Map<String, Object>> queryVExamResult(String sql, Object ...args) {
		
		 	Connection connection = null;
	        PreparedStatement preparedStatement = null;
	        ResultSet resultSet = null;
	        ArrayList<Map<String,Object>> list = null;
	        
	        try {
	            // 1.获取一个连接
	            connection = getConnection();

	            // 2.预编译一个sql语句，返回一个PrepareStatement对象
	            preparedStatement = connection.prepareStatement(sql);
	            // 3.填充占位符
	            for (int i = 0; i < args.length; i++) {
	                preparedStatement.setObject(i+1, args[i]);
	            }
	            // 4。执行sql,得到结果集
	            resultSet = preparedStatement.executeQuery();
	            // 5.获取元数据
	            ResultSetMetaData metaData = resultSet.getMetaData();
	            int columnCount = metaData.getColumnCount();
	            list = new ArrayList();
	            // 7.遍历得到每一行数据
	            while (resultSet.next()){
	            	
	            	Map<String,Object> map = new HashMap<>();
	            	
					map.put("reportNo", resultSet.getString("reportNo"));//报告单号
					map.put("examTypeLargeName", resultSet.getString("examTypeLargeName"));//检查大类名称
					map.put("examTypeName", resultSet.getString("examTypeName"));//检查类型名称
					map.put("patientId", resultSet.getString("patientId"));//病人id
					map.put("name", resultSet.getString("name"));//姓名
					map.put("sexName", resultSet.getString("sexName"));//性别名称
					map.put("birthday", resultSet.getString("birthday"));//出生日期
					map.put("requestUserId", resultSet.getString("requestUserId"));//申请人id
					map.put("requestUserName", resultSet.getString("requestUserName"));//申请人名称
					map.put("requestDeptId", resultSet.getString("requestDeptId"));//申请科室id
					map.put("requestDeptName", resultSet.getString("requestDeptName"));//申请科室名称
//					map.put("applyDateTime", resultSet.getString("applyDateTime"));//申请日期
					map.put("reporDeptId", resultSet.getString("reporDeptId"));//报告科室id
					map.put("reportDeptName", resultSet.getString("reportDeptName"));//报告科室名称
					map.put("examBodyName", resultSet.getString("examBodyName"));//检查部位名称
					map.put("examWayName", resultSet.getString("examWayName"));//检查方法名称
					map.put("examEquipment", resultSet.getString("examEquipment"));//检查设备名称
					map.put("reportDate", resultSet.getString("reportDate"));//报告日期
					map.put("reportUserName", resultSet.getString("reportUserName"));//报告人姓名
					map.put("reportUserId", resultSet.getString("reportUserId"));//报告人id
					map.put("reportResult", resultSet.getString("reportResult"));//报告结果
					map.put("examDate", resultSet.getString("examDate"));//检查日期
					map.put("examUserName", resultSet.getString("examUserName"));//检查人姓名
					map.put("examUserId", resultSet.getString("examUserId"));//检查人id
					map.put("examFinding", resultSet.getString("examFinding"));//检查所见
					map.put("reportRemarks", resultSet.getString("reportRemarks"));//报告备注
					map.put("fileLink", resultSet.getString("fileLink"));//文件链接（pdf地址）
//					map.put("filePicLink", resultSet.getString("filePicLink"));//文件链接（图片地址）
					map.put("crisisFlag", resultSet.getString("crisisFlag"));//危急值标志  0-否 1-是
					map.put("crisisDesc", resultSet.getString("crisisDesc"));//危急值描述
					
	                list.add(map);
	            }
	        } catch (Exception e) {
	            e.printStackTrace();
	            log.error("获取PACS检查单异常：" + e.getMessage());
	            closeConnection(connection, preparedStatement, resultSet);
	        } finally {
	            closeConnection(connection, preparedStatement, resultSet);
	        }
	        
	        return list;
	}

}

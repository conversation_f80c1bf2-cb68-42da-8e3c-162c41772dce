package cn.trasen.hrms.utils;


import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;

import com.alibaba.fastjson.JSONObject;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class SendMessageUtils {

	public static void sendMessageHnsrmyy(String mobiles,String content,String operName,String yqbm) {
		// 构建 XML 参数
		String paramString = "<Request>"
				+"<CORPORATE_CODE>1</CORPORATE_CODE>"
				+"<CORPORATE_NAME>创星</CORPORATE_NAME>"
				+"<YWLX>99</YWLX>"
				+"<YWLX_NAME>医务</YWLX_NAME>"
				+"<SYSTEM_NAME>医务系统</SYSTEM_NAME>"
				+"<MOBILES>"+mobiles+"</MOBILES>"
				+"<CONTENT>"+content+"</CONTENT>"
				+"<OPER_NAME>"+operName+"</OPER_NAME>"
				+"<YQBM>"+yqbm+"</YQBM>"
				+ "</Request>";
		try {
	        JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance();
	        Client client = dcf.createClient("http://192.168.10.35:9009/TrasenSmsWebService.asmx?wsdl");
	        log.info("调用参数:"+paramString);
	    	log.info("调用ExeWebServiceSmsData");
	    	Object[] objects = client.invoke("ExeWebServiceSmsData",paramString);
	    	log.info("返回数据:" + JSONObject.toJSONString(objects[0]));
        } catch (java.lang.Exception e) {
        	e.printStackTrace();
        }
	}
	
	public static void main(String[] args) {
        // 测试短信发送
		sendMessageHnsrmyy("17388950700","测试短信发送","admin","1");
    }
	
}

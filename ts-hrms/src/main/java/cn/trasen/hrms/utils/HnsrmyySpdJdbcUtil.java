package cn.trasen.hrms.utils;


import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

import cn.trasen.hrms.med.cost.model.MedCostView;
import lombok.extern.log4j.Log4j2;
 
/**
 * 湖南省人民医院影子库jdbc
 * <AUTHOR> 
 */
@Log4j2
public class HnsrmyySpdJdbcUtil {
	
	
	 /**
     * 获取一个连接
     * @return
     * @throws Exception
     */
    public static Connection getConnection() throws Exception {
    	
        String user = "spduser";
        String password = "123456";
        String url = "*****************************************";
        String driverClass = "oracle.jdbc.driver.OracleDriver";
     
        // 2. 加载驱动
        Class.forName(driverClass);

        Connection connection = DriverManager.getConnection(url, user, password);
        return connection;
    }

    /**
     * 关闭连接
     * @param connection
     * @param statement
     */
    public static void closeConnection(Connection connection, Statement statement) {
        if (connection !=  null){
            try {
                connection.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (statement != null){
            try {
                statement.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
    }

    /**
     * 关闭连接
     * @param connection
     * @param statement
     */
    public static void closeConnection(Connection connection, Statement statement, ResultSet resultSet) {
        if (resultSet != null){
            try {
                resultSet.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (connection !=  null){
            try {
                connection.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (statement != null){
            try {
                statement.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
    }


    /**
     * <AUTHOR>
     * @date: 21/1/12 11:49
     * @Description: query
     * @param sql 执行的sql
     * @param clazz 查询的结果的类型
     * @param args sql的参数
     * @return: List<T>
     */
    public static  <T> List<T> query(String sql, Class<T> clazz, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<T> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
                T t = clazz.newInstance();
                for (int i = 0; i < columnCount; i++) {
                    // 7.1获取列值
                    Object object = resultSet.getObject(i + 1);
                    // 7.2获取列别名
                    String columnLabel = metaData.getColumnLabel(i + 1);
                    // 7.3获取属性并设置属性的值
                    Field field = clazz.getDeclaredField(columnLabel.toLowerCase());
                    field.setAccessible(true);
                    field.set(t, object);
                }
                list.add(t);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }

        return list;
    }

    /**
     * @auther: ADMIN
     * @date: 21/1/12 11:52
     * @Description: update 更新
     * @param sql 执行的sql
     * @param obj 可变参数
     * @return: int 改变的条数
     */
    public static int update(String sql, Object ...obj){
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        try {
            // 1.获取连接
            connection = getConnection();
            // 2.预编译sql，返回一个PrepareStatement实例
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < obj.length; i++) {
                preparedStatement.setObject(i+1, obj[i]);
            }
            // 4.执行
            return preparedStatement.executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 5.关闭资源
            closeConnection(connection, preparedStatement);
        }
        return 0;
    }
    
    
    public static List<MedCostView> getMedCostViewList(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        List<MedCostView> list = new ArrayList<>();
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	MedCostView record = new MedCostView();
            	record.setBranchId(resultSet.getString("BRANCH_ID"));
            	record.setBranchName(resultSet.getString("BRANCH_NAME"));
            	record.setOutDeptName(resultSet.getString("OUT_DEPT_NAME"));
            	record.setOutDeptId(resultSet.getString("OUT_DEPT_ID"));
            	record.setChargingSettleAmount(resultSet.getString("CHARGING_SETTLE_AMOUNT"));
            	record.setNoChargingSettleAmount(resultSet.getString("NO_CHARGING_SETTLE_AMOUNT"));
            	record.setDzSettleAmount(resultSet.getString("DZ_SETTLE_AMOUNT"));
            	record.setGzSettleAmount(resultSet.getString("GZ_SETTLE_AMOUNT"));
            	record.setBgSettleAmount(resultSet.getString("BG_SETTLE_AMOUNT"));
            	record.setDlSettleAmount(resultSet.getString("DL_SETTLE_AMOUNT"));
            	record.setFdlSettleAmount(resultSet.getString("FDL_SETTLE_AMOUNT"));
            	record.setSjChargingSettleAmount(resultSet.getString("SJ_CHARGING_SETTLE_AMOUNT"));
            	record.setSjNoChargingSettleAmount(resultSet.getString("SJ_NO_CHARGING_SETTLE_AMOUNT"));
            	record.setCostMonth(resultSet.getString("COST_MONTH"));
            	record.setHisDeptCode(resultSet.getString("HIS_DEPT_CODE"));
                list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取SPD库数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }

	public static String getMedCostTotalAmount(String sql, Object ...args) {
		Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        String b = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
                b = resultSet.getString("TOTALAMOUNT");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取SPD库数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return b;
	}
    
}

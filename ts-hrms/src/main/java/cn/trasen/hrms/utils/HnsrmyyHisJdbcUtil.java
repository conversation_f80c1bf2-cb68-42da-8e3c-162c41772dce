package cn.trasen.hrms.utils;


import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.hrms.med.cost.model.HnsrmyyHisCost;
import cn.trasen.hrms.med.cslt.model.CsltAppySyncHis;
import cn.trasen.hrms.med.newTech.model.NewTechPatientInfo;
import cn.trasen.hrms.med.patient.model.PatientBirth;
import cn.trasen.hrms.med.patient.model.PatientCriticalValue;
import cn.trasen.hrms.med.patient.model.PatientInfo;
import cn.trasen.hrms.med.patient.model.PatientOperation;
import cn.trasen.hrms.med.patient.model.PatientOrderrecord;
import cn.trasen.hrms.med.patient.model.PatientTransferDept;
import cn.trasen.hrms.med.qua.model.MedDoctorRole;
import cn.trasen.hrms.med.qua.model.QuaAuthCfg;
import cn.trasen.hrms.med.risk.model.RiskPatientOperation;
import cn.trasen.hrms.model.HrmsEmployee;
import lombok.extern.log4j.Log4j2;
 
/**
 * 湖南省人民医院影子库jdbc
 * <AUTHOR> 
 */
@Log4j2
public class HnsrmyyHisJdbcUtil {
	
	
	 /**
     * 获取一个连接
     * @return
     * @throws Exception
     */
    public static Connection getConnection() throws Exception {
    	
        String user = "ODSMZV10";
        String password = "Trasen123";
        String url = "*********************************************";
        String driverClass = "oracle.jdbc.driver.OracleDriver";
     
        
//        String user = "sysdba";
//        String password = "HUN_admin2024";
//        String url = "jdbc:dm://192.168.9.149:5237/ts_base_oa";
//        String driverClass = "dm.jdbc.driver.DmDriver";

        // 2. 加载驱动
        Class.forName(driverClass);

        Connection connection = DriverManager.getConnection(url, user, password);
        return connection;
    }

    /**
     * 关闭连接
     * @param connection
     * @param statement
     */
    public static void closeConnection(Connection connection, Statement statement) {
        if (connection !=  null){
            try {
                connection.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (statement != null){
            try {
                statement.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
    }

    /**
     * 关闭连接
     * @param connection
     * @param statement
     */
    public static void closeConnection(Connection connection, Statement statement, ResultSet resultSet) {
        if (resultSet != null){
            try {
                resultSet.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (connection !=  null){
            try {
                connection.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (statement != null){
            try {
                statement.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
    }


    /**
     * <AUTHOR>
     * @date: 21/1/12 11:49
     * @Description: query
     * @param sql 执行的sql
     * @param clazz 查询的结果的类型
     * @param args sql的参数
     * @return: List<T>
     */
    public static  <T> List<T> query(String sql, Class<T> clazz, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<T> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
                T t = clazz.newInstance();
                for (int i = 0; i < columnCount; i++) {
                    // 7.1获取列值
                    Object object = resultSet.getObject(i + 1);
                    // 7.2获取列别名
                    String columnLabel = metaData.getColumnLabel(i + 1);
                    // 7.3获取属性并设置属性的值
                    Field field = clazz.getDeclaredField(columnLabel.toLowerCase());
                    field.setAccessible(true);
                    field.set(t, object);
                }
                list.add(t);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }

        return list;
    }

    /**
     * @auther: ADMIN
     * @date: 21/1/12 11:52
     * @Description: update 更新
     * @param sql 执行的sql
     * @param obj 可变参数
     * @return: int 改变的条数
     */
    public static int update(String sql, Object ...obj){
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        try {
            // 1.获取连接
            connection = getConnection();
            // 2.预编译sql，返回一个PrepareStatement实例
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < obj.length; i++) {
                preparedStatement.setObject(i+1, obj[i]);
            }
            // 4.执行
            return preparedStatement.executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 5.关闭资源
            closeConnection(connection, preparedStatement);
        }
        return 0;
    }
    
    public static List<CsltAppySyncHis> queryCsltAppySyncHis(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<CsltAppySyncHis> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	CsltAppySyncHis record = new CsltAppySyncHis();
            	record.setCsltAppyId(resultSet.getString("CSLT_APPY_ID"));
            	record.setAppyNo(resultSet.getString("APPY_NO"));
            	record.setCsltType(resultSet.getString("CSLT_TYPE"));
            	record.setCsltMold(resultSet.getString("CSLT_MOLD"));
            	record.setCsltLv(resultSet.getString("CSLT_LV"));
            	record.setHospArea(resultSet.getString("HOSP_AREA"));
            	record.setCsltOrgId(resultSet.getString("CSLT_ORG_ID"));
            	record.setCsltOrgName(resultSet.getString("CSLT_ORG_NAME"));
            	record.setCsltTime(resultSet.getString("CSLT_TIME"));
            	record.setPatnId(resultSet.getString("PATN_ID"));
            	record.setPatnName(resultSet.getString("PATN_NAME"));
            	record.setPatnGend(resultSet.getString("PATN_GEND"));
            	record.setPatnAge(resultSet.getString("PATN_AGE"));
            	record.setPatnBedno(resultSet.getString("PATN_BEDNO"));
            	record.setPatnInpNo(resultSet.getString("PATN_INP_NO"));
            	record.setPatnOrgName(resultSet.getString("PATN_ORG_NAME"));
            	record.setPatnIcdName(resultSet.getString("PATN_ICD_NAME"));
            	record.setAppyTime(resultSet.getString("APPY_TIME"));
            	record.setAppyOrgName(resultSet.getString("APPY_ORG_NAME"));
            	record.setAppyEmpName(resultSet.getString("APPY_EMP_NAME"));
            	record.setAppyTel(resultSet.getString("APPY_TEL"));
            	record.setIllhis(resultSet.getString("ILLHIS"));
            	record.setPup(resultSet.getString("PUP"));
            	record.setActStatus(resultSet.getString("ACT_STATUS"));
            	record.setCsltStatus(resultSet.getString("CSLT_STATUS"));
            	record.setActTime(resultSet.getString("ACT_TIME"));
            	record.setActUser(resultSet.getString("ACT_USER"));
            	record.setActUserName(resultSet.getString("ACT_USER_NAME"));
            	record.setActEmployeeId(resultSet.getString("ACT_EMPLOYEE_ID"));
            	record.setActEmployeeName(resultSet.getString("ACT_EMPLOYEE_NAME"));
            	record.setActEmployeeNo(resultSet.getString("ACT_EMPLOYEE_NO"));
            	record.setActJobtitle(resultSet.getString("ACT_JOBTITLE"));
            	record.setActTel(resultSet.getString("ACT_TEL"));
            	record.setActStartTime(resultSet.getString("ACT_START_TIME"));
            	record.setActEndTime(resultSet.getString("ACT_END_TIME"));
            	//record.setNotcFlag(resultSet.getString("NOTC_FLAG"));
            	record.setFnsTime(resultSet.getString("FNS_TIME"));
            	record.setFnsEmployeeId(resultSet.getString("FNS_EMPLOYEE_ID"));
            	record.setFnsEmployeeName(resultSet.getString("FNS_EMPLOYEE_NAME"));
            	record.setFnsEmployeeNo(resultSet.getString("FNS_EMPLOYEE_NO"));
            	record.setFnsJobtitle(resultSet.getString("FNS_JOBTITLE"));
            	record.setFnsTel(resultSet.getString("FNS_TEL"));
            	record.setAppyOrgDscr(resultSet.getString("APPY_ORG_DSCR"));
            	record.setCsltOrgDscr(resultSet.getString("CSLT_ORG_DSCR"));
            	record.setIsDeleted(resultSet.getString("IS_DELETED"));
            	record.setAppyEmpId(resultSet.getString("APPLY_EMP_ID"));
            	record.setAppyOrgId(resultSet.getString("APPLY_ORG_ID"));
            	record.setAppyHospArea(resultSet.getString("APPY_HOSP_AREA"));
            	
            	record.setHzmdScore(resultSet.getString("HZMD_SCORE"));
            	record.setHzzlScore(resultSet.getString("HZZL_SCORE"));
            	record.setHzbyxScore(resultSet.getString("HZBYX_SCORE"));
            	record.setHzmddfScore(resultSet.getString("HZMDDF_SCORE"));
            	record.setHzyjScore(resultSet.getString("HZYJ_SCORE"));
            	record.setConDocType(resultSet.getString("CON_DOC_TYPE"));
                list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取影子库数据异常：" + e.getMessage());
            System.out.println("获取影子库数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }
    
    public static List<MedDoctorRole> queryDoctorRole(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        List<MedDoctorRole> list = new ArrayList<>();
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	MedDoctorRole record = new MedDoctorRole();
            	record.setId(resultSet.getString("ID"));
            	record.setEmployeeId(resultSet.getString("EMPLOYEEID"));
            	record.setEmployeeNo(resultSet.getString("EMPLOYEEID"));
            	record.setPtcfq(resultSet.getString("PTCFQ")==null?"0":resultSet.getString("PTCFQ"));
            	record.setKjywcfq(resultSet.getString("KJYWCFQ")==null?"0":resultSet.getString("KJYWCFQ"));
            	record.setZyypq(resultSet.getString("ZYYPQ")==null?"0":resultSet.getString("ZYYPQ"));
            	record.setTsjkjywhzq(resultSet.getString("TSJKJYWHZQ")==null?"0":resultSet.getString("TSJKJYWHZQ"));
            	record.setMzypcfq(resultSet.getString("MZYPCFQ")==null?"0":resultSet.getString("MZYPCFQ"));
            	record.setKzlywcfq(resultSet.getString("KZLYWCFQ")==null?"0":resultSet.getString("KZLYWCFQ"));
            	record.setJsypcfq(resultSet.getString("JSYPCFQ")==null?"0":resultSet.getString("JSYPCFQ"));
            	record.setDxypcfq(resultSet.getString("DXYPCFQ")==null?"0":resultSet.getString("DXYPCFQ"));
            	record.setFsxypcfq(resultSet.getString("FSXYPCFQ")==null?"0":resultSet.getString("DXYPCFQ"));
            	record.setZzrsypcfq(resultSet.getString("ZZRSYPCFQ")==null?"0":resultSet.getString("ZZRSYPCFQ"));
            	record.setXdcfq(resultSet.getString("XDCFQ")==null?"0":resultSet.getString("XDCFQ"));
            	record.setSsdjqx(resultSet.getString("SSDJQX")==null?"0":resultSet.getString("SSDJQX"));
            	record.setGwypdjqx(resultSet.getString("GWYPDJQX")==null?"0":resultSet.getString("GWYPDJQX"));
            	record.setYjsyqx(resultSet.getString("YJSYQX")==null?"0":resultSet.getString("YJSYQX"));
            	record.setSsoOrgCode("hnsrmyy");
            	
                list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取影子库数据异常：" + e.getMessage());
            System.out.println("获取影子库数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }
    
    public static List<HrmsEmployee> syncHisEmployee(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        List<HrmsEmployee> list = new ArrayList<>();
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	HrmsEmployee record = new HrmsEmployee();
            	record.setEmployeeId(resultSet.getString("EMPLOYEEID"));
            	record.setEmployeeName(resultSet.getString("EMPLOYEENAME"));
            	record.setOrgId(resultSet.getString("ORGID"));
            	record.setHospCode(resultSet.getString("HOSPCODE"));
            	record.setIdentityNumber(resultSet.getString("IDENTITYNUMBER"));
            	record.setGender(resultSet.getString("GENDER"));
            	record.setPhoneNumber(resultSet.getString("PHONENUMBER"));
            	record.setBirthday(resultSet.getDate("BIRTHDAY"));
            	record.setWorkStartDate(resultSet.getDate("WORKSTARTDATE"));
            	record.setEntryDate(resultSet.getDate("ENTRYDATE"));
            	record.setOrgAttributes(resultSet.getString("ORGATTRIBUTES"));
            	record.setHisEmployeeNo(resultSet.getString("HISEMPLOYEENO"));
            	record.setHispwd(resultSet.getString("HISPWD"));
            	record.setTechnical(resultSet.getString("TECHNICAL"));
                list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取影子库数据异常：" + e.getMessage());
            System.out.println("获取影子库数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }

    public static List<PatientInfo> queryPatientInfoSyncHis(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<PatientInfo> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	PatientInfo record = new PatientInfo();
            	record.setId(resultSet.getString("ID"));
            	record.setPatnNo(resultSet.getString("PATN_NO"));
            	record.setIdCard(resultSet.getString("ID_CARD"));
            	record.setName(resultSet.getString("NAME"));
            	record.setSex(resultSet.getString("SEX"));
            	record.setHospArea(resultSet.getString("HOSP_AREA"));
            	record.setBirthday(resultSet.getTimestamp("BIRTHDAY"));
            	record.setPhoneNumber(resultSet.getString("PHONE_NUMBER"));
            	record.setHomeStreet(resultSet.getString("HOME_STREET"));
            	record.setInDate(resultSet.getTimestamp("IN_DATE"));
            	record.setOutDate(resultSet.getTimestamp("OUT_DATE"));
            	record.setDoctor(resultSet.getString("DOCTOR"));
            	record.setDoctorName(resultSet.getString("DOCTOR_NAME"));
            	record.setInDept(resultSet.getString("IN_DEPT"));
            	record.setInDeptName(resultSet.getString("IN_DEPT_NAME"));
            	record.setDeptId(resultSet.getString("DEPT_ID"));
            	record.setDeptName(resultSet.getString("DEPT_NAME"));
            	record.setHospArea(resultSet.getString("HOSP_AREA"));
            	record.setInDiagnosis(resultSet.getString("IN_DIAGNOSIS"));
            	record.setOutDiagnosis(resultSet.getString("OUT_DIAGNOSIS"));
            	record.setDiagnoseDate(resultSet.getTimestamp("DIAGNOSE_DATE"));
            	record.setFlag(resultSet.getString("FLAG"));
            	record.setOutMode(resultSet.getString("OUT_MODE"));
            	record.setBookDate(resultSet.getTimestamp("BOOK_DATE"));
            	record.setCancelBit(resultSet.getString("CANCEL_BIT"));
            	record.setBedNo(resultSet.getString("BED_NO"));
            	
            	String ORDER_BW = resultSet.getString("ORDER_BW");
            	String ORDER_BZ = resultSet.getString("ORDER_BZ");
            	
            	if(StrUtil.isNotBlank(ORDER_BW) && !"0".equals(ORDER_BW)) {
            		record.setIsBw("1");
            	}else {
            		record.setIsBw("0");
            	}
            	if(StrUtil.isNotBlank(ORDER_BZ) && !"0".equals(ORDER_BZ)) {
            		record.setIsBz("1");
            	}else {
            		record.setIsBz("0");
            	}
            	
                list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取影子库病人信息数据异常：" + e.getMessage());
            System.out.println("获取影子库病人信息数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }
    
    public static List<PatientOrderrecord> queryPatientOrderrecordSyncHis(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<PatientOrderrecord> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	PatientOrderrecord record = new PatientOrderrecord();
            	record.setId(resultSet.getString("ID"));
            	record.setPatnId(resultSet.getString("PATN_ID"));
            	record.setPatnNo(resultSet.getString("PATN_NO"));
            	record.setBabyId(resultSet.getString("BABY_ID"));
            	record.setDeptBr(resultSet.getString("DEPT_BR"));
            	record.setDeptId(resultSet.getString("DEPT_ID"));
            	record.setOrderDoc(resultSet.getString("ORDER_DOC"));
            	record.setOrderBdate(resultSet.getTimestamp("ORDER_BDATE"));
            	record.setOrderEdate(resultSet.getTimestamp("ORDER_EDATE"));
            	record.setHoitemId(resultSet.getString("HOITEM_ID"));
            	record.setOrderContext(resultSet.getString("ORDER_CONTEXT"));
            	record.setStatusFlag(resultSet.getString("STATUS_FLAG"));
            	record.setMngtype(resultSet.getString("MNGTYPE"));
            	record.setBookDate(resultSet.getTimestamp("BOOK_DATE"));
            	record.setOrderUpdateTime(resultSet.getTimestamp("ORDER_UPDATE_TIME"));
            	record.setDeleteBit(resultSet.getString("DELETE_BIT"));
                list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取影子库病人信息数据异常：" + e.getMessage());
            System.out.println("获取影子库病人信息数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }
    
    
    public static List<PatientOperation> queryPatientOperationSyncHis(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<PatientOperation> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	PatientOperation record = new PatientOperation();
            	record.setId(resultSet.getString("ID"));
            	record.setPatnId(resultSet.getString("PATN_ID"));
            	record.setPatnNo(resultSet.getString("PATN_NO"));
            	record.setOperationDate(resultSet.getTimestamp("OPERATION_DATE"));
            	record.setOperationName(resultSet.getString("OPERATION_NAME"));
            	record.setOperationAfterDiagnosis(resultSet.getString("OPERATION_AFTER_DIAGNOSIS"));
            	record.setZdys(resultSet.getString("ZDYS"));
            	record.setMzys(resultSet.getString("MZYS"));
            	record.setWcbj(resultSet.getString("WCBJ"));
            	record.setWcsj(resultSet.getTimestamp("WCSJ"));
            	record.setQklx(resultSet.getString("QKLX"));
            	record.setYxssdj(resultSet.getString("YXSSDJ"));
            	record.setBdelete(resultSet.getString("BDELETE"));
            	record.setApbj(resultSet.getString("APBJ"));    
            	record.setYsssrq(resultSet.getTimestamp("OPERATION_DATE"));
            	
                list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取影子库病人手术信息数据异常：" + e.getMessage());
            System.out.println("获取影子库病人手术信息数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }
    
    public static List<PatientCriticalValue> queryPatientCriticalValueSyncHis(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<PatientCriticalValue> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	PatientCriticalValue record = new PatientCriticalValue();
            	record.setId(resultSet.getString("ID"));
            	record.setPatnId(resultSet.getString("PATN_ID"));
            	record.setPatnNo(resultSet.getString("PATN_NO"));
            	record.setReportDate(resultSet.getTimestamp("REPORT_DATE"));
            	record.setCriticalValue(resultSet.getString("CRITICAL_VALUE"));
            	record.setOrderItemId(resultSet.getString("ORDER_ITEM_ID"));
            	record.setOrderItemName(resultSet.getString("ORDER_ITEM_NAME"));
            	record.setRequestDeptId(resultSet.getString("REQUEST_DEPT_ID"));
            	record.setRequestDeptName(resultSet.getString("REQUEST_DEPT_NAME"));
            	record.setSignDate(resultSet.getTimestamp("SIGN_DATE"));
            	record.setProcessDate(resultSet.getTimestamp("PROCESS_DATE"));
            	record.setStatus(resultSet.getString("STATUS"));
                list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取影子库病人危急值信息数据异常：" + e.getMessage());
            System.out.println("获取影子库病人危急值信息数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }
    
    
    public static List<PatientBirth> queryPatientBirthSyncHis(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<PatientBirth> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	PatientBirth record = new PatientBirth();
            	record.setId(resultSet.getString("ID"));
            	record.setPatnId(resultSet.getString("PATN_ID"));
            	record.setPatnNo(resultSet.getString("PATN_NO"));
            	record.setBirthDate(resultSet.getTimestamp("BIRTH_DATE"));
            	record.setBookDate(resultSet.getTimestamp("BOOK_DATE"));
                list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取影子库病人分娩信息数据异常：" + e.getMessage());
            System.out.println("获取影子库病人分娩信息数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }
    
    public static List<PatientTransferDept> queryPatientTransferDeptSyncHis(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<PatientTransferDept> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	PatientTransferDept record = new PatientTransferDept();
            	record.setId(resultSet.getString("ID"));
            	record.setPatnId(resultSet.getString("PATN_ID"));
            	record.setPatnNo(resultSet.getString("PATN_NO"));
            	record.setTransferDate(resultSet.getTimestamp("TRANSFER_DATE"));
            	record.setOutDeptId(resultSet.getString("OUT_DEPT_ID"));
            	record.setOutDeptName(resultSet.getString("OUT_DEPT_NAME"));
            	record.setInDeptId(resultSet.getString("IN_DEPT_ID"));
            	record.setInDeptName(resultSet.getString("IN_DEPT_NAME"));
            	record.setCancelBit(resultSet.getString("CANCEL_BIT"));
            	record.setFinishBit(resultSet.getString("FINISH_BIT"));
                list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取影子库病人转科信息数据异常：" + e.getMessage());
            System.out.println("获取影子库病人转科信息数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }
    
    public static List<RiskPatientOperation> queryFourOperationSyncHis(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<RiskPatientOperation> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	RiskPatientOperation record = new RiskPatientOperation();
            	record.setId(resultSet.getString("ID"));
            	record.setSno(resultSet.getString("SNO"));
            	record.setPatnId(resultSet.getString("PATN_ID"));
            	record.setPatnNo(resultSet.getString("PATN_NO"));
            	record.setName(resultSet.getString("NAME"));
            	record.setBedNo(resultSet.getString("BED_NO"));
            	record.setBirthday(resultSet.getTimestamp("BIRTHDAY"));
            	record.setSex(resultSet.getString("SEX"));
            	record.setAppDocId(resultSet.getString("APP_DOC_ID"));
            	record.setAppDocName(resultSet.getString("APP_DOC_NAME"));
            	record.setDeptId(resultSet.getString("DEPT_ID"));
            	record.setDeptName(resultSet.getString("DEPT_NAME"));
            	record.setHospArea(resultSet.getString("HOSP_AREA"));
            	record.setOperationDate(resultSet.getTimestamp("OPERATION_DATE"));
            	record.setOperationName(resultSet.getString("OPERATION_NAME"));
            	record.setOperationCode(resultSet.getString("OPERATION_CODE"));
            	record.setInternalLevel(resultSet.getString("INTERNAL_LEVEL"));
            	record.setNationalLevel(resultSet.getString("NATIONAL_LEVEL"));
            	record.setZdysCode(resultSet.getString("ZDYS_CODE"));
            	record.setZdysName(resultSet.getString("ZDYS_NAME"));
            	record.setZdysTitle(resultSet.getString("ZDYS_TITLE"));
            	record.setMzys(resultSet.getString("MZYS"));
            	record.setBdelete(resultSet.getString("BDELETE"));
            	record.setOperationAfterDiagnosis(resultSet.getString("OPERATION_AFTER_DIAGNOSIS"));
            	record.setJzss(resultSet.getString("JZSS"));
                list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取影子库四级手术数据异常：" + e.getMessage());
            System.out.println("获取影子库四级手术数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }
    
    public static List<RiskPatientOperation> queryFourOperationSyncHis2(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<RiskPatientOperation> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	RiskPatientOperation record = new RiskPatientOperation();
            	record.setId(resultSet.getString("ID"));
            	record.setBdelete(resultSet.getString("BDELETE"));
                list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取影子库四级手术数据异常：" + e.getMessage());
            System.out.println("获取影子库四级手术数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }
    
    /**
     * 获取影子库新技术开展病例数据
     * 
     * @param sql
     * @param args
     * @return
     */
    public static List<NewTechPatientInfo> queryNewTechPatientInfo(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<NewTechPatientInfo> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	NewTechPatientInfo record = new NewTechPatientInfo();
            	record.setTechCode(resultSet.getString("已行手术编码"));
            	record.setOperationDate(resultSet.getString("已行手术日期"));
            	record.setInPatientNo(resultSet.getString("住院号"));
            	record.setPatientName(resultSet.getString("患者姓名"));
            	record.setSex(resultSet.getString("性别"));
            	record.setMainDiagnosis(resultSet.getString("入院诊断"));
                list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取影子库新技术开展病例数据异常：" + e.getMessage());
            System.out.println("获取影子库新技术开展病例数据异常：" + e.getMessage());
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }
    
    public static List<QuaAuthCfg> queryHnsrmyyQuaCfgLvHosp(String sql, Object ...args) {
    	 Connection connection = null;
         PreparedStatement preparedStatement = null;
         ResultSet resultSet = null;
         ArrayList<QuaAuthCfg> list = null;
         try {
             // 1.获取一个连接
             connection = getConnection();

             // 2.预编译一个sql语句，返回一个PrepareStatement对象
             preparedStatement = connection.prepareStatement(sql);
             // 3.填充占位符
             for (int i = 0; i < args.length; i++) {
                 preparedStatement.setObject(i+1, args[i]);
             }
             // 4。执行sql,得到结果集
             resultSet = preparedStatement.executeQuery();
             // 5.获取元数据
             ResultSetMetaData metaData = resultSet.getMetaData();
             int columnCount = metaData.getColumnCount();
             list = new ArrayList();
             // 7.遍历得到每一行数据
             while (resultSet.next()){
             	 // 6.获取一个类的反射实例
            	QuaAuthCfg record = new QuaAuthCfg();
            	record.setItemCode(resultSet.getString("SSCODE"));
            	record.setAuthLvHosp(resultSet.getString("BY2"));
                list.add(record);
             }
         } catch (Exception e) {
             e.printStackTrace();
             closeConnection(connection, preparedStatement, resultSet);
         } finally {
             closeConnection(connection, preparedStatement, resultSet);
         }
         return list;
	}
    
    public static List<String> queryHnsrmyyHisWzje(String sql, Object ...args) {
		Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        List<String> wzje = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            wzje = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	wzje.add(resultSet.getString("WZJE"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return wzje;
	}
    
    public static List<HnsrmyyHisCost> queryHnsrmyyHisCost(String sql, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<HnsrmyyHisCost> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	HnsrmyyHisCost record = new HnsrmyyHisCost();
            	record.setDeptId(resultSet.getString("DEPT_CLASSID"));
            	record.setDeptName(resultSet.getString("DEPT_CLASSNAME"));
            	record.setYpf(resultSet.getString("YPF"));
            	record.setZje(resultSet.getString("ZJE"));
            	list.add(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }

	public static String queryHnsrmyyHisZxje(String sql, Object ...args) {
		Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        String zxje = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
            	zxje = resultSet.getString("ZXJE");
            }
        } catch (Exception e) {
            e.printStackTrace();
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return zxje;
	}

	public static List<String> queryDiagnosi(String sql,Object ...args) {
		 Connection connection = null;
         PreparedStatement preparedStatement = null;
         ResultSet resultSet = null;
         ArrayList<String> list = null;
         try {
             // 1.获取一个连接
             connection = getConnection();

             // 2.预编译一个sql语句，返回一个PrepareStatement对象
             preparedStatement = connection.prepareStatement(sql);
             // 3.填充占位符
             for (int i = 0; i < args.length; i++) {
                 preparedStatement.setObject(i+1, args[i]);
             }
             // 4。执行sql,得到结果集
             resultSet = preparedStatement.executeQuery();
             // 5.获取元数据
             ResultSetMetaData metaData = resultSet.getMetaData();
             int columnCount = metaData.getColumnCount();
             list = new ArrayList();
             // 7.遍历得到每一行数据
             while (resultSet.next()){
             	 // 6.获取一个类的反射实例
                list.add(resultSet.getString("NAME1"));
             }
         } catch (Exception e) {
             e.printStackTrace();
             closeConnection(connection, preparedStatement, resultSet);
         } finally {
             closeConnection(connection, preparedStatement, resultSet);
         }
         return list;
	}

	public static List<String> queryHisdeptCode(String sql,Object ...args) {
		Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<String> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
               list.add(resultSet.getString("DEPT_ID"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
	}
	
	public static List<Map<String, String>> queryHisSecondDept(String sql,Object ...args) {
		Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        List<Map<String,String>> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
               Map<String,String> map = new HashMap<>();
               map.put("classId", resultSet.getString("CLASS_ID"));
               map.put("deptName", resultSet.getString("DEPT_NAME"));
               list.add(map);
            }
        } catch (Exception e) {
            e.printStackTrace();
            closeConnection(connection, preparedStatement, resultSet);
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
	}

}

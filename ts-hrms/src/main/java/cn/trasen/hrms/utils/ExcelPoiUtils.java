package cn.trasen.hrms.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

/**
 * @Title: ExcelPoiUtils.java
 * @Package cn.trasen.hrms.utils
 * @Description: Excel POI导出工具类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司
 * @date 2020年5月19日 下午23:16:07
 * @version V1.0
 */
public class ExcelPoiUtils {

	/**
	 * @Title: readSimpleExcel
	 * @Description: 读取简单excel表格(支持在线流读取和文件路径读取两种方式)
	 * @param in
	 *            输入流
	 * @param filePath
	 *            文件路径
	 * @throws Exception
	 * @Return List<List<String>>
	 * <AUTHOR>
	 * @date 2020年5月19日 下午11:19:36
	 */
	public static List<List<String>> readSimpleExcel(InputStream in, String filePath) throws Exception {
		List<List<String>> results = new ArrayList<List<String>>();

		if (in == null) {
			File excelFile = new File(filePath);
			in = new FileInputStream(excelFile);
		}
		Workbook wb = WorkbookFactory.create(in); // 创建工作簿
		Sheet sheet = wb.getSheetAt(0); // 获得表格
		int rowCnt = sheet.getPhysicalNumberOfRows(); // 总行数
		int totalCols = 0;
		for (int r = 0; r < rowCnt; r++) {
			Row row = sheet.getRow(r); // 遍历每一行
			if (isRowEmpty(row)) {
				continue;
			}
			if (r == 0) { // 根据第一行的表头获取总列数
				totalCols = row.getPhysicalNumberOfCells();
			}
			List<String> list = new ArrayList<String>();
			for (int c = 0; c < totalCols; c++) {
				list.add(getCellVal(row.getCell(c)));
			}
			results.add(list);
		}
		return results;
	}

	/**
	 * @Title: isRowEmpty
	 * @Description: 判断行数据是否为空
	 * @param row
	 * @Return boolean
	 * <AUTHOR>
	 * @date 2020年5月19日 下午11:30:08
	 */
	public static boolean isRowEmpty(Row row) {
		for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
			Cell cell = row.getCell(i);
			if (cell != null && cell.getCellType() != Cell.CELL_TYPE_BLANK) {
				return false;
			}
		}
		return true;
	}

	public static String getCellVal(Cell cell) {
		String cellVal = "";
		if (cell != null) {
			switch (cell.getCellTypeEnum()) {
			case NUMERIC: // 数值/日期
				if (DateUtil.isCellDateFormatted(cell)) { // 日期型
					DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
					cellVal = df.format(cell.getDateCellValue());
				} else { // 数值型
					if (String.valueOf(cell.getNumericCellValue()).contains(".")) {
						DecimalFormat df = new DecimalFormat("#");
						cellVal = df.format(cell.getNumericCellValue());
					} else {
						cellVal = String.valueOf(cell.getNumericCellValue());
					}
				}
				break;
			case STRING: // 字符串
				cellVal = cell.getStringCellValue();
				break;
			case FORMULA: // 公式
				cellVal = cell.getCellFormula();
				break;
			case BLANK: // 空白
				cellVal = "";
				break;
			case BOOLEAN: // 布尔值
				cellVal = String.valueOf(cell.getBooleanCellValue());
				break;
			case ERROR: // 错误
				cellVal = "error";
			default:
				cellVal = "defaultVal";
				break;
			}
		}
		return cellVal.trim();
	}

}

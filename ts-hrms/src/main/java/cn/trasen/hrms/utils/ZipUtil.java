package cn.trasen.hrms.utils;

import cn.hutool.core.io.FileUtil;
import org.apache.commons.io.output.ByteArrayOutputStream;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @date 2021/10/23 16:37
 */
public class ZipUtil {

    /**
     * 把文件集合打成zip压缩包
     *
     * @param srcFiles
     * @return byte[]
     * <AUTHOR>
     * @date 2021/10/23 16:49
     */
    public static byte[] toZip(List<File> srcFiles) {
        ZipOutputStream zos = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            byteArrayOutputStream = new ByteArrayOutputStream();
            zos = new ZipOutputStream(byteArrayOutputStream);
            
            for (File srcFile : srcFiles) {
                byte[] buf = new byte[org.springframework.util.StreamUtils.BUFFER_SIZE];
                zos.putNextEntry(new ZipEntry(srcFile.getName()));
                int len;
                FileInputStream in = new FileInputStream(srcFile);
                try {
                    while ((len = in.read(buf)) != -1) {
                        zos.write(buf, 0, len);
                    }
                    zos.closeEntry();
                } finally {
                    in.close();
                }
            }
            zos.finish();
            byte[] zipByte = byteArrayOutputStream.toByteArray();
           // FileUtil.writeBytes(zipByte,"d:/222.zip");
            return zipByte;
        } catch (Exception e) {
            throw new RuntimeException("zipFile error from ZipUtils", e);
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (byteArrayOutputStream != null) {
                try {
                    byteArrayOutputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
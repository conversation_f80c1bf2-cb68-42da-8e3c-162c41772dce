package cn.trasen.hrms.utils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.jeecgframework.poi.excel.export.ExcelBatchExportServer;
import cn.trasen.hrms.utils.ExcelExportServer;

import org.jeecgframework.poi.excel.export.template.ExcelExportOfTemplateUtil;

/**
 * excel 导出工具类
 * 
 * <AUTHOR>
 * @version 1.0 2013-10-17
 */

public class ExcelExportUtils {
	private ExcelExportUtils() {
	}

	/**
	 * @param entity    表格标题属性
	 * @param pojoClass Excel对象Class
	 * @param dataSet   Excel对象数据List
	 */
	public static Workbook exportBigExcel(ExportParams entity, Class<?> pojoClass, Collection<?> dataSet) {
		ExcelBatchExportServer batachServer = ExcelBatchExportServer.getExcelBatchExportServer(entity, pojoClass);
		return batachServer.appendData(dataSet);
	}

	public static void closeExportBigExcel() {
		ExcelBatchExportServer batachServer = ExcelBatchExportServer.getExcelBatchExportServer(null, null);
		batachServer.closeExportBigExcel();
	}

	/**
	 * @param entity    表格标题属性
	 * @param pojoClass Excel对象Class
	 * @param dataSet   Excel对象数据List
	 */
	public static Workbook exportExcel(ExportParams entity, Class<?> pojoClass, Collection<?> dataSet) {
		Workbook workbook;
		if (ExcelType.HSSF.equals(entity.getType())) {
			workbook = new HSSFWorkbook();
		} else if (dataSet.size() < 10000) {
			workbook = new XSSFWorkbook();
		} else {
			workbook = new SXSSFWorkbook();
		}
		new ExcelExportServer().createSheet(workbook, entity, pojoClass, dataSet);
		return workbook;
	}

	/**
	 * 根据Map创建对应的Excel
	 * 
	 * @param entity     表格标题属性
	 * @param entityList Map对象列表
	 * @param dataSet    Excel对象数据List
	 */
	public static Workbook exportExcel(ExportParams entity, List<ExcelExportEntity> entityList,
			Collection<? extends Map<?, ?>> dataSet) {
		Workbook workbook;
		if (ExcelType.HSSF.equals(entity.getType())) {
			workbook = new HSSFWorkbook();
		} else if (dataSet.size() < 10000) {
			workbook = new XSSFWorkbook();
		} else {
			workbook = new SXSSFWorkbook();
		}
		new ExcelExportServer().createSheetForMap(workbook, entity, entityList, dataSet);
		return workbook;
	}

	/**
	 * 一个excel 创建多个sheet
	 * 
	 * @param list 多个Map key title 对应表格Title key entity 对应表格对应实体 key data Collection
	 *             数据
	 * @return
	 */
	public static Workbook exportExcel(List<Map<String, Object>> list, ExcelType type) {
		Workbook workbook;
		if (ExcelType.HSSF.equals(type)) {
			workbook = new HSSFWorkbook();
		} else {
			workbook = new XSSFWorkbook();
		}
		for (Map<String, Object> map : list) {
			ExcelExportServer server = new ExcelExportServer();
			server.createSheet(workbook, (ExportParams) map.get("title"), (Class<?>) map.get("entity"),
					(Collection<?>) map.get("data"));
		}
		return workbook;
	}

	/**
	 * 导出文件通过模板解析,不推荐这个了,推荐全部通过模板来执行处理
	 * 
	 * @param params    导出参数类
	 * @param pojoClass 对应实体
	 * @param dataSet   实体集合
	 * @param map       模板集合
	 * @return
	 */
	@Deprecated
	public static Workbook exportExcel(TemplateExportParams params, Class<?> pojoClass, Collection<?> dataSet,
			Map<String, Object> map) {
		return new ExcelExportOfTemplateUtil().createExcleByTemplate(params, pojoClass, dataSet, map);
	}

	/**
	 * 导出文件通过模板解析只有模板,没有集合
	 * 
	 * @param params 导出参数类
	 * @param map    模板集合
	 * @return
	 */
	public static Workbook exportExcel(TemplateExportParams params, Map<String, Object> map) {
		return new ExcelExportOfTemplateUtil().createExcleByTemplate(params, null, null, map);
	}

	/**
	 * 导出文件通过模板解析只有模板,没有集合 每个sheet对应一个map,导出到处,key是sheet的NUM
	 * 
	 * @param params 导出参数类
	 * @param map    模板集合
	 * @return
	 */
	public static Workbook exportExcel(Map<Integer, Map<String, Object>> map, TemplateExportParams params) {
		return new ExcelExportOfTemplateUtil().createExcleByTemplate(params, map);
	}

	public static void modifyCellStype(Workbook workbook) {
		Sheet sheet = workbook.getSheetAt(0);
		// 创建数字格式样式
		CellStyle numberCellStyle = workbook.createCellStyle();
		DataFormat dataFormat = workbook.createDataFormat();
		numberCellStyle.setDataFormat(dataFormat.getFormat("#,##0.00"));

		for (Row row : sheet) {
			for (Cell cell : row) {
				if (cell.getCellType() == Cell.CELL_TYPE_STRING) {
					try {
						double numbericValue = Double.parseDouble(cell.getStringCellValue());
						cell.setCellValue(numbericValue);
						cell.setCellStyle(numberCellStyle);
					} catch (NumberFormatException e) {
						cell.setCellStyle(workbook.createCellStyle());
					}
				}
			}
		}
	}

	public static void doubleStypeBug(Workbook workbook) {
		Sheet sheet = workbook.getSheetAt(0);
		for (Row row : sheet) {
			for (Cell cell : row) {
				if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
					if (cell.getNumericCellValue()== -1) { //因在导出时，默认将null导成了-1，需要进行修正
						cell.setCellValue("");
					}
				}
			}
		}
	}
	
	// 新增：为合并区域设置边框的工具方法
	public static void setBorderForMergedRegion(Sheet sheet, CellRangeAddress region, CellStyle style) {
	    int firstRow = region.getFirstRow();
	    int lastRow = region.getLastRow();
	    int firstCol = region.getFirstColumn();
	    int lastCol = region.getLastColumn();
	    
	    // 遍历合并区域的所有单元格
	    for (int i = firstRow; i <= lastRow; i++) {
	        Row row = sheet.getRow(i);
	        if (row == null) row = sheet.createRow(i);
	        
	        for (int j = firstCol; j <= lastCol; j++) {
	            Cell cell = row.getCell(j);
	            if (cell == null) cell = row.createCell(j);
	            
	            // 应用基本样式
	            cell.setCellStyle(style);
	        }
	    }
	}
	
	public static void setColList(List<ExcelExportEntity> colList, List<String> fieldList) {
		
		ExcelExportEntity colEntity = null;

		if (CollectionUtils.isNotEmpty(fieldList)) {

			for (String field : fieldList) {

				colEntity = new ExcelExportEntity(field, field);
				colEntity.setWidth(12);
				colEntity.setHeight(12);

				colList.add(colEntity);
				
			}
		}
	}
}

package cn.trasen.hrms.utils;

import java.math.BigDecimal;
import java.math.BigInteger;

import org.apache.commons.lang.ObjectUtils;

/**   
 * @Title: MathUtils.java 
 * @Package cn.trasen.hrms.utils 
 * @Description: 数字处理工具类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月15日 上午11:22:29 
 * @version V1.0   
 */
public class MathUtils {

	/**
	 * @Title: getBigDecimal
	 * @Description: Object转BigDecimal
	 * @param value
	 * @return
	 * @Return BigDecimal
	 * <AUTHOR>
	 * @date 2020年5月15日 上午11:23:40
	 */
	public static BigDecimal getBigDecimal(Object value) {
		BigDecimal ret = null;
		if (value != null) {
			if (value instanceof BigDecimal) {
				ret = (BigDecimal) value;
			} else if (value instanceof String) {
				ret = new BigDecimal((String) value);
			} else if (value instanceof BigInteger) {
				ret = new BigDecimal((BigInteger) value);
			} else if (value instanceof Number) {
				ret = new BigDecimal(Double.toString(((Number) value).doubleValue()));
			} else {
				throw new ClassCastException("Not possible to coerce [" + value + "] from class " + value.getClass() + " into a BigDecimal.");
			}
		}
		return ret == null ? new BigDecimal(0) : ret;
	}

	/**
	 * @Title: getBigDecimalByObjectUtils
	 * @Description: 通过ObjectUtils转换Object为BigDecimal
	 * @param value
	 * @Return BigDecimal
	 * <AUTHOR>
	 * @date 2020年6月1日 下午5:28:27
	 */
	public static BigDecimal getBigDecimalByObjectUtils(Object value) {
		BigDecimal ret = null;
		if (value instanceof String || value instanceof Long || value instanceof Integer || value instanceof Double) {
			ret = new BigDecimal(ObjectUtils.toString(value));
		}
		return ret == null ? new BigDecimal(0) : ret;
	}
	
}

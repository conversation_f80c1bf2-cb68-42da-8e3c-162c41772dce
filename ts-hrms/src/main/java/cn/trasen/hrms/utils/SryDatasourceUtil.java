package cn.trasen.hrms.utils;


import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
 
/**
 * 
 -- =============================================
 --文件说明：湖南省人民医院HIS影子库连接工具类
 --类名称: SryDatasourceUtil
 --创建时间：2024年11月20日 
 --作者：GW
 -- =============================================
 */
public class SryDatasourceUtil {
	
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 创建连接
	  -- 作者: GW
	  -- 创建时间: 2024年11月20日
	  -- @return
	  -- @throws Exception
	  -- =============================================
	 */
    public static Connection getConnection() throws Exception {

        String user = "ODSMZV10";
        String password = "Trasen123";
        String url = "*********************************************************************************************************************************************************************************************************************************";
        String driverClass = "oracle.jdbc.OracleDriver";

        // 2. 加载驱动
        Class.forName(driverClass);

        Connection connection = DriverManager.getConnection(url, user, password);
        return connection;
    }

    /**
     * 
      -- =============================================
      -- 功能描述: 关闭连接
      -- 作者: GW
      -- 创建时间: 2024年11月20日
      -- @param connection
      -- @param statement
      -- =============================================
     */
    public static void closeConnection(Connection connection, Statement statement) {
        if (connection !=  null){
            try {
                connection.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (statement != null){
            try {
                statement.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
    }

    /**
     * 
      -- =============================================
      -- 功能描述: 关闭连接
      -- 作者: GW
      -- 创建时间: 2024年11月20日
      -- @param connection
      -- @param statement
      -- @param resultSet
      -- =============================================
     */
    public static void closeConnection(Connection connection, Statement statement, ResultSet resultSet) {
        if (resultSet != null){
            try {
                resultSet.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (connection !=  null){
            try {
                connection.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (statement != null){
            try {
                statement.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
    }


    /**
     * 
      -- =============================================
      -- 功能描述: 获取数据
      -- 作者: GW
      -- 创建时间: 2024年11月20日
      -- @param sql
      -- @param clazz
      -- @param args
      -- @return
      -- =============================================
     */
    public static  <T> List<T> query(String sql, Class<T> clazz, Object ...args) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ArrayList<T> list = null;
        try {
            // 1.获取一个连接
            connection = getConnection();

            // 2.预编译一个sql语句，返回一个PrepareStatement对象
            preparedStatement = connection.prepareStatement(sql);
            // 3.填充占位符
            for (int i = 0; i < args.length; i++) {
                preparedStatement.setObject(i+1, args[i]);
            }
            // 4。执行sql,得到结果集
            resultSet = preparedStatement.executeQuery();
            // 5.获取元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            list = new ArrayList();
            // 7.遍历得到每一行数据
            while (resultSet.next()){
            	 // 6.获取一个类的反射实例
                T t = clazz.newInstance();
                for (int i = 0; i < columnCount; i++) {
                    // 7.1获取列值
                    Object object = resultSet.getObject(i + 1);
                    // 7.2获取列别名
                    String columnLabel = metaData.getColumnLabel(i + 1);
                    // 7.3获取属性并设置属性的值
                    Field field = clazz.getDeclaredField(columnLabel);
                    field.setAccessible(true);
                    field.set(t, object);
                }
                list.add(t);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeConnection(connection, preparedStatement, resultSet);
        }
        return list;
    }
}

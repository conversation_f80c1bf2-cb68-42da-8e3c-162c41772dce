package cn.trasen.hrms.utils;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

/**
 * 
 -- =============================================
 --文件说明：导入Excel 工具类
 --类名称: ImpotExcelUtils
 --创建时间：2024年11月20日 
 --作者：GW
 -- =============================================
 */
public class ImpotExcelUtils {
	/**
     * 
      -- =============================================
      -- 功能描述: 获取 第一个 Sheet 所有的行数据
      -- 作者: GW
      -- 创建时间: 2024年11月20日
      -- @param excelFilePath
      -- @return
      -- =============================================
     */
    public static List<Row> getAllRows(MultipartFile file) {
        List<Row> allRows = new ArrayList<>();

        //try-with-resources  语法 自动关闭了流
        try (InputStream fis = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
            Iterator<Row> rowIterator = sheet.iterator();
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                allRows.add(row);
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return allRows;
    }
}

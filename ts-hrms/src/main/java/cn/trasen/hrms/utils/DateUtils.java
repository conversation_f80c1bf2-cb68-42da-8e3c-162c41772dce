package cn.trasen.hrms.utils;

import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.Years;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.util.ObjectUtils;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import cn.hutool.core.util.StrUtil;
import cn.trasen.hrms.enums.DateFormatEnum;
import lombok.Builder;
import lombok.Data;

/**
 * @Title: DateHelper.java
 * @Package cn.trasen.hrms.utils
 * @Description: 日期处理工具类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司
 * @date 2020年5月9日 上午9:51:43
 * @version V1.0
 */
public class DateUtils {

	/**
	 * @Title: getDifferDays
	 * @Description: 计算两个日期之间相差的天数(入参Date类型的日期)
	 * @param startDate
	 *            较小的日期
	 * @param endDate
	 *            较大的日期
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年5月9日 上午10:09:23
	 */
	public static int getDifferDays(Date startDate, Date endDate) {
		DateTime start = new DateTime(startDate);
		DateTime end = new DateTime(endDate);
		return Days.daysBetween(start, end).getDays();
	}

	/**
	 * @Title: getDifferYears
	 * @Description: 计算两个日期之间相差的年份数(入参Date类型的日期)
	 * @param startDate
	 *            较小的日期
	 * @param endDate
	 *            较大的日期
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月30日 下午3:21:45
	 */
	public static int getDifferYears(Date startDate, Date endDate) throws Exception {
		DateTime start = new DateTime(startDate);
		DateTime end = new DateTime(endDate);
		return Years.yearsBetween(start, end).getYears();
	}

	/**
	 * @Title: getDifferDays
	 * @Description: 计算两个日期之间相差的天数(入参String类型的日期)
	 * @param startDate
	 *            较小的日期
	 * @param endDate
	 *            较大的日期 * @param dateFormatEnum 日期格式化枚举
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年5月9日 上午10:09:23
	 */
	public static int getDifferDays(String startTime, String endTime, DateFormatEnum dateFormatEnum) {
		DateTimeFormatter formatter = DateTimeFormat.forPattern(dateFormatEnum.getValue());
		DateTime start = formatter.parseDateTime(startTime);
		DateTime end = formatter.parseDateTime(endTime);
		return Days.daysBetween(start, end).getDays();
	}

	/**
	 * @Title: getDifferYears
	 * @Description: 计算两个日期之间相差的年份数(入参String类型的日期)
	 * @param startDate
	 *            较小的日期
	 * @param endDate
	 *            较大的日期
	 * @param dateFormatEnum
	 *            日期格式化枚举
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月30日 下午3:23:24
	 */
	public static int getDifferYears(String startTime, String endTime, DateFormatEnum dateFormatEnum) {
		DateTimeFormatter formatter = DateTimeFormat.forPattern(dateFormatEnum.getValue());
		DateTime start = formatter.parseDateTime(startTime);
		DateTime end = formatter.parseDateTime(endTime);
		return Years.yearsBetween(start, end).getYears();
	}

	public static String getLastMonth() {
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
		Date date = new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1); // 设置为上一个月
		date = calendar.getTime();
		return format.format(date);
	}

	/**
	 * <p>
	 * @Title: getPresentTimeStr
	 * </p>
	 * <p>
	 * @Description: 获取当前时间yyyy-MM-dd HH:mm:ss
	 * </p>
	 * <p>
	 * @Param:
	 * </p>
	 * <p>
	 * @Return: String
	 * </p>
	 * <P>
	 * @Date: 2020年7月28日 下午3:43:18
	 * </p>
	 * <p>
	 * <AUTHOR>
	 * </p>
	 */
	public static String getPresentTimeStr() {
		Date d = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return sdf.format(d);
	}
	
	/**
	 * Date转字符串yyyy-MM-dd HH:mm:ss
	 * @param d
	 * @return
	 */
	public static String getPresentTimeStr(Date d) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return sdf.format(d);
	}
	
	public static String getStringDateShort(Date date) {
		if(date != null) {
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			String dateString = formatter.format(date);
			return dateString;
		}
		return "";
	}
	
	public static String getStringDateShortYM(Date date) {
		if(date != null) {
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");
			String dateString = formatter.format(date);
			return dateString;
		}
		return "";
	}
	
    /**
     * 获取当前日期 yy-MM-dd
     *
     * @return 2019-08-27
     */
    public static String getDateByString() {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd");
        return sdf.format(date);
    }

	public static String getNoByDateStr() {
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
		return sdf.format(date);
	}
	
	public static Date getStringToDate(String str) throws Exception {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");// 注意月份是MM
		return simpleDateFormat.parse(str);
	}
	
	public static Date getStringToDatePlain(String str) throws Exception {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");// 注意月份是MM
		return simpleDateFormat.parse(str);
	}

	/**
	 * 获取两个日期之间的月份列表，包括开始结束日期
	 * 
	 * @param start
	 *            开始日期
	 * @param end
	 *            结束日期
	 * @param choose
	 *            0：取年份，1取月份，2取天
	 * @return 日期集合
	 */
	public static List<String> getDateSection(String str1, String str2) {
		try {
			Date startDate = new SimpleDateFormat("yyyy-MM").parse(str1);
			Date endDate = new SimpleDateFormat("yyyy-MM").parse(str2);

			Calendar calendar = Calendar.getInstance();
			calendar.setTime(startDate);
			// 获取开始年份和开始月份
			int startYear = calendar.get(Calendar.YEAR);
			int startMonth = calendar.get(Calendar.MONTH);
			// 获取结束年份和结束月份
			calendar.setTime(endDate);
			int endYear = calendar.get(Calendar.YEAR);
			int endMonth = calendar.get(Calendar.MONTH);
			//
			List<String> list = new ArrayList<String>();
			for (int i = startYear; i <= endYear; i++) {
				String date = "";
				if (startYear == endYear) {
					for (int j = startMonth; j <= endMonth; j++) {
						if (j < 9) {
							date = i + "-0" + (j + 1);
						} else {
							date = i + "-" + (j + 1);
						}
						list.add(date);
					}

				} else {
					if (i == startYear) {
						for (int j = startMonth; j < 12; j++) {
							if (j < 9) {
								date = i + "-0" + (j + 1);
							} else {
								date = i + "-" + (j + 1);
							}
							list.add(date);
						}
					} else if (i == endYear) {
						for (int j = 0; j <= endMonth; j++) {
							if (j < 9) {
								date = i + "-0" + (j + 1);
							} else {
								date = i + "-" + (j + 1);
							}
							list.add(date);
						}
					} else {
						for (int j = 0; j < 12; j++) {
							if (j < 9) {
								date = i + "-0" + (j + 1);
							} else {
								date = i + "-" + (j + 1);
							}
							list.add(date);
						}
					}
				}

			}
			return list;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 
	* @Title: getTwoMonthBefore  
	* @Description: 获取时间前后几个月的时间
	* @Params: @param date
	* @Params: @param month
	* @Params: @return      
	* @Return: String
	* <AUTHOR>
	* @date:2021年9月15日
	* @Throws
	 */
	public static String getMonthDifferDate(Date date,int month){
		 
        // 获取当前时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        
        Calendar calendar = Calendar.getInstance(); //得到日历
        calendar.setTime(date);//把当前时间赋给日历
        calendar.add(calendar.MONTH, month); //设置为前2月，可根据需求进行修改
        date = calendar.getTime();//获取2个月前的时间
 
        return dateFormat.format(date);
    }
	
/*	public static void main(String [] args) throws ParseException {
		
//		Calendar now = Calendar.getInstance();
//		now.add(Calendar.DAY_OF_MONTH, +30);
//		String endDate = new SimpleDateFormat("yyyy-MM-dd").format(now.getTime());
//		System.out.println(endDate);
	}*/
	
	
	
	/**   
	 * @Title: dateDiff   
	 * @Description: 获取时间相差小时 
	 * @param: @param startTime  2016-05-01 12:00
	 * @param: @param endTime 2016-05-01 14:00
	 * @param: @return      
	 * @return: long      
	 * @throws   
	 */
	public static double dateDiff(String startTime, String endTime) {
		double diff = 0.0;
		try {
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			long startDate = simpleDateFormat.parse(startTime).getTime();
			long endDate = simpleDateFormat.parse(endTime).getTime();
		    diff = (endDate-startDate)/1000/60;
	        return (double) diff;

		} catch (ParseException e) {
			e.printStackTrace();
		}
      return (double) diff;
	}
	
	
	/**   
	 * @Title: judgeSize   
	 * @Description: 判断结束时间大于开始时间   
	 * @param: @param startTime
	 * @param: @param endTime
	 * @param: @return      
	 * @return: boolean      
	 * @throws   
	 */
	public static boolean judgeSize(String startTime, String endTime) {
		try {
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			long startDate = simpleDateFormat.parse(startTime).getTime();
			long endDate = simpleDateFormat.parse(endTime).getTime();
	        return (endDate > startDate) || (endDate == startDate);
		} catch (ParseException e) {
			e.printStackTrace();
		}
      return false;
	}
	
	
	
	/**   
	 * @Title: getMonthFullDay   
	 * @Description: 传入年月获取当月所有日期    
	 * @param: @param yyyy
	 * @param: @param mm
	 * @param: @return     2021-09-01 
	 * @return: List<String>      
	 * @throws   
	 */
	public static List<String> getMonthFullDay(String yyyy , String mm){
		int year = Integer.valueOf(yyyy);
		int month = Integer.valueOf(mm);
	    SimpleDateFormat dateFormatYYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");
	    List<String> fullDayList = new ArrayList<>(32);
	    // 获得当前日期对象
	    Calendar cal = Calendar.getInstance();
	    cal.clear();// 清除信息
	    cal.set(Calendar.YEAR, year);
	    // 1月从0开始
	    cal.set(Calendar.MONTH, month-1 );
	    // 当月1号
	    cal.set(Calendar.DAY_OF_MONTH,1);
	    int count = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
	    for (int j = 1; j <= count ; j++) {
	        fullDayList.add(dateFormatYYYYMMDD.format(cal.getTime()));
	        cal.add(Calendar.DAY_OF_MONTH,1);
	    }
	    return fullDayList;
	}
	
	public static String getLastDayOfMonth(String yearMonth) {
		int year = Integer.parseInt(yearMonth.split("-")[0]);  //年
		int month = Integer.parseInt(yearMonth.split("-")[1]); //月
		Calendar cal = Calendar.getInstance();
		// 设置年份
		cal.set(Calendar.YEAR, year);
		// 设置月份
		// cal.set(Calendar.MONTH, month - 1);
		cal.set(Calendar.MONTH, month); //设置当前月的上一个月
		// 获取某月最大天数
		//int lastDay = cal.getActualMaximum(Calendar.DATE);
		int lastDay = cal.getMinimum(Calendar.DATE); //获取月份中的最小值，即第一天
		// 设置日历中月份的最大天数
		//cal.set(Calendar.DAY_OF_MONTH, lastDay);
		cal.set(Calendar.DAY_OF_MONTH, lastDay - 1); //上月的第一天减去1就是当月的最后一天
		// 格式化日期
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		return sdf.format(cal.getTime());
	}
	
	public static String getLastMonth(String dateStr, int addYear, int addMonth, int addDate)  {
		try {
			java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM");
			java.util.Date sourceDate = sdf.parse(dateStr);
			Calendar cal = Calendar.getInstance();
			cal.setTime(sourceDate);
			cal.add(Calendar.YEAR, addYear);
			cal.add(Calendar.MONTH, addMonth);
			cal.add(Calendar.DATE, addDate);
			java.text.SimpleDateFormat returnSdf = new java.text.SimpleDateFormat("yyyy-MM");
			String dateTmp = returnSdf.format(cal.getTime());
//			java.util.Date returnDate = returnSdf.parse(dateTmp);
			return dateTmp;
		} catch (Exception e) {
			e.printStackTrace();
			return "";		
		}

	}
	
	
	/**   
	 * @Title: getDateBefore30   
	 * @Description: 获取三十天之前时间   
	 * @param: @return      
	 * @return: String      
	 * @throws   
	 */
	public static String getDateBefore30() {
		Calendar now = Calendar.getInstance();
		now.add(Calendar.DAY_OF_MONTH, -30);
		String endDate = new SimpleDateFormat("yyyy-MM-dd").format(now.getTime());
		return endDate;
	}
	
	public static String getDateLater10() {
		Calendar now = Calendar.getInstance();
		now.add(Calendar.DAY_OF_MONTH, + 10);
		String endDate = new SimpleDateFormat("yyyy-MM-dd").format(now.getTime());
		return endDate;
	}
	
	
	  
	/**   
	 * @Title: getDays   
	 * @Description: 返回两个日期之间的所有天数
	 * @param: @param startTime
	 * @param: @param endTime
	 * @param: @return      
	 * @return: List<String>      
	 * @throws   
	 */
	public static List<String> getDays(String startTime, String endTime) {
	        // 返回的日期集合
	        List<String> days = new ArrayList<String>();
	        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
	        try {
	            Date start = dateFormat.parse(startTime);
	            Date end = dateFormat.parse(endTime);
	            Calendar tempStart = Calendar.getInstance();
	            tempStart.setTime(start);
	            Calendar tempEnd = Calendar.getInstance();
	            tempEnd.setTime(end);
	            tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)
	            while (tempStart.before(tempEnd)) {
	                days.add(dateFormat.format(tempStart.getTime()));
	                tempStart.add(Calendar.DAY_OF_YEAR, 1);
	            }
	        } catch (ParseException e) {
	            e.printStackTrace();
	        }
	        return days;
	}
	
	//本季度开始时间
	 public static Date getCurrentQuarterStartTime() {
	        Calendar c = Calendar.getInstance();
	        int currentMonth = c.get(Calendar.MONTH) + 1;
	        SimpleDateFormat longSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd");
	        Date now = null;
	        try {
	            if (currentMonth >= 1 && currentMonth <= 3)
	                c.set(Calendar.MONTH, 0);
	            else if (currentMonth >= 4 && currentMonth <= 6)
	                c.set(Calendar.MONTH, 3);
	            else if (currentMonth >= 7 && currentMonth <= 9)
	                c.set(Calendar.MONTH, 4);
	            else if (currentMonth >= 10 && currentMonth <= 12)
	                c.set(Calendar.MONTH, 9);
	            c.set(Calendar.DATE, 1);
	            now = longSdf.parse(shortSdf.format(c.getTime()) + " 00:00:00");
	        } catch (Exception e) {
	            e.printStackTrace();
	        }
	        return now;
	    }
	    
		//本季度结束时间
	    public static Date getCurrentQuarterEndTime() {
	        Calendar cal = Calendar.getInstance();
	        cal.setTime(getCurrentQuarterStartTime());
	        cal.add(Calendar.MONTH, 3);
	        return cal.getTime();
	    }
	    
	    //获取两个日期之间的所有天数
	    public static List<String> getBetweenDate(String startTime, String endTime){
	        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	        // 声明保存日期集合
	        List<String> list = new ArrayList<String>();
	        try {
	            // 转化成日期类型
	            Date startDate = sdf.parse(startTime);
	            Date endDate = sdf.parse(endTime);
	 
	            //用Calendar 进行日期比较判断
	            Calendar calendar = Calendar.getInstance();
	            while (startDate.getTime()<=endDate.getTime()){
	                // 把日期添加到集合
	                list.add(sdf.format(startDate));
	                // 设置日期
	                calendar.setTime(startDate);
	                //把日期增加一天
	                calendar.add(Calendar.DATE, 1);
	                // 获取增加后的日期
	                startDate=calendar.getTime();
	            }
	        } catch (ParseException e) {
	            e.printStackTrace();
	        }
	        return list;
	    }
	    
	    
	    //获取月份的天数
	    public static int getDaysOfMonth(Date date) {
	    	try {
				Calendar calendar = Calendar.getInstance();
				calendar.setTime(date);
				return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
			} catch (Exception e) {
				e.printStackTrace();
			}
	    	return 0;
	    }

	/** 
	* @Title: getSevenDate 
	* @Description: 获取过去十天的日期
	* @param @return    设定文件 
	* @return List<String>    返回类型 
	* @throws 
	*/
	public static List<Date> getSevenDate() {
		List<Date> dateList = new ArrayList<>();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		for (int i = 10; i >= 1; i--) {
			Date date = org.apache.commons.lang3.time.DateUtils.addDays(new Date(), -i);
//			String formatDate = sdf.format(date);
			dateList.add(date);
		}
		return dateList;
	}
	
	/** 
	* @Title: getMonthDay 
	* @Description: 返回年月的所有日期
	* @param @param year
	* @param @param month
	* @param @return    设定文件 
	* @return List<String>    返回类型 
	* @throws 
	*/
	public static List<String> getMonthDay(int year , int month){
	    SimpleDateFormat dateFormatYYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");
	    List<String> fullDayList = new ArrayList<>(32);
	    // 获得当前日期对象
	    Calendar cal = Calendar.getInstance();
	    cal.clear();// 清除信息
	    cal.set(Calendar.YEAR, year);
	    // 1月从0开始
	    cal.set(Calendar.MONTH, month-1 );
	    // 当月1号
	    cal.set(Calendar.DAY_OF_MONTH,1);
	    int count = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
	    for (int j = 1; j <= count ; j++) {
	        String format = dateFormatYYYYMMDD.format(cal.getTime()); //号
	        fullDayList.add(format);  //日期
	        cal.add(Calendar.DAY_OF_MONTH,1);
	    }
	    return fullDayList;
	}
	
	public static String extractYearMonthDayOfIdCard(String id) {
	    String year = null;
	    String month = null;
	    String day = null;
	    //正则匹配身份证号是否是正确的，15位或者17位数字+数字/x/X
	    if (id.matches("^\\d{15}|\\d{17}[\\dxX]$")) {
	        year = id.substring(6, 10);
	        month = id.substring(10,12);
	        day = id.substring(12,14);
	    }else {
	        return null;
	    }
	    return year + "-" + month + "-" + day;
	}
	
	/**
	 * 计算2个日期相差多少年
	 * 列：2011-02-02  ~  2017-03-02 大约相差 6.1 年
	 * @param fromDate
	 * @param toDate
	 * @return
	 */
	public static String yearCompare(Date fromDate,Date toDate){
	    DayCompare result = dayComparePrecise(fromDate, toDate);
	    double month = result.getMonth();
	    double year = result.getYear();
	    //返回2位小数，并且四舍五入
	    DecimalFormat df = new DecimalFormat("######0.0");
	    return df.format(year + month / 12);
	}
	
	
	/**
	 * 计算2个日期之间相差的  相差多少年月日
	 * 比如：2011-02-02 到  2017-03-02 相差 6年，1个月，0天
	 * @param fromDate
	 * @param toDate
	 * @return
	 */
	public static DayCompare dayComparePrecise(Date fromDate,Date toDate){
	    Calendar  from  =  Calendar.getInstance();
	    from.setTime(fromDate);
	    Calendar  to  =  Calendar.getInstance();
	    to.setTime(toDate);
	 
	    int fromYear = from.get(Calendar.YEAR);
	    int fromMonth = from.get(Calendar.MONTH);
	    int fromDay = from.get(Calendar.DAY_OF_MONTH);
	 
	    int toYear = to.get(Calendar.YEAR);
	    int toMonth = to.get(Calendar.MONTH);
	    int toDay = to.get(Calendar.DAY_OF_MONTH);
	    int year = toYear  -  fromYear;
	    int month = toMonth  - fromMonth;
	    int day = toDay  - fromDay;
	    return DayCompare.builder().day(day).month(month).year(year).build();
	}
	
	/**
	 * 计算2个日期之间相差的  以年、月、日为单位，各自计算结果是多少
	 * 比如：2011-02-02 到  2017-03-02
	 *                                以年为单位相差为：6年
	 *                                以月为单位相差为：73个月
	 *                                以日为单位相差为：2220天
	 * @param fromDate
	 * @param toDate
	 * @return
	 */
	public static DayCompare dayCompare(Date fromDate,Date toDate){
	    Calendar  from  =  Calendar.getInstance();
	    from.setTime(fromDate);
	    Calendar  to  =  Calendar.getInstance();
	    to.setTime(toDate);
	    //只要年月
	    int fromYear = from.get(Calendar.YEAR);
	    int fromMonth = from.get(Calendar.MONTH);
	 
	    int toYear = to.get(Calendar.YEAR);
	    int toMonth = to.get(Calendar.MONTH);
	 
	    int year = toYear  -  fromYear;
	    int month = toYear *  12  + toMonth  -  (fromYear  *  12  +  fromMonth);
	    int day = (int) ((to.getTimeInMillis()  -  from.getTimeInMillis())  /  (24  *  3600  *  1000));
	    return DayCompare.builder().day(day).month(month).year(year).build();
	}
	
    @Data
    @Builder
    public static class DayCompare{
        private int year;
        private int month;
        private int day;
    }
	
    
    public static String getXMonthLastDate(String date,int month){
        Calendar calendar=Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            //将传过来的日期设置给calendar
            calendar.setTime(sdf.parse(date));
//            System.out.println("当前日期="+sdf.format(calendar.getTime()));
            //将传过来的月份减去X个月或者加上X个月
            calendar.add(Calendar.MONTH, month);
            //System.out.println("向前推12月之前的日期="+sdf.format(calendar.getTime()));
            //获取月的最后一天日期
//            calendar.set(Calendar.DAY_OF_MONTH,calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        }catch (Exception e){
            e.printStackTrace();
        }
        return sdf.format(calendar.getTime());
    }

	
	//获取上月第一天
	public static String getlastMonthfirstDay(String date) {
		try {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd"); // 格式化时间
			Date stringToDate = getStringToDate(date);
			Calendar cal = Calendar.getInstance();// 获取当前日期
			cal.setTime(stringToDate);
			cal.add(Calendar.MONTH, -1);
			cal.set(Calendar.DAY_OF_MONTH,1);//设置为1号
			cal.set(Calendar.HOUR_OF_DAY,0);
			cal.set(Calendar.MINUTE,0);
			cal.set(Calendar.SECOND,0);
			String firstDay = format.format(cal.getTime());
			return firstDay;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public static String getlastMonthfirstDay(Date stringToDate) {
		try {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd"); // 格式化时间
			Calendar cal = Calendar.getInstance();// 获取当前日期
			cal.setTime(stringToDate);
			cal.add(Calendar.MONTH, -1);
			cal.set(Calendar.DAY_OF_MONTH,1);//设置为1号
			cal.set(Calendar.HOUR_OF_DAY,0);
			cal.set(Calendar.MINUTE,0);
			cal.set(Calendar.SECOND,0);
			String firstDay = format.format(cal.getTime());
			return firstDay;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	//获取上月最后一天
	public static String getlastMonthLastDay(String date) {
		try {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd"); // 格式化时间
			Date stringToDate = getStringToDate(date);
			Calendar cal = Calendar.getInstance();// 获取当前日期
			cal.setTime(stringToDate);
			cal.add(Calendar.MONTH, -1);
			cal.set(Calendar.DAY_OF_MONTH, 1);
			cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
			String lastDay  = format.format(cal.getTime());
			return lastDay;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public static String getlastMonthLastDay(Date stringToDate) {
		try {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd"); // 格式化时间
			Calendar cal = Calendar.getInstance();// 获取当前日期
			cal.setTime(stringToDate);
			cal.add(Calendar.MONTH, -1);
			cal.set(Calendar.DAY_OF_MONTH, 1);
			cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
			String lastDay  = format.format(cal.getTime());
			return lastDay;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	
	//获取本月第一天
	public static String getThisMonthfirstDay(String date) {
		try {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd"); // 格式化时间
			Date stringToDate = getStringToDate(date);
			Calendar cal = Calendar.getInstance();// 获取当前日期
			cal.setTime(stringToDate);
//			cal.add(Calendar.MONTH, -1);
			cal.set(Calendar.DAY_OF_MONTH,1);//设置为1号
			cal.set(Calendar.HOUR_OF_DAY,0);
			cal.set(Calendar.MINUTE,0);
			cal.set(Calendar.SECOND,0);
			String firstDay = format.format(cal.getTime());
			return firstDay;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	//获取本月月最后一天
	public static String getThisMonthLastDay(String date) {
		try {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd"); // 格式化时间
			Date stringToDate = getStringToDate(date);
			Calendar cal = Calendar.getInstance();// 获取当前日期
			cal.setTime(stringToDate);
//			cal.add(Calendar.MONTH, -1);
			cal.set(Calendar.DAY_OF_MONTH, 1);
			cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
			String lastDay  = format.format(cal.getTime());
			return lastDay;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	
	
	//取第一天
    public static String getFirstDay(Date date) {
    	SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
    	String startDay = df.format(new Date());
    	Calendar calendar = Calendar.getInstance();
    	calendar.set(Integer.parseInt(startDay.substring(0,4)), Integer.parseInt(startDay.substring(5,7)) - 1, 1);
    	String firstDayOfMonth = new SimpleDateFormat( "yyyy-MM-dd ").format(calendar.getTime());
    	return firstDayOfMonth;
    } 
    
	public static String getWeek(String str)  {
		try {
			String[] weekDays = { "星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六" };
			Calendar cal = Calendar.getInstance();
			cal.setTime(DateUtils.getStringToDate(str));
			// 指示一个星期中的某天。
			int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
			if (w < 0)
				w = 0;
			return weekDays[w];
		} catch (Exception e) {
			
		}
		return null;
	}
	
	/**
     * 根据身份证号计算退休日期
     * @return
     */
    public static Date getRetirementDate(String cardId) {
        // 判断用户身份证号为18位时
        if (cardId.length() == 18) {
            // 根据用户的身份证号得到用户出生日期字符串
            String birthdayString = cardId.substring(6).substring(0, 8);
            // 获取用户出生年份
            String year = birthdayString.substring(0, 4);
            // 获取用户出生月份
            String month = birthdayString.substring(4, 6);
            // 获取用户出生日期
            String day = birthdayString.substring(6, 8);
            // 拼接 例1990-05-27
            String birthday = year+"-"+month+"-"+day;
            // 转Date类型
            Date date = DateUtil.parseDate(birthday);
            // 获取当前时间
            Calendar cal = Calendar.getInstance();
            // 把时间设置为该用户的出生日期
            cal.setTime(date);
            // 判断用户的性别 这里是取的身份证倒数第二位判断性别
            if (Integer.parseInt(cardId.substring(16).substring(0, 1)) % 2 == 0) {
                // 女 设置退休日期为出生日期后55年
                cal.add(Calendar.YEAR, 55);
                return cal.getTime();
            } else {
                // 男 设置退休日期为出生日期后60年
                cal.add(Calendar.YEAR, 60);
                return cal.getTime();
            }
        // 判断身份证号为15位时
        } else if (cardId.length() == 15) {
            // 获取用户出生年份
            String year = "19" + cardId.substring(6, 8);
            // 获取用户出生月份
            String month = cardId.substring(8, 10);
            // 获取用户出生日期
            String day = cardId.substring(10, 12);
            // 拼接 例1990-05-27
            String birthday = year+"-"+month+"-"+day;
            // 转Date类型
            Date date = DateUtil.parseDate(birthday);
            // 获取当前时间
            Calendar cal = Calendar.getInstance();
            // 把时间设置为该用户的出生日期
            cal.setTime(date);
            // 判断用户的性别 同上
            if (Integer.parseInt(cardId.substring(14, 15)) % 2 == 0) {
                // 女 设置退休日期为出生日期后55年
                cal.add(Calendar.YEAR, 55);
                return cal.getTime();
            } else {
                // 男 设置退休日期为出生日期后60年
                cal.add(Calendar.YEAR, 60);
                return cal.getTime();
            }
        } else {
        	// 如果代码走到了这里,说明身份证号格式不正确,看着返吧
            return null;
        }
    }

	/**
	 * 获取当前日期 yy-MM-dd
	 *
	 * @return 2019-08-27
	 */
	public static String getDateString() {
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		return sdf.format(date);
	}
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据日期范围，获取连续的 日期Str 和 周，例如： {"dateStr" : "2024-12-28","weekStr","周六"}
	  -- 作者: GW
	  -- 创建时间: 2024年12月28日
	  -- @param searchType 0 7日 1本周 2上周 3下周 4自定义日期范围 5本月 6上月
	  			begntime  开始时间 yyyy-mm-dd
	  			endtime   结束时间 yyyy-mm-dd
	  -- @return
	  -- =============================================
	 */
	public static List<Map<String,String>> getDateStrAndWeekStr(String searchType ,String begntime,String endtime) {
		List<Map<String,String>> list = new ArrayList<Map<String,String>>();
		searchType = StrUtil.isEmpty(searchType) ? "4" : searchType; 
		Date now = new Date();
		Map<String,String> map ;
		Date date;
		switch (searchType) {
		case "1": // 本周
			for(int i = 0 ;i < 7 ;i++){
				map = new HashMap<String,String>();
				date = DateUtil.offsetDay(DateUtil.beginOfWeek(now), i);
				map.put("dateStr", DateUtil.formatDate(date));
				map.put("weekStr", Week.of(DateUtil.dayOfWeek(date)).toChinese("周"));
				list.add(map);
			}
			break;
		case "2": //上周
			for(int i = 0 ;i < 7 ;i++){
				map = new HashMap<String,String>();
				date = DateUtil.offsetDay(DateUtil.beginOfWeek(DateUtil.offsetWeek(now, -1)), i);
				map.put("dateStr", DateUtil.formatDate(date));
				map.put("weekStr", Week.of(DateUtil.dayOfWeek(date)).toChinese("周"));
				list.add(map);
			}
			break;
		case "3": //下周
			for(int i = 0 ;i < 7 ;i++){
				map = new HashMap<String,String>();
				date = DateUtil.offsetDay(DateUtil.beginOfWeek(DateUtil.offsetWeek(now, 1)), i);
				map.put("dateStr", DateUtil.formatDate(date));
				map.put("weekStr", Week.of(DateUtil.dayOfWeek(date)).toChinese("周"));
				list.add(map);
			}
			break;
		case "4": //自定义日期范围
			if(StrUtil.isEmpty(begntime) || StrUtil.isEmpty(endtime)){
				return list;
			}
			for(int i = 0 ;i <= (int)DateUtil.between(DateUtil.parse(begntime,"yyyy-MM-dd"), DateUtil.parse(endtime,"yyyy-MM-dd"), DateUnit.DAY) ;i++){
				map = new HashMap<String,String>();
				date = DateUtil.offsetDay(DateUtil.parse(begntime,"yyyy-MM-dd"), i);
				map.put("dateStr", DateUtil.formatDate(date));
				map.put("weekStr", Week.of(DateUtil.dayOfWeek(date)).toChinese("周"));
				list.add(map);
			}
			break;
		case "5": //本月
			for(int i = 0 ;i < DateUtil.dayOfMonth(DateUtil.endOfMonth(now));i++){
				map = new HashMap<String,String>();
				date = DateUtil.offsetDay(DateUtil.beginOfMonth(now), i);
				map.put("dateStr", DateUtil.formatDate(date));
				map.put("weekStr", Week.of(DateUtil.dayOfWeek(date)).toChinese("周"));
				list.add(map);
			}
			break;
		case "6": //上月
			Date lastMonthDate = DateUtil.lastMonth(); //上个月的这一天
			for(int i = 0 ;i < DateUtil.dayOfMonth(DateUtil.endOfMonth(lastMonthDate));i++){
				map = new HashMap<String,String>();
				date = DateUtil.offsetDay(DateUtil.beginOfMonth(lastMonthDate), i);
				map.put("dateStr", DateUtil.formatDate(date));
				map.put("weekStr", Week.of(DateUtil.dayOfWeek(date)).toChinese("周"));
				list.add(map);
			}
			break;
		default:
			for(int i = 0 ;i < 7 ;i++){
				map = new HashMap<String,String>();
				date = DateUtil.offsetDay(now, i);
				map.put("dateStr", DateUtil.formatDate(date));
				map.put("weekStr", Week.of(DateUtil.dayOfWeek(date)).toChinese("周"));
				list.add(map);
			}
			break;
		}
		return list;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据日期范围，获取连续的 日期Str
	  -- 作者: GW
	  -- 创建时间: 2024年12月28日
	  -- @param searchType  0 7日 1本周 2上周 3下周 4自定义日期范围 5本月 6上月
	  -- @param begntime 开始时间 yyyy-mm-dd
	  -- @param endtime 结束时间 yyyy-mm-dd
	  -- @return
	  -- =============================================
	 */
	public static List<String> getDateStr(String searchType ,String begntime,String endtime) {
		List<String> list = new ArrayList<String>();
		searchType = StrUtil.isEmpty(searchType) ? "4" : searchType; //默认自定义范围
		Date now = new Date();
		switch (searchType) {
		case "1": // 本周
			for(int i = 0 ;i < 7 ;i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.beginOfWeek(now), i)));
			}
			break;
		case "2": //上周
			for(int i = 0 ;i < 7 ;i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.beginOfWeek(DateUtil.offsetWeek(now, -1)), i)));
			}
			break;
		case "3": //下周
			for(int i = 0 ;i < 7 ;i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.beginOfWeek(DateUtil.offsetWeek(now, 1)), i)));
			}
			break;
		case "4": //自定义日期范围
			if(StrUtil.isEmpty(begntime) || StrUtil.isEmpty(endtime)){
				return list;
			}
			for(int i = 0 ;i <= (int)DateUtil.between(DateUtil.parse(begntime,"yyyy-MM-dd"), DateUtil.parse(endtime,"yyyy-MM-dd"), DateUnit.DAY) ;i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(begntime,"yyyy-MM-dd"), i)));
			}
			break;
		case "5": //本月
			for(int i = 0 ;i < DateUtil.dayOfMonth(DateUtil.endOfMonth(now));i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.beginOfMonth(now), i)));
			}
			break;
		case "6": //上月
			Date lastMonthDate = DateUtil.lastMonth(); //上个月的这一天
			for(int i = 0 ;i < DateUtil.dayOfMonth(DateUtil.endOfMonth(lastMonthDate));i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.beginOfMonth(lastMonthDate), i)));
			}
			break;
		default:
			for(int i = 0 ;i < 7 ;i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(now, i)));
			}
			break;
		}
		return list;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据指定的日期，获取type相应的日期范围的DateStr
	  -- @param date
	  -- @return
	  -- =============================================
	 */
	public static List<String> getDateStrByType(String type ,String dateStr) {
		List<String> list = new ArrayList<String>();
		try {
			Date date = DateUtil.parseDate(dateStr);
			switch (type) {
			case "1": // 周
				for(int i = 0 ;i < 7 ;i++){
					list.add(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.beginOfWeek(date), i)));
				}
				break;
			case "2": //月
				for(int i = 0 ;i < DateUtil.dayOfMonth(DateUtil.endOfMonth(date));i++){
					list.add(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.beginOfMonth(date), i)));
				}
				break;
			default:
				
			}
		} catch (Exception e) {
		}
		return list;
	}
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 计算缺失的日期
	  -- @param date
	  -- @return
	  -- =============================================
	 */
	public static List<String> getMissingDates(List<String> inputDates,String startDate,String endDate) {
		
	    // 步骤1：解析并去重
	    Set<LocalDate> existingDates = new HashSet<>();
	    for (String dateStr : inputDates) {
	        existingDates.add(LocalDate.parse(dateStr));
	    }

	    // 步骤2：计算两个日期之间的天数
	    String[] startDates = startDate.split("-");
	    //String[] endDates = endDate.split("-");
	    int year = Integer.valueOf(startDates[0]);
	    int month = Integer.valueOf(startDates[1]);
	    int day = Integer.valueOf(startDates[2]);
		LocalDate start = LocalDate.of(year, month, day);

//	    YearMonth yearMonth = YearMonth.of(year, month);
//        int days = yearMonth.lengthOfMonth();

		Date startD = DateUtil.parse(startDate);
		Date endD = DateUtil.parse(endDate);
		long days = DateUtil.between(startD,endD,DateUnit.DAY);

	    Set<LocalDate> allAprilDates = Stream.iterate(start, date -> date.plusDays(1))
	            .limit(days)
	            .collect(Collectors.toSet());

	    // 步骤3：计算缺失日期（差集）
	    allAprilDates.removeAll(existingDates);

	    // 步骤4：排序并输出
	    List<LocalDate> missingDates = allAprilDates.stream()
	            .sorted()
	            .collect(Collectors.toList());
	    
	    List<String> result = new ArrayList<>();
	    java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
	    for (LocalDate localDate : missingDates) {
	    	result.add(localDate.format(formatter));
		}

	    return result;
	}
	
	/**
     * 根据年份和季度计算该季度的最后一天
     * @param year 年份
     * @param quarter 季度 (1-4)
     * @param dataPattern 默认yyyy-MM-dd
     * @return 季度的最后一天的 String 对象
     */
    public static String getLastDayOfQuarter(int year, int quarter, String dataPattern) {
		// 参数校验
		if (quarter < 1 || quarter > 4) {
//			throw new IllegalArgumentException("季度必须在1到4之间");
			return null;
		}

		// 确定季度的最后一个月
		int lastMonthOfQuarter = quarter * 3;

		// 获取该月的最后一天
		YearMonth yearMonth = YearMonth.of(year, lastMonthOfQuarter);

		// 使用线程安全的 DateTimeFormatter
		if(ObjectUtils.isEmpty(dataPattern)){
			dataPattern = "yyyy-MM-dd";
		}
		java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern(dataPattern);

		return yearMonth.atEndOfMonth().format(formatter);
    }

	/**
	 * 根据年份和季度获取季度的第一天
	 * @param year
	 * @param quarter
     * @param dataPattern 默认yyyy-MM-dd
	 * @return
	 */
	public static String getFirstDayOfQuarter(int year, int quarter, String dataPattern) {
		// 校验季度是否合法（1-4）
		if (quarter < 1 || quarter > 4) {
//			throw new IllegalArgumentException("季度必须是 1-4 之间的整数");
			return null;
		}

		// 计算该季度的第一个月（1、4、7、10）
		int firstMonth = (quarter - 1) * 3 + 1;

		// 构造该季度的第一天
		LocalDate date = LocalDate.of(year, firstMonth, 1);

		// 使用线程安全的 DateTimeFormatter
		if(ObjectUtils.isEmpty(dataPattern)){
			dataPattern = "yyyy-MM-dd";
		}
		java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern(dataPattern);
		return date.format(formatter);
	}
	
	/**
     * 获取两个日期之间的所有周末日期（周六和周日）
     * @param startDateStr 开始日期，格式为"yyyy-MM-dd"
     * @param endDateStr 结束日期，格式为"yyyy-MM-dd"
     * @return 包含所有周末日期的列表，格式为"yyyy-MM-dd"
     */
    public static List<String> getWeekendDates(String startDateStr, String endDateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<String> weekendList = new ArrayList<>();
        
        try {
            // 解析输入的日期字符串
            Date startDate = sdf.parse(startDateStr);
            Date endDate = sdf.parse(endDateStr);
            
            // 验证日期有效性
            if (startDate.after(endDate)) {
                throw new IllegalArgumentException("开始日期不能晚于结束日期");
            }
            
            // 使用Calendar处理日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            
            // 循环检查每一天
            while (!calendar.getTime().after(endDate)) {
                int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
                
                // 判断是否为周六（Calendar.SATURDAY = 7）或周日（Calendar.SUNDAY = 1）
                if (dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY) {
                    weekendList.add(sdf.format(calendar.getTime()));
                }
                
                // 移动到下一天
                calendar.add(Calendar.DATE, 1);
            }
            
            return weekendList;
        } catch (ParseException e) {
            System.err.println("日期格式错误，请使用yyyy-MM-dd格式");
            e.printStackTrace();
        } catch (IllegalArgumentException e) {
            System.err.println(e.getMessage());
        }
        
        return null;
    }
    public static void main(String[] args) {
    	List<String> list = getWeekendDates("2025-08-01", "2025-08-31");
    	for(String k : list){
    		System.out.println(k);
    	}
	}
}

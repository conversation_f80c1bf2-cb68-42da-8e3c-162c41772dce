package cn.trasen.hrms.utils;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;

import cn.trasen.homs.core.utils.StringUtil;
 
/**
 * @Author: gongPing
 * ExportData迭代更新版本，增加了添加二级表头功能
 *传参1 String[][] headers {{一级表头,key},{一级表头,key},{一级表头,二级表头1,二级表头2...}}这里二级表头的key就是它的名称
 *传参2 title 标题 为空默认sheet
 *传参3 String[] mergeLine 要合并的列，这里是将指定的列全部合并
 *
 * 参考方法：/centerMeetingDayReport/excel
 * @Date: 2020/11/15 10:26
 */
public class ExportDataNew {
    private XSSFCellStyle headStyle;
    private XSSFCellStyle titleStyle;
    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    /**
     * 创建一行
     */
    private SXSSFRow row = null;
    private SXSSFCell cell = null;
    private int currentRow = 0;
    private XSSFCellStyle stringStyle;
    private String[][] headers;
    private String sheetName;
    private String sheetTitle;
    private Integer[] mergeLines;
    /**
     *
     * @param headers  表头列
     * @param title   标题
     * @param mergeLine  要合并单元格的列
     */
    public ExportDataNew(String[][] headers,String title,Integer[] mergeLine) throws FileNotFoundException {
        this.headers = headers;
        if(StringUtil.isEmpty(title)){
            this.sheetTitle ="sheet";
        }else{
            this.sheetTitle = title;
        }
 
        this.sheetName = "sheet1";
        this.mergeLines = mergeLine;
        try {
            workbook = new SXSSFWorkbook(1000);
            this.headStyle = (XSSFCellStyle) this.workbook.createCellStyle();
            headStyle.setBorderBottom(XSSFCellStyle.BORDER_THIN);
            headStyle.setBorderLeft(XSSFCellStyle.BORDER_THIN);
            headStyle.setBorderRight(XSSFCellStyle.BORDER_THIN);
            headStyle.setBorderTop(XSSFCellStyle.BORDER_THIN);
            headStyle.setFillForegroundColor(IndexedColors.AQUA.getIndex());
            headStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
            headStyle.setAlignment(XSSFCellStyle.ALIGN_CENTER);
            XSSFFont headFont = (XSSFFont) workbook.createFont();
            // 设置头部字体为宋体
            headFont.setFontName("宋体");
            // 粗体
            headFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
            headFont.setFontHeightInPoints((short) 11);
            // 单元格样式使用字体
            this.headStyle.setFont(headFont);
            
            
            
            
            
            this.titleStyle = (XSSFCellStyle) this.workbook.createCellStyle();
            titleStyle.setBorderBottom(XSSFCellStyle.BORDER_THIN);
            titleStyle.setBorderLeft(XSSFCellStyle.BORDER_THIN);
            titleStyle.setBorderRight(XSSFCellStyle.BORDER_THIN);
            titleStyle.setBorderTop(XSSFCellStyle.BORDER_THIN);
            titleStyle.setFillForegroundColor(IndexedColors.AQUA.getIndex());
            titleStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
            titleStyle.setAlignment(XSSFCellStyle.ALIGN_CENTER);
            XSSFFont titleStyleheadFont = (XSSFFont) workbook.createFont();
            // 设置头部字体为宋体
            titleStyleheadFont.setFontName("宋体");
            // 粗体
            titleStyleheadFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
            titleStyleheadFont.setFontHeightInPoints((short) 16);
            // 单元格样式使用字体
            this.titleStyle.setFont(titleStyleheadFont);
            
            
            this.stringStyle = (XSSFCellStyle) this.workbook.createCellStyle();
            //设置单元格样式 
            stringStyle.setAlignment(HorizontalAlignment.CENTER);//增加水平居中样式
            stringStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);//增加垂直居中样式
            //excle边框样式添加
            stringStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
            stringStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
            stringStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
            stringStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
            
 
            createSheet(sheetName, headers,sheetTitle);
        } catch (Exception exc) {
            exc.printStackTrace();
        }
 
    }
    
 
    /**
     * 创建表头
     *
     * @param sheetName
     * @param headers
     */
    private void createSheet(String sheetName, String[][] headers,String sheetTitle) {
        sheet = (SXSSFSheet) workbook.createSheet(sheetName);
        //创建第一行表头
        row = (SXSSFRow) sheet.createRow(currentRow);
        int realLen = 0;
        int startLen = 0;
        cell = (SXSSFCell) row.createCell(realLen);
        cell.setCellType(XSSFCell.CELL_TYPE_STRING);
        headStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headStyle.setAlignment(CellStyle.ALIGN_CENTER);//增加水平居中样式-old
        headStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);//增加垂直居中样式-old
        cell.setCellStyle(headStyle);//设置样式
        cell.setCellValue(sheetTitle);//设置标题
        
        cell.setCellStyle(titleStyle);   
        int titleLen = getTitleLen(headers);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, titleLen-1)); // 合并单元格
        currentRow++;
        row = (SXSSFRow) sheet.createRow(currentRow);
        for (int i = 0; i < headers.length; i++) {
            String[] headerT = headers[i];
            if (headerT.length > 2) {
                //一级表头
                Integer len = headerT.length;
                for (int j = 0; j < len - 1; j++) {
                    cell = (SXSSFCell) row.createCell(realLen);
                    realLen++;
                    cell.setCellType(XSSFCell.CELL_TYPE_STRING);
                    cell.setCellStyle(headStyle);
                    cell.setCellValue(headers[i][0]);
                }
                sheet.addMergedRegion(new CellRangeAddress(1, 1, startLen, realLen - 1)); // 合并单元格
                startLen = realLen;
 
            } else {
                cell = (SXSSFCell) row.createCell(i);
                cell.setCellType(XSSFCell.CELL_TYPE_STRING);
                cell.setCellValue(headers[i][0]);
                cell.setCellStyle(headStyle);
                realLen++;
                startLen++;
            }
 
        }
        currentRow++;
        //创建第二行表头
        realLen = 0;
        row = (SXSSFRow) sheet.createRow(currentRow);
        for (int i = 0; i < headers.length; i++) {
 
            String[] headerT = headers[i];
            if (headerT.length > 2) {
                //二级表头
                Integer len = headerT.length;
                for (int j = 1; j < len; j++) {
                    String title = headerT[j];
                    cell = (SXSSFCell) row.createCell(realLen);
                    realLen++;
                    cell.setCellType(XSSFCell.CELL_TYPE_STRING);
                    cell.setCellStyle(headStyle);
                    cell.setCellValue(title);
                }
 
            } else {
                cell = (SXSSFCell) row.createCell(i);
                cell.setCellType(XSSFCell.CELL_TYPE_STRING);
                cell.setCellStyle(headStyle);
                cell.setCellValue("");
                //合并1-2行
                sheet.addMergedRegion(new CellRangeAddress(1, 2, i, i));
                realLen++;
            }
        }
        currentRow++;
    }
 
    /**
     * 获取表格宽度
     *
     * @param headers
     * @return
     */
    private int getTitleLen(String[][] headers) {
        int realLen = 0;
        for (int i = 0; i < headers.length; i++) {
            String[] headerT = headers[i];
            if (headerT.length > 2) {
                //一级表头
                Integer len = headerT.length;
                for (int j = 0; j < len - 1; j++) {
                    realLen++;
                }
            } else {
                realLen++;
            }
        }return realLen;
 
    }
 
    /**
     * 导出excel
     *
     * @param listRows
     * @throws ParseException
     */
    private void poiWriteExcelTo2007(List<Map<String, String>> listRows, OutputStream out)
            throws ParseException {
        for (int i = 0; i < listRows.size(); i++) {
            row = (SXSSFRow) sheet.createRow(currentRow);
            Map<String, String> listCells = listRows.get(i);
            int realLen=0;
            
            /**
            for (int j = 0; j < this.headers.length; j++) {
                String[] headerT=this.headers[j];
                if(headerT.length>2){
                    for(int k=1;k<headerT.length;k++){
                        String obj = listCells.get(headerT[k]) == null ? "" : String.valueOf(listCells.get(headerT[k]));
                        cell = (SXSSFCell) row.createCell(realLen);
                        realLen++;
                        cell.setCellValue(obj);
                        cell.setCellStyle(stringStyle);
                    }
                }else{
                    String obj = listCells.get(this.headers[j][1]) == null ? "" : String.valueOf(listCells.get(this.headers[j][1]));
                    cell = (SXSSFCell) row.createCell(j);
                    realLen++;
                    cell.setCellValue(obj);
                    cell.setCellStyle(stringStyle);
                }
 
            }
            */
            for (int j = 0; j < this.headers.length; j++) {
                String[] headerT=this.headers[j];
                if(headerT.length>2){
                	String _strV = listCells.get(headerT[0]);
                	String[] _split = _strV.split("-");
                    for(int k=1;k<headerT.length;k++){
                    	if(k==1) {
                    	    String obj = Integer.valueOf(_split[0]) == 0 ? "" : _split[0];
                            cell = (SXSSFCell) row.createCell(realLen);
                            realLen++;
                            cell.setCellValue(obj);
                            cell.setCellStyle(stringStyle);
                    	}else if(k==2) {
                    		 String obj = Double.valueOf(_split[1]) <= 0 ? "" : _split[1];
                            cell = (SXSSFCell) row.createCell(realLen);
                            realLen++;
                            cell.setCellValue(obj);
                            cell.setCellStyle(stringStyle);
                    	}
                    }
                }else{
                    String obj = listCells.get(this.headers[j][1]) == null ? "" : String.valueOf(listCells.get(this.headers[j][1]));
                    cell = (SXSSFCell) row.createCell(j);
                    realLen++;
                    cell.setCellValue(obj);
                    cell.setCellStyle(stringStyle);
                }
 
            }
            currentRow++;
        }
       // sheet.addMergedRegion(new CellRangeAddress(3, 5, 0, 0)); // 合并单元格
        //合并列
        if(mergeLines!=null){
            System.out.println("合并列==========================");
            for(int  i=0 ;i<mergeLines.length;i++){
               int line= mergeLines[i];
                sheet.addMergedRegion(new CellRangeAddress(3, listRows.size()+2, line, line)); // 合并单元格
            }
        }
 
        try {
            workbook.write(out);
            out.flush();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
 
    /**
     * 导出
     *
     * @throws IOException
     * @throws ParseException
     */
    public void exportData(List<Map<String, String>> listRows, OutputStream out)
            throws IOException, ParseException {
        ExportDataNew exportData = new ExportDataNew(this.headers,this.sheetTitle,this.mergeLines);
        exportData.poiWriteExcelTo2007(listRows, out);
    }
 
 
 
    /**
     * 导出
     *
     * @throws IOException
     * @throws ParseException
     */
    public void exportData(String fileName, List<Map<String, String>> listRows, HttpServletResponse response)
            throws IOException, ParseException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment;Filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));
        exportData(listRows, response.getOutputStream());
    }
 
 
 
    public static void main(String[] args) throws IOException, ParseException {
    	
    	
    	
//        List<String> row1 = CollUtil.newArrayList("aa", "", "", "", "ee", "ff");
//        List<String> row2 = CollUtil.newArrayList("", "bb1", "", "", "ee1", "ff1");
//        List<String> row3 = CollUtil.newArrayList("", "", "cc2", "", "ee2", "ff2");
//        List<String> row4 = CollUtil.newArrayList("aa3", "", "", "", "ee3", "ff3");
//        List<String> row5 = CollUtil.newArrayList("", "", "", "bb4", "ee4", "ff4");
//        List<List<String>> rows = CollUtil.newArrayList(row1, row2, row3, row4, row5);
//
//        //通过工具类创建writer
//        ExcelWriter writer = ExcelUtil.getWriter("D:\\test\\writeTest1.xlsx");
//        //通过构造方法创建writer
//        //ExcelWriter writer = new ExcelWriter("d:/writeTest.xls");
//
//        //跳过前2行
//        writer.passCurrentRow();
//        writer.passCurrentRow();
//
//        //合并单元格后的标题行，使用默认标题样式
//        writer.merge(0, 1, 0, 3, "表格1", true);
//        writer.merge(0, 1, 4, 4, "表格2", true);
//        writer.merge(0, 1, 5, 5, "表格3", true);
//        //一次性写出内容，强制输出标题
//        writer.write(rows, true);
//        //关闭writer，释放内存
//        writer.close();
//   

    	
        //定义一个对象数组！
        String[][] headers=new String[5][];
        headers[0]= new String[]{"日期", "DATE"};
        headers[1]= new String[]{"星期","DATE_STR"};
        //会议设备
        String[] eqment=new String[3];
        eqment[0]="白班";
        //会议物资
        String[] materials=new String[3];
        materials[0]="晚班";
        //会议服务
        String[] server=new String[3];
        server[0]="长白班";
        for(int i =0;i<2;i++){
                    eqment[i+1]="测试服务A"+(i+1);
                    materials[i+1]="测试服务B"+(i+1);
                    server[i+1]="测试服务C"+(i+1);
        }
        headers[2]= eqment;
        headers[3]= materials;
        headers[4]= server;
        //数据集合
        List<Map<String,String>> listMap =new ArrayList<Map<String,String>>();
        for (int i=0;i<5;i++){
            Map<String,String> map= new HashMap<String,String>();
            map.put("TIME","vo.getTime()");
            map.put("DATE","vo.getDate()");
            map.put("DATE_STR","vo.getDateStr()");
            map.put("IS_TRUE","vo.getIsTrue()");
            map.put("LEADERS","vo.getLeaders()");
            map.put("LEVELS","vo.getLevels()");
            map.put("MEETING_RESERVATION","vo.getMeetingReservation()");
            map.put("MOBILE","vo.getMobile()");
            map.put("THEME","vo.getTheme()");
            map.put("NUM","vo.getNum().toString()");
            map.put("RESERVE_DEPT_NAME","vo.getReserveDeptName()");
            map.put("NAME","vo.getName()");
            //二级表头数据赋值
            for(int j=0;j<eqment.length;j++){
                map.put(eqment[j],"1");
                map.put(materials[j],"2"); 
                map.put(server[j],"3"); 
            }
            listMap.add(map);
        }
 
        //指定要合并的列，全部合并
//        Integer[] mergeLine={0,1};
        Integer[] mergeLine={};
    OutputStream out = new FileOutputStream("D:\\test\\test.xlsx");
        ExportDataNew exportDataNew = new ExportDataNew(headers,"接待中心每日会议汇总表",mergeLine);
        exportDataNew.exportData(listMap,out);
    }
}
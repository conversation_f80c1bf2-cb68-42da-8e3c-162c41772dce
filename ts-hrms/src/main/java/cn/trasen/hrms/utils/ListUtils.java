package cn.trasen.hrms.utils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

public class ListUtils {

	/**
     * @Description: 计算列表aList相对于bList的增加的情况，兼容任何类型元素的列表数据结构
     * @param aList 本列表
     * @param bList 对照列表
     * @return 返回增加的元素组成的列表
     */
    public static <E> List<E> getAddaListThanbList(List<E> aList, List<E> bList){
        List<E> addList = new ArrayList<E>();
        for (int i = 0; i < aList.size(); i++){
            if(!myListContains(bList, aList.get(i))){
                addList.add(aList.get(i));
            }
        }
        return addList;
    }
    
    /**
     * @Description: 计算列表aList相对于bList的减少的情况，兼容任何类型元素的列表数据结构
     * @param aList 本列表
     * @param bList 对照列表
     * @return 返回减少的元素组成的列表
     */
    public static <E> List<E> getReduceaListThanbList(List<E> aList, List<E> bList){
        List<E> reduceaList = new ArrayList<E>();
        for (int i = 0; i < bList.size(); i++){
            if(!myListContains(aList, bList.get(i))){
                reduceaList.add(bList.get(i));
            }
        }
        return reduceaList;
    }
    
    
    /**
     * @Description: 判断元素element是否是sourceList列表中的一个子元素
     * @param sourceList 源列表
     * @param element 待判断的包含元素
     * @return 包含返回 true，不包含返回 false
     */
    private static <E> boolean myListContains(List<E> sourceList, E element) {
        if (sourceList == null || element == null){
            return false;
        }
        if (sourceList.isEmpty()){
            return false;
        }
        for (E tip : sourceList){
            if(element.equals(tip)){
                return true;
            }
        }
        return false;
    }
    
    /**
     * @Description: 去除list重复数据
     * @param list
     * @return list
     */
    public static <E> List<E> cleanDisRepet(List<E> list){
    	 HashSet h = new HashSet(list);   
    	 list.clear();   
    	 list.addAll(h); 
		return list;
    }
    
    /**
     * @Description: 
     * @param key 排序key
     * @param ascending true 升序 false 降序
     * @return list
     */
    public static Comparator<Map<String, Object>> createComparator(String key, Comparator<Object> valueComparator, boolean ascending) {
		return (map1, map2) -> {
			Object v1 = map1.get(key);
			Object v2 = map2.get(key);
			
			int result = valueComparator.compare(v1, v2);
			return ascending ? result : -result;
		};
	}
	
}

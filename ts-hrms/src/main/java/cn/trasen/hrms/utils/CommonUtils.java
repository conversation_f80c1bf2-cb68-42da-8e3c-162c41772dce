package cn.trasen.hrms.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

import org.apache.commons.lang3.StringUtils;

import cn.trasen.homs.core.utils.StringUtil;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

public class CommonUtils {

	/**
	 * 
	 * @Title: getTemplateText   
	 * @Description: 模板变量值替换
	 * @param: @param map
	 * @param: @param template
	 * @param: @return      
	 * @return: String  
	 * @author: YueC
	 * @date:   2020年4月15日 上午11:39:00    
	 * @throws
	 */
	public static String getTemplateText(Map<String, Object> map, String template) {
        String text = template;
        //遍历Map中的所有Key，将得到的value值替换模板字符串中的变量值
        Set<String> keys = map.keySet();
        for (Iterator<String> it = keys.iterator(); it.hasNext();) {
            String key = it.next();
            text = text.replace("{" + key + "}", (String) map.get(key));
        }
        return text;
    }
	
	/**
     * 将一个List均分成n个list,主要通过偏移量来实现的
     *
     * @param source 源集合
     * @param limit 最大值
     * @return
     */
    public static <T> List<List<T>> averageAssign(List<T> source, int limit) {
        if (null == source || source.isEmpty()) {
            return Collections.emptyList();
        }
        List<List<T>> result = new ArrayList<>();
        int listCount = (source.size() - 1) / limit + 1;
        int remaider = source.size() % listCount; // (先计算出余数)
        int number = source.size() / listCount; // 然后是商
        int offset = 0;// 偏移量
        for (int i = 0; i < listCount; i++) {
            List<T> value;
            if (remaider > 0) {
                value = source.subList(i * number + offset, (i + 1) * number + offset + 1);
                remaider--;
                offset++;
            } else {
                value = source.subList(i * number + offset, (i + 1) * number + offset);
            }
            result.add(value);
        }
        return result;
    }
    
    /**
     * 
     * @Title: replaceSymbol   
     * @Description: 添加权限边界符号
     * @param: @param str
     * @param: @return      
     * @return: String  
     * @author: YueC
     * @date:   2020年4月18日 下午2:25:34    
     * @throws
     */
    public static String replaceSymbol(String str) {
    	String[] array = str.split(",");
		StringBuffer replaceStr = new StringBuffer();
		for (String string : array) {
			replaceStr.append("|").append(string).append("|").append(",");
		}
		return replaceStr.deleteCharAt(replaceStr.length()-1).toString();
    }
    
    /**
     * 字符串是否包含中文
     *
     * @param str 待校验字符串
     * @return true 包含中文字符 false 不包含中文字符
     * @throws EmptyException
     */
    public static boolean isContainChinese(String str){

        if (StringUtils.isEmpty(str)) {
           return false;
        }
        Pattern p = Pattern.compile("[\u4E00-\u9FA5|\\！|\\，|\\。|\\（|\\）|\\《|\\》|\\“|\\”|\\？|\\：|\\；|\\【|\\】]");
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        }
        return false;
    }
    
    /**
     * 字符串转换为日期:不支持yyM[M]d[d]格式
     * 
     * @param date
     * @return
     */
    public static final Date stringToDate(String date) {
        if (StringUtil.isEmpty(date)) {
            return null;
        }
        String separator = String.valueOf(date.charAt(4));
        String pattern = "yyyyMMdd";
        if (!separator.matches("\\d*")) {
            pattern = "yyyy" + separator + "MM" + separator + "dd";
            if (date.length() < 10) {
                pattern = "yyyy" + separator + "M" + separator + "d";
            }
        } else if (date.length() < 8) {
            pattern = "yyyyMd";
        }
        pattern += " HH:mm:ss.SSS";
        pattern = pattern.substring(0, Math.min(pattern.length(), date.length()));
        try {
            return new SimpleDateFormat(pattern).parse(date);
        } catch (ParseException e) {
            return null;
        }
    }
    
    /**
     * 
     * @Title: delHTMLTag   
     * @Description: 过滤掉html标签  
     * @param: @param htmlStr
     * @param: @return      
     * @return: String  
     * @author: Yuec    
     * @date:   2021年5月21日 下午2:31:59       
     * @throws
     */
    public static String delHTMLTag(String htmlStr){ 
        String regEx_script="<script[^>]*?>[\\s\\S]*?<\\/script>"; //定义script的正则表达式 
        String regEx_style="<style[^>]*?>[\\s\\S]*?<\\/style>"; //定义style的正则表达式 
        String regEx_html="<[^>]+>"; //定义HTML标签的正则表达式 
         
        Pattern p_script=Pattern.compile(regEx_script,Pattern.CASE_INSENSITIVE); 
        Matcher m_script=p_script.matcher(htmlStr); 
        htmlStr=m_script.replaceAll(""); //过滤  
         
        Pattern p_style=Pattern.compile(regEx_style,Pattern.CASE_INSENSITIVE); 
        Matcher m_style=p_style.matcher(htmlStr); 
        htmlStr=m_style.replaceAll(""); //过滤style标签 
         
        Pattern p_html=Pattern.compile(regEx_html,Pattern.CASE_INSENSITIVE); 
        Matcher m_html=p_html.matcher(htmlStr); 
        htmlStr=m_html.replaceAll(""); //过滤html标签 

        return htmlStr.trim(); //返回文本字符串 
    } 
    
    /**
     * 
     * @Title: StringFilter   
     * @Description: TODO(描述这个方法的作用)   
     * @param: @param str
     * @param: @return
     * @param: @throws PatternSyntaxException      
     * @return: String  
     * @author: Yuec    
     * @date:   2021年5月30日 下午4:48:13       
     * @throws
     */
    public static String stringFilter(String str) throws PatternSyntaxException {
    	// 只允许字母和数字 // String regEx ="[^a-zA-Z0-9]";
    	// 清除掉所有特殊字符
    	String regEx="[^\\x{4e00}-\\x{9fa5}0-9a-zA-Z]";
    	Pattern p = Pattern.compile(regEx);
    	Matcher m = p.matcher(str);
    	return m.replaceAll("").trim();
    }
    
    /**
     * 
     * @Title: toFirstChar
     * @Description: 汉字转简拼
     * @param @param chinese
     * @param @return 参数
     * @return String 返回类型
     * 2021年10月18日
     * ADMIN
     * @throws
     */
    public static String toFirstChar(String chinese){
        String pinyinStr = "";
        char[] newChar = chinese.toCharArray();  //转为单个字符
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        for (int i = 0; i < newChar.length; i++) {
            if (newChar[i] > 128) {
                try {
                    pinyinStr += PinyinHelper.toHanyuPinyinStringArray(newChar[i], defaultFormat)[0].charAt(0);
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    e.printStackTrace();
                }
            }else{
                pinyinStr += newChar[i];
            }
        }
        return pinyinStr;
    }

    /**
     * 
     * @Title: toPinyin
     * @Description:汉字转为拼音
     * @param @param chinese
     * @param @return 参数
     * @return String 返回类型
     * 2021年10月18日
     * ADMIN
     * @throws
     */
    public static String toPinyin(String chinese){
        String pinyinStr = "";
        char[] newChar = chinese.toCharArray();
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        for (int i = 0; i < newChar.length; i++) {
            if (newChar[i] > 128) {
                try {
                    pinyinStr += PinyinHelper.toHanyuPinyinStringArray(newChar[i], defaultFormat)[0];
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    e.printStackTrace();
                }
            }else{
                pinyinStr += newChar[i];
            }
        }
        return pinyinStr;
    }
    
    
}

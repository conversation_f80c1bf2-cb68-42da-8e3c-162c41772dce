package cn.trasen.hrms.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
/**
 * 
 -- =============================================
 --文件说明：List<Map<String,Object>> 处理工具类
 --类名称: LmsoUtils
 --创建时间：2024年10月31日 
 --作者：GW
 -- =============================================
 */
public class LmsoUtils {
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 按指定的字段名称比较大小
	  -- 作者: GW
	  -- 创建时间: 2024年10月30日
	  -- @param map1
	  -- @param map2
	  -- @return
	  -- =============================================
	 */
    public static int compareMapByfiledName(String filedName,Map<String, Object> map1, Map<String, Object> map2) {
    	if(StrUtil.isEmpty(filedName)){
    		throw new IllegalArgumentException("filedName is null");
    	}
        Object filed1 = map1.get(filedName);
        Object filed2 = map2.get(filedName);
        
        if(filed1 == null){
        	filed1 = 0;
        }
        if(filed2 == null){
        	filed2 = 0;
        }
        if (filed1 instanceof Integer && filed2 instanceof Integer) {
            return ((Integer) filed1).compareTo((Integer) filed2);
        } else if (filed1 instanceof Number && filed2 instanceof Number) {
            return Double.compare(((Number) filed1).doubleValue(), ((Number) filed2).doubleValue());
        } else {
            throw new IllegalArgumentException("Invalid filedName value types: " + filed1.getClass() + " and " + filed2.getClass());
        }
    }
    /**
     * 
      -- =============================================
      -- 功能描述: 将List<Map<String, Object>> 按指定的字段进行倒序排列
      -- 作者: GW
      -- 创建时间: 2024年10月30日
      -- @param filedName
      -- @param list
      -- @return
      -- =============================================
     */
    public static List<Map<String, Object>> bubbleSortByCnt(String filedName,List<Map<String, Object>> list) {
    	if(StrUtil.isEmpty(filedName)){
    		throw new IllegalArgumentException("filedName is null");
    	}
    	if(CollUtil.isNotEmpty(list)){
    		int n = list.size();
    		boolean swapped;
    		for (int i = 0; i < n - 1; i++) {
    			swapped = false;
    			for (int j = 0; j < n - 1 - i; j++) {
    				Map<String, Object> current = list.get(j);
    				Map<String, Object> next = list.get(j + 1);
    				
    				// 比较 age 字段
    				if (compareMapByfiledName(filedName,current, next) < 0) {
    					// 交换位置
    					Collections.swap(list, j, j + 1);
    					swapped = true;
    				}
    			}
    			
    			// 如果没有发生交换，则说明已经排好序
    			if (!swapped) {
    				break;
    			}
    		}
    	}
        return list;
    }
    
    /**
     * 
      -- =============================================
      -- 功能描述: Map<String,Object>  --> List<ExcelExportEntity>
      -- 作者: GW
      -- 创建时间: 2024年10月31日
      -- @param filedName
      -- @param list
      -- @return
      -- =============================================
     */
    public static List<ExcelExportEntity> getExcelExportEntityByObj(Object obj) {
    	List<ExcelExportEntity> ls = new ArrayList<ExcelExportEntity>();
    	if(ObjectUtil.isNotEmpty(obj)){
    		if(obj instanceof Map){
    			Map<String,Object> map = Convert.toMap(String.class, Object.class, obj);
    			map.forEach((k, v) -> {
    				ExcelExportEntity group = new ExcelExportEntity("ageFileds".equals(k) ? "年龄层次" 
    						: "genderFileds".equals(k) ? "性别分布" 
    								: "educationFileds".equals(k) ? "学历分布" : "职称分布" , k); 
    				List<Map<Object,Object>> fileds = (List<Map<Object,Object>>)v;
    				if(CollUtil.isNotEmpty(fileds)){
    					List<ExcelExportEntity> entitys = new ArrayList<>();
    					fileds.forEach(i -> {
    						entitys.add(new ExcelExportEntity(Convert.toStr(i.get("item_name")) , Convert.toStr(i.get("item_code")) ));
    						group.setList(entitys);
    					});
    				}
    				ls.add(group);
    			});
    		}
    	}
    	return ls;
    }
}

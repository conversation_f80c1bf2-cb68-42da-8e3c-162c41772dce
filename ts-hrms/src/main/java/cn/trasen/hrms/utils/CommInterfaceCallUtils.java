package cn.trasen.hrms.utils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.hrms.interfaceRegister.model.CommInterfaceLogs;
import cn.trasen.hrms.interfaceRegister.model.CommInterfaceRegister;
import cn.trasen.hrms.med.qua.model.QuaAuthCfg;
import tk.mybatis.mapper.entity.Example;

public class CommInterfaceCallUtils {

	
	
}

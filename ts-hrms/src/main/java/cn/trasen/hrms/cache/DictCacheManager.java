package cn.trasen.hrms.cache;

import cn.trasen.hrms.model.HrmsDictInfo;
import org.apache.commons.collections.CollectionUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Title: DictCacheManager.java 
 * @Package cn.trasen.hrms.cache 
 * @Description: 数据字典缓存类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月16日 下午1:45:42 
 * @version V1.0   
 */
public class DictCacheManager {

	private static final Map<String, List<HrmsDictInfo>> DICT_CACHE_MANAGER = new ConcurrentHashMap<String, List<HrmsDictInfo>>();

	private DictCacheManager() {

	}

	/**
	 * @Title: init
	 * @Description: 初始化
	 * @param records
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年4月16日 下午1:49:35
	 */
	public static void init(List<HrmsDictInfo> records) {
		if (CollectionUtils.isNotEmpty(records)) {
			DICT_CACHE_MANAGER.clear();
			List<HrmsDictInfo> list = null;
			for (HrmsDictInfo info : records) {
				if (DICT_CACHE_MANAGER.containsKey(info.getDictType())) {
					list = DICT_CACHE_MANAGER.get(info.getDictType());
					list.add(info);
				} else {
					list = new LinkedList<>();
					list.add(info);
					DICT_CACHE_MANAGER.put(info.getDictType(), list);
				}
			}
		}
	}

	/**
	 * @Title: getByKey
	 * @Description: 根据Key获取数据
	 * @param key
	 * @Return List<HrmsDictInfo>
	 * <AUTHOR>
	 * @date 2020年4月16日 下午1:48:56
	 */
	public static List<HrmsDictInfo> getByKey(String key) {
		return DICT_CACHE_MANAGER.get(key);
	}

	/**
	 * @Title: set
	 * @Description: 根据字典类型设置缓存
	 * @param key
	 * @param records
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年4月16日 下午1:48:27
	 */
	public static void set(String key, List<HrmsDictInfo> records) {
		DICT_CACHE_MANAGER.put(key, records);
	}

}

package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.contants.CommonContants;
import cn.trasen.hrms.dao.HrmsAttendanceStatisticsMapper;
import cn.trasen.hrms.model.HrmsAttendanceRecord;
import cn.trasen.hrms.model.HrmsAttendanceStatistics;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsSalaryItem;
import cn.trasen.hrms.service.HrmsAttendanceRecordService;
import cn.trasen.hrms.service.HrmsAttendanceStatisticsService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.service.HrmsSalaryItemService;
import cn.trasen.hrms.utils.DateUtils;

/**   
 * @Title: HrmsAttendanceStatisticsServiceImpl.java
 * @Package cn.trasen.hrms.service.impl
 * @Description: 考勤统计记录 业务层接口
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司
 * @date 2020年5月15日 上午10:08:46
 * @version V1.0
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsAttendanceStatisticsServiceImpl implements HrmsAttendanceStatisticsService {

	@Autowired
	HrmsAttendanceStatisticsMapper hrmsAttendanceStatisticsMapper;
	@Autowired
	HrmsAttendanceRecordService hrmsAttendanceRecordService;
	@Autowired
	HrmsSalaryItemService hrmsSalaryItemService;
	@Autowired
	HrmsEmployeeService hrmsEmployeeService;

	/**
	 * @Title: insert
	 * @Description: 保存考勤统计记录
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年5月15日 上午10:11:54
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsAttendanceStatistics entity) {
//		entity.setAttendanceStatisticsId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		return hrmsAttendanceStatisticsMapper.insert(entity);
	}

	/**
	 * @Title: getEmployeeStatisticsList
	 * @Description: 查询员工考勤明细列表
	 * @param entity
	 * @Return List<HrmsAttendanceStatistics>
	 * <AUTHOR> @date 2020年5月15日 下午5:02:29
	 */
	@Override
	public List<HrmsAttendanceStatistics> getEmployeeStatisticsList(HrmsAttendanceStatistics entity) {
		return hrmsAttendanceStatisticsMapper.getEmployeeStatisticsList(entity);
	}

	/**
	 * @Title: importStatisticsByExcel
	 * @Description: excel导入考勤统计记录
	 * @param list
	 * @Return PlatformResult<String>
	 * <AUTHOR>
	 * @date 2020年5月19日 下午11:58:52
	 */
	@Override
	@Transactional(readOnly = false)
	public PlatformResult<String> importStatisticsByExcel(List<List<String>> list) {
		if (CollectionUtils.isNotEmpty(list) && list.size() > 1) { // 第一行是表头
			List<String> headerList = list.get(0); // 取出表头，将表头动态字段根据名称转换成ID
			if (CollectionUtils.isNotEmpty(headerList)) {
				// 查询所有的考勤项目
				HrmsSalaryItem salaryItemEntity = new HrmsSalaryItem();
				salaryItemEntity.setDataCategory(CommonContants.DATA_CATEGORY_ATTENDANCE);
				List<HrmsSalaryItem> attendanceItemList = hrmsSalaryItemService.getList(salaryItemEntity);
				if (CollectionUtils.isNotEmpty(attendanceItemList)) {
					Map<String, String> attendanceItemMap = attendanceItemList.stream().collect(Collectors.toMap(HrmsSalaryItem::getSalaryItemName, HrmsSalaryItem::getSalaryItemId));
					// 将头部集合中，考勤项目的名称转换成ID
					for (int i = 2; i < headerList.size(); i++) {
						String head = headerList.get(i);
						headerList.set(i, attendanceItemMap.get(head));
					}
				}
			}

			String attendanceDate = new DateTime().plusMonths(-1).toString("yyyy-MM"); // 导入默认为上一个月的日期
			List<HrmsAttendanceRecord> saveAttendanceRecords = Lists.newArrayList(); // 要保存的考勤记录列表
			List<HrmsAttendanceStatistics> saveAttendanceStatistics = Lists.newArrayList(); // 要保存的考勤统计记录列表

			list.stream().skip(1).forEach(item -> {
				List<String> currRows = item;
				HrmsEmployee employee = hrmsEmployeeService.findByEmployeeNo(currRows.get(0));
				String attendanceRecordId = String.valueOf(IdWork.id.nextId()); // 考勤记录主键ID

				// 生成考勤记录表的数据
				HrmsAttendanceRecord attendanceRecord = new HrmsAttendanceRecord();
				attendanceRecord.setAttendanceRecordId(attendanceRecordId);
				attendanceRecord.setAttendanceDate(attendanceDate);
				attendanceRecord.setEmployeeId(employee.getEmployeeId());
				attendanceRecord.setEmployeeNo(employee.getEmployeeNo());
				attendanceRecord.setOrgId(employee.getOrgId());
				attendanceRecord.setOrgName(employee.getOrgName());
				attendanceRecord.setIsDeleted(Contants.IS_DELETED_FALSE);
				attendanceRecord.setCreateUser(UserInfoHolder.getCurrentUserCode());
				attendanceRecord.setCreateUserName(UserInfoHolder.getCurrentUserName());
				attendanceRecord.setCreateDate(new Date());
				saveAttendanceRecords.add(attendanceRecord);

				for (int i = 2; i < currRows.size(); i++) {
					// 生成考勤统计记录表的数据
					HrmsAttendanceStatistics statistics = new HrmsAttendanceStatistics();
					statistics.setAttendanceStatisticsId(String.valueOf(IdWork.id.nextId()));
					statistics.setAttendanceRecordId(attendanceRecordId);
					statistics.setEmployeeId(employee.getEmployeeId());
					statistics.setAttendanceItemId(headerList.get(i)); // 项目ID
					statistics.setAttendanceDays(Long.valueOf(currRows.get(i))); // 对应的考勤天数
					statistics.setIsDeleted(Contants.IS_DELETED_FALSE);
					statistics.setCreateUser(UserInfoHolder.getCurrentUserCode());
					statistics.setCreateUserName(UserInfoHolder.getCurrentUserName());
					statistics.setCreateDate(new Date());
					statistics.setOrgId(employee.getOrgId());
					statistics.setOrgName(employee.getOrgName());
					saveAttendanceStatistics.add(statistics);
				}
			});
			// 批量保存数据
			hrmsAttendanceRecordService.batchInsert(saveAttendanceRecords);
			batchProcess(saveAttendanceStatistics);
		}
		return PlatformResult.success();
	}
	
	/**
	 * 防止数据太大时内存溢出，每次保存2000条记录
	 * @Title: batchProcess
	 * @Description: 分页批量插入数据
	 * @param list
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年4月9日 下午4:43:13
	 */
	private void batchProcess(List<HrmsAttendanceStatistics> list) {
		int totalSize = list.size(); // 总记录数
		int pageSize = 2000; // 每次新增的记录数
		int totalPage = totalSize / pageSize; // 总页数
		if (totalSize % pageSize != 0) {
			totalPage += 1;
			if (totalSize < pageSize) {
				pageSize = totalSize;
			}
		}
		for (int i = 1; i < totalPage + 1; i++) {
			int start = (i - 1) * pageSize;
			int end = i * pageSize > totalSize ? (totalSize) : i * pageSize;
			hrmsAttendanceStatisticsMapper.batchInsert(list.subList(start, end));
		}
	}

	/**   
	 * <p>Title: insertStatistics</p>   
	 * <p>Description:生成统计数据 </p>   
	 * @param date   
	 */ 
	@Override
	@Transactional
	public void insertStatistics(String date) {
		Map<String, Object> param = new HashMap<>();
		param.put("date", date);
		param.put("createUser", UserInfoHolder.getCurrentUserCode());
		param.put("createUserName", UserInfoHolder.getCurrentUserName());
		param.put("createDate", new Date());
		HrmsAttendanceStatistics record = new HrmsAttendanceStatistics();
		record.setCountDate(date);
		hrmsAttendanceStatisticsMapper.delete(record);  //删除之前的统计
		hrmsAttendanceStatisticsMapper.insertStatistics(param);  //重新统计
	}

	@Override
	public int deleteByParam(Map<String, Object> map) {
		return hrmsAttendanceStatisticsMapper.deleteByParam(map);
	}

	@Override
	public int insertByParam(Map<String, Object> map) {
		return hrmsAttendanceStatisticsMapper.insertByParam(map);
	}
	
	//科室审核
	@Override
	public List<HrmsAttendanceStatistics> getDeptAuditStatisticsList(HrmsAttendanceStatistics entity) {
		return hrmsAttendanceStatisticsMapper.getDeptAuditStatisticsList(entity);
	}

	@Override
	public List<HrmsAttendanceStatistics> getAllDayStatisticsList(HrmsAttendanceStatistics entity) {
		return hrmsAttendanceStatisticsMapper.getAllDayStatisticsList(entity);
	}

	@Override
	public List<HrmsAttendanceStatistics> getPersonnelSpecialDayStatisticsList(
			HrmsAttendanceStatistics entity) {
		return hrmsAttendanceStatisticsMapper.getPersonnelSpecialDayStatisticsList(entity);
	}
	
	//全勤统计
	@Override
	public List<HrmsAttendanceStatistics> getStatementAllList(HrmsAttendanceStatistics entity) {
		return hrmsAttendanceStatisticsMapper.getStatementAllList(entity);
	}

	@Override
	public List<HrmsAttendanceStatistics> getStatementSpecialList(HrmsAttendanceStatistics entity) {
		return hrmsAttendanceStatisticsMapper.getStatementSpecialList(entity);
	}

	/**   
	 * <p>Title: getlastmonthpeople</p>   
	 * <p>Description: 统计三个月的</p>   
	 * @return   
	 */
	@Override
	public Map<String, Integer> getlastmonthpeople() {
		Map<String, Integer>  returnMap = new HashMap<>();
		Map<String, String>  paramMap = new HashMap<>();
		String lastMonth = DateUtils.getLastMonth();
		paramMap.put("employeeId", UserInfoHolder.getCurrentUserId());
		paramMap.put("date", lastMonth);
		//获取上月考勤
		Integer re1 = hrmsAttendanceStatisticsMapper.getEmployeeIdAttendance(paramMap);
		Integer re3 = hrmsAttendanceStatisticsMapper.getEmployeeIdAttendanceLeave(paramMap);
		paramMap.put("date", lastMonth.split("-")[0]);
		//获取全年
		Integer re2 = hrmsAttendanceStatisticsMapper.getEmployeeIdAttendance(paramMap);
		returnMap.put("month", re1);
		returnMap.put("year",re2);
		returnMap.put("leave",re3);
		return returnMap;
	}

}

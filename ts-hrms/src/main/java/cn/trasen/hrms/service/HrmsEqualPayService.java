package cn.trasen.hrms.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.model.HrmsEqualPay;

/**
 * @ClassName HrmsEqualPayService
 * @Description TODO
 * @date 2021��9��9�� ����2:26:05
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsEqualPayService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2021��9��9�� ����2:26:05
	 * <AUTHOR>
	 */
	Integer save(HrmsEqualPay record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2021��9��9�� ����2:26:05
	 * <AUTHOR>
	 */
	Integer update(HrmsEqualPay record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2021��9��9�� ����2:26:05
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsEqualPay
	 * @date 2021��9��9�� ����2:26:05
	 * <AUTHOR>
	 */
	HrmsEqualPay selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsEqualPay>
	 * @date 2021��9��9�� ����2:26:05
	 * <AUTHOR>
	 */
	List<HrmsEqualPay> getDataSetList(Page page, HrmsEqualPay record);
	
	/**
	 * 
	* @Title: synEmployeeMessage  
	* @Description: 同工同酬流程审批后数据同步
	* @Params: @param dataMap      
	* @Return: void
	* <AUTHOR>
	* @date:2021年9月9日
	* @Throws
	 */
	void synEmployeeMessage(Map<String,Object> dataMap);
}

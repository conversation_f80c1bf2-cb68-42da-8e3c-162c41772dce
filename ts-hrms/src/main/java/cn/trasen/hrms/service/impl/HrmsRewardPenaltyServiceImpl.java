package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsRewardPenaltyMapper;
import cn.trasen.hrms.model.HrmsRewardPenalty;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsRewardPenaltyService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsRewardPenaltyServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 奖惩记录 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月22日 下午12:22:32 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsRewardPenaltyServiceImpl implements HrmsRewardPenaltyService {

	@Autowired
	HrmsRewardPenaltyMapper hrmsRewardPenaltyMapper;
	@Autowired
	HrmsDictInfoService hrmsDictInfoService;

	/**
	 * @Title: insert
	 * @Description: 新增奖惩记录
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsRewardPenalty entity) {
		entity.setId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsRewardPenaltyMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 更新奖惩记录
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsRewardPenalty entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsRewardPenaltyMapper.updateByPrimaryKeySelective(entity);
	}

	/**
	 * @Title: deleted
	 * @Description: 删除奖惩记录
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsRewardPenalty rewardPenalty = hrmsRewardPenaltyMapper.selectByPrimaryKey(id);
		if (rewardPenalty != null) {
			rewardPenalty.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsRewardPenaltyMapper.updateByPrimaryKeySelective(rewardPenalty);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取奖惩记录列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsRewardPenalty>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsRewardPenalty> getDataList(Page page, HrmsRewardPenalty entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");

		Example example = new Example(HrmsRewardPenalty.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		example.and().andEqualTo("employeeId", entity.getEmployeeId());

		if (StringUtils.isNotBlank(entity.getRewardPenaltyType())) { // 奖惩记录类型
			example.and().andEqualTo("rewardPenaltyType", entity.getRewardPenaltyType());
		}
		if (StringUtils.isNotBlank(entity.getRewardPenaltyTitle())) { // 奖惩记录标题
			example.and().andLike("rewardPenaltyTitle", "%" + entity.getRewardPenaltyTitle() + "%");
		}
		return hrmsRewardPenaltyMapper.selectByExampleAndRowBounds(example, page);
	}

	/**
	 * @Title: getList
	 * @Description: 查询奖惩记录列表(不分页)
	 * @param page
	 * @param entity
	 * @Return List<HrmsRewardPenalty>
	 * <AUTHOR>
	 * @date 2020年4月21日 下午4:42:46
	 */
	@Override
	public List<HrmsRewardPenalty> getList(HrmsRewardPenalty entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");

		Example example = new Example(HrmsRewardPenalty.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		example.and().andEqualTo("employeeId", entity.getEmployeeId());

		if (StringUtils.isNoneBlank(entity.getId())) {
			example.and().andEqualTo("rewardPenaltyId", entity.getId());
		}

		List<HrmsRewardPenalty> list = hrmsRewardPenaltyMapper.selectByExample(example);
		if (CollectionUtils.isNotEmpty(list)) {
			Map<String, String> rewardPenaltyTypeMap = hrmsDictInfoService.convertDictMap(DictContants.REWARD_PENALTY_TYPE); // 奖惩类型字典
			for (HrmsRewardPenalty rewardPenalty : list) {
				rewardPenalty.setRewardPenaltyTypeText(rewardPenaltyTypeMap.get(rewardPenalty.getRewardPenaltyType())); // 奖惩类型文本值
			}
		}
		return list;
	}

}

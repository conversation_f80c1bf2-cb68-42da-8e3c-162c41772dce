package cn.trasen.hrms.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsContractMapper;
import cn.trasen.hrms.enums.ContractOperationStatusEnum;
import cn.trasen.hrms.enums.ContractStatusEnum;
import cn.trasen.hrms.model.HrmsContract;
import cn.trasen.hrms.model.HrmsContractLog;
import cn.trasen.hrms.model.HrmsDocumentAccessory;
import cn.trasen.hrms.service.HrmsContractLogService;
import cn.trasen.hrms.service.HrmsContractService;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsDocumentAccessoryService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * 合同管理 接口实现类
 *
 */
@Slf4j
@Service
public class HrmsContractServiceImpl implements HrmsContractService {
	
	@Autowired
	HrmsEmployeeFeignService hrmsEmployeeFeignService;
	
	@Autowired
	private HrmsContractMapper hrmsContractMapper;

	@Autowired
	HrmsContractLogService hrmsContractLogService;
	
	@Autowired
	HrmsDictInfoService hrmsDictInfoService;
	
	@Autowired
	HrmsDocumentAccessoryService hrmsDocumentAccessoryService;
	
	@Autowired
	GlobalSettingsFeignService globalSettingsFeignService;

	/* (non-Javadoc)
	 * 添加合同
	 */
	@Override
	@Transactional
	public int insert(HrmsContract entity) throws Exception {
		
		if(!StringUtil.isEmpty(entity.getContractNumber())) {
			HrmsContract record = new HrmsContract();
			record.setContractNumber(entity.getContractNumber());
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			//验证是否已存在合同编号
			List<HrmsContract> listBean = hrmsContractMapper.select(record);
			if(listBean != null && listBean.size() > 0) {
				throw new RuntimeException("已存在相同的合同编号");
			}
		}
		
		//根据员工id获取员工信息
		PlatformResult<EmployeeResp> empObj = hrmsEmployeeFeignService.getEmployeeDetailByCode(entity.getEmployeeNo());
		EmployeeResp emp = empObj.getObject();
		entity.setEmployeeName(emp.getEmployeeName());
		entity.setEmployeeNo(emp.getEmployeeNo());
		entity.setCard(emp.getIdentityNumber());
		entity.setEmployeeId(emp.getEmployeeId());
		entity.setEmpOrgId(emp.getOrgId());
		entity.setEmpOrgName(emp.getOrgName());
		
		String businessId = IdUtil.getId();
		entity.setContractId(businessId);
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSignType(ContractStatusEnum.CONTRACT_STATUS_1.getKey());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsContractMapper.insertSelective(entity);//添加合同
		
		//添加附件信息表
		List<HrmsDocumentAccessory> accessoryList = entity.getAccessoryList();
		if(accessoryList != null && accessoryList.size() > 0) {
			for(int i = 0; i < accessoryList.size(); i++) {
				accessoryList.get(i).setBusinessId(businessId);
				hrmsDocumentAccessoryService.insert(accessoryList.get(i));
			}
		}
		
		HrmsContractLog log = new HrmsContractLog();
		log.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		log.setActionType(ContractOperationStatusEnum.OPERATION_STATUS_1.getVal());  
		log.setEmployeeNo(entity.getEmployeeNo());
		log.setEmployeeName(entity.getEmployeeName());
		log.setCard(entity.getCard());
		log.setContent("签订合同:"+entity.getContractNumber());
		hrmsContractLogService.insert(log);  //添加操作记录
		return 1;
	}



	@Override
	@Transactional
	public void update(HrmsContract entity) throws Exception {
	
		HrmsContract beforeContractNumberBean = hrmsContractMapper.selectByPrimaryKey(entity.getContractId());
		
		if("2".equals(entity.getAllotedTime())) {  //改为无固定期限  就去掉结束时间
			entity.setEndTime(null);
		}
		
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateDate(new Date());
		hrmsContractMapper.updateByPrimaryKeySelective(entity);
		
		
		hrmsDocumentAccessoryService.deleted(entity.getContractId());  	//重新处理附件
		List<HrmsDocumentAccessory> accessoryList = entity.getAccessoryList();
		if(accessoryList != null && accessoryList.size() > 0) {
			for(int i = 0; i < accessoryList.size(); i++) {
				accessoryList.get(i).setBusinessId(entity.getContractId());
				hrmsDocumentAccessoryService.insert(accessoryList.get(i));
			}
		}
		
		try {
			HrmsContractLog log = new HrmsContractLog();
			log.setActionType(ContractOperationStatusEnum.OPERATION_STATUS_2.getVal());
			log.setEmployeeNo(entity.getEmployeeNo());
			log.setEmployeeName(entity.getEmployeeName());
			log.setCard(entity.getCard());
			log.setContent(changeVal(beforeContractNumberBean, entity));
			hrmsContractLogService.insert(log); // 添加操作记录
		} catch (Exception e) {
			log.error("添加操作日志异常" + e.getMessage(), e);
		}
		
	}
	
	
	/**
	 * 验证改变的值
	 * @return
	 */
	private String changeVal(HrmsContract beforeContractNumberBean,HrmsContract entity) {
		StringBuilder sb = new StringBuilder("编辑合同：" +beforeContractNumberBean.getContractNumber());
		if(!beforeContractNumberBean.getContractNumber().equals(entity.getContractNumber())) {
			sb.append(",合同编号："+beforeContractNumberBean.getContractNumber() + "变更为："+ entity.getContractNumber());
		}
		if(!beforeContractNumberBean.getContractType().equals(entity.getContractType())) {
			sb.append(",合同类型："+beforeContractNumberBean.getContractType() + "变更为："+ entity.getContractType());
		}
		if(!beforeContractNumberBean.getSignTime().equals(entity.getSignTime())) {
			sb.append(",合同签订日期："+DateUtils.getStringDateShort(beforeContractNumberBean.getSignTime()) + "变更为："+ DateUtils.getStringDateShort(entity.getSignTime()));
		}
		if(!beforeContractNumberBean.getAllotedTime().equals(entity.getAllotedTime())) {
			sb.append(",合同期限："+beforeContractNumberBean.getAllotedTime() + "变更为："+ entity.getAllotedTime());
		}
		if(!beforeContractNumberBean.getStartTime().equals(entity.getStartTime())) {
			sb.append(",合同开始日期："+DateUtils.getStringDateShort(beforeContractNumberBean.getStartTime()) + "变更为："+ DateUtils.getStringDateShort(entity.getStartTime()));
		}
		if(!beforeContractNumberBean.getEndTime().equals(entity.getEndTime())) {
			sb.append(",合同结束日期："+DateUtils.getStringDateShort(beforeContractNumberBean.getEndTime()) + "变更为："+ DateUtils.getStringDateShort(entity.getEndTime()));
		}
		if(!beforeContractNumberBean.getFrequency().equals(entity.getFrequency())) {
			sb.append(",合同签订次数："+beforeContractNumberBean.getFrequency() + "变更为："+ entity.getFrequency());
		}
		if(null == beforeContractNumberBean.getContracJobs()) {
			sb.append(",约定岗位变更为："+ entity.getContracJobsName());
		}else {
			if(!beforeContractNumberBean.getContracJobsName().equals(entity.getContracJobsName())) {
				sb.append(",约定岗位："+beforeContractNumberBean.getContracJobsName() + "变更为："+ entity.getContracJobsName());
			}
		}
		if((StringUtil.isEmpty(beforeContractNumberBean.getArchiveSite())) && (!StringUtil.isEmpty(entity.getArchiveSite())) ) {
			sb.append(",存档地变更为："+ entity.getArchiveSite());
		}else {
			sb.append(",存档地："+beforeContractNumberBean.getArchiveSite() + "变更为："+ entity.getArchiveSite());
		}
		return sb.toString();
	}
	
	//续签
	@Override
	@Transactional
	public void renew(HrmsContract entity) throws Exception {
		
		// TODO 老合同改为已续签
		HrmsContract beforeContractNumberBean = hrmsContractMapper.selectByPrimaryKey(entity.getContractId());
		
		//根据员工id获取员工信息
		PlatformResult<EmployeeResp> empObj = hrmsEmployeeFeignService.getEmployeeDetailByCode(entity.getEmployeeNo());
		EmployeeResp emp = empObj.getObject();
		entity.setEmployeeName(emp.getEmployeeName());
		entity.setEmployeeNo(emp.getEmployeeNo());
		entity.setCard(emp.getIdentityNumber());
		entity.setEmployeeId(emp.getEmployeeId());
		entity.setEmpOrgId(emp.getOrgId());
		entity.setEmpOrgName(emp.getOrgName());
		
		String businessId = IdUtil.getId();
		entity.setContractId(businessId);
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSignType(ContractStatusEnum.CONTRACT_STATUS_1.getKey());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsContractMapper.insertSelective(entity);  //添加合同
		
		//原合同改为已续签
		beforeContractNumberBean.setSignType(ContractStatusEnum.CONTRACT_STATUS_2.getKey());
		beforeContractNumberBean.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		beforeContractNumberBean.setUpdateDate(new Date());
		hrmsContractMapper.updateByPrimaryKeySelective(beforeContractNumberBean);

		
		//添加附件信息表
		List<HrmsDocumentAccessory> accessoryList = entity.getAccessoryList();
		if(accessoryList != null && accessoryList.size() > 0) {
			for(int i = 0; i < accessoryList.size(); i++) {
				accessoryList.get(i).setBusinessId(businessId);
				hrmsDocumentAccessoryService.insert(accessoryList.get(i));
			}
		}
		
		HrmsContractLog log = new HrmsContractLog();
		log.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		log.setActionType(ContractOperationStatusEnum.OPERATION_STATUS_3.getVal());  
		log.setEmployeeNo(entity.getEmployeeNo());
		log.setEmployeeName(entity.getEmployeeName());
		log.setCard(entity.getCard());
		log.setContent("续签合同:"+entity.getContractNumber() + "原合同号："+entity.getBeforeContractNumber());
		hrmsContractLogService.insert(log);  //添加操作记录
		
	}

	//解除合同
	@Override
	@Transactional
	public void relieve(HrmsContract entity) throws Exception {
		
		entity.setSignType(ContractStatusEnum.CONTRACT_STATUS_4.getKey());
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateDate(new Date());
		hrmsContractMapper.updateByPrimaryKeySelective(entity);
		
		//添加附件信息表
		List<HrmsDocumentAccessory> accessoryList = entity.getAccessoryList();
		if(accessoryList != null && accessoryList.size() > 0) {
			for(int i = 0; i < accessoryList.size(); i++) {
				accessoryList.get(i).setBusinessId(entity.getContractId());
				accessoryList.get(i).setRelieve("1");
				hrmsDocumentAccessoryService.insert(accessoryList.get(i));
			}
		}
		
		HrmsContractLog log = new HrmsContractLog();
		log.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		log.setActionType(ContractOperationStatusEnum.OPERATION_STATUS_4.getVal());  
		log.setEmployeeNo(entity.getEmployeeNo());
		log.setEmployeeName(entity.getEmployeeName());
		log.setCard(entity.getCard());
		log.setContent("解除合同:"+entity.getContractNumber());
		hrmsContractLogService.insert(log);  //添加操作记录
		
	}

	//删除合同
	@Override
	@Transactional
	public void remove(HrmsContract entity) throws Exception {
		
		entity.setSignType(ContractStatusEnum.CONTRACT_STATUS_5.getKey());
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateDate(new Date());
		hrmsContractMapper.updateByPrimaryKeySelective(entity);
		
		HrmsContractLog log = new HrmsContractLog();
		log.setActionType(ContractOperationStatusEnum.OPERATION_STATUS_5.getVal());  
		log.setEmployeeNo(entity.getEmployeeNo());
		log.setEmployeeName(entity.getEmployeeName());
		log.setCard(entity.getCard());
		log.setContent("删除合同:"+entity.getContractNumber());
		log.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsContractLogService.insert(log);  //添加操作记录
		
	}
	

	/* (non-Javadoc)
	 * 员工合同列表
	 */
	@Override
	public List<HrmsContract> getEmployeeDataList(Page page, HrmsContract entity) {
		
		
		//经开医院排除8开头的账号
		PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getGlobalSetting("Y");
		String orgCode = globalSetting.getObject().getOrgCode();
		
		if("csjkyy".equals(orgCode)) {
			entity.setIsJk("1");
		}
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsContract> empAll =  hrmsContractMapper.getEmployeePageList(page,entity);
		List<String> empIdList = new ArrayList<>();
		empAll.forEach(item ->{
			empIdList.add(item.getEmployeeId());
		});
		entity.setEmpIdList(empIdList);
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsContract> contractList = hrmsContractMapper.getEmployeeContractList(entity);
		contractList.forEach(item ->{
			if(!"1".equals(item.getSignType())) {
				item.setContractNumber(item.getContractNumber()+"(" + ContractStatusEnum.getValByKey(item.getSignType()) + ")");
				item.setSignTypeName("未签订");
			}else {
				item.setSignTypeName("已签订");
			}
		});
		
		if(contractList != null && contractList.size() > 0) {
			Map<String, List<HrmsContract>> collect = contractList.stream()
			        .collect(Collectors.groupingBy(HrmsContract::getEmployeeId));			
			empAll.forEach(item ->{
				item.setContract(collect.get(item.getEmployeeId()));
			});
		}
		return empAll;
	}
	
	/* (non-Javadoc)
	 * 合同签订记录列表
	 */
	@Override
	public List<HrmsContract> getDataList(Page page, HrmsContract entity) {
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsContract> pageList = hrmsContractMapper.getPageList(page,entity);
		if(pageList != null && pageList.size() > 0) {
			for(HrmsContract hc : pageList) {  
				 //到期倒计时
				if("1".equals(hc.getAllotedTime())) {
					if(hc.getEndTime() != null) {
						hc.setCountDown(DateUtils.getDifferDays(new Date(), hc.getEndTime()) + "天");
					}else {
						hc.setCountDown("-");
					}
				}
				hc.setSignTypeName(ContractStatusEnum.getValByKey(hc.getSignType()));
				hc.setAccessoryList(hrmsDocumentAccessoryService.getDataListByBusinessId(hc.getContractId()));	//处理附件类型
			}
		}
		
		return pageList;
	}
	
	

	/* (non-Javadoc)
	 * 合同提醒列表
	 */
	@Override
	public List<HrmsContract> getRemindDataList(Page page, HrmsContract entity) {
		
		//entity.setStartExpireDate(DateUtils.getStringDateShort(DateUtils.getCurrentQuarterEndTime()));
		//改为从当前时间开始
		entity.setStartExpireDate(DateUtils.getStringDateShort(new Date()));
		entity.setEndExpireDate(DateUtils.getMonthDifferDate(DateUtils.getCurrentQuarterEndTime(),3));
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsContract> pageList = hrmsContractMapper.getRemindDataList(page,entity);
		if(pageList != null && pageList.size() > 0) {
			for(HrmsContract hc : pageList) {  
				 //到期倒计时
				if("1".equals(hc.getAllotedTime())) {
					if(hc.getEndTime() != null) {
						hc.setCountDown(DateUtils.getDifferDays(new Date(), hc.getEndTime()) + "天");
					}else {
						hc.setCountDown("-");
					}
				}
				hc.setSignTypeName(ContractStatusEnum.getValByKey(hc.getSignType()));
				hc.setAccessoryList(hrmsDocumentAccessoryService.getDataListByBusinessId(hc.getContractId()));	//处理附件类型
			}
		}
		
		return pageList;
	}
	

	//导入合同信息
	@Override
	@Transactional
	public PlatformResult<Map<String,Object>> excelImportContract(List<HrmsContract> list, String type) throws Exception {
		
		Map<String,Object> retMap = new HashMap<>();
		
		//验证重复合同编号
		Map<Object, Long> collectNumber = list.stream()
				.collect(Collectors.groupingBy(HrmsContract::getContractNumber, Collectors.counting()));
		// 筛出有重复的编号
		List<Object> number = collectNumber.keySet().stream().filter(key -> collectNumber.get(key) > 1)
				.collect(Collectors.toList());
		// 可以知道有哪些姓名有重复
		
		if(number != null && number.size() > 0) {
			throw new RuntimeException("要导入的数据中存在重合合同编号"+number.get(0));
		}
		
		//查询数据库是否有重复编号
		HrmsContract record = new HrmsContract();
		record.setIsDeleted(Contants.IS_DELETED_FALSE);
		record.setSignType(ContractStatusEnum.CONTRACT_STATUS_5.getKey());

		Example example = new Example(HrmsContract.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andNotEqualTo("signType", ContractStatusEnum.CONTRACT_STATUS_5.getKey());
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsContract> allList = hrmsContractMapper.selectByExample(example);
		
		
		Map<String, String> allMap = allList.stream()
				.collect(Collectors.toMap(HrmsContract::getContractNumber, HrmsContract::getContractId, (key1, key2) -> key2));

		
		if("2".equals(type)) {  //type:2：全量
			hrmsContractMapper.deleteAll();		//删除所有记录导入
		}
		
		List<String> empNoList = new ArrayList<>();
		//拿到所有工号
		list.stream().forEach(item ->{
			empNoList.add(item.getEmployeeNo());
		});
		PlatformResult<List<EmployeeResp>> empList = hrmsEmployeeFeignService.getEmployeeDetailByCodes(empNoList);
		List<EmployeeResp> empObj = empList.getObject();
		Map<String, EmployeeResp> empMap = new HashMap<String, EmployeeResp>();
		if(empObj != null && empObj.size() > 0) {
			empMap = empObj.stream()
					.collect(Collectors.toMap(EmployeeResp::getEmployeeNo, Function.identity(), (key1, key2) -> key2));
		}
		
		Map<String, String> contractTypeMap = hrmsDictInfoService.convertDictValueKeyMap(DictContants.LABOR_CONTRACT_TYPE);  //合同类型
		Map<String, String> personalIdentityMap = hrmsDictInfoService.convertDictValueKeyMap(DictContants.PERSONAL_IDENTITY);  //身份
		
		
		for (int i = 0; i < list.size(); i++) {
			
			if(allMap.containsKey(list.get(i).getContractNumber())) {
				throw new RuntimeException("工号："+list.get(i).getEmployeeNo() + "的合同编号'"+list.get(i).getContractNumber()+"'已存在！</br>");
			}
			
			if(empMap.get(list.get(i).getEmployeeNo()) == null ) {
				throw new RuntimeException("工号："+list.get(i).getEmployeeNo() + "在系统中找不到对应的人员信息</br>");
			}
			list.get(i).setContractId(IdUtil.getId());
			list.get(i).setIsDeleted(Contants.IS_DELETED_FALSE);
			list.get(i).setCreateUser(UserInfoHolder.getCurrentUserCode());
			list.get(i).setCreateUserName(UserInfoHolder.getCurrentUserName());
			list.get(i).setCreateDate(new Date());
			list.get(i).setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			// TODO 处理身份证号 名字 员工id
			
			if(!StringUtil.isEmpty(list.get(i).getSignTimeStr())) {  //合同签订日期
				try {
					SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
					Date date = simpleDateFormat1.parse(list.get(i).getSignTimeStr());
					list.get(i).setSignTime(date);
				} catch (Exception e) {
					try {
						SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy.MM.dd");//注意月份是MM
						Date date = simpleDateFormat2.parse(list.get(i).getSignTimeStr());
						list.get(i).setSignTime(date);
					} catch (Exception e1) {
						try {
							SimpleDateFormat simpleDateFormat3 = new SimpleDateFormat("yyyy/MM/dd");//注意月份是MM
							Date date = simpleDateFormat3.parse(list.get(i).getSignTimeStr());
							list.get(i).setSignTime(date);
						} catch (Exception e2) {
							throw new RuntimeException("" + list.get(i).getEmployeeName() + "合同签订日期格式不对");
						}
					}
				}
			}else {
				throw new RuntimeException(list.get(i).getEmployeeNo() + "合同签订日期不能为空");
			}
			
			if(!StringUtil.isEmpty(list.get(i).getStartTimeStr())) {  //合同开始日期
				try {
					SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
					Date date = simpleDateFormat1.parse(list.get(i).getStartTimeStr());
					list.get(i).setStartTime(date);
				} catch (Exception e) {
					try {
						SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy.MM.dd");//注意月份是MM
						Date date = simpleDateFormat2.parse(list.get(i).getStartTimeStr());
						list.get(i).setStartTime(date);
					} catch (Exception e1) {
						try {
							SimpleDateFormat simpleDateFormat3 = new SimpleDateFormat("yyyy/MM/dd");//注意月份是MM
							Date date = simpleDateFormat3.parse(list.get(i).getStartTimeStr());
							list.get(i).setStartTime(date);
						} catch (Exception e2) {
							throw new RuntimeException("" + list.get(i).getEmployeeName() + "合同开始日期格式不对");
						}
					}
				}
			}else {
				throw new RuntimeException(list.get(i).getEmployeeNo() + "合同开始日期不能为空");
			}
			
			if(!StringUtil.isEmpty(list.get(i).getEndTimeStr())) {  //合同结束日期
				try {
					SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
					Date date = simpleDateFormat1.parse(list.get(i).getEndTimeStr());
					list.get(i).setEndTime(date);
				} catch (Exception e) {
					try {
						SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy.MM.dd");//注意月份是MM
						Date date = simpleDateFormat2.parse(list.get(i).getEndTimeStr());
						list.get(i).setEndTime(date);
					} catch (Exception e1) {
						try {
							SimpleDateFormat simpleDateFormat3 = new SimpleDateFormat("yyyy/MM/dd");//注意月份是MM
							Date date = simpleDateFormat3.parse(list.get(i).getEndTimeStr());
							list.get(i).setEndTime(date);
						} catch (Exception e2) {
							throw new RuntimeException("" + list.get(i).getEmployeeName() + "合同结束日期格式不对");
						}
					}
				}
			}
			
			list.get(i).setEmployeeId(empMap.get(list.get(i).getEmployeeNo()).getEmployeeId());
			if(!StringUtil.isEmpty(list.get(i).getContractTypeName())) {
				list.get(i).setContractType(contractTypeMap.get(list.get(i).getContractTypeName().trim())); //合同类型字典值
			}else {
				throw new RuntimeException(list.get(i).getEmployeeNo() + "的合同类型不正确");
			}
			
			if("固定期限".equals(list.get(i).getAllotedTime())) {
				list.get(i).setAllotedTime("1");
			}else if("无固定期限".equals(list.get(i).getAllotedTime())){
				list.get(i).setAllotedTime("2");
			}else {
				throw new RuntimeException(list.get(i).getEmployeeNo() + "的合同期限不正确");
			}
			if(!StringUtil.isEmpty(list.get(i).getSignTypeName())) {
				list.get(i).setSignType(ContractStatusEnum.getKeyByVal(list.get(i).getSignTypeName().trim())); //签订状态
			}else {
				throw new RuntimeException(list.get(i).getEmployeeNo() + "的合同签订状态不正确");
			}
			
			
			//处理期限时间
			if("1".equals(list.get(i).getAllotedTime())) {  //固定期限才处理
				list.get(i).setAllotedDate(list.get(i).getAllotedDate().trim());
				if("年".equals(list.get(i).getAllotedType())) {
					list.get(i).setAllotedType("Y");
				}else if("月".equals(list.get(i).getAllotedType())) {
					list.get(i).setAllotedType("M");
				}else if("天".equals(list.get(i).getAllotedType())) {
					list.get(i).setAllotedType("D");
				}
			}
			
			if("是".equals(list.get(i).getRemind())) {
				list.get(i).setRemind("1");
			}else {
				list.get(i).setRemind("2");
			}
			
			if(!StringUtil.isEmpty(list.get(i).getContracJobsName())) {
				list.get(i).setContracJobs(personalIdentityMap.get(list.get(i).getContracJobsName()));
			}
			list.get(i).setEmployeeName(empMap.get(list.get(i).getEmployeeNo()).getEmployeeName());
			list.get(i).setCard(empMap.get(list.get(i).getEmployeeNo()).getIdentityNumber());
			list.get(i).setEmpOrgId(empMap.get(list.get(i).getEmployeeNo()).getOrgId());
			list.get(i).setEmpOrgName(empMap.get(list.get(i).getEmployeeNo()).getOrgName());
		}

		
		
		
		String checkVal = checkVal(list);
		if(checkVal.length() > 0) {
			throw new RuntimeException(""+checkVal);
		}
		int count = 0;
		if(list.size() > 500) {
	        int toIndex = 500;
	        int listSize = list.size();
	        for(int i= 0; i<list.size();i+=500){
	            //作用为toIndex最后没有500条数据则剩余几条list中就装几条
	            if(i+500 > listSize){
	                toIndex = listSize-i;
	            }
	            List<HrmsContract> sepaList = list.subList(i,i+toIndex);
	            count += hrmsContractMapper.batchInsert(sepaList);
	        }
		}else {
			count = hrmsContractMapper.batchInsert(list);
		}
		
		retMap.put("total",   count);
		retMap.put("info", checkVal);
		retMap.put("success", true);
		return PlatformResult.success(retMap);
	}
	
	
	/**
	 * 检验导入数据完整性 不完整的移除掉
	 * @return
	 */
	private String checkVal(List<HrmsContract> list) {
		StringBuilder  sb = new StringBuilder();
		for (int i = 0; i < list.size(); i++) {
			
			StringBuilder  _row = new StringBuilder();
			
			if(StringUtil.isEmpty(list.get(i).getEmployeeNo())) {
				_row.append("员工工号为空、");
			}
			if(StringUtil.isEmpty(list.get(i).getEmployeeName())) {
				_row.append("员工姓名为空、");
			}
			if(StringUtil.isEmpty(list.get(i).getContractType())) {
				_row.append("合同类型为空、");
			}
			if(StringUtil.isEmpty(list.get(i).getSignType())) {
				_row.append("合同状态为空、");
			}
			if("1".equals(list.get(i).getAllotedTime())) {  //固定期限
				if(StringUtil.isEmpty(list.get(i).getAllotedDate())) {
					_row.append("合同期限为空、");
				}
			}
			if(list.get(i).getStartTime() == null) {
				_row.append("合同开始日期为空、");
			}
			if(list.get(i).getFrequency() != null && Integer.valueOf(list.get(i).getFrequency()) < 1) {
				_row.append("合同签订次数为空");
			}
			if(_row.length() > 0) {
				_row.insert(0,"第"+(i+1)+"行");
				_row.append("</br>");
				sb.append(_row);
			}
		}
		return sb.toString();
		
	}

	/* (non-Javadoc)
	 * 查询个人所有合同信息
	 */
	@Override
	public List<HrmsContract> getAllByEmployeeId(String employeeId) {
		
		Example example = new Example(HrmsContract.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andEqualTo("employeeId", employeeId);
		example.setOrderByClause("sign_time");
		List<HrmsContract> list = hrmsContractMapper.selectByExample(example);
		Map<String, String> laborContractMap = hrmsDictInfoService.convertDictMap(DictContants.LABOR_CONTRACT_TYPE); // 获取合同类型的字典值
		list.forEach(item ->{
			item.setContractTypeName(laborContractMap.get(item.getContractType()));
			item.setSignTypeName(ContractStatusEnum.getValByKey(item.getSignType()));
			item.setAccessoryList(hrmsDocumentAccessoryService.getDataListByBusinessId(item.getContractId()));	//处理附件类型
		});
		
		return list;
	}



	/* (non-Javadoc)
	 * 定时任务改变合同状态
	 */
	@Override
	public void updateSignTypeJob() {
		hrmsContractMapper.updateSignTypeJob();
	}



	/* (non-Javadoc)
	 * 修改到期提醒
	 */
	@Override
	public int updateRemind(String contractId,String remind) {
		HrmsContract record = new HrmsContract();
		record.setContractId(contractId);
		record.setRemind(remind);
		return hrmsContractMapper.updateByPrimaryKeySelective(record);
	}



	@Override
	public Map<String, Object> getBubble(HrmsContract entity) {
		Page page = new Page();
		page.setPageSize(Integer.MAX_VALUE);
		
		// entity.setStartExpireDate(DateUtils.getStringDateShort(DateUtils.getCurrentQuarterEndTime()));
		//修改为从当前时间开始
		entity.setStartExpireDate(DateUtils.getStringDateShort(new Date()));
		entity.setEndExpireDate(DateUtils.getMonthDifferDate(DateUtils.getCurrentQuarterEndTime(),3));
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsContract> pageList = hrmsContractMapper.getRemindDataList(page,entity);
		Map<String, Object> retMap = new HashMap<>();
		retMap.put("count", pageList.size());
		String text = "";
		
		if("1".equals(entity.getSignType())) {
			text = pageList.size() + "份合同已到期，请及时处理！";
			
		}else {
			text = pageList.size() + "份合同至下季度将到期";
			/*if("Y".equals(entity.getAllotedType()) ) {
				text = pageList.size() + "份合同"+entity.getAllotedDate() +"年内即将到期";
			}else if("M".equals(entity.getAllotedType()) ) {
				text = pageList.size() + "份合同"+entity.getAllotedDate() +"月内即将到期";
			}else if("D".equals(entity.getAllotedType()) ) {
				text = pageList.size() + "份合同"+entity.getAllotedDate() +"日内即将到期";
			}*/
		}
		retMap.put("text", text);
		return retMap;
	}



	/* (non-Javadoc)
	 * 新增劳动合同查询是否有已签订的合同
	 */
	@Override
	public String getEmployeeOldContract(HrmsContract entity) {
		PlatformResult<EmployeeResp> empObj = hrmsEmployeeFeignService.getEmployeeDetailByCode(entity.getEmployeeNo());
		EmployeeResp emp = empObj.getObject();
		if(emp != null) {
			entity.setEmployeeId(emp.getEmployeeId());
			entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			HrmsContract bean = hrmsContractMapper.getEmployeeOldContract(entity);
			if(bean != null) {
				return "已签订合同，编号'"+bean.getContractNumber()+"',合同签订日期"+
						DateUtils.getStringDateShort(bean.getStartTime()) + " 至 " + 
						DateUtils.getStringDateShort(bean.getEndTime()) ;
			}
		}
		return "";
	}

	@Override
	public List<Map<String, Object>> getWarningContract(Map<String, Object> params) {
		return hrmsContractMapper.getWarningContract(params);
	}



	/* (non-Javadoc)
	 * 插入一条续签合同记录
	 */
	@Override
	public void syncContract(Map<String, Object> dataMap) {
		
		String userCode = dataMap.get("L_LaunchUserCode").toString();
		PlatformResult<EmployeeResp> empObj = hrmsEmployeeFeignService.getEmployeeDetailByCode(userCode);
		EmployeeResp emp = empObj.getObject();
		HrmsContract entity = new HrmsContract();
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		HrmsContract beforeContractNumberBean = hrmsContractMapper.findOldContract(emp.getEmployeeId(),ssoOrgCode);
		if(null == beforeContractNumberBean) {
			throw new RuntimeException("合同制员工审批添加一条续签记录没有找到员 合同信息");
		}
		
		BeanUtils.copyProperties(beforeContractNumberBean, entity);
		
		beforeContractNumberBean.setSignType(ContractStatusEnum.CONTRACT_STATUS_2.getKey());
		beforeContractNumberBean.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		beforeContractNumberBean.setUpdateDate(new Date());
		beforeContractNumberBean.setRemind("2");
		hrmsContractMapper.updateByPrimaryKeySelective(beforeContractNumberBean);
		
		//添加一条续签劳动合同记录  老合同改为已续签

		
		entity.setEmployeeNo(userCode);
		entity.setEmployeeName(emp.getEmployeeName());
		entity.setEmployeeNo(emp.getEmployeeNo());
		entity.setCard(emp.getIdentityNumber());
		entity.setEmployeeId(emp.getEmployeeId());
		entity.setEmpOrgId(emp.getOrgId());
		entity.setEmpOrgName(emp.getOrgName());
		String businessId = IdUtil.getId();
		entity.setContractId(businessId);
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSignType(ContractStatusEnum.CONTRACT_STATUS_1.getKey());
		entity.setUpdateUser(null);
		entity.setUpdateDate(null);

		//处理要替换的数据
		if(!StringUtil.isEmpty(beforeContractNumberBean.getContractNumber())) {
			String[] split = beforeContractNumberBean.getContractNumber().split("-");
			if(split != null && split.length == 2) {
				Integer _newBh = Integer.valueOf(split[1]) +1;
				entity.setContractNumber(split[0]+_newBh);
			}else {
				entity.setContractNumber(beforeContractNumberBean.getContractNumber() + "-01");
			}
		}
		
		entity.setSignTime(new Date());
		entity.setAllotedTime("1");  //合同是否有期限
		entity.setAllotedDate("5");  //合同期限5年
		entity.setAllotedType("Y");  //合同期限类型
		if(!StringUtil.isEmpty(beforeContractNumberBean.getFrequency()) && Integer.valueOf(beforeContractNumberBean.getFrequency()) >0 ) {
			entity.setFrequency((Integer.valueOf(beforeContractNumberBean.getFrequency()) +1) + "");
		}else {
			entity.setFrequency("1");
		}
		
		//处理合同开始日期和合同结束日期
		if(null != beforeContractNumberBean.getEndTime()) {
			Date date= beforeContractNumberBean.getEndTime();
			Calendar calendar = new GregorianCalendar(); 
			calendar.setTime(date); 
			// 把日期往后增加一天,整数  往后推,负数往前移动 
			calendar.add(calendar.DATE, 1); 
			// 这个时间就是日期往后推一天的结果 
			date=calendar.getTime(); 
			entity.setStartTime(date);  //后面一天
			calendar.add(calendar.YEAR, 5);
			date=calendar.getTime();
			entity.setEndTime(date);  //后面5年
		}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsContractMapper.insertSelective(entity);  //添加合同
		
		/*//添加附件信息表
		List<HrmsDocumentAccessory> accessoryList = entity.getAccessoryList();
		if(accessoryList != null && accessoryList.size() > 0) {
			for(int i = 0; i < accessoryList.size(); i++) {
				accessoryList.get(i).setBusinessId(businessId);
				hrmsDocumentAccessoryService.insert(accessoryList.get(i));
			}
		}*/
		
		HrmsContractLog log = new HrmsContractLog();
		log.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		log.setActionType(ContractOperationStatusEnum.OPERATION_STATUS_3.getVal());  
		log.setEmployeeNo(entity.getEmployeeNo());
		log.setEmployeeName(entity.getEmployeeName());
		log.setCard(entity.getCard());
		log.setContent("续签合同:"+entity.getContractNumber() + "原合同号："+beforeContractNumberBean.getContractNumber());
		hrmsContractLogService.insert(log);  //添加操作记录
		
	}



	@Override
	public void renewflow(Map<String, Object> dataMap) {
		
		//获取流程同步过来的关键字段（工号   本次拟续签情况，续签合同开始时间，续签合同结束时间，续签合同类型，岗位类型）
		String userCode = dataMap.get("L_employeeeNo").toString();  //工号
		String frequency = dataMap.get("L_frequency").toString();  //本次拟续签情况（签订次数）
		String allotedDate = dataMap.get("L_allotedDate").toString();  //合同期限
		String start_time = dataMap.get("L_startTime").toString();  //续签合同开始时间
		String end_time = dataMap.get("L_endTime").toString();  //续签合同结束时间
		//String contract_type_name = dataMap.get("L_contract_type_name").toString();  //续签合同类型
		//	String contrac_jobs = dataMap.get("contrac_jobs").toString();  //岗位类型
	
		//处理数据

		//取第几次签订
		if("第一次签订".equals(frequency)) {
			frequency ="1";
		}else if("第二次签订".equals(frequency)) {
			frequency ="2";
		}else if("第三次签订".equals(frequency)) {
			frequency ="3";	
		}else if("第四次签订".equals(frequency)) {
			frequency ="4";	
		}else if("第五次签订".equals(frequency)) {
			frequency ="5";	
		}
		
		
		
		
		PlatformResult<EmployeeResp> empObj = hrmsEmployeeFeignService.getEmployeeDetailByCode(userCode);
		EmployeeResp emp = empObj.getObject();
		HrmsContract entity = new HrmsContract();
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		HrmsContract beforeContractNumberBean = hrmsContractMapper.findOldContract(emp.getEmployeeId(),ssoOrgCode);
		if(null != beforeContractNumberBean) {
			BeanUtils.copyProperties(beforeContractNumberBean, entity);
			beforeContractNumberBean.setSignType(ContractStatusEnum.CONTRACT_STATUS_2.getKey());
			beforeContractNumberBean.setUpdateUser(UserInfoHolder.getCurrentUserCode());
			beforeContractNumberBean.setUpdateDate(new Date());
			beforeContractNumberBean.setRemind("2");
			hrmsContractMapper.updateByPrimaryKeySelective(beforeContractNumberBean);
		}
		
		//添加一条续签劳动合同记录  老合同改为已续签
		if(StringUtil.isEmpty(entity.getContractType())) {
			entity.setContractType("1");  //没有值就设置为劳动合同
		}
		entity.setEmployeeNo(userCode);
		entity.setEmployeeName(emp.getEmployeeName());
		entity.setEmployeeNo(emp.getEmployeeNo());
		entity.setCard(emp.getIdentityNumber());
		entity.setEmployeeId(emp.getEmployeeId());
		entity.setEmpOrgId(emp.getOrgId());
		entity.setEmpOrgName(emp.getOrgName());
		String businessId = IdUtil.getId();
		entity.setContractId(businessId);
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSignType(ContractStatusEnum.CONTRACT_STATUS_1.getKey());
		entity.setUpdateUser(null);
		entity.setUpdateDate(null);
		entity.setFrequency(frequency);
		
		//处理要替换的数据
		if(beforeContractNumberBean != null && !StringUtil.isEmpty(beforeContractNumberBean.getContractNumber())) {
			String[] split = beforeContractNumberBean.getContractNumber().split("-");
			if(split != null && split.length == 2) {
				Integer _newBh = Integer.valueOf(split[1]) +1;
				if(_newBh < 10) {
					entity.setContractNumber(split[0]+"-0"+_newBh);
				}else {
					entity.setContractNumber(split[0]+"-"+_newBh);
				}
				
			}else {
				entity.setContractNumber(beforeContractNumberBean.getContractNumber() + "-01");
			}
		}else {
			entity.setContractNumber(emp.getEmployeeNo()+ "-01");
		}
		
		entity.setSignTime(new Date());
		entity.setAllotedTime("1");  //合同是否有期限
		entity.setAllotedDate(allotedDate);  //合同期限5年
		entity.setAllotedType("Y");  //合同期限类型
		try {
			entity.setStartTime(DateUtils.getStringToDate(start_time) );  //开始时间
			entity.setEndTime(DateUtils.getStringToDate(end_time) );  //结束时间
		} catch (Exception e) {
			log.error("时间转换异常");
		}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsContractMapper.insertSelective(entity);  //添加合同
		
		HrmsContractLog logs = new HrmsContractLog();
		logs.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		logs.setActionType(ContractOperationStatusEnum.OPERATION_STATUS_3.getVal());  
		logs.setEmployeeNo(entity.getEmployeeNo());
		logs.setEmployeeName(entity.getEmployeeName());
		logs.setCard(entity.getCard());
		if(beforeContractNumberBean != null) {
			logs.setContent("续签合同:"+entity.getContractNumber() + "原合同号："+beforeContractNumberBean.getContractNumber());	
		}else {
			logs.setContent("续签合同:"+entity.getContractNumber() + "原合同号： 无");
		}
		
		hrmsContractLogService.insert(logs);  //添加操作记录
		
		//修改员工档案的合同开始时间 合同结束时间 合同是否到期
		PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getGlobalSetting("Y");
		String orgCode = globalSetting.getObject().getOrgCode();
		if("csjkyy".equals(orgCode)) {
			try {
				//直接修改数据库
				Map<String,Object> parMap = new HashMap<>();
				parMap.put("employeeId", emp.getEmployeeId());
				parMap.put("startTime", start_time);
				parMap.put("endTime", end_time);
				hrmsContractMapper.updateEmployee(parMap);
			} catch (Exception e) {
				log.error("经开修改员工合同信息"+e.getMessage(),e);
			}
		}
	
		
	}
	
}

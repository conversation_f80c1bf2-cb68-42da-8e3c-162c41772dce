package cn.trasen.hrms.service;

import java.util.List;
import java.util.Map;


/**   
 * @ClassName:  HrmsIndexService   
 * @Description:人事首页服务类  
 * @author: WZH
 * @date:   2021年12月8日 下午2:04:25      
 * @Copyright:  
 */
public interface HrmsIndexService {
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取事件预警
	  -- 作者: GW
	  -- 创建时间: 2024年10月15日
	  -- @param type  1今日、2本月、3本年
	  -- @return
	  -- =============================================
	 */
	Map<String, Object> getEvtWarn(Map<String,String> params);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取今日动态
	  -- 作者: GW
	  -- 创建时间: 2024年10月15日
	  -- @param type
	  -- @return
	  -- =============================================
	 */
	Map<String, Object> getDayDyna();
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取人员去向
	  -- 作者: GW
	  -- 创建时间: 2024年10月16日
	  -- @param type: 1今日、2本月、3自定义时间范围
	  			start_date： 开始日期  yyyy-mm-dd 
	  			end_date：结束日期  yyyy-mm-dd 
	  -- @return
	  -- =============================================
	 */
	Map<String,Object> getPsnDstn(Map<String, String> param);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取异动人员
	  -- 作者: GW
	  -- 创建时间: 2024年10月16日
	  -- @param type 1今日、2本月、3本年
	  -- @return
	  -- =============================================
	 */
	Map<String,Object> getPsnTrns(Map<String,String> params);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取人才结构-性别、年龄、工龄
	  -- 作者: GW
	  -- 创建时间: 2024年10月16日
	  -- @param type
	  -- @return
	  -- =============================================
	 */
	Map<String, Object> getStruGendAgeWorkyear();
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取学历分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月16日
	  -- @return
	  -- =============================================
	 */
	List<Map<String, Object>> getPsnEduc();
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获编制类型分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月16日
	  -- @return
	  -- =============================================
	 */
	List<Map<String, Object>> getEstablishment();
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获职称分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月16日
	  -- @return
	  -- =============================================
	 */
	List<Map<String, Object>> getJobtitle();
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取岗位分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月30日
	  -- @return
	  -- =============================================
	 */
	List<Map<String, Object>> getPersonalIdentity();
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 异动分析
	  -- 作者: GW
	  -- 创建时间: 2024年10月16日
	  -- @param type: 1本月、2本年、3自定义时间范围
	  			start_date： 开始日期  yyyy-mm-dd 
	  			end_date：结束日期  yyyy-mm-dd 
	  -- @return
	  -- =============================================
	 */
	Map<String,Object> getTrnsAna(Map<String,String> params);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 假勤分析
	  -- 作者: GW
	  -- 创建时间: 2024年10月17日
	  -- @param type: 1 7日、2本月、3本年、4自定义时间范围
	  			start_date： 开始日期  yyyy-mm-dd 
	  			end_date：结束日期  yyyy-mm-dd 
	  -- @return
	  -- =============================================
	 */
	Map<String,Object> getPsnDstnAna(Map<String,String> params);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 预警分析
	  -- 作者: GW
	  -- 创建时间: 2024年10月17日
	  -- @param type: 1本月、2本年、3自定义时间范围
	  			start_date： 开始日期  yyyy-mm-dd 
	  			end_date：结束日期  yyyy-mm-dd 
	  -- @return
	  -- =============================================
	 */
	List<Map<String, Object>> getEvtWarnAna(Map<String,String> params);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取档案分析
	  -- 作者: GW
	  -- 创建时间: 2024年10月18日
	  -- @param 
	  -- @return
	  		fileIntegrity 档案完整
	  		groupIntegrity 分组完整
	  		deptIntegrity  科室完整
	  -- =============================================
	 */
	Map<String, Object> getFileIntegrityAna();
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取档案详情分析
	  -- 作者: GW
	  -- 创建时间: 2024年10月19日
	  -- @return
	  -- =============================================
	 */
	Map<String, Object> getFileDetailUpdtAna();
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 档案详情分析导出
	  -- 作者: GW
	  -- 创建时间: 2024年10月31日
	  -- @param type
	  -- @return
	  -- =============================================
	 */
	Map<String,Object> getFileDetailUpdtExpt(String type);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取待审批事件
	  -- 作者: GW
	  -- 创建时间: 2024年10月21日
	  -- @return
	  -- =============================================
	 */
	Map<String, Object> getInstanceInfo();

}

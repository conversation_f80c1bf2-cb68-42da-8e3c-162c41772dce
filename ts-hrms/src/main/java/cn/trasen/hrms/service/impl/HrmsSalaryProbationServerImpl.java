package cn.trasen.hrms.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsSalaryProbationMapper;
import cn.trasen.hrms.model.HrmsSalaryProbation;
import cn.trasen.hrms.service.HrmsSalaryProbationServer;
import tk.mybatis.mapper.entity.Example;

/**   
 * @ClassName:  HrmsSalaryProbationServerImpl   
 * @Description:试用期岗位工资实现类   
 * @author: WZH
 * @date:   2021年11月2日 上午10:44:55      
 * @Copyright:  
 */
@Service
public class HrmsSalaryProbationServerImpl implements HrmsSalaryProbationServer {
	
	@Autowired
	HrmsSalaryProbationMapper hrmsSalaryProbationMapper;

	@Override
	public int insert(HrmsSalaryProbation entity) {
		
		Example example = new Example(HrmsSalaryProbation.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("educationType", entity.getEducationType());	
		HrmsSalaryProbation selectOneByExample = hrmsSalaryProbationMapper.selectOneByExample(example);
		if(selectOneByExample != null) {
			throw new RuntimeException("已设置学位工资");
		}
		entity.setId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateDate(new Date());
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		return hrmsSalaryProbationMapper.insert(entity); 
	
	}

	@Override
	public int update(HrmsSalaryProbation entity) {
		
		Example example = new Example(HrmsSalaryProbation.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("educationType", entity.getEducationType());	
		example.and().andNotEqualTo("id", entity.getId());
		HrmsSalaryProbation selectOneByExample = hrmsSalaryProbationMapper.selectOneByExample(example);
		if(selectOneByExample != null) {
			throw new RuntimeException("已设置学位工资");
		}
		
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsSalaryProbationMapper.updateByPrimaryKeySelective(entity);
	}

	@Override
	public int delete(String id) {
		HrmsSalaryProbation entity = hrmsSalaryProbationMapper.selectByPrimaryKey(id);
		entity.setIsDeleted(Contants.IS_DELETED_TURE);
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsSalaryProbationMapper.updateByPrimaryKeySelective(entity);

	}

	@Override
	public List<HrmsSalaryProbation> getDataList(Page page, HrmsSalaryProbation entity) {
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return	hrmsSalaryProbationMapper.getDataList(page,entity);
	}

	@Override
	public BigDecimal getSalary(String employeeId) {
		// TODO Auto-generated method stub
		return hrmsSalaryProbationMapper.getSalary(employeeId);
	}
}

package cn.trasen.hrms.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.BootComm.utils.PinYinUtil;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.contants.CommonContants;
import cn.trasen.hrms.dao.HrmsSalaryItemMapper;
import cn.trasen.hrms.enums.AttendanceItemTypeEnum;
import cn.trasen.hrms.enums.SalaryCountTypeEnum;
import cn.trasen.hrms.enums.SalaryItemTypeEnum;
import cn.trasen.hrms.model.HrmsSalaryItem;
import cn.trasen.hrms.service.HrmsSalaryItemService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsSalaryItemServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 薪酬项目 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月26日 下午4:40:20 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsSalaryItemServiceImpl implements HrmsSalaryItemService {

	@Autowired
	HrmsSalaryItemMapper hrmsSalaryItemMapper;

	/**
	 * @Title: insert
	 * @Description: 新增薪酬项目
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsSalaryItem entity) {
		entity.setSalaryItemId(String.valueOf(IdWork.id.nextId()));
		entity.setSalaryItemCode(PinYinUtil.converterToFirstSpell(entity.getSalaryItemName()).toUpperCase()); // 项目编码为名称转拼音首字母大写
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		return hrmsSalaryItemMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 更新薪酬项目
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsSalaryItem entity) {
		Assert.hasText(entity.getSalaryItemId(), "salaryItemId must be not null.");

		HrmsSalaryItem salaryItem = hrmsSalaryItemMapper.selectByPrimaryKey(entity.getSalaryItemId());
		if (salaryItem != null) {
			salaryItem.setSalaryItemName(entity.getSalaryItemName());
			salaryItem.setSalaryItemType(entity.getSalaryItemType());
			salaryItem.setCountType(entity.getCountType());
			salaryItem.setSerialNumber(entity.getSerialNumber());
			salaryItem.setSalaryItemAmount(entity.getSalaryItemAmount());
			salaryItem.setAttendanceType(entity.getAttendanceType());
			if (SalaryItemTypeEnum.ITEM_TYPE_2.getKey().equals(entity.getSalaryItemType())) { // 类型为计算时清空固定金额的值
				salaryItem.setSalaryItemAmount(null);
			}
			salaryItem.setCountFormula(entity.getCountFormula());
			salaryItem.setCountFormulaText(entity.getCountFormulaText());
			if (SalaryItemTypeEnum.ITEM_TYPE_4.getKey().equals(entity.getSalaryItemType())) { // 类型为固定金额时清空计算公式
				salaryItem.setCountFormula(null);
				salaryItem.setCountFormulaText(null);
			}
			salaryItem.setIsReal(entity.getIsReal());
			salaryItem.setUpdateUser(UserInfoHolder.getCurrentUserCode());
			salaryItem.setUpdateUserName(UserInfoHolder.getCurrentUserName());
			salaryItem.setUpdateDate(new Date());
			return hrmsSalaryItemMapper.updateByPrimaryKey(salaryItem);
		}
		return 0;
	}

	/**
	 * @Title: deleted
	 * @Description: 删除薪酬项目
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsSalaryItem salaryItem = hrmsSalaryItemMapper.selectByPrimaryKey(id);
		if (salaryItem != null) {
			salaryItem.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsSalaryItemMapper.updateByPrimaryKeySelective(salaryItem);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取薪酬项目列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsSalaryItem>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsSalaryItem> getDataList(Page page, HrmsSalaryItem entity) {
		Assert.hasText(entity.getDataCategory(), "data_category must be not null.");

		Example example = new Example(HrmsSalaryItem.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("dataCategory", entity.getDataCategory());
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(entity.getSalaryItemName())) { // 薪酬项目名称
			example.and().andLike("salaryItemName", "%" + entity.getSalaryItemName() + "%");
		}
		if (StringUtils.isNotBlank(entity.getSalaryItemType())) { // 薪酬项目类型
			example.and().andEqualTo("salaryItemType", entity.getSalaryItemType());
		}

		List<HrmsSalaryItem> list = hrmsSalaryItemMapper.selectByExampleAndRowBounds(example, page);
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsSalaryItem item : list) {
				item.setId(item.getSalaryItemId());
				if (CommonContants.DATA_CATEGORY_SALARY.equals(entity.getDataCategory())) { // 薪酬项目
					item.setSalaryItemTypeText(SalaryItemTypeEnum.getValByKey(item.getSalaryItemType()));
				} else if (CommonContants.DATA_CATEGORY_ATTENDANCE.equals(entity.getDataCategory())) { // 考勤项目
					item.setSalaryItemTypeText(AttendanceItemTypeEnum.getValByKey(item.getSalaryItemType()));
				}
				item.setCountTypeText(SalaryCountTypeEnum.getValByKey(item.getCountType())); // 计算类型
			}
		}
		return list;
	}

	/**
	 * @Title: getList
	 * @Description: 查询薪酬项目列表(不分页)
	 * @param entity
	 * @Return List<HrmsSalaryItem>
	 * <AUTHOR>
	 * @date 2020年5月15日 下午3:16:22
	 */
	@Override
	public List<HrmsSalaryItem> getList(HrmsSalaryItem entity) {
		Example example = new Example(HrmsSalaryItem.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(entity.getDataCategory())) { // 数据类别
			example.and().andEqualTo("dataCategory", entity.getDataCategory());
		}
		if (StringUtils.isNotBlank(entity.getSalaryItemType())) { // 项目类型
			example.and().andEqualTo("salaryItemType", entity.getSalaryItemType());
		}
		return hrmsSalaryItemMapper.selectByExample(example);
	}

	/**
	 * @Title: getCalculationFormulaItemList
	 * @Description: 计算公式界面获取薪酬项目列表
	 * @param entity
	 * @Return List<HrmsSalaryItem>
	 * <AUTHOR>
	 * @date 2020年5月9日 下午4:37:46
	 */
	@Override
	public List<HrmsSalaryItem> getCalculationFormulaItemList(HrmsSalaryItem entity) {
		Assert.hasText(entity.getDataCategory(), "data_category must be not null.");

		Example example = new Example(HrmsSalaryItem.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("dataCategory", entity.getDataCategory());
		if (CommonContants.DATA_CATEGORY_SALARY.equals(entity.getDataCategory())) { // 类别: 薪酬项目
			example.and().andNotEqualTo("salaryItemType", SalaryItemTypeEnum.ITEM_TYPE_2.getKey()); // 排除类型为计算公式的
		} else {

		}
		if (StringUtils.isNotBlank(entity.getSalaryItemId())) { // 薪酬项目ID
			example.and().andNotEqualTo("salaryItemId", entity.getSalaryItemId());
		}
		return hrmsSalaryItemMapper.selectByExample(example);
	}

	/**
	 * @Title: validate
	 * @Description: 校验数据
	 * @param entity
	 * @Return PlatformResult<String>
	 * <AUTHOR>
	 * @date 2020年5月11日 上午10:59:27
	 */
	@Override
	public PlatformResult<String> validate(HrmsSalaryItem entity) {


		if (entity.getSalaryItemAmount() != null && entity.getSalaryItemAmount().compareTo(new BigDecimal(0)) == -1) {

			return PlatformResult.failure("金额必须大于0");
		}

		Assert.hasText(entity.getDataCategory(), "data_category must be not null.");

		Example example = new Example(HrmsSalaryItem.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("dataCategory", entity.getDataCategory());


		// 项目ID
		if (StringUtils.isNotBlank(entity.getSalaryItemId())) {
			example.and().andNotEqualTo("salaryItemId", entity.getSalaryItemId());
		}
		// 校验名称重复
		if (StringUtils.isNotBlank(entity.getSalaryItemName())) {
			example.and().andEqualTo("salaryItemName", entity.getSalaryItemName());
			if (hrmsSalaryItemMapper.selectByExample(example).size() > 0) {
				return PlatformResult.failure("已存在的项目名称");
			}
		}
		return PlatformResult.success();
	}

	/**
	 * @Title: validateDelete
	 * @Description: 删除校验数据
	 * @param entity
	 * @Return PlatformResult<String>
	 * <AUTHOR>
	 * @date 2020年6月1日 下午3:46:45
	 */
	@Override
	public PlatformResult<String> validateDelete(String id) {
		Example example = new Example(HrmsSalaryItem.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andLike("countFormula", "%" + id + "%");

		if (hrmsSalaryItemMapper.selectByExample(example).size() > 0) {
			return PlatformResult.failure("该项目被计算公式引用，不能删除！");
		}
		return PlatformResult.success();
	}

	/**   
	 * <p>Title: getAttendanceTypeList</p>   
	 * <p>Description:获取所有的考勤类型 </p>   
	 * @return   
	 */ 
	@Override
	@Transactional(readOnly = false)
	public List<HrmsSalaryItem> getAttendanceTypeList() {
		Example example = new Example(HrmsSalaryItem.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andLike("attendanceType", "1").orLike("attendanceType", "2").orLike("attendanceType", "3");
		example.and().andEqualTo("dataCategory", "2");
		example.orderBy("serialNumber");
		return hrmsSalaryItemMapper.selectByExample(example);
	}

}

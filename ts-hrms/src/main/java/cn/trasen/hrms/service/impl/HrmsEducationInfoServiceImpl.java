package cn.trasen.hrms.service.impl;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsEducationInfoMapper;
import cn.trasen.hrms.enums.IncidentAuditStatusEnum;
import cn.trasen.hrms.model.HrmsEducationInfo;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsEducationInfoService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.utils.ExportExcelUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.map.CaseInsensitiveMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**   
 * @Title: HrmsEducationInfoServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 员工学历信息 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月12日 下午4:26:17 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsEducationInfoServiceImpl implements HrmsEducationInfoService {

	@Autowired
	HrmsEducationInfoMapper hrmsEducationInfoMapper;
	@Autowired
	HrmsDictInfoService hrmsDictInfoService;
	@Autowired
	HrmsEmployeeService hrmsEmployeeService;

	/**
	 * @Title: insert
	 * @Description: 新增学历信息
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsEducationInfo entity) {
		if("1".equals(entity.getHighestLevel())) {
			Example example = new Example(HrmsEducationInfo.class);
			example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
			example.and().andEqualTo("employeeId", entity.getEmployeeId());
			example.and().andEqualTo("highestLevel","1");
			example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
			List<HrmsEducationInfo> result = hrmsEducationInfoMapper.selectByExample(example);
			Assert.isTrue(result.size() <= 0, "不允许有多条最高学历信息");
		}
		
		//验证是否有相同学历的
	/*	Example example2 = new Example(HrmsEducationInfo.class);
		example2.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
		example2.and().andEqualTo("employeeId", entity.getEmployeeId());
		example2.and().andEqualTo("learnWay",entity.getLearnWay());
		List<HrmsEducationInfo> result2 = hrmsEducationInfoMapper.selectByExample(example2);
		Assert.isTrue(result2.size() <= 0, "不予许有相同学习形式学历");*/
		
		entity.setId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		//新增成功状态为待审批
		entity.setApprovalStatus("1");
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		entity.setXlfj(entity.getBusinessId());
		int row = hrmsEducationInfoMapper.insert(entity);
		// 更新文件
//		if(StringUtils.isNotBlank(entity.getBusinessId())) {
//			fileClientService.updateTempBusinessId(entity.getId(), entity.getBusinessId());
//		}
		return row;
	}

	/**
	 * @Title: update
	 * @Description: 更新学历信息
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsEducationInfo entity) {
		entity.setXlfj(entity.getBusinessId());
		//验证是否有相同学历的
//		Example example2 = new Example(HrmsEducationInfo.class);
//		example2.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
//		example2.and().andEqualTo("employeeId", entity.getEmployeeId());
//		example2.and().andNotEqualTo("learnWay",entity.getLearnWay());
//		example2.and().andEqualTo("educationInfoId",entity.getLearnWay());
//		List<HrmsEducationInfo> result2 = hrmsEducationInfoMapper.selectByExample(example2);
//		Assert.isTrue(result2.size() <= 0, "不予许有相同学习形式学历");
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		int row = hrmsEducationInfoMapper.updateByPrimaryKeySelective(entity);
//		if(row > 0) {
//			if(StringUtils.isNotBlank(entity.getBusinessId())) {
//				fileClientService.updateTempBusinessId(entity.getId(), entity.getBusinessId());
//			}
//		}
		return row;
	}

	/**
	 * @Title: deleted
	 * @Description: 删除学历信息
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsEducationInfo educationInfo = hrmsEducationInfoMapper.selectByPrimaryKey(id);
		if (educationInfo != null) {
			educationInfo.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsEducationInfoMapper.updateByPrimaryKeySelective(educationInfo);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取学历信息列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsEducationInfo>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsEducationInfo> getDataList(Page page, HrmsEducationInfo entity) {

//		Example example = new Example(HrmsEducationInfo.class);
//		example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
//		if(StringUtils.isNotBlank(entity.getEmployeeId())) {
//			example.and().andEqualTo("employeeId", entity.getEmployeeId());
//		}
//		if(StringUtils.isNotBlank(entity.getEmployeeName())) {
//			example.and().andLike("employeeName","%"+entity.getEmployeeName()+"%");
//		}
//		if(StringUtils.isNotBlank(entity.getEmployeeNo())) {
//			example.and().andLike("employeeNo","%"+entity.getEmployeeNo()+"%");
//		}
		ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
		String orgRang = thpsUser.getOrgRang();
		if(!UserInfoHolder.ISADMIN()) {	// 是否管理员   
			if(!StringUtil.isEmpty(orgRang)) {//查询组织范围数据
				 entity.setHtOrgIdList(orgRang);
			}
		}
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsEducationInfo> list = hrmsEducationInfoMapper.getDataList(entity,page);
//		List<HrmsEducationInfo> list = hrmsEducationInfoMapper.selectByExampleAndRowBounds(example, page);
		if (CollectionUtils.isNotEmpty(list)) {
			Map<String, String> educationTypeMap = hrmsDictInfoService.convertDictMap(DictContants.EDUCATION_TYPE); // 学历类型字典
			Map<String, String> learnWayMap = hrmsDictInfoService.convertDictMap(DictContants.LEARN_WAY); // 学习形式字典
			Map<String, String> schoolSystemMap = hrmsDictInfoService.convertDictMap(DictContants.SCHOOLSYSTEM_YEARS); // 学制字典
//			Map<String, String> educationDegreeMap = hrmsDictInfoService.convertDictMap(DictContants.EDUCATION_DEGREE); // 学位字典
			for (HrmsEducationInfo edu : list) {
				edu.setEducationTypeText(educationTypeMap.get(edu.getEducationType())); // 学历类型文本值
				edu.setLearnWayText(learnWayMap.get(edu.getLearnWay())); // 学习形式文本值
				edu.setSchoolSystemText(schoolSystemMap.get(edu.getSchoolSystem())); // 学制文本值
//				edu.setEducationDegree(educationDegreeMap.get(edu.getEducationDegree()));
			}
		}
		return list;
	}

	/**
	 * @Title: getList
	 * @Description: 获取学历信息列表(不分页)
	 * @param entity
	 * @Return List<HrmsEducationInfo>
	 * <AUTHOR>
	 * @date 2020年4月20日 下午6:09:11
	 */
	@Override
	public List<HrmsEducationInfo> getList(HrmsEducationInfo entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");

		Example example = new Example(HrmsEducationInfo.class);
		example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		example.and().andEqualTo("employeeId", entity.getEmployeeId());
		example.and().andEqualTo("approvalStatus","4");
		if (StringUtils.isNotBlank(entity.getId())) {
			example.and().andEqualTo("educationInfoId", entity.getId());
		}

		List<HrmsEducationInfo> list = hrmsEducationInfoMapper.selectByExample(example);
		if (CollectionUtils.isNotEmpty(list)) {
			Map<String, String> educationTypeMap = hrmsDictInfoService.convertDictMap(DictContants.EDUCATION_TYPE); // 学历类型字典
			Map<String, String> learnWayMap = hrmsDictInfoService.convertDictMap(DictContants.LEARN_WAY); // 学习形式字典
			Map<String, String> schoolSystemMap = hrmsDictInfoService.convertDictMap(DictContants.SCHOOLSYSTEM_YEARS); // 学制字典
			for (HrmsEducationInfo edu : list) {
				edu.setEducationTypeText(educationTypeMap.get(edu.getEducationType())); // 学历类型文本值
				edu.setLearnWayText(learnWayMap.get(edu.getLearnWay())); // 学习形式文本值
				edu.setSchoolSystemText(schoolSystemMap.get(edu.getSchoolSystem())); // 学制文本值
			}
		}
		return list;
	}

	@Override
	public HrmsEducationInfo getHighestEducation(String employeeId) {
		return hrmsEducationInfoMapper.getHighestEducation(employeeId);
	}

	@Override
	@Transactional(readOnly = false)
	public void incidentAudit(String educationInfoId) {
		if(!StringUtil.isEmpty(educationInfoId)) {
			String[] split = educationInfoId.split(",");
			for (int i = 0; i < split.length; i++) {
				String _id = split[i];
				HrmsEducationInfo hrmsEducationInfo = hrmsEducationInfoMapper.selectByPrimaryKey(_id);
				if (hrmsEducationInfo != null) {
					hrmsEducationInfo.setApprovalStatus(IncidentAuditStatusEnum.AUDIT_STATUS_4.getKey());
				}
				//如果是最高学历，其他的改为否
				if("1".equals(hrmsEducationInfo.getHighestLevel())) {
					hrmsEducationInfoMapper.updateHighestLevel(hrmsEducationInfo.getEmployeeId());	
				}
				hrmsEducationInfoMapper.updateByPrimaryKeySelective(hrmsEducationInfo);
			}
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void cancelIncidentAudit(String educationInfoId) {
		
		if(!StringUtil.isEmpty(educationInfoId)) {
			String[] split = educationInfoId.split(",");
			for (int i = 0; i < split.length; i++) {
				String _id = split[i];
				HrmsEducationInfo hrmsEducationInfo = hrmsEducationInfoMapper.selectByPrimaryKey(_id);
				if (hrmsEducationInfo != null) {
					hrmsEducationInfo.setApprovalStatus(IncidentAuditStatusEnum.AUDIT_STATUS_1.getKey());
				}
				hrmsEducationInfoMapper.updateByPrimaryKeySelective(hrmsEducationInfo);
			}
		}
	}
	
	//导入学历信息
	@Override
	@Transactional(readOnly = false)
	public PlatformResult<String> excelImportEducation(List<HrmsEducationInfo> list) throws Exception{
		
		HrmsEmployee entity = new HrmsEmployee();
		List<HrmsEmployee> empList = hrmsEmployeeService.getList(entity);
		Map<String, String> empMap = new CaseInsensitiveMap<String, String>();
		empList.forEach(item ->{
			empMap.put(item.getEmployeeNo(), item.getEmployeeId());
		});
		
		Map<String, String> educationTypeMaps = hrmsDictInfoService.convertDictValueKeyMap(DictContants.EDUCATION_TYPE); // 学历类型
		Map<String, String> schoolsystemYearsMaps = hrmsDictInfoService.convertDictValueKeyMap(DictContants.SCHOOLSYSTEM_YEARS); // 学制
		Map<String, String> learnWayMaps = hrmsDictInfoService.convertDictValueKeyMap(DictContants.LEARN_WAY); // 学习形式
		Map<String, String> educationDegreeMaps = hrmsDictInfoService.convertDictValueKeyMap(DictContants.EDUCATION_DEGREE); // 学位类型
		
		Integer count = 0;
		
		if(list != null && list.size() > 0) {
			for(int i = 0 ; i  < list.size() ; i++) {
				list.get(i).setEmployeeId(empMap.get(list.get(i).getEmployeeNo()));
				list.get(i).setApprovalStatus("4");
				//根据文本值获取字典
				list.get(i).setEducationType(educationTypeMaps.get(list.get(i).getEducationType()));
				list.get(i).setSchoolSystem(schoolsystemYearsMaps.get(list.get(i).getSchoolSystem()));
				list.get(i).setLearnWay(learnWayMaps.get(list.get(i).getLearnWay()));
				list.get(i).setEducationDegree(educationDegreeMaps.get(list.get(i).getEducationDegree()));
				//处理日期
				list.get(i).setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				if(!StringUtil.isEmpty(list.get(i).getStartTimeStr())) {
					
					if("-".equals(list.get(i).getStartTimeStr()) || "/".equals(list.get(i).getStartTimeStr())) {
						
						list.get(i).setStartTimeStr(null);
						
					}else {
						
						try {
							SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
							Date date = simpleDateFormat1.parse(list.get(i).getStartTimeStr());
							list.get(i).setStartTime(date);
						} catch (Exception e) {
							try {
								SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy/MM/dd");//注意月份是MM
								Date date = simpleDateFormat2.parse(list.get(i).getStartTimeStr());
								list.get(i).setStartTime(date);
							} catch (Exception e1) {
								throw new Exception("" + list.get(i).getEmployeeName() + "入学时间格式不对");
							}
						}
						
					}
				}
				
				if(!StringUtil.isEmpty(list.get(i).getEndTimeStr())) {
					
					if("-".equals(list.get(i).getEndTimeStr()) || "/".equals(list.get(i).getEndTimeStr())) {
						
						list.get(i).setEndTimeStr(null);
						
					}else {
					
						try {
							SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
							Date date = simpleDateFormat1.parse(list.get(i).getEndTimeStr());
							list.get(i).setEndTime(date);
						} catch (Exception e) {
							try {
								SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy/MM/dd");//注意月份是MM
								Date date = simpleDateFormat2.parse(list.get(i).getEndTimeStr());
								list.get(i).setEndTime(date);
							} catch (Exception e1) {
								throw new Exception("" + list.get(i).getEmployeeName() + "毕业时间格式不对");
							}
						}
					}
					
				}
				
				//最高学历
				if(!StringUtil.isEmpty(list.get(i).getHighestLevel()) && "否".equals(list.get(i).getHighestLevel())) {
					list.get(i).setHighestLevel("2");
				}else {
					list.get(i).setHighestLevel("1");
				}
				
				list.get(i).setId(String.valueOf(IdWork.id.nextId()));
				list.get(i).setIsDeleted(Contants.IS_DELETED_FALSE);
				list.get(i).setCreateUser(UserInfoHolder.getCurrentUserCode());
				list.get(i).setCreateUserName(UserInfoHolder.getCurrentUserName());
				list.get(i).setCreateDate(new Date());
				Example example = new Example(HrmsEducationInfo.class);
				example.and().andEqualTo("employeeId", list.get(i).getEmployeeId());
				example.and().andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
				example.and().andEqualTo("schoolName", list.get(i).getSchoolName());
				example.and().andEqualTo("educationType", list.get(i).getEducationType());
				example.setOrderByClause(" id desc LIMIT 1 ");
				HrmsEducationInfo educationInfo = hrmsEducationInfoMapper.selectOneByExample(example);

				if (educationInfo == null) {
					hrmsEducationInfoMapper.insertSelective(list.get(i));
				} else {
					list.get(i).setId(educationInfo.getId());
					hrmsEducationInfoMapper.updateByPrimaryKeySelective(list.get(i));

				}
			}

//			if(list.size() > 500) {
//		        int toIndex = 500;
//		        int listSize = list.size();
//		        for(int i= 0; i<list.size();i+=500){
//		            //作用为toIndex最后没有500条数据则剩余几条list中就装几条
//		            if(i+500 > listSize){
//		                toIndex = listSize-i;
//		            }
//		            List<HrmsEducationInfo> sepaList = list.subList(i,i+toIndex);
//		            count += hrmsEducationInfoMapper.batchInsert(sepaList);
//		        }
//			}else {
//				count = hrmsEducationInfoMapper.batchInsert(list);
//			}
		}
		
		return PlatformResult.success(String.valueOf(count));
	}

	@Override
	public PlatformResult<String> download(HttpServletResponse response) {
		ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
		String filename = "员工学历导入模板.xlsx";
		String template = "template/ygxldr.xlsx";
		ClassPathResource resource = new ClassPathResource(template);
		exportExcelUtil.downloadExportExcel(filename, response, resource);
		return PlatformResult.success();
	}

}
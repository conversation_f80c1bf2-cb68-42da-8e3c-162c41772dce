package cn.trasen.hrms.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.model.Committee;
import cn.trasen.hrms.model.CommitteeEmployee;

/**
 * 党委班子server
 * <AUTHOR>
 *
 */
public interface CommitteeService {


	/**
	 * 新增党委班子
	 * @param entity
	 * @return
	 */
	int insert(Committee entity);


	/**
	 * 修改党委班子
	 * @param entity
	 * @return
	 */
	int update(Committee entity);


	/**
	 * 删除党委班子
	 * @param id
	 * @return
	 */
	int deleted(String id);


	/**
	 * 查询党委班子 分组
	 * @param page
	 * @param entity
	 * @return
	 */
	List<Committee> getDataList(Page page, Committee entity);


	/**
	 * 党委班子不分组
	 * @param entity
	 * @return
	 */
	List<Committee> getList(Committee entity);


	List<CommitteeEmployee> getCommitteeDataList(Page page, CommitteeEmployee entity);


	int insertCommitteeEmployee(CommitteeEmployee entity);


	int updateCommitteeEmployee(CommitteeEmployee entity);


	int deleteCommitteeEmployee(String id);
}

package cn.trasen.hrms.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.DataBaseProvider;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.common.JdGridTableEntity;
import cn.trasen.hrms.dao.HrmsSchedulingHolidaysMapper;
import cn.trasen.hrms.model.HrmsSchedulingHolidays;
import cn.trasen.hrms.service.HrmsSchedulingHolidaysService;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;

@Service
public class HrmsSchedulingHolidaysServiceImpl implements HrmsSchedulingHolidaysService {
	
	@Autowired
	private HrmsSchedulingHolidaysMapper hrmsSchedulingHolidaysMapper;
	
	@Transactional
	@Override
	public int update(HrmsSchedulingHolidays entity) {
		//先删除对应日期的设置
		HrmsSchedulingHolidays record = new HrmsSchedulingHolidays();
		record.setHolidaysDate(entity.getHolidaysDate());
		hrmsSchedulingHolidaysMapper.delete(record);
		//1、工作日。2周末，3节假日
		entity.setHolidaysId(IdUtil.getId());
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsSchedulingHolidaysMapper.insertSelective(entity);
		return 1;
	}

	@Override
	public List<HrmsSchedulingHolidays> getHolidays(HrmsSchedulingHolidays entity) {
		Example example = new Example(HrmsSchedulingHolidays.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		//根据当前登录账号机构编码过滤查询数据
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(entity.getHolidaysDate())) {
			example.and().andLike("holidaysDate","%" +  entity.getHolidaysDate() +"%" );
		}
		return hrmsSchedulingHolidaysMapper.selectByExample(example);
	}

	@Override
	public List<JdGridTableEntity> getTableHeadCols(Map<String, String> paramMap) {
		//根据当前登录账号机构编码过滤查询数据
		paramMap.put("ssoOrgCode",UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsSchedulingHolidays> tableHeadCols = hrmsSchedulingHolidaysMapper.getTableHeadCols(paramMap);
		List<JdGridTableEntity> result = new ArrayList<>();
		if (tableHeadCols != null && tableHeadCols.size() >0) {
			for (HrmsSchedulingHolidays item : tableHeadCols) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(item.getHolidaysDate());
				entity.setName(item.getHolidaysDate());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				entity.setEditable(false);
				entity.setIndex("");
				result.add(entity);
			}
		}
		return result;
	}

	@Override
	public List<HrmsSchedulingHolidays> getholidaysByDate(HrmsSchedulingHolidays entity) {
		Assert.hasText(entity.getSearchStartDate(), "查询开始日期不能为空");
		Assert.hasText(entity.getSearchEndDate(), "查询结束日期不能为空");
		Example example = new Example(HrmsSchedulingHolidays.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		//根据当前登录账号机构编码过滤查询数据
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if("1".equals(entity.getSearchType())) {
			List<String> type = new ArrayList<>();
			type.add("3");
			type.add("2");
			example.and().andIn("holidaysType",type );
		}else {
			example.and().andEqualTo("holidaysType", "3");
		}
		
		if (DataBaseProvider.databaseId.equalsIgnoreCase("mysql")){
			example.and().andCondition("DATE_FORMAT(holidays_date,'%Y-%m-%d') >= '" + entity.getSearchStartDate() 
			+ "'  and DATE_FORMAT(holidays_date,'%Y-%m-%d') <= '" + entity.getSearchEndDate()+"'" );
		}
		if (DataBaseProvider.databaseId.equalsIgnoreCase("kingbase")){
			example.and().andCondition("to_char(holidays_date) >= '" + entity.getSearchStartDate() 
			+ "'  and to_char(holidays_date) <= '" + entity.getSearchEndDate()+"'" );
		}
			
		example.setOrderByClause("holidays_date ASC");
		return hrmsSchedulingHolidaysMapper.selectByExample(example);
	}

	@Override
	public List<HrmsSchedulingHolidays> getholidaysByDateJJ(HrmsSchedulingHolidays entity) {
		
		Assert.hasText(entity.getSearchStartDate(), "查询开始日期不能为空");
		Assert.hasText(entity.getSearchEndDate(), "查询结束日期不能为空");
		Example example = new Example(HrmsSchedulingHolidays.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		//根据当前登录账号机构编码过滤查询数据
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if("1".equals(entity.getSearchType())) {
			List<String> type = new ArrayList<>();
			type.add("3");
			type.add("2");
			example.and().andIn("holidaysType",type );
		}else {
			example.and().andEqualTo("holidaysType", "3");
		}
		if (DataBaseProvider.databaseId.equalsIgnoreCase("mysql")){
			example.and().andCondition("DATE_FORMAT(holidays_date,'%Y-%m-%d') >= '" + entity.getSearchStartDate() 
			+ "'  and DATE_FORMAT(holidays_date,'%Y-%m-%d') <= '" + entity.getSearchEndDate()+"'" );
		}
		if (DataBaseProvider.databaseId.equalsIgnoreCase("kingbase")){
			example.and().andCondition("to_char(holidays_date) >= '" + entity.getSearchStartDate() 
			+ "'  and to_char(holidays_date) <= '" + entity.getSearchEndDate()+"'" );
		}
		example.setOrderByClause("holidays_date ASC");
		return hrmsSchedulingHolidaysMapper.selectByExample(example);
	}

	/**
	 * 根据日期查询补班的数据
	 * @param entity
	 * @return
	 */
	@Override
	public List<HrmsSchedulingHolidays> getholidaysByDateBb(HrmsSchedulingHolidays entity) {
		Assert.hasText(entity.getSearchStartDate(), "查询开始日期不能为空");
		Assert.hasText(entity.getSearchEndDate(), "查询结束日期不能为空");
		Example example = new Example(HrmsSchedulingHolidays.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("holidaysType","1");
		if (DataBaseProvider.databaseId.equalsIgnoreCase("mysql")){		
			example.and().andCondition("DATE_FORMAT(holidays_date,'%Y-%m-%d') >= '" + entity.getSearchStartDate()
				+ "'  and DATE_FORMAT(holidays_date,'%Y-%m-%d') <= '" + entity.getSearchEndDate()+"'" );
		}else {
			example.and().andCondition("to_char(holidays_date) >= '" + entity.getSearchStartDate()
			+ "'  and to_char(holidays_date) <= '" + entity.getSearchEndDate()+"'" );
		}
		example.setOrderByClause("holidays_date ASC");
		return hrmsSchedulingHolidaysMapper.selectByExample(example);
	}

	@Override
	@Transactional
	public void syncSchedulingHolidays() {
		
		List<Map<String,Object>> commHolidaysList = hrmsSchedulingHolidaysMapper.selectCommHolidays();
		
		if(CollectionUtils.isNotEmpty(commHolidaysList)) {
			
			for (Map<String, Object> map : commHolidaysList) {
				
				if(null != map.get("vacation")) {
					String vacation = (String) map.get("vacation");
					String[] vacations = vacation.split("\\|");
					
					for (String holiday : vacations) {
						HrmsSchedulingHolidays hrmsSchedulingHolidays = new HrmsSchedulingHolidays();
						hrmsSchedulingHolidays.setHolidaysType("3");
						hrmsSchedulingHolidays.setHolidaysName((String)map.get("name"));
						hrmsSchedulingHolidays.setHolidaysDate(holiday);
						update(hrmsSchedulingHolidays);
					}
				}
				
				if(null != map.get("remark")) {
					String remark = (String) map.get("remark");
					String[] remarks = remark.split("\\|");
					
					for (String holiday : remarks) {
						HrmsSchedulingHolidays hrmsSchedulingHolidays = new HrmsSchedulingHolidays();
						hrmsSchedulingHolidays.setHolidaysType("1");
						hrmsSchedulingHolidays.setHolidaysDate(holiday);
						update(hrmsSchedulingHolidays);
					}
				}
				
			}
		}
	}
	
	
}

package cn.trasen.hrms.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.model.HrmsBlacklist;

/**    
  * <P> @Description: 黑名单管理Service层</p>
  * <P> @Date: 2020年3月11日  上午10:55:56 </p>
  * <P> @Author: panqic </p>
  * <P> @Company: 湖南爱笑恩信息科技有限公司 </p>
  * <P> @version V1.0    </p> 
  */

public interface HrmsBlacklistService {

	/**
	 * 
	 * <p> @Title: getDataList</p>
	 * <p> @Description: 获取列表</p>
	 * <p> @param page
	 * <p> @param personnelmgrBlacklist
	 * <p> @return</p>
	 * <p> @Return: List<PersonnelmgrBlacklist></p>
	 * <p> <AUTHOR>
	 */
	List<HrmsBlacklist> getDataList(Page page, HrmsBlacklist entity);
	
	/**
	 * 
	 * <p> @Title: SelectByPrimaryKey</p>
	 * <p> @Description: 通过主键查询</p>
	 * <p> @param id
	 * <p> @return</p>
	 * <p> @Return: PersonnelmgrBlacklist</p>
	 * <p> <AUTHOR>
	 */
	HrmsBlacklist SelectByPrimaryKey(String id);
	
	/**
	 * 
	 * <p> @Title: insert</p>
	 * <p> @Description: 新增</p>
	 * <p> @param personnelmgrBlacklist
	 * <p> @return</p>
	 * <p> @Return: int</p>
	 * <p> <AUTHOR>
	 */
	int insert(HrmsBlacklist entity);
	
	/**
	 * 
	 * <p> @Title: update</p>
	 * <p> @Description: 修改</p>
	 * <p> @param personnelmgrBlacklist
	 * <p> @return</p>
	 * <p> @Return: int</p>
	 * <p> <AUTHOR>
	 */
	int update(HrmsBlacklist entity);
	
	/**
	 * 
	 * <p> @Title: deleted</p>
	 * <p> @Description: 删除</p>
	 * <p> @param id
	 * <p> @return</p>
	 * <p> @Return: int</p>
	 * <p> <AUTHOR>
	 */
	int deleted(String id);
	
}

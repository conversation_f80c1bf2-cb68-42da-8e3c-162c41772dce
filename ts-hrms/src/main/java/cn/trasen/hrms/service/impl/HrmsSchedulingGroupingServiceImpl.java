package cn.trasen.hrms.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.hrms.dao.HrmsSchedulingGroupingMapper;
import cn.trasen.hrms.model.HrmsSchedulingGrouping;
import cn.trasen.hrms.model.HrmsSchedulingGroupingEmp;
import cn.trasen.hrms.model.HrmsSchedulingHolidays;
import cn.trasen.hrms.model.HrmsSchedulingManage;
import cn.trasen.hrms.service.HrmsSchedulingGroupingEmpService;
import cn.trasen.hrms.service.HrmsSchedulingGroupingService;
import cn.trasen.hrms.service.HrmsSchedulingHolidaysService;
import cn.trasen.hrms.service.HrmsSchedulingManageService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName: HrmsSchedulingGroupingServiceImpl
 * @Description:考勤组实现类
 * @author: WZH
 * @date: 2021年7月14日 下午3:58:19
 * @Copyright:
 */
@Service
public class HrmsSchedulingGroupingServiceImpl implements HrmsSchedulingGroupingService {

	@Autowired
	private HrmsSchedulingGroupingMapper hrmsSchedulingGroupingMapper;

	@Autowired
	private HrmsEmployeeFeignService hrmsEmployeeFeignService;

	@Autowired
	private HrmsSchedulingGroupingEmpService hrmsSchedulingGroupingEmpService;
	
	@Autowired
	private HrmsSchedulingHolidaysService hrmsSchedulingHolidaysService;
	
	@Autowired
	private HrmsSchedulingManageService hrmsSchedulingManageService;

	@Override
	@Transactional
	public void insert(HrmsSchedulingGrouping entity) {
		
		Example example = new Example(HrmsSchedulingGrouping.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("frequencyGroupingName", entity.getFrequencyGroupingName());
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsSchedulingGrouping> list = hrmsSchedulingGroupingMapper.selectByExample(example);
		if(list != null && list.size() > 0) {
			throw new RuntimeException("考勤组已存在");
		}
		String employeeNo = entity.getEmployeeNo();
		Assert.hasText(employeeNo, "考勤组人员不能为空");
		String id = IdUtil.getId();
		entity.setFrequencyGroupingId(id);
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsSchedulingGroupingMapper.insertSelective(entity); //添加考勤组
		List<HrmsSchedulingGroupingEmp> listHSGE = getSchedulingGroupingEmp(employeeNo,id,entity.getOrgId());
		hrmsSchedulingGroupingEmpService.batchInsert(listHSGE);
		
		//自动排班
		autoScheduling(entity, id, listHSGE);
		
	}

	@Override
	public void update(HrmsSchedulingGrouping entity) {
		String employeeNo = entity.getEmployeeNo();
		Assert.hasText(entity.getFrequencyGroupingId(), "ID不能为空.");
		Assert.hasText(employeeNo, "考勤组人员不能为空");
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		hrmsSchedulingGroupingMapper.updateByPrimaryKeySelective(entity);
		hrmsSchedulingGroupingEmpService.delete((entity.getFrequencyGroupingId()));// 删除所有员工
		List<HrmsSchedulingGroupingEmp> listHSGE = getSchedulingGroupingEmp(employeeNo,entity.getFrequencyGroupingId(),entity.getOrgId());
		hrmsSchedulingGroupingEmpService.batchInsert(listHSGE);
		
		//自动排班
		autoScheduling(entity, entity.getFrequencyGroupingId(), listHSGE);
		
	}
	
	private void autoScheduling(HrmsSchedulingGrouping entity, String id, List<HrmsSchedulingGroupingEmp> listHSGE) {
		
		if("1".equals(entity.getAutoScheduling())) {
			
			List<String> days = DateUtils.getDays(entity.getAutoStartDate(), entity.getAutoEndDate());
			
			//需要排除周末和节假日
			HrmsSchedulingHolidays hrmsSchedulingHolidays = new HrmsSchedulingHolidays();
			List<HrmsSchedulingHolidays> holidaysList = hrmsSchedulingHolidaysService.getHolidays(hrmsSchedulingHolidays);
			
			List<String> holidays = new ArrayList<>();//节假日
			List<String> workdays = new ArrayList<>();//工作日
			for (HrmsSchedulingHolidays holiday : holidaysList) {
				if("3".equals(holiday.getHolidaysType()) && StringUtils.isNotBlank(holiday.getHolidaysDate())) {
					holidays.add(holiday.getHolidaysDate());
				}
				if("1".equals(holiday.getHolidaysType()) && StringUtils.isNotBlank(holiday.getHolidaysDate())) {
					workdays.add(holiday.getHolidaysDate());
				}
			}
			 
			List<HrmsSchedulingManage> schedulingList = new ArrayList<>();
			
			for (String day : days) {
				
				boolean weekend = DateUtil.isWeekend(DateUtil.parse(day)); //判断是否周末
				
				if((!weekend || workdays.contains(day)) && !holidays.contains(day)) {//不是周末或者是工作日 并且不是节假日
					
					for (HrmsSchedulingGroupingEmp hrmsSchedulingGroupingEmp : listHSGE) {
						
						HrmsSchedulingManage hrmsSchedulingManage = new HrmsSchedulingManage();
						hrmsSchedulingManage.setSchedulingId(IdUtil.getId());
						hrmsSchedulingManage.setIsDeleted(Contants.IS_DELETED_FALSE);
						hrmsSchedulingManage.setCreateUser(UserInfoHolder.getCurrentUserCode());
						hrmsSchedulingManage.setCreateUserName(UserInfoHolder.getCurrentUserName());
						hrmsSchedulingManage.setCreateDate(new Date());
						hrmsSchedulingManage.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
						
						hrmsSchedulingManage.setSchedulingDate(day); //排期日期
						hrmsSchedulingManage.setEmployeeId(hrmsSchedulingGroupingEmp.getEmployeeId()); //人员id
						hrmsSchedulingManage.setFrequencyId(entity.getAutoJurisdictionId());//班次id
						hrmsSchedulingManage.setEmpOrgId(id); //机构id
						hrmsSchedulingManage.setFrequencyGroupingId(entity.getFrequencyGroupingId());//考勤组id
						
						schedulingList.add(hrmsSchedulingManage);
						
					}
				}
			}
			
			if(CollectionUtils.isNotEmpty(schedulingList)) {
				hrmsSchedulingManageService.batchInsert(schedulingList,true);
			}
			
		}
	}


	@Override
	public boolean deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsSchedulingGrouping bean = new HrmsSchedulingGrouping();
		bean.setFrequencyGroupingId(id);
		bean.setIsDeleted(Contants.IS_DELETED_TURE);
		bean.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		bean.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		bean.setUpdateDate(new Date());
		int re1 = hrmsSchedulingGroupingMapper.updateByPrimaryKeySelective(bean);
		int re2 = hrmsSchedulingGroupingEmpService.updateByGroupingId(id);	// 删除员工信息
		return re1 == 1 && re2 > 0;
	}

	@Override
	public List<HrmsSchedulingGrouping> getDataList(Page page, HrmsSchedulingGrouping entity) {
		//只展示自己可以的考勤组
		
		if(!UserInfoHolder.ISADMIN()) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
		}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsSchedulingGroupingMapper.getDataList(page,entity);
	}

	@Override
	public List<HrmsSchedulingGrouping> getList(HrmsSchedulingGrouping entity) {
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsSchedulingGroupingMapper.selectAll();
	}

	@Override
	public HrmsSchedulingGrouping findDetailById(String id) {
		HrmsSchedulingGrouping bean = hrmsSchedulingGroupingMapper.selectByPrimaryKey(id);
		List<HrmsSchedulingGroupingEmp> empList = hrmsSchedulingGroupingEmpService.getDetailByGroupinId(id);
		bean.setSchedulingGroupingEmp(empList);
		return bean;
	}
	
	
	/**   
	 * @Title: test   
	 * @Description: 返回员工信息
	 * @param: @return      
	 * @return: List<HrmsSchedulingGroupingEmp>      
	 * @throws   
	 */
	private  List<HrmsSchedulingGroupingEmp> getSchedulingGroupingEmp(String employeeNo,String groupingId,String orgId){
		List<String> asList = Arrays.asList(employeeNo.split(","));
		//获取所有员工信息
		PlatformResult<List<EmployeeResp>> employeeDetailByCodes = hrmsEmployeeFeignService.getEmployeeDetailByCodes(asList);
		List<EmployeeResp> empList = employeeDetailByCodes.getObject();
		//添加人员
		List<HrmsSchedulingGroupingEmp> listHSGE = new ArrayList<>();
		empList.forEach(item ->{
			HrmsSchedulingGroupingEmp emp = new HrmsSchedulingGroupingEmp();
			emp.setFrequencyGroupingEmpId(IdUtil.getId());
			emp.setFrequencyGroupingId(groupingId);
			emp.setEmployeeId(item.getEmployeeId());
			emp.setEmployeeName(item.getEmployeeName());
			emp.setEmployeeNo(item.getEmployeeNo());
			emp.setIsDeleted(Contants.IS_DELETED_FALSE);
			emp.setCreateUser(UserInfoHolder.getCurrentUserCode());
			emp.setCreateUserName(UserInfoHolder.getCurrentUserName());
			emp.setCreateDate(new Date());
			emp.setOrgId(orgId);
			emp.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			listHSGE.add(emp);
		});
		return listHSGE;
	}

	@Override
	public List<HrmsSchedulingGrouping> getPageAllList(Page page, HrmsSchedulingGrouping entity) {
		// TODO Auto-generated method stub
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsSchedulingGroupingMapper.getPageAllList(entity);
	}

	@Override
	public List<HrmsSchedulingGrouping> getDataListByGroup( HrmsSchedulingGrouping entity) {
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsSchedulingGroupingMapper.getDataListByGroup(entity);
	}

}

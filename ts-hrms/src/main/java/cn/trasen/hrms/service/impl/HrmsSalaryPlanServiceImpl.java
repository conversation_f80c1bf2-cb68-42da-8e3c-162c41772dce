package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.contants.CommonContants;
import cn.trasen.hrms.dao.HrmsSalaryPlanMapper;
import cn.trasen.hrms.model.HrmsSalaryPlan;
import cn.trasen.hrms.service.HrmsSalaryPlanService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsSalaryPlanServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 薪酬方案 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月27日 下午3:31:28 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsSalaryPlanServiceImpl implements HrmsSalaryPlanService {

	@Autowired
	HrmsSalaryPlanMapper hrmsSalaryPlanMapper;

	/**
	 * @Title: insert
	 * @Description: 新增薪酬方案
	 * @param entity
	 * @Return PlatformResult<String>
	 * <AUTHOR>
	 * @date 2020年4月26日 上午9:38:03
	 */
	@Override
	@Transactional(readOnly = false)
	public PlatformResult<String> insert(HrmsSalaryPlan entity) {
		String planId = String.valueOf(IdWork.id.nextId());
		entity.setSalaryPlanId(planId);
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setIsEnable(CommonContants.IS_ENABLE_TRUE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		if (hrmsSalaryPlanMapper.insert(entity) > 0) {
			return PlatformResult.success(planId);
		}
		return PlatformResult.failure();
	}

	/**  
	 * @Title: update
	 * @Description: 更新薪酬方案
	 * @Param: 
	 * <AUTHOR>
	 * @date 2020年3月27日 下午3:37:40 
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsSalaryPlan entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsSalaryPlanMapper.updateByPrimaryKeySelective(entity);
	}

	/**  
	 * @Title: deleted
	 * @Description: 删除薪酬方案
	 * @Param: 
	 * <AUTHOR>
	 * @date 2020年3月27日 下午3:37:40 
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsSalaryPlan hrmsSalaryPlan = hrmsSalaryPlanMapper.selectByPrimaryKey(id);
		if (hrmsSalaryPlan != null) {
			hrmsSalaryPlan.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsSalaryPlanMapper.updateByPrimaryKeySelective(hrmsSalaryPlan);
	}

	/**  
	 * @Title: getDataList
	 * @Description: 获取薪酬方案列表
	 * @Param: 
	 * <AUTHOR>
	 * @date 2020年3月27日 下午3:37:40 
	 */
	@Override
	public List<HrmsSalaryPlan> getDataList(Page page, HrmsSalaryPlan entity) {
		Example example = new Example(HrmsSalaryPlan.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);

		if (StringUtils.isNotBlank(entity.getSalaryPlanName())) { // 薪酬方案名称
			example.and().andLike("salaryPlanName", "%" + entity.getSalaryPlanName() + "%");
		}
		return hrmsSalaryPlanMapper.selectByExampleAndRowBounds(example, page);
	}

	/**
	 * 
	 * @Title: getAllList
	 * @Description: 获取所有薪酬方案列表
	 * @param isEnable 是否启用: 1=是; 2=否;
	 * @param isDeleted 删除标识: Y=是; N=否;
	 * @return 
	 * @Return: List<HrmsSalaryPlan>
	 * <AUTHOR>
	 * @date 2020年3月31日 下午5:18:54
	 */
	@Override
	public List<HrmsSalaryPlan> getSalaryPlanList(String isEnable, String isDeleted) {
		return hrmsSalaryPlanMapper.getSalaryPlanList(isEnable, isDeleted);
	}

}

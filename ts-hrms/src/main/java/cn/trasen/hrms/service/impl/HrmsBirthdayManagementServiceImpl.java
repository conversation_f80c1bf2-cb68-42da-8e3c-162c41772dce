package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.contants.CommonContants;
import cn.trasen.hrms.dao.HrmsBirthdayManagementMapper;
import cn.trasen.hrms.model.HrmsBirthdayManagement;
import cn.trasen.hrms.service.HrmsBirthdayManagementService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsBirthdayManagementServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 生日管理 业务层接口实现类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年6月17日 上午10:17:56 
 * @version V1.0   
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsBirthdayManagementServiceImpl implements HrmsBirthdayManagementService {

	@Autowired
	HrmsBirthdayManagementMapper hrmsBirthdayManagementMapper;

	/**
	 * @Title: batchInsert
	 * @Description: 批量插入(无事务)
	 * @param list
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月17日 上午10:31:13
	 */
	@Override
	public int batchInsert(List<HrmsBirthdayManagement> list) {
		return hrmsBirthdayManagementMapper.batchInsert(list);
	}

	/**
	 * @Title: insert
	 * @Description: 新增信息
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月18日 下午6:03:29
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsBirthdayManagement entity) {
		entity.setBirthdayManagementId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		return hrmsBirthdayManagementMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 更新信息
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月18日 下午1:50:20
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsBirthdayManagement entity) {
		if (StringUtils.isNotBlank(entity.getBirthdayManagementId())) {
			HrmsBirthdayManagement birthday = hrmsBirthdayManagementMapper.selectByPrimaryKey(entity.getBirthdayManagementId());
			if (birthday != null) {
				birthday.setReceiveStatus(CommonContants.BIRTH_RECEIVE_STATUS_1);
				birthday.setReceiveDate(entity.getReceiveDate());
				birthday.setBirthdayGift(entity.getBirthdayGift());
				birthday.setRemark(entity.getRemark());
				birthday.setUpdateUser(UserInfoHolder.getCurrentUserCode());
				birthday.setUpdateUserName(UserInfoHolder.getCurrentUserName());
				birthday.setUpdateDate(new Date());
				return hrmsBirthdayManagementMapper.updateByPrimaryKey(birthday);
			}
		}
		return 0;
	}

	/**
	 * @Title: deleted
	 * @Description: 删除信息
	 * @param id
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月18日 下午6:06:30
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsBirthdayManagement birthday = hrmsBirthdayManagementMapper.selectByPrimaryKey(id);
		if (birthday != null) {
			birthday.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsBirthdayManagementMapper.updateByPrimaryKeySelective(birthday);
	}

	/**  
	 * @Title: getDataList
	 * @Description: TODO
	 * @Param: 
	 * <AUTHOR>
	 * @date 2020年6月17日 上午10:44:05 
	 */
	@Override
	public List<HrmsBirthdayManagement> getDataList(Page page, HrmsBirthdayManagement entity) {
		Example example = new Example(HrmsBirthdayManagement.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		if (StringUtils.isNotBlank(entity.getEmployeeNo())) { // 员工工号
			example.and().andLike("employeeNo", "%" + entity.getEmployeeNo().trim()+ "%" );
		}
		if (StringUtils.isNotBlank(entity.getEmployeeName())) { // 员工姓名
			example.and().andLike("employeeName", "%" + entity.getEmployeeName().trim() + "%" );
		}
		if (StringUtils.isNotBlank(entity.getReceiveStatus())) { // 领取状态
			example.and().andEqualTo("receiveStatus", entity.getReceiveStatus());
		}
		if (StringUtils.isNotBlank(entity.getStartDate()) && StringUtils.isNotBlank(entity.getEndDate())) { // 开始时间、结束时间都不为空
			example.and().andBetween("dateOfBirth", entity.getStartDate(), entity.getEndDate());
		} else if (StringUtils.isNotBlank(entity.getStartDate()) && StringUtils.isBlank(entity.getEndDate())) { // 开始时间不为空
			example.and().andGreaterThanOrEqualTo("dateOfBirth", entity.getStartDate());
		} else if (StringUtils.isBlank(entity.getStartDate()) && StringUtils.isNotBlank(entity.getEndDate())) { // 结束时间不为空
			example.and().andLessThanOrEqualTo("dateOfBirth", entity.getEndDate());
		} else {
			// 都为空不处理
		}

		List<HrmsBirthdayManagement> list = hrmsBirthdayManagementMapper.selectByExampleAndRowBounds(example, page);
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsBirthdayManagement birthday : list) {
				if (CommonContants.BIRTH_RECEIVE_STATUS_1.equals(birthday.getReceiveStatus())) {
					birthday.setReceiveStatusText("已领取");
				} else {
					birthday.setReceiveStatusText("未领取");
				}
			}
		}
		return list;
	}

}

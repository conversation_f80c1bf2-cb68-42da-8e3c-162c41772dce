package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.CustEmpInfoMapper;
import cn.trasen.hrms.model.CustEmpInfo;
import cn.trasen.hrms.service.CustEmpInfoService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CustEmpInfoServiceImpl
 * @Description TODO
 * @date 2024��10��15�� ����3:04:50
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CustEmpInfoServiceImpl implements CustEmpInfoService {

	@Autowired
	private CustEmpInfoMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(CustEmpInfo record) {
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CustEmpInfo record) {
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CustEmpInfo record = new CustEmpInfo();
		record.setInfoId(id);
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public CustEmpInfo selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CustEmpInfo> getDataSetList(Page page, CustEmpInfo record) {
		Example example = new Example(CustEmpInfo.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<CustEmpInfo> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

package cn.trasen.hrms.service.impl;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsEmployeeMapper;
import cn.trasen.hrms.dao.HrmsPartyMembersMapper;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsPartyMembers;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsPartyMembersService;
import cn.trasen.hrms.utils.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName HrmsPartyMembersServiceImpl
 * @Description TODO
 * @date 2021��12��18�� ����3:15:15
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsPartyMembersServiceImpl implements HrmsPartyMembersService {

	@Autowired
	private HrmsPartyMembersMapper mapper;
	
	@Autowired
	private HrmsDictInfoService hrmsDictInfoService;
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsPartyMembers record) {

		Example example = new Example(HrmsPartyMembers.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("empId", record.getEmpId());
		HrmsPartyMembers partyMembers = mapper.selectOneByExample(example);
		
		Assert.isNull(partyMembers, "已存在该党员信息");
		
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		record.setPartyStatus("0");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsPartyMembers record) {
		
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		if("1".equals(record.getPartyStatus())) {
			record.setExamineDate(new Date());
			record.setExamineUser(user.getUsercode());
			record.setExamineUserName(user.getUsername());
			mapper.updateEmployeePoliticalStatus(record);
		}
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsPartyMembers record = new HrmsPartyMembers();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsPartyMembers selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsPartyMembers> getDataSetList(Page page, HrmsPartyMembers record) {
		//根据当前登录账号机构编码过滤查询数据
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<String> politicalStatusList = new ArrayList<>();
		if("Y".equals(record.getOtherPartyMembers())){
			politicalStatusList.add("5");
			politicalStatusList.add("6");
			politicalStatusList.add("7");
			politicalStatusList.add("8");
			politicalStatusList.add("9");
			politicalStatusList.add("10");
			politicalStatusList.add("11");
			politicalStatusList.add("12");
		}else{
			politicalStatusList.add("1");
			politicalStatusList.add("2");
		}
		record.setPoliticalStatusList(politicalStatusList);
		
		List<HrmsPartyMembers> records = mapper.selectList(page, record);
		
		Map<String, String> politicalStatus = hrmsDictInfoService.convertDictMap("political_status");
		Map<String, String> personalIdentity = hrmsDictInfoService.convertDictMap("personal_identity");
		//性别
		Map<String, String> sexMap = hrmsDictInfoService.convertDictMap("SEX");
		//民族
		Map<String, String> nationalityNameMap = hrmsDictInfoService.convertDictMap("nationality_name");
		//学历
		Map<String, String> educationTypeMap = hrmsDictInfoService.convertDictMap("education_type");
		//编制类型
		Map<String, String> establishmentTypeMap = hrmsDictInfoService.convertDictMap("establishment_type");
		
		for (HrmsPartyMembers hrmsPartyMembers : records) {
			hrmsPartyMembers.setPoliticalStatusText(politicalStatus.get(hrmsPartyMembers.getPoliticalStatus()));
			hrmsPartyMembers.setPostNameText(personalIdentity.get(hrmsPartyMembers.getPostName()));
			hrmsPartyMembers.setGender(sexMap.get(hrmsPartyMembers.getGender()));
			hrmsPartyMembers.setNationality(nationalityNameMap.get(hrmsPartyMembers.getNationality()));
			hrmsPartyMembers.setEducationType(educationTypeMap.get(hrmsPartyMembers.getEducationType()));
			hrmsPartyMembers.setEstablishmentType(establishmentTypeMap.get(hrmsPartyMembers.getEstablishmentType()));
		}
		
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public Map<String, Object> selectEmployeeById(String empId) {
		Map<String, Object> result = mapper.selectEmployeeById(empId);
		Map<String, String> politicalStatus = hrmsDictInfoService.convertDictMap("political_status");
		Map<String, String> personalIdentity = hrmsDictInfoService.convertDictMap("personal_identity");
		if (result.containsKey("personal_identity") && result.get("personal_identity")!=null) {
			result.put("postNameText",personalIdentity.get(result.get("personal_identity").toString()));
		}
		if (result.containsKey("political_status") && result.get("political_status")!=null) {
			result.put("politicalStatusText",personalIdentity.get(result.get("political_status").toString()));
		}
		return result;
	}

	@Override
	@Transactional(readOnly = false)
	public void syncEmployeeData() {
		List<HrmsEmployee> employeeList = mapper.selectSyncEmployee(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsPartyMembers> list = new ArrayList<>();
		List<HrmsPartyMembers> updateList = new ArrayList<>();
		for (HrmsEmployee hrmsEmployee : employeeList) {
			
			Example example = new Example(HrmsPartyMembers.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("empId", hrmsEmployee.getEmployeeId());
			HrmsPartyMembers partyMembers = mapper.selectOneByExample(example);
			if(null == partyMembers) {
				HrmsPartyMembers hrmsPartyMembers = new HrmsPartyMembers();
				hrmsPartyMembers.setId(IdGeneraterUtils.nextId());
				hrmsPartyMembers.setEmpId(hrmsEmployee.getEmployeeId());
				hrmsPartyMembers.setPoliticalStatus(hrmsEmployee.getPoliticalStatus());
				hrmsPartyMembers.setPartyDate(hrmsEmployee.getPartyDate());
				hrmsPartyMembers.setRetireDate(hrmsEmployee.getRetireDate());
				hrmsPartyMembers.setPartyStatus("1");
				hrmsPartyMembers.setEmpCode(hrmsEmployee.getEmployeeNo());
				hrmsPartyMembers.setEmpName(hrmsEmployee.getEmployeeName());
				hrmsPartyMembers.setDeptName(hrmsEmployee.getOrgName());
				hrmsPartyMembers.setIdcard(hrmsEmployee.getIdentityNumber());
				hrmsPartyMembers.setPostName(hrmsEmployee.getPersonalIdentity());
				hrmsPartyMembers.setCreateDate(new Date());
				hrmsPartyMembers.setUpdateDate(new Date());
				hrmsPartyMembers.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				ThpsUser user = UserInfoHolder.getCurrentUserInfo();
				if (user != null) {
					hrmsPartyMembers.setCreateUser(user.getUsercode());
					hrmsPartyMembers.setCreateUserName(user.getUsername());
					hrmsPartyMembers.setUpdateUser(user.getUsercode());
					hrmsPartyMembers.setUpdateUserName(user.getUsername());
				}
				hrmsPartyMembers.setIsDeleted("N");
				list.add(hrmsPartyMembers);
			}else {
				partyMembers.setPoliticalStatus(hrmsEmployee.getPoliticalStatus());
				partyMembers.setPartyDate(hrmsEmployee.getPartyDate());
				partyMembers.setRetireDate(hrmsEmployee.getRetireDate());
				partyMembers.setEmpCode(hrmsEmployee.getEmployeeNo());
				partyMembers.setEmpName(hrmsEmployee.getEmployeeName());
				partyMembers.setDeptName(hrmsEmployee.getOrgName());
				partyMembers.setIdcard(hrmsEmployee.getIdentityNumber());
				partyMembers.setPostName(hrmsEmployee.getPersonalIdentity());
				partyMembers.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				updateList.add(partyMembers);
			}
		}
		
		for (HrmsPartyMembers hrmsPartyMembers : list) {
			mapper.insertSelective(hrmsPartyMembers);
		}
		
		for (HrmsPartyMembers hrmsPartyMembers : updateList) {
			mapper.updateByPrimaryKeySelective(hrmsPartyMembers);
		}
	}
}

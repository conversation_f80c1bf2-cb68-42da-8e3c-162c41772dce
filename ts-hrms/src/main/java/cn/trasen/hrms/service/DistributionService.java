package cn.trasen.hrms.service;

import cn.trasen.hrms.bean.TreeListResp;

import java.util.List;
import java.util.Map;

/**   
 * @ClassName:  DistributionService   
 * @Description:报表service  
 * @author: WZH
 * @date:   2021年7月30日 上午11:45:01      
 * @Copyright:  
 */
public interface DistributionService {
	
	
	/**   
	 * @Title: orgDistribution   
	 * @Description: 科室分布报表  
	 * @param: @param ids
	 * @param: @return      
	 * @return: List<Map<String,String>>      
	 * @throws   
	 */
	List<TreeListResp> orgDistribution(List<String> orgIds);

	/**
	 * 查询科室人员报表
	 * @param orgIds
	 * @param employeeName
	 * @return
	 */
	List<TreeListResp> getOrgEmployeeList(List<String> orgIds,List<String> employeeStatus,String employeeName);

	
	/**   
	 * @Title: getCoefficient   
	 * @Description: 系数报表  
	 * @param: @param employeeIds
	 * @param: @return      
	 * @return: List<Map<String,String>>      
	 * @throws   
	 */
	List<Map<String, String>> getCoefficient(List<String> employeeIds);
}

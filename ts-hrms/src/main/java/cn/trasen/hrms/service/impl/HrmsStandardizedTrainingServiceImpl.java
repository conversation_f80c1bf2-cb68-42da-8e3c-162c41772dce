package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsStandardizedTrainingMapper;
import cn.trasen.hrms.model.HrmsStandardizedTraining;
import cn.trasen.hrms.service.HrmsStandardizedTrainingService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsStandardizedTrainingServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 员工规范化培训记录 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月13日 上午9:45:23 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsStandardizedTrainingServiceImpl implements HrmsStandardizedTrainingService {

	@Autowired
	HrmsStandardizedTrainingMapper hrmsStandardizedTrainingMapper;

	/**
	 * @Title: insert
	 * @Description: 新增规范化培训记录
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsStandardizedTraining entity) {
		entity.setId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		return hrmsStandardizedTrainingMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 更新规范化培训记录
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsStandardizedTraining entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsStandardizedTrainingMapper.updateByPrimaryKeySelective(entity);
	}

	/**
	 * @Title: deleted
	 * @Description: 删除规范化培训记录
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsStandardizedTraining standardizedTraining = hrmsStandardizedTrainingMapper.selectByPrimaryKey(id);
		if (standardizedTraining != null) {
			standardizedTraining.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsStandardizedTrainingMapper.updateByPrimaryKeySelective(standardizedTraining);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取规范化培训记录列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsStandardizedTraining>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsStandardizedTraining> getDataList(Page page, HrmsStandardizedTraining entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");

		Example example = new Example(HrmsStandardizedTraining.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		example.and().andEqualTo("employeeId", entity.getEmployeeId());

		if (StringUtils.isNotBlank(entity.getProfession())) { // 培训专业
			example.and().andLike("profession", "%" + entity.getProfession() + "%");
		}
		return hrmsStandardizedTrainingMapper.selectByExampleAndRowBounds(example, page);
	}

	/**
	 * @Title: getList
	 * @Description: 查询规范化培训记录列表(不分页)
	 * @param entity 
	 * @Return List<HrmsStandardizedTraining>
	 * <AUTHOR>
	 * @date 2020年4月22日 上午9:13:52
	 */
	@Override
	public List<HrmsStandardizedTraining> getList(HrmsStandardizedTraining entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");

		Example example = new Example(HrmsStandardizedTraining.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("employeeId", entity.getEmployeeId());
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(entity.getId())) {
			example.and().andEqualTo("standardizedTrainingId", entity.getId());
		}
		return hrmsStandardizedTrainingMapper.selectByExample(example);
	}

}

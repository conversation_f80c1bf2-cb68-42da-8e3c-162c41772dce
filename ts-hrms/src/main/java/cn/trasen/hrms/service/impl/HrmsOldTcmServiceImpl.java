package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsOldTcmMapper;
import cn.trasen.hrms.model.HrmsOldTcm;
import cn.trasen.hrms.service.HrmsOldTcmService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsOldTcmServiceImpl
 * @Description TODO
 * @date 2023��10��30�� ����2:04:31
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsOldTcmServiceImpl implements HrmsOldTcmService {

	@Autowired
	private HrmsOldTcmMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsOldTcm record) {
		record.setId(String.valueOf(IdWork.id.nextId()));
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		//修改时更新机构编码
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		if (user != null) {
			//修改时更新机构名称
			record.setSsoOrgName(user.getOrgName());
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsOldTcm record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		//修改时更新机构编码
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		if (user != null) {
			//修改时更新机构名称
			record.setSsoOrgName(user.getOrgName());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsOldTcm record = new HrmsOldTcm();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsOldTcm selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsOldTcm> getDataSetList(Page page, HrmsOldTcm record) {
		Example example = new Example(HrmsOldTcm.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		//根据当前登录账号机构编码过滤查询数据
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if(!StringUtil.isEmpty(record.getYearDate())) {
			criteria.andLike("yearDate", "%" + record.getYearDate() + "%");
		}
		if(!StringUtil.isEmpty(record.getEmployeeName())) {
			criteria.andLike("employeeName", "%" + record.getEmployeeName() + "%");
		}
		List<HrmsOldTcm> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

package cn.trasen.hrms.service.impl;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.hrms.dao.*;
import cn.trasen.hrms.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.hrms.model.HrmsWarningCompare;
import cn.trasen.hrms.model.HrmsWarningRecord;
import cn.trasen.hrms.model.HrmsWarningSetting;
import cn.trasen.hrms.service.HrmsContractService;
import cn.trasen.hrms.service.HrmsWarningSettingService;
import cn.trasen.hrms.utils.CommonUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @ClassName HrmsWarningSettingServiceImpl
 * @Description TODO
 * @date 2021��10��11�� ����4:28:49
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsWarningSettingServiceImpl implements HrmsWarningSettingService {

	@Resource
	private HrmsWarningSettingMapper mapper;
	
	@Resource
	private HrmsWarningCompareMapper hrmsWarningCompareMapper;
	
	@Resource
	private HrmsWarningRecordMapper hrmsWarningRecordMapper;
	
	@Autowired
	private HrmsContractService hrmsContractService;
	
	@Resource
	private HrmsEmployeeBecomeMapper hrmsEmployeeBecomeMapper;
	
	@Resource
	private HrmsPostMapper hrmsPostMapper;
	
	@Resource
	private HrmsJobtitleInfoMapper hrmsJobtitleInfoMapper;
	
	@Resource
	private HrmsEmployeeMapper hrmsEmployeeMapper;
	
	@Resource
	private InformationFeignService informationFeignService;

	@Resource
	private HrmsLeaveReportMapper hrmsLeaveReportMapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsWarningSetting record) {
		Assert.hasText(record.getWarningTitle(), "预警标题不能为空！");
		Assert.hasText(record.getWarningType(), "预警类型不能为空!");
		Assert.hasText(record.getConditions(), "预警指定条件不能为空！");

		Example ex = new Example(HrmsWarningSetting.class);
		Example.Criteria crt = ex.createCriteria();
//		crt.andEqualTo("warningTitle", record.getWarningTitle());
		crt.andEqualTo("conditions",record.getConditions());
		crt.andEqualTo("warningType",record.getWarningType());
		crt.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		crt.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		HrmsWarningSetting hrmsWarningSetting = mapper.selectOneByExample(ex);
		
		Assert.isTrue(null == hrmsWarningSetting, "相同预警类型及指定条件只能有一条预警设置，请修改后再提交！");
		
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		List<HrmsWarningCompare> hrmsWarningCompareList = record.getHrmsWarningCompareList();
		
		Example example = new Example(HrmsWarningCompare.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("warningId", record.getId());
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		hrmsWarningCompareMapper.deleteByExample(example);
		
		if(hrmsWarningCompareList.size() > 0) {
			
			for (HrmsWarningCompare hrmsWarningCompare : hrmsWarningCompareList) {
				hrmsWarningCompare.setWarningId(record.getId());
			}
			
			hrmsWarningCompareMapper.batchInsert(record.getHrmsWarningCompareList());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsWarningSetting record) {
		Assert.hasText(record.getId(), "预警设置id不能为空！");
		Assert.hasText(record.getWarningTitle(), "预警标题不能为空！");
		Assert.hasText(record.getWarningType(), "预警类型不能为空!");
		Assert.hasText(record.getConditions(), "预警指定条件不能为空！");
		Example ex = new Example(HrmsWarningSetting.class);
		Example.Criteria crt = ex.createCriteria();
		crt.andEqualTo("conditions",record.getConditions());
		crt.andEqualTo("warningType",record.getWarningType());
		crt.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		crt.andNotEqualTo("id",record.getId());
		crt.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		HrmsWarningSetting hrmsWarningSetting = mapper.selectOneByExample(ex);
		Assert.isTrue(null == hrmsWarningSetting, "相同预警类型及指定条件只能有一条预警设置，请修改后再提交！");
		
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		List<HrmsWarningCompare> hrmsWarningCompareList = record.getHrmsWarningCompareList();
		
		Example example = new Example(HrmsWarningCompare.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("warningId", record.getId());
		hrmsWarningCompareMapper.deleteByExample(example);
		
		if(hrmsWarningCompareList.size() > 0) {
			for (HrmsWarningCompare hrmsWarningCompare : hrmsWarningCompareList) {
				hrmsWarningCompare.setWarningId(record.getId());
			}
			hrmsWarningCompareMapper.batchInsert(record.getHrmsWarningCompareList());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsWarningSetting record = new HrmsWarningSetting();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsWarningSetting selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsWarningSetting> getDataSetList(Page page, HrmsWarningSetting record) {
		Example example = new Example(HrmsWarningSetting.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsWarningSetting> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsWarningSetting> selectWarningList(String status) {
		Example example = new Example(HrmsWarningSetting.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		//根据当前登录账号机构编码过滤查询数据
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if(StringUtils.isNotBlank(status)) {
			criteria.andEqualTo("status", "1");
			criteria.andIsNotNull("conditions");
		}
		List<HrmsWarningSetting> warningList = mapper.selectByExample(example);
		for (HrmsWarningSetting hrmsWarningSetting : warningList) {
			Example example2 = new Example(HrmsWarningCompare.class);
			Example.Criteria criteria2 = example2.createCriteria();
			criteria2.andEqualTo("warningId", hrmsWarningSetting.getId());
			List<HrmsWarningCompare> hrmsWarningCompareList = hrmsWarningCompareMapper.selectByExample(example2);
			hrmsWarningSetting.setHrmsWarningCompareList(hrmsWarningCompareList);
		}
		return warningList;
	}

	@Override
	public List<HrmsWarningSetting> selectGroupWarningSettingList(String status){
		List<HrmsWarningSetting> result = new ArrayList<>();
		List<String> warningTypes = Arrays.asList("到期提醒","到龄提醒","逾期提醒");
		HrmsWarningSetting entity = null;
		List<HrmsWarningSetting> warningList = null;
		Example example = null;
		Example.Criteria criteria = null;
		Example example2 = null;
		Example.Criteria criteria2 = null;
		List<HrmsWarningCompare> hrmsWarningCompareList = null;
		for(String type : warningTypes){
			//初始化类型为一个虚拟提示设置对象
			entity = new HrmsWarningSetting();
			entity.setWarningType(type);
			entity.setWarningTitle(type);
			entity.setId(IdGeneraterUtils.nextId());
			entity.setName(type);
			example = new Example(HrmsWarningSetting.class);
			criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
			if(StringUtils.isNotBlank(status)) {
				criteria.andEqualTo("status", "1");
			}
			criteria.andEqualTo("warningType",type);
			warningList = mapper.selectByExample(example);
			for (HrmsWarningSetting hrmsWarningSetting : warningList) {
				hrmsWarningSetting.setPid(entity.getId());
				hrmsWarningSetting.setName(hrmsWarningSetting.getWarningTitle());
				example2 = new Example(HrmsWarningCompare.class);
				criteria2 = example2.createCriteria();
				criteria2.andEqualTo("warningId", hrmsWarningSetting.getId());
				hrmsWarningCompareList = hrmsWarningCompareMapper.selectByExample(example2);
				hrmsWarningSetting.setHrmsWarningCompareList(hrmsWarningCompareList);
			}
			if(CollUtil.isNotEmpty(warningList)){
				entity.setChildren(warningList);
			}
			result.add(entity);
		}
		return result;
	}

	@Override
	@Transactional(readOnly = false)
	public void generateWarningSettingData() {
		//更新合同、转正、退休预警状态
		hrmsWarningRecordMapper.updateContractWarningStatus();
		hrmsWarningRecordMapper.updateEmployeeBecomeWarningStatus();
		hrmsWarningRecordMapper.updateEmployeeRetirementWarningStatus();
		//拿到预警设置的数据
		List<HrmsWarningSetting> hrmsWarningSettingList = selectWarningList("1");
		//拿到所有非逾期提醒的设置
		List<String> settingList = hrmsWarningSettingList.stream().filter(vo->!"逾期提醒".equals(vo.getWarningType())).map(HrmsWarningSetting::getId).collect(Collectors.toList());
		if(CollUtil.isNotEmpty(settingList)) {
			//批量将预警记录已到期的数据改成已提醒
			Example example = new Example(HrmsWarningRecord.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("warningStatus", "0");
			criteria.andIn("settingId",settingList);
			criteria.andLessThan("dueDate", DateUtils.getStringDateShort(DateUtil.date()));
			HrmsWarningRecord warningRecord = new HrmsWarningRecord();
			warningRecord.setWarningStatus("1");
			warningRecord.setUpdateDate(new Date());
			hrmsWarningRecordMapper.updateByExampleSelective(warningRecord,example);
		}

		for (HrmsWarningSetting hrmsWarningSetting : hrmsWarningSettingList) {
			try {
				Map<String, Object> params = new HashMap<>();
				params.put("advanceDays", hrmsWarningSetting.getAdvanceDays());
				List<HrmsWarningCompare> hrmsWarningCompareList = hrmsWarningSetting.getHrmsWarningCompareList();
				if (null != hrmsWarningCompareList && hrmsWarningCompareList.size() > 0) {
					StringBuffer compareSql = new StringBuffer();
					compareSql.append(" and (");
					for (int i = 0; i < hrmsWarningCompareList.size(); i++) {
						HrmsWarningCompare hrmsWarningCompare = hrmsWarningCompareList.get(i);
						compareSql.append("e.").append(hrmsWarningCompare.getCompareType())
								.append(hrmsWarningCompare.getCompareSymbols())
								.append("'").append(hrmsWarningCompare.getCompareValue()).append("'");
						if (i < hrmsWarningCompareList.size() - 1) {
							compareSql.append(hrmsWarningCompare.getCompareLogic());
						}
					}
					compareSql.append(")");
					params.put("compareSql", compareSql.toString());
				}
				if("逾期提醒".equals(hrmsWarningSetting.getWarningType())){
					params.put("is_expired","1");
					if ("合同到期".equals(hrmsWarningSetting.getConditions())) {
						List<Map<String, Object>> hrmsContractList = hrmsContractService.getWarningContract(params);
						if (null != hrmsContractList && hrmsContractList.size() > 0) {
							insertOrUpdate(hrmsWarningSetting, hrmsContractList);
						}
					}
					if ("转正日期".equals(hrmsWarningSetting.getConditions())) {
						List<Map<String, Object>> warningBecomeList = hrmsEmployeeBecomeMapper.getWarningBecome(params);
						if (null != warningBecomeList && warningBecomeList.size() > 0) {
							insertOrUpdate(hrmsWarningSetting, warningBecomeList);
						}
					}
					if ("退休年龄".equals(hrmsWarningSetting.getConditions())) {
						List<Map<String, Object>> warningRetireDataList = hrmsEmployeeMapper.getWarningRetireData(params);

						if (null != warningRetireDataList && warningRetireDataList.size() > 0) {
							insertOrUpdate(hrmsWarningSetting, warningRetireDataList);
						}
					}
				}else {
					if ("合同到期".equals(hrmsWarningSetting.getConditions())) {

						List<Map<String, Object>> hrmsContractList = hrmsContractService.getWarningContract(params);

						if (null != hrmsContractList && hrmsContractList.size() > 0) {
							insertOrUpdate(hrmsWarningSetting, hrmsContractList);
						}
					}
					if ("转正日期".equals(hrmsWarningSetting.getConditions())) {

						List<Map<String, Object>> warningBecomeList = hrmsEmployeeBecomeMapper.getWarningBecome(params);

						if (null != warningBecomeList && warningBecomeList.size() > 0) {
							insertOrUpdate(hrmsWarningSetting, warningBecomeList);
						}
					}
					if ("岗位晋升日期".equals(hrmsWarningSetting.getConditions())) {
						List<Map<String, Object>> warningPromotionPostList = hrmsPostMapper.getWarningPromotionPostData(params);

						if (null != warningPromotionPostList && warningPromotionPostList.size() > 0) {
							insertOrUpdate(hrmsWarningSetting, warningPromotionPostList);
						}
					}
					if ("职称晋升日期".equals(hrmsWarningSetting.getConditions())) {
						List<Map<String, Object>> warningPromotionJobList = hrmsJobtitleInfoMapper.getWarningPromotionJobData(params);

						if (null != warningPromotionJobList && warningPromotionJobList.size() > 0) {
							insertOrUpdate(hrmsWarningSetting, warningPromotionJobList);
						}
					}
					if ("生日日期".equals(hrmsWarningSetting.getConditions())) {
						List<Map<String, Object>> warningBirthdayList = hrmsEmployeeMapper.getWarningBirthday(params);

						if (null != warningBirthdayList && warningBirthdayList.size() > 0) {
							insertOrUpdate(hrmsWarningSetting, warningBirthdayList);
						}
					}
					if ("请假结束日期".equals(hrmsWarningSetting.getConditions())) {
						List<Map<String, Object>> warningBirthdayList = hrmsLeaveReportMapper.getWarningLeaveMoreThan30DaysData(params);

						if (null != warningBirthdayList && warningBirthdayList.size() > 0) {
							insertOrUpdate(hrmsWarningSetting, warningBirthdayList);
						}
					}
					if ("退休年龄".equals(hrmsWarningSetting.getConditions())) {

						params.put("conditionsNumbers", hrmsWarningSetting.getConditionsNumbers());

						List<Map<String, Object>> warningRetireDataList = hrmsEmployeeMapper.getWarningRetireData(params);

						if (null != warningRetireDataList && warningRetireDataList.size() > 0) {
							insertOrUpdate(hrmsWarningSetting, warningRetireDataList);
						}
					}
					if ("党龄".equals(hrmsWarningSetting.getConditions())) {

						params.put("conditionsNumbers", hrmsWarningSetting.getConditionsNumbers());

						List<Map<String, Object>> warningPartyDataList = hrmsEmployeeMapper.getWarningPartyData(params);

						if (null != warningPartyDataList && warningPartyDataList.size() > 0) {
							insertOrUpdate(hrmsWarningSetting, warningPartyDataList);
						}
					}
					if ("单位连续工龄".equals(hrmsWarningSetting.getConditions())) {

						params.put("conditionsNumbers", hrmsWarningSetting.getConditionsNumbers());

						List<Map<String, Object>> warningEntryDataList = hrmsEmployeeMapper.getWarningEntryData(params);

						if (null != warningEntryDataList && warningEntryDataList.size() > 0) {
							insertOrUpdate(hrmsWarningSetting, warningEntryDataList);
						}
					}
				}
			}catch (Exception e){
				log.error("预警设置["+hrmsWarningSetting.getWarningTitle()+"]生成预警数据执行失败：" + e.getMessage(),e);
			}
		}
	}

	private void insertOrUpdate(HrmsWarningSetting hrmsWarningSetting, List<Map<String, Object>> list) {
		List<HrmsWarningRecord> insertWarningRecordList = new ArrayList<>();
		List<HrmsWarningRecord> updateWarningRecordList = new ArrayList<>();
		
		for (Map<String, Object>  map : list) {
			HrmsWarningRecord record = new HrmsWarningRecord();
			record.setEmpCode((String) map.get("employee_no"));
			record.setWarningTitle(hrmsWarningSetting.getWarningTitle());
			record.setIsDeleted("N");
			record.setWarningStatus("0");
			if(map.containsKey("remark") && StringUtils.isNotEmpty((String)map.get("remark"))){
				record.setRemark((String)map.get("remark"));
			}
			HrmsWarningRecord hrmsWarning = hrmsWarningRecordMapper.selectOne(record);
			long remainingDays = 0L;
			long oerdueDays = 0;
			if(map.get("remainingDays") instanceof Long){
				remainingDays = (Long) map.get("remainingDays");
			}else{
				remainingDays = Long.valueOf(map.get("remainingDays").toString());
			}
			if("逾期提醒".equals(hrmsWarningSetting.getWarningType())){
				oerdueDays = -1L * remainingDays ;
				remainingDays = 0;
			}else{
				oerdueDays = 0;
			}
			Object upgradeDate = map.get("upgradeDate");
			String date = "";
			if(upgradeDate!=null && upgradeDate instanceof java.sql.Date){
				SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
				date = formatter.format(upgradeDate);
			}else if(upgradeDate!=null && upgradeDate instanceof Date){
				SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
				date = formatter.format(upgradeDate);
			}else {
				date = (String) upgradeDate;
			}
			if(null  == hrmsWarning && remainingDays >= 0) {
				HrmsWarningRecord hrmsWarningRecord = new HrmsWarningRecord();
				hrmsWarningRecord.setId(IdGeneraterUtils.nextId());
				hrmsWarningRecord.setWarningTitle(hrmsWarningSetting.getWarningTitle());
				hrmsWarningRecord.setSsoOrgCode(hrmsWarningSetting.getSsoOrgCode());
				hrmsWarningRecord.setCreateDate(new Date());
				hrmsWarningRecord.setDueDate(date);
				hrmsWarningRecord.setWarningStatus("0");
				hrmsWarningRecord.setEmpCode((String) map.get("employee_no"));
				hrmsWarningRecord.setEmpDept((String) map.get("org_id"));
				hrmsWarningRecord.setEmpDeptName((String) map.get("deptName"));
				if(null != map.get("employee_name") && "" != map.get("employee_name")) {
					hrmsWarningRecord.setEmpName((String) map.get("employee_name"));
					hrmsWarningRecord.setEmpNamePinyin(PinyinUtil.getFirstLetter((String) map.get("employee_name"),""));
				}
				record.setWarningStatus("0");
				hrmsWarningRecord.setEmpPhone((String) map.get("phone_number"));
				hrmsWarningRecord.setEmpSex((String) map.get("gender"));
				hrmsWarningRecord.setEmpId((String) map.get("employee_id"));
				hrmsWarningRecord.setIsDeleted("N");
				hrmsWarningRecord.setSurplusDate(String.valueOf(remainingDays));
				hrmsWarningRecord.setOverdueDays(String.valueOf(oerdueDays));
				hrmsWarningRecord.setUpdateDate(new Date());
				hrmsWarningRecord.setSettingId(hrmsWarningSetting.getId());
				if(map.containsKey("remark")) {
					hrmsWarningRecord.setRemark((String) map.get("remark"));
				}
				insertWarningRecordList.add(hrmsWarningRecord);
			}
			if(null  != hrmsWarning) {
				hrmsWarning.setSurplusDate(String.valueOf(remainingDays));
				hrmsWarning.setOverdueDays(String.valueOf(oerdueDays));
				hrmsWarning.setUpdateDate(new Date());
				hrmsWarning.setDueDate(date);
				if(remainingDays <= -1){
					hrmsWarning.setWarningStatus("1");
                    hrmsWarning.setSurplusDate("0");
				}
				hrmsWarning.setSsoOrgCode(hrmsWarningSetting.getSsoOrgCode());
				updateWarningRecordList.add(hrmsWarning);
			}
		}
		
		if(insertWarningRecordList.size() > 0) {
			hrmsWarningRecordMapper.batchInsert(insertWarningRecordList);
		}
		if(updateWarningRecordList.size() > 0) {
			hrmsWarningRecordMapper.batchUpdate(updateWarningRecordList);
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void remindWarningSettingData() {
		
		List<Map<String,Object>> resultList = new ArrayList<>();
		Map<String,Object> params = new HashMap<>();
		
		params.put("warningCycle", "每天");
		List<Map<String,Object>> dayList = mapper.remindWarningSettingData(params);
		resultList.addAll(dayList);
		
		params.put("warningCycle", "每周");
		List<Map<String,Object>> weekList = mapper.remindWarningSettingData(params);
		resultList.addAll(weekList);
		
		params.put("warningCycle", "每月");
		List<Map<String,Object>> monthList = mapper.remindWarningSettingData(params);
		resultList.addAll(monthList);
		
		params.put("warningCycle", "每年");
		List<Map<String,Object>> yearList = mapper.remindWarningSettingData(params);
		resultList.addAll(yearList);
		
		params.put("warningCycle", "一次性");
		List<Map<String,Object>> oneList = mapper.remindWarningSettingData(params);
		resultList.addAll(oneList);
		
		if(resultList.size() > 0) {
			for (Map<String, Object> map : resultList) {
				
				Map<String,Object> templateMap = new HashMap<>();
				templateMap.put("预警类型名称", map.get("warning_title")); 
				templateMap.put("预警记录数", String.valueOf((Long)map.get("notice_numbers")));
				String content = CommonUtils.getTemplateText(templateMap,(String) map.get("notice_template"));
				
				String notice_type = (String) map.get("notice_type");
				
				if(notice_type.contains("1")) {
					NoticeReq notice = NoticeReq.builder()
							.content(content)
							.noticeType("3")
							.receiver((String) map.get("notice_user"))
							.sender("admin")
							.senderName("系统管理员")
							.subject((String) map.get("warning_title"))
							.toUrl("/warning")
							.wxSendType("2")
							.source("预警提醒")
							.build();
					informationFeignService.sendNotice(notice);
				}
				
				if(notice_type.contains("2")) {
					NoticeReq notice = NoticeReq.builder()
							.content(content)
							.noticeType("1")
							.receiver((String) map.get("notice_user"))
							.sender("admin")
							.senderName("系统管理员")
							.subject((String) map.get("warning_title"))
							.toUrl("/warning")
							.wxSendType("2")
							.source("预警提醒")
							.build();
					informationFeignService.sendNotice(notice);
				}
			}
			
			if(oneList.size() > 0) {//一次性提醒  提醒完后删除
				for (Map<String, Object> map : oneList) {
					HrmsWarningSetting hrmsWarningSetting = new HrmsWarningSetting();
					hrmsWarningSetting.setId((String) map.get("setting_id"));
					hrmsWarningSetting.setIsDeleted("Y");
					mapper.updateByPrimaryKeySelective(hrmsWarningSetting);
				}
			}
		}
		
	}
}

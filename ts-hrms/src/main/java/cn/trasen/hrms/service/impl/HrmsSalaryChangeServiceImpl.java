package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsSalaryChangeMapper;
import cn.trasen.hrms.dao.HrmsSalaryItemMapper;
import cn.trasen.hrms.model.HrmsSalaryChange;
import cn.trasen.hrms.model.HrmsSalaryItem;
import cn.trasen.hrms.service.HrmsSalaryChangeService;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;

/**   
 * @ClassName:  HrmsSalaryChangeServiceImpl   
 * @Description:薪酬值改变实现类
 * @author: WZH
 * @date:   2021年10月28日 下午3:58:18      
 * @Copyright:  
 */
@Service
public class HrmsSalaryChangeServiceImpl implements HrmsSalaryChangeService {
	
	@Autowired
	private HrmsSalaryChangeMapper hrmsSalaryChangeMapper;
	
	@Autowired
	HrmsSalaryItemMapper hrmsSalaryItemMapper;
	
	@Transactional
	@Override
	public int updateOrInsert(HrmsSalaryChange entity) {
		
		entity.setSalaryChangeId(IdUtil.getId());
		/*HrmsSalaryChange del = new HrmsSalaryChange();
		del.setEmployeeId(entity.getEmployeeId());
		del.setSalaryItemCode(entity.getSalaryItemCode());
		del.setSalaryChangeTime(entity.getSalaryChangeTime());
		hrmsSalaryChangeMapper.delete(del);*/
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		
		//获取项目中文名称
		HrmsSalaryItem selectOne = 	hrmsSalaryItemMapper.selectByPrimaryKey(entity.getSalaryItemCode());
		entity.setSalaryItemName(selectOne.getSalaryItemName());
		hrmsSalaryChangeMapper.insert(entity);
		return 1;
	}

	@Override
	public List<HrmsSalaryChange> getList(HrmsSalaryChange entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");
		Assert.hasText(entity.getSalaryChangeTime(), "salaryChangeTime must be not null.");
		Example example = new Example(HrmsSalaryChange.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("employeeId", entity.getEmployeeId());
		example.and().andEqualTo("salaryChangeTime", entity.getSalaryChangeTime());
		return hrmsSalaryChangeMapper.selectByExample(example);
	}

	@Override
	public int deleteByDate(String payrollDate) {
		return hrmsSalaryChangeMapper.deleteByDate(payrollDate);
	}

}

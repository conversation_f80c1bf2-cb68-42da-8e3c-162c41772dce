package cn.trasen.hrms.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.date.DateUtil;
import cn.trasen.BootComm.utils.ApplicationUtils;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.feign.message.NoticeService;
import cn.trasen.hrms.dao.HrmsEmployeeBecomeMapper;
import cn.trasen.hrms.model.CustEmpBase;
import cn.trasen.hrms.model.HrmsEmployeeBecome;
import cn.trasen.hrms.service.CustEmpBaseService;
import cn.trasen.hrms.service.HrmsEmployeeBecomeService;
import cn.trasen.hrms.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsEmployeeBecomeServiceImpl
 * @Description TODO
 * @date 2021��9��15�� ����2:50:33
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsEmployeeBecomeServiceImpl implements HrmsEmployeeBecomeService {

	@Autowired
	private HrmsEmployeeBecomeMapper mapper;
	
	@Autowired
	private CustEmpBaseService custEmpBaseService;
	
	@Autowired
	private GlobalSettingsFeignService globalSettingsFeignService;
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsEmployeeBecome record) {
		record.setId(ApplicationUtils.GUID32());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		// 转正 & 离职
		
		if("1".equals(record.getChangeStatus())) {
			//修改员工状态为在职
			if(StringUtils.isNotBlank(record.getEmployeeNo())) {
				
				CustEmpBase custEmpBase = custEmpBaseService.findEmployeeByCode(record.getEmployeeNo());
				
				custEmpBase.setEmployeeStatus("1");
				
				custEmpBaseService.update(custEmpBase);
			}
		}else if("2".equals(record.getChangeStatus())) {
			//修改员工状态为在职
			if(StringUtils.isNotBlank(record.getEmployeeNo())) {
				
				CustEmpBase custEmpBase = custEmpBaseService.findEmployeeByCode(record.getEmployeeNo());
				
				custEmpBase.setEmployeeStatus("4");  //离职
				
				custEmpBaseService.update(custEmpBase);
			}
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsEmployeeBecome record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsEmployeeBecome record = new HrmsEmployeeBecome();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsEmployeeBecome selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public List<HrmsEmployeeBecome> getList(Page page, HrmsEmployeeBecome record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getGlobalSetting("Y");
		String orgCode = globalSetting.getObject().getOrgCode();
		
		Example example = new Example(HrmsEmployeeBecome.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsEmployeeBecome> records = new ArrayList<HrmsEmployeeBecome>();
		if(null==record.getStatus() || record.getStatus()==1) {
			
			if("csjkyy".equals(orgCode)) {
				records = mapper.getToBeConfirmedByCsjkyyList(page,record);
			}else {
				records = mapper.getToBeConfirmedList(page,record);
			}
			if(CollectionUtils.isNotEmpty(records)) {
				SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
				for(HrmsEmployeeBecome item : records) {
					String estimateDate = "";
					if("lyszyyy".equals(orgCode)){
						if(null!=item.getEntryDate() && null!=item.getEducationType()) {
							if("2".equals(item.getEducationType())|| "3".equals(item.getEducationType()) || "4".equals(item.getEducationType())
									|| "5".equals(item.getEducationType())
									|| "6".equals(item.getEducationType())) {
								
								//本科及以下学历两个月试用期
								estimateDate = DateUtils.getMonthDifferDate(item.getSyksrq(), 2);
								item.setEstimateDate(estimateDate);
							}else if("1".equals(item.getEducationType())|| "7".equals(item.getEducationType()) || "10".equals(item.getEducationType())){
								//本科以上学历一个月试用期
								estimateDate = DateUtils.getMonthDifferDate(item.getSyksrq(), 1);
								item.setEstimateDate(estimateDate);
							}
						}
					}else{
						estimateDate = DateUtil.format(item.getPositiveTime(), "yyyy-MM-dd");
						item.setEstimateDate(estimateDate);
					}
					
					if(StringUtils.isNotBlank(estimateDate)) {
						
						try {
							int days = DateUtils.getDifferDays(new Date(), sf.parse(estimateDate));
							item.setCountDownDays(days+"天");
						} catch (ParseException e) {
							log.error("时间转换异常"+e.getMessage(),e);
						}
					}
					
				}
			}
			
		}else  {  //已转正
			if(record.getStatus() == 2){
				record.setChangeStatus("1");
			}else if(record.getStatus() == 3){
				record.setChangeStatus("2");
			}
			records = mapper.getAlreadyComeList(page,record);
		}
		
		return records;
	}
	
	@Transactional(readOnly = false)
	public void autoBecome() {
		Page page = new Page();
		page.setPageSize(Integer.MAX_VALUE);
		HrmsEmployeeBecome record = new HrmsEmployeeBecome();
		record.setStatus(1);
		List<HrmsEmployeeBecome> list = getList(page, record);
		
		String nowDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
		
		for(HrmsEmployeeBecome item : list) {
			
			if(StringUtils.isNotBlank(item.getEstimateDate()) && nowDate.equals(item.getEstimateDate())) {
				
				record.setId(ApplicationUtils.GUID32());
				record.setCreateDate(new Date());
				record.setUpdateDate(new Date());
				record.setIsDeleted("N");
				record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				//修改员工状态为在职
				if(StringUtils.isNotBlank(record.getEmployeeNo())) {
					
					CustEmpBase custEmpBase = custEmpBaseService.findEmployeeByCode(record.getEmployeeNo());
					
					custEmpBase.setEmployeeStatus("1"); 
					
					custEmpBaseService.update(custEmpBase);
				}
				
				mapper.insertSelective(record);
			}
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void saveHrmsEmployeeBecome(HrmsEmployeeBecome record) {
		
		CustEmpBase custEmpBase = custEmpBaseService.findEmployeeByCode(record.getEmployeeNo());
		
		record.setId(ApplicationUtils.GUID32());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		
		record.setEmployeeName(custEmpBase.getEmployeeName());
		record.setApplyDeptId(custEmpBase.getOrgId());
		record.setApplyDeptName(custEmpBase.getOrgName());
		record.setChangeStatus("1");
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		
		mapper.insertSelective(record);
		
		custEmpBase.setEmployeeStatus("1");
		custEmpBaseService.update(custEmpBase);
		
	}

	@Override
	public void employeeBeComeMessage() {
		PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getGlobalSetting("Y");
		String orgCode = globalSetting.getObject().getOrgCode();
		if("csjkyy".equals(orgCode)) {
			Page page = new Page();
			page.setPageSize(10000);
			page.setPageNo(1);
			page.setSidx("create_date");
			page.setSord("desc");
			
			HrmsEmployeeBecome record = new HrmsEmployeeBecome();
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			List<HrmsEmployeeBecome> records = mapper.getToBeConfirmedByCsjkyyList(page,record);
			
			if(CollectionUtils.isNotEmpty(records)){
				for(HrmsEmployeeBecome item : records) {
					SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
					String estimateDate = "";
					if(null!=item.getEntryDate() && null!=item.getEducationType()) {
						if("2".equals(item.getEducationType())|| "3".equals(item.getEducationType()) || "4".equals(item.getEducationType())
								|| "5".equals(item.getEducationType())
								|| "6".equals(item.getEducationType())) {
							
							//本科及以下学历两个月试用期
							estimateDate = DateUtils.getMonthDifferDate(item.getSyksrq(), 2);
							item.setEstimateDate(estimateDate);
						}else if("1".equals(item.getEducationType())|| "7".equals(item.getEducationType()) || "10".equals(item.getEducationType())){
							//本科以上学历一个月试用期
							estimateDate = DateUtils.getMonthDifferDate(item.getSyksrq(), 1);
							item.setEstimateDate(estimateDate);
						}
					}
					
					if(StringUtils.isNotBlank(estimateDate)) {
						int days = 0;
						try {
							days = DateUtils.getDifferDays(new Date(), sf.parse(estimateDate));
						} catch (ParseException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
						
						if(days == 3){
							NoticeReq noticeVo = NoticeReq.builder()
									.content("尊敬的" + item.getEmployeeName()+",您好！祝贺您即将结束试用期，请尽快登录OA提交试用期阶段性考核评估表哦！")
									.noticeType("3")
									.subject("转正提醒")
									.sender("admin")
									.senderName("admin")
									.receiver(item.getEmployeeNo())
									.wxSendType("2")
									.build();
							NoticeService.sendAsynNotice(noticeVo);
						}
					}
					
				}
			}
			
		}
	}
}

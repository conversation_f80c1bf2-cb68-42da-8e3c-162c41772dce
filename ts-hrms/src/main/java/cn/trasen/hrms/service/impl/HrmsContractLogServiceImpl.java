package cn.trasen.hrms.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsContractLogMapper;
import cn.trasen.hrms.model.HrmsContractLog;
import cn.trasen.hrms.service.HrmsContractLogService;
import cn.trasen.hrms.utils.IdUtil;

/**
 * <AUTHOR>
 * 合同操作记录实现类
 *
 */
@Service
public class HrmsContractLogServiceImpl implements HrmsContractLogService {
	
	@Autowired
	private HrmsContractLogMapper hrmsContractLogMapper;

	@Override
	public int insert(HrmsContractLog entity) {
		entity.setId(IdUtil.getId());
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsContractLogMapper.insertSelective(entity);
	}

	/* (non-Javadoc)
	 * 操作记录列表
	 */
	@Override
	public List<HrmsContractLog> getDataList(Page page, HrmsContractLog entity) throws Exception {
     
    	if(!StringUtil.isEmpty(entity.getActionType())) {
			List<String> asList = Arrays.asList(entity.getActionType().split(","));
			String str = "";
			for(int i =0 ;i<asList.size();i++) {
				str += "'"+ asList.get(i) +"',";
			}
			str = str.substring(0,str.length()-1);
			entity.setActionType(str);
		}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
    	return hrmsContractLogMapper.getDataList(page,entity);
	}

}

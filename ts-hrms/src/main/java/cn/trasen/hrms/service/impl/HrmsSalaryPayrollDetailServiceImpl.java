package cn.trasen.hrms.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.hrms.dao.HrmsSalaryPayrollDetailMapper;
import cn.trasen.hrms.model.HrmsSalaryPayroll;
import cn.trasen.hrms.model.HrmsSalaryPayrollDetail;
import cn.trasen.hrms.service.HrmsSalaryPayrollDetailService;

/**   
 * @Title: HrmsSalaryPayrollDetailServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 薪酬工资明细 业务层接口
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月7日 下午5:27:39 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsSalaryPayrollDetailServiceImpl implements HrmsSalaryPayrollDetailService {

	@Autowired
	HrmsSalaryPayrollDetailMapper hrmsSalaryPayrollDetailMapper;

	/**
	 * @Title: batchInsert
	 * @Description: 批量新增
	 * @param list
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年4月7日 下午5:28:45
	 */
	@Override
	public int batchInsert(List<HrmsSalaryPayrollDetail> list) {
		return hrmsSalaryPayrollDetailMapper.batchInsert(list);
	}

	/**
	 * @Title: getPayrollDetailList
	 * @Description: 查询工资明细列表
	 * @param entity
	 * @Return List<HrmsSalaryPayrollDetail>
	 * <AUTHOR>
	 * @date 2020年4月9日 上午11:31:25
	 */
	@Override
	public List<HrmsSalaryPayrollDetail> getPayrollDetailList(HrmsSalaryPayrollDetail entity) {
		Assert.hasText(entity.getSalaryPayrollId(), "salaryPayrollId must be not null.");

		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		return hrmsSalaryPayrollDetailMapper.getPayrollDetailList(entity);
	}

	/**
	 * @Title: getSalaryPayrollDetailList
	 * @Description: 查询员工薪资明细记录
	 * @param entity
	 * @Return List<HrmsSalaryPayrollDetail>
	 * <AUTHOR>
	 * @date 2020年5月8日 上午10:13:13
	 */
	@Override
	public List<HrmsSalaryPayrollDetail> getSalaryPayrollDetailList(HrmsSalaryPayroll entity) {
		return hrmsSalaryPayrollDetailMapper.getSalaryPayrollDetailList(entity);
	}

	/**
	 * @Title: updatePayrollDetail
	 * @Description: 更新工资数据
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年7月1日 上午9:58:01
	 */
	@Override
	@Transactional(readOnly = false)
	public int updatePayrollDetail(HrmsSalaryPayrollDetail entity) {
		return hrmsSalaryPayrollDetailMapper.updatePayrollDetail(entity);
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteTableData() {
		hrmsSalaryPayrollDetailMapper.deleteTableData();
	}
	
	//根据月份删除明细
	@Override
	public int deleteByPayrOllDate(String payrollDate) {
		// TODO Auto-generated method stub
		return hrmsSalaryPayrollDetailMapper.deleteByPayrOllDate(payrollDate);
	}

}

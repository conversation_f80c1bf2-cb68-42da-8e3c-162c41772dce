package cn.trasen.hrms.service.impl;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsFixedAttendanceRuleMapper;
import cn.trasen.hrms.model.HrmsFixedAttendanceRule;
import cn.trasen.hrms.service.HrmsFixedAttendanceRuleService;
import tk.mybatis.mapper.entity.Example;

/**
  * @Description: 固定考勤规则业务层接口实现类
  * @Date: 2020年4月27日  上午11:28:14 
  * @Author: Z 
  * @Company: 湖南创星科技股份有限公司 
  * @version V1.0
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsFixedAttendanceRuleServiceImpl implements HrmsFixedAttendanceRuleService {

	@Autowired
	HrmsFixedAttendanceRuleMapper hrmsFixedAttendanceRuleMapper;

	/**
	 * @Title: getFixedAttendanceRule
	 * @Description: 获取固定考勤规则
	 * @Return HrmsFixedAttendanceRule
	 * <AUTHOR>
	 * @date 2020年4月27日 上午11:35:25
	 */
	@Override
	public HrmsFixedAttendanceRule getFixedAttendanceRule() {
		Example example = new Example(HrmsFixedAttendanceRule.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		return hrmsFixedAttendanceRuleMapper.selectOneByExample(example);
	}

	/**   
	 * <p>Title: insert</p>   
	 * <p>Description: 新增</p>   
	 * @param hrmsFixedAttendanceRule
	 * @return   
	 * @see cn.trasen.hrms.service.HrmsFixedAttendanceRuleService#insert(cn.trasen.hrms.model.HrmsFixedAttendanceRule)   
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsFixedAttendanceRule entity) {
		entity.setFixedAttendanceRuleId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateDate(new Date());
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsFixedAttendanceRuleMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 修改固定考勤规则
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年4月27日 上午11:53:30
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsFixedAttendanceRule entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsFixedAttendanceRuleMapper.updateByPrimaryKeySelective(entity);
	}

}

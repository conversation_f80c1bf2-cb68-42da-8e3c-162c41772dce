package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsVoluntaryMapper;
import cn.trasen.hrms.model.HrmsVoluntary;
import cn.trasen.hrms.service.HrmsVoluntaryService;



/**    
  * <P> @Description: 志愿者完成情况serviceImpl</p>
  * <P> @Date: 2020年8月24日  下午4:43:16 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsVoluntaryServiceImpl implements HrmsVoluntaryService {

	@Autowired
	HrmsVoluntaryMapper hrmsVoluntaryMapper;
	
	@Transactional(readOnly = false)
	@Override
	public int deleted(String id) {
		return hrmsVoluntaryMapper.deleteByPrimaryKey(id);
	}
	
	@Override
	public List<HrmsVoluntary> getDataList(Page page, HrmsVoluntary entity) {
		if(!StringUtil.isEmpty(entity.getEmployeeName())) {
			entity.setEmployeeName(entity.getEmployeeName().trim());
		}
		if(!StringUtil.isEmpty(entity.getEmployeeDept())) {
			entity.setEmployeeDept(entity.getEmployeeDept().trim());
		}
		if(!StringUtil.isEmpty(entity.getEmployeeNo())) {
			entity.setEmployeeNo(entity.getEmployeeNo().trim());
		}
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsVoluntaryMapper.getDataList(entity, page);
	}
	
	@Transactional(readOnly = false)
	@Override
	public String excelImport(List<HrmsVoluntary> list) {
		Map<String, Object> param = new HashMap<>();
		param.put("list", list);
		param.put("create_date", new Date());
		param.put("create_user", UserInfoHolder.getCurrentUserCode());
		param.put("create_user_name", UserInfoHolder.getCurrentUserName());
		int re =  hrmsVoluntaryMapper.batchInsert(param);
		return re == 0 ? "0" : String.valueOf(re);
	}

	@Override
	public List<HrmsVoluntary> getDataCountList(Page page, HrmsVoluntary entity) {
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsVoluntaryMapper.getDataCountList(entity, page);
	}

	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsVoluntary entity) {
		if(StringUtil.isEmpty(entity.getId())) {
			entity.setId(null);
		}
		entity.setCreateDate(new Date());
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setEmployeeDept(entity.getOldOrgName());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsVoluntaryMapper.insertSelective(entity);
//		 return hrmsVoluntaryMapper.insertVoluntary(entity);
	}

	@Override
	@Transactional(readOnly = false)
	public int update(HrmsVoluntary entity) {
		
		entity.setUpdateDate(new Date());
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		return hrmsVoluntaryMapper.updateByPrimaryKeySelective(entity);
	}


}

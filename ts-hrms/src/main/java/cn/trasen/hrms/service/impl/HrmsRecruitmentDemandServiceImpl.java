package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsRecruitmentDemandMapper;
import cn.trasen.hrms.enums.PriorityTypeEnum;
import cn.trasen.hrms.model.HrmsRecruitmentDemand;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsRecruitmentDemandService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsRecruitmentDemandServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 招聘需求 业务层接口实现类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月21日 下午3:24:58 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsRecruitmentDemandServiceImpl implements HrmsRecruitmentDemandService {

	@Autowired
	HrmsRecruitmentDemandMapper hrmsRecruitmentDemandMapper;
	@Autowired
	HrmsDictInfoService hrmsDictInfoService;

	/**
	 * @Title: insert
	 * @Description: 新增招聘需求
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsRecruitmentDemand entity) {
		entity.setRecruitmentDemandId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		return hrmsRecruitmentDemandMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 更新招聘需求
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsRecruitmentDemand entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsRecruitmentDemandMapper.updateByPrimaryKeySelective(entity);
	}
	
	@Override
	@Transactional(readOnly = false)
	public int update(String recruitmentDemandId) {
		HrmsRecruitmentDemand entity = new HrmsRecruitmentDemand();
		entity.setIsExamine("1");
		entity.setRecruitmentDemandId(recruitmentDemandId);
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsRecruitmentDemandMapper.updateByPrimaryKeySelective(entity);
	}


	/**
	 * @Title: deleted
	 * @Description: 删除招聘需求
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsRecruitmentDemand demand = hrmsRecruitmentDemandMapper.selectByPrimaryKey(id);
		if (demand != null) {
			demand.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsRecruitmentDemandMapper.updateByPrimaryKey(demand);
	}

	/**
	 * @Title: getDataList
	 * @Description: 查询招聘需求列表(分页)
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsRecruitmentDemand>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsRecruitmentDemand> getDataList(Page page, HrmsRecruitmentDemand entity) {
		Example example = new Example(HrmsRecruitmentDemand.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

		if (StringUtils.isNotBlank(entity.getApplicantName())) { // 申请人姓名
			example.and().andLike("applicantName", "%" + entity.getApplicantName() + "%");
		}

		if (StringUtils.isNotBlank(entity.getRecruitmentPost())) { // 招聘岗位
			example.and().andLike("recruitmentPost", "%" + entity.getRecruitmentPost() + "%");
		}

		if (StringUtils.isNotBlank(entity.getPriority())) { // 优先级
			example.and().andEqualTo("priority", entity.getPriority());
		}

		if (StringUtils.isNotBlank(entity.getPostCategory())) { // 岗位类别
			example.and().andEqualTo("postCategory", entity.getPostCategory());
		}

		List<HrmsRecruitmentDemand> list = hrmsRecruitmentDemandMapper.selectByExampleAndRowBounds(example, page);
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsRecruitmentDemand demand : list) {
				demand.setPriorityText(PriorityTypeEnum.getValByKey(demand.getPriority())); // 优先级文本值
			}
		}
		return list;
	}

}

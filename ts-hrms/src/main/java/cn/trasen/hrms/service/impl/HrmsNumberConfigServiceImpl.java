package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsNumberConfigMapper;
import cn.trasen.hrms.enums.NumberConfigEnum;
import cn.trasen.hrms.model.HrmsNumberConfig;
import cn.trasen.hrms.service.HrmsNumberConfigService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsEmployeeNumberConfigServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 工号配置规则 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月17日 下午4:59:30 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsNumberConfigServiceImpl implements HrmsNumberConfigService {

	@Autowired
	private HrmsNumberConfigMapper hrmsNumberConfigMapper;

	/**
	 * @Title: insert
	 * @Description: 新增编号规则
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsNumberConfig entity) {
		entity.setNumberConfigId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		return hrmsNumberConfigMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 更新编号规则
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsNumberConfig entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsNumberConfigMapper.updateByPrimaryKeySelective(entity);
	}

	/**
	 * @Title: deleted
	 * @Description: 删除编号规则
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsNumberConfig numberConfig = hrmsNumberConfigMapper.selectByPrimaryKey(id);
		if (numberConfig != null) {
			numberConfig.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsNumberConfigMapper.updateByPrimaryKeySelective(numberConfig);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取编号规则列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsNumberConfig>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsNumberConfig> getDataList(Page page, HrmsNumberConfig entity) {
		Example example = new Example(HrmsNumberConfig.class);
		example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
		if(!StringUtil.isEmpty(entity.getRemark())) {
			example.and().andLike("remark", "%" + entity.getRemark() + "%");
		}

		List<HrmsNumberConfig> list = hrmsNumberConfigMapper.selectByExampleAndRowBounds(example, page);
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsNumberConfig conf : list) {
				conf.setConfigCategoryText(NumberConfigEnum.getValByKey(conf.getConfigCategory())); // 配置类别文本值
			}
		}
		return list;
	}

	/**
	 * 1. 根据配置规则组装生成员工工号
	 * 2. 生成工号后，更新初始值
	 * @Title: generatorEmployeeNumber
	 * @Description: 生成员工工号
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public String generatorEmployeeNumber() {
		String employeeNumber = "";
		Example example = new Example(HrmsNumberConfig.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("configCategory", NumberConfigEnum.EMPLOYEE_NO_CONF.getKey());
		
		HrmsNumberConfig config = hrmsNumberConfigMapper.selectOneByExample(example);
		if (config != null) {
			Integer digits = config.getSerialDigits(); // 序号位数
			String initialNumber = config.getInitialNumber(); // 序号初始值

			if (StringUtils.isNotBlank(initialNumber)) { // 初始值不为空
				if (digits != null && initialNumber.length() < digits.intValue()) { // 初始值位数小于序号位数
					employeeNumber = config.getSerialPrefix() + StringUtils.leftPad(initialNumber, digits.intValue(), "0") + config.getSerialSuffix();
				} else {
					employeeNumber = config.getSerialPrefix() + initialNumber + config.getSerialSuffix();
				}
				// 更新初始值
				config.setInitialNumber(String.valueOf(Integer.valueOf(initialNumber) + 1));
				hrmsNumberConfigMapper.updateByPrimaryKeySelective(config);
			}
		}
		return employeeNumber;
	}

	/**
	 * @Title: validate
	 * @Description: 数据校验
	 * @param entity
	 * @Return PlatformResult<String>
	 * <AUTHOR>
	 * @date 2020年5月26日 上午10:21:22
	 */
	@Override
	public PlatformResult<String> validate(HrmsNumberConfig entity) {
		Assert.hasText(entity.getConfigCategory(), "configCategory must be not null.");

		Example example = new Example(HrmsNumberConfig.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("configCategory", entity.getConfigCategory());

		if (StringUtils.isNotBlank(entity.getNumberConfigId())) { // 编号规则ID
			example.and().andNotEqualTo("numberConfigId", entity.getNumberConfigId());
		}
		if (hrmsNumberConfigMapper.selectByExample(example).size() > 0) {
			return PlatformResult.failure("已存在" + NumberConfigEnum.getValByKey(entity.getConfigCategory()) + "的编号规则");
		}
		return PlatformResult.success();
	}
}

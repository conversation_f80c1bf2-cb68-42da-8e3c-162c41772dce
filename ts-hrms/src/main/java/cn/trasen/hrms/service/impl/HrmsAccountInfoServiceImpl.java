package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsAccountInfoMapper;
import cn.trasen.hrms.model.HrmsAccountInfo;
import cn.trasen.hrms.service.HrmsAccountInfoService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsAccountInfoServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 员工账号信息 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月11日 下午3:32:24 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsAccountInfoServiceImpl implements HrmsAccountInfoService {

	@Autowired
	HrmsAccountInfoMapper hrmsAccountInfoMapper;

	/**
	 * @Title: insert
	 * @Description: 新增账号信息
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsAccountInfo entity) {
		entity.setAccountInfoId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
		entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsAccountInfoMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 更新账号信息
	 * @Param: accountInfo
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsAccountInfo entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsAccountInfoMapper.updateByPrimaryKeySelective(entity);
	}

	/**
	 * @Title: deleted
	 * @Description: 删除账号信息
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsAccountInfo accountInfo = hrmsAccountInfoMapper.selectByPrimaryKey(id);
		if (accountInfo != null) {
			accountInfo.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsAccountInfoMapper.updateByPrimaryKeySelective(accountInfo);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取账号信息列表
	 * @Param: page
	 * @param: accountInfo
	 * @Return: List<HrmsAccountInfo>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsAccountInfo> getDataList(Page page, HrmsAccountInfo accountInfo) {
		Assert.hasText(accountInfo.getEmployeeId(), "employeeId must be not null.");
		
		Example example = new Example(HrmsAccountInfo.class);
		example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("employeeId", accountInfo.getEmployeeId());
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(accountInfo.getAccountType())) { // 帐号类型
			example.and().andEqualTo("account_type", accountInfo.getAccountType());
		}
		
		return hrmsAccountInfoMapper.selectByExampleAndRowBounds(example, page);
	}

}

package cn.trasen.hrms.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.hrms.dao.HrmsOrgOperateRecordMapper;
import cn.trasen.hrms.model.HrmsOrgOperateRecord;
import cn.trasen.hrms.service.HrmsOrgOperateRecordService;

/**   
 * @Title: HrmsOrgOperateRecordServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 机构操作记录 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年6月23日 上午10:44:26 
 * @version V1.0   
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsOrgOperateRecordServiceImpl implements HrmsOrgOperateRecordService {
	
	@Autowired
	HrmsOrgOperateRecordMapper hrmsOrgOperateRecordMapper;

	/**
	 * @Title: batchInsert
	 * @Description: 批量插入
	 * @param list
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月23日 上午11:27:58
	 */
	@Override
	public int batchInsert(List<HrmsOrgOperateRecord> list) {
		return hrmsOrgOperateRecordMapper.batchInsert(list);
	}

}

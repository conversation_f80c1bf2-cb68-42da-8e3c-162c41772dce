package cn.trasen.hrms.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.model.CustEmpBase;
import cn.trasen.hrms.model.HrmsEmployee;

/**
 * @ClassName CustEmpBaseService
 * @Description TODO
 * @date 2024��10��15�� ����3:00:23
 * <AUTHOR>
 * @version 1.0
 */
public interface CustEmpBaseService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��10��15�� ����3:00:23
	 * <AUTHOR>
	 */
	Integer save(CustEmpBase record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��10��15�� ����3:00:23
	 * <AUTHOR>
	 */
	Integer update(CustEmpBase record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��10��15�� ����3:00:23
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CustEmpBase
	 * @date 2024��10��15�� ����3:00:23
	 * <AUTHOR>
	 */
	CustEmpBase selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CustEmpBase>
	 * @date 2024��10��15�� ����3:00:23
	 * <AUTHOR>
	 */
	DataSet<CustEmpBase> getDataSetList(Page page, CustEmpBase record);
	List<CustEmpBase> getDataList(CustEmpBase record);

	CustEmpBase findEmployeeByCode(String employeeNo);

	Set<String> findAllEmployeeId();

	void initEmployee(HrmsEmployee hrmsEmployee);

	void updateCodePwd(HrmsEmployee hrmsEmployee);
	
	void updateByIdentityNumber(Map<String, Object> data);
}

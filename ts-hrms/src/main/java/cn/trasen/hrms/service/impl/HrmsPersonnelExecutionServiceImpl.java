package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsPersonnelExecutionMapper;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsPersonnelExecution;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.service.HrmsPersonnelExecutionService;
import cn.trasen.hrms.utils.DateUtils;

/**
 * 人事事件定时任务实现类
 * <AUTHOR>
 *
 */
@Service
public class HrmsPersonnelExecutionServiceImpl implements HrmsPersonnelExecutionService {
	
	
	@Autowired
	private HrmsPersonnelExecutionMapper hrmsPersonnelExecutionMapper;
	
	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;
	

	/* (non-Javadoc)
	 * 添加人事事件定时任务
	 */
	@Override
	public int insert(HrmsPersonnelExecution entity) {
		entity.setId(String.valueOf(IdWork.id.nextId()));
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setStopEvent("0");  //默认未执行
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		int row = hrmsPersonnelExecutionMapper.insertSelective(entity);
		return row;
	}

	/* (non-Javadoc)
	 * 修改人事事件定时任务
	 */
	@Override
	public int update(HrmsPersonnelExecution entity) {
		entity.setOccurrenceTime(new Date());
		return hrmsPersonnelExecutionMapper.updateByPrimaryKeySelective(entity);
	}

	/* (non-Javadoc)
	 * 修改人事事件定时任务
	 */
	@Override
	public int deleted(String id) {
		// TODO Auto-generated method stub
		return 0;
	}

	/* (non-Javadoc)
	 * 查询人事事件定时任务列表
	 */
	@Override
	public List<HrmsPersonnelExecution> getList(HrmsPersonnelExecution entity) {
		entity.setExecutionTime(DateUtils.getStringDateShort(new Date()));
		return hrmsPersonnelExecutionMapper.getList(entity);
	}

	/* (non-Javadoc)
	 * 定时任务执行修改员工状态
	 * @see cn.trasen.hrms.service.HrmsPersonnelExecutionService#personnelExecutionJob()
	 */
	@Transactional
	@Override
	public void personnelExecutionJob() {
		HrmsPersonnelExecution entity = new HrmsPersonnelExecution();
		List<HrmsPersonnelExecution> list = getList(entity);
		if(list != null && list.size() > 0) {
			for (int i = 0; i < list.size(); i++) {
				HrmsEmployee empBean = new HrmsEmployee();
				empBean.setEmployeeId(list.get(i).getEmployeeId());
				empBean.setEmployeeStatus(list.get(i).getNewEmployeeStatus());
				if("退休".equals(empBean.getRemark())) {
					empBean.setIsRetire("1");  //退休事件设置为已退休过
					empBean.setIsEnable("0");//停用
				}
				int row = hrmsEmployeeService.update(empBean);  //修改员工
				if(row <= 1) {
					HrmsPersonnelExecution record = new HrmsPersonnelExecution();
					record.setId(list.get(i).getId());
					record.setStopEvent("1");
					record.setOccurrenceTime(new Date());
					hrmsPersonnelExecutionMapper.updateByPrimaryKeySelective(record);	//执行记录数据
				}
			}
		}
	}

}

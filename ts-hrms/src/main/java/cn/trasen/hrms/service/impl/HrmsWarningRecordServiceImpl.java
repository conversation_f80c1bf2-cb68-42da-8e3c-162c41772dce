package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsWarningRecordMapper;
import cn.trasen.hrms.model.HrmsWarningRecord;
import cn.trasen.hrms.service.HrmsWarningRecordService;
import cn.trasen.hrms.utils.CommonUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @ClassName HrmsWarningRecordServiceImpl
 * @Description TODO
 * @date 2021��10��14�� ����3:25:27
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsWarningRecordServiceImpl implements HrmsWarningRecordService {

	@Resource
	private HrmsWarningRecordMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsWarningRecord record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsWarningRecord record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsWarningRecord record = new HrmsWarningRecord();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsWarningRecord selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsWarningRecord> getDataSetList(Page page, HrmsWarningRecord record) {
//		Example example = new Example(HrmsWarningRecord.class);
//		Example.Criteria criteria = example.createCriteria();
//		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
//		criteria.andCondition(" emp_code in (select employee_no from cust_emp_base where is_deleted='N' and employee_status not in ('4','7','8'))");
//		if(StringUtils.isNotBlank(record.getEmpName())
//	        		&& !CommonUtils.isContainChinese(record.getEmpName())) {
//			criteria.andLike("empNamePinyin", "%" + record.getEmpName() + "%");
//	    }
//		if(StringUtils.isNotBlank(record.getEmpName()) && CommonUtils.isContainChinese(record.getEmpName())) {
//			criteria.andLike("empName", "%" + record.getEmpName() + "%");
//		}
//		if(StringUtils.isNotBlank(record.getEmpCode())) {
//			criteria.andLike("empCode", "%" + record.getEmpCode() + "%");
//		}
//		if(StringUtils.isNotBlank(record.getEmpSex())) {
//			criteria.andEqualTo("empSex", record.getEmpSex());
//		}
//		if(StringUtils.isNotBlank(record.getWarningTitle())) {
//			criteria.andEqualTo("warningTitle", record.getWarningTitle());
//		}
//		if(StringUtils.isNotBlank(record.getWarningStatus())) {
//			criteria.andEqualTo("warningStatus", record.getWarningStatus());
//		}
//		if(CollectionUtils.isNotEmpty(record.getWarningStatusList())) {
//			criteria.andIn("warningStatus", record.getWarningStatusList());
//		}
//		List<HrmsWarningRecord> records = mapper.selectByExampleAndRowBounds(example, page);
		
		if(!ObjectUtils.isEmpty(page.getSidx())){
			record.setSidx(page.getSidx());
			record.setSord(page.getSord());
		}
		if(StringUtils.isNotBlank(record.getEmpName())
        		&& !CommonUtils.isContainChinese(record.getEmpName())) {
			record.setEmpNamePinyin(record.getEmpName());
		}
		//根据当前登录账号机构编码过滤查询数据
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsWarningRecord> records = mapper.seletcList(page, record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public Long selectCountRecordByUserCode(String userCode) {
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		return mapper.selectCountRecordByUserCode(userCode,ssoOrgCode);
	}
}

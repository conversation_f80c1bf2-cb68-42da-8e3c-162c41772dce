package cn.trasen.hrms.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.bean.BeanUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsJobDescriptionMapper;
import cn.trasen.hrms.hlgl.service.CallHlglService;
import cn.trasen.hrms.model.HrmsJobDescription;
import cn.trasen.hrms.service.HrmsJobDescriptionService;
import cn.trasen.hrms.ywgl.server.CallYwglService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsJobDescriptionServiceImpl
 * @Description TODO
 * @date 2021��12��22�� ����5:24:49
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsJobDescriptionServiceImpl implements HrmsJobDescriptionService {

	@Autowired
	private HrmsJobDescriptionMapper mapper;
	
	@Autowired
	private CallHlglService callHlglService;
	
	@Autowired
	private CallYwglService callYwglService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsJobDescription record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsJobDescription record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsJobDescription record = new HrmsJobDescription();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsJobDescription selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsJobDescription> getDataSetList(Page page, HrmsJobDescription record) {
		Example example = new Example(HrmsJobDescription.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsJobDescription> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public HrmsJobDescription selectHrmsJobDescriptionByEmpId(String empId) {
		Example example = new Example(HrmsJobDescription.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andEqualTo("empId", empId);
		return mapper.selectOneByExample(example);
	}

	@Override
	@Transactional(readOnly = false)
	public void generateHrmsJobDescriptionData() {
		//查询护理系统岗位说明书数据
		List<Map<String, Object>> jobDescriptionList = callHlglService.selectAllPersonJobDescription();
		
		//查询医务系统岗位说明书数据
		List<Map<String, Object>> ywglJobDescriptionList = callYwglService.selectAllPersonJobDescription();
		
		//查询员工数据
		List<Map<String, Object>> hrmsEmployeeList = mapper.selectAllHrmsEmployee();
		
		List<HrmsJobDescription> addHrmsJobDescription = new ArrayList<>();
		
		for (Map<String, Object> jobDescription : jobDescriptionList) {
			for (Map<String, Object> hrmsEmployee : hrmsEmployeeList) {
				if(jobDescription.get("idcard").equals(hrmsEmployee.get("identity_number"))) {
					HrmsJobDescription record = new HrmsJobDescription();
					record.setEmpId((String) hrmsEmployee.get("employee_id"));
					record.setEmpCode((String) hrmsEmployee.get("employee_no"));
					record.setEmpName((String) hrmsEmployee.get("employee_name"));
					record.setEmpDept((String) hrmsEmployee.get("org_id"));
					record.setEmpDeptName((String) hrmsEmployee.get("name"));
					record.setJobLevel((String) jobDescription.get("nurseClassName"));
					record.setAttachedDept((String) jobDescription.get("appendantDeptName"));
					record.setJobType((String) jobDescription.get("careerSeriesName"));
					record.setEmpTitle((String) jobDescription.get("jobTypeName"));
					record.setDirectLeader((String) jobDescription.get("supervisorJobName"));
					record.setNurseLevel((String) jobDescription.get("nurseClassName"));
					record.setInternalRelations((String) jobDescription.get("consultReportRelation"));
					record.setClinicalNursing((String) jobDescription.get("clinicalNurseRelation"));
					record.setExternalRelations((String) jobDescription.get("externalRelation"));
					record.setJobDuties((String) jobDescription.get("jobResponsibilities"));
					record.setQualification((String) jobDescription.get("qualification"));
					
					addHrmsJobDescription.add(record);
				}
			}
		}
		
		for (Map<String, Object> jobDescription : ywglJobDescriptionList) {
			for (Map<String, Object> hrmsEmployee : hrmsEmployeeList) {
				if(jobDescription.get("idcard").equals(hrmsEmployee.get("identity_number"))) {
					HrmsJobDescription record = new HrmsJobDescription();
					record.setEmpId((String) hrmsEmployee.get("employee_id"));
					record.setEmpCode((String) hrmsEmployee.get("employee_no"));
					record.setEmpName((String) hrmsEmployee.get("employee_name"));
					record.setEmpDept((String) hrmsEmployee.get("org_id"));
					record.setEmpDeptName((String) hrmsEmployee.get("name"));
					record.setJobLevel((String) jobDescription.get("nurseClassName"));
					record.setAttachedDept((String) jobDescription.get("appendantDeptName"));
					record.setJobType((String) jobDescription.get("careerSeriesName"));
					record.setEmpTitle((String) jobDescription.get("jobTypeName"));
					record.setDirectLeader((String) jobDescription.get("supervisorJobName"));
					record.setNurseLevel((String) jobDescription.get("nurseClassName"));
					record.setInternalRelations((String) jobDescription.get("consultReportRelation"));
					record.setClinicalNursing((String) jobDescription.get("clinicalNurseRelation"));
					record.setExternalRelations((String) jobDescription.get("externalRelation"));
					record.setJobDuties((String) jobDescription.get("jobResponsibilities"));
					record.setQualification((String) jobDescription.get("qualification"));
					
					addHrmsJobDescription.add(record);
				}
			}
		}
		
		for (HrmsJobDescription hrmsJobDescription : addHrmsJobDescription) {
			Example example = new Example(HrmsJobDescription.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("empId", hrmsJobDescription.getEmpId());
			HrmsJobDescription jobDescription = mapper.selectOneByExample(example);
			if(null == jobDescription) {
				hrmsJobDescription.setId(IdGeneraterUtils.nextId());
				hrmsJobDescription.setIsDeleted("N");
				hrmsJobDescription.setCreateDate(new Date());
				hrmsJobDescription.setCreateUser("admin");
				hrmsJobDescription.setCreateUserName("admin");
				hrmsJobDescription.setUpdateDate(new Date());
				hrmsJobDescription.setUpdateUser("admin");
				hrmsJobDescription.setUpdateUserName("admin");
				hrmsJobDescription.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				mapper.insertSelective(hrmsJobDescription);
			}else {
				BeanUtil.copyProperties(hrmsJobDescription, jobDescription);
				mapper.updateByPrimaryKey(jobDescription);
			}
		}
		
	}
}

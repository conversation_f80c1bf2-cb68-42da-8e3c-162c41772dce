package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.common.JdGridTableEntity;
import cn.trasen.hrms.dao.HrmsSalaryPlanDetailMapper;
import cn.trasen.hrms.enums.SalaryCountTypeEnum;
import cn.trasen.hrms.enums.SalaryItemTypeEnum;
import cn.trasen.hrms.model.HrmsSalaryPlanDetail;
import cn.trasen.hrms.service.HrmsSalaryPlanDetailService;

/**   
 * @Title: HrmsSalaryPlanDetailServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 方案明细 业务层接口
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月30日 上午10:47:35 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsSalaryPlanDetailServiceImpl implements HrmsSalaryPlanDetailService {

	@Autowired
	HrmsSalaryPlanDetailMapper hrmsSalaryPlanDetailMapper;

	/**
	 * @Title: insert
	 * @Description: 新增薪酬方案明细
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年4月29日 上午9:47:01
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsSalaryPlanDetail entity) {
		Assert.notNull(entity.getSalaryPlanId(), "salaryPlanId must not be null.");
		Assert.notEmpty(entity.getSalaryItemIds(), "salaryItemIds must not be empty");

		List<HrmsSalaryPlanDetail> list = Lists.newArrayList();
		for (String id : entity.getSalaryItemIds()) {
			HrmsSalaryPlanDetail detail = new HrmsSalaryPlanDetail();
			detail.setSalaryPlanDetailId(String.valueOf(IdWork.id.nextId()));
			detail.setItemId(id);
			detail.setSalaryPlanId(entity.getSalaryPlanId());
			detail.setIsDeleted(Contants.IS_DELETED_FALSE);
			detail.setCreateUser(UserInfoHolder.getCurrentUserCode());
			detail.setCreateUserName(UserInfoHolder.getCurrentUserName());
			detail.setCreateDate(new Date());
			list.add(detail);
		}
		// 删除当前方案的数据
		hrmsSalaryPlanDetailMapper.deleteBySalaryPlanId(entity.getSalaryPlanId());
		return hrmsSalaryPlanDetailMapper.batchInsert(list);
	}

	/**
	 * @Title: getSalaryPlanItemIds
	 * @Description: 根据薪酬方案ID获取薪酬项目ID集合
	 * @param salaryPlanId 薪酬方案ID
	 * @Return List<String>
	 * <AUTHOR>
	 * @date 2020年4月29日 上午9:41:38
	 */
	@Override
	public List<String> getSalaryPlanItemIds(String salaryPlanId) {
		Assert.notNull(salaryPlanId, "salaryPlanId must not be null.");
		return hrmsSalaryPlanDetailMapper.getSalaryPlanItemIds(salaryPlanId, Contants.IS_DELETED_FALSE);
	}

	/**  
	 * @Title: getListBySalaryPlanId
	 * @Description: 根据薪酬方案ID查询明细列表
	 * @Param: salaryPlanId 薪酬方案ID
	 * <AUTHOR>
	 * @date 2020年3月30日 下午3:44:56 
	 */
	@Override
	public List<HrmsSalaryPlanDetail> getListBySalaryPlanId(String salaryPlanId) {
		Assert.notNull(salaryPlanId, "salaryPlanId must not be null.");

		List<HrmsSalaryPlanDetail> list = hrmsSalaryPlanDetailMapper.getListBySalaryPlanId(salaryPlanId);
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsSalaryPlanDetail detail : list) {
				detail.setSalaryItemTypeText(SalaryItemTypeEnum.getValByKey(detail.getSalaryItemType())); // 项目类型文本值
				detail.setCountTypeText(SalaryCountTypeEnum.getValByKey(detail.getCountType())); // 计算类型文本值
			}
		}
		return list;
	}

	/**
	 * @Title: getTableHeadCols
	 * @Description: 查询薪酬档案JdGrid表头列表
	 * @param salaryPlanId
	 * @Return List<JdGridTableEntity>
	 * <AUTHOR>
	 * @date 2020年5月7日 下午6:21:14
	 */
	@Override
	public List<JdGridTableEntity> getTableHeadCols(String salaryPlanId) {
		Assert.notNull(salaryPlanId, "salaryPlanId must not be null.");

		List<JdGridTableEntity> result = Lists.newArrayList();
		result.add(new JdGridTableEntity("ID", "salaryPayrollId", "", 100, false, true, false, "")); // 薪酬记录ID
		result.add(new JdGridTableEntity("员工id" ,"employeeId", "", 60, false, true, false, "")); // 员工状态
		result.add(new JdGridTableEntity("明细" ,"isDetails", "", 60, false, true, false, "")); // 是否有明细
		result.add(new JdGridTableEntity("职员编码", "employeeNo", "", 50, false, false, false, "center")); // 员工工号
		result.add(new JdGridTableEntity("姓名", "employeeName", "", 50, false, false, false, "center")); // 员工姓名
		result.add(new JdGridTableEntity("工资账号", "empPayroll", "", 70, false, false, false, "center"));
		result.add(new JdGridTableEntity("部门" ,"orgName", "", 100, false, false, false, "center")); // 机构名称
		result.add(new JdGridTableEntity("岗位名称" ,"personalIdentityText", "", 100, false, false, false, "center")); // 岗位名称
		result.add(new JdGridTableEntity("编制类型" ,"establishmentType", "", 60, false, false, false, "center")); // 编制类型
		result.add(new JdGridTableEntity("员工状态" ,"employeeStatus", "", 60, false, false, false, "center")); // 员工状态
//		result.add(new JdGridTableEntity("员工类别", "employeeCategoryText", "", 100, false, false, false, "")); // 员工类别
//		result.add(new JdGridTableEntity("岗位名称", "postName", "", 100, false, false, false, "")); // 岗位名称
//		result.add(new JdGridTableEntity("职称", "jobTitleName", "", 100, false, false, false, "")); // 职称
//		result.add(new JdGridTableEntity("职称级别", "jobTitleLevel", "", 100, false, false, false, "")); // 职称级别
		
		// 查询薪酬方案关联的项目列表
		List<HrmsSalaryPlanDetail> list = hrmsSalaryPlanDetailMapper.getListBySalaryPlanId(salaryPlanId);
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsSalaryPlanDetail detail : list) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(detail.getSalaryItemName());
				entity.setName(detail.getItemId());
				entity.setWidth(80);
				entity.setHidden(false);
				entity.setSortable(false);
				entity.setAlign("center");
				if (SalaryItemTypeEnum.ITEM_TYPE_3.getKey().equals(detail.getSalaryItemType())
						||SalaryItemTypeEnum.ITEM_TYPE_5.getKey().equals(detail.getSalaryItemType())) {
					entity.setEditable(true);
				} else {
					entity.setEditable(false);
				}
				result.add(entity);
			}
		}
		return result;
	}

}

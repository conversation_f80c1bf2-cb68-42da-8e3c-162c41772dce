package cn.trasen.hrms.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsJobtitleInfoMapper;
import cn.trasen.hrms.dao.HrmsPostMapper;
import cn.trasen.hrms.model.HrmsJobtitleInfo;
import cn.trasen.hrms.model.PostInformation;
import cn.trasen.hrms.service.PromotionService;

/**   
 * @Title: HrmsPostServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 岗位信息 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月19日 上午11:09:28 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class PromotionServiceImpl implements  PromotionService {
	
	@Autowired
	HrmsPostMapper hrmsPostMapper;
	
	@Autowired
	HrmsJobtitleInfoMapper hrmsJobtitleInfoMapper;


	@Override
	public List<PostInformation> getPromotionPostDataList(Page page, PostInformation entity) {
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsPostMapper.getPromotionPostDataList(page,entity);
	}

	@Override
	public List<HrmsJobtitleInfo> getPromotionJobDataList(Page page, HrmsJobtitleInfo entity) {
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsJobtitleInfoMapper.getPromotionJobDataList(page,entity);
	}

}

package cn.trasen.hrms.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsSchedulingFrequencyGroupMapper;
import cn.trasen.hrms.dao.HrmsSchedulingFrequencyMapper;
import cn.trasen.hrms.model.HrmsSchedulingFrequency;
import cn.trasen.hrms.model.HrmsSchedulingFrequencyGroup;
import cn.trasen.hrms.service.HrmsSchedulingFrequencyGroupService;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**   
 * @ClassName:  HrmsSchedulingFrequencyGroupServiceImpl   
 * @Description:组合班次实现类
 * @author: WZH
 * @date:   2021年7月14日 下午3:28:00      
 * @Copyright:  
 */
@Service
public class HrmsSchedulingFrequencyGroupServiceImpl implements HrmsSchedulingFrequencyGroupService {
	
	@Autowired
	HrmsSchedulingFrequencyGroupMapper hrmsSchedulingFrequencyGroupMapper;
	@Autowired
	HrmsSchedulingFrequencyMapper hrmsSchedulingFrequencyMapper;

	@Override
	public int insert(HrmsSchedulingFrequencyGroup entity) {
		
	/*	Example example = new Example(HrmsSchedulingFrequencyGroup.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("frequencyGroupName", entity.getFrequencyGroupName());
		List<HrmsSchedulingFrequencyGroup> list = hrmsSchedulingFrequencyGroupMapper.selectByExample(example);
		if(list != null && list.size() > 0) {
			throw new RuntimeException("组合班次已存在");
		}*/
		
		entity.setFrequencyGroupId(IdUtil.getId());
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
		entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsSchedulingFrequencyGroupMapper.insertSelective(entity);
	}

	@Override
	public int update(HrmsSchedulingFrequencyGroup entity) {
		Assert.hasText(entity.getFrequencyGroupId(), "ID不能为空.");
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsSchedulingFrequencyGroupMapper.updateByPrimaryKeySelective(entity);
	}

	@Override
	public int deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsSchedulingFrequencyGroup bean = new HrmsSchedulingFrequencyGroup();
		bean.setFrequencyGroupId(id);
		bean.setIsDeleted(Contants.IS_DELETED_TURE);
		bean.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		bean.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		bean.setUpdateDate(new Date());
		return hrmsSchedulingFrequencyGroupMapper.updateByPrimaryKeySelective(bean);
	}

	@Override
	public List<HrmsSchedulingFrequencyGroup> getDataList(Page page, HrmsSchedulingFrequencyGroup entity) {
		Example example = new Example(HrmsSchedulingFrequencyGroup.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(entity.getFrequencyGroupName())) {
			String condition = entity.getFrequencyGroupName();
			criteria.andLike("frequencyGroupName", "%" + condition.trim() + "%");
		}
		example.and().andCondition(" FIND_IN_SET('"+UserInfoHolder.getCurrentUserInfo().getDeptcode()+"', org_id) or org_Id is null or  org_Id='' ");
//		example.and(
//				example.createCriteria().andEqualTo("orgId", UserInfoHolder.getCurrentUserInfo().getDeptcode())
//				.orEqualTo("orgId", ""));
		List<HrmsSchedulingFrequencyGroup> list = hrmsSchedulingFrequencyGroupMapper.selectByExampleAndRowBounds(example, page);
		if(list != null && list.size() > 0) {  //加上颜色
			
			for(int i=0;i<list.size();i++) {
				String ids = list.get(i).getFrequencyGroupContentId();
				List<String> asList = Arrays.asList(ids.split("\\|"));
				StringBuilder sb = new StringBuilder();
				StringBuilder sbFT = new StringBuilder();
				String frequencyColours = "";
				String frequencyTime = "";
				if(asList != null && asList.size() > 0) {
					for(int k=0;k<asList.size();k++) {
						HrmsSchedulingFrequency sfBean = hrmsSchedulingFrequencyMapper.selectByPrimaryKey(asList.get(k));
						if(sfBean != null) {
							sb.append(sfBean.getFrequencyColour()).append("|");
							sbFT.append(sfBean.getFrequencyTime()).append("|");
						}
					}
					if(sb.length() > 0) {
						frequencyColours = sb.deleteCharAt(sb.length()-1).toString();
					}
					if(sbFT.length() > 0) {
						frequencyTime = sbFT.deleteCharAt(sbFT.length()-1).toString();
					}
					list.get(i).setFrequencyColours(frequencyColours);
					list.get(i).setFrequencyTimes(frequencyTime);
				}
				
			}
		}
		return list;
	}

	@Override
	public List<HrmsSchedulingFrequencyGroup> getList(HrmsSchedulingFrequencyGroup entity) {
		return hrmsSchedulingFrequencyGroupMapper.selectAll();
	}

	@Override
	public HrmsSchedulingFrequencyGroup findDetailById(String id) {
		return hrmsSchedulingFrequencyGroupMapper.selectByPrimaryKey(id);
	}

}

package cn.trasen.hrms.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.model.HrmsContract;

/**
 * <AUTHOR>
 * 合同管理接口
 *
 */
public interface HrmsContractService {
		
	/**
	 * 添加合同
	 * @param entity
	 * @return
	 */
	int insert(HrmsContract entity) throws Exception;
	
	/**
	 * 编辑合同信息
	 * @param entity
	 * @return
	 */
	void update(HrmsContract entity)  throws Exception;
	
	/**
	 * 续签合同信息
	 * @param entity
	 * @return
	 */
	void renew(HrmsContract entity)  throws Exception;
	
	/**
	 * 解除
	 * @param entity
	 */
	void relieve(HrmsContract entity) throws Exception;

	/**
	 * 删除劳动合同
	 * @param entity
	 */
	void remove(HrmsContract entity) throws Exception;
	
	
	
	
	/**
	 * 合同信息列表
	 * @param page
	 * @param entity
	 * @return
	 */
	List<HrmsContract> getDataList(Page page, HrmsContract entity);


	/**
	 * 导入合同信息
	 * @param list
	 * @param type
	 * @return
	 */
	PlatformResult<Map<String,Object>> excelImportContract(List<HrmsContract> list, String type)  throws Exception ;

	/**
	 * 查询个人所有合同信息
	 * @param employeeId
	 * @return
	 */
	List<HrmsContract> getAllByEmployeeId(String employeeId);

	/**到期提醒列表
	 * @param page
	 * @param entity
	 * @return
	 */
	List<HrmsContract> getRemindDataList(Page page, HrmsContract entity);

	/**
	 * 定时任务 改变合同状态
	 */
	void updateSignTypeJob();

	/**
	 * 修改到期提醒
	 * @param contractId
	 * @param remind 
	 * @return
	 */
	int updateRemind(String contractId, String remind);

	/**	员工合同分页
	 * @param page
	 * @param entity
	 * @return
	 */
	List<HrmsContract> getEmployeeDataList(Page page, HrmsContract entity);

	/**
	 * 到期提醒泡泡
	 * @param entity
	 * @return
	 */
	Map<String, Object> getBubble(HrmsContract entity);

	/**
	 * 根据人员查询上一条劳动合同
	 * @param employeeNo
	 * @return
	 */
	String getEmployeeOldContract(HrmsContract entity);
	
	/**
	 * 
	 * @Title: getWarningContract
	 * @Description: 查询预警合同数据
	 * @param @param params
	 * @param @return 参数
	 * @return List<HrmsContract> 返回类型
	 * 2021年10月14日
	 * ADMIN
	 * @throws
	 */
	List<Map<String, Object>> getWarningContract(Map<String,Object> params);

		/**
	 * 
	 * @Title: syncContract
	 * @Description: 同步员工续签合同信息
	 * @param @param params
	 * @param @return 参数
	 * @return List<HrmsContract> 返回类型
	 * 2022年7月9日
	 * ADMIN
	 * @throws
	 */
	void syncContract(Map<String, Object> formData);

	/**
	 * 经开合同续签流程同步数据
	 * @param formData
	 */
	void renewflow(Map<String, Object> formData);




	
}

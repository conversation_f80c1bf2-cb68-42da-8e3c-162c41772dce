package cn.trasen.hrms.service.impl;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsEmployeeMapper;
import cn.trasen.hrms.dao.HrmsTimekeeperMapper;
import cn.trasen.hrms.model.HrmsTimekeeper;
import cn.trasen.hrms.service.HrmsTimekeeperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**   
 * @Title: HrmsAccountInfoServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 员工账号信息 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月11日 下午3:32:24 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsTimekeeperServiceImpl implements HrmsTimekeeperService {
	
	@Autowired
	HrmsTimekeeperMapper hrmsTimekeeperMapper;
	
	
	@Transactional(readOnly = false)
	@Override
	public int insert(List<HrmsTimekeeper> entity) {
		deleted(entity.get(0).getTimekeeperId(),entity.get(0).getUploadType());  //删除考勤员名下所有人
//		hrmsTimekeeperMapper.deleteByEmpList(entity);  //清空所有被考勤人重新设置（保证唯一性）
		ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();
		entity.forEach(item->{
			item.setSsoOrgCode(userInfo.getCorpcode());
			item.setSsoOrgName(userInfo.getOrgName());
			item.setCreateUser(userInfo.getUsercode());
		});
		return hrmsTimekeeperMapper.batchInsert(entity);
	}
	
	@Transactional(readOnly = false)
	@Override
	public int deleted(String id,Integer uploadType) {
		HrmsTimekeeper record = new HrmsTimekeeper();
		record.setTimekeeperId(id);
		record.setUploadType(Integer.valueOf(uploadType));
		return hrmsTimekeeperMapper.delete(record);
	}
	
	@Transactional(readOnly = false)
	@Override
	public List<HrmsTimekeeper> getDataAllList(HrmsTimekeeper entity) {
		Example example = new Example(HrmsTimekeeper.class);
		example.createCriteria().andEqualTo("timekeeperId", entity.getTimekeeperId());
		example.createCriteria().andEqualTo("uploadType", entity.getUploadType());
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		return hrmsTimekeeperMapper.selectByExample(example);
	}

	@Override
	public List<HrmsTimekeeper> getAuditlist(HrmsTimekeeper entity) {
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		return hrmsTimekeeperMapper.getAuditlist(ssoOrgCode);
	}

	@Override
	public List<HrmsTimekeeper> getDataList(Page page, HrmsTimekeeper entity) {
		//根据当前账号机构编号过滤
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsTimekeeperMapper.getDataList(page,entity);
	}

	@Override
	public int deleteByEmployeeId(HrmsTimekeeper entity) {
		return hrmsTimekeeperMapper.delete(entity);
	}
}

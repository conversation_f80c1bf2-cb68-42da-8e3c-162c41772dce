package cn.trasen.hrms.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.model.HrmsOldTcm;

/**
 * @ClassName HrmsOldTcmService
 * @Description TODO
 * @date 2023��10��30�� ����2:04:31
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsOldTcmService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��10��30�� ����2:04:31
	 * <AUTHOR>
	 */
	Integer save(HrmsOldTcm record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��10��30�� ����2:04:31
	 * <AUTHOR>
	 */
	Integer update(HrmsOldTcm record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��10��30�� ����2:04:31
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsOldTcm
	 * @date 2023��10��30�� ����2:04:31
	 * <AUTHOR>
	 */
	HrmsOldTcm selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsOldTcm>
	 * @date 2023��10��30�� ����2:04:31
	 * <AUTHOR>
	 */
	DataSet<HrmsOldTcm> getDataSetList(Page page, HrmsOldTcm record);
}

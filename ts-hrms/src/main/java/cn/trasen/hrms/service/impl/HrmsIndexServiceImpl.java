package cn.trasen.hrms.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.trasen.hrms.dao.HrmsEmployeeMapper;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.dao.HrmsIndexMapper;
import cn.trasen.hrms.service.HrmsIndexService;
import cn.trasen.hrms.utils.LmsoUtils;

import javax.annotation.Resource;

/**   
 * @ClassName:  HrmsIndexServiceImpl   
 * @Description:人事首页服务类
 * @author: WZH
 * @date:   2021年12月8日 下午2:04:51      
 * @Copyright:  
 */
@Service
public class HrmsIndexServiceImpl implements HrmsIndexService {
	
	@Resource
	HrmsIndexMapper hrmsIndexMapper;

	@Autowired
	DictItemFeignService dictItemFeignService;
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取事件预警
	  -- 作者: GW
	  -- 创建时间: 2024年10月17日
	  -- @param type  1今日3本月 4本年
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String, Object> getEvtWarn(Map<String,String> params) {
		if(StrUtil.isEmpty(Convert.toStr(params.get("type")))){
			throw new RuntimeException("查询类型【type】参数不能为空！");
		}
		Date now = new Date();
		Map<String,String> param = new HashMap<String,String>();
		//根据当前登录账号机构编码过滤查询数据
		param.put("sso_org_code",  UserInfoHolder.getCurrentUserCorpCode());
		switch (Convert.toStr(params.get("type"))) {
		case "1": // 今日
			param.put("start_date", DateUtil.today());
			param.put("end_date", DateUtil.today());
			break;
		case "3": //本月
			param.put("start_date", DateUtil.formatDate(DateUtil.beginOfMonth(now)));
			param.put("end_date", DateUtil.formatDate(DateUtil.endOfMonth(now)));
			break;
		default:
			return null;
		}
		Map<String,Object> result = new HashMap<String,Object>();
		int count = 0;
		//先查有多少种事件预警类型
		List<Map<String, Object>> warningRecordList = this.hrmsIndexMapper.getEvtWarn(param);
		if(CollUtil.isNotEmpty(warningRecordList)){
			result.put("count", warningRecordList.stream().mapToInt(i -> Convert.toInt(i.get("cnt"))).sum());
			//记录需要删除的数据
			List<Map<String, Object>> removeData = new ArrayList<>();
			//获取逾期预警记录
            List<Map<String, Object>> overdueWarningRecordList = warningRecordList.stream().filter(vo->"逾期提醒".equals(Convert.toStr(vo.get("warning_type")))).collect(Collectors.toList());
            warningRecordList.forEach(vo->{
                vo.put("sortCntNum",Convert.toInt(vo.get("cnt")));
			    if(!"逾期提醒".equals(Convert.toStr(vo.get("warning_type")))
                        && ("合同到期".equals(Convert.toStr(vo.get("conditions")))
                            ||"转正日期".equals(Convert.toStr(vo.get("conditions")))
                            ||"退休年龄".equals(Convert.toStr(vo.get("conditions"))))
                        ){
                    List<Map<String, Object>> tempMap =  overdueWarningRecordList.stream().filter(overdue -> Convert.toStr(vo.get("conditions")).equals(Convert.toStr(overdue.get("conditions")))).collect(Collectors.toList());
                    if(CollUtil.isNotEmpty(tempMap)){
                        vo.put("overdue_warning_title",tempMap.get(0).get("warning_title"));
                        vo.put("overdue_warning_type",tempMap.get(0).get("warning_type"));
                        vo.put("overdue_conditions",tempMap.get(0).get("conditions"));
                        vo.put("overdue_cnt",tempMap.get(0).get("cnt"));
                        vo.put("sortCntNum",Convert.toInt(vo.get("cnt"))+ Convert.toInt(tempMap.get(0).get("cnt")));
						removeData.addAll(tempMap);
                    }else{
                        vo.put("overdue_warning_title","");
                        vo.put("overdue_warning_type","逾期提醒");
                        vo.put("overdue_conditions",Convert.toStr(vo.get("conditions")));
                        vo.put("overdue_cnt",0);
                        vo.put("sortCntNum",Convert.toInt(vo.get("cnt")));
                    }
			    }
            });
            //删除多余数据
			warningRecordList.removeAll(removeData);
			//排序
            warningRecordList.sort((o1,o2)->Convert.toStr(o2.get("sortCntNum")).compareTo(Convert.toStr(o1.get("sortCntNum"))));
//			LmsoUtils.bubbleSortByCnt("sortCntNum",warningRecordList);
			result.put("data", warningRecordList);
		}else{
			result.put("count",count);
			result.put("data", null);
		}
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取今日动态
	  -- 作者: GW
	  -- 创建时间: 2024年10月15日
	  -- @param type
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String, Object> getDayDyna() {
		Map<String, String> params = new HashMap<>();
		params.put("sso_org_code",UserInfoHolder.getCurrentUserCorpCode());
		return hrmsIndexMapper.getDayDyna(params);
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取人员去向
	  -- 作者: GW
	  -- 创建时间: 2024年10月16日
	  -- @param type: 1今日、3本月、5自定义时间范围
	  			start_date： 开始日期  yyyy-mm-dd 
	  			end_date：结束日期  yyyy-mm-dd 
	  			examine_status 审核状态
	  -- @return 
	  -- =============================================
	 */
	@Override
	public Map<String,Object> getPsnDstn(Map<String, String> params) {
		if(StrUtil.isEmpty(Convert.toStr(params.get("type")))){
			throw new RuntimeException("查询类型【type】参数不能为空！");
		}
		//根据当前登录账号机构编码过滤查询数据
		params.put("sso_org_code",  UserInfoHolder.getCurrentUserCorpCode());
		Date now = new Date();
		switch (params.get("type")) {
		case "1": // 今日
			params.put("start_date", DateUtil.today());
			params.put("end_date", DateUtil.today());
			break;
		case "3": //本月
			params.put("start_date", DateUtil.formatDate(DateUtil.beginOfMonth(now)));
			params.put("end_date", DateUtil.formatDate(DateUtil.endOfMonth(now)));
			break;
		case "4": //本年
			params.put("start_date", DateUtil.formatDate(DateUtil.beginOfYear(now)));
			params.put("end_date", DateUtil.formatDate(DateUtil.endOfYear(now)));
			break;
		case "5": //自定义日期范围
			if(StrUtil.isEmpty(params.get("start_date")) || StrUtil.isEmpty(params.get("end_date"))){
				throw new RuntimeException("开始日期、结束日期不能为空！");
			}
		default:
			break;
		}
		Map<String, Object> result = new HashMap<String,Object>();
		List<Map<String, Object>> leaveTypeList = this.hrmsIndexMapper.getPsnDstn(new HashMap<String,String>());
		if(CollUtil.isNotEmpty(leaveTypeList)){
			List<String> leaveTypes = leaveTypeList.stream().map(i -> Convert.toStr(i.get("title"))).distinct().collect(Collectors.toList());
//			params.put("examine_status", "1"); //只取状态正常或审核通过的数据
			params.put("isData","true"); // 过滤销假数据
			List<Map<String, Object>> list = hrmsIndexMapper.getPsnDstn(params);
			leaveTypes.forEach(i -> {
				//查询时间段内没有数据的类型设置添加默认值
				long count = list.stream().filter(j -> StrUtil.equals(i, Convert.toStr(j.get("title")))).count();
				if(count < 1){
					if(i.indexOf("_")>0) {
						Map<String, Object> m = new HashMap<String, Object>();
						m.put("leave_type", i.split("_")[0]);
						m.put("type", i.split("_")[1]);
						m.put("cnt", 0);
                        m.put("startDate", params.get("start_date"));
                        m.put("endDate", params.get("end_date"));
						list.add(m);
					}
				}
			});
//			result.put("totalCnt", list.stream().filter(vo-> vo.get("employee_code")!=null).map(i -> Convert.toBigDecimal(i.get("cnt"))).distinct().count());
			result.put("totalCnt", list.stream().filter(vo-> vo.get("cnt")!=null && Convert.toBigDecimal(vo.get("cnt")).compareTo(BigDecimal.ZERO)>0 )
					.map(i -> Convert.toBigDecimal(i.get("cnt"))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
			//排序
			list.sort((o1,o2)->Convert.toStr(o2.get("cnt")).compareTo(Convert.toStr(o1.get("cnt"))));
			result.put("dataList", list);
		}
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取异动人员
	  -- 作者: GW
	  -- 创建时间: 2024年10月16日
	  -- @param type 1今日、3本月、4本年
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String, Object> getPsnTrns(Map<String,String> params) {
		if(StrUtil.isEmpty(Convert.toStr(params.get("type")))){
			throw new RuntimeException("查询类型【type】参数不能为空！");
		}
		//根据当前登录账号机构编码过滤查询数据
		params.put("sso_org_code",  UserInfoHolder.getCurrentUserCorpCode());
		Date now = new Date();
		switch (params.get("type")) {
		case "1": // 今日
			params.put("start_date", DateUtil.today());
			params.put("end_date", DateUtil.today());
			break;
		case "3": //本月
			params.put("start_date", DateUtil.formatDate(DateUtil.beginOfMonth(now)));
			params.put("end_date", DateUtil.formatDate(DateUtil.endOfMonth(now)));
			break;
		case "4": //本年
			params.put("start_date", DateUtil.formatDate(DateUtil.beginOfYear(now)));
			params.put("end_date", DateUtil.formatDate(DateUtil.endOfYear(now)));
			break;
		case "5": //自定义日期范围
			if(StrUtil.isEmpty(params.get("start_date")) || StrUtil.isEmpty(params.get("end_date"))){
				throw new RuntimeException("开始日期、结束日期不能为空！");
			}
		default:
			break;
		}
		Map<String, Object> result = new HashMap<String,Object>();
		BigDecimal totalCnt = BigDecimal.ZERO;
		List<Map<String, Object>> causeList = this.hrmsIndexMapper.getPsnTrns(new HashMap<String,String>());
		if(CollUtil.isNotEmpty(causeList)){
			List<String> causes = causeList.stream().map(i -> Convert.toStr(i.get("cause"))).distinct().collect(Collectors.toList());
			List<Map<String, Object>> list = hrmsIndexMapper.getPsnTrns(params);
			if(CollUtil.isNotEmpty(list)){
				totalCnt = list.stream().filter(i -> i.get("cnt") != null).map(i -> Convert.toBigDecimal(i.get("cnt"))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
			}
			causes.forEach(i -> {
				if(null == list.stream().filter(j -> StrUtil.equals(i, Convert.toStr(j.get("cause")))).findFirst().orElse(null)){
					Map<String,Object> m = new HashMap<String,Object>();
					m.put("cause", i);
                    m.put("startDate", params.get("start_date"));
                    m.put("endDate", params.get("end_date"));
					m.put("cnt", BigDecimal.ZERO);
					list.add(m);
				}
			});
			result.put("totalCnt", totalCnt);
			LmsoUtils.bubbleSortByCnt("cnt",list);
			result.put("dataList", list);
		}
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取人才结构-性别、年龄、工龄
	  -- 作者: GW
	  -- 创建时间: 2024年10月16日
	  -- @param type
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String, Object> getStruGendAgeWorkyear() {
		Map<String, String> params = new HashMap<>();
		//根据当前登录账号机构编码过滤查询数据1
		params.put("sso_org_code",UserInfoHolder.getCurrentUserCorpCode());
		return hrmsIndexMapper.getStruGendAgeWorkyear(params);
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取学历分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月16日
	  -- @return
	  -- =============================================
	 */
	@Override
	public List<Map<String, Object>> getPsnEduc() {
		Map<String, String> params = new HashMap<>();
		//根据当前登录账号机构编码过滤查询数据
		params.put("sso_org_code",UserInfoHolder.getCurrentUserCorpCode());
		List<Map<String, Object>> result = hrmsIndexMapper.getPsnEduc(params);
		if(CollUtil.isNotEmpty(result)){
			List<DictItemResp> dictItemRespList = dictItemFeignService.getDictItemByTypeCode("EDUCATION_TYPE").getObject();
			BigDecimal totalCnt = result.stream().filter(i -> i.get("cnt") != null).map(i -> Convert.toBigDecimal(i.get("cnt"))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
			result.forEach(i -> {
				if(null != i.get("education_type")){
					DictItemResp dict = dictItemRespList.stream().filter(j -> StrUtil.equals(Convert.toStr(i.get("education_type")), j.getItemCode())).findFirst().orElse(null);
					i.put("education_type_dictText", null == dict ? "未说明的学历类别" : dict.getItemName());
					//顺序排列
					i.put("sort", (null == dict.getSort() ? Convert.toInt(dict.getItemCode()) : Convert.toInt(dict.getSort())) * -1);
				}else{
					i.put("education_type_dictText", "无学历");
					i.put("sort", -999);
				}
				if(null != totalCnt && totalCnt.compareTo(BigDecimal.ZERO) > 0) { 
					i.put("prop", Convert.toBigDecimal(i.get("cnt")).divide(totalCnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)));
				}
			});
			LmsoUtils.bubbleSortByCnt("sort",result);
		}
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获编制类型分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月16日
	  -- @return
	  -- =============================================
	 */
	@Override
	public List<Map<String, Object>> getEstablishment() {
		Map<String, String> params = new HashMap<>();
		//根据当前登录账号机构编码过滤查询数据
		params.put("sso_org_code",UserInfoHolder.getCurrentUserCorpCode());
		List<Map<String, Object>> result = hrmsIndexMapper.getEstablishment(params);
		if(CollUtil.isNotEmpty(result)){
			List<DictItemResp> dictItemRespList = dictItemFeignService.getDictItemByTypeCode("ESTABLISHMENT_TYPE").getObject();
			BigDecimal totalCnt = result.stream().filter(i -> i.get("cnt") != null).map(i -> Convert.toBigDecimal(i.get("cnt"))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
			result.forEach(i -> {
				if(null != i.get("establishment_type")){
					DictItemResp dict = dictItemRespList.stream().filter(j -> StrUtil.equals(Convert.toStr(i.get("establishment_type")), j.getItemCode())).findFirst().orElse(null);
					i.put("establishment_type_dictText", null == dict ? "未说明的编制类型" : dict.getItemName());
				}else{
					i.put("establishment_type_dictText", "其它");
				}
				if(null != totalCnt && totalCnt.compareTo(BigDecimal.ZERO) > 0) { 
					i.put("prop", Convert.toBigDecimal(i.get("cnt")).divide(totalCnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)));
				}
			});
		}
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获职称分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月16日
	  -- @return
	  -- =============================================
	 */
	@Override
	public List<Map<String, Object>> getJobtitle() {
		Map<String, String> params = new HashMap<>();
		//根据当前登录账号机构编码过滤查询数据
		params.put("sso_org_code",UserInfoHolder.getCurrentUserCorpCode());
		List<Map<String, Object>> result = hrmsIndexMapper.getJobtitle(params);
		if(CollUtil.isNotEmpty(result)){
			BigDecimal totalCnt = result.stream().filter(i -> i.get("cnt") != null).map(i -> Convert.toBigDecimal(i.get("cnt"))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
			result.forEach(i -> {
				if(null != totalCnt && totalCnt.compareTo(BigDecimal.ZERO) > 0) { 
					i.put("prop", Convert.toBigDecimal(i.get("cnt")).divide(totalCnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)));
				}
			});
			LmsoUtils.bubbleSortByCnt("cnt",result);
		}
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取岗位分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月30日
	  -- @return
	  -- =============================================
	 */
	@Override
	public List<Map<String, Object>> getPersonalIdentity() {
		Map<String, String> params = new HashMap<>();
		//根据当前登录账号机构编码过滤查询数据
		params.put("sso_org_code",UserInfoHolder.getCurrentUserCorpCode());
		List<Map<String, Object>> result = hrmsIndexMapper.getPersonalIdentity(params);
		if(CollUtil.isNotEmpty(result)){
			BigDecimal totalCnt = result.stream().filter(i -> i.get("cnt") != null).map(i -> Convert.toBigDecimal(i.get("cnt"))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
			result.forEach(i -> {
				if(null != totalCnt && totalCnt.compareTo(BigDecimal.ZERO) > 0) { 
					i.put("prop", Convert.toBigDecimal(i.get("cnt")).divide(totalCnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)));
				}
			});
			LmsoUtils.bubbleSortByCnt("cnt",result);
		}
		return result;
	}
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 异动分析
	  -- 作者: GW
	  -- 创建时间: 2024年10月17日
	  -- @param type: 3本月、4本年、5自定义时间范围
	  			start_date： 开始日期  yyyy-mm-dd 
	  			end_date：结束日期  yyyy-mm-dd 
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String,Object> getTrnsAna(Map<String,String> param) {
		if(StrUtil.isEmpty(param.get("type"))){
			throw new RuntimeException("查询类型【type】参数不能为空！");
		}
		Map<String,Object> map = new HashMap<>(); //返回对象
		Date now = new Date();
		List<String> list = new ArrayList<String>();
		switch (param.get("type")) {
		case "3": // 本月
			for(int i = 0 ;i < Convert.toInt(DateUtil.dayOfMonth(now)) ;i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(now, i * -1)));
			}
			param.put("start_date", DateUtil.formatDate(DateUtil.beginOfMonth(now)));
			param.put("end_date", DateUtil.formatDate(DateUtil.endOfMonth(now)));
			param.put("group_type", "1");
			break;
		case "4": //本年
			for(int i = 0 ;i <= DateUtil.thisMonth() ;i++){
				list.add(DateUtil.format(DateUtil.offsetMonth(now, i * -1),"yyyy-MM"));
			}
			param.put("start_date", DateUtil.formatDate(DateUtil.beginOfYear(now)));
			param.put("end_date", DateUtil.formatDate(DateUtil.endOfYear(now)));
			param.put("group_type", "2");
			break;
		case "5": //自定义日期范围
			if(StrUtil.isEmpty(param.get("start_date")) || StrUtil.isEmpty(param.get("end_date"))){
				throw new RuntimeException("开始日期、结束日期不能为空！");
			}
			for(int i = 0 ;i <= (int)DateUtil.between(DateUtil.parse(param.get("start_date"),"yyyy-MM-dd"), DateUtil.parse(param.get("end_date"),"yyyy-MM-dd"), DateUnit.DAY) ;i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(param.get("end_date"),"yyyy-MM-dd"), i * -1)));
			}
			param.put("group_type", "1");
			break;
		default:
			return null;
		}
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		//根据当前登录账号机构编码过滤查询数据
		param.put("sso_org_code",UserInfoHolder.getCurrentUserCorpCode());
		//获取所有异动类型
		List<Map<String, Object>> titleList = hrmsIndexMapper.getTrnsAna(param);
		List<Map<String, Object>> temp = hrmsIndexMapper.getTrnsAna(param);
		//根据类型排序
		List<Map<String,Object>> sortList = new ArrayList<>();
		if(CollUtil.isNotEmpty(titleList)){
			List<String> causes = titleList.stream().filter(vo->Convert.toInt(vo.get("cnt"))>0).map(i -> Convert.toStr(i.get("cause"))).distinct().collect(Collectors.toList());
			Collections.reverse(list);
			list.forEach(i -> {
				Map<String, Object> m = new HashMap<String,Object>();
				List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
				m.put("date", i);
				if(CollUtil.isNotEmpty(causes)){
					causes.forEach(j -> {
						Map<String, Object> b = new HashMap<String,Object>();
						b.put("cause", j);
						Map<String, Object> d = temp.stream().filter(k -> StrUtil.equals(Convert.toStr(k.get("cause")), j) && StrUtil.equals(Convert.toStr(k.get("effective_date")), i)).findFirst().orElse(null);
						b.put("cnt", null != d && null != d.get("cnt") ? d.get("cnt") : 0);
						if("4".equals(Convert.toStr(param.get("type")))){
							b.put("startDate", DateUtil.formatDate(DateUtil.beginOfMonth(DateUtil.parse(i+"-01 "))));
							b.put("endDate", DateUtil.formatDate(DateUtil.endOfMonth(DateUtil.parse(i+"-01 "))));
						}else{
							b.put("startDate", i);
							b.put("endDate", i);
						}
						dataList.add(b);
					});
				}
				m.put("dataList", dataList);
				result.add(m);
			});

			causes.stream().forEach(key->{
				Map<String,Object> sortMap = new HashMap<>();
				sortMap.put("cause",key);
				sortMap.put("totalCount",temp.stream().filter(vo-> key.equals(Convert.toStr(vo.get("cause")))).mapToInt(vo->Convert.toInt(vo.get("cnt"))).sum());
				sortList.add(sortMap);
			});
			sortList.sort((o1,o2)->Convert.toInt(o2.get("totalCount")).compareTo(Convert.toInt(o1.get("totalCount"))));

		}
		map.put("data",result);
		map.put("orderColum",sortList);
		return map;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 假勤分析
	  -- 作者: GW
	  -- 创建时间: 2024年10月17日
	  -- @param type: 0 7日、3本月、4本年、5自定义时间范围
	  			start_date： 开始日期  yyyy-mm-dd 
	  			end_date：结束日期  yyyy-mm-dd 
	  -- @return 
	  -- =============================================
	 */
	@Override
	public Map<String,Object> getPsnDstnAna(Map<String,String> param) {
		if(StrUtil.isEmpty(param.get("type"))){
			throw new RuntimeException("查询类型【type】参数不能为空！");
		}
		Map<String, Object> resultMap = new HashMap<>();
		Date now = new Date();
		List<String> list = new ArrayList<String>();
		Map<String, String> searchMap = new HashMap<String,String>();
		//根据当前登录账号机构编码过滤查询数据
		searchMap.put("sso_org_code",UserInfoHolder.getCurrentUserCorpCode());
		switch (param.get("type")) {
		case "0": // 7日
			for(int i = 0 ;i < 7 ;i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(now, i * -1)));
			}
			searchMap.put("start_date", DateUtil.formatDate(DateUtil.offsetDay(now, -7)));
			searchMap.put("end_date", DateUtil.formatDate(DateUtil.endOfYear(now)));

			break;
		case "3": //本月
			for(int i = 0 ;i < Convert.toInt(DateUtil.dayOfMonth(now)) ;i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(now, i * -1)));
			}
			searchMap.put("start_date", DateUtil.formatDate(DateUtil.beginOfMonth(now)));
			searchMap.put("end_date", DateUtil.formatDate(DateUtil.endOfMonth(now)));
			break;
		case "4": //本年
			for(int i = 0 ;i <= DateUtil.thisMonth() ;i++){
				list.add(DateUtil.format(DateUtil.offsetMonth(now, i * -1),"yyyy-MM"));
			}
			searchMap.put("start_date", DateUtil.formatDate(DateUtil.beginOfYear(now)));
			searchMap.put("end_date", DateUtil.formatDate(DateUtil.endOfYear(now)));
			break;
		case "5": //自定义时间范围
			if(StrUtil.isEmpty(param.get("start_date")) || StrUtil.isEmpty(param.get("end_date"))){
				throw new RuntimeException("开始日期、结束日期不能为空！");
			}
			for(int i = 0 ;i <= (int)DateUtil.between(DateUtil.parse(param.get("start_date"),"yyyy-MM-dd"), DateUtil.parse(param.get("end_date"),"yyyy-MM-dd"), DateUnit.DAY) ;i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(param.get("end_date"),"yyyy-MM-dd"), i * -1)));
			}
			searchMap.put("start_date", param.get("start_date"));
			searchMap.put("end_date", param.get("end_date"));
			break;
		default:
			return null;
		}
		//先获取所有的  leave_type
		List<Map<String, Object>> typeDatas = hrmsIndexMapper.getPsnDstn(searchMap);
		List<Map<String, Object>> result = new ArrayList<Map<String,Object>>();
		if(CollUtil.isNotEmpty(typeDatas)){
			List<String> leaveTypes = typeDatas.stream().filter(vo->Convert.toInt(vo.get("cnt"))>0).map(j -> Convert.toStr(j.get("leave_type"))).distinct().collect(Collectors.toList());
			//这里目前只能循环统计，因为假勤是一个时间范围，例如某人请假 从1日至7日，在数据库里面就只有一条记录，但这7天他都应该被统计出来
			Collections.reverse(list);
			list.forEach(i -> {
				Map<String, Object> temp = new HashMap<String,Object>();
				temp.put("dateStr", i);
				Map<String, String> params = new HashMap<String,String>();
				//根据当前登录账号机构编码过滤查询数据
				params.put("sso_org_code",UserInfoHolder.getCurrentUserCorpCode());
//				params.put("examine_status","1"); //只取状态正常或审核通过的数据
                params.put("isData","true");
				params.put("start_date", i.length() == 7 ? DateUtil.beginOfMonth(DateUtil.parse(i,"yyyy-MM")).toDateStr() : i);
				params.put("end_date", i.length() == 7 ? DateUtil.endOfMonth(DateUtil.parse(i,"yyyy-MM")).toDateStr() : i);
				List<Map<String, Object>> ls = hrmsIndexMapper.getPsnDstn(params); 
				List<Map<String, Object>> dataList = new ArrayList<Map<String,Object>>();
				leaveTypes.forEach(j -> {
					long m = ls.stream().filter(k -> StrUtil.equals(j, Convert.toStr(k.get("leave_type")))).mapToLong(k -> Convert.toLong(k.get("cnt"))).sum();
					Map<String, Object> b = new HashMap<String,Object>();
					b.put("leave_type", j);
					b.put("cnt", m);
					if("4".equals(Convert.toStr(param.get("type")))){
						b.put("startDate", DateUtil.formatDate(DateUtil.beginOfMonth(DateUtil.parse(i+"-01 "))));
						b.put("endDate", DateUtil.formatDate(DateUtil.endOfMonth(DateUtil.parse(i+"-01 "))));
					}else{
						b.put("startDate", i);
						b.put("endDate", i);
					}
					dataList.add(b);
				});
				temp.put("dataList", dataList);
				result.add(temp);
			});
		}
		resultMap.put("orderColum", hrmsIndexMapper.getPsnDstn(searchMap));
		resultMap.put("data",result);
		return resultMap;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 预警分析
	  -- 作者: GW
	  -- 创建时间: 2024年10月17日
	  -- @param type: 3本月、4本年、5自定义时间范围
	  			start_date： 开始日期  yyyy-mm-dd 
	  			end_date：结束日期  yyyy-mm-dd 
	  -- @return 
	  -- =============================================
	 */
	@Override
	public List<Map<String, Object>> getEvtWarnAna(Map<String,String> param) {
		if(StrUtil.isEmpty(param.get("type"))){
			throw new RuntimeException("查询类型【type】参数不能为空！");
		}
		Date now = new Date();
		List<String> list = new ArrayList<String>();
		switch (param.get("type")) {
		case "3": // 本月
			for(int i = 0 ;i < Convert.toInt(DateUtil.dayOfMonth(now)) ;i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(now, i * -1)));
			}
			param.put("start_date", DateUtil.formatDate(DateUtil.beginOfMonth(now)));
			param.put("end_date", DateUtil.formatDate(DateUtil.endOfMonth(now)));
			param.put("group_type", "1");
			break;
		case "4": //本年
			for(int i = 0 ;i <= DateUtil.thisMonth() ;i++){
				list.add(DateUtil.format(DateUtil.offsetMonth(now, i * -1),"yyyy-MM"));
			}
			param.put("start_date", DateUtil.formatDate(DateUtil.beginOfYear(now)));
			param.put("end_date", DateUtil.formatDate(DateUtil.endOfYear(now)));
			param.put("group_type", "2");
			break;
		case "5": //自定义日期范围
			if(StrUtil.isEmpty(param.get("start_date")) || StrUtil.isEmpty(param.get("end_date"))){
				throw new RuntimeException("开始日期、结束日期不能为空！");
			}
			for(int i = 0 ;i <= (int)DateUtil.between(DateUtil.parse(param.get("start_date"),"yyyy-MM-dd"), DateUtil.parse(param.get("end_date"),"yyyy-MM-dd"), DateUnit.DAY) ;i++){
				list.add(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(param.get("end_date"),"yyyy-MM-dd"), i * -1)));
			}
			param.put("group_type", "1");
			break;
		default:
			return null;
		}
		//去掉日期限制
		param.remove("start_date");
		param.remove("end_date");
		List<Map<String, Object>> temp = hrmsIndexMapper.getEvtWarn(param);
		List<Map<String, Object>> result = new ArrayList<Map<String,Object>>();
		//为防止某个日期数据库没有记录，导致缺少这个日期的数据
		if(CollUtil.isNotEmpty(list)){
			List<String> warningTitles = temp.stream().map(j -> Convert.toStr(j.get("warning_title"))).distinct().collect(Collectors.toList());
			Collections.reverse(list);
			list.forEach(i -> {
				Map<String, Object> e = new HashMap<String,Object>();
				e.put("dateStr", i);
				List<Map<String, Object>> dataList = new ArrayList<Map<String,Object>>();
				warningTitles.forEach(j -> {
					Map<String, Object> m = temp.stream().filter(k -> StrUtil.equals(j, Convert.toStr(k.get("warning_title"))) && StrUtil.equals(i, Convert.toStr(k.get("due_date")))).findFirst().orElse(null);
					Map<String, Object> b = new HashMap<String,Object>();
					b.put("warning_title", j);
					b.put("cnt", null == m ? BigDecimal.ZERO : m.get("cnt"));
					dataList.add(b);
				});
				e.put("dataList", dataList);
				result.add(e);
			});
		}
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 档案分析
	  -- 作者: GW
	  -- 创建时间: 2024年10月18日
	  -- @param 
	  -- @return
	  		fileIntegrity 完整分析
	  		fileUpdtInfo 合格分析
	  -- =============================================
	 */
	@Override
	public Map<String, Object> getFileIntegrityAna() {
		Map<String, String> param = new HashMap<String,String>();
		//根据当前登录账号机构编码过滤查询数据
		param.put("sso_org_code",UserInfoHolder.getCurrentUserCorpCode());
		List<Map<String,String>> groupList = this.hrmsIndexMapper.getGroupList(param);
		Map<String, Object> result = new HashMap<String,Object>();
		if(CollUtil.isNotEmpty(groupList)){
			List<Map<String, Object>> emps = this.getEmpFileIntegrity(groupList);
			if(CollUtil.isNotEmpty(emps)){
				Map<String,Object> fileIntegrity = new HashMap<String,Object>();
//				List<Map<String,Object>> l = emps.stream().filter(j -> "1".equals(j.get("integrity"))).collect(Collectors.toList());
				fileIntegrity.put("integrityCnt", emps.stream().filter(j -> "1".equals(j.get("integrity"))).count());
				fileIntegrity.put("unIntegrityCnt", emps.stream().filter(j -> "0".equals(j.get("integrity"))).count());
				result.put("fileIntegrity", fileIntegrity);
			}
		}
		//获取每个员工的档案修改情况
		List<Map<String,Object>> updtInfos = this.hrmsIndexMapper.getFileUpdtInfo(param);
		Map<String,Object> fileUpdtInfo = new HashMap<String,Object>();
		fileUpdtInfo.put("notAuditCnt", CollUtil.isNotEmpty(updtInfos) ? updtInfos.stream().filter(i -> i.get("status") == null || "0".equals(i.get("status"))).count() : BigDecimal.ZERO);
		fileUpdtInfo.put("auditingCnt", CollUtil.isNotEmpty(updtInfos) ? updtInfos.stream().filter(i -> "1".equals(i.get("status"))).count() : BigDecimal.ZERO);
		fileUpdtInfo.put("auditSucCont", CollUtil.isNotEmpty(updtInfos) ? updtInfos.stream().filter(i -> "2".equals(i.get("status"))).count() : BigDecimal.ZERO);
		fileUpdtInfo.put("auditFailCnt", CollUtil.isNotEmpty(updtInfos) ? updtInfos.stream().filter(i -> "3".equals(i.get("status"))).count() : BigDecimal.ZERO);
		result.put("fileUpdtInfo", fileUpdtInfo);
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取每个员工的档案是否完整
	  				1、查询机构有哪些档案组
	  				2、查询每个组的权限设置
	  				3、获取每条权限设置的对应的必填字段(有编辑权限)
	  				4、拼接好判空条件，查询每个员工每个档案组的完整情况
	  				5、标识每个员工的integrity(完整性)字段：每个档案组都完整则标识为完整(1)、有某一个档案组不完整则标识为不完整(0)
	  -- 作者: GW
	  -- 创建时间: 2024年11月6日
	  -- @param groupList
	  -- @return
	  -- =============================================
	 */
	List<Map<String, Object>> getEmpFileIntegrity(List<Map<String,String>> groupList){
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		if(CollUtil.isNotEmpty(groupList)){
			List<String> sqls = new ArrayList<String>();
			//获取每个组 的权限设置
			List<Map<String, String>> auths = this.hrmsIndexMapper.getGroupAuthList(UserInfoHolder.getCurrentUserCorpCode());
			groupList.forEach(group -> {
				StringBuffer ands = new StringBuffer(); // 用来判空的条件  例如   and empid is not null and gender is not null
				StringBuffer condition = new StringBuffer(); //用来判断是否需要进入判空  如：t1.personal_identity in('1','2') or t1.employee_id is null
				if(CollUtil.isNotEmpty(auths)){
					List<Map<String,String>> groupAuths = auths.stream().filter(i -> StrUtil.equals(group.get("id"), i.get("group_id"))).collect(Collectors.toList());
					if(CollUtil.isNotEmpty(groupAuths)){
						groupAuths.forEach(auth -> {
							if(StrUtil.isNotEmpty(auth.get("emp_ids")) && StrUtil.isNotEmpty(auth.get("auth_type"))){
								condition.append("2".equals(auth.get("auth_type")) ? StrUtil.format("or t1.personal_identity in('{}')", auth.get("emp_ids").replaceAll(",", "','")) 
										: StrUtil.format("or t1.employee_id {}", auth.get("emp_ids").contains("BENREN") ? " is not null " 
												: StrUtil.format(" in('{}')", auth.get("emp_ids").replaceAll(",", "','"))));
								//查询必填字段：个人信息、聘任信息、执业信息按字段来查询，其余的直接按group_id 来查询
								String extSql = "1".equals(group.get("id")) || "2".equals(group.get("id")) || "3".equals(group.get("id")) ? 
										StrUtil.format(" and exists(select 1 from cust_emp_auth_field t2 where t2.is_deleted = 'N' and t1.id = t2.field_id and t2.authority_id = '{}' and t2.is_edit = '1') ", auth.get("id")) : 
											StrUtil.format(" and exists(select 1 from cust_emp_auth_field t2 where t2.is_deleted = 'N' and t2.authority_id = '{}' and t2.is_edit = '1') and group_id = '{}'", auth.get("id"),group.get("id"));
								Map<String,String> param = new HashMap<String,String>();
								param.put("extSql", extSql);
								//根据当前登录账号机构编码过滤查询数据
								param.put("sso_org_code",UserInfoHolder.getCurrentUserCorpCode());
								List<String> fields = this.hrmsIndexMapper.getGroupMustsList(param);
								if(ObjectUtil.isNotEmpty(fields)){
									fields.forEach(field -> {
										ands.append(StrUtil.format(" and ifnull(t2.{},'') <> '' ", field));
									});
								}
							}
						});
					}
				}
				//参数说明：1、case when 条件，是否进入判空  2、表名   3、关联字段  4、是否添加is_deleted = 'N'条件  5、判空条件组  6、档案组名称
				StringBuffer sql = new StringBuffer(StrUtil.format("case when {} then (select count(1) from {} t2 where t1.employee_id = {} {}{}) else 1 end as {} ,", 
						StrUtil.removePrefixIgnoreCase(condition.toString(), "or"),
							"1".equals(group.get("id")) ? "(select * from cust_emp_base x1 left join cust_emp_info x2 on x1.employee_id = x2.info_id)" : group.get("table_name"),
									"cust_emp_info".equals(group.get("table_name")) && ! "1".equals(group.get("id"))  ? "t2.info_id" : "t2.employee_id",
											"cust_emp_info".equals(group.get("table_name")) ? "" : " and t2.is_deleted = 'N' ",
													ands,
														group.get("group_name")));
				//如果没有必填项就直接 视为完整
				sqls.add(ands.length() > 0 ? sql.toString() : StrUtil.format(" 1 as {} , ", "'" + group.get("group_name") + "'"));
			});
			result = this.hrmsIndexMapper.getFileIntegrityAna(sqls, UserInfoHolder.getCurrentUserCorpCode());
			if(CollUtil.isNotEmpty(result)){
				//标识完整性(integrity)字段 
				result.forEach(i -> {
					i.put("integrity", "1");//先都标识为完整    0代表不完整  1代表完整
					groupList.forEach(j -> {
						if("0".equals(Convert.toStr(i.get(j.get("group_name"))))){
							i.put("integrity", "0");//每个组都完整才算完整，其中有某个组不完整的则整个为不完整
						}
						//数量 > 1 的标记为1
						if(i.get(j.get("group_name")) != null && Convert.toInt(i.get(j.get("group_name"))) > 1){
							i.put(j.get("group_name"), "1");
						}
					});
				});
			}
		}
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取档案详情分析
	  -- 作者: GW
	  -- 创建时间: 2024年10月19日
	  -- @return  分组完整 groupIntegrity
	  			    科室完整 deptIntegrity
	  			    分组合格 groupAdj
	  			    科室合格 deptAdj
	  -- =============================================
	 */
	@Override
	public Map<String, Object> getFileDetailUpdtAna() {
		Map<String, Object> result = new HashMap<String,Object>();
		Map<String, String> param = new HashMap<String,String>();
		//根据当前登录账号机构编码过滤查询数据
		param.put("sso_org_code",UserInfoHolder.getCurrentUserCorpCode());
		List<Map<String,String>> groupList = this.hrmsIndexMapper.getGroupList(param);
		List<Map<String, Object>> emps = this.getEmpFileIntegrity(groupList);
		//统计分组完整
		List<Map<String, Object>> groupIntegrityList = new ArrayList<Map<String,Object>>();
		groupList.forEach(i -> {
			Map<String,Object> groupBean = new HashMap<String,Object>();
			groupBean.put("title", i.get("group_name"));
			groupBean.put("integrityCnt", emps.stream().filter(j -> "1".equals(Convert.toStr(j.get(i.get("group_name"))))).count());
			groupBean.put("unIntegrityCnt", emps.stream().filter(j -> "0".equals(Convert.toStr(j.get(i.get("group_name"))))).count());
			groupIntegrityList.add(groupBean);
		});
//		LmsoUtils.bubbleSortByCnt("unIntegrityCnt",groupIntegrityList);
		result.put("groupIntegrity", groupIntegrityList);

		//获取每个员工每个档案组修改合格情况
		List<Map<String,Object>> groupUpdtInfos = this.hrmsIndexMapper.getFileGroupUpdtInfo(param);
		//统计分组合格
		List<Map<String, Object>> groupAdjList = new ArrayList<Map<String,Object>>();
		//获取当前在职人数
		Map<String, Object> employeeCount = getDayDyna();
		Long totalEmployeeCnt = 0L;
		if(employeeCount!=null && employeeCount.containsKey("total_cnt")){
			totalEmployeeCnt = Convert.toLong(employeeCount.get("total_cnt"));
		}
		final Long totalCount = totalEmployeeCnt;
		groupList.forEach(i -> {
			//获取分组对应的统计数据
			List<Map<String,Object>> deptStandardDatas  = groupUpdtInfos.stream().filter(vo-> StrUtil.equals(Convert.toStr(i.get("group_name")),Convert.toStr(vo.get("group_name")))).collect(Collectors.toList());
			Map<String, Object> m = new HashMap<String,Object>();
			m.put("title",i.get("group_name"));
			if(CollUtil.isNotEmpty(deptStandardDatas) && deptStandardDatas.get(0) != null) {
				m.put("notAuditCnt", deptStandardDatas.get(0).get("auditUserCnt") != null ? totalCount - Convert.toLong(deptStandardDatas.get(0).get("auditUserCnt")) : BigDecimal.ZERO);
				m.put("auditingCnt", deptStandardDatas.get(0).get("auditingCnt") != null ? deptStandardDatas.get(0).get("auditingCnt") : BigDecimal.ZERO);
				m.put("auditSucCont", deptStandardDatas.get(0).get("auditSucCont") != null? deptStandardDatas.get(0).get("auditSucCont") : BigDecimal.ZERO);
				m.put("auditFailCnt", deptStandardDatas.get(0).get("auditFailCont") != null ? deptStandardDatas.get(0).get("auditFailCont") : BigDecimal.ZERO);
			}else{
				m.put("notAuditCnt", totalCount);
				m.put("auditingCnt", 0);
				m.put("auditSucCont", 0);
				m.put("auditFailCnt", 0);
			}

//			m.put("adjCnt", groupUpdtInfos.stream().filter(j -> StrUtil.equals(i.get("id"), Convert.toStr(j.get("group_id"))) && "1".equals(Convert.toStr(j.get("audit_status")))).mapToInt(j -> Convert.toInt(j.get("cnt"))).sum());
//			m.put("unAdjCnt", groupUpdtInfos.stream().filter(j -> StrUtil.equals(i.get("id"), Convert.toStr(j.get("group_id"))) && "2".equals(Convert.toStr(j.get("audit_status")))).mapToInt(j -> Convert.toInt(j.get("cnt"))).sum());
			groupAdjList.add(m);
		});
//		LmsoUtils.bubbleSortByCnt("auditFailCnt",groupAdjList);
		result.put("groupAdj", groupAdjList);//分组合格

		//统计科室完整
        //查询员工档案修改审核结果
        List<Map<String,Object>> empAdjList = this.hrmsIndexMapper.getFileUpdtInfo(param);
        List<Map<String, Object>> deptIntegrityList = new ArrayList<Map<String,Object>>(); //科室完整
        List<Map<String, Object>> deptAdjList = new ArrayList<Map<String,Object>>();  //科室合格
        List<String> orgNames = emps.stream().map(i -> Convert.toStr(i.get("org_name"))).distinct().collect(Collectors.toList());
        if(CollUtil.isNotEmpty(orgNames)){
            orgNames.forEach(i -> {
                //科室完整
				Long deptUserCount =  emps.stream().filter(j -> StrUtil.equals(i, Convert.toStr(j.get("org_name")))).count();
                Map<String, Object> deptBean = new HashMap<String,Object>();
                deptBean.put("title", i);
                deptBean.put("integrityCnt", emps.stream().filter(j -> StrUtil.equals(i, Convert.toStr(j.get("org_name"))) && "1".equals(j.get("integrity"))).count());
                deptBean.put("unIntegrityCnt", emps.stream().filter(j -> StrUtil.equals(i, Convert.toStr(j.get("org_name"))) && "0".equals(j.get("integrity"))).count());
                deptIntegrityList.add(deptBean);

                //科室合格
                Map<String, Object> deptAdjBean = new HashMap<String,Object>();
				List<Map<String, Object>> deptDataList = empAdjList.stream().filter(vo -> StrUtil.equals(i,Convert.toStr(vo.get("org_name")))).collect(Collectors.toList());
				if (CollUtil.isNotEmpty(deptDataList)) {
					Map<String, List<Map<String, Object>>> deptStandardDatas = deptDataList.stream().collect(Collectors.groupingBy(st -> Convert.toStr(st.get("status"))));
					deptAdjBean.put("notAuditCnt", deptStandardDatas.containsKey("0") ? deptStandardDatas.get("0").size() : BigDecimal.ZERO);
					deptAdjBean.put("auditingCnt", deptStandardDatas.containsKey("1") ? deptStandardDatas.get("1").size() : BigDecimal.ZERO);
					deptAdjBean.put("auditSucCont", deptStandardDatas.containsKey("2") ? deptStandardDatas.get("2").size() : BigDecimal.ZERO);
					deptAdjBean.put("auditFailCnt", deptStandardDatas.containsKey("3") ? deptStandardDatas.get("3").size() : BigDecimal.ZERO);
				} else {
					deptAdjBean.put("notAuditCnt", deptUserCount);
					deptAdjBean.put("auditingCnt", 0);
					deptAdjBean.put("auditSucCont", 0);
					deptAdjBean.put("auditFailCnt", 0);
				}
				deptAdjBean.put("title", StringUtils.isNotBlank(i) ? i : "无科室");
                deptAdjList.add(deptAdjBean);
            });
        }
        LmsoUtils.bubbleSortByCnt("unIntegrityCnt",deptIntegrityList);
        result.put("deptIntegrity", deptIntegrityList);

//        //科室合格
//		List<Map<String, Object>> deptAdjList = new ArrayList<Map<String,Object>>();
//		orgNames.forEach(i -> {
//			Map<String, Object> m = new HashMap<String,Object>();
//			m.put("title", i);
//			m.put("adjCnt", groupUpdtInfos.stream().filter(j -> StrUtil.equals(i, Convert.toStr(j.get("org_name"))) && "1".equals(Convert.toStr(j.get("audit_status")))).mapToInt(j -> Convert.toInt(j.get("cnt"))).sum());
//			m.put("unAdjCnt", groupUpdtInfos.stream().filter(j -> StrUtil.equals(i, Convert.toStr(j.get("org_name"))) && "2".equals(Convert.toStr(j.get("audit_status")))).mapToInt(j -> Convert.toInt(j.get("cnt"))).sum());
//			deptAdjList.add(m);
//		});
		LmsoUtils.bubbleSortByCnt("auditFailCnt",deptAdjList);
		result.put("deptAdj", deptAdjList);//科室合格
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取待审批事件
	  -- 作者: GW
	  -- 创建时间: 2024年10月21日
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String, Object> getInstanceInfo() {
		Map<String,Object> result = new HashMap<String,Object>();
		BigDecimal totalCont = BigDecimal.ZERO;
		Map<String, String> param = new HashMap<String,String>();
		//根据当前登录账号机构编码过滤查询数据
		param.put("sso_org_code",  UserInfoHolder.getCurrentUserCorpCode());
		List<Map<String, Object>> instanceInfos = this.hrmsIndexMapper.getInstanceInfo(param);
		//人事事件数据
		List<String> types = Arrays.asList("离职管理", "退休管理", "死亡事件","延聘管理","返聘管理");
		List<Map<String, Object>> data = new ArrayList<Map<String, Object>>();
		for(String i : types){
			Map<String,Object> m = new HashMap<String,Object>();
			List<Map<String, Object>> ls = instanceInfos.stream().filter(j -> StrUtil.equals(i, Convert.toStr(j.get("incident_category_text")))).collect(Collectors.toList());
			if(CollUtil.isNotEmpty(ls)){
				for(Map<String, Object> j : ls){
					j.put("message", StrUtil.format("【{}】的【{}】待审批 {}", j.get("employee_name"), i,j.get("create_date")));
					totalCont = totalCont.add(Convert.toBigDecimal(ls.size()));
				}
			}
			m.put("typeName", i);
			m.put("cnt", CollUtil.isNotEmpty(ls) ? ls.size() : BigDecimal.ZERO);
			m.put("dataList", ls);
			data.add(m);
		}

		//调动管理数据
		List<Map<String, Object>> changes = this.hrmsIndexMapper.getEmpChange(param);
		Map<String,Object> m = new HashMap<String,Object>();
		m.put("typeName", "调动管理");
		m.put("cnt", CollUtil.isNotEmpty(changes) ? changes.size() : BigDecimal.ZERO);
		if(CollUtil.isNotEmpty(changes)){
			changes.forEach(i -> {
				i.put("message", StrUtil.format("【{}】的【调动管理{}】待审批 {}", i.get("employee_name"), null == i.get("change_type") ? "" : "->" + i.get("change_type"),i.get("create_date")));
			});
			totalCont = totalCont.add(Convert.toBigDecimal(changes.size()));
		}
		m.put("dataList", changes);
		data.add(m);
		//党员花名册数据
		List<Map<String, Object>> partyMembers = this.hrmsIndexMapper.getPartyMembers(param);
		m = new HashMap<String,Object>();
		m.put("typeName", "党员花名册");
		m.put("cnt", CollUtil.isNotEmpty(partyMembers) ? partyMembers.size() : BigDecimal.ZERO);
		if(CollUtil.isNotEmpty(partyMembers)){
			partyMembers.forEach(i -> {
				i.put("message", StrUtil.format("【{}】的【党员花名册】待审批{} ", i.get("employee_name"),i.get("create_date")));
			});
			totalCont = totalCont.add(Convert.toBigDecimal(partyMembers.size()));
		}
		m.put("dataList", partyMembers);
		data.add(m);
		//TODO 培训计划数据
		//档案事件
		String assigneeNo = UserInfoHolder.getCurrentUserCode();
//		param.put("assignee_no", UserInfoHolder.getCurrentUserCode()); //查全员待审批的，然后标记是否是待当前账号审批
		List<Map<String, Object>> fileEvts = this.hrmsIndexMapper.getToApprFileEvt(param);
		m = new HashMap<String,Object>();
		m.put("typeName", "档案事件");
		m.put("cnt", CollUtil.isNotEmpty(fileEvts) ? fileEvts.size() : BigDecimal.ZERO);
		if(CollUtil.isNotEmpty(fileEvts)){
			fileEvts.forEach(i -> {
				i.put("message", StrUtil.format("【{}】的【{}】待审批{} ", i.get("employee_name"),i.get("operation_title"),i.get("create_date")));
				i.put("isJump", StrUtil.equals(assigneeNo, Convert.toStr(i.get("assignee_no"))));
			});
			totalCont = totalCont.add(Convert.toBigDecimal(fileEvts.size()));
		}
		m.put("dataList", fileEvts);
		data.add(m);
		//去除数量为0的分组数据
		List<Map<String,Object>> zeroData = data.stream().filter(vo-> Convert.toInt(vo.get("cnt"))<1).collect(Collectors.toList());
		data.removeAll(zeroData);
		if(CollUtil.isNotEmpty(data)) {
			LmsoUtils.bubbleSortByCnt("cnt", data);
		}
		result.put("data", data);
		result.put("totalCont", totalCont);
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 档案详情分析导出
	  -- 作者: GW
	  -- 创建时间: 2024年10月31日
	  -- @param type：
	  				1：分组完整
	  				2：科室完整
	  				3：分组合格
	  				4：科室合格
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String,Object> getFileDetailUpdtExpt(String type) {
		if(StrUtil.isEmpty(type)){
			throw new RuntimeException("导出类型【type】参数不能为空！");
		}
		Map<String, Object> result = new HashMap<String,Object>();
		List<Map<String,Object>> dataList = new ArrayList<Map<String,Object>>();
		Map<String, String> param = new HashMap<String,String>();
		//根据当前登录账号机构编码过滤查询数据
		param.put("sso_org_code",  UserInfoHolder.getCurrentUserCorpCode());
		//分组列表
		List<Map<String,String>> groupList = this.hrmsIndexMapper.getGroupList(param);
		//员工分组完整性
		List<Map<String, Object>> emps = this.getEmpFileIntegrity(groupList);
		List<String> orgNames = emps.stream().map(i -> Convert.toStr(i.get("org_name"))).distinct().collect(Collectors.toList());
		//获取每个员工每个档案组修改合格情况
		List<Map<String,Object>> groupUpdtInfos = this.hrmsIndexMapper.getFileGroupUpdtInfo(param);
		switch (type) {
		case "1":  //分组完整性
			for(Map<String,String> i : groupList){
				Map<String,Object> m = new HashMap<String,Object>();
				m.put("title", i.get("group_name"));//标题
				m.put("integrityCnt", emps.stream().filter(j -> "1".equals(Convert.toStr(j.get(i.get("group_name"))))).count());//完整人数
				m.put("unIntegrityCnt", emps.stream().filter(j -> "0".equals(Convert.toStr(j.get(i.get("group_name"))))).count());//不完整人数
				m.put("totalCnt", emps.size());//总人数
				m.put("empNames", emps.stream().filter(j -> "0".equals(Convert.toStr(j.get(i.get("group_name"))))).map(j -> Convert.toStr(j.get("employee_name"))).collect(Collectors.joining(", ")));//不完整名单
				dataList.add(m);
			}
			break;
		case "2":  //科室完整性
			for(String i : orgNames){
				Map<String, Object> m = new HashMap<String,Object>();
				m.put("title", i);
				m.put("integrityCnt", emps.stream().filter(j -> StrUtil.equals(i, Convert.toStr(j.get("org_name"))) && "1".equals(j.get("integrity"))).count());
				m.put("unIntegrityCnt", emps.stream().filter(j -> StrUtil.equals(i, Convert.toStr(j.get("org_name"))) && "0".equals(j.get("integrity"))).count());
				m.put("totalCnt", emps.stream().filter(j -> StrUtil.equals(i, Convert.toStr(j.get("org_name")))).count());//总人数
				m.put("empNames", emps.stream().filter(j -> StrUtil.equals(i, Convert.toStr(j.get("org_name"))) && "0".equals(j.get("integrity")))
						.map(j -> Convert.toStr(j.get("employee_name"))).collect(Collectors.joining(", ")));//不完整名单
				dataList.add(m);
			}
			break;
		case "3":  //分组合格
			//获取当前在职人数
			Map<String, Object> employeeCount = getDayDyna();
			Long totalEmployeeCnt = 0L;
			if(employeeCount!=null && employeeCount.containsKey("total_cnt")){
				totalEmployeeCnt = Convert.toLong(employeeCount.get("total_cnt"));
			}
			final Long totalCount = totalEmployeeCnt;
			for(Map<String,String> i : groupList){
				//获取分组对应的统计数据
				List<Map<String,Object>> deptStandardDatas  = groupUpdtInfos.stream().filter(vo-> StrUtil.equals(Convert.toStr(i.get("group_name")),Convert.toStr(vo.get("group_name")))).collect(Collectors.toList());
				Map<String, Object> m = new HashMap<String,Object>();
				m.put("title",i.get("group_name"));
				m.put("totalCnt", totalEmployeeCnt);//总人数
				if(CollUtil.isNotEmpty(deptStandardDatas) && deptStandardDatas.get(0) != null) {
					m.put("notAuditCnt", deptStandardDatas.get(0).get("auditUserCnt") != null ? totalCount - Convert.toLong(deptStandardDatas.get(0).get("auditUserCnt")) : BigDecimal.ZERO);
					m.put("auditingCnt", deptStandardDatas.get(0).get("auditingCnt") != null ? deptStandardDatas.get(0).get("auditingCnt") : BigDecimal.ZERO);
					m.put("auditSucCont", deptStandardDatas.get(0).get("auditSucCont") != null? deptStandardDatas.get(0).get("auditSucCont") : BigDecimal.ZERO);
					m.put("auditFailCnt", deptStandardDatas.get(0).get("auditFailCont") != null ? deptStandardDatas.get(0).get("auditFailCont") : BigDecimal.ZERO);
					m.put("empNames", deptStandardDatas.get(0).get("auditFailUser") != null ? deptStandardDatas.get(0).get("auditFailUser"):"");//不合格人员名单
				}else{
					m.put("notAuditCnt", totalCount);
					m.put("auditingCnt", 0);
					m.put("auditSucCont", 0);
					m.put("auditFailCnt", 0);
				}
				dataList.add(m);
			}
			break;
		case "4":  //科室合格
			Map<String, Object> deptAdjBean = null;
			Map<String, List<Map<String, Object>>> deptStandardDatas = null;
			List<Map<String, Object>> deptDataList = null;
			//查询员工档案修改审核结果
			List<Map<String,Object>> empAdjList = this.hrmsIndexMapper.getFileUpdtInfo(param);
			for(String i : orgNames){
				//科室完整
				Long deptUserCount =  emps.stream().filter(j -> StrUtil.equals(i, Convert.toStr(j.get("org_name")))).count();
				deptAdjBean = new HashMap<String,Object>();
				//查询当前科室对应的合格数据
				deptDataList = empAdjList.stream().filter(vo -> StrUtil.equals(i,Convert.toStr(vo.get("org_name")))).collect(Collectors.toList());
				if (CollUtil.isNotEmpty(deptDataList)) {
					deptStandardDatas = deptDataList.stream().collect(Collectors.groupingBy(st -> Convert.toStr(st.get("status"))));
					deptAdjBean.put("notAuditCnt", deptStandardDatas.containsKey("0") ? deptStandardDatas.get("0").size() : BigDecimal.ZERO);
					deptAdjBean.put("auditingCnt", deptStandardDatas.containsKey("1") ? deptStandardDatas.get("1").size() : BigDecimal.ZERO);
					deptAdjBean.put("auditSucCont", deptStandardDatas.containsKey("2") ? deptStandardDatas.get("2").size() : BigDecimal.ZERO);
					deptAdjBean.put("auditFailCnt", deptStandardDatas.containsKey("3") ? deptStandardDatas.get("3").size() : BigDecimal.ZERO);
					deptAdjBean.put("empNames", deptDataList.stream().filter(j -> "3".equals(Convert.toStr(j.get("status")))).distinct()
							.map(j -> Convert.toStr(j.get("employee_name")) + "->" + Convert.toStr(j.get("auditFailField"))).collect(Collectors.joining(", ")));//不合格名单
				} else {
					deptAdjBean.put("notAuditCnt", deptUserCount);
					deptAdjBean.put("auditingCnt", 0);
					deptAdjBean.put("auditSucCont", 0);
					deptAdjBean.put("auditFailCnt", 0);
				}
				deptAdjBean.put("totalCnt", deptUserCount);//总人数
				deptAdjBean.put("title", StringUtils.isNotBlank(i) ? i : "无科室");
				dataList.add(deptAdjBean);
			}
			break;
		default:
			break;
		}
		//排序后生成序号
		LmsoUtils.bubbleSortByCnt("1".equals(type) || "2".equals(type) ? "unIntegrityCnt" : "3".equals(type) || "4".equals(type) ? "auditFailCnt" :"totalCnt",dataList);
		int index = 1;//序号
		for(Map<String,Object> i : dataList){
			i.put("index", index++);
		}
		result.put("dataList", dataList);
		List<Map<Object,Object>> fileds = new ArrayList<Map<Object,Object>>();
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "index"},{"item_name", "序号"},{"sort", 1},{"width", 6}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "title"},{"item_name", "1".equals(type) || "3".equals(type) ? "分组" : "科室"},{"sort", 2},{"width", 30}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "totalCnt"},{"item_name", "总人数"},{"sort", 3},{"width", 10}}));
		if( "1".equals(type) || "2".equals(type)) {
			fileds.add(MapUtil.of(new Object[][]{{"item_code","integrityCnt"}, {"item_name", "完成人数"}, {"sort", 4}, {"width", 12}}));
			fileds.add(MapUtil.of(new Object[][]{{"item_code", "unIntegrityCnt"}, {"item_name", "未完成人数"}, {"sort", 5}, {"width", 14}}));
			fileds.add(MapUtil.of(new Object[][] {{"item_code", "empNames"},{"item_name", "未完成名单"},{"sort", 6},{"width", 120}}));
		}else if( "3".equals(type) || "4".equals(type)){
			fileds.add(MapUtil.of(new Object[][]{{"item_code","notAuditCnt"}, {"item_name", "未发起人数"}, {"sort", 4}, {"width", 12}}));
			fileds.add(MapUtil.of(new Object[][]{{"item_code", "auditingCnt"}, {"item_name", "审批中人数"}, {"sort", 5}, {"width", 14}}));
			fileds.add(MapUtil.of(new Object[][]{{"item_code", "auditSucCont"}, {"item_name", "合格人数"}, {"sort", 6}, {"width", 14}}));
			fileds.add(MapUtil.of(new Object[][]{{"item_code", "auditFailCnt"}, {"item_name", "不合格人数"}, {"sort", 7}, {"width", 14}}));
			fileds.add(MapUtil.of(new Object[][] {{"item_code", "empNames"},{"item_name", "不合格名单"},{"sort", 8},{"width", 120}}));
		}
		result.put("fileds", fileds.stream().sorted(Comparator.comparing(i -> Convert.toInt(i.get("sort")))).collect(Collectors.toList()));
		return result;
	}
}

package cn.trasen.hrms.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.CommTableSnapshotSaveReq;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.enums.EnableEnum;
import cn.trasen.homs.feign.base.CommTableSnapshotFeignService;
import cn.trasen.homs.feign.base.DictItemFeignService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.DataBaseProvider;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.contants.CommonContants;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsSalaryLevelMapper;
import cn.trasen.hrms.model.HrmsSalaryLevel;
import cn.trasen.hrms.model.HrmsSalaryLevelWage;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsSalaryLevelService;
import cn.trasen.hrms.service.HrmsSalaryLevelWageService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: HrmsSalaryLevelServiceImpl.java
 * @Package cn.trasen.hrms.service.impl
 * @Description: 薪级表 业务层接口实现
 * @Company: 湖南创星科技股份有限公司
 * @date 2020年4月1日 下午10:02:37
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsSalaryLevelServiceImpl implements HrmsSalaryLevelService {

    @Autowired
    HrmsSalaryLevelMapper hrmsSalaryLevelMapper;
    @Autowired
    HrmsDictInfoService hrmsDictInfoService;
    @Autowired
    HrmsSalaryLevelWageService hrmsSalaryLevelWageService;

    @Autowired
    CommTableSnapshotFeignService commTableSnapshotFeignService;

    @Autowired
    DictItemFeignService dictItemFeignService;

    /**
     * @Title: insert
     * @Description: 新增薪级
     * @Param: entity
     * <AUTHOR>
     * @date 2020年4月2日 上午11:11:58
     */
    @Override
    @Transactional(readOnly = false)
    public int insert(HrmsSalaryLevel entity) {
        String salaryLevelId = String.valueOf(IdWork.id.nextId());
        entity.setSalaryLevelId(salaryLevelId);
        entity.setIsEnable(CommonContants.IS_ENABLE_TRUE);
        entity.setIsDeleted(Contants.IS_DELETED_FALSE);
        entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
        entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
        entity.setCreateDate(new Date());
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        HrmsSalaryLevelWage salaryLevelWage = new HrmsSalaryLevelWage();
        salaryLevelWage.setSalaryLevelId(salaryLevelId);
        salaryLevelWage.setIsEnable("1");
        salaryLevelWage.setSalaryLevelWage(BigDecimal.ZERO);
        hrmsSalaryLevelWageService.insert(salaryLevelWage);
        // 增加处理快照
        CommTableSnapshotSaveReq commTableSnapshotSaveReq = new CommTableSnapshotSaveReq();
        commTableSnapshotSaveReq.setTableName("hrms_salary_level");
        commTableSnapshotSaveReq.setRowPkValue(entity.getSalaryLevelId());
        commTableSnapshotSaveReq.setNow(entity);
        fillDescField(entity);
        commTableSnapshotFeignService.save(commTableSnapshotSaveReq);
        return hrmsSalaryLevelMapper.insert(entity);
    }

    /**
     * @Title: update
     * @Description: 更新薪级
     * @Param: entity
     * @Return: int
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public int update(HrmsSalaryLevel entity) {

        HrmsSalaryLevel o = new HrmsSalaryLevel();
        if (!StringUtils.isBlank(entity.getSalaryLevelId())) {
            o = hrmsSalaryLevelMapper.selectByPrimaryKey(entity.getSalaryLevelId());
        }

        entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        entity.setUpdateDate(new Date());
        int res = hrmsSalaryLevelMapper.updateByPrimaryKeySelective(entity);

        HrmsSalaryLevel n = hrmsSalaryLevelMapper.selectByPrimaryKey(entity.getSalaryLevelId());

        // 增加快照
        CommTableSnapshotSaveReq commTableSnapshotSaveReq = new CommTableSnapshotSaveReq();
        commTableSnapshotSaveReq.setTableName("hrms_salary_level");
        commTableSnapshotSaveReq.setRowPkValue(entity.getSalaryLevelId());
        fillDescField(o);
        commTableSnapshotSaveReq.setOld(o);
        fillDescField(n);
        commTableSnapshotSaveReq.setNow(n);

        commTableSnapshotFeignService.save(commTableSnapshotSaveReq);
        return res;
    }

    private void fillDescField(HrmsSalaryLevel hrmsSalaryLevel) {


        // 填充isEnableLable
        if (StringUtils.isNotBlank(hrmsSalaryLevel.getIsEnable())) {
            if(hrmsSalaryLevel.getIsEnable().equals("1")){
                hrmsSalaryLevel.setIsEnableLable("启用");
            }else{
                hrmsSalaryLevel.setIsEnableLable("禁用");
            }
        }

        // 填充类型名称
        if (StringUtils.isNotBlank(hrmsSalaryLevel.getSalaryLevelCategory())) {
            PlatformResult<DictItemResp> ret = dictItemFeignService.getDictItemByDictTypeIdAndItemNameValue("salary_level_category", hrmsSalaryLevel.getSalaryLevelCategory());
            if (ret.isSuccess() && ret.getObject() != null && ret.getObject().getItemName() != null) {
                hrmsSalaryLevel.setSalaryLevelCategoryName(ret.getObject().getItemName());
            }
        }

    }

    /**
     * @Title: deleted
     * @Description: 删除薪级
     * @Param: id
     * @Return: int
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public int deleted(String id) {
        HrmsSalaryLevel salaryLevel = hrmsSalaryLevelMapper.selectByPrimaryKey(id);
        if (salaryLevel != null) {
            salaryLevel.setIsDeleted(Contants.IS_DELETED_TURE);
        }
        return hrmsSalaryLevelMapper.updateByPrimaryKeySelective(salaryLevel);
    }

    /**
     * @param entity
     * @Title: getDataList
     * @Description: 获取薪级列表
     * @Param: page
     * @Return: List<HrmsSalaryLevel>
     * <AUTHOR>
     */
    @Override
    public List<HrmsSalaryLevel> getDataList(Page page, HrmsSalaryLevel entity) {
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return hrmsSalaryLevelMapper.getList(entity,page);
//        Example example = new Example(HrmsSalaryLevel.class);
//        example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
//        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
//        if (StringUtils.isNotBlank(entity.getSalaryLevelName())) { // 薪级名称
//            example.and().andLike("salaryLevelName", "%" + entity.getSalaryLevelName() + "%");
//        }
//        if (StringUtils.isNotBlank(entity.getSalaryLevelCategory())) { // 薪级类别
//            example.and().andEqualTo("salaryLevelCategory", entity.getSalaryLevelCategory());
//        }
//
//        if (StringUtils.isNotBlank(entity.getIsEnable())) {
//            example.and().andEqualTo("isEnable", entity.getIsEnable());
//        }
//        if (DataBaseProvider.databaseId.equalsIgnoreCase("mysql")) {
//        	example.setOrderByClause("salary_level_category, COALESCE(CAST(grade AS UNSIGNED),9999) ");
//        }
//        List<HrmsSalaryLevel> list = hrmsSalaryLevelMapper.selectByExampleAndRowBounds(example, page);
//        if (CollectionUtils.isNotEmpty(list)) {
//            Map<String, String> salaryLevelCategoryMap = hrmsDictInfoService.convertDictMap(DictContants.SALARY_LEVEL_CATEGORY);
//            if (salaryLevelCategoryMap != null && salaryLevelCategoryMap.size() > 0) {
//                for (HrmsSalaryLevel salaryLevel : list) {
//                    salaryLevel.setSalaryLevelCategoryName(salaryLevelCategoryMap.get(salaryLevel.getSalaryLevelCategory())); // 薪级类别名称
//                }
//            }
//        }
//        return list;
    }

    /**
     * @param entity
     * @Title: getList
     * @Description: 查询薪级列表(不分页)
     * @Return List<HrmsSalaryLevel>
     * <AUTHOR>
     * @date 2020年4月17日 下午2:12:00
     */
    @Override
    public List<HrmsSalaryLevel> getList(HrmsSalaryLevel entity) {
        //根据当前登录账号机构编码过滤查询数据
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return hrmsSalaryLevelMapper.getList(entity);
    }

    /**
     * 该方法关联了薪级工资表过滤，新增时只显示没有录入过工资的薪级列表，编辑时不过滤
     *
     * @param entity
     * @Title: getComboboxList
     * @Description: 获取薪级下拉列表
     * @Return List<HrmsSalaryLevel>
     * <AUTHOR>
     * @date 2020年4月10日 下午2:17:28
     */
    @Override
    public List<HrmsSalaryLevel> getComboboxList(HrmsSalaryLevel entity) {
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<HrmsSalaryLevel> list = Lists.newArrayList();
        if (StringUtils.isBlank(entity.getSalaryLevelId())) {
            entity.setIsDeleted(Contants.IS_DELETED_FALSE);
            entity.setIsEnable(CommonContants.IS_ENABLE_TRUE);
            list = hrmsSalaryLevelMapper.getComboboxList(entity);
        } else {
            Example example = new Example(HrmsSalaryLevel.class);
            example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
            example.and().andEqualTo("isEnable", CommonContants.IS_ENABLE_TRUE);
            list = hrmsSalaryLevelMapper.selectByExample(example);
        }
        return list;
    }

    /**
     * @param list
     * @Title: excelImportSalaryLevelAndWage
     * @Description: excel导入薪级及工资
     * @Return PlatformResult<String>
     * <AUTHOR>
     * @date 2020年6月11日 上午9:52:54
     */
    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> excelImportSalaryLevelAndWage(List<HrmsSalaryLevelWage> list) {
        if (CollectionUtils.isEmpty(list)) {
            return PlatformResult.failure("导入内容为空");
        }
        if (list.stream().anyMatch(item -> StringUtils.isBlank(item.getSalaryLevelName()))) {
            return PlatformResult.failure("导入失败，薪级名称不能为空");
        }
        if (list.stream().anyMatch(item -> StringUtils.isBlank(item.getSalaryLevelCategoryName()))) {
            return PlatformResult.failure("导入失败，薪级类别不能为空");
        }
        // 按类别和名称分组，判断同一类别下是否有存在相同的名称
        Map<String, Map<String, Long>> maps = list.stream().collect(Collectors.groupingBy(HrmsSalaryLevelWage::getSalaryLevelCategoryName, Collectors.groupingBy(HrmsSalaryLevelWage::getSalaryLevelName, Collectors.counting())));
        if (maps != null && maps.size() > 0) {
            for (Map.Entry<String, Map<String, Long>> entry : maps.entrySet()) {
                Map<String, Long> childMap = entry.getValue();
                for (Map.Entry<String, Long> childEntry : childMap.entrySet()) {
                    if (childEntry.getValue().intValue() > 1) {
                        return PlatformResult.failure("导入失败，薪级类别【" + entry.getKey() + "】中存在相同岗位名称");
                    }
                }
            }
        }

        // 查询所有薪级信息
        Example example = new Example(HrmsSalaryLevel.class);
        example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("isEnable", CommonContants.IS_ENABLE_TRUE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        List<HrmsSalaryLevel> allSalaryLevels = hrmsSalaryLevelMapper.selectByExample(example);
        List<String> allSalaryLevelNameList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(allSalaryLevels)) {
            for (HrmsSalaryLevel o : allSalaryLevels) {
                allSalaryLevelNameList.add(o.getSalaryLevelCategory() + "-" + o.getSalaryLevelName());
            }
        }

        Map<String, String> salaryLevelCategoryMap = hrmsDictInfoService.convertDictValueKeyMap(DictContants.SALARY_LEVEL_CATEGORY); // 薪级类别字典数据
        List<HrmsSalaryLevel> insertSalaryLevelList = Lists.newArrayList(); // // 要插入的薪级信息列表
        List<HrmsSalaryLevelWage> insertSalaryLevelWageList = Lists.newArrayList(); // 要插入的薪级工资列表

        for (HrmsSalaryLevelWage item : list) {
            String salaryLevelNameKey = salaryLevelCategoryMap.get(item.getSalaryLevelCategoryName() + "-" + item.getSalaryLevelName()); // 薪级类别-薪级名称
            if (!allSalaryLevelNameList.contains(salaryLevelNameKey)) { // 过滤数据库中已存在的相同的薪级信息
                HrmsSalaryLevel salaryLevel = new HrmsSalaryLevel();
                String salaryLevelId = String.valueOf(IdWork.id.nextId());
                salaryLevel.setSalaryLevelId(salaryLevelId);
                salaryLevel.setSalaryLevelName(item.getSalaryLevelName());
                salaryLevel.setGrade(item.getGrade());
                salaryLevel.setSalaryLevelCategory(salaryLevelCategoryMap.get(item.getSalaryLevelCategoryName()));
                salaryLevel.setIsEnable(CommonContants.IS_ENABLE_TRUE);
                salaryLevel.setIsDeleted(Contants.IS_DELETED_FALSE);
                salaryLevel.setCreateUser(UserInfoHolder.getCurrentUserCode());
                salaryLevel.setCreateUserName(UserInfoHolder.getCurrentUserName());
                salaryLevel.setCreateDate(new Date());
                salaryLevel.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                insertSalaryLevelList.add(salaryLevel);

                item.setSalaryLevelWageId(String.valueOf(IdWork.id.nextId()));
                item.setSalaryLevelId(salaryLevelId);
                item.setIsEnable(CommonContants.IS_ENABLE_TRUE);
                item.setIsDeleted(Contants.IS_DELETED_FALSE);
                item.setCreateUser(UserInfoHolder.getCurrentUserCode());
                item.setCreateUserName(UserInfoHolder.getCurrentUserName());
                item.setCreateDate(new Date());
                item.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                insertSalaryLevelWageList.add(item);
            }
        }
        int insertCnt = 0;
        if (CollectionUtils.isNotEmpty(insertSalaryLevelList)) {
            hrmsSalaryLevelMapper.batchInsert(insertSalaryLevelList);
            hrmsSalaryLevelWageService.batchInsert(insertSalaryLevelWageList);
            insertCnt = insertSalaryLevelList.size();
        }
        return PlatformResult.success(String.valueOf(insertCnt));
    }

    @Override
    public List<HrmsSalaryLevelWage> getNewComboboxList(HrmsSalaryLevel entity) {
        List<HrmsSalaryLevelWage> list = Lists.newArrayList();
        entity.setIsDeleted(Contants.IS_DELETED_FALSE);
        entity.setIsEnable(CommonContants.IS_ENABLE_TRUE);
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        list = hrmsSalaryLevelMapper.getNewComboboxList(entity);
        return list;
    }

    @Override
    public List<Map<String, Object>> getSalaryLevel() {
        List<Map<String, Object>> list = hrmsSalaryLevelMapper.getSalaryType();
        List<Map<String, String>> salaryLevelList = hrmsSalaryLevelMapper.getSalaryLevel();
        List<Map<String, String>> levelList = null;
        for (Map<String, Object> map : list) {
            levelList = new ArrayList<>();
            for (Map<String, String> levelMap : salaryLevelList) {
                if (StrUtil.equals(String.valueOf(map.get("salaryTypeId")), levelMap.get("salaryTypeId"))) {
                    levelList.add(levelMap);
                }
            }
            map.put("salaryLevel", levelList);
        }
        return list;
    }

}

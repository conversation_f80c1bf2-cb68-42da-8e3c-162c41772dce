package cn.trasen.hrms.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.hrms.dao.HrmsResearchCostMapper;
import cn.trasen.hrms.dao.HrmsResearchTopicMapper;
import cn.trasen.hrms.model.HrmsResearchCost;
import cn.trasen.hrms.model.HrmsResearchTopic;
import cn.trasen.hrms.service.HrmsResearchCostService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsResearchCostServiceImpl
 * @Description TODO
 * @date 2021��11��6�� ����3:44:54
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsResearchCostServiceImpl implements HrmsResearchCostService {

	@Autowired
	private HrmsResearchCostMapper mapper;
	
	@Autowired
	private HrmsResearchTopicMapper hrmsResearchTopicMapper;
 
	@Autowired
	private InformationFeignService informationFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsResearchCost record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsResearchCost record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		Example example = new Example(HrmsResearchCost.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("topicId", record.getTopicId());
		List<HrmsResearchCost> records = mapper.selectByExample(example);
		
		BigDecimal totalPriceUse = new BigDecimal(0);
		for (HrmsResearchCost hrmsResearchCost : records) {
			if(record.getId().equals(hrmsResearchCost.getId())) {
				totalPriceUse = totalPriceUse.add(record.getUseCost());
			}else {
				totalPriceUse = totalPriceUse.add(hrmsResearchCost.getUseCost());
			}
		}
		HrmsResearchTopic hrmsResearchTopic = new HrmsResearchTopic();
		hrmsResearchTopic.setId(record.getTopicId());
		hrmsResearchTopic.setTotalPriceUse(totalPriceUse);
		hrmsResearchTopic.setUpdateDate(new Date());
		hrmsResearchTopicMapper.updateByPrimaryKeySelective(hrmsResearchTopic);
		
		HrmsResearchTopic topic = hrmsResearchTopicMapper.selectByPrimaryKey(record.getTopicId());
		BigDecimal surplus = topic.getTotalPrice().subtract(totalPriceUse);
		StringBuffer sb = new StringBuffer();
		sb.append(user.getUsername()).append("更新了课题“")
		.append(hrmsResearchTopic.getTopicName()).append("”经费明细，请您知悉！");
		if(surplus.compareTo(BigDecimal.ZERO) == -1) {
			sb.append("金额已超支").append(surplus.setScale(2, RoundingMode.HALF_UP)).append("元");
		}else {
			sb.append("课题剩余金额").append(surplus.setScale(2, RoundingMode.HALF_UP)).append("元");
		}
		
		NoticeReq notice = NoticeReq.builder()
				.content(sb.toString())
				.noticeType("1")
				.receiver(hrmsResearchTopic.getCreateUser())
				.sender("admin")
				.senderName("系统管理员")
				.subject("经费变更提醒")
				.toUrl("/research/topic").source("科研课题")
				.build();
		informationFeignService.sendNotice(notice);
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsResearchCost record = new HrmsResearchCost();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		HrmsResearchCost hrmsResearchCost = mapper.selectByPrimaryKey(id);
		
		HrmsResearchTopic hrmsResearchTopic = hrmsResearchTopicMapper.selectByPrimaryKey(hrmsResearchCost.getTopicId());
		
		hrmsResearchTopic.setTotalPriceUse(hrmsResearchTopic.getTotalPriceUse().subtract(hrmsResearchCost.getUseCost()));
		hrmsResearchTopic.setUpdateDate(new Date());
		hrmsResearchTopicMapper.updateByPrimaryKeySelective(hrmsResearchTopic);
		
		BigDecimal surplus = hrmsResearchTopic.getTotalPrice().subtract(hrmsResearchTopic.getTotalPriceUse());
		StringBuffer sb = new StringBuffer();
		sb.append(user.getUsername()).append("更新了课题“")
		.append(hrmsResearchTopic.getTopicName()).append("”经费明细，请您知悉！");
		if(surplus.compareTo(BigDecimal.ZERO) == -1) {
			sb.append("金额已超支").append(surplus.setScale(2, RoundingMode.HALF_UP)).append("元");
		}else {
			sb.append("课题剩余金额").append(surplus.setScale(2, RoundingMode.HALF_UP)).append("元");
		}
		
		NoticeReq notice = NoticeReq.builder()
				.content(sb.toString())
				.noticeType("1")
				.receiver(hrmsResearchTopic.getCreateUser())
				.sender("admin")
				.senderName("系统管理员")
				.subject("经费变更提醒")
				.toUrl("/research/topic").source("科研课题")
				.build();
		informationFeignService.sendNotice(notice);
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsResearchCost selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsResearchCost> getDataSetList(Page page, HrmsResearchCost record) {
		Example example = new Example(HrmsResearchCost.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsResearchCost> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	@Transactional(readOnly = false)
	public void saveHrmsResearchCostList(List<HrmsResearchCost> list,String delIds) {
		
		if(StringUtils.isNotBlank(delIds)) {
			String[] idsArray = delIds.split(",");
			for (String id : idsArray) {
				HrmsResearchCost hrmsResearchCost = new HrmsResearchCost();
				hrmsResearchCost.setId(id);
				hrmsResearchCost.setUpdateDate(new Date());
				hrmsResearchCost.setIsDeleted("Y");
				ThpsUser user = UserInfoHolder.getCurrentUserInfo();
				if (user != null) {
					hrmsResearchCost.setUpdateUser(user.getUsercode());
					hrmsResearchCost.setUpdateUserName(user.getUsername());
				}
				mapper.updateByPrimaryKeySelective(hrmsResearchCost);
			}
		}
		
		if(list.size() > 0) {
			String topicId = list.get(0).getTopicId();
			//mapper.deleteByTopicId(topicId);
			
			BigDecimal totalPriceUse = new BigDecimal(0);
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			for (HrmsResearchCost hrmsResearchCost : list) {
				if(StringUtils.isNotBlank(hrmsResearchCost.getId())) {
					hrmsResearchCost.setUpdateDate(new Date());
					hrmsResearchCost.setUpdateUser(user.getUsercode());
					hrmsResearchCost.setUpdateUserName(user.getUsername());
					mapper.updateByPrimaryKeySelective(hrmsResearchCost);
				}else {
					hrmsResearchCost.setId(IdGeneraterUtils.nextId());
					hrmsResearchCost.setCreateDate(new Date());
					hrmsResearchCost.setUpdateDate(new Date());
					hrmsResearchCost.setIsDeleted("N");
					if (user != null) {
						hrmsResearchCost.setCreateUser(user.getUsercode());
						hrmsResearchCost.setCreateUserName(user.getUsername());
						hrmsResearchCost.setUpdateUser(user.getUsercode());
						hrmsResearchCost.setUpdateUserName(user.getUsername());
						hrmsResearchCost.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
					}
					mapper.insertSelective(hrmsResearchCost);
				}
				
				totalPriceUse = totalPriceUse.add(hrmsResearchCost.getUseCost());
			}
			
			HrmsResearchTopic record = new HrmsResearchTopic();
			record.setId(topicId);
			record.setTotalPriceUse(totalPriceUse);
			record.setUpdateDate(new Date());
			hrmsResearchTopicMapper.updateByPrimaryKeySelective(record);
			
			HrmsResearchTopic hrmsResearchTopic = hrmsResearchTopicMapper.selectByPrimaryKey(topicId);
			BigDecimal surplus = hrmsResearchTopic.getTotalPrice().subtract(totalPriceUse);
			StringBuffer sb = new StringBuffer();
			sb.append(user.getUsername()).append("更新了课题“")
			.append(hrmsResearchTopic.getTopicName()).append("”经费明细，请您知悉！");
			if(surplus.compareTo(BigDecimal.ZERO) == -1) {
				sb.append("金额已超支").append(surplus.setScale(2, RoundingMode.HALF_UP)).append("元");
			}else {
				sb.append("课题剩余金额").append(surplus.setScale(2, RoundingMode.HALF_UP)).append("元");
			}
			
			NoticeReq notice = NoticeReq.builder()
					.content(sb.toString())
					.noticeType("1")
					.receiver(hrmsResearchTopic.getCreateUser())
					.sender("admin")
					.senderName("系统管理员")
					.subject("经费变更提醒")
					.toUrl("/research/topic").source("科研课题")
					.build();
			informationFeignService.sendNotice(notice);
		}
	}

	@Override
	public List<HrmsResearchCost> selectHrmsResearchCostList(HrmsResearchCost record) {
		Example example = new Example(HrmsResearchCost.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andEqualTo("topicId", record.getTopicId());
		example.setOrderByClause("create_date desc");
		List<HrmsResearchCost> records = mapper.selectByExample(example);
		return records;
	}
}

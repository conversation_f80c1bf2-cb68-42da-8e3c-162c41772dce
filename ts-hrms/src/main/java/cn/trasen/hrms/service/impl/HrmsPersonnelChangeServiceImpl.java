package cn.trasen.hrms.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.date.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import cn.trasen.homs.bean.oa.EmployeeReq;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.UserDataPermissionVo;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.feign.oa.OAEmployeeFeignService;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsPersonnelChangeMapper;
import cn.trasen.hrms.dao.HrmsTimekeeperMapper;
import cn.trasen.hrms.enums.DateFormatEnum;
import cn.trasen.hrms.enums.GenderTypeEnum;
import cn.trasen.hrms.enums.IncidentAuditStatusEnum;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsPersonnelChange;
import cn.trasen.hrms.model.HrmsPersonnelTransaction;
import cn.trasen.hrms.model.HrmsTimekeeper;
import cn.trasen.hrms.model.HrmsWorkExperienceHospital;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.service.HrmsPersonnelChangeService;
import cn.trasen.hrms.service.HrmsPersonnelTransactionService;
import cn.trasen.hrms.service.HrmsWorkExperienceHospitalService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.UserPermissionManager;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**   
 * @Title: HrmsPositionServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 人员调动 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月25日 上午11:45:39
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsPersonnelChangeServiceImpl implements HrmsPersonnelChangeService {

	@Resource
	HrmsPersonnelChangeMapper hrmsPersonnelChangeMapper;
	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;
//	@Autowired
//	private FileFeignService fileClientService;
//
	@Autowired
	private HrmsPersonnelTransactionService hrmsPersonnelTransactionService;
	
	@Resource
	HrmsTimekeeperMapper hrmsTimekeeperMapper;
	
	@Autowired
	private HrmsDictInfoService hrmsDictInfoService;
	
	
	@Autowired
	OAEmployeeFeignService oAEmployeeFeignService;
	
	@Autowired
	GlobalSettingsFeignService globalSettingsFeignService;
	
	//院内工作经验
	@Autowired
	private HrmsWorkExperienceHospitalService hrmsWorkExperienceHospitalService;
	
	

	/**
	 * @Title: validate
	 * @Description: 数据校验
	 * @param entity
	 * @Return PlatformResult<String>
	 * <AUTHOR>
	 * @date 2020年5月25日 下午2:01:00
	 */
	@Override
	public PlatformResult<String> validate(HrmsPersonnelChange entity) {
		Example example = new Example(HrmsPersonnelChange.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);

		// if (StringUtils.isNotBlank(entity.getEmployeeId())) { // 员工ID
		// example.and().andNotEqualTo("positionId", entity.getEmployeeId());
		// }
		//
		// if (hrmsPersonnelChangeMapper.selectByExample(example).size() > 0) {
		// return PlatformResult.failure("已存在");
		// }
		return PlatformResult.success();
	}

	/**
	 * @Title: insert
	 * @Description: 新增人员调动
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsPersonnelChange entity) {
		entity.setPersonnelChangeId(String.valueOf(IdWork.id.nextId()));
		entity.setApprovalStatus(IncidentAuditStatusEnum.AUDIT_STATUS_1.getKey());
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getSafeGlobalSetting("Y");
		String orgCode = globalSetting.getObject().getOrgCode();
		if("csjkyy".equals(orgCode)) {
			//获取原来的一些属性
			Map<String,String> employeeMap = hrmsEmployeeService.getEmployeeByEmployeeNo(entity.getEmployeeNo());
			entity.setOldJobAttributes(employeeMap.get("job_attributes"));
			entity.setOldOrgAttributes(employeeMap.get("org_attributes"));
			entity.setOldPostType(employeeMap.get("post_type"));	
		}

		int row = hrmsPersonnelChangeMapper.insert(entity);
		if (row > 0) {
			// 更新文件
			// System.out.println("BusinessId:"+hrmsTrainCourse.getBusinessId());
//			if (StringUtils.isNotBlank(entity.getBusinessId())) {
//				fileClientService.updateTempBusinessId(entity.getPersonnelChangeId(), entity.getBusinessId());
//			}
		}
		return row;
	}

	/**
	 * @Title: update
	 * @Description: 更新人员调动
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsPersonnelChange entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());

		return hrmsPersonnelChangeMapper.updateByPrimaryKeySelective(entity);
	}

	/**
	 * @throws Exception 
	 * @Title: incidentAudit
	 * @Description: 审核人员调动
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int incidentAudit(String ids) throws Exception {
		
		Map<String, String> personalIdentityMap = hrmsDictInfoService.convertDictMap(DictContants.PERSONAL_IDENTITY); // 岗位类别
		//拿到调动类型字典
		Map<String, String> employeeTransferTypeMap = hrmsDictInfoService.convertDictMap(DictContants.EMPLOYEE_TRANSFER_TYPE); // 岗位类别
		String[] split = ids.split(",");
		int re = 0;
		HrmsPersonnelChange hrmsPersonnelChange = new HrmsPersonnelChange();
		for (int i = 0; i < split.length; i++) {
			
			hrmsPersonnelChange.setPersonnelChangeId(split[i]);
			hrmsPersonnelChange.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			HrmsPersonnelChange personnelChangeInfo = hrmsPersonnelChangeMapper.getPersonnelChangeById(hrmsPersonnelChange);
			
			if (personnelChangeInfo != null) {
				personnelChangeInfo.setApprovalStatus(IncidentAuditStatusEnum.AUDIT_STATUS_4.getKey());

				//判断是否立即生效，如果不是立即生效则只更新审批状态，通过定时任务进行员工信息的修改
				Date  date = personnelChangeInfo.getChangeStartDate();
				if(date!= null){
					date = DateUtil.beginOfDay(date);
					Date currentDate =  DateUtil.beginOfDay(DateUtil.date());
					if(date.compareTo(currentDate)>0){
						re += hrmsPersonnelChangeMapper.updateByPrimaryKeySelective(personnelChangeInfo);
						continue;
					}
				}
				
				// 更新员工科室id
				String employeeId = personnelChangeInfo.getEmployeeId();
				HrmsEmployee employee = hrmsEmployeeService.findDetailById(employeeId);
				if(employee==null){
					continue;
				}
				String oldOrgId = employee.getOrgId();
				String oldOrgName = employee.getOrgName();
				
				employee.setOrgId(personnelChangeInfo.getNewOrgId()); //新科室
				employee.setPositionId(personnelChangeInfo.getNewPositionId());  //新职务
				
				employee.setPersonalIdentity(personnelChangeInfo.getNewPersonalIdentity());  //新岗位
				employee.setShifouzhongcengganbu(personnelChangeInfo.getShifouzhongcengganbu());
				
				PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getSafeGlobalSetting("Y");
				String orgCode = globalSetting.getObject().getOrgCode();
				if("csjkyy".equals(orgCode)) {
					employee.setJobAttributes(personnelChangeInfo.getJobAttributes());
					employee.setOrgAttributes(personnelChangeInfo.getOrgAttributes());
					employee.setPostType(personnelChangeInfo.getPostType());
				}
				
				hrmsEmployeeService.update(employee);
				
				HrmsPersonnelTransaction  hpt = new HrmsPersonnelTransaction(employee.getEmployeeNo(),
						employee.getEmployeeName(), employeeId, oldOrgId, oldOrgName,personnelChangeInfo.getNewOrgId(), 
						personnelChangeInfo.getNewOrgName(),DateUtils.getStringDateShort(personnelChangeInfo.getChangeStartDate()), employeeTransferTypeMap.get(personnelChangeInfo.getChangeType()), "是", 
						hrmsPersonnelTransactionService.getBatchNumber(),null, null, null);
				hpt.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				hrmsPersonnelTransactionService.insert(hpt);  //添加人员异动记录
				
				//删掉原来的审核人，重新添加审核人
				HrmsTimekeeper del = new HrmsTimekeeper();
				del.setEmployeeId(employeeId);
				hrmsTimekeeperMapper.delete(del);
				HrmsTimekeeper timekeeper = new HrmsTimekeeper();
				timekeeper.setTimekeeperId(personnelChangeInfo.getTimekeeperId());
				timekeeper.setEmployeeName(employee.getEmployeeName());
				timekeeper.setEmployeeId(employeeId);
				timekeeper.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				hrmsTimekeeperMapper.insert(timekeeper);
				re += hrmsPersonnelChangeMapper.updateByPrimaryKeySelective(personnelChangeInfo);
				
				//添加人员档案院内工作经历
				HrmsWorkExperienceHospital hrmsWorkExperienceHospital = new HrmsWorkExperienceHospital();
				hrmsWorkExperienceHospital.setEmployeeId(personnelChangeInfo.getEmployeeId());
				hrmsWorkExperienceHospital.setOldPost(personnelChangeInfo.getOldPersonalIdentityText());
				hrmsWorkExperienceHospital.setNewPost(personnelChangeInfo.getNewPersonalIdentityText());
				hrmsWorkExperienceHospital.setOldPosition(personnelChangeInfo.getOldPositionName());
				hrmsWorkExperienceHospital.setNewPosition(personnelChangeInfo.getNewPositionName());
				hrmsWorkExperienceHospital.setChangeType(personnelChangeInfo.getChangeType());
				hrmsWorkExperienceHospital.setChangeStartDate(personnelChangeInfo.getChangeStartDate());
				hrmsWorkExperienceHospital.setGzjlfj(personnelChangeInfo.getFileId());
				hrmsWorkExperienceHospital.setOldDept(personnelChangeInfo.getOldOrgName());
				hrmsWorkExperienceHospital.setNewDept(personnelChangeInfo.getNewOrgName());
				hrmsWorkExperienceHospital.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				hrmsWorkExperienceHospitalService.save(hrmsWorkExperienceHospital);
				
				//同步人员档案岗位信息
				HrmsEmployee hrmsEmployee = new HrmsEmployee();
				
				hrmsEmployee.setEmployeeId(employeeId);
				
				hrmsEmployee.setEmployeeNo(employee.getEmployeeNo());
				
				hrmsEmployee.setPersonalIdentity(personnelChangeInfo.getNewPersonalIdentity());
				
				hrmsEmployee.setPositionId(personnelChangeInfo.getNewPositionId());
				
				
				if( "lyszyy".equals(orgCode)) {  //浏阳中特殊处理
					if("4".equals(personnelChangeInfo.getChangeType())) {
						
						hrmsEmployee.setEmployeeStatus("14");
					}
					
					//如果是升职 ，修改首次任职时间
					if("2".equals(personnelChangeInfo.getChangeType())) {
						Map<String,String> paramMap = new HashMap();
						paramMap.put("employeeId", employeeId);
						paramMap.put("changeStartDate",DateUtils.getStringDateShort(personnelChangeInfo.getChangeStartDate()));
						hrmsPersonnelChangeMapper.updateZcgbscrzsj(paramMap);
					}
				}
				
				hrmsEmployeeService.update(hrmsEmployee);
				
			}
			
		}
		return re;
	}

	private HrmsEmployee setPersonalIdentity(String newPersonalIdentity) {
		// TODO Auto-generated method stub
		return null;
	}

	/**
	 * @Title: deleted
	 * @Description: 删除人员调动
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsPersonnelChange personnelChangeInfo = hrmsPersonnelChangeMapper.selectByPrimaryKey(id);
		if (personnelChangeInfo != null) {
			personnelChangeInfo.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsPersonnelChangeMapper.updateByPrimaryKeySelective(personnelChangeInfo);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取人员调动列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsPosition>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsPersonnelChange> getDataList(Page page, HrmsPersonnelChange entity) {
		
//		//数据权限
//		cn.trasen.BootComm.utils.ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
//		String orgRang = thpsUser.getOrgRang();
//		if(!UserInfoHolder.ISADMIN()) {	// 是否管理员   
//			if(!StringUtil.isEmpty(orgRang)) {//查询组织范围数据
//				 entity.setHtOrgIdList(orgRang);
//			}
//		}
		
		
		
		UserDataPermissionVo userDataPermissionVo = UserPermissionManager.getInstance().getHrmsUserDataPermission();
		
		if(null!=userDataPermissionVo) {
			
			if(CollectionUtils.isNotEmpty(userDataPermissionVo.getOrgCodeList())){
				
				entity.setOrgIds(userDataPermissionVo.getOrgCodeList());
			}
			if(StringUtils.isNotBlank(userDataPermissionVo.getUserCode())){
				
				entity.setCreateUser(userDataPermissionVo.getUserCode());
			}
			
		}
		
		//长沙经开医院调动管理 护理部、医务科根据人员类别进行权限控制  医务科（医疗、医技、药剂） 护理部（护理类）
		PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getSafeGlobalSetting("Y");
		
		String orgCode = globalSetting.getObject().getOrgCode();
		if("csjkyy".equals(orgCode)) {
			//获取用户角色
			ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
			String roleCode = thpsUser.getSysRoleCode();
			if(roleCode.contains("HULIROLE")) { //护理角色
				entity.setRole("huliRole");
			}
			if(roleCode.contains("YIWUROLE")) { //护理角色
				entity.setRole("yiwuRole");
			}
		}
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsPersonnelChange> list = hrmsPersonnelChangeMapper.getDataSetList(page, entity);
		Map<String, String> employeeTransferTypeMap = hrmsDictInfoService.convertDictMap(DictContants.EMPLOYEE_TRANSFER_TYPE); // 调动类型
		//获取调动类型下拉框
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsPersonnelChange item : list) {
				item.setId(item.getPersonnelChangeId());
				item.setGenderText(GenderTypeEnum.getValByKey(item.getGender())); // 性别文本值
				item.setChangeTypeText(employeeTransferTypeMap.get(item.getChangeType()));// 调动类型
				item.setApprovalStatusText(IncidentAuditStatusEnum.getValByKey(item.getApprovalStatus()));// 审批状态
			}
		}
		return list;
	}

	/**
	 * @Title: getList
	 * @Description: 查询人员调动列表(不分页)
	 * @param entity
	 * @Return List<HrmsPosition>
	 * <AUTHOR>
	 * @date 2020年4月16日 下午2:56:02
	 */
	@Override
	public List<HrmsPersonnelChange> getList(HrmsPersonnelChange entity) {
		
		//数据权限
		ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
		String orgRang = thpsUser.getOrgRang();
		if(!UserInfoHolder.ISADMIN()) {	// 是否管理员   
			if(!StringUtil.isEmpty(orgRang)) {//查询组织范围数据
				 entity.setHtOrgIdList(orgRang);
			}
		}
		Map<String, String> employeeTransferTypeMap = hrmsDictInfoService.convertDictMap(DictContants.EMPLOYEE_TRANSFER_TYPE); // 调动类型
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsPersonnelChange> list = hrmsPersonnelChangeMapper.getList(entity);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsPersonnelChange item : list) {
				if (item.getChangeStartDate() != null) {
					item.setChangeStartDateExport(sdf.format(item.getChangeStartDate()));
				}
				//调动类型改为取字典
				item.setChangeTypeText(employeeTransferTypeMap.get(item.getChangeType()));// 调动类型
				item.setApprovalStatusText(IncidentAuditStatusEnum.getValByKey(item.getApprovalStatus()));// 审批状态
			}
		}
		return list;
	}

	/**
	 * @Title: getTransferOrderData
	 * @Description: 获取调动单人员数据
	 * @param entity
	 * @Return List<HrmsPersonnelChange>
	 * <AUTHOR>
	 * @date 2020年6月22日 下午2:03:09
	 */
	@Override
	public List<HrmsPersonnelChange> getTransferOrderData(HrmsPersonnelChange entity) {
		List<HrmsPersonnelChange> list = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(entity.getChangeIdList())) {
			entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			list = hrmsPersonnelChangeMapper.getTransferOrderData(entity);
			if (CollectionUtils.isNotEmpty(list)) {
				for (HrmsPersonnelChange o : list) {
					o.setGenderText(GenderTypeEnum.getValByKey(o.getGender())); // 性别文本值
					o.setGenerateDate(new DateTime().toString(DateFormatEnum.YYYY_MM_DD.getValue()));
				}
			}
		}
		return list;
	}

	@Override
	public List<HrmsPersonnelChange> getChangeStatusByEmployeeId(String employeeId) {
		return hrmsPersonnelChangeMapper.getChangeStatusByEmployeeId(employeeId);
	}

	/**
	 * 取消调动
	 */
	@Override
	@Transactional(readOnly = false)
	public int cancel(String personnelChangeId)  throws Exception  {
	
		HrmsPersonnelChange hrmsPersonnelChange = new HrmsPersonnelChange();
			hrmsPersonnelChange.setPersonnelChangeId(personnelChangeId);
			hrmsPersonnelChange.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			HrmsPersonnelChange personnelChangeInfo = hrmsPersonnelChangeMapper.getPersonnelChangeById(hrmsPersonnelChange);  //找到数据
			
			if (personnelChangeInfo != null) {
				personnelChangeInfo.setApprovalStatus(IncidentAuditStatusEnum.AUDIT_STATUS_1.getKey());
				
				// 更新员工科室id
				String employeeId = personnelChangeInfo.getEmployeeId();
				HrmsEmployee employee = hrmsEmployeeService.findDetailById(employeeId);
				
				employee.setOrgId(personnelChangeInfo.getOldOrgId()); //新科室
				employee.setPositionId(personnelChangeInfo.getOldPositionId());  //新职务
				
				employee.setPersonalIdentity(personnelChangeInfo.getOldPersonalIdentity());  //新岗位
				
				hrmsEmployeeService.update(employee);
				
				//修改oa表机构
				EmployeeReq employeeReq = new EmployeeReq();
				employeeReq.setEmployeeId(employeeId);
				employeeReq.setOrgCode(personnelChangeInfo.getOldOrgId());
				employeeReq.setOrgId(personnelChangeInfo.getOldOrgId());
				employeeReq.setOrgName(personnelChangeInfo.getOldOrgName());
				oAEmployeeFeignService.insertOrUpdate(employeeReq);
				
				
				//删除人员异动记录
				hrmsPersonnelTransactionService.deleteByEmployeeNoAndDate(employee.getEmployeeNo(),DateUtils.getStringDateShort(personnelChangeInfo.getChangeStartDate()));
				
				//删除院内工作经历       
				hrmsWorkExperienceHospitalService.deleteByEmployeeNoAndDate(personnelChangeInfo.getEmployeeId(), DateUtils.getStringDateShort(personnelChangeInfo.getChangeStartDate()));
				
				//同步人员档案岗位信息
				
				HrmsEmployee hrmsEmployee = new HrmsEmployee();
				
				hrmsEmployee.setEmployeeId(employeeId);
				
				hrmsEmployee.setPersonalIdentity(personnelChangeInfo.getOldPersonalIdentity());
				
				hrmsEmployee.setPositionId(personnelChangeInfo.getOldPositionId());
				
				//浏阳中才处理
				PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getSafeGlobalSetting("Y");
				
				String orgCode = globalSetting.getObject().getOrgCode();
				
				if( "lyszyy".equals(orgCode)) {  //浏阳中特殊处理
					if("4".equals(personnelChangeInfo.getChangeType())) {
						hrmsEmployee.setEmployeeStatus("1");
					}
					
					//如果是升职 ，修改首次任职时间
					if("2".equals(personnelChangeInfo.getChangeType())) {
						Map<String,String> paramMap = new HashMap();
						paramMap.put("employeeId", employeeId);
						paramMap.put("changeStartDate",DateUtils.getStringDateShort(personnelChangeInfo.getChangeStartDate()));
						hrmsPersonnelChangeMapper.updateZcgbscrzsj(paramMap);
					}
				}
				
				hrmsEmployeeService.update(hrmsEmployee);
				hrmsPersonnelChangeMapper.updateByPrimaryKeySelective(personnelChangeInfo);
			}
		return 1;
	
	}

}

package cn.trasen.hrms.service.impl;

import java.time.Instant;
import java.time.LocalDate;
import java.time.Year;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsEvaluationResultMapper;
import cn.trasen.hrms.model.HrmsEvaluationResult;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.service.HrmsEvaluationResultService;

import javax.annotation.Resource;

/**
 * 员工考核记录实现类
 * <AUTHOR>
 *
 */
@Service
public class HrmsEvaluationResultServiceImpl implements HrmsEvaluationResultService {
		
	@Resource
	private HrmsEvaluationResultMapper hrmsEvaluationResultMapper;
	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;
	
	@Override
	public int insert(HrmsEvaluationResult entity) {
		entity.setId(String.valueOf(IdWork.id.nextId()));
		
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsEvaluationResultMapper.insert(entity);
	}

	@Override
	public int update(HrmsEvaluationResult entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsEvaluationResultMapper.updateByPrimaryKeySelective(entity);
	}

	@Override
	public int deleted(String id) {
		HrmsEvaluationResult bean = hrmsEvaluationResultMapper.selectByPrimaryKey(id);
		if (bean != null) {
			bean.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsEvaluationResultMapper.updateByPrimaryKeySelective(bean);
	}

	@Override
	public List<HrmsEvaluationResult> getDataList(Page page, HrmsEvaluationResult entity) {
		
		if(!StringUtil.isEmpty(entity.getAssessYear())) {
			String[] split = entity.getAssessYear().split(" - ");
			List<String> yearList = new ArrayList<>();
			LocalDate start = LocalDate.of(Integer.parseInt(split[0]), 1, 1);
	        LocalDate end = LocalDate.of(Integer.parseInt(split[1]), 3, 31);
	        long timstrap = start.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
	        long endtime = end.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
	        for (String s : getTimePeriodFromTwoTime(timstrap, endtime)) {
	        	yearList.add(s);
	        }
	        entity.setYearList(yearList);
		}

		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsEvaluationResultMapper.getDataList(page, entity);
	}
	
	private static List<String> getTimePeriodFromTwoTime(Long startTime, Long endTime) {
        LocalDate start = Instant.ofEpochMilli(startTime).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        LocalDate end = Instant.ofEpochMilli(endTime).atZone(ZoneOffset.ofHours(8)).toLocalDate();

        List<String> result = new ArrayList<>();

        Year startyear = Year.from(start);
        Year endYear = Year.from(end);
        // 包含最后一个时间
        for (long i = 0; i <= ChronoUnit.YEARS.between(startyear, endYear); i++) {
            result.add(startyear.plusYears(i).toString());
        }
        return result;
    }



	@Override
	public List<HrmsEvaluationResult> getList(HrmsEvaluationResult entity) {
		// TODO Auto-generated method stub
		return hrmsEvaluationResultMapper.getList(entity);
	}

	@Override
	@Transactional
	public PlatformResult<Map<String, Object>> excelImportAssess(List<HrmsEvaluationResult> list) {
		Map<String,Object> retMap = new HashMap<>();
		//验证重复身份证
		Map<Object, Long> collectNumber = list.stream()
				.collect(Collectors.groupingBy(HrmsEvaluationResult::getIdentityNumber, Collectors.counting()));
		// 筛出重复身份证
		List<Object> number = collectNumber.keySet().stream().filter(key -> collectNumber.get(key) > 1)
				.collect(Collectors.toList());
		// 可以知道有哪些姓名有重复
		if(number != null && number.size() > 0) {
			throw new RuntimeException("要导入的数据中存在重复身份证号码"+number.get(0));
		}
		
		List<String> identityNumberList = new ArrayList<>();
		//拿到所有工号
		list.stream().forEach(item ->{
			identityNumberList.add(item.getIdentityNumber().trim().toLowerCase());
		});
		List<EmployeeResp> empList = hrmsEmployeeService.getEmployeeDetailByIdentityNumber(identityNumberList);
		Map<String, EmployeeResp> empMap = new HashMap<String, EmployeeResp>();
		if(empList != null && empList.size() > 0) {
			empMap = empList.stream()
					.collect(Collectors.toMap(EmployeeResp::getIdentityNumber, Function.identity(), (key1, key2) -> key2));
		}
		
		for (int i = 0; i < list.size(); i++) {
			if(empMap != null && empMap.size() > 0) {
				//为空的数据记录
				String employeeId = "";
				if(null != empMap.get(list.get(i).getIdentityNumber().trim().toLowerCase())) {
					employeeId = empMap.get(list.get(i).getIdentityNumber().trim().toLowerCase()).getEmployeeId();
				}
				if(StringUtil.isEmpty(employeeId)) {
					throw new RuntimeException("姓名："+list.get(i).getEmployeeName() + " 在系统中找不到对应的人员信息</br>");
				}

				if(StringUtil.isEmpty(list.get(i).getAssessYear())) {
					throw new RuntimeException("姓名："+list.get(i).getEmployeeName() + " 的考核年份为空</br>");
				}else {
                    String assessYear = list.get(i).getAssessYear().trim();
				    if(assessYear.endsWith("年")){
                        assessYear = assessYear.replace("年","");
                    }
					list.get(i).setAssessYear(assessYear);
				}
				
				String assessOrg = list.get(i).getAssessOrg();
				if(StringUtil.isEmpty(assessOrg)) {
					throw new RuntimeException("姓名："+list.get(i).getEmployeeName() + " 的上报单位为空</br>");
				}else {
					list.get(i).setAssessOrg(list.get(i).getAssessOrg().trim());
				}
				

				if(StringUtil.isEmpty(list.get(i).getAssessResult())) {
					throw new RuntimeException("姓名："+list.get(i).getEmployeeName() + " 的考核结果为空</br>");
				}else {
                    String assessResult = list.get(i).getAssessResult().trim();
                    //去除考核的中文括号
                    assessResult = assessResult.replace("（","(").replace("）",")");
					list.get(i).setAssessResult(assessResult);
				}
				
				list.get(i).setEmployeeId(employeeId);  //插入员工id
				list.get(i).setId(String.valueOf(IdWork.id.nextId()));
				list.get(i).setIsDeleted(Contants.IS_DELETED_FALSE);
				list.get(i).setCreateUser(UserInfoHolder.getCurrentUserCode());
				list.get(i).setCreateUserName(UserInfoHolder.getCurrentUserName());
				list.get(i).setCreateDate(new Date());
				list.get(i).setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			}else {
				throw new RuntimeException("在系统中找不到 "+list.get(0).getEmployeeName() +"对应的人员信息</br>");
			}
		}
		
		//先删除这个年份的数据
		HrmsEvaluationResult delRecord = new HrmsEvaluationResult();
		delRecord.setAssessYear(list.get(0).getAssessYear());
		hrmsEvaluationResultMapper.delete(delRecord);
		
		int count = 0;
		if(list.size() > 500) {
	        int toIndex = 500;
	        int listSize = list.size();
	        for(int i= 0; i<list.size();i+=500){
	            //作用为toIndex最后没有500条数据则剩余几条list中就装几条
	            if(i+500 > listSize){
	                toIndex = listSize-i;
	            }
	            List<HrmsEvaluationResult> sepaList = list.subList(i,i+toIndex);
	            count += hrmsEvaluationResultMapper.batchInsert(sepaList);
	        }
		}else {
			count = hrmsEvaluationResultMapper.batchInsert(list);
		}
		
		retMap.put("total",   count);
		retMap.put("info",   "总共"+list.size() + "条" +"导入"+count + "条");
		retMap.put("success", true);
		return PlatformResult.success(retMap);
		
	}

}

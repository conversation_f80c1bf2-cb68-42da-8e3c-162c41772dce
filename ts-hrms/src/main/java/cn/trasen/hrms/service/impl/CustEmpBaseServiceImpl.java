package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.CustEmpBaseMapper;
import cn.trasen.hrms.model.CustEmpBase;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.service.CustEmpBaseService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CustEmpBaseServiceImpl
 * @Description TODO
 * @date 2024��10��15�� ����3:00:23
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CustEmpBaseServiceImpl implements CustEmpBaseService {

	@Autowired
	private CustEmpBaseMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(CustEmpBase record) {
		record.setEmployeeId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CustEmpBase record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CustEmpBase record = new CustEmpBase();
		record.setEmployeeId(IdGeneraterUtils.nextId());
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public CustEmpBase selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CustEmpBase> getDataSetList(Page page, CustEmpBase record) {
		Example example = new Example(CustEmpBase.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<CustEmpBase> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public CustEmpBase findEmployeeByCode(String employeeNo) {
		
		return mapper.findEmployeeByCode(employeeNo);
		
	}

	@Override
	public List<CustEmpBase> getDataList(CustEmpBase record) {
		Example example = new Example(CustEmpBase.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return mapper.selectByExample(example);
	}

	@Override
	public Set<String> findAllEmployeeId() {
		return mapper.findAllEmployeeId();
	}

	@Override
	@Transactional(readOnly = false)
	public void initEmployee(HrmsEmployee hrmsEmployee) {
		mapper.initEmployee(hrmsEmployee);
	}

	@Override
	@Transactional(readOnly = false)
	public void updateCodePwd(HrmsEmployee hrmsEmployee) {
		mapper.updateCodePwd(hrmsEmployee);
	}
	
	@Override
	@Transactional(readOnly = false)
	public void updateByIdentityNumber(Map<String, Object> data) {
		mapper.updateByIdentityNumber(data);
	}
	
}

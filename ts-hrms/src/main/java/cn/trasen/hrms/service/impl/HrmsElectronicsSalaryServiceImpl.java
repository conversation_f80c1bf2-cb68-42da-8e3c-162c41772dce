package cn.trasen.hrms.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsElectronicsSalarySheetMapper;
import cn.trasen.hrms.model.HrmsElectronicsSalarySheet;
import cn.trasen.hrms.service.HrmsElectronicsSalaryService;
import cn.trasen.hrms.utils.DateUtils;
import tk.mybatis.mapper.entity.Example;

/**    
  * <P> @Description: 电子工资条实现类</p>
  * <P> @Date: 2020年7月16日  下午4:02:41 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 

@Service   
public class HrmsElectronicsSalaryServiceImpl implements HrmsElectronicsSalaryService {

	@Autowired
	HrmsElectronicsSalarySheetMapper hrmsElectronicsSalarySheetMapper;

	/**   
	 * <p>Title: getDataList</p>   
	 * <p>Description:查询电子工资条 </p>   
	 * @param page
	 * @param entity
	 * @return   
	 */ 
	@Override
	public List<HrmsElectronicsSalarySheet> getDataList(Page page, HrmsElectronicsSalarySheet entity) {
		
		Example example = new Example(HrmsElectronicsSalarySheet.class);
		
		if(!StringUtil.isEmpty(entity.getSendMonth())) {
			example.createCriteria().andLike("sendMonth", "%"+entity.getSendMonth()+"%");
		}
		if(!StringUtil.isEmpty(entity.getSheetZydm())) {
			example.and().andLike("sheetZydm", "%"+entity.getSheetZydm()+"%");
		}
		if(!StringUtil.isEmpty(entity.getSheetEmpname())) {
			example.and().andLike("sheetEmpname", "%"+entity.getSheetEmpname()+"%");
		}
		if(!StringUtil.isEmpty(entity.getDeptName())) {
			example.and().andLike("deptName", "%"+entity.getDeptName()+"%");
		}
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
//		return hrmsElectronicsSalarySheetMapper.selectByExampleAndRowBounds(example, page);
		return hrmsElectronicsSalarySheetMapper.getDataList(entity, page);
		
	}

	@Override
	@Transactional
	public String excelImportEmployee(List<HrmsElectronicsSalarySheet> list,String date) {
		
		Example example = new Example(HrmsElectronicsSalarySheet.class);
		example.createCriteria().andEqualTo("sendMonth", date);
		hrmsElectronicsSalarySheetMapper.deleteByExample(example);
		
		Map<String, Object> param = new HashMap<>();
		param.put("list", list);
		param.put("sendMonth", date);
		param.put("create_date", new Date());
		param.put("create_user", UserInfoHolder.getCurrentUserCode());
		param.put("create_user_name", UserInfoHolder.getCurrentUserName());
		int re = hrmsElectronicsSalarySheetMapper.batchInsert(param);
		return re == 0 ? "0" : String.valueOf(re);
	}

	@Override
	public HrmsElectronicsSalarySheet getCurrentPersonSalary(String currentUserCode, String dateStr) {
		return hrmsElectronicsSalarySheetMapper.getCurrentPersonSalary(currentUserCode,dateStr);
	}

	@Override
	public List<HrmsElectronicsSalarySheet> getPersonageDataList(Page page, HrmsElectronicsSalarySheet entity) {
		List<HrmsElectronicsSalarySheet> personageDataList = new ArrayList<>();
		if (StringUtil.isEmpty(entity.getSheetZydm()) && StringUtil.isEmpty(entity.getSheetEmpname())) {
			return personageDataList;
		}
		if(!StringUtil.isEmpty(entity.getSendMonth())) {
			String[] split = entity.getSendMonth().split(" - ");
			List<String> dateSection = DateUtils.getDateSection(split[0], split[1]);
			entity.setMonthList(dateSection);
		}else {
			return personageDataList;
		}
		personageDataList = hrmsElectronicsSalarySheetMapper.getPersonageDataList(entity, page);
		return personageDataList;
	}

	@Override
	public List<HrmsElectronicsSalarySheet> getHospitalDataList(Page page, HrmsElectronicsSalarySheet entity) {
		 List<HrmsElectronicsSalarySheet> hospitalDataList = hrmsElectronicsSalarySheetMapper.getHospitalDataList(entity,page);
		return hospitalDataList;
	}
}

package cn.trasen.hrms.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;

import cn.trasen.BootComm.utils.ApplicationUtils;
import cn.trasen.homs.bean.base.FileAttachmentResp;
import cn.trasen.homs.bean.document.Attachment;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.FileAttachmentFeignService;
import cn.trasen.homs.feign.oa.DocumentFeignClient;
import cn.trasen.hrms.dao.HrmsEqualPayMapper;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsEqualPay;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.service.HrmsEqualPayService;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsEqualPayServiceImpl
 * @Description TODO
 * @date 2021��9��9�� ����2:26:05
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsEqualPayServiceImpl implements HrmsEqualPayService {

	@Autowired
	private HrmsEqualPayMapper mapper;
	
	@Autowired
	private DocumentFeignClient documentFeignClient;
	
	@Autowired
	private FileAttachmentFeignService fileAttachmentFeignService;
	
	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsEqualPay record) {
		record.setId(ApplicationUtils.GUID32());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		//修改员工编制类型
		if(StringUtils.isNotBlank(record.getEmployeeCode())) {
			
			HrmsEmployee hrmsEmployee = hrmsEmployeeService.findEmployeeByCode(record.getEmployeeCode());
			
			hrmsEmployee.setEstablishmentType("3");
			
			hrmsEmployeeService.update(hrmsEmployee);
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsEqualPay record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsEqualPay record = new HrmsEqualPay();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsEqualPay selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public List<HrmsEqualPay> getDataSetList(Page page, HrmsEqualPay record) {
		Example example = new Example(HrmsEqualPay.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if(StringUtils.isNotBlank(record.getEmployeeName())) {
			example.and().andLike("employeeName", "%"+record.getEmployeeName()+"%").orLike("employeeCode","%"+record.getEmployeeName()+"%");
		}
		
		List<HrmsEqualPay> records = mapper.selectByExampleAndRowBounds(example, page);
		if(CollUtil.isNotEmpty(records)) {
			List<String> empnoList = records.stream().map(HrmsEqualPay::getEmployeeCode).collect(Collectors.toList());
			List<HrmsEmployee> empList = hrmsEmployeeService.findByEmployeeNos(empnoList);
			for (HrmsEqualPay hrmsEqualPay : records) {
				for (HrmsEmployee emp : empList) {
					if(StrUtil.equals(hrmsEqualPay.getEmployeeCode(),emp.getEmployeeNo())){
						hrmsEqualPay.setIdentityNumber(emp.getIdentityNumber());
					}
				}
			}
		}
		return records;
	}
	
	/**
	 * 
	* @Title: synEmployeeMessage  
	* @Description: 同工同酬流程审批后数据同步
	* @Params: @param dataMap      
	* @Return: void
	* <AUTHOR>
	* @date:2021年9月9日
	* @Throws
	 */
	@Transactional(readOnly = false)
	public void synEmployeeMessage(Map<String,Object> dataMap) {
		
		try {
			String userCode = dataMap.get("L_LaunchUserCode").toString();
			
			String userName = (String) dataMap.get("L_LaunchUserName");//流程发起人名称
			
		    String L_LaunchDeptCode = (String) dataMap.get("L_LaunchDeptCode");//流程发起人部门编码
		    String L_LaunchDeptName = (String) dataMap.get("L_LaunchDeptName");//流程发起人部门名称
		    String lBusinessId = (String) dataMap.get("L_BusinessId");
		    String workflowInstId = (String) dataMap.get("workflowInstId");
		    String workflowNo = (String) dataMap.get("workflowNo");
		    String equalPayDate =(String) dataMap.get("equalPayDate");
		    
		    if(StringUtils.isNotBlank(equalPayDate)) {
		    	HrmsEqualPay pay = HrmsEqualPay.class.newInstance();
				
				String businessId = ApplicationUtils.GUID32();

				
				BeanUtils.populate(pay, dataMap);
				
				pay.setEqualPayDate(equalPayDate);
				pay.setEmployeeName(userName);
				pay.setEmployeeCode(userCode);
				pay.setId(IdGeneraterUtils.nextId());
				pay.setCreateDate(new Date());
				pay.setUpdateDate(new Date());
				pay.setIsDeleted("N");
				pay.setOrgId(L_LaunchDeptCode);
				pay.setOrgName(L_LaunchDeptName);
				pay.setBusinessId(businessId);
				pay.setLBusinessId(lBusinessId);
				pay.setWorkflowInstId(workflowInstId);
				pay.setWorkflowNo(workflowNo);
				pay.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				int count = mapper.insertSelective(pay);
				
				//新增后 同步附件信息
				if(count>0) {
					
					String educationFile = dataMap.get("educationFile")!=null?dataMap.get("educationFile").toString():"";
					String professionalFile = null!=dataMap.get("professionalFile")?dataMap.get("professionalFile").toString():"";
					String zyzsFile = null!=dataMap.get("zyzsFile")?dataMap.get("zyzsFile").toString():"";
					String gpFile = null!=dataMap.get("gpFile")?dataMap.get("gpFile").toString():"";
					String xuexinNetFile = null!=dataMap.get("xuexinNetFile")?dataMap.get("xuexinNetFile").toString():"";
					String idCardFile = null!=dataMap.get("idCardFile")?dataMap.get("idCardFile").toString():"";
					
//					synFile(educationFile, userCode, userName);
//					synFile(professionalFile, userCode, userName);
//					synFile(zyzsFile, userCode, userName);
//					synFile(gpFile, userCode, userName);
//					synFile(xuexinNetFile, userCode, userName);
//					synFile(idCardFile, userCode, userName);
					
					synFileNew(educationFile, professionalFile,zyzsFile,gpFile,xuexinNetFile,idCardFile,userCode, userName,businessId);
					
					pay.setBusinessId(businessId);
					
				}
				
				//修改人员编制类型为同工同酬
				HrmsEmployee hrmsEmployee = hrmsEmployeeService.findEmployeeByCode(userCode);
				
				hrmsEmployee.setEstablishmentType("3");
				
				hrmsEmployeeService.update(hrmsEmployee);
		    }
			
		} catch (Exception e) {
			log.error("同工同筹回调异常："+e.getMessage(),e);
		} 
		
	}
	
	/**  
	* @Title: synFileNew  
	* @Description: 同步附件
	* @Params: @param educationFile
	* @Params: @param professionalFile
	* @Params: @param zyzsFile
	* @Params: @param gpFile
	* @Params: @param xuexinNetFile
	* @Params: @param idCardFile
	* @Params: @param userCode
	* @Params: @param userName      
	* @Return: void
	* <AUTHOR>
	* @date:2021年9月15日
	* @Throws    
	*/  
	private void synFileNew(String educationFile, String professionalFile, String zyzsFile, String gpFile,
			String xuexinNetFile, String idCardFile, String userCode, String userName,String businessId) {
		
		StringBuffer fileIds = new StringBuffer();
		
		
		if(StringUtils.isNotBlank(educationFile)) {
			
			fileIds.append(educationFile+",");
		}
		
		if(StringUtils.isNotBlank(professionalFile)) {
			
			fileIds.append(professionalFile+",");
		}
		
		if(StringUtils.isNotBlank(zyzsFile)) {
			
			fileIds.append(zyzsFile+",");
		}
		
		if(StringUtils.isNotBlank(gpFile)) {
			
			fileIds.append(gpFile+",");
		}
		
		if(StringUtils.isNotBlank(xuexinNetFile)) {
			
			fileIds.append(xuexinNetFile+",");
		}
		
		if(StringUtils.isNotBlank(idCardFile)) {
			
			fileIds.append(idCardFile);
		}
		
		if(StringUtils.isNotBlank(fileIds.toString())) {
			
			PlatformResult<List<Attachment>> attList = documentFeignClient.selectByIds(fileIds.toString(), UserInfoHolder.getToken());
			
			List<FileAttachmentResp> fileAttachmentRespList = new ArrayList<>();
        	for (Attachment attachmentReq : attList.getObject()) {
        		FileAttachmentResp fileAttachmentResp = new FileAttachmentResp();
        		fileAttachmentResp.setId(attachmentReq.getId());
        		fileAttachmentResp.setOriginalName(attachmentReq.getOriginalName());
        		fileAttachmentResp.setModuleName(attachmentReq.getModuleName());
        		fileAttachmentResp.setFilePath(attachmentReq.getFilePath());
        		fileAttachmentResp.setFileSize(Long.valueOf(attachmentReq.getFileSize()));
        		fileAttachmentResp.setRealPath(attachmentReq.getRealPath());
        		fileAttachmentResp.setFileExtension(attachmentReq.getFileExtension());
        		fileAttachmentResp.setBusinessId(businessId);
        		fileAttachmentResp.setCreateDate(new Date());
        		fileAttachmentResp.setUpdateUser(userCode);
        		fileAttachmentResp.setUpdateDate(new Date());
        		fileAttachmentResp.setCreateUser(userCode);
        		fileAttachmentResp.setCreateUserName(userName);
        		fileAttachmentResp.setIsDeleted("N");
        		fileAttachmentRespList.add(fileAttachmentResp);
			}
        	fileAttachmentFeignService.saveAttachmentList(JSON.toJSONString(fileAttachmentRespList));
		}
		
	}

	/**
	 * 
	* @Title: synFile  
	* @Description: 同步附件信息
	* @Params: @param fileIds      
	* @Return: void
	* <AUTHOR>
	* @date:2021年9月9日
	* @Throws
	 */
	public void synFile(String fileIds,String userCode,String userName) {
		
		if(StringUtils.isNotBlank(fileIds)) {
        	PlatformResult<List<Attachment>> attList = documentFeignClient.selectByIds(fileIds, UserInfoHolder.getToken());
        	if(attList.isSuccess()) {
	        	List<FileAttachmentResp> fileAttachmentRespList = new ArrayList<>();
	        	for (Attachment attachmentReq : attList.getObject()) {
	        		FileAttachmentResp fileAttachmentResp = new FileAttachmentResp();
	        		fileAttachmentResp.setId(attachmentReq.getId());
	        		fileAttachmentResp.setOriginalName(attachmentReq.getOriginalName());
	        		fileAttachmentResp.setModuleName(attachmentReq.getModuleName());
	        		fileAttachmentResp.setFilePath(attachmentReq.getFilePath());
	        		fileAttachmentResp.setFileSize(Long.valueOf(attachmentReq.getFileSize()));
	        		fileAttachmentResp.setRealPath(attachmentReq.getRealPath());
	        		fileAttachmentResp.setFileExtension(attachmentReq.getFileExtension());
	        		fileAttachmentResp.setBusinessId(fileIds);
	        		fileAttachmentResp.setCreateDate(new Date());
	        		fileAttachmentResp.setUpdateUser(userCode);
	        		fileAttachmentResp.setUpdateDate(new Date());
	        		fileAttachmentResp.setCreateUser(userCode);
	        		fileAttachmentResp.setCreateUserName(userName);
	        		fileAttachmentResp.setIsDeleted("N");
	        		fileAttachmentRespList.add(fileAttachmentResp);
				}
	        	fileAttachmentFeignService.saveAttachmentList(JSON.toJSONString(fileAttachmentRespList));
	        }
        }
	}
}

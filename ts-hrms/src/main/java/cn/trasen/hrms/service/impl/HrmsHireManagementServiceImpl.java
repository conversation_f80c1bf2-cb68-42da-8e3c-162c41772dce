package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsHireManagementMapper;
import cn.trasen.hrms.enums.HireStatusEnum;
import cn.trasen.hrms.model.HrmsHireManagement;
import cn.trasen.hrms.model.HrmsInterviewManagement;
import cn.trasen.hrms.service.HrmsHireManagementService;
import cn.trasen.hrms.service.HrmsInterviewManagementService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsHireManagementServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 录用管理 业务层接口实现类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月22日 下午2:43:21 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsHireManagementServiceImpl implements HrmsHireManagementService {

	@Autowired
	HrmsHireManagementMapper hrmsHireManagementMapper;
	@Autowired
	HrmsInterviewManagementService hrmsInterviewManagementService;

	/**
	 * @Title: hire
	 * @Description: 录用
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年5月22日 下午2:50:25
	 */
	@Override
	@Transactional(readOnly = false)
	public int hire(HrmsHireManagement entity) {
		Assert.hasText(entity.getInterviewManagementId(), "interviewManagementId must be not null.");

		entity.setHireManagementId(String.valueOf(IdWork.id.nextId()));
		entity.setHireStatus(HireStatusEnum.HIRE_STATUS_1.getKey()); // 录用操作后默认为未审批状态
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		entity.setResumeStorehouseId(entity.getResumeStorehouseId());
		HrmsInterviewManagement interviewManagement = hrmsInterviewManagementService.selectByPrimaryKey(entity.getInterviewManagementId());
		if (interviewManagement != null) {
			entity.setEmployeeName(interviewManagement.getEmployeeName());
			entity.setTelephoneNumber(interviewManagement.getTelephoneNumber());
			entity.setEmail(interviewManagement.getEmail());
			// 更新面试状态
			interviewManagement.setInterviewStatus(entity.getInterviewStatus());
			hrmsInterviewManagementService.updateByEntity(interviewManagement);
		}
		return hrmsHireManagementMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 更新录用记录
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年5月22日 下午3:04:45
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsHireManagement entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsHireManagementMapper.updateByPrimaryKeySelective(entity);
	}

	/**
	 * @Title: getDataList
	 * @Description: 查询录用记录列表(分页)
	 * @param page
	 * @param entity
	 * @Return List<HrmsHireManagement>
	 * <AUTHOR>
	 * @date 2020年5月22日 下午3:08:47
	 */
	@Override
	public List<HrmsHireManagement> getDataList(Page page, HrmsHireManagement entity) {
		Example example = new Example(HrmsHireManagement.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(entity.getEmployeeName())) { // 姓名
			example.and().andLike("employeeName", entity.getEmployeeName() + "%");
		}

		if (StringUtils.isNotBlank(entity.getHireStatus())) { // 录用状态
			example.and().andEqualTo("hireStatus", entity.getHireStatus());
		}

		List<HrmsHireManagement> list = hrmsHireManagementMapper.selectByExampleAndRowBounds(example, page);
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsHireManagement hire : list) {
				hire.setHireStatusText(HireStatusEnum.getValByKey(hire.getHireStatus())); // 录用状态文本值
			}
		}
		return list;
	}
}

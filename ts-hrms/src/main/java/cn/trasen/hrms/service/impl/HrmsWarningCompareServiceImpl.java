package cn.trasen.hrms.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsWarningCompareMapper;
import cn.trasen.hrms.model.HrmsWarningCompare;
import cn.trasen.hrms.service.HrmsWarningCompareService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsWarningCompareServiceImpl
 * @Description TODO
 * @date 2021��10��11�� ����5:24:56
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsWarningCompareServiceImpl implements HrmsWarningCompareService {

	@Autowired
	private HrmsWarningCompareMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsWarningCompare record) {
		record.setId(IdGeneraterUtils.nextId());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsWarningCompare record) {
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsWarningCompare record = new HrmsWarningCompare();
		record.setId(id);
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsWarningCompare selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsWarningCompare> getDataSetList(Page page, HrmsWarningCompare record) {
		Example example = new Example(HrmsWarningCompare.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsWarningCompare> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

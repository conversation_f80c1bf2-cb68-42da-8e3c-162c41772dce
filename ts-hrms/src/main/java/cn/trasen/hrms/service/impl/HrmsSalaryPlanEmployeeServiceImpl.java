package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsSalaryPlanEmployeeMapper;
import cn.trasen.hrms.model.HrmsSalaryPlanEmployee;
import cn.trasen.hrms.service.HrmsSalaryPlanEmployeeService;

/**   
 * @Title: HrmsSalaryPlanEmployeeServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 薪酬方案人员 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月30日 上午10:47:57 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsSalaryPlanEmployeeServiceImpl implements HrmsSalaryPlanEmployeeService {

	@Autowired
	HrmsSalaryPlanEmployeeMapper hrmsSalaryPlanEmployeeMapper;

	/**
	 * @Title: insert
	 * @Description: 新增薪酬方案人员
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年4月29日 上午9:12:50
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsSalaryPlanEmployee entity) {
		Assert.notNull(entity.getSalaryPlanId(), "salaryPlanId must not be null.");
		Assert.notEmpty(entity.getEmployeeIds(), "employeeIds must not be empty.");

		List<HrmsSalaryPlanEmployee> list = Lists.newArrayList();
		for (String id : entity.getEmployeeIds()) {
			HrmsSalaryPlanEmployee employee = new HrmsSalaryPlanEmployee();
			employee.setSalaryPlanEmployeeId(String.valueOf(IdWork.id.nextId()));
			employee.setEmployeeId(id);
			employee.setSalaryPlanId(entity.getSalaryPlanId());
			employee.setIsDeleted(Contants.IS_DELETED_FALSE);
			employee.setCreateUser(UserInfoHolder.getCurrentUserCode());
			employee.setCreateUserName(UserInfoHolder.getCurrentUserName());
			employee.setCreateDate(new Date());
			list.add(employee);
		}
		// 删除当前方案的数据
		hrmsSalaryPlanEmployeeMapper.deleteBySalaryPlanId(entity.getSalaryPlanId());
		return hrmsSalaryPlanEmployeeMapper.batchInsert(list);
	}

	/**
	 * @Title: getSalaryPlanEmployee
	 * @Description: 获取薪酬方案人员Id集合(逗号分隔)
	 * @param entity
	 * @Return String
	 * <AUTHOR>
	 * @date 2020年4月28日 下午5:56:45
	 */
	@Override
	public List<String> getSalaryPlanEmployeeIds(String salaryPlanId) {
		Assert.notNull(salaryPlanId, "salaryPlanId must not be null.");
		return hrmsSalaryPlanEmployeeMapper.getSalaryPlanEmployeeIds(salaryPlanId, Contants.IS_DELETED_FALSE);
	}

	/**  
	 * @Title: getListByPlanId
	 * @Description: 获取薪酬方案人员列表
	 * @Param: salaryPlanId 薪酬方案ID
	 * <AUTHOR>
	 * @date 2020年4月7日 下午2:47:44 
	 */
	@Override
	public List<HrmsSalaryPlanEmployee> getListByPlanId(String salaryPlanId) {
		Assert.notNull(salaryPlanId, "salaryPlanId must not be null.");

		HrmsSalaryPlanEmployee entity = new HrmsSalaryPlanEmployee();
		entity.setSalaryPlanId(salaryPlanId);
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);

		return hrmsSalaryPlanEmployeeMapper.getList(entity);
	}

}

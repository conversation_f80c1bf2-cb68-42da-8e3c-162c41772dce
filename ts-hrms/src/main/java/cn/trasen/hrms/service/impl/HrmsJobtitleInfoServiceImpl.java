package cn.trasen.hrms.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.base.JobtitleBasicListReq;
import cn.trasen.homs.bean.base.JobtitleBasicListResp;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.homs.feign.base.HrmsJobtitleBasicFeignService;
import cn.trasen.hrms.bean.EmployeeJobtitleListResp;
import cn.trasen.hrms.bean.JobTitleImport;
import cn.trasen.hrms.contants.CommonContants;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsJobtitleInfoMapper;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsJobtitleAppoint;
import cn.trasen.hrms.model.HrmsJobtitleInfo;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.service.HrmsJobtitleAppointService;
import cn.trasen.hrms.service.HrmsJobtitleInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

/**   
 * @Title: HrmsJobtitleInfoServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 职称信息 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月23日 下午4:30:21 
 * @version V1.0   
 */
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsJobtitleInfoServiceImpl implements HrmsJobtitleInfoService {

	@Autowired
	HrmsJobtitleInfoMapper hrmsJobtitleInfoMapper;
	@Autowired
	HrmsDictInfoService hrmsDictInfoService;
	@Autowired
	HrmsEmployeeService hrmsEmployeeService;
	@Autowired
	HrmsJobtitleBasicFeignService hrmsJobtitleBasicService;
	@Autowired
	HrmsJobtitleAppointService hrmsJobtitleAppointService;
	


	@Autowired
	HrmsEmployeeFeignService hrmsEmployeeFeignService;


	@Autowired
	HrmsJobtitleBasicFeignService hrmsJobtitleBasicFeignService;
	/**
	 * @Title: validate
	 * @Description: 数据校验
	 * @param entity
	 * @Return PlatformResult<String>
	 * <AUTHOR>
	 * @date 2020年5月19日 上午9:45:49
	 */
	@Override
	public PlatformResult<String> validate(HrmsJobtitleInfo entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");

		if (!validateHighestLevel(entity).isSuccess()) { // 校验最高职称唯一
			return validateHighestLevel(entity);
		}
		return PlatformResult.success();
	}

	/**
	 * @Title: validateHighestLevel
	 * @Description: 校验最高职称唯一
	 * @param entity
	 * @Return PlatformResult<String>
	 * <AUTHOR>
	 * @date 2020年5月19日 上午10:20:10
	 */
	private PlatformResult<String> validateHighestLevel(HrmsJobtitleInfo entity) {
		Example example = new Example(HrmsJobtitleInfo.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("employeeId", entity.getEmployeeId());
		if (CommonContants.HIGHESTLEVEL_TRUE.equals(entity.getHighestLevel())) {
			example.and().andEqualTo("highestLevel", CommonContants.HIGHESTLEVEL_TRUE);
			if (StringUtils.isNotBlank(entity.getId())) { // 职称信息ID
				example.and().andNotEqualTo("jobtitleInfoId", entity.getId());
			}
			if (hrmsJobtitleInfoMapper.selectByExample(example).size() > 0) {
				return PlatformResult.failure("已设置最高职称");
			}
		}
		return PlatformResult.success();
	}

	/**
	 * @Title: insert
	 * @Description: 新增职称信息
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsJobtitleInfo entity) {
		//entity.setJobtitleInfoId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		int row = hrmsJobtitleInfoMapper.insert(entity);
		
		if(row > 0) {
			// 更新文件
//			if(StringUtils.isNotBlank(entity.getBusinessId())) {
//				fileClientService.updateTempBusinessId(entity.getId(), entity.getBusinessId());
//			}
		}
		
//		//查询用户信息，findDetailById
//		HrmsEmployee hrmsEmployee = hrmsEmployeeService.findDetailById(entity.getEmployeeId());		
//		//在审核表添加一条已审核的数据
//		HrmsJobtitleAppoint bean = new HrmsJobtitleAppoint();
//		BeanUtils.copyProperties(entity,bean);//左边是要取值的对象，右边是要赋值的对象
//		bean.setEmployeeName(hrmsEmployee.getEmployeeName());
//		bean.setEmployeeNo(hrmsEmployee.getEmployeeNo());
//		bean.setAssessTime(entity.getAcquisitionDate());
//		bean.setApprovalStatus("4");
//		hrmsJobtitleAppointService.insert(bean);
		return row;
	}

	/**
	 * @Title: update
	 * @Description: 更新职称信息
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsJobtitleInfo entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		int row = hrmsJobtitleInfoMapper.updateByPrimaryKeySelective(entity);
		
		if (row > 0) {
//			if (StringUtils.isNotBlank(entity.getBusinessId())) {
//				fileClientService.updateTempBusinessId(entity.getId(), entity.getBusinessId());
//			}
		}
	
		return row;
	}

	/**
	 * @Title: deleted
	 * @Description: 删除职称信息
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsJobtitleInfo jobtitleInfo = hrmsJobtitleInfoMapper.selectByPrimaryKey(id);
		if (jobtitleInfo != null) {
			jobtitleInfo.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		hrmsJobtitleInfoMapper.deleteByPrimaryKey(id);
		hrmsJobtitleInfoMapper.deleteByAppoint(jobtitleInfo);
		return 1;
//		return hrmsJobtitleInfoMapper.updateByPrimaryKeySelective(jobtitleInfo);
	}

	/**
	 * @Title: getDataList
	 * @Description: 查询职称信息列表(分页)
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsJobtitleInfo>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsJobtitleInfo> getDataList(Page page, HrmsJobtitleInfo entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");

		Example example = new Example(HrmsJobtitleInfo.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		example.and().andEqualTo("employeeId", entity.getEmployeeId());

		return hrmsJobtitleInfoMapper.selectByExampleAndRowBounds(example, page);
	}

	/**
	 * @Title: getList
	 * @Description: 查询职称信息列表(不分页)
	 * @param entity
	 * @Return List<HrmsJobtitleInfo>
	 * <AUTHOR>
	 * @date 2020年4月23日 下午4:33:20
	 */
	@Override
	public List<HrmsJobtitleInfo> getList(HrmsJobtitleInfo entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsJobtitleInfo> list = hrmsJobtitleInfoMapper.getList(entity);
		if (CollectionUtils.isNotEmpty(list)) {
			Map<String, String> acceptMethodMap = hrmsDictInfoService.convertDictMap(DictContants.CERTIFICATE_OBTAIN_TYPE); // 获取途径字典
			for (HrmsJobtitleInfo jobtitleInfo : list) {
				jobtitleInfo.setAcceptMethodText(acceptMethodMap.get(jobtitleInfo.getAcceptMethod())); // 获取途径
			}
		}
		return list;
	}

	//导入职称信息
	@Override
	@Transactional(readOnly = false)
	public PlatformResult<String> excelImportJobtitle(List<JobTitleImport> list) {
		Set<String> empCode = new HashSet<>();
		
		if(CollectionUtils.isEmpty(list)) {
			return PlatformResult.failure("模板数据格式无效，请检查后重新导入");
		}
		
		for (JobTitleImport jobTitleImport : list) {
			empCode.add(jobTitleImport.getEmployeeNo());
		}
		PlatformResult<List<EmployeeResp>> platformResult = hrmsEmployeeFeignService.getEmployeeDetailByCodes(new ArrayList(empCode));

		List<EmployeeResp> employeeRespList = platformResult.getObject();
		
		if(CollectionUtils.isEmpty(employeeRespList)) {
			return PlatformResult.failure("人员数据不存在，请检查后重新导入");
		}

		StringBuilder text =new StringBuilder();
		for (JobTitleImport jobTitleImport : list) {
			for (EmployeeResp employeeResp : employeeRespList) {
				if (jobTitleImport.getEmployeeNo().equals(employeeResp.getEmployeeNo())) {

					HrmsJobtitleInfo hrmsJobtitleInfo = new HrmsJobtitleInfo();
					hrmsJobtitleInfo.setId(String.valueOf(IdWork.id.nextId()));
					hrmsJobtitleInfo.setCreateDate(new Date());
					hrmsJobtitleInfo.setUpdateDate(new Date());
					hrmsJobtitleInfo.setCreateUser(UserInfoHolder.getCurrentUserId());
					hrmsJobtitleInfo.setIsDeleted(Contants.IS_DELETED_FALSE);
					hrmsJobtitleInfo.setEmployeeId(employeeResp.getEmployeeId());
					hrmsJobtitleInfo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());

					JobtitleBasicListReq jobtitleBasicListReq = new JobtitleBasicListReq();
					jobtitleBasicListReq.setJobtitleBasicName(jobTitleImport.getJobtitleCategory());
					jobtitleBasicListReq.setEqJobtitleBasicPid("0");
					JobtitleBasicListResp jobtitleBasicListResp = hrmsJobtitleBasicFeignService.get(jobtitleBasicListReq).getObject();

					if (jobtitleBasicListResp != null) {
						hrmsJobtitleInfo.setJobtitleCategory(jobtitleBasicListResp.getJobtitleBasicId());
						JobtitleBasicListReq jobtitleBasicListReq2 = new JobtitleBasicListReq();
						jobtitleBasicListReq2.setJobtitleBasicName(jobTitleImport.getJobtitleLevel());
						jobtitleBasicListReq2.setEqJobtitleBasicPid(jobtitleBasicListResp.getJobtitleBasicId());
						JobtitleBasicListResp jobtitleBasicListResp2 = hrmsJobtitleBasicFeignService.get(jobtitleBasicListReq2).getObject();

						if (jobtitleBasicListResp2 != null) {
							hrmsJobtitleInfo.setJobtitleLevel(jobtitleBasicListResp2.getJobtitleBasicId());
							JobtitleBasicListReq jobtitleBasicListReq3 = new JobtitleBasicListReq();
							jobtitleBasicListReq3.setJobtitleBasicName(jobTitleImport.getJobtitleName());
							jobtitleBasicListReq3.setEqJobtitleBasicPid(jobtitleBasicListResp2.getJobtitleBasicId());
							JobtitleBasicListResp jobtitleBasicListResp3 = hrmsJobtitleBasicFeignService.get(jobtitleBasicListReq3).getObject();
							if (Objects.isNull(jobtitleBasicListResp3) || Objects.isNull(jobtitleBasicListResp3.getJobtitleBasicId())){
								text.append("工号：" + jobTitleImport.getEmployeeNo()+","+ jobTitleImport.getJobtitleName());
							    continue;
							} else {
								hrmsJobtitleInfo.setJobtitleName(jobtitleBasicListResp3.getJobtitleBasicId());
							}
						}
					}

					if (StringUtils.isBlank(jobTitleImport.getAcquisitionDate())==false) {
						hrmsJobtitleInfo.setAssessmentDate(DateUtil.parse(jobTitleImport.getAcquisitionDate(), "yyyy-MM-dd"));
					}
					if (StringUtils.isBlank(jobTitleImport.getHighestLevel())==false) {
						hrmsJobtitleInfo.setHighestLevel(jobTitleImport.getHighestLevel().equals("是") ? "1" : "2");
					}


					if (hrmsJobtitleInfo.getJobtitleCategory() != null) {
						Example example = new Example(HrmsJobtitleInfo.class);
						example.and().andEqualTo("employeeId", hrmsJobtitleInfo.getEmployeeId());
						example.and().andEqualTo("jobtitleCategory", hrmsJobtitleInfo.getJobtitleCategory());
						example.and().andEqualTo("jobtitleLevel", hrmsJobtitleInfo.getJobtitleLevel());
						example.and().andEqualTo("jobtitleName", hrmsJobtitleInfo.getJobtitleName());
						example.setOrderByClause(" id desc LIMIT 1 ");
						HrmsJobtitleInfo hrmsJobtitleInfoNew = hrmsJobtitleInfoMapper.selectOneByExample(example);
						if (hrmsJobtitleInfoNew == null) {
							hrmsJobtitleInfo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
							hrmsJobtitleInfoMapper.insertSelective(hrmsJobtitleInfo);
						} else {
							hrmsJobtitleInfo.setId(hrmsJobtitleInfoNew.getId());
							hrmsJobtitleInfoMapper.updateByPrimaryKeySelective(hrmsJobtitleInfo);

						}
					}
					break;
				}
			}
		}
		if (StringUtils.isNotEmpty(text)){
			text.append("不存在，请调整！其他的导入成功");
			return PlatformResult.failure(text.toString());
		}
		return PlatformResult.success();
	}

	@Override
	public int updateHighestLevel(String employeeId) {
		return hrmsJobtitleInfoMapper.updateHighestLevel(employeeId);
	}

	@Override
	@Transactional(readOnly = false)
	public int directInsert(HrmsJobtitleInfo entity) {
		//entity.setJobtitleInfoId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		
		
		//修改提醒数据
		hrmsJobtitleInfoMapper.updateTreated(entity.getEmployeeId());
		
		hrmsJobtitleInfoMapper.insert(entity);
		
//		if(StringUtils.isNotBlank(entity.getBusinessId())) {
//			fileClientService.updateTempBusinessId(entity.getId(), entity.getBusinessId());
//		}
		
		//查询用户信息，findDetailById
		HrmsEmployee hrmsEmployee = hrmsEmployeeService.findDetailById(entity.getEmployeeId());		
		//在审核表添加一条已审核的数据
		HrmsJobtitleAppoint bean = new HrmsJobtitleAppoint();
		BeanUtils.copyProperties(entity,bean);//左边是要取值的对象，右边是要赋值的对象
		bean.setEmployeeName(hrmsEmployee.getEmployeeName());
		bean.setEmployeeNo(hrmsEmployee.getEmployeeNo());
		bean.setAssessTime(entity.getAcquisitionDate());
		bean.setApprovalStatus("4");
		hrmsJobtitleAppointService.insert(bean);

		return 1;
	}

	@Override
	public int deleteByApointBean(HrmsJobtitleAppoint hrmsJobtitleAppoint) {
		return hrmsJobtitleInfoMapper.deleteByApointBean(hrmsJobtitleAppoint);
	}


	@Override
	/**
	 * 获取员工职称列表
	 *
	 * @return java.util.List<cn.trasen.hrms.bean.GetEmployeeJobtitleListResp>
	 * <AUTHOR>
	 * @date 2021/11/17 17:09
	 */
	public	List<EmployeeJobtitleListResp> getEmployeejobtitleList() {
		//根据当前登录账号机构编码过滤查询数据
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		return   hrmsJobtitleInfoMapper.getEmployeejobtitleList(ssoOrgCode);
	}
}

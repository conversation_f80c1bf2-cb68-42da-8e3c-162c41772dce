package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsDoctorCertifiedMapper;
import cn.trasen.hrms.model.HrmsDoctorCertified;
import cn.trasen.hrms.service.HrmsDoctorCertifiedService;
import tk.mybatis.mapper.entity.Example;

@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsDoctorCertifiedServiceImpl implements HrmsDoctorCertifiedService {
	
	
	@Autowired
	HrmsDoctorCertifiedMapper hrmsDoctorCertifiedMapper;

	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsDoctorCertified entity) {
		entity.setDoctorCertifiedId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsDoctorCertifiedMapper.insert(entity);
	}

	@Override
	@Transactional(readOnly = false)
	public int update(HrmsDoctorCertified entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateDate(new Date());
		return hrmsDoctorCertifiedMapper.updateByPrimaryKeySelective(entity);
	}

	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsDoctorCertified bean = hrmsDoctorCertifiedMapper.selectByPrimaryKey(id);
		if (bean != null) {
			bean.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsDoctorCertifiedMapper.updateByPrimaryKeySelective(bean);
	}

	@Override
	public List<HrmsDoctorCertified> getDataList(Page page, HrmsDoctorCertified entity) {
		Example example = new Example(HrmsDoctorCertified.class);
		example.setOrderByClause("register_date DESC");
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if(!StringUtil.isEmpty(entity.getEmployeeId())) {
			criteria.andEqualTo("employeeId", entity.getEmployeeId());
		}
		if(!StringUtil.isEmpty(entity.getDoctorCertifiedId())) {
			criteria.andEqualTo("doctorCertifiedId", entity.getDoctorCertifiedId());
		}
		return hrmsDoctorCertifiedMapper.selectByExampleAndRowBounds(example, page);
	}

}

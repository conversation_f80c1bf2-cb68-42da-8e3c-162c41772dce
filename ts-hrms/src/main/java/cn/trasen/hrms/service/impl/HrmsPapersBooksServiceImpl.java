package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsPapersBooksMapper;
import cn.trasen.hrms.model.HrmsPapersBooks;
import cn.trasen.hrms.service.HrmsPapersBooksService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsPapersBooksServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 员工论文著作 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月12日 下午5:48:24 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsPapersBooksServiceImpl implements HrmsPapersBooksService {

	@Autowired
	HrmsPapersBooksMapper hrmsPapersBooksMapper;

	/**
	 * @Title: insert
	 * @Description: 新增论文著作
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsPapersBooks entity) {
		entity.setId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		return hrmsPapersBooksMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 更新论文著作
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsPapersBooks entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsPapersBooksMapper.updateByPrimaryKeySelective(entity);
	}

	/**
	 * @Title: deleted
	 * @Description: 删除论文著作
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsPapersBooks papersBooks = hrmsPapersBooksMapper.selectByPrimaryKey(id);
		if (papersBooks != null) {
			papersBooks.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsPapersBooksMapper.updateByPrimaryKeySelective(papersBooks);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取论文著作列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsPapersBooks>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsPapersBooks> getDataList(Page page, HrmsPapersBooks entity) {
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsPapersBooksMapper.getDataList(entity,page);
//		return hrmsPapersBooksMapper.selectByExampleAndRowBounds(example, page);
	}

	/**
	 * @Title: getList
	 * @Description: 查询论文著作列表(不分页)
	 * @param entity
	 * @Return List<HrmsPapersBooks>
	 * <AUTHOR>
	 * @date 2020年4月21日 下午3:44:27
	 */
	@Override
	public List<HrmsPapersBooks> getList(HrmsPapersBooks entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");

		Example example = new Example(HrmsPapersBooks.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		example.and().andEqualTo("employeeId", entity.getEmployeeId());
		
		if (StringUtils.isNotBlank(entity.getId())) {
			example.and().andEqualTo("papersBooksId", entity.getId());
		}
		return hrmsPapersBooksMapper.selectByExample(example);
	}

}

package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsScheduleAttendanceRuleMapper;
import cn.trasen.hrms.model.HrmsScheduleAttendanceRule;
import cn.trasen.hrms.service.HrmsScheduleAttendanceRuleService;
import tk.mybatis.mapper.entity.Example;

/**    
  * <P> @Description: 排班考勤规则Service层实现类</p>
  * <P> @Date: 2020年3月27日  上午10:04:48 </p>
  * <P> @Author: panqic </p>
  * <P> @Company: 湖南爱笑恩信息科技有限公司 </p>
  * <P> @version V1.0    </p> 
  */

@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsScheduleAttendanceRuleServiceImpl implements HrmsScheduleAttendanceRuleService {

	@Autowired
	HrmsScheduleAttendanceRuleMapper hrmsScheduleAttendanceRuleMapper;
	
	/**   
	 * <p>Title: getDataList</p>   
	 * <p>Description: 获取列表</p>   
	 * @param page
	 * @param hrmsScheduleAttendanceRule
	 * @return   
	 * @see cn.trasen.hrms.service.HrmsScheduleAttendanceRuleService#getDataList(cn.trasen.homs.core.feature.orm.mybatis.Page, cn.trasen.hrms.model.HrmsScheduleAttendanceRule)   
	 */ 
	@Override
	public List<HrmsScheduleAttendanceRule> getDataList(Page page, HrmsScheduleAttendanceRule hrmsScheduleAttendanceRule) {
		Example example = new Example(HrmsScheduleAttendanceRule.class);
		example.createCriteria().andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		return hrmsScheduleAttendanceRuleMapper.selectByExampleAndRowBounds(example, page); 
	}

	/**   
	 * <p>Title: SelectByPrimaryKey</p>   
	 * <p>Description: 根据主键查询</p>   
	 * @param id
	 * @return   
	 * @see cn.trasen.hrms.service.HrmsScheduleAttendanceRuleService#SelectByPrimaryKey(java.lang.String)   
	 */ 
	@Override
	public HrmsScheduleAttendanceRule SelectByPrimaryKey(String id) {
		return hrmsScheduleAttendanceRuleMapper.selectByPrimaryKey(id); 
	}

	/**   
	 * <p>Title: insert</p>   
	 * <p>Description: 新增</p>   
	 * @param hrmsScheduleAttendanceRule
	 * @return   
	 * @see cn.trasen.hrms.service.HrmsScheduleAttendanceRuleService#insert(cn.trasen.hrms.model.HrmsScheduleAttendanceRule)   
	 */ 
	@Override
	public int insert(HrmsScheduleAttendanceRule hrmsScheduleAttendanceRule) {
		hrmsScheduleAttendanceRule.setId(String.valueOf(IdWork.id.nextId()));
		hrmsScheduleAttendanceRule.setIsDeleted(Contants.IS_DELETED_FALSE);
		hrmsScheduleAttendanceRule.setCreateDate(new Date());
		hrmsScheduleAttendanceRule.setCreateUser(UserInfoHolder.getCurrentUserCode());
		hrmsScheduleAttendanceRule.setCreateUserName(UserInfoHolder.getCurrentUserName());
		hrmsScheduleAttendanceRule.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsScheduleAttendanceRuleMapper.insert(hrmsScheduleAttendanceRule); 
	}

	/**   
	 * <p>Title: update</p>   
	 * <p>Description: 修改</p>   
	 * @param hrmsScheduleAttendanceRule
	 * @return   
	 * @see cn.trasen.hrms.service.HrmsScheduleAttendanceRuleService#update(cn.trasen.hrms.model.HrmsScheduleAttendanceRule)   
	 */ 
	@Override
	public int update(HrmsScheduleAttendanceRule hrmsScheduleAttendanceRule) {
		hrmsScheduleAttendanceRule.setUpdateDate(new Date());
		hrmsScheduleAttendanceRule.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		hrmsScheduleAttendanceRule.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		if(StringUtils.isNotBlank(hrmsScheduleAttendanceRule.getIsDeleted()) && hrmsScheduleAttendanceRule.getIsDeleted().equals("0")) {
			hrmsScheduleAttendanceRule.setIsDeleted(Contants.IS_DELETED_FALSE);
		}
		return hrmsScheduleAttendanceRuleMapper.updateByPrimaryKeySelective(hrmsScheduleAttendanceRule); 
	}

	/**   
	 * <p>Title: deleted</p>   
	 * <p>Description: 删除</p>   
	 * @param id
	 * @return   
	 * @see cn.trasen.hrms.service.HrmsScheduleAttendanceRuleService#deleted(java.lang.String)   
	 */ 
	@Override
	public int deleted(String id) {
		HrmsScheduleAttendanceRule hrmsScheduleAttendanceRule = hrmsScheduleAttendanceRuleMapper.selectByPrimaryKey(id);
		hrmsScheduleAttendanceRule.setIsDeleted(Contants.IS_DELETED_TURE);
		return hrmsScheduleAttendanceRuleMapper.updateByPrimaryKeySelective(hrmsScheduleAttendanceRule); 
	}

}

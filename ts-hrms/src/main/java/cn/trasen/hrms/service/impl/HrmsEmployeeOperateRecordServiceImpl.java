package cn.trasen.hrms.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsEmployeeOperateRecordMapper;
import cn.trasen.hrms.model.HrmsEmployeeOperateRecord;
import cn.trasen.hrms.model.HrmsWorkExperience;
import cn.trasen.hrms.service.HrmsEmployeeOperateRecordService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsEmployeeOperateRecordServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 员工操作记录 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年6月29日 下午5:09:14 
 * @version V1.0   
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsEmployeeOperateRecordServiceImpl implements HrmsEmployeeOperateRecordService {

	@Autowired
	HrmsEmployeeOperateRecordMapper hrmsEmployeeOperateRecordMapper;

	/**
	 * @Title: batchInsert
	 * @Description: 批量插入
	 * @param list
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月29日 下午5:10:07
	 */
	@Override
	public int batchInsert(List<HrmsEmployeeOperateRecord> list) {
		return hrmsEmployeeOperateRecordMapper.batchInsert(list);
	}

	@Override
	public List<HrmsEmployeeOperateRecord> getDataList(Page page, HrmsEmployeeOperateRecord entity) {
		Example example = new Example(HrmsWorkExperience.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andLike("", "");
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		return hrmsEmployeeOperateRecordMapper.selectByExampleAndRowBounds(example, page);
	}
}

package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsBlacklistMapper;
import cn.trasen.hrms.enums.GenderTypeEnum;
import cn.trasen.hrms.model.HrmsBlacklist;
import cn.trasen.hrms.service.HrmsBlacklistService;
import tk.mybatis.mapper.entity.Example;

/**    
  * <P> @Description: 黑名单管理Service实现类</p>
  * <P> @Date: 2020年3月11日  上午10:56:14 </p>
  * <P> @Author: panqic </p>
  * <P> @Company: 湖南爱笑恩信息科技有限公司 </p>
  * <P> @version V1.0    </p> 
  */

@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsBlacklistServiceImpl implements HrmsBlacklistService {

	@Autowired
	HrmsBlacklistMapper hrmsBlacklistMapper;

	/**   
	 * <p>Title: getDataList</p>   
	 * <p>Description: 获取列表</p>   
	 * @param page
	 * @param entity
	 * @return   
	 * @see cn.trasen.hrms.service.HrmsBlacklistService#getDataList(cn.trasen.homs.core.feature.orm.mybatis.Page, cn.trasen.hrms.model.HrmsBlacklist)   
	 */
	@Override
	public List<HrmsBlacklist> getDataList(Page page, HrmsBlacklist entity) {
		Example example = new Example(HrmsBlacklist.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		//根据当前登录账号机构编码过滤查询数据
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(entity.getEmployeeName())) { // 姓名
			example.and().andLike("employeeName", "%" + entity.getEmployeeName() + "%");
		}

		List<HrmsBlacklist> list = hrmsBlacklistMapper.selectByExampleAndRowBounds(example, page);
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsBlacklist blacklist : list) {
				blacklist.setGenderText(GenderTypeEnum.getValByKey(blacklist.getGender())); // 性别文本值
			}
		}
		return list;
	}

	/**   
	 * <p>Title: SelectByPrimaryKey</p>   
	 * <p>Description: 通过主键查询</p>   
	 * @param id
	 * @return   
	 * @see cn.trasen.hrms.service.HrmsBlacklistService#SelectByPrimaryKey(java.lang.String)   
	 */
	@Override
	public HrmsBlacklist SelectByPrimaryKey(String id) {
		return hrmsBlacklistMapper.selectByPrimaryKey(id);
	}

	/**   
	 * <p>Title: insert</p>   
	 * <p>Description: 新增</p>   
	 * @param entity
	 * @return   
	 * @see cn.trasen.hrms.service.HrmsBlacklistService#insert(cn.trasen.hrms.model.HrmsBlacklist)   
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsBlacklist entity) {
		entity.setId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateDate(new Date());
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsBlacklistMapper.insert(entity);
	}

	/**   
	 * <p>Title: update</p>   
	 * <p>Description: 修改</p>   
	 * @param entity
	 * @return   
	 * @see cn.trasen.hrms.service.HrmsBlacklistService#update(cn.trasen.hrms.model.HrmsBlacklist)   
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsBlacklist entity) {
		entity.setUpdateDate(new Date());
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		if (StringUtils.isNotBlank(entity.getIsDeleted()) && entity.getIsDeleted().equals("0")) {
			entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		}
		return hrmsBlacklistMapper.updateByPrimaryKeySelective(entity);
	}

	/**   
	 * <p>Title: deleted</p>   
	 * <p>Description: 删除</p>   
	 * @param id
	 * @return   
	 * @see cn.trasen.hrms.service.HrmsBlacklistService#deleted(java.lang.String)   
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsBlacklist entity = hrmsBlacklistMapper.selectByPrimaryKey(id);
		entity.setIsDeleted(Contants.IS_DELETED_TURE);
		return hrmsBlacklistMapper.updateByPrimaryKeySelective(entity);
	}

}

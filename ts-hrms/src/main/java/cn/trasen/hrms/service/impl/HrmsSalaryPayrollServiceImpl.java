package cn.trasen.hrms.service.impl;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.trasen.hrms.utils.ExcelStyleUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.assertj.core.util.Lists;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.nfunk.jep.JEP;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.google.common.collect.Maps;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.UserDataPermissionVo;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.common.JdGridTableEntity;
import cn.trasen.hrms.contants.CommonContants;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsSalaryPayrollDetailMapper;
import cn.trasen.hrms.dao.HrmsSalaryPayrollMapper;
import cn.trasen.hrms.enums.SalaryCountTypeEnum;
import cn.trasen.hrms.enums.SalaryItemTypeEnum;
import cn.trasen.hrms.enums.SalaryPayrollSendStatusEnum;
import cn.trasen.hrms.model.HrmsSalaryAudit;
import cn.trasen.hrms.model.HrmsSalaryItem;
import cn.trasen.hrms.model.HrmsSalaryPayroll;
import cn.trasen.hrms.model.HrmsSalaryPayrollDetail;
import cn.trasen.hrms.model.HrmsSalaryPlan;
import cn.trasen.hrms.model.HrmsSalaryPlanDetail;
import cn.trasen.hrms.model.HrmsSalaryPlanEmployee;
import cn.trasen.hrms.service.HrmsAttendanceStatisticsService;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsSalaryAuditService;
import cn.trasen.hrms.service.HrmsSalaryChangeService;
import cn.trasen.hrms.service.HrmsSalaryItemService;
import cn.trasen.hrms.service.HrmsSalaryPayrollDetailService;
import cn.trasen.hrms.service.HrmsSalaryPayrollService;
import cn.trasen.hrms.service.HrmsSalaryPlanDetailService;
import cn.trasen.hrms.service.HrmsSalaryPlanEmployeeService;
import cn.trasen.hrms.service.HrmsSalaryPlanService;
import cn.trasen.hrms.service.HrmsSalaryProbationServer;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.MathUtils;
import cn.trasen.hrms.utils.UserPermissionManager;

/**   
 * @Title: HrmsSalaryPayrollServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 薪酬工资表 业务层接口实现类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月31日 上午11:23:02 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsSalaryPayrollServiceImpl implements HrmsSalaryPayrollService {

	@Autowired
	HrmsSalaryPayrollMapper hrmsSalaryPayrollMapper;
	@Autowired
	HrmsDictInfoService hrmsDictInfoService;
	@Autowired
	HrmsSalaryPlanService hrmsSalaryPlanService;
	@Autowired
	HrmsSalaryItemService hrmsSalaryItemService;
	@Autowired
	HrmsSalaryPlanDetailService hrmsSalaryPlanDetailService;
	@Autowired
	HrmsSalaryPlanEmployeeService hrmsSalaryPlanEmployeeService;
	@Autowired
	HrmsSalaryPayrollDetailService hrmsSalaryPayrollDetailService;
	@Autowired
	HrmsAttendanceStatisticsService hrmsAttendanceStatisticsService;
	@Autowired
	HrmsSalaryPayrollDetailMapper hrmsSalaryPayrollDetailMapper;
	@Autowired
	HrmsSalaryProbationServer hrmsSalaryProbationServer;
	@Autowired
	HrmsSalaryChangeService hrmsSalaryChangeService;
	@Autowired
	HrmsSalaryAuditService hrmsSalaryAuditService;

	/**
	 * @Title: generatePayroll
	 * @Description: 生成工资表记录
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年4月7日 下午4:57:22
	 */
	@Override
	@Transactional(readOnly = false)
	public void generatePayroll(HrmsSalaryPayroll hrmsSalaryPayroll) {
		//Assert.isNull(hrmsSalaryPayroll.getPayrollDateStr(), "生成工资月份为空");
		String payrollDate = hrmsSalaryPayroll.getPayrollDateStr();  //生成工资月份
		
		//判断是否审核完成
		HrmsSalaryAudit entity = new HrmsSalaryAudit();
		entity.setAuditDate(payrollDate);
		boolean salaryAuditByDateAndPlan = hrmsSalaryAuditService.getSalaryAuditByDateAndPlan(entity);
		if(salaryAuditByDateAndPlan) {
			throw new RuntimeException("已审核月份不能重新生成");
		}
		
		//生成工资之前先删除当月的数据
		//1.先删除工资记录表
		hrmsSalaryPayrollDetailService.deleteByPayrOllDate(payrollDate);
		//2.删除工资明细表
		hrmsSalaryPayrollMapper.deleteByPayrOllDate(payrollDate);
		//3 删除当月修改记录
		hrmsSalaryChangeService.deleteByDate(payrollDate);
		
		// 获取薪酬方案列表
		List<HrmsSalaryPlan> plans = hrmsSalaryPlanService.getSalaryPlanList(CommonContants.IS_ENABLE_TRUE, Contants.IS_DELETED_FALSE);
		if (CollectionUtils.isNotEmpty(plans)) {
			plans.stream().forEach(item -> {
				String salaryPlanId = item.getSalaryPlanId(); // 当前薪酬方案ID
				List<HrmsSalaryPayroll> payrollRecords = Lists.newArrayList(); // 工资记录集合
				List<HrmsSalaryPayrollDetail> payrollDetails = Lists.newArrayList(); // 工资明细集合

				// 查询当前方案的员工列表
				List<HrmsSalaryPlanEmployee> planEmployees = hrmsSalaryPlanEmployeeService.getListByPlanId(salaryPlanId);
				// 查询当前方案的项目列表
				List<HrmsSalaryPlanDetail> planDetails = hrmsSalaryPlanDetailService.getListBySalaryPlanId(salaryPlanId);

				if (CollectionUtils.isNotEmpty(planEmployees)) {
					// 生成月工资发放记录
					// DateTime dateTime = new
					// DateTime().plusMonths(1).dayOfMonth().withMinimumValue().withMillisOfDay(0);
					// 默认生成当前月份上一个月第一天的日期
//					String payrollDate = new DateTime().plusMonths(-1).toString("yyyy-MM"); // 发放月份
				
					// 查询所有薪酬项目列表
					HrmsSalaryItem salaryItem = new HrmsSalaryItem();
					salaryItem.setDataCategory(CommonContants.DATA_CATEGORY_SALARY);
					List<HrmsSalaryItem> list = hrmsSalaryItemService.getList(salaryItem);

					for (HrmsSalaryPlanEmployee employee : planEmployees) {  //循环员工
						String payrollId = String.valueOf(IdWork.id.nextId()); // 月工资记录ID
						HrmsSalaryPayroll payroll = new HrmsSalaryPayroll();
						payroll.setSalaryPayrollId(payrollId);
						payroll.setSalaryPlanId(salaryPlanId);
						payroll.setEmployeeId(employee.getEmployeeId());
						payroll.setPayrollDate(payrollDate);
						payroll.setIsDeleted(Contants.IS_DELETED_FALSE);
						payroll.setCreateUser(UserInfoHolder.getCurrentUserCode());
						payroll.setCreateUserName(UserInfoHolder.getCurrentUserName());
						payroll.setCreateDate(new Date());
						payrollRecords.add(payroll);

						// 生成月工资明细记录
						if (CollectionUtils.isNotEmpty(planDetails)) {
							// 根据员工ID查询考勤统计记录
						/*	HrmsAttendanceStatistics entity = new HrmsAttendanceStatistics();
							entity.setEmployeeId(employee.getEmployeeId());
							entity.setAttendanceDate(payrollDate);
							List<HrmsAttendanceStatistics> statistics = hrmsAttendanceStatisticsService.getEmployeeStatisticsList(entity);
							Map<String, HrmsAttendanceStatistics> attendanceItemMap = Maps.newHashMap();
							for (HrmsAttendanceStatistics sta : statistics) {
								attendanceItemMap.put(CommonContants.SALARYITEM_SYMBOL + sta.getAttendanceItemId(), sta);
							}*/

							// 获得薪酬项目的一个匹配规则(项目 - 金额)
							Map<String, BigDecimal> itemAmountMap = getSalaryItemAmountRuleMap(list, employee);

							for (HrmsSalaryPlanDetail d : planDetails) {
								HrmsSalaryPayrollDetail detail = new HrmsSalaryPayrollDetail();
								detail.setSalaryPayrollDetailId(String.valueOf(IdWork.id.nextId()));
								detail.setPayrollDate(payrollDate);
								detail.setSalaryPayrollId(payrollId);
								detail.setSalaryItemId(d.getItemId());
								detail.setIsDeleted(Contants.IS_DELETED_FALSE);
								detail.setCreateUser(UserInfoHolder.getCurrentUserCode());
								detail.setCreateUserName(UserInfoHolder.getCurrentUserName());
								detail.setCreateDate(new Date());
								// 项目类型为系统，根据code通过反射获取相应的金额，方便在扩展系统项目时不用一直增加if判断
								if (SalaryItemTypeEnum.ITEM_TYPE_1.getKey().equals(d.getSalaryItemType())) {
									//试用期员工获取试用期岗位工资
									if("99".equals(employee.getEmployeeStatus()) && "262882626441100001".equals( d.getItemId())) {  //员工状态为99的
										BigDecimal salaryItemAmount = hrmsSalaryProbationServer.getSalary(employee.getEmployeeId());
										detail.setSalaryAmount(salaryItemAmount);
									}else {
										try {
											String getMethodName = "get" + d.getSalaryItemCode().substring(0, 1).toUpperCase() + d.getSalaryItemCode().substring(1); // 获取方法名
											Method method = employee.getClass().getMethod(getMethodName); // 获取dto对象中的方法名
											Object obj = method.invoke(employee); // 调用方法获得值
											detail.setSalaryAmount(MathUtils.getBigDecimal(obj));
										} catch (Exception e) {
											e.printStackTrace();
										}
									}
									
								} else if (SalaryItemTypeEnum.ITEM_TYPE_2.getKey().equals(d.getSalaryItemType())) { // 项目类型为计算
									String countFormula = d.getCountFormula(); // 计算公式
									JEP jep = new JEP();
									// 匹配薪酬项目
									for (Map.Entry<String, BigDecimal> entry : itemAmountMap.entrySet()) {
										if (countFormula.indexOf(entry.getKey()) != -1) {
											jep.addVariable(entry.getKey(), entry.getValue().doubleValue());
										}
									}
									
									// 匹配考勤项目
								/*	for (Map.Entry<String, HrmsAttendanceStatistics> entry : attendanceItemMap.entrySet()) {
										if (countFormula.indexOf(entry.getKey()) != -1) {
											HrmsAttendanceStatistics statisticsEntity = entry.getValue();
											// 考勤类型为计算时，获取考勤天数和金额得到该项目的金额
											if (AttendanceItemTypeEnum.ITEM_TYPE_1.getKey().equals(statisticsEntity.getAttendanceItemType())) {
												BigDecimal attendanceItemAmount = statisticsEntity.getAttendanceItemAmount();
												Integer attendanceItemDays = statisticsEntity.getAttendanceDays();
												if (attendanceItemAmount != null && attendanceItemDays != null) {
													BigDecimal total = attendanceItemAmount.multiply(new BigDecimal(attendanceItemDays));
													jep.addVariable(entry.getKey(), total.doubleValue());
												}
											} else if (AttendanceItemTypeEnum.ITEM_TYPE_2.getKey().equals(statisticsEntity.getAttendanceItemType())) {
												// 考勤类型为天数时，直接读取考勤天数
												Integer attendanceItemDays = statisticsEntity.getAttendanceDays() == null ? 0 : statisticsEntity.getAttendanceDays();
												jep.addVariable(entry.getKey(), Double.valueOf(attendanceItemDays));
											}
										}
									}*/
									
									
									jep.parseExpression(countFormula);
									BigDecimal amount = new BigDecimal(0);
									if (SalaryCountTypeEnum.COUNT_TYPE_2.getKey().equals(d.getCountType())) { // 计算类型为扣减时
										BigDecimal countResult = MathUtils.getBigDecimal(jep.getValueAsObject());
										detail.setSalaryAmount(amount.subtract(countResult).setScale(2, BigDecimal.ROUND_HALF_UP));
									} else {
										detail.setSalaryAmount(MathUtils.getBigDecimal(jep.getValueAsObject()).setScale(2, BigDecimal.ROUND_HALF_UP));
									}
								} else if (SalaryItemTypeEnum.ITEM_TYPE_3.getKey().equals(d.getSalaryItemType())) { // 项目类型为人工
									detail.setSalaryAmount(new BigDecimal(0));
								} else if (SalaryItemTypeEnum.ITEM_TYPE_4.getKey().equals(d.getSalaryItemType())) { // 项目类型为固定金额
									BigDecimal amount = new BigDecimal(0);
									if (SalaryCountTypeEnum.COUNT_TYPE_2.getKey().equals(d.getCountType())) { // 计算类型为扣减时
										detail.setSalaryAmount(amount.subtract(d.getSalaryItemAmount()).setScale(2, BigDecimal.ROUND_HALF_UP));
									} else {
										detail.setSalaryAmount(d.getSalaryItemAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
									}

								}else if(SalaryItemTypeEnum.ITEM_TYPE_5.getKey().equals(d.getSalaryItemType())) {  // 手工录入引用上月
									//查询上月数据
									HrmsSalaryPayrollDetail record = new HrmsSalaryPayrollDetail();
									record.setSalaryPlanId(salaryPlanId);
									record.setEmployeeId(employee.getEmployeeId());
									record.setSalaryItemId(d.getSalaryItemCode());
									record.setPayrollDate(DateUtils.getLastMonth(payrollDate,0,-1,0));
									HrmsSalaryPayrollDetail selectOne = hrmsSalaryPayrollDetailMapper.lastMonthSalary(record);
									if(selectOne != null) {
										detail.setSalaryAmount(selectOne.getSalaryAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
									}
								}
								payrollDetails.add(detail);
							}
						}
					}
					hrmsSalaryPayrollMapper.batchInsert(payrollRecords); // 批量新增工资表
					batchProcess(payrollDetails); // 批量新增工资明细
				}
			});
		}
	}

	/**
	 * @Title: getSalaryItemAmountRuleMap
	 * @Description: 根据薪酬项目获取项目金额，组成一个Key-value的map集合
	 * @param list
	 * @param employee
	 * @Return Map<String,BigDecimal>
	 * <AUTHOR>
	 * @date 2020年5月15日 上午11:32:05
	 */
	private Map<String, BigDecimal> getSalaryItemAmountRuleMap(List<HrmsSalaryItem> list, HrmsSalaryPlanEmployee employee) {
		Map<String, BigDecimal> map = Maps.newHashMap(); // 项目和金额对应的map
		try {
			for (HrmsSalaryItem o : list) {
				String key = CommonContants.SALARYITEM_SYMBOL + o.getSalaryItemId();

				// 如果系统内置项目，根据code通过反射获取相应的金额，方便在扩展内置项目时不用一直增加if判断
				if (SalaryItemTypeEnum.ITEM_TYPE_1.getKey().equals(o.getSalaryItemType())) {
					String getMethodName = "get" + o.getSalaryItemCode().substring(0, 1).toUpperCase() + o.getSalaryItemCode().substring(1); // 获取方法名
					Method method = employee.getClass().getMethod(getMethodName); // 获取dto对象中的方法名
					Object obj = method.invoke(employee); // 调用方法获得值
					map.put(key, MathUtils.getBigDecimal(obj) == null ? new BigDecimal(0) : MathUtils.getBigDecimal(obj));
				} else if (SalaryItemTypeEnum.ITEM_TYPE_4.getKey().equals(o.getSalaryItemType())) { // 项目类型为固定金额
					BigDecimal amount = new BigDecimal(0);
					if (SalaryCountTypeEnum.COUNT_TYPE_2.getKey().equals(o.getCountType())) { // 计算类型为扣减时
						map.put(key, amount.subtract(o.getSalaryItemAmount()));
					} else {
						map.put(key, o.getSalaryItemAmount());
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return map;
	}

	/**
	 * 防止数据太大时内存溢出，每次保存2000条记录
	 * @Title: batchProcess
	 * @Description: 分页批量插入数据
	 * @param list
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年4月9日 下午4:43:13
	 */
	private void batchProcess(List<HrmsSalaryPayrollDetail> list) {
		int totalSize = list.size(); // 总记录数
		int pageSize = 1000; // 每次新增的记录数
		int totalPage = totalSize / pageSize; // 总页数
		if (totalSize % pageSize != 0) {
			totalPage += 1;
			if (totalSize < pageSize) {
				pageSize = totalSize;
			}
		}
		for (int i = 1; i < totalPage + 1; i++) {
			int start = (i - 1) * pageSize;
			int end = i * pageSize > totalSize ? (totalSize) : i * pageSize;
			hrmsSalaryPayrollDetailService.batchInsert(list.subList(start, end));
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteTableData() {
		hrmsSalaryPayrollMapper.deleteTableData();
	}

	/**
	 * @Title: getPageList
	 * @Description: 获取工资列表（分页）
	 * @param page
	 * @param entity
	 * @Return List<HrmsSalaryPayroll>
	 * <AUTHOR>
	 * @date 2020年4月9日 上午9:52:25
	 */
	@Override
	public List<HrmsSalaryPayroll> getPageList(Page page, HrmsSalaryPayroll entity) {
		UserDataPermissionVo userDataPermissionVo = UserPermissionManager.getInstance().getUserDataPermission();
		if (CollectionUtils.isNotEmpty(userDataPermissionVo.getOrgCodeList())) {
			entity.setOrgIdList(userDataPermissionVo.getOrgCodeList());
		}
		if (StringUtils.isNotBlank(userDataPermissionVo.getUserCode())) {
			entity.setUserCode(userDataPermissionVo.getUserCode());
		}
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		List<HrmsSalaryPayroll> list = hrmsSalaryPayrollMapper.getPageList(page, entity);
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsSalaryPayroll o : list) {
				o.setSendStatusText(SalaryPayrollSendStatusEnum.getValByKey(o.getSendStatus())); // 发送状态
			}
		}
		return list;
	}

	/**
	 * 根据薪酬方案ID获取薪酬人员，并关联查询薪资发放记录
	 * @Title: getSalaryRecord
	 * @Description: 获取员工薪资记录
	 * @param page
	 * @param entity
	 * @Return List<HrmsSalaryPayroll>
	 * <AUTHOR>
	 * @date 2020年5月8日 上午9:41:53
	 */
	@Override
	public List<Map<String, Object>> getSalaryRecord(Page page, HrmsSalaryPayroll entity) {
		List<Map<String, Object>> result = Lists.newArrayList();
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		// 获取薪资发放员工列表
		List<HrmsSalaryPayroll> list = hrmsSalaryPayrollMapper.getSalaryPayrollEmployee(page, entity);
		if (CollectionUtils.isNotEmpty(list)) {
			// 员工薪资明细列表
			List<HrmsSalaryPayrollDetail> payrollDetails = hrmsSalaryPayrollDetailService.getSalaryPayrollDetailList(entity);
			if (CollectionUtils.isNotEmpty(payrollDetails)) {
				// 将员工薪资明细根据员工ID转换成Map
				Map<String, List<HrmsSalaryPayrollDetail>> payrollDetailMap = payrollDetails.stream().collect(Collectors.groupingBy(HrmsSalaryPayrollDetail::getEmployeeId));
				Map<String, String> employeeStatusMap = hrmsDictInfoService.convertDictMap(DictContants.EMPLOYEE_STATUS);  //员工状态
				Map<String, String> establishmentTypeMap = hrmsDictInfoService.convertDictMap(DictContants.ESTABLISHMENT_TYPE);  //编制类型
				list.stream().forEach(payroll -> {
					Map<String, Object> map = Maps.newHashMap();
					map.put("salaryPayrollId", payroll.getSalaryPayrollId());
					map.put("employeeNo", payroll.getEmployeeNo());
					map.put("employeeId", payroll.getEmployeeId());
					map.put("employeeName", payroll.getEmployeeName());
					map.put("orgName", payroll.getOrgName());
					map.put("employeeIn", payroll.getOrgName());
					map.put("isDetails", payroll.getIsDetails());
					map.put("personalIdentityText", payroll.getPersonalIdentityText());
					if(!StringUtils.isEmpty(payroll.getEstablishmentType())) {
						map.put("establishmentType",  establishmentTypeMap.get(payroll.getEstablishmentType()));
					}else {
						map.put("establishmentType",  "");
					}
					if(!StringUtils.isEmpty(payroll.getEmployeeStatus())) {
						map.put("employeeStatus", employeeStatusMap.get(payroll.getEmployeeStatus())); 
					}else {
						map.put("employeeStatus", ""); 
					}
					
					//262882626441100008   应发工资  salaryShould
					BigDecimal salaryShould = new BigDecimal(0);
					//262882626441100009  扣除小计 salaryDeduction
					BigDecimal salaryDeduction = new BigDecimal(0);
					//262882626441100010  实发工资  salaryPractical
					BigDecimal salaryPractical = new BigDecimal(0);
					// 取出工资明细数据
					List<HrmsSalaryPayrollDetail> details = payrollDetailMap.get(payroll.getEmployeeId());
					if (CollectionUtils.isNotEmpty(details)) {
						for (HrmsSalaryPayrollDetail d : details) {
							if (StringUtils.isNotBlank(d.getSalaryItemId())) {
								if (null != d.getSalaryAmount()) {
									if ("1".equals(d.getCountType())) { // 应发合计累加
										salaryShould = salaryShould.add(d.getSalaryAmount());
									} else if ("2".equals(d.getCountType())) { // 扣减累加
										if("4".equals(d.getSalaryItemType())) {  //固定扣减 转为正数累加
											salaryDeduction = salaryDeduction.add(d.getSalaryAmount().negate());
										}else {
											salaryDeduction = salaryDeduction.add(d.getSalaryAmount());
										}
										
									}
								}
								map.put(d.getSalaryItemId(), d.getSalaryAmount());
							}
						}
					}
					salaryPractical = salaryShould.subtract(salaryDeduction);  //实发 = 应发 -扣减
					map.put("262882626441100008", salaryShould);
					map.put("262882626441100009", salaryDeduction);
					map.put("262882626441100010", salaryPractical);
					result.add(map);
				});

			}
		}
		return result;
	}

	@Override
	public void export(Page page, HttpServletRequest request, HttpServletResponse response, HrmsSalaryPayroll entity) {
		// TODO Auto-generated method stub
		Assert.hasText(entity.getSalaryPlanId(), "salaryPlanId must be not null.");
		String filename = "薪酬记录表.xls";
		List<JdGridTableEntity> jdGridTableEntities = hrmsSalaryPlanDetailService.getTableHeadCols(entity.getSalaryPlanId());
		// 获取标头
		if (CollectionUtils.isNotEmpty(jdGridTableEntities)) {
			List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();

			//导出数据去掉id列
			for(int i=0;i<jdGridTableEntities.size();i++) {
				if(jdGridTableEntities.get(i) != null && "ID".equals(jdGridTableEntities.get(i).getLabel())) {
					jdGridTableEntities.remove(i);
				}
				if(jdGridTableEntities.get(i) != null && "员工id".equals(jdGridTableEntities.get(i).getLabel())) {
					jdGridTableEntities.remove(i);
				}
				
				if(jdGridTableEntities.get(i) != null && "明细".equals(jdGridTableEntities.get(i).getLabel())) {
					jdGridTableEntities.remove(i);
				}
			}
			// 设置表头
			setColList(colList, jdGridTableEntities);

			List<Map<String, Object>> salaryRecordMaps = getSalaryRecord(page, entity);
			try {
				ExportParams exportParams = new ExportParams();
				exportParams.setStyle(ExcelStyleUtil.class);
				Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, salaryRecordMaps);

				response.setContentType("application/vnd.ms-excel");
				response.setCharacterEncoding("UTF-8");
				response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(filename, "UTF-8"));

				OutputStream fos = response.getOutputStream();
				workbook.write(fos);
				fos.close();
			} catch (FileNotFoundException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}

	private void setColList(List<ExcelExportEntity> colList, List<JdGridTableEntity> jdGridTableEntities) {
		// TODO Auto-generated method stub
		if (CollectionUtils.isNotEmpty(jdGridTableEntities)) {
			for (JdGridTableEntity jdGridTableEntity : jdGridTableEntities) {
				if (StringUtils.isNotBlank(jdGridTableEntity.getName())) {
					colList.add(new ExcelExportEntity(jdGridTableEntity.getLabel(), jdGridTableEntity.getName(), 15));
				}
			}
		}
	}
	
	@Override
	@Transactional(readOnly = false)
	public void deleteByPayrollDate(HrmsSalaryPayroll entity) {
		//Assert.isNull(entity.getPayrollDateStr(), "删除工资月份为空");
		String payrollDate = entity.getPayrollDateStr();  //生成工资月份
		
		//已审核的月份不能删除
		HrmsSalaryAudit bean = new HrmsSalaryAudit();
		bean.setAuditDate(payrollDate);
		boolean salaryAuditByDateAndPlan = hrmsSalaryAuditService.getSalaryAuditByDateAndPlan(bean);
		if(salaryAuditByDateAndPlan) {
			throw new RuntimeException("已审核月份不能删除");
		}
		
		//生成工资之前先删除当月的数据
		//1.先删除工资记录表
		hrmsSalaryPayrollDetailService.deleteByPayrOllDate(payrollDate);
		//2.删除工资明细表
		hrmsSalaryPayrollMapper.deleteByPayrOllDate(payrollDate);
		//删除修改记录
		hrmsSalaryChangeService.deleteByDate(payrollDate);
	}

}

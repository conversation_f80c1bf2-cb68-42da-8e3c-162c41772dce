package cn.trasen.hrms.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.model.CustEmpInfo;

/**
 * @ClassName CustEmpInfoService
 * @Description TODO
 * @date 2024��10��15�� ����3:04:50
 * <AUTHOR>
 * @version 1.0
 */
public interface CustEmpInfoService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��10��15�� ����3:04:50
	 * <AUTHOR>
	 */
	Integer save(CustEmpInfo record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��10��15�� ����3:04:50
	 * <AUTHOR>
	 */
	Integer update(CustEmpInfo record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��10��15�� ����3:04:50
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CustEmpInfo
	 * @date 2024��10��15�� ����3:04:50
	 * <AUTHOR>
	 */
	CustEmpInfo selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CustEmpInfo>
	 * @date 2024��10��15�� ����3:04:50
	 * <AUTHOR>
	 */
	DataSet<CustEmpInfo> getDataSetList(Page page, CustEmpInfo record);
}

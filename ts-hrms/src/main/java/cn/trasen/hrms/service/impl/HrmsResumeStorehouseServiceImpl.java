package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.contants.CommonContants;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsHireManagementMapper;
import cn.trasen.hrms.dao.HrmsInterviewManagementMapper;
import cn.trasen.hrms.dao.HrmsResumeStorehouseMapper;
import cn.trasen.hrms.enums.GenderTypeEnum;
import cn.trasen.hrms.model.HrmsHireManagement;
import cn.trasen.hrms.model.HrmsInterviewManagement;
import cn.trasen.hrms.model.HrmsResumeStorehouse;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsResumeStorehouseService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsResumeStorehouseServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 简历库 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月22日 上午9:33:13 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsResumeStorehouseServiceImpl implements HrmsResumeStorehouseService {

	@Autowired
	HrmsResumeStorehouseMapper hrmsResumeStorehouseMapper;
	
	@Autowired
	HrmsDictInfoService hrmsDictInfoService;
	
//	@Autowired
//	private FileFeignService fileClientService;
//
	@Autowired
	private HrmsInterviewManagementMapper hrmsInterviewManagementService;
	
	@Autowired	
	private HrmsHireManagementMapper hrmsHireManagementService;

	/**
	 * @Title: insert
	 * @Description: 新增简历库
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsResumeStorehouse entity) {
		entity.setResumeStorehouseId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setResumeStatus(CommonContants.RESUME_STATUS_1); // 新增简历时默认为未邀约状态
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		int row = hrmsResumeStorehouseMapper.insert(entity);
		if(row > 0) {
			// 更新文件
//			System.out.println("BusinessId:"+entity.getBusinessId());
//			if(StringUtils.isNotBlank(entity.getBusinessId())) {
//				fileClientService.updateTempBusinessId(entity.getResumeStorehouseId(), entity.getBusinessId());
//			}
		}
		return row;
	}

	/**
	 * @Title: update
	 * @Description: 更新简历库
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsResumeStorehouse entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		
		//修改面试管理的基本信息
		Example example = new Example(HrmsInterviewManagement.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("resumeStorehouseId", entity.getResumeStorehouseId());
		HrmsInterviewManagement hrmsInterviewManagement = new HrmsInterviewManagement();
		hrmsInterviewManagement.setTelephoneNumber(entity.getTelephoneNumber());
		hrmsInterviewManagement.setEmail(entity.getEmail());
		hrmsInterviewManagementService.updateByExampleSelective(hrmsInterviewManagement, example);
		
		//修改录用管理的信息
		Example example2 = new Example(HrmsHireManagement.class);
		example2.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example2.and().andEqualTo("resumeStorehouseId", entity.getResumeStorehouseId());
		HrmsHireManagement hrmsHireManagement = new HrmsHireManagement();
		hrmsHireManagement.setTelephoneNumber(entity.getTelephoneNumber());
		hrmsHireManagement.setEmail(entity.getEmail());
		
		hrmsHireManagementService.updateByExampleSelective(hrmsHireManagement,example2);
		
		
		return hrmsResumeStorehouseMapper.updateByPrimaryKeySelective(entity);
	}

	/**
	 * @Title: updateById
	 * @Description: 根据ID更新简历库(无事务,供其他Service调用)
	 * @param id
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年5月25日 下午2:10:40
	 */
	@Override
	public int updateById(String id) {
		HrmsResumeStorehouse storehouse = hrmsResumeStorehouseMapper.selectByPrimaryKey(id);
		if (storehouse != null) {
			storehouse.setResumeStatus(CommonContants.RESUME_STATUS_2);
			storehouse.setUpdateUser(UserInfoHolder.getCurrentUserCode());
			storehouse.setUpdateUserName(UserInfoHolder.getCurrentUserName());
			storehouse.setUpdateDate(new Date());
		}
		return hrmsResumeStorehouseMapper.updateByPrimaryKeySelective(storehouse);
	}

	/**
	 * @Title: deleted
	 * @Description: 删除简历库
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsResumeStorehouse storehouse = hrmsResumeStorehouseMapper.selectByPrimaryKey(id);
		if (storehouse != null) {
			storehouse.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsResumeStorehouseMapper.updateByPrimaryKeySelective(storehouse);
	}

	/**
	 * @Title: getDataList
	 * @Description: 查询简历库列表(分页)
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsResumeStorehouse>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsResumeStorehouse> getDataList(Page page, HrmsResumeStorehouse entity) {
		Example example = new Example(HrmsResumeStorehouse.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(entity.getEmployeeName())) { // 姓名
			example.and().andLike("employeeName", entity.getEmployeeName() + "%");
		}

		if (StringUtils.isNotBlank(entity.getApplyPost())) { // 应聘岗位
			example.and().andLike("applyPost", "%" + entity.getApplyPost() + "%");
		}

		if (StringUtils.isNotBlank(entity.getResumeStatus())) { // 简历状态
			example.and().andEqualTo("resumeStatus", entity.getResumeStatus());
		}

		List<HrmsResumeStorehouse> list = hrmsResumeStorehouseMapper.selectByExampleAndRowBounds(example, page);
		if (CollectionUtils.isNotEmpty(list)) {
			Map<String, String> marriageStatusMap = hrmsDictInfoService.convertDictMap(DictContants.MARRIAGE_STATUS); // 婚姻状况数据字典
			Map<String, String> educationTypeMap = hrmsDictInfoService.convertDictMap(DictContants.EDUCATION_TYPE); // 学历类型数据字典
			Map<String, String> sourceChannelMap = hrmsDictInfoService.convertDictMap(DictContants.RESUME_SOURCE_CHANNEL); // 来源渠道数据字典
			for (HrmsResumeStorehouse storehouse : list) {
				storehouse.setGenderText(GenderTypeEnum.getValByKey(storehouse.getGender())); // 性别文本值
				storehouse.setMarriageStatusText(marriageStatusMap.get(storehouse.getMarriageStatus())); // 婚姻状况文本值
				storehouse.setEducationTypeText(educationTypeMap.get(storehouse.getEducationType())); // 学历类型文本值
				storehouse.setSourceChannelText(sourceChannelMap.get(storehouse.getSourceChannel())); // 来源渠道文本值
				if (CommonContants.RESUME_STATUS_1.equals(storehouse.getResumeStatus())) { // 邀约状态文本值
					storehouse.setResumeStatusText("未邀约");
				} else {
					storehouse.setResumeStatusText("已邀约");
				}
			}
		}
		return list;
	}

	/**
	 * @Title: selectByPrimaryKey
	 * @Description: 根据主键ID查询
	 * @param id
	 * @Return HrmsResumeStorehouse
	 * <AUTHOR>
	 * @date 2020年5月22日 上午11:46:28
	 */
	@Override
	public HrmsResumeStorehouse selectByPrimaryKey(String id) {
		return hrmsResumeStorehouseMapper.selectByPrimaryKey(id);
	}

}

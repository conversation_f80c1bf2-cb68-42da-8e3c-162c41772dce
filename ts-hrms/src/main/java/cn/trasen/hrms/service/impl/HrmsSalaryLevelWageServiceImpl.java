package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.CommTableSnapshotSaveReq;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.feign.base.CommTableSnapshotFeignService;
import cn.trasen.hrms.model.HrmsSalaryLevel;
import cn.trasen.hrms.salary.enums.EnableEnum;
import cn.trasen.hrms.salary.model.HrmsSalaryPolicyStandard;
import cn.trasen.hrms.salary.service.HrmsSalaryPolicyStandardService;
import cn.trasen.hrms.service.HrmsSalaryLevelService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsSalaryLevelWageMapper;
import cn.trasen.hrms.model.HrmsSalaryLevelWage;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsSalaryLevelWageService;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**   
 * @Title: HrmsSalaryLevelWageServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 薪级工资 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月1日 下午10:03:54 
 * @version V1.0   
 */
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsSalaryLevelWageServiceImpl implements HrmsSalaryLevelWageService {

	@Resource
	HrmsSalaryLevelWageMapper hrmsSalaryLevelWageMapper;
	@Autowired
	HrmsDictInfoService hrmsDictInfoService;

	@Autowired
	private CommTableSnapshotFeignService commTableSnapshotFeignService;

	@Autowired
	private HrmsSalaryLevelService hrmsSalaryLevelService;

	@Autowired
	private HrmsSalaryPolicyStandardService hrmsSalaryPolicyStandardService;

	/**
	 * @Title: insert
	 * @Description: 新增薪级工资
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsSalaryLevelWage entity) {
		Example example = new Example(HrmsSalaryLevelWage.class);
		Example.Criteria criteria = example.createCriteria();
		criteria .andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andEqualTo("salaryLevelId", entity.getSalaryLevelId());
		if(StringUtils.isNotBlank(entity.getPolicyStandardId())) {
			example.and().andEqualTo("policyStandardId", entity.getPolicyStandardId());
		}
		List<HrmsSalaryLevelWage> list = hrmsSalaryLevelWageMapper.selectByExample(example);
		if(list != null && list.size() > 0) {
			throw new RuntimeException("已设置相同薪级等级工资配置");
		}
		entity.setSalaryLevelWageId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsSalaryLevelWageMapper.insert(entity);
		// 增加处理快照
		this.fillDescField(entity);
		CommTableSnapshotSaveReq commTableSnapshotSaveReq = new CommTableSnapshotSaveReq();
		commTableSnapshotSaveReq.setTableName("hrms_salary_level_wage");
		commTableSnapshotSaveReq.setRowPkValue(entity.getSalaryLevelWageId());
		commTableSnapshotSaveReq.setNow(entity);
		commTableSnapshotFeignService.save(commTableSnapshotSaveReq);
		return 1;
	}
	
	/**
	 * @Title: batchInsert
	 * @Description: 批量插入
	 * @param list
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月11日 上午10:45:17
	 */
	@Override
	public int batchInsert(List<HrmsSalaryLevelWage> list) {
		return hrmsSalaryLevelWageMapper.batchInsert(list);
	}

	/**
	 * @Title: update
	 * @Description: 更新薪级工资
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsSalaryLevelWage entity) {
		HrmsSalaryLevelWage oldWage = hrmsSalaryLevelWageMapper.selectByPrimaryKey(entity.getSalaryLevelWageId());
		oldWage.setSalaryLevelCategory(entity.getSalaryLevelCategory());
		oldWage.setSalaryLevelCategoryName(entity.getSalaryLevelCategoryName());
		oldWage.setSalaryLevelName(entity.getSalaryLevelName());
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		hrmsSalaryLevelWageMapper.updateByPrimaryKeySelective(entity);
		// 增加处理快照
		this.fillDescField(oldWage);
		this.fillDescField(entity);
		CommTableSnapshotSaveReq commTableSnapshotSaveReq = new CommTableSnapshotSaveReq();
		commTableSnapshotSaveReq.setTableName("hrms_salary_level_wage");
		commTableSnapshotSaveReq.setRowPkValue(entity.getSalaryLevelWageId());
		commTableSnapshotSaveReq.setOld(oldWage);
		commTableSnapshotSaveReq.setNow(entity);
		commTableSnapshotFeignService.save(commTableSnapshotSaveReq);
		return 1;
	}

	/**
	 * @Title: deleted
	 * @Description: 删除薪级工资
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsSalaryLevelWage salaryLevelWage = hrmsSalaryLevelWageMapper.selectByPrimaryKey(id);
		if (salaryLevelWage != null) {
			salaryLevelWage.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsSalaryLevelWageMapper.updateByPrimaryKeySelective(salaryLevelWage);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取薪级工资列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsSalaryLevelWage>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsSalaryLevelWage> getDataList(Page page, HrmsSalaryLevelWage entity) {
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsSalaryLevelWage> list = hrmsSalaryLevelWageMapper.getList(page, entity);
		//获取所有标准数据
		Example example = new Example(HrmsSalaryLevelWage.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsSalaryLevelWage> policyStandardSalaryLevelWages = hrmsSalaryLevelWageMapper.selectByExample(example);
		if (CollectionUtils.isNotEmpty(list)) {
			//填充政策标准数据
			if(CollUtil.isNotEmpty(policyStandardSalaryLevelWages)){
				Map<String, List<HrmsSalaryLevelWage>> collect = policyStandardSalaryLevelWages.stream().filter(vo-> StringUtils.isNotBlank(vo.getSalaryLevelId()))
						.collect(Collectors.groupingBy(HrmsSalaryLevelWage::getSalaryLevelId));
				list.forEach(item ->{
					item.setPolicyStandardSalaryLevelWages(collect.get(item.getSalaryLevelId()));
				});
			}
			Map<String, String> salaryLevelCategoryMap = hrmsDictInfoService.convertDictMap(DictContants.SALARY_LEVEL_CATEGORY);
			if (salaryLevelCategoryMap != null && salaryLevelCategoryMap.size() > 0) {
				for (HrmsSalaryLevelWage wage : list) {
					wage.setSalaryLevelCategoryName(salaryLevelCategoryMap.get(wage.getSalaryLevelCategory()));
					wage.setIsEnableLabel(EnableEnum.getValByKey(wage.getIsEnable()));
				}
			}
		}
		return list;
	}

	/**
	 * 查询岗位工资设置列表
	 * @param entity
	 * @return
	 */
	@Override
	public List<HrmsSalaryLevelWage> getSalaryLevelWagesSettingList(HrmsSalaryLevelWage entity){
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		return hrmsSalaryLevelWageMapper.getSalaryLevelWagesSettingList(entity);
	}

	/**
	 * 批量保存岗位工资数据
	 * @param entity
	 * @return
	 */
	@Transactional(readOnly = false)
	@Override
	public void salaryLevelWagesSettingBatchSave(HrmsSalaryLevelWage entity){
		Assert.hasText(entity.getSalaryLevelCategory(),"薪级类别不能为空");
		Assert.hasText(entity.getPolicyStandardId(),"政策标准不能为空");
		if(CollUtil.isEmpty(entity.getPolicyStandardSalaryLevelWages())){
			throw new BusinessException("请至少保存一条数据");
		}
		for (HrmsSalaryLevelWage salaryLevelWage : entity.getPolicyStandardSalaryLevelWages()) {
			salaryLevelWage.setPolicyStandardId(entity.getPolicyStandardId());
			salaryLevelWage.setSalaryLevelCategory(entity.getSalaryLevelCategory());
			if(StringUtils.isNotBlank(salaryLevelWage.getSalaryLevelWageId())){
				update(salaryLevelWage);
			}else{
				salaryLevelWage.setIsEnable("1");
				insert(salaryLevelWage);
			}
		}
	}
	
	@Override
	public HrmsSalaryLevelWage getDataByLevelId(HrmsSalaryLevelWage entity) {

		Page page = new Page();
		page.setPageSize(Integer.MAX_VALUE);
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		List<HrmsSalaryLevelWage> list = hrmsSalaryLevelWageMapper.getDataByLevelId(page, entity);
		if (CollectionUtils.isNotEmpty(list)) {
			Map<String, String> salaryLevelCategoryMap = hrmsDictInfoService.convertDictMap(DictContants.SALARY_LEVEL_CATEGORY);
			if (salaryLevelCategoryMap != null && salaryLevelCategoryMap.size() > 0) {
				for (HrmsSalaryLevelWage wage : list) {
					wage.setSalaryLevelCategoryName(salaryLevelCategoryMap.get(wage.getSalaryLevelCategory()));
				}
			}
		}
		if(list!= null && list.size() >0){
			return list.get(0);
		}
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public void batchEable(List<String> salaryLevelWageIds, String enable) {
		Example example = new Example(HrmsSalaryLevelWage.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andIn("salaryLevelWageId",salaryLevelWageIds);
		HrmsSalaryLevelWage salaryLevelWage = new HrmsSalaryLevelWage();
		salaryLevelWage.setIsEnable(enable);
		for (String wageId : salaryLevelWageIds) {
			HrmsSalaryLevelWage oldWage = hrmsSalaryLevelWageMapper.selectByPrimaryKey(wageId);
			HrmsSalaryLevelWage newWage = BeanUtil.copyProperties(oldWage, HrmsSalaryLevelWage.class);
			newWage.setIsEnable(enable);
			// 增加处理快照
			this.fillDescField(oldWage);
			this.fillDescField(newWage);
			CommTableSnapshotSaveReq commTableSnapshotSaveReq = new CommTableSnapshotSaveReq();
			commTableSnapshotSaveReq.setTableName("hrms_salary_level_wage");
			commTableSnapshotSaveReq.setRowPkValue(wageId);
			commTableSnapshotSaveReq.setOld(oldWage);
			commTableSnapshotSaveReq.setNow(newWage);
			commTableSnapshotFeignService.save(commTableSnapshotSaveReq);
		}
		hrmsSalaryLevelWageMapper.updateByExampleSelective(salaryLevelWage,example);
	}

	/**
	 *
	 * @Title importData
	 * @Description 导入
	 * @return Map
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	@Transactional(readOnly = false)
	@Override
	public Map<String,Object> importData(List<HrmsSalaryLevelWage> list){
		log.info("导入的数据:" + list);
		if(CollectionUtil.isEmpty(list)){
			throw new BusinessException("请至少添加一条数据");
		}
		//薪级类别
		Map<String, String> salaryCategoryConvertDictMap = hrmsDictInfoService.convertDictValueKeyMap(DictContants.SALARY_LEVEL_CATEGORY);
		Map<String, String> salaryLevelMap = convertSarlayLevelMap();
		//获取政策标准列表
		Map<String, String> policyStandardMap = convertPolicyStandardMap();

		StringBuffer errorMsg =  new StringBuffer();
		int sucessNum = 0;
		for (HrmsSalaryLevelWage salaryLevelWage : list) {
			if (StringUtils.isNotBlank(salaryLevelWage.getSalaryLevelCategoryName())) {//薪级类别
				salaryLevelWage.setSalaryLevelCategory(salaryCategoryConvertDictMap.get(salaryLevelWage.getSalaryLevelCategoryName().trim()));
			}
			if (StringUtils.isNotBlank(salaryLevelWage.getSalaryLevelName())) {//薪级类别
				salaryLevelWage.setSalaryLevelId(salaryLevelMap.get(salaryLevelWage.getSalaryLevelName().trim()+"@"+ salaryLevelWage.getSalaryLevelCategoryName().trim()));
			}
			if (StringUtils.isNotBlank(salaryLevelWage.getPolicyStandardName())) {//政策标准
				salaryLevelWage.setPolicyStandardId(policyStandardMap.get(salaryLevelWage.getPolicyStandardName().trim()));
			}
			salaryLevelWage.setIsDeleted("N");
			salaryLevelWage.setIsEnable("1");
			try {
				sucessNum += insert(salaryLevelWage);
			}catch (Exception e){
				errorMsg.append("薪级等级："+salaryLevelWage.getSalaryLevelName()+"[类别："+salaryLevelWage.getSalaryLevelCategoryName()+"]导入失败："+e.getMessage()).append(".\n");
				e.printStackTrace();
			}
		}
		Map<String,Object> map = new HashMap<>();
		map.put("successNum",sucessNum);
		map.put("errorNum",list.size() - sucessNum);
		map.put("errorMsg",errorMsg.toString());
		return map;
	}

	private void fillDescField(HrmsSalaryLevelWage record) {
		// 填充isEnable
		if (StringUtils.isNotBlank(record.getIsEnable())) {
			if(StrUtil.equals("1",record.getIsEnable())){
				record.setIsEnable("启用");
			}else if(StrUtil.equals("2",record.getIsEnable()))
				record.setIsEnable("禁用");
		}
	}

	//薪级等级
	private Map<String, String> convertSarlayLevelMap() {
		Map<String, String> map = Maps.newHashMap();
		List<HrmsSalaryLevel> salaryLevels = hrmsSalaryLevelService.getList(new HrmsSalaryLevel());
		if (CollUtil.isNotEmpty(salaryLevels)) {
			for (HrmsSalaryLevel d : salaryLevels) {
				map.put(d.getSalaryLevelName()+"@"+d.getSalaryLevelCategoryName(),d.getSalaryLevelId());
			}
		}
		return map;
	}

	//政策标准
	private Map<String, String> convertPolicyStandardMap() {
		Map<String, String> map = Maps.newHashMap();
		List<HrmsSalaryPolicyStandard> policyStandardList = hrmsSalaryPolicyStandardService.getList(new HrmsSalaryPolicyStandard());
		if (CollUtil.isNotEmpty(policyStandardList)) {
			for (HrmsSalaryPolicyStandard d : policyStandardList) {
				map.put(d.getPolicyStandardName(),d.getPolicyStandardId());
			}
		}
		return map;
	}
}

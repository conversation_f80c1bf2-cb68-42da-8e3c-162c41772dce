package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsSuggestionBoxMapper;
import cn.trasen.hrms.model.HrmsSuggestionBox;
import cn.trasen.hrms.service.HrmsSuggestionBoxService;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsSuggestionBoxServiceImpl
 * @Description TODO
 * @date 2022��10��22�� ����4:44:32
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsSuggestionBoxServiceImpl implements HrmsSuggestionBoxService {

	@Autowired
	private HrmsSuggestionBoxMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsSuggestionBox record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		record.setStatus("1");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setUpdateUser(user.getUsercode());
		}
		String opinion =Html2Text(record.getOpinion()) ;
		//新增过滤  加密
		if(sqlValidate(opinion)) {
			throw new BusinessException("存在非法字符");
		}
		
//		 byte[] desCrypto = DesUtils.desCrypto(opinion.getBytes());
		 record.setOpinion(opinion);
		return mapper.insertSelective(record);
	}
	
    //效验  
    private  boolean sqlValidate(String str) {  
        str = str.toLowerCase();//统一转为小写  
        String badStr = "'|and|exec|execute|insert|select|delete|update|count|drop|*|%|chr|mid|master|truncate|" +  
                "char|declare|sitename|net user|xp_cmdshell|;|or|-|+|,|like'|and|exec|execute|insert|create|drop|" +  
                "table|from|grant|use|group_concat|column_name|" +  
                "information_schema.columns|table_schema|union|where|select|delete|update|order|by|count|*|" +  
                "chr|mid|master|truncate|char|declare|or|;|-|--|+|,|like|//|/|%|#";//过滤掉的sql关键字，可以手动添加  
        String[] badStrs = badStr.split("\\|");  
        for (int i = 0; i < badStrs.length; i++) {  
            if (str.indexOf(badStrs[i]) >= 0) {  
                return true;  
            }  
        }  
        return false;  
    } 
    
	public static String Html2Text(String inputString) {
		 String htmlStr = inputString; //含html标签的字符串 
		    String textStr = "";
		    java.util.regex.Pattern p_script;
		    java.util.regex.Matcher m_script;
		    java.util.regex.Pattern p_style;
		    java.util.regex.Matcher m_style;
		    java.util.regex.Pattern p_html;
		    java.util.regex.Matcher m_html;
		    try {
		 	   
		        String regEx_script = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>"; //定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script> } 
		        String regEx_style = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>"; //定义style的正则表达式{或<style[^>]*?>[\\s\\S]*?<\\/style> } 
		        String regEx_html = "<[^>]+>"; //定义HTML标签的正则表达式 
		        p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
		        m_script = p_script.matcher(htmlStr);
		        htmlStr = m_script.replaceAll(""); //过滤script标签 
		        p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
		        m_style = p_style.matcher(htmlStr);
		        htmlStr = m_style.replaceAll(""); //过滤style标签 
		        p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
		        m_html = p_html.matcher(htmlStr);
		        htmlStr = m_html.replaceAll(""); //过滤html标签 
		        textStr = htmlStr;
		    }  catch(Exception e) {
		        log.error("Html2Text: " + e.getMessage());
		    }
		return textStr;
	}
    

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsSuggestionBox record) {
		record.setUpdateDate(new Date());
		record.setStatus("2"); //已查看
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsSuggestionBox record = new HrmsSuggestionBox();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public HrmsSuggestionBox selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		
		 HrmsSuggestionBox selectByPrimaryKey = mapper.selectByPrimaryKey(id);
		//状态改为已查看
		HrmsSuggestionBox record = new HrmsSuggestionBox();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setStatus("2"); //已查看
		record.setLookTime(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setLookCode(user.getUsercode());
			record.setLookName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
		}
		mapper.updateByPrimaryKeySelective(record);
		/*//内容解密
		byte[] decrypt = null;
		try {
			decrypt = DesUtils.decrypt(selectByPrimaryKey.getOpinion().getBytes());
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			log.error("解密失败");
		}
		 selectByPrimaryKey.setOpinion(new String(decrypt))*/;
		return selectByPrimaryKey;
	}

	@Override
	public DataSet<HrmsSuggestionBox> getDataSetList(Page page, HrmsSuggestionBox record) {
		Example example = new Example(HrmsSuggestionBox.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(!StringUtil.isEmpty(record.getOpinion())) {
			criteria.andLike("opinion", "%" + record.getOpinion() + "%");
		}
		if(!StringUtil.isEmpty(record.getBoxType())) {
			criteria.andEqualTo("boxType", record.getBoxType());
		}
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsSuggestionBox> records = mapper.selectByExampleAndRowBounds(example, page);
		//遍历解密
	/*	records.forEach(item->{
			try {
				item.setOpinion(new String(DesUtils.decrypt(item.getOpinion().getBytes())));
			} catch (Exception e) {
				log.error("解密失败"+e.getMessage());
			}
		});*/
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public boolean verificationPwd(String pwdOne, String pwdTwo,String pwdId) {
		
		Map<String,String> suggestionPwd = mapper.selectSuggestionPwd(pwdId);
		
		String pwd_one = suggestionPwd.get("pwd_one");
		String pwd_two = suggestionPwd.get("pwd_two");
		
		if(pwdOne.equals(pwd_one) && pwdTwo.equals(pwd_two)) {
			return true;
		}
		
		return false;
	}
	
	
}

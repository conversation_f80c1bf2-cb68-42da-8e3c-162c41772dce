package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsEmployeeExtendMapper;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsEmployeeExtend;
import cn.trasen.hrms.service.HrmsEmployeeExtendService;

/**   
 * @Title: HrmsEmployeeExtendServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 员工扩展信息 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月17日 下午4:58:17 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsEmployeeExtendServiceImpl implements HrmsEmployeeExtendService {

	@Autowired
	private HrmsEmployeeExtendMapper hrmsEmployeeExtendMapper;
	
	/**
	 * @Title: batchInsert
	 * @Description: 批量新增
	 * @param list
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月17日 下午4:17:45
	 */
	@Override
	public int batchInsert(List<HrmsEmployeeExtend> list) {
		return hrmsEmployeeExtendMapper.batchInsert(list);
	}

	/**
	 * @Title: 新增员工扩展信息
	 * @Description: TODO
	 * @Param: 
	 * <AUTHOR>
	 * @date 2020年3月25日 下午3:08:39
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsEmployeeExtend entity) {
		entity.setEmployeeExtendId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		return hrmsEmployeeExtendMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 更新员工扩展信息
	 * @Param: 
	 * <AUTHOR>
	 * @date 2020年3月25日 下午3:08:53
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsEmployeeExtend entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsEmployeeExtendMapper.updateByPrimaryKeySelective(entity);
	}

	/**
	 * @Title: deleted
	 * @Description: 删除员工扩展信息
	 * @Param: 
	 * <AUTHOR>
	 * @date 2020年3月25日 下午3:09:01
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsEmployeeExtend extend = hrmsEmployeeExtendMapper.selectByPrimaryKey(id);
		if (extend != null) {
			extend.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsEmployeeExtendMapper.updateByPrimaryKeySelective(extend);
	}

	/**  
	 * 该方法不加事务，事务在新增员工业务层方法统一管理
	 * @Title: saveOrUpdate
	 * @Description: 新增/修改员工扩展信息(新增员工时使用该方法)
	 * @Param: entity
	 * <AUTHOR>
	 * @date 2020年3月25日 下午4:50:59 
	 */
	@Override
	public void saveOrUpdate(HrmsEmployee entity) {
		if (StringUtils.isNotBlank(entity.getEmployeeId())) {
			HrmsEmployeeExtend extend = hrmsEmployeeExtendMapper.findByEmployeeId(entity.getEmployeeId());
			if (extend == null) {
				extend = new HrmsEmployeeExtend();
			}
			extend.setEmployeeId(entity.getEmployeeId());
			extend.setEnterpriseId(entity.getEnterpriseId());
			extend.setOldEmployeeNo(entity.getOldEmployeeNo());
			extend.setHisEmployeeNo(entity.getHisEmployeeNo());
			extend.setEntryDate(entity.getEntryDate());
			extend.setRetireDate(entity.getRetireDate());
			extend.setQuitDate(entity.getQuitDate());
			extend.setReemploymentDate(entity.getReemploymentDate());
			extend.setPartyDate(entity.getPartyDate());
			extend.setWorkStartDate(entity.getWorkStartDate());
			extend.setUnitStartDate(entity.getUnitStartDate());
			extend.setPersonalIdentity(entity.getPersonalIdentity());
			extend.setWorkNature(entity.getWorkNature());
			extend.setGoodAt(entity.getGoodAt());
			extend.setCheckWorkDepart(entity.getCheckWorkDepart());
			extend.setReviewDepart(entity.getReviewDepart());
			extend.setUpgradeFlag(entity.getUpgradeFlag());
			extend.setImproveFlag(entity.getImproveFlag());
			extend.setIsDuplicateEntry(entity.getIsDuplicateEntry());
			extend.setEmergencyContact(entity.getEmergencyContact());
			extend.setEmergencyTel(entity.getEmergencyTel());
			extend.setProbationSalary(entity.getProbationSalary());
			extend.setRegularSalary(entity.getRegularSalary());
			extend.setBuySocialDate(entity.getBuySocialDate());
			extend.setBuyProvidentDate(entity.getBuyProvidentDate());
			extend.setSalaryRemark(entity.getSalaryRemark());

			
			//锦屏添加字段
			extend.setJobDescriptionType(entity.getJobDescriptionType());
			extend.setJobDescriptionTypeTime(entity.getJobDescriptionTypeTime());
			extend.setConcurrentPosition(entity.getConcurrentPosition());
			extend.setConcurrentPositionTime(entity.getConcurrentPositionTime());
			extend.setIsLeader(entity.getIsLeader());
			extend.setPostType(entity.getPostType());
			extend.setDoctorQualificationCertificate(entity.getDoctorQualificationCertificate());
			extend.setMidwife(entity.getMidwife());
			extend.setStartEmployDate(entity.getStartEmployDate());
			extend.setEndEmployDate(entity.getEndEmployDate());
			extend.setIsVeteran(entity.getIsVeteran());
			extend.setUnitName(entity.getUnitName());
			extend.setBusinessId(entity.getBusinessId());
			extend.setBusinessId2(entity.getBusinessId2());
			extend.setBusinessId3(entity.getBusinessId3());
			extend.setBornAddress(entity.getBornAddress());
			extend.setBornAddressName(entity.getBornAddressName());
			extend.setAuthorizedOrg(entity.getAuthorizedOrg());

			extend.setFirstEducationType(entity.getFirstEducationType());
			extend.setEmployDuty(entity.getEmployDuty());
			extend.setEmployDutyDate(entity.getEmployDutyDate());
			extend.setEmployDutyEquallyDate(entity.getEmployDutyEquallyDate());
			extend.setEmployDutyDuration(entity.getEmployDutyDuration());
			extend.setComplianceTraining(entity.getComplianceTraining());
			
			extend.setOperationDate(entity.getOperationDate());
			extend.setOperationOrg(entity.getOperationOrg());
			extend.setOperationScope(entity.getOperationScope());
			extend.setOperationNumber(entity.getOperationNumber());
			extend.setOperationType(entity.getOperationType());
			extend.setArchiveAddress(entity.getArchiveAddress());
		
//			if (StringUtils.isBlank(extend.getEmployeeExtendId())) {
//				extend.setEmployeeExtendId(String.valueOf(IdWork.id.nextId()));
//				extend.setIsDeleted(Contants.IS_DELETED_FALSE);
//				extend.setCreateUser(UserInfoHolder.getCurrentUserCode());
//				extend.setCreateUserName(UserInfoHolder.getCurrentUserName());
//				extend.setCreateDate(new Date());
//				hrmsEmployeeExtendMapper.insert(extend);
//			} else {
//				extend.setUpdateUser(UserInfoHolder.getCurrentUserCode());
//				extend.setUpdateUserName(UserInfoHolder.getCurrentUserName());
//				extend.setUpdateDate(new Date());
//				hrmsEmployeeExtendMapper.updateByPrimaryKeySelective(extend);
//			}
		}
	}

}

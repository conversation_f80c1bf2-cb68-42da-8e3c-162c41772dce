package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsInterviewManagementMapper;
import cn.trasen.hrms.enums.InterviewStatusEnum;
import cn.trasen.hrms.model.HrmsInterviewManagement;
import cn.trasen.hrms.model.HrmsResumeStorehouse;
import cn.trasen.hrms.service.HrmsInterviewManagementService;
import cn.trasen.hrms.service.HrmsResumeStorehouseService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsInterviewManagementServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 面试管理 业务层接口实现类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月22日 上午11:21:24 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsInterviewManagementServiceImpl implements HrmsInterviewManagementService {

	@Autowired
	HrmsInterviewManagementMapper hrmsInterviewManagementMapper;
	@Autowired
	HrmsResumeStorehouseService hrmsResumeStorehouseService;

	/**
	 * @Title: interviewInvitation
	 * @Description: 面试邀约
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年5月22日 上午11:43:30
	 */
	@Override
	@Transactional(readOnly = false)
	public int interviewInvitation(HrmsInterviewManagement entity) {
		Assert.hasText(entity.getResumeStorehouseId(), "resumeStorehouseId must be not null.");
		entity.setInterviewManagementId( String.valueOf(IdWork.id.nextId()));
		entity.setInterviewStatus(InterviewStatusEnum.INTERVIEW_STATUS_1.getKey()); // 发起邀约默认为待面试状态
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		// 从简历库读取面试人员基本信息
		HrmsResumeStorehouse storehouse = hrmsResumeStorehouseService.selectByPrimaryKey(entity.getResumeStorehouseId());
		if (storehouse != null) {
			entity.setEmployeeName(storehouse.getEmployeeName());
			entity.setApplyPost(storehouse.getApplyPost());
			entity.setTelephoneNumber(storehouse.getTelephoneNumber());
			entity.setEmail(storehouse.getEmail());
			entity.setResumeStorehouseId(entity.getResumeStorehouseId());
		}
		int result = hrmsInterviewManagementMapper.insert(entity);
		if (result > 0) {
			hrmsResumeStorehouseService.updateById(entity.getResumeStorehouseId());
			return result;
		}
		return result;
	}

	/**
	 * @Title: update
	 * @Description: 更新面试记录
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年5月22日 下午1:59:50
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsInterviewManagement entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsInterviewManagementMapper.updateByPrimaryKeySelective(entity);
	}

	/**
	 * @Title: updateByEntity
	 * @Description: 根据ID更新面试记录(不加事务,便于其他Service调用)
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年5月25日 下午3:13:37
	 */
	@Override
	public int updateByEntity(HrmsInterviewManagement entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsInterviewManagementMapper.updateByPrimaryKeySelective(entity);
	}

	/**
	 * @Title: getDataList
	 * @Description: 查询面试记录(分页)
	 * @param page
	 * @param entity
	 * @Return List<HrmsInterviewManagement>
	 * <AUTHOR>
	 * @date 2020年5月22日 下午2:02:46
	 */
	@Override
	public List<HrmsInterviewManagement> getDataList(Page page, HrmsInterviewManagement entity) {
		Example example = new Example(HrmsInterviewManagement.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);

		if (StringUtils.isNotBlank(entity.getEmployeeName())) { // 姓名
			example.and().andLike("employeeName", entity.getEmployeeName() + "%");
		}

		if (StringUtils.isNotBlank(entity.getApplyPost())) { // 应聘岗位
			example.and().andLike("applyPost", "%" + entity.getApplyPost() + "%");
		}

		if (StringUtils.isNotBlank(entity.getInterviewStatus())) { // 面试状态
			example.and().andEqualTo("interviewStatus", entity.getInterviewStatus());
		}

		List<HrmsInterviewManagement> list = hrmsInterviewManagementMapper.selectByExampleAndRowBounds(example, page);
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsInterviewManagement interview : list) {
				interview.setInterviewStatusText(InterviewStatusEnum.getValByKey(interview.getInterviewStatus())); // 面试状态文本值
				String evaluationStatusText = "未评价";
				if(interview.getEvaluationStatus() != null && interview.getEvaluationStatus() == 2) {
					evaluationStatusText = "已评价";
				}
				interview.setEvaluationStatusText(evaluationStatusText);
			}
		}
		return list;
	}

	/**
	 * @Title: selectByPrimaryKey
	 * @Description: 根据主键ID查询
	 * @param id 主键ID
	 * @Return HrmsInterviewManagement
	 * <AUTHOR>
	 * @date 2020年5月22日 下午2:57:21
	 */
	@Override
	public HrmsInterviewManagement selectByPrimaryKey(String id) {
		return hrmsInterviewManagementMapper.selectByPrimaryKey(id);
	}

}

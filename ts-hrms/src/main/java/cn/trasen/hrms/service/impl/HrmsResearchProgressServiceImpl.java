package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.hrms.dao.HrmsResearchProgressMapper;
import cn.trasen.hrms.model.HrmsResearchProgress;
import cn.trasen.hrms.model.HrmsResearchTopic;
import cn.trasen.hrms.service.HrmsResearchProgressService;
import cn.trasen.hrms.service.HrmsResearchTopicService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsResearchProgressServiceImpl
 * @Description TODO
 * @date 2021��11��6�� ����3:44:15
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsResearchProgressServiceImpl implements HrmsResearchProgressService {

	@Autowired
	private HrmsResearchProgressMapper mapper;
	
	@Autowired
	private HrmsResearchTopicService hrmsResearchTopicService;
 
	@Autowired
	private InformationFeignService informationFeignService;
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsResearchProgress record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		record.setProgressType("1");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		HrmsResearchTopic hrmsResearchTopic = hrmsResearchTopicService.selectById(record.getTopicId());
		
		hrmsResearchTopicService.updateDateBYid(record.getTopicId());
		
		StringBuffer sb = new StringBuffer();
		sb.append(user.getUsername()).append("更新了课题“")
		.append(hrmsResearchTopic.getTopicName()).append("”进展，请您知悉！");
		
		NoticeReq notice = NoticeReq.builder()
				.content(sb.toString())
				.noticeType("1")
				.receiver(hrmsResearchTopic.getCreateUser())
				.sender("admin")
				.senderName("系统管理员")
				.subject("进度更新提醒")
				.toUrl("/research/topic").source("科研课题")
				.build();
		informationFeignService.sendNotice(notice);
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsResearchProgress record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		HrmsResearchTopic hrmsResearchTopic = hrmsResearchTopicService.selectById(record.getTopicId());
		hrmsResearchTopicService.updateDateBYid(record.getTopicId());
		StringBuffer sb = new StringBuffer();
		sb.append(user.getUsername()).append("更新了课题“")
		.append(hrmsResearchTopic.getTopicName()).append("”进展，请您知悉！");
		
		NoticeReq notice = NoticeReq.builder()
				.content(sb.toString())
				.noticeType("1")
				.receiver(hrmsResearchTopic.getCreateUser())
				.sender("admin")
				.senderName("系统管理员")
				.subject("进度更新提醒")
				.toUrl("/research/topic").source("科研课题")
				.build();
		informationFeignService.sendNotice(notice);
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsResearchProgress record = new HrmsResearchProgress();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsResearchProgress selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsResearchProgress> getDataSetList(Page page, HrmsResearchProgress record) {
		Example example = new Example(HrmsResearchProgress.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsResearchProgress> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsSchedulingFrequencyMapper;
import cn.trasen.hrms.model.HrmsSchedulingFrequency;
import cn.trasen.hrms.service.HrmsSchedulingFrequencyService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * @ClassName: HrmsSchedulingFrequencyServiceImpl
 * @Description:班次基本信息实现类
 * @author: WZH
 * @date: 2021年7月14日 上午11:35:25
 * @Copyright:
 */
@Service
public class HrmsSchedulingFrequencyServiceImpl implements HrmsSchedulingFrequencyService {
	

	@Autowired
	HrmsSchedulingFrequencyMapper hrmsSchedulingFrequencyMapper;

	@Override
	public int insert(HrmsSchedulingFrequency hrmsSchedulingFrequency) {
		

		if("1".equals(hrmsSchedulingFrequency.getFrequencyType())) {
			//考勤小时计算
			String frequencyTime = hrmsSchedulingFrequency.getFrequencyTime();
			String[] split = frequencyTime.split(",");
			double countDate = 0;
			String min = "2021-05-01 ";
			String max = "2021-05-02 ";
			for (int i = 0; i < split.length; i++) {
				String[] split2 = split[i].split("-");  //00:00-04:00
				if(DateUtils.judgeSize(min +split2[0], min +split2[1])) {
					countDate += DateUtils.dateDiff(min +split2[0], min +split2[1]);
				}else {
					countDate += DateUtils.dateDiff(min +split2[0], max +split2[1]);
				}
			}
			hrmsSchedulingFrequency.setDayHours(String.format("%.1f", (countDate / 60)));
		}else {
			hrmsSchedulingFrequency.setDayHours("0");
			hrmsSchedulingFrequency.setDayLong("0");
		}
		hrmsSchedulingFrequency.setFrequencyId(IdUtil.getId());
		hrmsSchedulingFrequency.setIsDeleted(Contants.IS_DELETED_FALSE);
		hrmsSchedulingFrequency.setCreateUser(UserInfoHolder.getCurrentUserCode());
		hrmsSchedulingFrequency.setCreateUserName(UserInfoHolder.getCurrentUserName());
		hrmsSchedulingFrequency.setCreateDate(new Date());
		hrmsSchedulingFrequency.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsSchedulingFrequencyMapper.insertSelective(hrmsSchedulingFrequency);
	}
	
	
	
	@Override
	public int update(HrmsSchedulingFrequency hrmsSchedulingFrequency) {
		
		Assert.hasText(hrmsSchedulingFrequency.getFrequencyId(), "ID不能为空.");
		
	/*	Example example = new Example(HrmsSchedulingFrequency.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("frequencyName", hrmsSchedulingFrequency.getFrequencyName());
		example.and().andNotEqualTo("frequencyId", hrmsSchedulingFrequency.getFrequencyId());
		List<HrmsSchedulingFrequency> list = hrmsSchedulingFrequencyMapper.selectByExample(example);
		if(list != null && list.size() > 0) {
			throw new RuntimeException("班次已存在");
		}*/
		if("1".equals(hrmsSchedulingFrequency.getFrequencyType())) {
			//考勤小时计算
			String frequencyTime = hrmsSchedulingFrequency.getFrequencyTime();
			String[] split = frequencyTime.split(",");
			double countDate = 0;
			String min = "2021-05-01 ";
			String max = "2021-05-02 ";
			for (int i = 0; i < split.length; i++) {
				String[] split2 = split[i].split("-");  //00:00-04:00
				if(DateUtils.judgeSize(min +split2[0], min +split2[1])) {
					countDate += DateUtils.dateDiff(min +split2[0], min +split2[1]);
				}else {
					countDate += DateUtils.dateDiff(min +split2[0], max +split2[1]);
				}
			}
			hrmsSchedulingFrequency.setDayHours(String.format("%.1f", (countDate / 60)));
		}else {
			hrmsSchedulingFrequency.setDayHours("0");
			hrmsSchedulingFrequency.setDayLong("0");
		}
		hrmsSchedulingFrequency.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		hrmsSchedulingFrequency.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		hrmsSchedulingFrequency.setUpdateDate(new Date());
		return hrmsSchedulingFrequencyMapper.updateByPrimaryKeySelective(hrmsSchedulingFrequency);
	}

	@Override
	public int deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsSchedulingFrequency bean = new HrmsSchedulingFrequency();
		bean.setFrequencyId(id);
		bean.setIsDeleted(Contants.IS_DELETED_TURE);
		bean.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		bean.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		bean.setUpdateDate(new Date());
		return hrmsSchedulingFrequencyMapper.updateByPrimaryKeySelective(bean);
	}

	@Override
	public List<HrmsSchedulingFrequency> getDataList(Page page, HrmsSchedulingFrequency hrmsSchedulingFrequency) {
		Example example = new Example(HrmsSchedulingFrequency.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		//根据当前登录账号机构编码过滤查询数据
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(hrmsSchedulingFrequency.getFrequencyName())) {
			String condition = hrmsSchedulingFrequency.getFrequencyName();
			criteria.andLike("frequencyName", "%" + condition.trim() + "%");
		}
//		example.and(
//				example.createCriteria().andEqualTo("orgId", ).orEqualTo("orgId", ""));
		example.and().andCondition(" FIND_IN_SET('"+UserInfoHolder.getCurrentUserInfo().getDeptcode()+"', org_id) or org_Id is null or  org_Id='' ");
		return hrmsSchedulingFrequencyMapper.selectByExampleAndRowBounds(example, page);
	}

	@Override
	public List<HrmsSchedulingFrequency> getList(HrmsSchedulingFrequency hrmsSchedulingFrequency) {
		return hrmsSchedulingFrequencyMapper.selectAll();
	}

	@Override
	public HrmsSchedulingFrequency findDetailById(String id) {
		return hrmsSchedulingFrequencyMapper.selectByPrimaryKey(id);
	}



	
	@Override
	public List<HrmsSchedulingFrequency> getJurisdictionPageList(Page page, HrmsSchedulingFrequency hrmsSchedulingFrequency) {
		Example example = new Example(HrmsSchedulingFrequency.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		//根据当前登录账号机构编码过滤查询数据
		hrmsSchedulingFrequency.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(hrmsSchedulingFrequency.getFrequencyName())) {
			String condition = hrmsSchedulingFrequency.getFrequencyName();
			criteria.andLike("frequencyName", "%" + condition.trim() + "%");
		}
		if(!UserInfoHolder.ISADMIN()) {
			hrmsSchedulingFrequency.setIsAdmin("1");
			if(StringUtils.isEmpty(hrmsSchedulingFrequency.getOrgId())) {
				hrmsSchedulingFrequency.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
				hrmsSchedulingFrequency.setCreateUser(UserInfoHolder.getCurrentUserCode());
			}
		}
		return hrmsSchedulingFrequencyMapper.getPageList(page,hrmsSchedulingFrequency);
	}



	/* (non-Javadoc)
	 * 修改节假日是休息还是算上班
	 */
	@Override
	public int updateRest(HrmsSchedulingFrequency entity) {
		// TODO Auto-generated method stub
		return hrmsSchedulingFrequencyMapper.updateRest(entity);
	}

}

package cn.trasen.hrms.service;

import java.util.List;
import java.util.Map;

import cn.trasen.hrms.model.HrmsDictInfo;

/**   
 * @Title: HrmsDictInfoService.java 
 * @Package cn.trasen.hrms.service 
 * @Description: 数据字典 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月23日 下午4:12:23 
 * @version V1.0   
 */
public interface HrmsDictInfoService {
	
//	/**
//	 * @Title: insert
//	 * @Description: 新增字典
//	 * @Param: entity
//	 * @Return: int
//	 * <AUTHOR>
//	 */
//	int insert(HrmsDictInfo entity);
//
//	/**
//	 * @Title: update
//	 * @Description: 修改字典
//	 * @Param:
//	 * @Return: int
//	 * <AUTHOR>
//	 */
//	int update(HrmsDictInfo entity);

//	/**
//	 *
//	 * @Title: deleted
//	 * @Description: 删除字典
//	 * @Param: id
//	 * @Return: int
//	 * <AUTHOR>
//	 */
//	int deleted(String id);

//	/**
//	 * @Title: getDataList
//	 * @Description: 获取数据字典列表
//	 * @Param: page
//	 * @param entity
//	 * @Return: List<HrmsDictInfo>
//	 * <AUTHOR>
//	 */
//	List<HrmsDictInfo> getDataList(Page page, HrmsDictInfo entity);

	/**
	 * @Title: getDictInfoListByDictType
	 * @Description: 根据字典类型获取数据字典
	 * @Param: dictType 字典类型
	 * @Return: List<HrmsDictInfo>
	 * <AUTHOR>
	 */
	List<HrmsDictInfo> getDictInfoListByDictType(String dictType);

//	/**
//	 * @Title: getDictByTypeAndValue
//	 * @Description: 根据类型和值查询字典
//	 * @Param: type
//	 * @param value
//	 * @Return: HrmsDictInfo
//	 * <AUTHOR>
//	 */
//	HrmsDictInfo getDictByTypeAndValue(String type, Object value);

	/**
	 * @Title: convertDictMap
	 * @Description: 查询字典列表并转换成Map形式(key-value)
	 * @Param: dictType 数据字典类型
	 * @Return: Map<String,String>
	 * <AUTHOR>
	 * @date 2020年3月26日 下午3:16:32
	 */
	Map<String, String> convertDictMap(String dictType);

	/**
	 * @Title: convertDictValueKeyMap
	 * @Description: 查询字典列表并转换成Map形式(value-key)
	 * @param dictType
	 * @Return Map<String,String>
	 * <AUTHOR>
	 * @date 2020年6月8日 下午3:08:22
	 */
	Map<String, String> convertDictValueKeyMap(String dictType);
//
//	/**
//	 * @Title: excelImportDictInfo
//	 * @Description: excel导入数据字典
//	 * @param list
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年4月14日 下午1:51:22
//	 */
//	PlatformResult<String> excelImportDictInfo(List<HrmsDictInfo> list);
//
//	/**
//	 * @Title: initCache
//	 * @Description: 初始化缓存
//	 * @Return void
//	 * <AUTHOR>
//	 * @date 2020年4月16日 下午1:53:01
//	 */
//	void initCache();
//
//	/**
//	 * @Title: validate
//	 * @Description: 数据校验
//	 * @param entity
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年5月26日 下午2:26:10
//	 */
//	PlatformResult<String> validate(HrmsDictInfo entity);

}

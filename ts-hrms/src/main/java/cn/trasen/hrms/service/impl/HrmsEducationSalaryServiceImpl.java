package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsEducationSalaryMapper;
import cn.trasen.hrms.model.HrmsEducationSalary;
import cn.trasen.hrms.service.HrmsEducationSalaryService;

/**    
  * <P> @Description: TODO</p>
  * <P> @Date: 2020年12月3日  下午3:53:53 </p>
  * <P> @Author: wang<PERSON>hua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
 
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsEducationSalaryServiceImpl implements HrmsEducationSalaryService {
	
	@Autowired
	private HrmsEducationSalaryMapper hrmsEducationSalaryMapper;

	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsEducationSalary entity) {
		
		HrmsEducationSalary record = new HrmsEducationSalary();
		record.setEmployeeCategory(entity.getEmployeeCategory());
		record.setFirstEducationType(entity.getFirstEducationType());
		HrmsEducationSalary selectOne = hrmsEducationSalaryMapper.selectOne(record);
		if(selectOne != null) {
			return 0;
		}else {
			entity.setEducationSalaryId(String.valueOf(IdWork.id.nextId()));
			entity.setIsDeleted(Contants.IS_DELETED_FALSE);
			entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
			entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
			entity.setCreateDate(new Date());
			entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			return hrmsEducationSalaryMapper.insert(entity);
		}
	}

	@Override
	@Transactional(readOnly = false)
	public int delete(String id) {
		return hrmsEducationSalaryMapper.deleteByPrimaryKey(id);
	}

	@Override
	public List<HrmsEducationSalary> getDataList(Page page, HrmsEducationSalary entity) {
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsEducationSalaryMapper.getDataList(entity, page);
	}

	@Override
	@Transactional(readOnly = false)
	public int update(HrmsEducationSalary entity) {
		
		HrmsEducationSalary record = new HrmsEducationSalary();
		record.setEmployeeCategory(entity.getEmployeeCategory());
		record.setFirstEducationType(entity.getFirstEducationType());
		HrmsEducationSalary selectOne = hrmsEducationSalaryMapper.selectOne(record);
		if(selectOne != null && !selectOne.getEducationSalaryId().equals(entity.getEducationSalaryId())) {
			return 0;
		}else {
			entity.setUpdateDate(new Date());
			entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
			entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
			return hrmsEducationSalaryMapper.updateByPrimaryKeySelective(entity);
		}
		
		
	}

}

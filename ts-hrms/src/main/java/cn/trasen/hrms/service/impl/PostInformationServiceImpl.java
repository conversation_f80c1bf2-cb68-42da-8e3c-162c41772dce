package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.PostInformationMapper;
import cn.trasen.hrms.model.PostInformation;
import cn.trasen.hrms.service.PostInformationService;

@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class PostInformationServiceImpl implements PostInformationService {
	
	@Autowired
	PostInformationMapper postInformationImplMapper;
	

	@Override
	public List<PostInformation> getList(String employeeId) {
		return postInformationImplMapper.getList(employeeId);
	}

	@Transactional(readOnly = false)
	@Override
	public int insert(PostInformation entity) {
		
		//历史改为已处理状态
		postInformationImplMapper.updateTreated(entity.getEmployeeId());
		
		entity.setId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptId());
		entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		int row = postInformationImplMapper.insertSelective(entity);
//		if(row > 0) {
//			// 更新文件
//			if(StringUtils.isNotBlank(entity.getBusinessId())) {
//				fileClientService.updateTempBusinessId(entity.getId(), entity.getBusinessId());
//			}
//		}
//
//
		
		return row;
	}

	@Transactional(readOnly = false)
	@Override
	public int update(PostInformation entity) {
		entity.setUpdateDate(new Date());
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		int row =  postInformationImplMapper.updateByPrimaryKeySelective(entity);
		
//		if (row > 0) {
//			if (StringUtils.isNotBlank(entity.getBusinessId())) {
//				fileClientService.updateTempBusinessId(entity.getId(), entity.getBusinessId());
//			}
//		}
		
		return row;
	}

	@Transactional(readOnly = false)
	@Override
	public int deleted(String id) {
		return postInformationImplMapper.deleteByPrimaryKey(id);
	}

	@Override
	public PostInformation getHistoryPostById(String id) {
		return postInformationImplMapper.selectByPrimaryKey(id);
	}

}

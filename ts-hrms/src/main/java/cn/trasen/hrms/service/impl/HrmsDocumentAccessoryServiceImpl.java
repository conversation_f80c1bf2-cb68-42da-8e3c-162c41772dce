package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsDocumentAccessoryMapper;
import cn.trasen.hrms.model.HrmsDocumentAccessory;
import cn.trasen.hrms.service.HrmsDocumentAccessoryService;
import cn.trasen.hrms.utils.IdUtil;


@Service
public class HrmsDocumentAccessoryServiceImpl implements HrmsDocumentAccessoryService {

	@Autowired
	HrmsDocumentAccessoryMapper hrmsDocumentAccessoryMapper;
	
	@Override
	public int insert(HrmsDocumentAccessory entity) {
		entity.setId(IdUtil.getId());
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsDocumentAccessoryMapper.insertSelective(entity);
	}

	@Override
	public int update(HrmsDocumentAccessory entity) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public int deleted(String businessId) {
		return hrmsDocumentAccessoryMapper.updateByBusinessId(businessId);
	}

	@Override
	public List<HrmsDocumentAccessory> getDataListByBusinessId(String businessId) {
		HrmsDocumentAccessory record = new HrmsDocumentAccessory();
		record.setBusinessId(businessId);
		record.setIsDeleted(Contants.IS_DELETED_FALSE);
		return hrmsDocumentAccessoryMapper.select(record);
	}

}

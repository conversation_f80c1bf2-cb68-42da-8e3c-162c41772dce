package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.BootComm.utils.ApplicationUtils;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrNoticeMapper;
import cn.trasen.hrms.dao.HrNoticeRedMapper;
import cn.trasen.hrms.model.HrNotice;
import cn.trasen.hrms.model.HrNoticeRed;
import cn.trasen.hrms.service.HrNoticeService;
import tk.mybatis.mapper.entity.Example;



@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrNoticeServiceImpl implements HrNoticeService{
	
	@Autowired
	private HrNoticeMapper hrNoticeMapper;
	
	@Autowired
	private HrNoticeRedMapper hrNoticeRedMapper;
	
	

	/**
	 * 查询通知公告列表
	 *
	 * @return
	 */
	public List<HrNotice> getDataSetList(Page page, HrNotice record) {
		
		String usercode = UserInfoHolder.getCurrentUserCode();
		
		Example example = new Example(HrNotice.class);
		example.createCriteria().andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
		if(StringUtils.isNotBlank(record.getTitle())) {
			example.and().andLike("title", "%"+record.getTitle().trim()+"%");
		}
		
		if(StringUtils.isNotBlank(record.getStartReleaseTime()) && StringUtils.isNotBlank(record.getEndReleaseTime())) {
			example.and().andBetween("createDate", record.getStartReleaseTime(), record.getEndReleaseTime());
		}
		
		Boolean isAdmin = UserInfoHolder.ISADMIN();// 是否管理员
		
		if(!isAdmin) {
			example.and().andEqualTo("sendType",1).orLike("receiveCode", "%"+usercode+"%");
		}
		
		example.setOrderByClause("create_Date DESC");
		
		return hrNoticeMapper.selectByExampleAndRowBounds(example, page);
		
	}

	/**
	 * @Title: findById  
	 * @Description: 根据ID查询通知公告列表
	 * @param @param noticeId
	 * @param @return    参数  
	 * @return HrNotice    返回类型  
	 * @throws
	 */
	@Transactional(readOnly = false)
	public HrNotice findById(String noticeId) {
		
		HrNotice record = hrNoticeMapper.selectByPrimaryKey(noticeId);
		
		String usercode = UserInfoHolder.getCurrentUserCode();
		
		//读取通知公告
		Example example = new Example(HrNoticeRed.class);
		example.createCriteria().andEqualTo("noticeId",noticeId).andEqualTo("redUser",usercode).andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
		
		List<HrNoticeRed> redList = hrNoticeRedMapper.selectByExample(example);
		
		if(CollectionUtils.isEmpty(redList)) {
			HrNoticeRed hrNoticeRed = new HrNoticeRed();
			hrNoticeRed.setId(ApplicationUtils.GUID32());
			hrNoticeRed.setNoticeId(noticeId);
			hrNoticeRed.setCreateDate(new Date());
			hrNoticeRed.setCreateUser(usercode);
			hrNoticeRed.setCreateUserName(UserInfoHolder.getCurrentUserName());
			hrNoticeRed.setRedUser(usercode);
			hrNoticeRed.setRedUserName(UserInfoHolder.getCurrentUserName());
			hrNoticeRed.setIsDeleted(Contants.IS_DELETED_FALSE);
			hrNoticeRedMapper.insert(hrNoticeRed);
		}
		
		return record;
	}
	
	/**
	 * 修改通知公告
	 * @param record
	 * @return
	 */
	@Transactional(readOnly = false)
	public int update(HrNotice record) {
		
		record.setIsDeleted(Contants.IS_DELETED_TURE);
		record.setUpdateDate(new Date());
		record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		
		return hrNoticeMapper.updateByPrimaryKeySelective(record);
	}

	/**
	 * 新增通知公告
	 * @param record
	 * @return
	 */
	@Transactional(readOnly = false)
	public int insert(HrNotice record) {
		
		record.setNoticeId(ApplicationUtils.GUID32());
		record.setIsDeleted(Contants.IS_DELETED_FALSE);
		record.setCreateUser(UserInfoHolder.getCurrentUserCode());
		record.setCreateUserName(UserInfoHolder.getCurrentUserName());
		record.setCreateDate(new Date());
		
		
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			record.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			record.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		
//		// 更新文件
//		if(StringUtils.isNotBlank(record.getBusinessId())) {
//			fileClientService.updateTempBusinessId(record.getNoticeId(), record.getBusinessId());
//		}
//
		return hrNoticeMapper.insert(record);
		
	}

	/**
	 * 根据ID删除通知公告
	 *
	 * @return
	 */
	@Transactional(readOnly = false)
	public int deleteById(String noticeId) {
		
		HrNotice record = hrNoticeMapper.selectByPrimaryKey(noticeId);
		
		record.setIsDeleted(Contants.IS_DELETED_TURE);
		record.setUpdateDate(new Date());
		record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		
		return hrNoticeMapper.updateByPrimaryKeySelective(record);
		
	}
	
	/**
	 * 
	* @Title: 根据当前登录人获取未读公告  
	* @Description: TODO
	* @Params: @return      
	* @Return: HrNotice
	* <AUTHOR>
	* @date:2020年7月22日
	* @Throws
	 */
	public HrNotice getUnreadHrNoticeInfo() {
	
		String userCode = UserInfoHolder.getCurrentUserCode();
		
		
		
		return hrNoticeMapper.getUnreadHrNoticeInfo(userCode);
	}
}

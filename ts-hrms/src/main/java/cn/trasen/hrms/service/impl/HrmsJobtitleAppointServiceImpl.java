package cn.trasen.hrms.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsJobtitleAppointMapper;
import cn.trasen.hrms.enums.EmployeeStatusEnum;
import cn.trasen.hrms.enums.IncidentAuditStatusEnum;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsJobtitleAppoint;
import cn.trasen.hrms.model.HrmsJobtitleInfo;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.service.HrmsJobtitleAppointService;
import cn.trasen.hrms.service.HrmsJobtitleInfoService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsJobtitleAppointServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 职称聘任信息 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月27日 上午10:18:20 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsJobtitleAppointServiceImpl implements HrmsJobtitleAppointService {

	@Autowired
	HrmsJobtitleAppointMapper hrmsJobtitleAppointMapper;
	@Autowired
	HrmsDictInfoService hrmsDictInfoService;
	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;
	@Autowired
	private HrmsJobtitleInfoService hrmsJobtitleInfoService;

	/**
	 * @Title: insert
	 * @Description: 新增职称聘任信息
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsJobtitleAppoint entity) {
		
		if("1".equals(entity.getHighestLevel())) {
			Example example = new Example(HrmsJobtitleAppoint.class);
			example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
			example.and().andEqualTo("employeeId", entity.getEmployeeId());
			example.and().andEqualTo("highestLevel","1");
			
			List<HrmsJobtitleAppoint> result = hrmsJobtitleAppointMapper.selectByExample(example);
			
			Assert.isTrue(result.size() <= 0, "不允许有多条最高职称信息");
		}
		
		entity.setJobtitleAppointId(String.valueOf(IdWork.id.nextId()));
		if(StringUtil.isEmpty(entity.getApprovalStatus())) {
			entity.setApprovalStatus(IncidentAuditStatusEnum.AUDIT_STATUS_1.getKey());
		}
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		int row = hrmsJobtitleAppointMapper.insert(entity);
		if(row > 0) {
//			// 更新文件
//			if(StringUtils.isNotBlank(entity.getBusinessId())) {
//				fileClientService.updateTempBusinessId(entity.getJobtitleAppointId(), entity.getBusinessId());
//			}
		}
		return row;
	}

	/**
	 * @Title: update
	 * @Description: 更新职称聘任信息
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsJobtitleAppoint entity) {
		
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		
		int row = hrmsJobtitleAppointMapper.updateByPrimaryKeySelective(entity);
		if(row > 0) {
//			if(StringUtils.isNotBlank(entity.getBusinessId())) {
//				fileClientService.updateTempBusinessId(entity.getJobtitleAppointId(), entity.getBusinessId());
//			}
		}
		return row;
	}

	/**
	 * @Title: deleted
	 * @Description: 删除职称聘任信息
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsJobtitleAppoint jobtitleAppoint = hrmsJobtitleAppointMapper.selectByPrimaryKey(id);
		if (jobtitleAppoint != null) {
			jobtitleAppoint.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsJobtitleAppointMapper.updateByPrimaryKeySelective(jobtitleAppoint);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取职称聘任信息列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsJobtitleAppoint>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsJobtitleAppoint> getDataList(Page page, HrmsJobtitleAppoint entity) {
		
		//数据权限
		ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
		String orgRang = thpsUser.getOrgRang();
		if(!UserInfoHolder.ISADMIN()) {	// 是否管理员   
			if(!StringUtil.isEmpty(orgRang)) {//查询组织范围数据
				 entity.setHtOrgIdList(orgRang);
			}
		}
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsJobtitleAppoint> list = hrmsJobtitleAppointMapper.getDataSetList(page, entity);

		if (CollectionUtils.isNotEmpty(list)) {
			Map<String, String> acceptMethodMap = hrmsDictInfoService.convertDictMap(DictContants.CERTIFICATE_OBTAIN_TYPE); // 获取途径字典
			for (HrmsJobtitleAppoint jobtitleAppoint : list) {
				jobtitleAppoint.setAcceptMethodText(acceptMethodMap.get(jobtitleAppoint.getAcceptMethod())); // 获取途径
				jobtitleAppoint.setApprovalStatusText(IncidentAuditStatusEnum.getValByKey(jobtitleAppoint.getApprovalStatus()));//审批状态
			}
		}
		return list;
	}

	/**
	 * @Title: getList
	 * @Description: 查询职称聘任信息列表(不分页)
	 * @param entity
	 * @Return List<HrmsJobtitleAppoint>
	 * <AUTHOR>
	 * @date 2020年4月23日 下午4:33:20
	 */
	@Override
	public List<HrmsJobtitleAppoint> getList(HrmsJobtitleAppoint entity) {
		
		//数据权限
		ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
		String orgRang = thpsUser.getOrgRang();
		if(!UserInfoHolder.ISADMIN()) {	// 是否管理员   
			if(!StringUtil.isEmpty(orgRang)) {//查询组织范围数据
				 entity.setHtOrgIdList(orgRang);
			}
		}
		
		List<HrmsJobtitleAppoint> list = hrmsJobtitleAppointMapper.getList(entity);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		if (CollectionUtils.isNotEmpty(list)) {
			Map<String, String> acceptMethodMap = hrmsDictInfoService.convertDictMap(DictContants.CERTIFICATE_OBTAIN_TYPE); // 获取途径字典
			for (HrmsJobtitleAppoint jobtitleAppoint : list) {
				if(jobtitleAppoint.getAssessTime()!= null){
					jobtitleAppoint.setAssessTimeExport(sdf.format(jobtitleAppoint.getAssessTime()));
				}

				jobtitleAppoint.setAcceptMethodText(acceptMethodMap.get(jobtitleAppoint.getAcceptMethod())); // 获取途径
				jobtitleAppoint.setApprovalStatusText(IncidentAuditStatusEnum.getValByKey(jobtitleAppoint.getApprovalStatus()));//审批状态
			}
		}
		return list;
	}

	/**
	 * @throws Exception 
	 * @Title: incidentAudit
	 * @Description: 审核
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int incidentAudit(String id) throws Exception {
		if(!StringUtil.isEmpty(id)) {
			String[] split = id.split(",");
			for (int i = 0; i < split.length; i++) {
				String _id = split[i];
				HrmsJobtitleAppoint hrmsJobtitleAppoint = hrmsJobtitleAppointMapper.selectByPrimaryKey(_id);
				if (hrmsJobtitleAppoint != null) {
					hrmsJobtitleAppoint.setApprovalStatus(IncidentAuditStatusEnum.AUDIT_STATUS_4.getKey());
				}

				//更改员工状态
				String employeeId = hrmsJobtitleAppoint.getEmployeeId();
				HrmsEmployee employee = hrmsEmployeeService.findDetailById(employeeId);
				employee.setEmployeeStatus(EmployeeStatusEnum.EMPLOYEE_STATUS_1.getKey());
				hrmsEmployeeService.update(employee);
				
				//其他的职称信息都改为否
				if("1".equals(hrmsJobtitleAppoint.getHighestLevel())) {
					hrmsJobtitleInfoService.updateHighestLevel(employeeId);
					hrmsJobtitleAppointMapper.updateHighestLevel(employeeId);
				}
				
				//插入职称信息
				HrmsJobtitleInfo hrmsJobtitleInfo = new HrmsJobtitleInfo();
				hrmsJobtitleInfo.setId(String.valueOf(IdWork.id.nextId()));
				hrmsJobtitleInfo.setEmployeeId(hrmsJobtitleAppoint.getEmployeeId());
				hrmsJobtitleInfo.setJobtitleCategory(hrmsJobtitleAppoint.getJobtitleCategory());
				hrmsJobtitleInfo.setJobtitleLevel(hrmsJobtitleAppoint.getJobtitleLevel());
				hrmsJobtitleInfo.setJobtitleName(hrmsJobtitleAppoint.getJobtitleName());
				hrmsJobtitleInfo.setAcceptMethod(hrmsJobtitleAppoint.getAcceptMethod());
				hrmsJobtitleInfo.setCertificateNumber(hrmsJobtitleAppoint.getCertificateNumber());
				hrmsJobtitleInfo.setAssessmentDate(hrmsJobtitleAppoint.getAssessTime());
				hrmsJobtitleInfo.setAcquisitionDate(hrmsJobtitleAppoint.getAssessTime());
				hrmsJobtitleInfo.setAssessmentAgency(hrmsJobtitleAppoint.getMechanismName());
				hrmsJobtitleInfo.setHighestLevel(hrmsJobtitleAppoint.getHighestLevel());
				hrmsJobtitleInfo.setRemark(hrmsJobtitleAppoint.getRemark());
				hrmsJobtitleInfo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				hrmsJobtitleInfoService.insert(hrmsJobtitleInfo);
				hrmsJobtitleAppointMapper.updateByPrimaryKeySelective(hrmsJobtitleAppoint);
			}
		}
		return 1;
	}

	@Override
	@Transactional(readOnly = false)
	public void cancelIncidentAudit(String jobtitleAppointId) {
		if(!StringUtil.isEmpty(jobtitleAppointId)) {
			String[] split = jobtitleAppointId.split(",");
			for (int i = 0; i < split.length; i++) {
				String _id = split[i];
				HrmsJobtitleAppoint hrmsJobtitleAppoint = hrmsJobtitleAppointMapper.selectByPrimaryKey(_id);
				if (hrmsJobtitleAppoint != null) {
					hrmsJobtitleAppoint.setApprovalStatus(IncidentAuditStatusEnum.AUDIT_STATUS_1.getKey());
				}
				//更改员工状态
			/*	String employeeId = hrmsJobtitleAppoint.getEmployeeId();
				HrmsEmployee employee = hrmsEmployeeService.findDetailById(employeeId);
				employee.setEmployeeStatus(EmployeeStatusEnum.EMPLOYEE_STATUS_1.getKey());
				hrmsEmployeeService.update(employee);*/
				//删除职称信息
				hrmsJobtitleInfoService.deleteByApointBean(hrmsJobtitleAppoint);

				hrmsJobtitleAppointMapper.updateByPrimaryKeySelective(hrmsJobtitleAppoint);
			}
		}
		

	}
}

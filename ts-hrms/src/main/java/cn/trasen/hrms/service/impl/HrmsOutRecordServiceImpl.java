package cn.trasen.hrms.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.core.utils.DataBaseProvider;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.base.FileAttachmentResp;
import cn.trasen.homs.bean.document.Attachment;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.FileAttachmentFeignService;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.feign.oa.DocumentFeignClient;
import cn.trasen.hrms.dao.HrmsOutRecordGpMapper;
import cn.trasen.hrms.dao.HrmsOutRecordHyMapper;
import cn.trasen.hrms.dao.HrmsOutRecordMapper;
import cn.trasen.hrms.dao.HrmsOutRecordXxMapper;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsOutRecord;
import cn.trasen.hrms.model.HrmsOutRecordGp;
import cn.trasen.hrms.model.HrmsOutRecordHy;
import cn.trasen.hrms.model.HrmsOutRecordXx;
import cn.trasen.hrms.model.HrmsPersonnelTransaction;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.service.HrmsOutRecordService;
import cn.trasen.hrms.service.HrmsPersonnelTransactionService;

/**
 * @ClassName HrmsOutRecordServiceImpl
 * @Description TODO
 * @date 2021��8��27�� ����2:10:59
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsOutRecordServiceImpl implements HrmsOutRecordService {

	@Autowired
	private HrmsOutRecordMapper mapper;

	@Autowired
	private HrmsOutRecordGpMapper gpmapper;

	@Autowired
	private HrmsOutRecordHyMapper hymapper;
	
	@Autowired
	private HrmsOutRecordXxMapper xxmapper;
	
	@Autowired
	private DocumentFeignClient documentFeignClient;
	
	@Autowired
	private FileAttachmentFeignService fileAttachmentFeignService;
	
	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;
	
	@Autowired
	HrmsPersonnelTransactionService hrmsPersonnelTransactionService;
	
	@Autowired
	GlobalSettingsFeignService globalSettingsFeignService;
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsOutRecord record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		record.setIsDeleted("N");
		record.setStatus("1");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		if(StringUtils.isNotBlank(record.getFileId())) {
        	PlatformResult<List<Attachment>> attList = documentFeignClient.selectByIds(record.getFileId(), UserInfoHolder.getToken());
        	if(attList.isSuccess()) {
	        	List<FileAttachmentResp> fileAttachmentRespList = new ArrayList<>();
	        	for (Attachment attachmentReq : attList.getObject()) {
	        		FileAttachmentResp fileAttachmentResp = new FileAttachmentResp();
	        		fileAttachmentResp.setId(attachmentReq.getId());
	        		fileAttachmentResp.setOriginalName(attachmentReq.getOriginalName());
	        		fileAttachmentResp.setModuleName(attachmentReq.getModuleName());
	        		fileAttachmentResp.setFilePath(attachmentReq.getRealPath());
	        		fileAttachmentResp.setFileSize(Long.valueOf(attachmentReq.getFileSize()));
	        		fileAttachmentResp.setRealPath(attachmentReq.getFilePath());
	        		fileAttachmentResp.setFileExtension(attachmentReq.getFileExtension());
	        		fileAttachmentResp.setBusinessId(record.getId());
	        		fileAttachmentResp.setCreateDate(new Date());
	        		fileAttachmentResp.setUpdateUser(record.getApplyUser());
	        		fileAttachmentResp.setUpdateDate(new Date());
	        		fileAttachmentResp.setCreateUser(record.getApplyUser());
	        		fileAttachmentResp.setCreateUserName(record.getApplyUserName());
	        		fileAttachmentResp.setIsDeleted("N");
	        		fileAttachmentRespList.add(fileAttachmentResp);
				}
	        	fileAttachmentFeignService.saveAttachmentList(JSON.toJSONString(fileAttachmentRespList));
	        }
        }
		HrmsEmployee hrmsEmployee = hrmsEmployeeService.findByEmployeeNo(record.getApplyUser());
		if(StringUtils.isBlank(record.getApplyTime())) {
			record.setApplyTime(DateUtil.format(new Date(), "yyyy-MM-dd"));
		}
		record.setApplyUser(hrmsEmployee.getEmployeeNo());
	    record.setApplyUserName(hrmsEmployee.getEmployeeName());
	    record.setApplyDept(hrmsEmployee.getOrgId());
	    record.setApplyDeptName(hrmsEmployee.getOrgName());
	    record.setEmployeeId(hrmsEmployee.getEmployeeId());
	    
	    //进修 规培 添加异动记录
	    if("规培".equals(record.getOutType()) || "进修".equals(record.getOutType())) {
	    	String effectiveDate = record.getStartTime();
	    	if(StringUtils.isBlank(effectiveDate)){
				effectiveDate = DateUtil.format(new Date(), "yyyy-MM-dd");
			}
		    HrmsPersonnelTransaction hpt = new HrmsPersonnelTransaction(hrmsEmployee.getEmployeeNo(),
		    		hrmsEmployee.getEmployeeName(), hrmsEmployee.getEmployeeId(), hrmsEmployee.getOrgId(), hrmsEmployee.getOrgName(), null,
						null,effectiveDate , record.getOutType(), "是", hrmsPersonnelTransactionService.getBatchNumber(),
						null, null, null);
			hpt.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				hrmsPersonnelTransactionService.insert(hpt); // 添加人事事件
	    }

		if("进修".equals(record.getOutType())){
			return mapper.insertSelective(record);
		}else if("外出学习".equals(record.getOutType()) || "外出会议".equals(record.getOutType())|| "公务外出".equals(record.getOutType())){
			HrmsOutRecordHy hy = new HrmsOutRecordHy();
			BeanUtil.copyProperties(record,hy);
			return hymapper.insertSelective(hy);
		}else if("下乡".equals(record.getOutType())){
			HrmsOutRecordXx xx = new HrmsOutRecordXx();
			BeanUtil.copyProperties(record,xx);
			return xxmapper.insertSelective(xx);
		}else{
			HrmsOutRecordGp gp = new HrmsOutRecordGp();
			BeanUtil.copyProperties(record,gp);
			return gpmapper.insertSelective(gp);
		}
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsOutRecord record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		if(StringUtils.isNotBlank(record.getFileId())) {
        	PlatformResult<List<Attachment>> attList = documentFeignClient.selectByIds(record.getFileId(), UserInfoHolder.getToken());
        	if(attList.isSuccess()) {
	        	List<FileAttachmentResp> fileAttachmentRespList = new ArrayList<>();
	        	for (Attachment attachmentReq : attList.getObject()) {
	        		FileAttachmentResp fileAttachmentResp = new FileAttachmentResp();
	        		fileAttachmentResp.setId(attachmentReq.getId());
	        		fileAttachmentResp.setOriginalName(attachmentReq.getOriginalName());
	        		fileAttachmentResp.setModuleName(attachmentReq.getModuleName());
	        		fileAttachmentResp.setFilePath(attachmentReq.getFilePath());
	        		fileAttachmentResp.setFileSize(Long.valueOf(attachmentReq.getFileSize()));
	        		fileAttachmentResp.setRealPath(attachmentReq.getRealPath());
	        		fileAttachmentResp.setFileExtension(attachmentReq.getFileExtension());
	        		fileAttachmentResp.setBusinessId(record.getId());
	        		fileAttachmentResp.setCreateDate(new Date());
	        		fileAttachmentResp.setUpdateUser(record.getApplyUser());
	        		fileAttachmentResp.setUpdateDate(new Date());
	        		fileAttachmentResp.setCreateUser(record.getApplyUser());
	        		fileAttachmentResp.setCreateUserName(record.getApplyUserName());
	        		fileAttachmentResp.setIsDeleted("N");
	        		fileAttachmentRespList.add(fileAttachmentResp);
				}
	        	fileAttachmentFeignService.saveAttachmentList(JSON.toJSONString(fileAttachmentRespList));
	        }
        }
		
		HrmsEmployee hrmsEmployee = hrmsEmployeeService.findByEmployeeNo(record.getApplyUser());
		record.setApplyDept(hrmsEmployee.getOrgId());
		record.setApplyDeptName(hrmsEmployee.getOrgName());
	    record.setEmployeeId(hrmsEmployee.getEmployeeId());

		if("进修".equals(record.getOutType())){
			return mapper.updateByPrimaryKeySelective(record);
		}else if("外出学习".equals(record.getOutType()) || "外出会议".equals(record.getOutType())|| "公务外出".equals(record.getOutType())){
			HrmsOutRecordHy hy = new HrmsOutRecordHy();
			BeanUtil.copyProperties(record,hy);
			return hymapper.updateByPrimaryKeySelective(hy);
		}else{
			HrmsOutRecordGp gp = new HrmsOutRecordGp();
			BeanUtil.copyProperties(record,gp);
			return gpmapper.updateByPrimaryKeySelective(gp);
		}
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsOutRecord record = new HrmsOutRecord();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//三个表都删除
		HrmsOutRecordHy hy = new HrmsOutRecordHy();
		BeanUtil.copyProperties(record,hy);

		HrmsOutRecordGp gp = new HrmsOutRecordGp();
		BeanUtil.copyProperties(record,gp);
		hymapper.updateByPrimaryKeySelective(hy);
		gpmapper.updateByPrimaryKeySelective(gp);
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsOutRecord selectById(String id) {
		HrmsOutRecord ret = new HrmsOutRecord();
		Assert.hasText(id, "ID不能为空.");
		//一个一个找
		HrmsOutRecordHy hrmsOutRecordHy = hymapper.selectByPrimaryKey(id);
		HrmsOutRecordGp hrmsOutRecordgp = gpmapper.selectByPrimaryKey(id);
		HrmsOutRecordXx hrmsOutRecordXx = xxmapper.selectByPrimaryKey(id);
		if(hrmsOutRecordHy != null){
			BeanUtil.copyProperties(hrmsOutRecordHy,ret);
			return ret;
		}else if(hrmsOutRecordgp != null ){
			BeanUtil.copyProperties(hrmsOutRecordgp,ret);
			return ret;
		}else if(hrmsOutRecordXx != null ){
			BeanUtil.copyProperties(hrmsOutRecordXx,ret);
			return ret;
		}else{
			return mapper.selectByPrimaryKey(id);
		}
	}

	@Override
	public List<HrmsOutRecord> getDataSetList(Page page, HrmsOutRecord record) {
		
		if(StringUtils.isBlank(record.getOutType())){
			record.setOutType("进修");
		}
		
		PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getGlobalSetting("Y");
		
		String orgCode = globalSetting.getObject().getOrgCode();
		
		if("csjkyy".equals(orgCode)){
			
			//经开医院 科教科专员权限（护理类、医疗医技类、药剂类）
			Boolean right = UserInfoHolder.getRight("KJKZY");
			if(!"admin".equals(UserInfoHolder.getCurrentUserCode()) && right){
				record.setRoleCode("Y");
			}
		}
		if(StringUtils.isNotBlank(page.getSidx())){
            if("out_days".equals(page.getSidx())){
		         if(DataBaseProvider.databaseId.contains("mysql")){
                    page.setSidx("CAST(out_days AS SIGNED)");
                }else{
                    page.setSidx("CAST(out_days AS INT)");
                }
            }
        }
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		if("进修".equals(record.getOutType())){
			List<HrmsOutRecord> records = mapper.getDataSetList(page, record);
			return records;
		}else if("规培".equals(record.getOutType())){
			List<HrmsOutRecord> records = gpmapper.getDataSetList(page, record);
			return records;
		}else if("下乡".equals(record.getOutType())){
			List<HrmsOutRecord> records = xxmapper.getDataSetList(page, record);
			return records;
		}else{
			List<HrmsOutRecord> records = hymapper.getDataSetList(page, record);
			return records;
		}

	}

	@Override
	public List<Map<String, Object>> getOutStudyAndMeeting(Page page,HrmsOutRecordHy record) {
		//根据当前登录账号机构编码过滤查询数据
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		// TODO Auto-generated method stub
		return hymapper.getOutStudyAndMeeting(page,record);
	}

	@Override
	public Map<String, Object> getOutStudyAndMeetingCount(HrmsOutRecordHy record) {
		//根据当前登录账号机构编码过滤查询数据
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		// TODO Auto-generated method stub
		return hymapper.getOutStudyAndMeetingCount(record);
	}
}

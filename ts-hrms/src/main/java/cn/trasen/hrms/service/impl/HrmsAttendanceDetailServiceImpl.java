package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsAttendanceDateilMapper;
import cn.trasen.hrms.dao.HrmsAttendanceRecordMapper;
import cn.trasen.hrms.dao.HrmsEmployeeMapper;
import cn.trasen.hrms.model.HrmsAttendanceDetail;
import cn.trasen.hrms.model.HrmsAttendanceRecord;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.service.HrmsAttendanceDetailService;
import cn.trasen.hrms.service.HrmsAttendanceRecordService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import tk.mybatis.mapper.entity.Example;


/**    
  * <P> @Description: 考勤明细</p>
  * <P> @Date: 2020年7月9日  下午5:26:57 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
    
@Service
public class HrmsAttendanceDetailServiceImpl implements HrmsAttendanceDetailService {

	@Autowired
	private HrmsAttendanceDateilMapper hrmsAttendanceDateilMapper;
	
	@Autowired
	private HrmsAttendanceRecordService hrmsAttendanceRecordService;
	
	@Autowired
	HrmsAttendanceRecordMapper hrmsAttendanceRecordMapper;
	
	@Autowired
	HrmsEmployeeService hrmsEmployeeService;


	/**   
	 * <p>Title: insert</p>   
	 * <p>Description: 添加考勤</p>   
	 * @param entity
	 * @return   
	 */ 
	@Override
	public int insert(HrmsAttendanceDetail entity) {
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsAttendanceDateilMapper.insert(entity);
	}


	@Override
	public int batchInsert(List<HrmsAttendanceDetail> listDetail) {
		return hrmsAttendanceDateilMapper.batchInsert(listDetail);
	}


	@Override
	public int batchDelete(Map<String, Object> map) {
		return hrmsAttendanceDateilMapper.batchDelete(map);	
	}


	@Override
	public List<HrmsAttendanceDetail> getTimecardDetail(HrmsAttendanceDetail entity) {
//		Example example = new Example(HrmsAttendanceDetail.class);
//		example.createCriteria().andEqualTo("employeeId",entity.getEmployeeId());
//		example.and().andLike("attendanceDate", "%"+entity.getAttendanceDate()+"%");
//		example.orderBy("attendanceDate");
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsAttendanceDateilMapper.getTimecardDetail(entity);
	}


	/**   
	 * <p>Title: batchDetailInsert</p>   
	 * <p>Description:保存考勤明细 </p>   
	 * @param list   
	 */ 
	@Override
	@Transactional
	public void batchDetailInsert(List<HrmsAttendanceDetail> list) {
		
		String month = list.get(0).getAttendanceDate().substring(0,7); //获取年月
		String empId = list.get(0).getEmployeeId();
		if (empId.equals("255528341720641539")) {
			System.out.println("123213");
		}
		
		HrmsAttendanceRecord arBean = findRecordByParam(empId,month);  //查询主表数据
		String  attendanceRecordId = "";
		if(arBean != null) {  
			attendanceRecordId = arBean.getAttendanceRecordId();
		}else {
			
			HrmsEmployee empBean = hrmsEmployeeService.findDetailById(empId);
			
			attendanceRecordId = String.valueOf(IdWork.id.nextId());
			HrmsAttendanceRecord entity = new HrmsAttendanceRecord();
			entity.setAttendanceRecordId(attendanceRecordId);
			entity.setAttendanceDate(month);
			entity.setApprovalStatus("0");
			entity.setEmployeeId(empId);
			entity.setBelongOrg(empBean.getOrgId());
			entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			hrmsAttendanceRecordService.insert(entity);
		}
		Example example = new Example(HrmsAttendanceDetail.class);
		example.createCriteria().andEqualTo("employeeId",empId);
		example.and().andLike("attendanceDate", "%"+month+"%");
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		hrmsAttendanceDateilMapper.deleteByExample(example);
		
		for(int i = 0; i<list.size();i++) {
			list.get(i).setAttendanceDetailId(String.valueOf(IdWork.id.nextId()));
			list.get(i).setAttendanceRecordId(attendanceRecordId);
			list.get(i).setCreateUser(UserInfoHolder.getCurrentUserCode());
			list.get(i).setCreateUserName(UserInfoHolder.getCurrentUserName());
			list.get(i).setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			list.get(i).setCreateDate(new Date());
		}
		hrmsAttendanceDateilMapper.batchInsert(list);
		//状态改为未提交
		if(arBean!= null) {
			arBean.setApprovalStatus("0");
			hrmsAttendanceRecordMapper.updateByPrimaryKeySelective(arBean);
		}
	}
	
	private HrmsAttendanceRecord findRecordByParam(String empId,String date) {
		Example example = new Example(HrmsAttendanceRecord.class);
		example.createCriteria().andEqualTo("employeeId",empId);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		example.and().andLike("attendanceDate", "%"+date+"%");
		return 	hrmsAttendanceRecordMapper.selectOneByExample(example);
		
	}


	@Override
	public List<HrmsAttendanceDetail> getTimecardDetailByType(HrmsAttendanceDetail entity) {
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsAttendanceDateilMapper.getTimecardDetailByType(entity);
	}


	@Override
	public int batchDeleteByDateAndEmpIds(Map<String, Object> map) {
		return hrmsAttendanceDateilMapper.batchDeleteByDateAndEmpIds(map);
		
	}

}

package cn.trasen.hrms.service.impl;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsSalaryAuditMapper;
import cn.trasen.hrms.model.HrmsSalaryAudit;
import cn.trasen.hrms.service.HrmsSalaryAuditService;
import tk.mybatis.mapper.entity.Example;

@Service
public class HrmsSalaryAuditServiceImpl implements HrmsSalaryAuditService {
	
	@Autowired
	HrmsSalaryAuditMapper hrmsSalaryAuditMapper;

	@Override
	public int insert(HrmsSalaryAudit entity) {
		
		Example example = new Example(HrmsSalaryAudit.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("auditDate", entity.getAuditDate());
//		example.and().andEqualTo("salaryPlanId", entity.getSalaryPlanId());
		HrmsSalaryAudit bean = hrmsSalaryAuditMapper.selectOneByExample(example);
		if(bean != null) {
			throw  new RuntimeException("无需重复审核");
		}
		
		entity.setSalaryAuditId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsSalaryAuditMapper.insertSelective(entity);
	}

	@Override
	public int update(HrmsSalaryAudit entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsSalaryAuditMapper.updateByPrimaryKeySelective(entity);
	}

	@Override
	public int deleted(HrmsSalaryAudit entity) {
		Example example = new Example(HrmsSalaryAudit.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("auditDate", entity.getAuditDate());
//		example.and().andEqualTo("salaryPlanId", entity.getSalaryPlanId());
		return hrmsSalaryAuditMapper.deleteByExample(example);
	}

	@Override
	public boolean getSalaryAuditByDateAndPlan(HrmsSalaryAudit entity) {
		Example example = new Example(HrmsSalaryAudit.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("auditDate", entity.getAuditDate());
//		example.and().andEqualTo("salaryPlanId", entity.getSalaryPlanId());
		HrmsSalaryAudit bean = hrmsSalaryAuditMapper.selectOneByExample(example);
		return (bean != null);
	}

}

package cn.trasen.hrms.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.model.HrNotice;

/**
 * @ClassName: HrNoticeService  
 * @Description: 通知公告Service
 * <AUTHOR>
 * @date 2018年8月17日 下午6:19:50
 */
public interface HrNoticeService {
	/**
	 * 查询通知公告列表
	 *
	 * @return
	 */
	List<HrNotice> getDataSetList(Page page, HrNotice record);

	/**
	 * @Title: findById  
	 * @Description: 根据ID查询通知公告列表
	 * @param @param noticeId
	 * @param @return    参数  
	 * @return HrNotice    返回类型  
	 * @throws
	 */
	HrNotice findById(String noticeId);
	
	/**
	 * 修改通知公告
	 * @param record
	 * @return
	 */
	int update(HrNotice record);

	/**
	 * 新增通知公告
	 * @param record
	 * @return
	 */
	int insert(HrNotice record);

	/**
	 * 根据ID删除通知公告
	 *
	 * @param id
	 * @return
	 */
	int deleteById(String noticeId);
	
	/**
	 * 
	* @Title: 根据当前登录人获取未读公告  
	* @Description: TODO
	* @Params: @return      
	* @Return: HrNotice
	* <AUTHOR>
	* @date:2020年7月22日
	* @Throws
	 */
	HrNotice getUnreadHrNoticeInfo();

}

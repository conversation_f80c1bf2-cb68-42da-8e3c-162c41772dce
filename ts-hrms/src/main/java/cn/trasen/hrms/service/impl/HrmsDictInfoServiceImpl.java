package cn.trasen.hrms.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.google.common.collect.Maps;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.model.HrmsDictInfo;
import cn.trasen.hrms.service.HrmsDictInfoService;

/**   
 * @Title: HrmsDictInfoServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 字典表 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月23日 下午4:12:35 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsDictInfoServiceImpl implements HrmsDictInfoService {

	@Autowired
	DictItemFeignService dictItemService;

//	@Autowired
//	HrmsDictInfoMapper hrmsDictInfoMapper;



//	/**
//	 * @Title: insert
//	 * @Description: 新增字典
//	 * @Param: entity
//	 * @Return: int
//	 * <AUTHOR>
//	 */
//	@Override
//	@Transactional(readOnly = false)
//	public int insert(HrmsDictInfo entity) {
//		entity.setDictInfoId(String.valueOf(IdWork.id.nextId()));
//		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
//		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
//		entity.setCreateDate(new Date());
//		return hrmsDictInfoMapper.insert(entity);
//	}

//	/**
//	 * @Title: update
//	 * @Description: 修改字典
//	 * @Param:
//	 * @Return: int
//	 * <AUTHOR>
//	 */
//	@Override
//	@Transactional(readOnly = false)
//	public int update(HrmsDictInfo entity) {
//		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
//		entity.setUpdateDate(new Date());
//		return hrmsDictInfoMapper.updateByPrimaryKeySelective(entity);
//	}

//	/**
//	 *
//	 * @Title: deleteById
//	 * @Description: 删除字典
//	 * @Param: id
//	 * @Return: int
//	 * <AUTHOR>
//	 */
//	@Override
//	@Transactional(readOnly = false)
//	public int deleted(String id) {
//		HrmsDictInfo dictInfo = hrmsDictInfoMapper.selectByPrimaryKey(id);
//		if (dictInfo != null) {
//			dictInfo.setIsDeleted(Contants.IS_DELETED_TURE);
//		}
//		return hrmsDictInfoMapper.updateByPrimaryKeySelective(dictInfo);
//	}

//	/**
//	 * @Title: getDataList
//	 * @Description: 获取数据字典列表
//	 * @Param: page
//	 * @param entity
//	 * @Return: List<HrmsDictInfo>
//	 * <AUTHOR>
//	 */
//	@Override
//	public List<HrmsDictInfo> getDataList(Page page, HrmsDictInfo entity) {
//		Example example = new Example(HrmsDictInfo.class);
//		Example.Criteria criteria = example.createCriteria();
//		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
//
//		if (StringUtils.isNotBlank(entity.getDictType())) { // 字典类型
//			criteria.andLike("dictType", "%" + entity.getDictType() + "%");
//		}
//		if (StringUtils.isNotBlank(entity.getDictName())) { // 字典名称
//			criteria.andLike("dictName", "%" + entity.getDictName() + "%");
//		}
//		return hrmsDictInfoMapper.selectByExampleAndRowBounds(example, page);
//	}

	/**
	 * @Title: getDictInfoListByDictType
	 * @Description: 根据字典类型获取数据字典
	 * @Param: dictType 字典类型
	 * @Return: List<HrmsDictInfo>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsDictInfo> getDictInfoListByDictType(String dictType) {
//		Assert.notNull(dictType, "dictType must not be null.");
//
//		List<HrmsDictInfo> list = DictCacheManager.getByKey(dictType);
//		if (CollectionUtils.isEmpty(list)) {
//			list = hrmsDictInfoMapper.getDictInfoListByDictType(dictType);
//			DictCacheManager.set(dictType, list);
//		}

		List<DictItemResp> dictItems=dictItemService.getDictItemByTypeCode(dictType).getObject();
		List<HrmsDictInfo> hrmsDictInfos=new ArrayList<>();

		for (DictItemResp dictItem:dictItems) {
			HrmsDictInfo hrmsDictInfo = new HrmsDictInfo();
			hrmsDictInfo.setDictInfoId(dictItem.getId());
			hrmsDictInfo.setDictName(dictItem.getItemName());
			hrmsDictInfo.setDictType(dictType);
			hrmsDictInfo.setDictValue(dictItem.getItemNameValue());
			hrmsDictInfo.setRemark(dictItem.getRemark());
			hrmsDictInfos.add(hrmsDictInfo);
		}
		return hrmsDictInfos;
	}

	/**
	 * @Title: getDictByTypeAndValue
	 * @Description: 根据类型和值查询字典
	 * @Param: type
	 * @param value
	 * @Return: HrmsDictInfo
	 * <AUTHOR>
	 */
//	@Override
//	public HrmsDictInfo getDictByTypeAndValue(String type, Object value) {
//		Example example = new Example(HrmsDictInfo.class);
//		example.createCriteria().andEqualTo("dictType", type).andNotEqualTo("dictValue", value);
//		return hrmsDictInfoMapper.selectOneByExample(example);
//	}

	/**  
	 * @Title: convertDictMap
	 * @Description: 查询字典列表并转换成Map形式
	 * @Param: dictType 数据字典类型
	 * <AUTHOR>
	 * @date 2020年3月26日 下午3:18:24 
	 */
	@Override
	public Map<String, String> convertDictMap(String dictType) {
		Assert.notNull(dictType, "dictType must not be null.");
		Map<String, String> map = Maps.newHashMap();
		List<HrmsDictInfo> list= getDictInfoListByDictType( dictType);
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsDictInfo d : list) {
				map.put(d.getDictValue(), d.getDictName());
			}
		}
		return map;
	}

	/**
	 * @Title: convertDictValueKeyMap
	 * @Description: 查询字典列表并转换成Map形式(value-key)
	 * @param dictType
	 * @Return Map<String,String>
	 * <AUTHOR>
	 * @date 2020年6月8日 下午3:08:22
	 */
	@Override
	public Map<String, String> convertDictValueKeyMap(String dictType) {
		Assert.notNull(dictType, "dictType must not be null.");

		Map<String, String> map = Maps.newHashMap();
		List<HrmsDictInfo> list= getDictInfoListByDictType( dictType);
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsDictInfo d : list) {
				map.put(d.getDictName(), d.getDictValue());
			}
		}
		return map;
	}

//	/**
//	 * @Title: excelImportDictInfo
//	 * @Description: excel导入数据字典
//	 * @param list
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年4月14日 下午1:51:22
//	 */
//	@Override
//	@Transactional(readOnly = false)
//	public PlatformResult<String> excelImportDictInfo(List<HrmsDictInfo> list) {
//		if (CollectionUtils.isEmpty(list)) {
//			return PlatformResult.failure("导入内容为空");
//		}
//		if (list.stream().anyMatch(item -> StringUtils.isBlank(item.getDictType()))) {
//			return PlatformResult.failure("导入失败，字典类型不能为空");
//		}
//		Map<String, Map<String, Long>> maps = list.stream().collect(Collectors.groupingBy(HrmsDictInfo::getDictType, Collectors.groupingBy(HrmsDictInfo::getDictValue, Collectors.counting())));
//		if (maps != null && maps.size() > 0) {
//			for (Map.Entry<String, Map<String, Long>> entry : maps.entrySet()) {
//				Map<String, Long> childMap = entry.getValue();
//				for (Map.Entry<String, Long> childEntry : childMap.entrySet()) {
//					if (childEntry.getValue().intValue() > 1) {
//						return PlatformResult.failure("导入失败，字典类型【" + entry.getKey() + "】存在相同字典值");
//					}
//				}
//			}
//		}
//		for (HrmsDictInfo dict : list) {
//			dict.setDictInfoId(String.valueOf(IdWork.id.nextId()));
//			dict.setIsDeleted(Contants.IS_DELETED_FALSE);
//			dict.setCreateUser(UserInfoHolder.getCurrentUserCode());
//			dict.setCreateDate(new Date());
//		}
//		hrmsDictInfoMapper.batchInsert(list);
//		return PlatformResult.success();
//	}

//	/**
//	 * @Title: initCache
//	 * @Description: 初始化缓存
//	 * @Param:
//	 * <AUTHOR>
//	 * @date 2020年4月16日 下午1:53:59
//	 */
//	@Override
//	public void initCache() {
//		HrmsDictInfo record = new HrmsDictInfo();
//		record.setIsDeleted(Contants.IS_DELETED_FALSE);
//		List<HrmsDictInfo> list = hrmsDictInfoMapper.select(record);
//		DictCacheManager.init(list);
//	}

//	/**
//	 * @Title: validate
//	 * @Description: 数据校验
//	 * @param entity
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年5月26日 下午2:26:10
//	 */
//	@Override
//	public PlatformResult<String> validate(HrmsDictInfo entity) {
//		Assert.notNull(entity.getDictType(), "dictType must not be null.");
//		Assert.notNull(entity.getDictName(), "dictName must not be null.");
//		Assert.notNull(entity.getDictValue(), "dictValue must not be null.");
//
//		PlatformResult<String> result = null;
//		// 校验字典名称
//		result = validDictName(entity);
//		if (!result.isSuccess()) {
//			return result;
//		}
//		// 校验字典值
//		result = validDictValue(entity);
//		if (!result.isSuccess()) {
//			return result;
//		}
//		return PlatformResult.success();
//	}
//
//	/**
//	 * @Title: validName
//	 * @Description: 校验名称是否重复
//	 * @param entity
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年5月26日 下午2:40:07
//	 */
//	private PlatformResult<String> validDictName(HrmsDictInfo entity) {
//		Example example = new Example(HrmsDictInfo.class);
//		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
//
//		// 校验名称是否重复
//		example.and().andEqualTo("dictType", entity.getDictType());
//		example.and().andEqualTo("dictName", entity.getDictName());
//		if (StringUtils.isNotBlank(entity.getDictInfoId())) {
//			example.and().andNotEqualTo("dictInfoId", entity.getDictInfoId());
//		}
//		if (hrmsDictInfoMapper.selectByExample(example).size() > 0) {
//			return PlatformResult.failure(entity.getDictType() + "已存在字典名称: " + entity.getDictName());
//		}
//		return PlatformResult.success();
//	}

//	/**
//	 * @Title: validDictValue
//	 * @Description: 校验字典值是否重复
//	 * @param entity
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年5月26日 下午2:41:02
//	 */
//	private PlatformResult<String> validDictValue(HrmsDictInfo entity) {
//		Example example = new Example(HrmsDictInfo.class);
//		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
//
//		// 校验名称是否重复
//		example.and().andEqualTo("dictType", entity.getDictType());
//		example.and().andEqualTo("dictValue", entity.getDictValue());
//		if (StringUtils.isNotBlank(entity.getDictInfoId())) {
//			example.and().andNotEqualTo("dictInfoId", entity.getDictInfoId());
//		}
//		if (hrmsDictInfoMapper.selectByExample(example).size() > 0) {
//			return PlatformResult.failure(entity.getDictType() + "已存在字典值: " + entity.getDictValue());
//		}
//		return PlatformResult.success();
//	}

}

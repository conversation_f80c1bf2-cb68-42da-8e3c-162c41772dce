package cn.trasen.hrms.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.dao.HrmsLifecycleMapper;
import cn.trasen.hrms.model.HrmsContract;
import cn.trasen.hrms.model.HrmsLifecycle;
import cn.trasen.hrms.service.HrmsLifecycleService;

/**   
 * @ClassName:  HrmsLifecycleServiceImpl   
 * @Description:生命周期实现类 
 * @author: WZH
 * @date:   2021年11月13日 上午11:35:04      
 * @Copyright:  
 */
@Service
public class HrmsLifecycleServiceImpl implements HrmsLifecycleService {

	@Autowired
	HrmsLifecycleMapper hrmsLifecycleMapper;
	
	@Override
	public int batchinsert(List<HrmsLifecycle> list) {
		int count = 0;
		if (list.size() > 500) {
			int toIndex = 500;
			int listSize = list.size();
			for (int i = 0; i < list.size(); i += 500) {
				// 作用为toIndex最后没有500条数据则剩余几条list中就装几条
				if (i + 500 > listSize) {
					toIndex = listSize - i;
				}
				List<HrmsLifecycle> sepaList = list.subList(i, i + toIndex);
				count += hrmsLifecycleMapper.batchInsert(sepaList);
			}
		} else {
			count = hrmsLifecycleMapper.batchInsert(list);
		}
		return count;
	}
	
	
	@Override
	public int insert(HrmsLifecycle entity) {
		
		return 0;
	}

	@Override
	public int delete(HrmsLifecycle entity) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public List<HrmsContract> getDataAllList(HrmsLifecycle entity) {
		// TODO Auto-generated method stub
		return hrmsLifecycleMapper.getDataAllList(entity);
	}

	@Override
	public List<HrmsContract> getDataList(Page page, HrmsLifecycle entity) {
		// TODO Auto-generated method stub
		return null;
	}



}

package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: LaborContractStatusEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 劳动合同状态枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月9日 上午12:35:28 
 * @version V1.0   
 */
@Getter
public enum LaborContractStatusEnum {
	
	/**
	 * 未签
	 */
	LABOR_CONTRACT_TYPE_0("0", "未签"),
	/**
	 * 已签
	 */
	LABOR_CONTRACT_TYPE_1("1", "已签"),
	/**
	 * 已过期
	 */
	LABOR_CONTRACT_TYPE_2("2", "已过期"),
	/**
	 * 已续签
	 */
	LABOR_CONTRACT_TYPE_3("3", "已续签"),
	/**
	 * 已解除
	 */
	LABOR_CONTRACT_TYPE_4("4", "已解除");

	private final String key;
	private final String val;

	private LaborContractStatusEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (LaborContractStatusEnum item : LaborContractStatusEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return "";
	}
}

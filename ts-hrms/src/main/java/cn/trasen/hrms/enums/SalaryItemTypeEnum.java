package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: SalaryItemTypeEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 薪酬项目类型枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月26日 下午5:08:03 
 * @version V1.0   
 */
@Getter
public enum SalaryItemTypeEnum {

	/**
	 * 系统
	 */
	ITEM_TYPE_1("1", "系统"),
	/**
	 * 计算
	 */
	ITEM_TYPE_2("2", "计算"),
	/**
	 * 手工录入
	 */
	ITEM_TYPE_3("3", "手工录入"),
	/**
	 * 固定金额
	 */
	ITEM_TYPE_4("4", "固定金额"),
	/**
	 * 引用上月
	 */
	ITEM_TYPE_5("5", "手工录入(引用上月)"),
	;

	private final String key;
	private final String val;

	private SalaryItemTypeEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (SalaryItemTypeEnum item : SalaryItemTypeEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return "";
	}

}

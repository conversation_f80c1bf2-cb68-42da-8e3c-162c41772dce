package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: DateFormatEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 日期格式化枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月9日 上午9:55:59 
 * @version V1.0   
 */
@Getter
public enum DateFormatEnum {

	/**
	 * MM-dd
	 */
	MM_DD("MM-dd"),
	/**
	 * yyyy-MM-dd
	 */
	YYYY_MM_DD("yyyy-MM-dd"),
	/**
	 * yyyy-MM-dd 00:00:00
	 */
	YYYY_MM_DD_BEGIN("yyyy-MM-dd 00:00:00"),
	/**
	 * yyyy-MM-dd 23:59:59
	 */
	YYYY_MM_DD_END("yyyy-MM-dd 23:59:59"),
	/**
	 * yyyyMMdd
	 */
	YYYYMMDD("yyyyMMdd"),
	/**
	 * yyyy-MM-dd HH:mm:ss
	 */
	YYYY_MM_DD_HH_mm_ss("yyyy-MM-dd HH:mm:ss"),
	/**
	 * yyyyMMddHHmmssSSS
	 */
	YYYYMMddHHmmssSSS("yyyyMMddHHmmssSSS"),
	/**
	 * yyyyMMddHHmmss
	 */
	YYYYMMddHHmmss("yyyyMMddHHmmss"),
	/**
	 * MMMdHHmmss
	 */
	MMMdHHmmss("MMMdHHmmss");

	private String value;

	DateFormatEnum(String value) {
		this.value = value;
	}

}

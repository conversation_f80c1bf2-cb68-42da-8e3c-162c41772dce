package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: PriorityTypeEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 调动审批状态类型 枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月25日 下午4:03:41
 * @version V1.0   
 */
@Getter
public enum IncidentAuditStatusEnum {

	/**
	 * 未审批
	 */
	AUDIT_STATUS_1("1", "未审批"),
	/**
	 * 审批中
	 */
	AUDIT_STATUS_2("2", "审批中"),
	/**
	 * 已退回
	 */
	AUDIT_STATUS_3("3", "已退回"),
	/**
	 * 已审批
	 */
	AUDIT_STATUS_4("4", "已审批");

	private final String key;
	private final String val;

	private IncidentAuditStatusEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (IncidentAuditStatusEnum conf : IncidentAuditStatusEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}
}

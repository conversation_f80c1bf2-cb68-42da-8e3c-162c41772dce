package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: PriorityTypeEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 离职类型 枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月25日 下午4:03:41
 * @version V1.0   
 */
@Getter
public enum IncidentResignationTypeEnum {

	/**
	 * 辞职
	 */
	RESIGNATION_TYPE_1("1", "辞职"),
	/**
	 * 辞退
	 */
	RESIGNATION_TYPE_2("2", "辞退"),
	/**
	 * 劝退
	 */
	RESIGNATION_TYPE_3("3", "劝退"),
	/**
	 * 调离
	 */
	RESIGNATION_TYPE_4("4", "调离"),
	/**
	 * 停薪留职
	 */
	RESIGNATION_TYPE_5("5", "停薪留职"),
	/**
	 * 其他
	 */
	RESIGNATION_TYPE_6("6", "其他");

	private final String key;
	private final String val;

	private IncidentResignationTypeEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (IncidentResignationTypeEnum conf : IncidentResignationTypeEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}
}

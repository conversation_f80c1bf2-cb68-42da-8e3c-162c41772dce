package cn.trasen.hrms.enums;

import lombok.Getter;


/**
 * 合同操作类型字典枚举类
 * <AUTHOR>
 *
 */
@Getter
public enum  ContractOperationStatusEnum {
	
	OPERATION_STATUS_1("1", "签订"),

	OPERATION_STATUS_2("2", "编辑"),
	
	OPERATION_STATUS_3("3", "续签"),
	
	OPERATION_STATUS_4("4", "解除"),

	OPERATION_STATUS_5("5", "删除");

	private final String key;
	private final String val;

	private ContractOperationStatusEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (ContractOperationStatusEnum conf : ContractOperationStatusEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}
}

package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: PriorityTypeEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 调动类型 枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月25日 下午4:03:41
 * @version V1.0   
 */
@Getter
public enum TransferChangeTypeEnum {

	/**
	 * 调动
	 */
	PRIORITY_TYPE_1("1", "调动"),
	/**
	 * 升迁
	 */
	PRIORITY_TYPE_2("2", "升迁"),
	/**
	 * 降级
	 */
	PRIORITY_TYPE_3("3", "降级");

	private final String key;
	private final String val;

	private TransferChangeTypeEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (TransferChangeTypeEnum conf : TransferChangeTypeEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}
}

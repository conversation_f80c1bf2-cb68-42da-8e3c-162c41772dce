package cn.trasen.hrms.enums;

import org.apache.shiro.crypto.hash.format.Shiro1CryptFormat;

import lombok.Getter;


/**    
  * <P> @Description: 是否的枚举类</p>
  * <P> @Date: 2021年2月22日  上午9:46:38 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
    
@Getter
public enum WhetherEnum {
	

	WHETHER_ENUM_1("1", "是"),

	WHETHER_ENUM_2("2", "否");

	private final String key;
	private final String val;

	private WhetherEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (WhetherEnum item : WhetherEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return "";
	}

}

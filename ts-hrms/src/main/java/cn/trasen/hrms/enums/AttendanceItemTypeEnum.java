package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: AttendanceItemTypeEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 考勤项目类型枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月26日 下午4:46:59 
 * @version V1.0   
 */
@Getter
public enum AttendanceItemTypeEnum {
	
	/**
	 * 计算
	 */
	ITEM_TYPE_1("1", "计算"),
	/**
	 * 考勤天数
	 */
	ITEM_TYPE_2("2", "考勤天数");

	private final String key;
	private final String val;

	private AttendanceItemTypeEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (AttendanceItemTypeEnum item : AttendanceItemTypeEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return "";
	}

}

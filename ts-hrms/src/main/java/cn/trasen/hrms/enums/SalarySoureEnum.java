package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: HireStatusEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 薪酬来源枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2021年6月18日 下午3:15:38 
 * @version V1.0   
 */
@Getter
public enum SalarySoureEnum {

	CONTRACT_STATUS_1(0, "档案"),

	CONTRACT_STATUS_2(1, "晋升"),

	CONTRACT_STATUS_3(2, "薪酬");

	private final Integer key;
	private final String val;

	private SalarySoureEnum(Integer key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(Integer key) {
		for (SalarySoureEnum conf : SalarySoureEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}
	
	public static Integer getKeyByVal(String val) {
		for (SalarySoureEnum item : SalarySoureEnum.values()) {
			if (item.val.equals(val)) {
				return item.key;
			}
		}
		return 0;
	}
}

package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: SalaryCountTypeEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 薪酬计算类型枚举类 
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月26日 下午5:19:49 
 * @version V1.0   
 */
@Getter
public enum SalaryCountTypeEnum {

	/**
	 * 收入
	 */
	COUNT_TYPE_1("1", "收入"),
	/**
	 * 扣减
	 */
	COUNT_TYPE_2("2", "扣减");

	private final String key;
	private final String val;

	private SalaryCountTypeEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (SalaryCountTypeEnum item : SalaryCountTypeEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return "";
	}
}

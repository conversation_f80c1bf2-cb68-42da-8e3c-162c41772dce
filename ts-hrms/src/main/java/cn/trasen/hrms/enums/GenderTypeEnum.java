package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: GenderTypeEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 性别枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月10日 上午11:04:02 
 * @version V1.0   
 */
@Getter
public enum GenderTypeEnum {

	/**
	 * 男
	 */
	GENDER_TYPE_1("0", "男"),
	/**
	 * 女
	 */
	GENDER_TYPE_2("1", "女"),
	/**
	 * 未知
	 */
	GENDER_TYPE_3("3", "未知");

	private final String key;
	private final String val;

	private GenderTypeEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (GenderTypeEnum item : GenderTypeEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return "";
	}

	/**
	 * @Title: getKeyByVal
	 * @Description: 根据val获得key值
	 * @param val
	 * @Return String
	 * <AUTHOR>
	 * @date 2020年6月17日 下午5:30:51
	 */
	public static String getKeyByVal(String val) {
		for (GenderTypeEnum item : GenderTypeEnum.values()) {
			if (item.val.equals(val)) {
				return item.key;
			}
		}
		return "";
	}

}

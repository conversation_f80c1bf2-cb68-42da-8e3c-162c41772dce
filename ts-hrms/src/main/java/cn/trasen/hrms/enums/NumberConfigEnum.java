package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: NumberConfigEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 编号配置规则 枚举
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月22日 下午3:40:16 
 * @version V1.0   
 */
@Getter
public enum NumberConfigEnum {

	/**
	 * 员工工号配置
	 */
	EMPLOYEE_NO_CONF("1", "员工工号配置");

	private final String key;
	private final String val;

	private NumberConfigEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (NumberConfigEnum conf : NumberConfigEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}
}

package cn.trasen.hrms.enums;

import lombok.Getter;

/**
 * @ClassName: PlanStateEnum  
 * @Description: 计划状态
 * <AUTHOR>
 * @date 2020年5月27日 上午11:10:01
 */
@Getter
public enum PlanStateEnum {
	// 状态(0:草稿  1:待审批 2:审批通过 待开始  3:审批驳回 4:取消计划 5:已开始  6：已结束)
	
	PLAN_STATUS_DRAFT("0", "草稿"),
	
	PLAN_STATUS_PEND("1", "待审批"),
	
	PLAN_STATUS_PASS("2", "审批通过"),
	
	PLAN_STATUS_REJECT("3", "审批驳回"),
	
	PLAN_STATUS_CANCEL("4", "取消计划"),
	
	PLAN_STATUS_START("5", "已开始"),
	
	PLAN_STATUS_END("6", "已结束");

	private final String key;
	private final String val;

	private PlanStateEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (PlanStateEnum conf : PlanStateEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}
}

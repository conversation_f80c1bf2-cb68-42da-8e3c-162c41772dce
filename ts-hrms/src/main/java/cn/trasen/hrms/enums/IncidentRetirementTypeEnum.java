package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: PriorityTypeEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 退休类型 枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月25日 下午4:03:41
 * @version V1.0   
 */
@Getter
public enum IncidentRetirementTypeEnum {

	/**
	 * 正常退休
	 */
	RETIREMENT_TYPE_1("1", "正常退休"),
	/**
	 * 离休
	 */
	RETIREMENT_TYPE_2("2", "离休"),
	/**
	 * 病退
	 */
	RETIREMENT_TYPE_3("3", "病退"),
	/**
	 * 其他
	 */
	RETIREMENT_TYPE_4("4", "其他");

	private final String key;
	private final String val;

	private IncidentRetirementTypeEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (IncidentRetirementTypeEnum conf : IncidentRetirementTypeEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}
}

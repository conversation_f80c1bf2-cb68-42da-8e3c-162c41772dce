package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: HireStatusEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 录用状态 枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月22日 下午3:15:38 
 * @version V1.0   
 */
@Getter
public enum HireStatusEnum {
	
	/**
	 * 未审批
	 */
	HIRE_STATUS_1("1", "未审批"),
	/**
	 * 审批通过
	 */
	HIRE_STATUS_2("2", "审批通过"),
	/**
	 * 已发Offer
	 */
	HIRE_STATUS_3("3", "已发Offer"),
	/**
	 * 拒绝
	 */
	HIRE_STATUS_4("4", "拒绝");

	private final String key;
	private final String val;

	private HireStatusEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (HireStatusEnum conf : HireStatusEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}
}

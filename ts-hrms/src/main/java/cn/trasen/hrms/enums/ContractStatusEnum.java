package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: HireStatusEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 合同签订状态
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2021年6月18日 下午3:15:38 
 * @version V1.0   
 */
@Getter
public enum  ContractStatusEnum {
	
	CONTRACT_STATUS_1("1", "已签订"),

	CONTRACT_STATUS_2("2", "已续签"),
	
	CONTRACT_STATUS_3("3", "已到期"),
	
	CONTRACT_STATUS_4("4", "已解除"),

	CONTRACT_STATUS_5("5", "已删除");

	private final String key;
	private final String val;

	private ContractStatusEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (ContractStatusEnum conf : ContractStatusEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}
	
	public static String getKeyByVal(String val) {
		for (ContractStatusEnum item : ContractStatusEnum.values()) {
			if (item.val.equals(val)) {
				return item.key;
			}
		}
		return "";
	}
}

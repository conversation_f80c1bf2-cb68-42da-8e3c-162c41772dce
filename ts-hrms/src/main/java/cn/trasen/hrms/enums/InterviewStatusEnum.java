package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: InterviewStatusEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 面试状态 枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月22日 上午11:52:06 
 * @version V1.0   
 */
@Getter
public enum InterviewStatusEnum {

	/**
	 * 待面试
	 */
	INTERVIEW_STATUS_1("1", "待面试"),
	/**
	 * 爽约
	 */
	INTERVIEW_STATUS_2("2", "爽约"),
	/**
	 * 录用
	 */
	INTERVIEW_STATUS_3("3", "录用"),
	/**
	 * 不录用
	 */
	INTERVIEW_STATUS_4("4", "不录用"),
	/**
	 * 已面试
	 */
	INTERVIEW_STATUS_5("5", "已面试");

	private final String key;
	private final String val;

	private InterviewStatusEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (InterviewStatusEnum conf : InterviewStatusEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}

}

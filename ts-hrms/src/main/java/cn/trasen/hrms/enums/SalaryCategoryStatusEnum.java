package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: HireStatusEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 薪酬类别枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2021年6月18日 下午3:15:38 
 * @version V1.0   
 */
@Getter
public enum SalaryCategoryStatusEnum {

	CONTRACT_STATUS_1(1, "岗位类别"),

	CONTRACT_STATUS_2(2, "薪级类别"),

	CONTRACT_STATUS_3(3, "薪酬项目"),

	CONTRACT_STATUS_4(4, "员工状态");

	private final Integer key;
	private final String val;

	private SalaryCategoryStatusEnum(Integer key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (SalaryCategoryStatusEnum conf : SalaryCategoryStatusEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}
	
	public static Integer getKeyByVal(String val) {
		for (SalaryCategoryStatusEnum item : SalaryCategoryStatusEnum.values()) {
			if (item.val.equals(val)) {
				return item.key;
			}
		}
		return 0;
	}
}

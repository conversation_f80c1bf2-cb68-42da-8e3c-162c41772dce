package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: SalaryPayrollSendStatusEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 工资发放状态枚举
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月9日 上午9:49:02 
 * @version V1.0   
 */
@Getter
public enum SalaryPayrollSendStatusEnum {

	/**
	 * 未发送
	 */
	SEND_STATUS_1("1", "未发送"),
	/**
	 * 已发送
	 */
	SEND_STATUS_2("2", "已发送"),
	/**
	 * 撤回
	 */
	SEND_STATUS_3("3", "撤回");

	private final String key;
	private final String val;

	private SalaryPayrollSendStatusEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (SalaryPayrollSendStatusEnum item : SalaryPayrollSendStatusEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return "";
	}

}

package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: PriorityTypeEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 死亡类型 枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月25日 下午4:03:41
 * @version V1.0   
 */
@Getter
public enum IncidentDeathTypeEnum {

	/**
	 * 正常死亡
	 */
	DEATH_TYPE_1("1", "正常死亡"),
	/**
	 * 意外死亡
	 */
	DEATH_TYPE_2("2", "意外死亡"),
	/**
	 * 其他
	 */
	DEATH_TYPE_3("3", "其他");

	private final String key;
	private final String val;

	private IncidentDeathTypeEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (IncidentDeathTypeEnum conf : IncidentDeathTypeEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}
}

package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: PriorityTypeEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 优先级 枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月21日 下午4:03:41 
 * @version V1.0   
 */
@Getter
public enum PriorityTypeEnum {
	
	/**
	 * 特急的
	 */
	PRIORITY_TYPE_1("1", "特急的"),
	/**
	 * 紧急的
	 */
	PRIORITY_TYPE_2("2", "紧急的"),
	/**
	 * 特急的
	 */
	PRIORITY_TYPE_3("3", "一般的"),
	/**
	 * 轻微的
	 */
	PRIORITY_TYPE_4("4", "轻微的");

	private final String key;
	private final String val;

	private PriorityTypeEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: 
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (PriorityTypeEnum conf : PriorityTypeEnum.values()) {
			if (conf.key.equals(key)) {
				return conf.val;
			}
		}
		return "";
	}
}

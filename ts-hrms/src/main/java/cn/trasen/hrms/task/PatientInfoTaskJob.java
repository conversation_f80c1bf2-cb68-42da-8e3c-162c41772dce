package cn.trasen.hrms.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.hrms.med.patient.service.PatientBirthService;
import cn.trasen.hrms.med.patient.service.PatientCriticalValueService;
import cn.trasen.hrms.med.patient.service.PatientInfoService;
import cn.trasen.hrms.med.patient.service.PatientOperationService;
import cn.trasen.hrms.med.patient.service.PatientOrderrecordService;
import cn.trasen.hrms.med.patient.service.PatientTransferDeptService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class PatientInfoTaskJob {

	@Autowired
	private PatientInfoService patientInfoService;
	
	@Autowired
	private PatientOrderrecordService patientOrderrecordService;
	
	@Autowired
	private PatientOperationService patientOperationService;
	
	@Autowired
	private PatientCriticalValueService patientCriticalValueService;
	
	@Autowired
	private PatientBirthService patientBirthService;
	
	@Autowired
	private PatientTransferDeptService patientTransferDeptService;
	
	
	@Value("${csltFlag:}")
	private String csltFlag; //是否开启医务定时任务（省人医）
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 拉取HIS影子库住院患者基本信息（省人医）
	  -- 作者: wch
	  -- 创建时间: 2025年04月08日
	  -- 
	  -- =============================================
	 */
	@Scheduled(cron = "0 0/5 * * * ?")//每5分拉取
	public void syncPatientInfoByHis() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================拉取住院患者基本信息开始=============================================");
				long startTime = System.currentTimeMillis();
				patientInfoService.updateOrSavePatientInfo();//插入或者更新患者基本信息
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);
				log.info("=============================================拉取住院患者基本信息结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("拉取HIS住院患者基本信息失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 拉取HIS影子库住院患者(病危/病重医嘱信息)（省人医）
	  -- 作者: wch
	  -- 创建时间: 2025年04月08日
	  -- 
	  -- =============================================
	 */
	//@Scheduled(cron = "0 0/5 * * * ?")//每15分拉取
	public void syncPatientOrderrecordByHis() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================拉取住院患者(病危/病重医嘱信息)开始=============================================");
				long startTime = System.currentTimeMillis();
				patientOrderrecordService.updateOrSavePatientOrderrecord();//插入或者更新住院患者(病危/病重医嘱信息)
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);
				log.info("=============================================拉取住院患者(病危/病重医嘱信息)结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("拉取HIS住院患者(病危/病重医嘱信息)失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 拉取HIS影子库住院患者手术信息（省人医）
	  -- 作者: wch
	  -- 创建时间: 2025年04月14日
	  -- 
	  -- =============================================
	 */
	//@Scheduled(cron = "0 0/5 * * * ?")//每15分拉取
	public void syncPatientOperationByHis() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================拉取住院患者(手术信息)开始=============================================");
				long startTime = System.currentTimeMillis();
				patientOperationService.updateOrSavePatientOperation();//插入或者更新住院患者(手术信息)
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);
				log.info("=============================================拉取住院患者(手术信息)结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("拉取HIS住院患者(手术信息)失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 拉取HIS影子库住院患者危急值信息（省人医）
	  -- 作者: wch
	  -- 创建时间: 2025年04月14日
	  -- 
	  -- =============================================
	 */
	//@Scheduled(cron = "0 0/5 * * * ?")//每15分拉取
	public void syncPatientCriticalValueByHis() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================拉取住院患者(危急值信息)开始=============================================");
				long startTime = System.currentTimeMillis();
				patientCriticalValueService.updateOrSavePatientCriticalValue();//插入或者更新住院患者(危急值信息)
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);
				log.info("=============================================拉取住院患者(危急值信息)结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("拉取HIS住院患者(危急值信息)失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 拉取HIS影子库住院患者分娩婴儿信息（省人医）
	  -- 作者: wch
	  -- 创建时间: 2025年04月14日
	  -- 
	  -- =============================================
	 */
	//@Scheduled(cron = "0 0/5 * * * ?")//每15分拉取
	public void syncPatientBirthByHis() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================拉取住院患者(分娩婴儿信息)开始=============================================");
				long startTime = System.currentTimeMillis();
				patientBirthService.updateOrSavePatientBirth();//插入或者更新住院患者(分娩婴儿信息)
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);
				log.info("=============================================拉取住院患者(分娩婴儿信息)结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("拉取HIS住院患者(分娩婴儿信息)失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 拉取HIS影子库住院患者转科信息（省人医）
	  -- 作者: wch
	  -- 创建时间: 2025年04月14日
	  -- 
	  -- =============================================
	 */
	//@Scheduled(cron = "0 0/5 * * * ?")//每15分拉取
	public void syncPatientTransferDeptByHis() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================拉取住院患者(转科信息)开始=============================================");
				long startTime = System.currentTimeMillis();
				patientTransferDeptService.updateOrSavePatientTransferDept();//插入或者更新住院患者(转科信息)
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);
				log.info("=============================================拉取住院患者(转科信息)结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("拉取HIS住院患者(转科信息)失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	
	
	/**
	 * 拉取平台HIS患者信息数据开始
	 */
	//@Scheduled(cron = "0 0/5 * * * ?")
	public void syncPlatformDoctorRole() {
		try {
			log.info("=============================================拉取平台HIS患者信息数据开始=============================================");
			long startTime = System.currentTimeMillis();
			patientInfoService.syncPlatformPatientInfo();
			long endTime = System.currentTimeMillis();
			long duration = (endTime - startTime);  
			log.info("=============================================拉取平台HIS患者信息数据结束，耗时：" + duration + "=============================================");
		} catch (Exception e) {
			e.printStackTrace();
			log.error("拉取平台HIS患者信息数据失败，原因："+e.getMessage(),e);
		}
	}
}

package cn.trasen.hrms.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.hrms.train.service.HrmsTrainPlanService;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * 培训管理定时任务
 */
@Slf4j
@Component
public class HrmsTrainPlanJob {

	@Autowired
	private HrmsTrainPlanService hrmsTrainPlanService;
	
	/**
	 * 更新培训计划状态 每分钟执行一次
	 */
	@Scheduled(cron = "0 0/5 * * * ?" )
	public void updateTrainPlanStauts() {
		try {
			hrmsTrainPlanService.updateTrainPlanStauts();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}

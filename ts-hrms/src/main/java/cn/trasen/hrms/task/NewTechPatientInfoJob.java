//package cn.trasen.hrms.task;
//
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import cn.trasen.hrms.med.newTech.service.NewTechInfoService;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * <AUTHOR>
// * 新技术患者数据定时任务
// * 已改为表单输入患者暂时不用定时任务抽数据
// */
//@Slf4j
//@Component
//public class NewTechPatientInfoJob {
//	
//	@Autowired
//	private NewTechInfoService newTechInfoService;
//	
//	/**
//	 * 每天晚上晚上0点35分，抽取新技术展开病例数据
//	 */
//	@Scheduled(cron = "0 35 0 * * ? ")
//	public void saveNewTechPatientInfo() {
//		try {
//			log.info("=========定时同步新技术开展病例数据==========");
//			newTechInfoService.generateNewTechPatientInfoData(null);
//    		
//		} catch (Exception e) {
//			log.error("定时同步新技术开展病例数据执行失败：" + e.getMessage(),e);
//		}
//	}
//}

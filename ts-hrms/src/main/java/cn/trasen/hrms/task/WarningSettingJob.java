package cn.trasen.hrms.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.hrms.service.HrmsWarningSettingService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class WarningSettingJob {
	
	@Autowired
	private HrmsWarningSettingService hrmsWarningSettingService;

	/**
	 * 生成预警数据  每天凌晨执行
	 * @Title: generateWarningSettingData
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param  参数
	 * @return void 返回类型
	 * 2021年10月14日
	 * ADMIN
	 * @throws
	 */
//	@Scheduled(cron = "0 15 16 * * ? ")
	@Scheduled(cron = "0 0/30 * * * ? ")
	public void generateWarningSettingData() {
		try {
			log.info("=========生产预警数据开始==========");
			hrmsWarningSettingService.generateWarningSettingData();
		} catch (Exception e) {
			log.error("生成预警数据执行失败：" + e.getMessage(),e);
		}
	}
	
	/**
	 * 
	 * @Title: remindWarningSettingDatagenerateWarningSettingData
	 * @Description: 预警提醒  每分钟执行一次
	 * @param  参数
	 * @return void 返回类型
	 * 2021年10月19日
	 * ADMIN
	 * @throws
	 */
	@Scheduled(cron = "0 0/1 * * * ? ")
	public void remindWarningSettingData() {
		try {
			hrmsWarningSettingService.remindWarningSettingData();
		} catch (Exception e) {
			log.error("预警提醒执行失败：" + e.getMessage(),e);
		}
	}
	
	
	
}

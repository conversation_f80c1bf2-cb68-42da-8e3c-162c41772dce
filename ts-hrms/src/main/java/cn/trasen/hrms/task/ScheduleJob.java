package cn.trasen.hrms.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.hrms.med.schedule.service.MedScheduleRecordService;
import lombok.extern.slf4j.Slf4j;

/**
 * 医务-排班管理定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ScheduleJob {
	
	@Autowired
	private MedScheduleRecordService medScheduleRecordService;
	
	@Value("${csltFlag:0}")
	private String csltFlag; //是否开启医务定时任务（省人医）
	
	/**
	 * 每天凌晨35分开始执行
	 */
	@Scheduled(cron = "0 35 0 * * ? ")
	public void saveNewTechPatientInfo() {
		if(!"1".equals(csltFlag)) {
			return;
		}
		try {
			log.info("=============================================排班管理修改学生和进修生状态开始=============================================");
			medScheduleRecordService.deleteScheduleByOutofdate();
			log.info("=============================================排班管理修改学生和进修生状态结束=============================================");
		} catch (Exception e) {
			e.printStackTrace();
			log.error("排班管理修改学生和进修生状态失败，原因："+e.getMessage(),e);
		}
	}
}

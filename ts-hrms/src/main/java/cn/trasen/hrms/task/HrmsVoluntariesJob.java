package cn.trasen.hrms.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.hrms.service.HrmsContractService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.voluntaries.service.HrmsVoluntariesActivityService;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * 志愿者定时任务
 */
@Slf4j
@Component
public class HrmsVoluntariesJob {

	@Autowired
	private HrmsVoluntariesActivityService hrmsVoluntariesActivityService;
	
	/**
	 * @Title: 每分钟执行一次更新志愿者活动状态
	 * @Description: 
	 * @param       
	 * @return void      
	 * @throws
	 */
//	@Scheduled(cron = "0 0/1 * * * ?")
	public void updateVoluntariesActivityJob() {
		try {
			hrmsVoluntariesActivityService.updateVoluntariesActivityJob();
			log.error("###########更新志愿者活动状态定时任执行完毕#############"+DateUtils.getPresentTimeStr());
		} catch (Exception e) {
			log.error("更新志愿者活动状态定时任务执行失败："+e.getMessage(),e);
		}
	}
	
}

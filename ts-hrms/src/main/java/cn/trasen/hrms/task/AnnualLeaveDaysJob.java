package cn.trasen.hrms.task;

import cn.trasen.hrms.attendance.annualLeave.service.HrmsAnnualLeaveEmpService;
import cn.trasen.hrms.service.HrmsWarningSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 员工年假定时任务
 */
@Slf4j
@Component
public class AnnualLeaveDaysJob {
	
	@Autowired
	private HrmsAnnualLeaveEmpService hrmsAnnualLeaveEmpService;

	/**
	 * 每年1月1日晚上0点10分，将上年度为剩余年假设置为本年的上年度剩余年假
	 * @Title: generateWarningSettingData
	 * @return void 返回类型
	 * 2021年10月14日
	 * ADMIN
	 * @throws
	 */
//	@Scheduled(cron = "0 33 * * * ?  ")
	@Scheduled(cron = "0 10 0 1 1 ?  ")
	public void saveLastYearRemainingAnnualLeaveDays() {
		try {
			log.info("=========保存上年剩余年假==========");
			hrmsAnnualLeaveEmpService.saveLastYearRemainingAnnualLeaveDays();
		} catch (Exception e) {
			log.error("保存上年剩余年假数据执行失败：" + e.getMessage(),e);
		}
	}
	
	/**
	 * 
	 * @Title: 每天晚上晚上0点30分，计算上年失效年假
	 * @return void 返回类型
	 * 2021年10月19日
	 * ADMIN
	 * @throws
	 */
//	@Scheduled(cron = "0 54 * * * ? ")
	@Scheduled(cron = "0 30 0 * * ? ")
	public void saveLastYearLapsedAnnualLeaveDays() {
		try {
			log.info("=========计算并保存上一年的失效年假==========");
			hrmsAnnualLeaveEmpService.saveLastYearLapsedAnnualLeaveDays();
		} catch (Exception e) {
			log.error("计算并保存上一年的失效年假数据执行失败：" + e.getMessage(),e);
		}
	}
}

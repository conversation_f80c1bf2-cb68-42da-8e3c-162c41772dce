package cn.trasen.hrms.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsPersonnelChangeMapper;
import cn.trasen.hrms.model.HrmsPersonnelChange;
import cn.trasen.hrms.service.HrmsPersonnelChangeService;
import cn.trasen.hrms.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * 人事事件-人员调动生效执行
 */
@Slf4j
@Component
public class PersonnelTransferExecution {

	@Autowired
	private HrmsPersonnelChangeService hrmsPersonnelChangeService;

	@Resource
	private HrmsPersonnelChangeMapper hrmsPersonnelChangeMapper;
	
	/**
	 * @Title: 每天凌晨一点执行一次定时任务 改变人员状态
	 * @Description: 
	 * @param       
	 * @return void      
	 * @throws
	 */
	@Scheduled(cron = "0 30 0 * * ?")
	public void personnelExecutionJob() {
		try {
			Page page = new Page();
			page.setPageNo(1);
			page.setPageSize(Integer.MAX_VALUE);
			page.setSidx(" t1.create_date ");
			page.setSord(" desc ");
			HrmsPersonnelChange entity = new HrmsPersonnelChange();
			entity.setIsDeleted("N");
			entity.setApprovalStatus("4");
			//获取当天生效的调动数据，进行批量执行
			entity.setStartDate(DateUtils.getStringDateShort(DateUtil.date()));
			entity.setEndDate(DateUtils.getStringDateShort(DateUtil.date()));
			entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			List<HrmsPersonnelChange> list =  hrmsPersonnelChangeMapper.getDataSetList(page,entity);
			if(CollUtil.isNotEmpty(list)){
				String ids = list.stream().map(HrmsPersonnelChange::getPersonnelChangeId).collect(Collectors.joining(","));
				hrmsPersonnelChangeService.incidentAudit(ids);
			}
			log.error("###########人事事件-调动管理定时生效 执行完毕#############"+DateUtils.getPresentTimeStr());
		} catch (Exception e) {
			log.error("人事事件-调动管理定时生效 执行异常："+e.getMessage(),e);
		}
	}
	
}

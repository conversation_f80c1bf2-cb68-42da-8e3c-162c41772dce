package cn.trasen.hrms.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.hrms.med.rounds.service.RoundsSchedulingService;
import cn.trasen.hrms.med.rounds.service.RoundsTaskService;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 -- =============================================
 --文件说明：行政查房:查房任务管理记录生成
 -- =============================================
 */
@Slf4j
@Component
public class RoundsTaskJob {

	@Autowired
	private RoundsTaskService roundsTaskService;
	
	@Autowired
	private RoundsSchedulingService roundsSchedulingService;
	
	
	@Value("${roundsTaskFlag:1}")
	private String roundsTaskFlag; //是否开启查房定时任务
	
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 定时生成查房任务管理记录
	  -- 作者: wch
	  -- 创建时间: 2025年03月10日
	  -- 
	  -- =============================================
	 */
	//@Scheduled(cron = "0 0/2 * * * ?")//每2分拉取
	@Scheduled(cron = "0 15 0 * * ?")//每天0点过15分拉取
	public void addRoundsTask() {
		if("1".equals(roundsTaskFlag)) {
			try {
				log.info("=============================================生成查房任务开始=============================================");
				long startTime = System.currentTimeMillis();
				roundsTaskService.addRoundsTask(0);
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);  
				log.info("=============================================生成查房任务结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("拉取HIS影子库会诊信息失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 定时提醒明天排班需要查房人员
	  -- 作者: wch
	  -- 创建时间: 2025年03月14日
	  -- 
	  -- =============================================
	 */
	@Scheduled(cron = "0 0 12 * * ?")//每天12提醒明天查房人员
	public void reminderRoundsSchedulingMessage() {
		if("1".equals(roundsTaskFlag)) {
			try {
				log.info("=============================================提醒查房人员消息发送开始=============================================");
				long startTime = System.currentTimeMillis();
				roundsSchedulingService.reminderRoundsSchedulingMessage();
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);  
				log.info("=============================================提醒查房人员消息发送结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("消息提醒发送失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 定时提醒整改科室护士长、科主任，上传整改内容
	  -- 作者: wch
	  -- 创建时间: 2025年04月10日
	  -- 
	  -- =============================================
	 */
	//@Scheduled(cron = "0 0/2 * * * ?")//每2分拉取
	@Scheduled(cron = "0 0 11 * * ?")//提前一天的11点提醒整改科室
	public void correctionOverdueMessage() {//整改超期提醒
		if("1".equals(roundsTaskFlag)) {
			try {
				log.info("=============================================提醒查房人员消息发送开始=============================================");
				long startTime = System.currentTimeMillis();
				roundsTaskService.correctionOverdueMessage(1);//1天后到期
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);  
				log.info("=============================================提醒查房人员消息发送结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("消息提醒发送失败，原因："+e.getMessage(),e);
			}
		}
	}
}

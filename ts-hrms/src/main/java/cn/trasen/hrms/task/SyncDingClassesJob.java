package cn.trasen.hrms.task;

import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.hrms.dingtalk.service.CaptureDingAttendance;
import cn.trasen.hrms.hlgl.service.CallHlglService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.ywgl.server.CallYwglService;
import lombok.extern.slf4j.Slf4j;



/** 
* @ClassName: SyncDingClassesJob 
* @Description: 同步钉钉班次
* <AUTHOR>  
* @date 2022年9月23日 下午4:47:31 
*  
*/
@Slf4j
@Component
public class SyncDingClassesJob {
	
	@Autowired
	CaptureDingAttendance call;
	
	@Value("${jkyysyncjob}")
	private String jkyysyncjob;   //经开医院提醒开关
	
	
	//同步打卡信息
	@Scheduled(cron = "0 30 1 * * ? ")
	public void syncDingAttendance() {
		if("1".equals(jkyysyncjob)) {
			try {
				log.error("获取钉钉打卡考勤数据开始："+DateUtils.getPresentTimeStr());
				call.syncDingAttendance();	
				log.error("获取钉钉打卡考勤数据结束："+DateUtils.getPresentTimeStr());
			} catch (Exception e) {
				log.error("获取钉钉打卡考勤数据任务："+e.getMessage(),e);
			}
		}
		
	}
	
	
	//同步排班信息
	@Scheduled(cron = "0 25 1 * * ? ")
	public void synClasses() {
		if("1".equals(jkyysyncjob)) {
			try {
				log.error("获取钉钉打班次数据开始："+DateUtils.getPresentTimeStr());
				call.synClasses();  //同步医务
				log.error("获取钉钉打班次数据结束："+DateUtils.getPresentTimeStr());
			} catch (Exception e) {
				log.error("获取钉钉打班次任务："+e.getMessage(),e);
			}
		}
	}
}

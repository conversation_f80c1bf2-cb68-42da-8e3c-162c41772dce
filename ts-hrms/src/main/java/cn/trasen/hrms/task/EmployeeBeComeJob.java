/**
 * @Title: EmployeeBeComeJob.java  
 * @Package: cn.trasen.hrms.task  
 * @Date: 2021年9月16日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.hrms.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.hrms.service.HrmsEmployeeBecomeService;
import cn.trasen.hrms.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 
* @ClassName: 员工自动计算转正  
 * @Author: 86189
 * @Date: 2021年9月16日
 */
@Slf4j
@Component
public class EmployeeBeComeJob {
	
	@Autowired
	private HrmsEmployeeBecomeService hrmsEmployeeBecomeService;
	
	/**
	 * 
	* @Title: updateSignTypeJob  
	* @Description: 每天凌晨三点执行
	* @Params:       
	* @Return: void
	* <AUTHOR>
	* @date:2021年9月16日
	* @Throws
	 */
	@Scheduled(cron = "0 0 3 * * ?")
	public void updateSignTypeJob() {
		try {
			hrmsEmployeeBecomeService.autoBecome();
		} catch (Exception e) {
			log.error("员工转正定时任务执行失败："+e.getMessage(),e);
		}
	}
	
	
	@Scheduled(cron = "0 0 8 * * ?")
	public void employeeBeComeMessage() {
		try {
			hrmsEmployeeBecomeService.employeeBeComeMessage();
		} catch (Exception e) {
			log.error("员工转正提醒定时任务执行失败："+e.getMessage(),e);
		}
	}
}

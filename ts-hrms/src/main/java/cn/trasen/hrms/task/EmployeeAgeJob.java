/**
 * @Title: EmployeeBeComeJob.java  
 * @Package: cn.trasen.hrms.task  
 * @Date: 2021年9月16日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.hrms.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 
* @ClassName: 员工自动计算转正  
 * @Author: 86189
 * @Date: 2021年9月16日
 */
@Slf4j
@Component
public class EmployeeAgeJob {
	
//	@Autowired
//	private HrmsEmployeeService hrmsEmployeeService;
	
	/**
	 * 
	* @Title: updateSignTypeJob  
	* @Description: 每天凌晨三点执行
	* @Params:       
	* @Return: void
	* <AUTHOR>
	* @date:2021年9月16日
	* @Throws
	 */
//	@Scheduled(cron = "0 0 2 * * ?")
//	public void updateAge() {
//		try {
//			int re = hrmsEmployeeService.updateAge();
//			log.error("年龄自动增长成功："+DateUtils.getPresentTimeStr() + " 更新条数："+re);
//		} catch (Exception e) {
//			log.error("年龄自动增长失败："+e.getMessage(),e);
//		}
//	}
	
//	@Scheduled(cron = "0 0 3 * * ?")
//	public void updateAgeSection() {
//		try {
//			int re = hrmsEmployeeService.updateAgeSection();
//			log.error("年龄区间自动增长成功："+DateUtils.getPresentTimeStr()+ " 更新条数："+re);
//		} catch (Exception e) {
//			log.error("年龄区间自动增长失败执行失败："+e.getMessage(),e);
//		}
//	}
	
	//设置工龄
//	@Scheduled(cron = "0 0 4 * * ?")
//	public void updateSseniority() {
//		try {
//			int re = hrmsEmployeeService.updateSseniority();
//			log.error("工龄自动增长成功："+DateUtils.getPresentTimeStr()+ " 更新条数："+re);
//		} catch (Exception e) {
//			log.error("工龄自动增长失败执行失败："+e.getMessage(),e);
//		}
//	}
}

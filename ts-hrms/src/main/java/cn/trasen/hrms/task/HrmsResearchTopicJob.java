package cn.trasen.hrms.task;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.hrms.model.HrmsResearchTopic;
import cn.trasen.hrms.service.HrmsResearchTopicService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class HrmsResearchTopicJob {
	
	@Autowired
	private HrmsResearchTopicService hrmsResearchTopicService;
	
	@Autowired
	private InformationFeignService informationFeignService;
	
	/**
	 * 科研课题到期提醒
	 * @Title: remindHrmsResearchTopic
	 * @Description: 每天9点执行
	 * @param  参数
	 * @return void 返回类型
	 * 2021年10月14日
	 * ADMIN
	 * @throws
	 */
	@Scheduled(cron = "0 0 9 * * ? ")
	public void remindHrmsResearchTopic() {
		try {
			List<HrmsResearchTopic> hrmsResearchTopicList = hrmsResearchTopicService.selectRemindHrmsResearchTopicList();
			for (HrmsResearchTopic hrmsResearchTopic : hrmsResearchTopicList) {
				StringBuffer sb = new StringBuffer();
				sb.append("课题“").append(hrmsResearchTopic.getTopicName()).append("”");
				sb.append(hrmsResearchTopic.getMonthVal()).append("个月后到达执行结束时间，请您及时跟进！");
				
				NoticeReq notice = NoticeReq.builder()
						.content(sb.toString())
						.noticeType("1")
						.receiver(hrmsResearchTopic.getHandlerUser() + "," + hrmsResearchTopic.getCreateUser())
						.sender("admin")
						.senderName("系统管理员")
						.subject("到期预警提醒")
						.toUrl("/research/topic")
						.build();
				informationFeignService.sendNotice(notice);
			}
		} catch (Exception e) {
			log.error("科研课题到期提醒执行失败：" + e.getMessage(),e);
		}
	}
	
	
	
	
}

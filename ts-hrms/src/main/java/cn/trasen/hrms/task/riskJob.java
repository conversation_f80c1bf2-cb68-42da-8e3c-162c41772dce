package cn.trasen.hrms.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.hrms.med.qualityApply.service.MedQualityApplyService;
import cn.trasen.hrms.med.risk.service.RiskOperationDiscussService;
import cn.trasen.hrms.med.risk.service.RiskPatientOperationService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class riskJob {

	@Autowired
	private RiskOperationDiscussService riskOperationDiscussService;
	
	@Autowired
	private RiskPatientOperationService riskPatientOperationService;
	
	@Autowired
	private MedQualityApplyService medQualityApplyService;
	
	@Value("${csltFlag:}")
	private String csltFlag; //是否开启医务定时任务（省人医）
	
	
	/**
	 * 
	 * 
	  -- =============================================
	  -- 功能描述: 拉取HIS影子库住院患者基本信息（省人医）
	  -- 作者: wch
	  -- 创建时间: 2025年04月29日
	  -- 
	  -- =============================================
	 */
	//@Scheduled(cron = "0 0/2 * * * ?")//每2分拉取
	@Scheduled(cron = "0 45 14,20 * * ? ")//每天下午14点45、晚上20点45拉取数据
	public void syncRiskPatientOperationByHis() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================拉取住院患者已安排的四级手术开始=============================================");
				long startTime = System.currentTimeMillis();
				riskPatientOperationService.updateOrSaveFourOperation();//插入或者更新四级手术信息
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);
				log.info("=============================================拉取住院患者已安排的四级手术结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("拉取住院患者已安排的四级手术信息失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 下午15点给主刀医生发送上午手术排台完成后短信（省人医）
	  -- 作者: wch
	  -- 创建时间: 2025年04月29日
	  -- 
	  -- =============================================
	 */
	@Scheduled(cron = "0 0 15 * * ? ")//下午15点给主刀医生发送上午手术排台完成后短信
	public void sendSsMessage() {
		if("1".equals(csltFlag)) {
			try {
				riskPatientOperationService.sendSsMessage();//插入或者更新四级手术信息
			} catch (Exception e) {
				e.printStackTrace();
				log.error("发送短信失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 拉取EMR术前讨论病历（省人医）
	  -- 作者: wch
	  -- 创建时间: 2025年04月29日
	  -- 
	  -- =============================================
	 */
	//@Scheduled(cron = "0 0/2 * * * ?")//每2分拉取
	@Scheduled(cron = "0 45 20 * * ?")//每天下午8点45拉取一下数据
	public void syncRiskOperationDiscussByEmr() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================拉取EMR术前讨论病历开始=============================================");
				long startTime = System.currentTimeMillis();
				riskOperationDiscussService.saveOperationDiscuss();//插入EMR术前讨论病历
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);
				log.info("=============================================拉取EMR术前讨论病历结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("拉取EMR术前讨论病历信息失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 4级手术术前未讨论消息推送（省人医）
	  -- 作者: wch
	  -- 创建时间: 2025年04月29日
	  -- 
	  -- =============================================
	 */
	//@Scheduled(cron = "0 0/2 * * * ?")//每2分拉取
	@Scheduled(cron = "0 0 21 * * ?")//提前一天的晚上9点提醒明天手术
	public void riskOperationMessageLeader() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================4级手术术前未讨论消息推送开始=============================================");
				long startTime = System.currentTimeMillis();
				riskPatientOperationService.riskOperationLeaderMessage();//4级手术术前未讨论消息推送(推送院领导)
				//riskPatientOperationService.riskOperationMessage();//4级手术术前未讨论消息推送(推送临床医生)
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);
				log.info("=============================================4级手术术前未讨论消息推送结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("拉取EMR术前讨论病历信息失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 4级手术术前未讨论消息推送（省人医）
	  -- 作者: wch
	  -- 创建时间: 2025年04月29日
	  -- 
	  -- =============================================
	 */
	//@Scheduled(cron = "0 0/2 * * * ?")//每2分拉取
	@Scheduled(cron = "0 5 21 * * ?")//提前一天的晚上9点提醒明天手术
	public void riskOperationMessage() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================4级手术术前未讨论消息推送开始=============================================");
				long startTime = System.currentTimeMillis();
				//riskPatientOperationService.riskOperationLeaderMessage();//4级手术术前未讨论消息推送(推送院领导)
				riskPatientOperationService.riskOperationMessage();//4级手术术前未讨论消息推送(推送临床医生)
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);
				log.info("=============================================4级手术术前未讨论消息推送结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("拉取EMR术前讨论病历信息失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	/**
	 * 统计质控率  2小时更新一次
	 */
	@Scheduled(cron = "0 0 0/2 * * ? ")
	public void queryQualityApplyEmr() {
		if("1".equals(csltFlag)) {
			try {
				medQualityApplyService.queryQualityApplyEmr();
			} catch (Exception e) {
				e.printStackTrace();
				log.error("统计质控率失败，原因："+e.getMessage(),e);
			}
		}
	}
}

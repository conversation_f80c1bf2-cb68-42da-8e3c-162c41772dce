package cn.trasen.hrms.task;

import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import cn.trasen.hrms.hlgl.service.CallHlglService;
import cn.trasen.hrms.service.HrmsSchedulingHolidaysService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.ywgl.server.CallYwglService;
import lombok.extern.slf4j.Slf4j;


/**   
 * @ClassName:  SchedulingSyncJob   
 * @Description:班次同步   
 * @author: WZH
 * @date:   2021年11月18日 上午10:41:08      
 * @Copyright:  
 */
@Slf4j
@Component
public class SchedulingSyncJob {
	
	@Autowired
	private CallHlglService callHlglService;
	
	@Autowired
	private CallYwglService callYwglService;
	
	@Autowired
	private HrmsSchedulingHolidaysService hrmsSchedulingHolidaysService;
	
	
	//两小时同步一次
	@Scheduled(cron = "0 30 23 * * ? ")
	public void syncHlgl() {
		try {
			  Map<String,String> parMap = new HashMap<String, String>();
			  parMap.put("startDate", DateUtils.getDateBefore30() );
			  parMap.put("endDate",  DateUtils.getDateLater10());
			log.error("同步护理系统排班数据开始："+DateUtils.getPresentTimeStr());
			callHlglService.findAllDate(parMap);	//护理管理同步
			log.error("同步护理系统排班数据结束："+DateUtils.getPresentTimeStr());
		} catch (Exception e) {
			log.error("护理同步挺尸任务："+e.getMessage(),e);
		}
	}
	
	
	@Scheduled(cron = "0 50 23 * * ? ")
	public void synvYwgl() {
		try {
			  Map<String,String> parMap = new HashMap<String, String>();
			  parMap.put("startDate", DateUtils.getDateBefore30() );
			  parMap.put("endDate",  DateUtils.getDateLater10());
			log.error("同步医务系统排班数据开始："+DateUtils.getPresentTimeStr());
			callYwglService.findAllDate(parMap);  //同步医务
			log.error("同步医务系统排班数据结束："+DateUtils.getPresentTimeStr());
		} catch (Exception e) {
			log.error("护理同步挺尸任务："+e.getMessage(),e);
		}
	}
	
	
	/**
	 * 同步节假日数据
	 */
	@Scheduled(cron = "0 0/50 * * * ? ")
	public void syncSchedulingHolidays() {
		try {
			log.info("==========排班节假日同步数据任务开始=========="+DateUtils.getPresentTimeStr());
			
			hrmsSchedulingHolidaysService.syncSchedulingHolidays();
			
			log.info("==========排班节假日同步数据任务结束=========="+DateUtils.getPresentTimeStr());
		} catch (Exception e) {
			e.printStackTrace();
			log.error("排班节假日同步数据任务失败："+e.getMessage(),e);
		}
	}
	
	
}

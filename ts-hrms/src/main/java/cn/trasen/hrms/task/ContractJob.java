package cn.trasen.hrms.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.hrms.service.HrmsContractService;
import cn.trasen.hrms.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * 劳动合同定时任务
 */
@Slf4j
@Component
public class ContractJob {

	@Autowired
	private HrmsContractService hrmsContractService;
	
	/**
	 * @Title: 每天凌晨一点执行一次定时任务 改变到期合同状态
	 * @Description: 
	 * @param       
	 * @return void      
	 * @throws
	 */
	@Scheduled(cron = "0 0 1 * * ?")
	public void updateSignTypeJob() {
		try {
			hrmsContractService.updateSignTypeJob();
			log.error("###########合同到期定时任执行完毕#############"+DateUtils.getPresentTimeStr());
		} catch (Exception e) {
			log.error("合同到期定时任务执行失败："+e.getMessage(),e);
		}
	}
	
}

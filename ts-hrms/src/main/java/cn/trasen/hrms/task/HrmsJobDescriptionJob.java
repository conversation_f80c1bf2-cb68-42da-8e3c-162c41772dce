package cn.trasen.hrms.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.hrms.service.HrmsJobDescriptionService;
import cn.trasen.hrms.service.HrmsWarningSettingService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class HrmsJobDescriptionJob {
	
	@Autowired
	private HrmsJobDescriptionService hrmsJobDescriptionService;
	
	@Autowired
	private GlobalSettingsFeignService globalSettingsFeignService;

	
	/**
	 * 
	 * @Title: generateHrmsJobDescriptionData
	 * @Description: 每天凌晨4点同步数据
	 * @param  参数
	 * @return void 返回类型
	 * 2021年12月25日
	 * ADMIN
	 * @throws
	 */
	@Scheduled(cron = "0 0 4 1/1 * ? ")
	public void generateHrmsJobDescriptionData() {
		try {
			PlatformResult<GlobalSetting> safeGlobalSetting = globalSettingsFeignService.getSafeGlobalSetting("N");
			
			String orgCode = safeGlobalSetting.getObject().getOrgCode();
			
			if("lyszyyy".equals(orgCode)) {
				hrmsJobDescriptionService.generateHrmsJobDescriptionData();
			}
			
		} catch (Exception e) {
			log.error("同步岗位说明书数据失败：" + e.getMessage(),e);
		}
	}
	
}

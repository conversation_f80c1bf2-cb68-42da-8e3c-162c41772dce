package cn.trasen.hrms.task;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.properties.AppConfigProperties;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.message.NoticeService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * 人事事件
 */
@Slf4j
@Component
public class personnelEvent {

	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;
	
    @Autowired
    AppConfigProperties appConfigProperties;
	
	@Value("${jkyytxkg}")
	private String jkyytxkg;   //经开医院提醒开关
	
	/**
	 * @Title: 试用期工作计划表提醒时间：入院时间7天内提醒新入职人员
	 */
	@Scheduled(cron = "0 51 07 ? * *")
	public void Event1() {
		try {
			
			if("1".equals(jkyytxkg)) {
				 UserLoginService.loginContext("admin");
				//查询入院7天的人员
				List<EmployeeResp> list = hrmsEmployeeService.getRy7day();
				for (EmployeeResp emp : list) {
					StringBuffer sb = new StringBuffer();
					sb.append("请您及时填写  <试用期工作计划表>");
					
			          NoticeReq notice =
			                    NoticeReq.builder()
			                            .content(sb.toString())
			                            .noticeType("3")
			                            .receiver(emp.getEmployeeNo())  //接收人
			                            .sender(UserInfoHolder.getCurrentUserCode()) //发送人
			                            .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
			                            .subject("试用期工作计划表")
			                            .wxSendType("2")
			                            .toUrl("/process/start").source("流程发起")
			                            .build();
			            NoticeService.sendAsynNotice(notice);
					
				}
				
				log.error("###########试用期工作计划表提醒时间：入院时间7天内提醒新入职人员 执行完毕#############"+DateUtils.getPresentTimeStr());
			}
		
		} catch (Exception e) {
			log.error("试用期工作计划表提醒时间：入院时间7天-->定时任务执行失败："+e.getMessage(),e);
		}
	}
	
	/**
	 * @Title: 新员工入职关怀表提醒时间：入院时间27天内
	 */
	@Scheduled(cron = "0 52 07 ? * *")
	public void Event2() {
		try {
			if("1".equals(jkyytxkg)) {
				 UserLoginService.loginContext("admin");
				List<EmployeeResp> list = hrmsEmployeeService.getRy27day();
				for (EmployeeResp emp : list) {
					StringBuffer sb = new StringBuffer();
					sb.append("请您及时填写  <新员工入职关怀表>");
					
			          NoticeReq notice =
			                    NoticeReq.builder()
			                            .content(sb.toString())
			                            .noticeType("3")
			                            .receiver(emp.getEmployeeNo())  //接收人
			                            .sender(UserInfoHolder.getCurrentUserCode()) //发送人
			                            .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
			                            .subject("新员工入职关怀表")
			                            .wxSendType("2")
			                            .toUrl("/process/start").source("流程发起")
			                            .build();
			            NoticeService.sendAsynNotice(notice);
				}
				log.error("###########新员工入职关怀表提醒定时任执行完毕#############"+DateUtils.getPresentTimeStr());
			}
			
		} catch (Exception e) {
			log.error("新员工入职关怀表提醒 定时任务执行失败："+e.getMessage(),e);
		}
	}
	
	/**
	 * @Title: 试用期阶段性考核评估表（第一个月）：入院时间27天后
	 */
	@Scheduled(cron = "0 53 07 ? * *")
	public void Event3() {
		try {
			if("1".equals(jkyytxkg)) {
				 UserLoginService.loginContext("admin");
				List<EmployeeResp> list = hrmsEmployeeService.getRy27day();
				for (EmployeeResp emp : list) {
					StringBuffer sb = new StringBuffer();
					sb.append("请您及时填写  <试用期阶段性考核评估表（第一个月）>");
					  NoticeReq notice =
			                    NoticeReq.builder()
			                            .content(sb.toString())
			                            .noticeType("3")
			                            .receiver(emp.getEmployeeNo())  //接收人
			                            .sender(UserInfoHolder.getCurrentUserCode()) //发送人
			                            .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
			                            .subject("试用期阶段性考核评估表（第一个月）")
			                            .wxSendType("2")
			                            .build();
			            NoticeService.sendAsynNotice(notice);
				}
				log.error("###########试用期阶段性考核评估表（第一个月）定时任执行完毕#############"+DateUtils.getPresentTimeStr());
			}
			
		} catch (Exception e) {
			log.error("试用期阶段性考核评估表（第一个月） 定时任务执行失败："+e.getMessage(),e);
		}
	}
	
	/**
	 * @Title: 试用期阶段性考核评估表（第二个月）：入院时间57天后
	 */
	@Scheduled(cron = "0 54 07 ? * *")
	public void Event4() {
		try {
			if("1".equals(jkyytxkg)) {
				 UserLoginService.loginContext("admin");
				List<EmployeeResp> list = hrmsEmployeeService.getRy57day();
				for (EmployeeResp emp : list) {
					StringBuffer sb = new StringBuffer();
					sb.append("请您及时填写  <试用期阶段性考核评估表（第二个月）>");
					  NoticeReq notice =
			                    NoticeReq.builder()
			                            .content(sb.toString())
			                            .noticeType("3")
			                            .receiver(emp.getEmployeeNo())  //接收人
			                            .sender(UserInfoHolder.getCurrentUserCode()) //发送人
			                            .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
			                            .subject("试用期阶段性考核评估表（第二个月）")
			                            .wxSendType("2")
			                            .toUrl("/process/start").source("流程发起")
			                            .build();
			            NoticeService.sendAsynNotice(notice);
				}
				log.error("###########试用期阶段性考核评估表（第一个月）定时任执行完毕#############"+DateUtils.getPresentTimeStr());	
			}
			
		} catch (Exception e) {
			log.error("试用期阶段性考核评估表（第一个月） 定时任务执行失败："+e.getMessage(),e);
		}
	}
	
	/**
	 * @Title:试用期员工考核：转正时间前15天
	 */
	@Scheduled(cron = "0 55 07 ? * *")
	public void Event5() {
		try {
			if("1".equals(jkyytxkg)) {
				 UserLoginService.loginContext("admin");
				List<EmployeeResp> list = hrmsEmployeeService.getRysyqday();
				for (EmployeeResp emp : list) {
					StringBuffer sb = new StringBuffer();
					sb.append("试用期员工考核");
					  NoticeReq notice =
			                    NoticeReq.builder()
			                            .content(sb.toString())
			                            .noticeType("3")
			                            .receiver(emp.getEmployeeNo())  //接收人
			                            .sender(UserInfoHolder.getCurrentUserCode()) //发送人
			                            .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
			                            .subject("试用期员工考核")
			                            .wxSendType("2")
			                            .toUrl("/process/start").source("流程发起")
			                            .build();
			            NoticeService.sendAsynNotice(notice);
				}
				log.error("###########试用期员工考核定时任执行完毕#############"+DateUtils.getPresentTimeStr());
			}
			
		} catch (Exception e) {
			log.error("试用期员工考核  定时任务执行失败："+e.getMessage(),e);
		}
	}
	
	
	@Scheduled(cron = "0 55 07 ? * *")
	public void Event9() {
		try {
			if("1".equals(jkyytxkg)) {
				 UserLoginService.loginContext("admin");
				//查询入院3天的人员
				List<EmployeeResp> list = hrmsEmployeeService.getRy3day();
				for (EmployeeResp emp : list) {
					StringBuffer sb = new StringBuffer();
					sb.append("请及时登录协同办公平台完善人员档案信息，若已完善请忽略！");
			          NoticeReq notice =
			                    NoticeReq.builder()
			                            .content(sb.toString())
			                            .noticeType("3")
			                            .receiver(emp.getEmployeeNo())  //接收人
			                            .sender(UserInfoHolder.getCurrentUserCode()) //发送人
			                            .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
			                            .subject("完善个人信息")
			                            .wxSendType("2")
			                            .build();
			            NoticeService.sendAsynNotice(notice);
				}
				
				log.error("###########入职第三天提醒完善个人信息#############"+DateUtils.getPresentTimeStr());
			}
		
		} catch (Exception e) {
			log.error("入职第三天提醒完善个人信息-->定时任务执行失败："+e.getMessage(),e);
		}
	}
	
}

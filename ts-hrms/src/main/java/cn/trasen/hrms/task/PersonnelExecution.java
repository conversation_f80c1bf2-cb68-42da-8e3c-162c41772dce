package cn.trasen.hrms.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import cn.trasen.hrms.service.HrmsPersonnelExecutionService;
import cn.trasen.hrms.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * 人事事件定时执行任务
 */
@Slf4j
@Component
public class PersonnelExecution {

	@Autowired
	private HrmsPersonnelExecutionService hrmsPersonnelExecutionService;
	
	/**
	 * @Title: 每天凌晨一点执行一次定时任务 改变人员状态
	 * @Description: 
	 * @param       
	 * @return void      
	 * @throws
	 */
	@Scheduled(cron = "0 0 1 * * ?")
	public void personnelExecutionJob() {
		try {
			hrmsPersonnelExecutionService.personnelExecutionJob();
			log.error("###########人事事件定时执行 执行完毕#############"+DateUtils.getPresentTimeStr());
		} catch (Exception e) {
			log.error("人事事件定时执行 执行完毕："+e.getMessage(),e);
		}
	}
	
}

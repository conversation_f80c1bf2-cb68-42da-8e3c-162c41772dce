package cn.trasen.hrms.task;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import cn.trasen.hrms.med.cost.service.MedCostViewService;
import cn.trasen.hrms.med.crisisValue.service.MedCrisisValueService;
import cn.trasen.hrms.med.cslt.model.CsltToHisDto;
import cn.trasen.hrms.med.cslt.service.CsltAppyService;
import cn.trasen.hrms.med.cslt.service.CsltScduDetlService;
import cn.trasen.hrms.med.cslt.service.CsltScduService;
import cn.trasen.hrms.med.qua.service.MedDoctorLevelService;
import cn.trasen.hrms.med.qua.service.MedDoctorRoleService;
import cn.trasen.hrms.med.qua.service.MedPharmacistRoleService;
import cn.trasen.hrms.med.qua.service.MedRoleStatisticsService;
import cn.trasen.hrms.med.qua.service.QuaAuthCfgService;
import cn.trasen.hrms.med.qua.service.QuaAuthItemDetlService;
import cn.trasen.hrms.med.qualityApply.service.MedQualityApplyService;
import cn.trasen.hrms.web.Client.TrasenIfCentClientV2;
import cn.trasen.hrms.web.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 -- =============================================
 --文件说明：会诊申请信息自动更新排班状态
 --类名称: CsltAppyJob
 --创建时间：2024年11月18日 
 --作者：GW
 -- =============================================
 */
@Slf4j
@Component
public class CsltJob {
	
	@Autowired
	private CsltAppyService csltAppyService;
	@Autowired
	private CsltScduService csltScduService;
	@Autowired
	private CsltScduDetlService csltScduDetlService;
	@Autowired
	private MedDoctorRoleService medDoctorRoleService;
	@Autowired
	private QuaAuthCfgService quaAuthCfgService;
	@Autowired
	private MedPharmacistRoleService medPharmacistRoleService;
	@Autowired
	private QuaAuthItemDetlService quaAuthItemDetlService;
	@Autowired
	private MedDoctorLevelService medDoctorLevelService;
	@Autowired
	private MedCrisisValueService medCrisisValueService;
	@Autowired
	private MedRoleStatisticsService medRoleStatisticsService;
	@Autowired
	private MedCostViewService medCostViewService;
	@Autowired
	private MedQualityApplyService medQualityApplyService;
	
	@Value("${csltFlag:}")
	private String csltFlag; //是否开启医务定时任务（省人医）
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 自动更新会诊申请信息的超过状态
	  -- 作者: GW
	  -- 创建时间: 2024年11月18日
	  -- 
	  -- =============================================
	 */
	@Scheduled(cron = "0 0 * * * ?")
	public void updateSignTypeJob() {
		if("1".equals(csltFlag)) {
			try {
				csltAppyService.updtIsOtStatus();
			} catch (Exception e) {
				e.printStackTrace();
				log.error("自动更新会诊申请信息的超过状态失败，原因："+e.getMessage(),e);
			}
		}
	}
	@Scheduled(cron = "0 0 0 * * ?")
	public void pushCsltToHis() {
		if("1".equals(csltFlag)) {
			try {
				List<CsltToHisDto> list = csltScduService.getPushHisData();
				if(CollUtil.isNotEmpty(list)){
					log.info("=============================================推送会诊排班信息开始=============================================");
					long startTime = System.currentTimeMillis();
					TrasenIfCentClientV2 client = TrasenIfCentClientV2.getInstance();
					String pid = "khel255rv";//授权ID
					String secret = "lVntbHG&JYG05o0novQV%Ubt$gWe1I5F";//秘钥
					String interfaceId = "857819778897682432";//接口ID
					
					Map<String, List<String>> data = new HashMap<>();
					List<String> dataList = new ArrayList<>();
					JSONArray array = JSONUtil.createArray();
					list.forEach(i -> {
						array.add(i);
					});
					dataList.add(StrUtil.format("{\"Request\":{\"ywbh\":\"SaveSchdule\",\"SCHDULEDT\":{}}}", array));
					data.put("PostString", dataList);
					String result = client.postParameterUseToken("http://192.168.10.166:8888", interfaceId, pid, secret, data);
					log.info("=============================================推送会诊排班信息HIS返回结果：" + result + "=============================================");
					List<String> successIds = WebUtils.parseResult(result);
					if(CollUtil.isNotEmpty(successIds)){
						csltScduDetlService.batchUpdtPushFlag(successIds);
					}
					long endTime = System.currentTimeMillis();
					long duration = (endTime - startTime);  
					log.info("=============================================推送会诊排班信息结束，耗时：" + duration + "=============================================");
				}
			} catch (Exception e) {
				e.printStackTrace();
				log.error("推送排班信息失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 拉取HIS影子库会诊信息记录
	  -- 作者: wch
	  -- 创建时间: 2025年01月15日
	  -- 
	  -- =============================================
	 */
	//@Scheduled(cron = "0 0/2 * * * ?")//每2分拉取
	@Scheduled(cron = "0 5 0 * * ?")//每天0点过5分拉取
	public void syncCsltAppyByHis() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================拉取会诊申请记录信息开始=============================================");
				long startTime = System.currentTimeMillis();
				csltAppyService.updateOrSaveCsltAppyByHis();//拉取普通、科间会诊
				csltAppyService.updateOrSaveCsltAppyByHis2();//拉取急诊、科间会诊；普通、院内会诊
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);  
				log.info("=============================================拉取会诊申请记录信息结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
				log.error("拉取HIS影子库会诊信息失败，原因："+e.getMessage(),e);
			}
		}
	}
	
	
	/**
	 * 拉取省人医HIS员工数据  30分钟拉一次
	 */
	@Scheduled(cron = "0 0/30 * * * ?")
	public void syncHisEmployee() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================拉取省人医员工数据开始=============================================");
				long startTime = System.currentTimeMillis();
				csltAppyService.syncHisEmployee();
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);  
				log.info("=============================================拉取省人医员工数据结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 拉取省人医人事系统数据
	 */
	@Scheduled(cron = "0 0 0 * * ? ")
	public void syncHrmEmployee() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================拉取省人医人事系统员工数据开始=============================================");
				long startTime = System.currentTimeMillis();
				csltAppyService.syncHrmEmployee();
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);  
				log.info("=============================================拉取省人医人事系统员工数据结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 拉取省人医医生处方权(省人医)
	 */
	@Scheduled(cron = "0 0/10 * * * ?")
	public void syncDoctorRole() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================拉取省人医医生处方权数据开始=============================================");
				long startTime = System.currentTimeMillis();
				medDoctorRoleService.syncDoctorRole();
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);  
				log.info("=============================================拉取省人医医生处方权数据结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 推送药师权限到his(省人医)
	 */
	@Scheduled(cron = "0 0/5 * * * ?")
	public void pushMedPharmacistRole() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================推送药师权限到his开始=============================================");
				long startTime = System.currentTimeMillis();
				medPharmacistRoleService.pushMedPharmacistRole();
				long endTime = System.currentTimeMillis();
				long duration = (endTime - startTime);  
				log.info("=============================================推送药师权限到his结束，耗时：" + duration + "=============================================");
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 资质看板数据统计(省人医)
	 */
	@Scheduled(cron = "0 0 0/8 * * ? ")
	public void computationMedRoleData() {
		if("1".equals(csltFlag)) {
			try {
				medRoleStatisticsService.computationMedRoleData();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 耗材看板数据拉取(省人医)  每月1、2、3号 凌晨3点开始抽 上月的数据
	 */
	@Scheduled(cron = "0 0 3 1,2,3 * ? ")
	//@Scheduled(cron = "0 0/30 * * * ?")
	public void syncMedCostView() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================拉取耗材看板（省人医）数据开始=============================================");
				medCostViewService.syncMedCostView();
				log.info("=============================================拉取耗材看板（省人医）数据结束=============================================");
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 手术字典院内等级同步HIS(省人医)  每天凌晨3点执行
	 */
	@Scheduled(cron = "0 0 3 * * ? ")
	public void syncQuaCfgLvHosp() {
		if("1".equals(csltFlag)) {
			try {
				log.info("=============================================手术字典院内等级同步HIS数据开始=============================================");
				quaAuthCfgService.syncQuaCfgLvHosp();
				log.info("=============================================手术字典院内等级同步HIS数据结束=============================================");
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 平台手术字典数据
	 */
	//@Scheduled(cron = "0 0 0 * * ?")
	@Scheduled(cron = "0 0/30 * * * ?")
	public void syncPlatformOperationDict() {
		try {
			log.info("=============================================拉取平台手术字典数据开始=============================================");
			long startTime = System.currentTimeMillis();
			quaAuthCfgService.syncPlatformOperationDict();
			long endTime = System.currentTimeMillis();
			long duration = (endTime - startTime);  
			log.info("=============================================拉取平台手术字典数据结束，耗时：" + duration + "=============================================");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 平台手术权限  2小时拉一次
	 */
	@Scheduled(cron = "0 0 0/2 * * ? ")
	//@Scheduled(cron = "0 0/15 * * * ?")
	public void syncPlatformOperPowers() {
		try {
			log.info("=============================================拉取平台手术权限数据开始=============================================");
			long startTime = System.currentTimeMillis();
			quaAuthItemDetlService.syncPlatformOperPowers();
			long endTime = System.currentTimeMillis();
			long duration = (endTime - startTime);  
			log.info("=============================================拉取平台手术权限数据结束，耗时：" + duration + "=============================================");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 拉取平台医师处方权限数据
	 */
	@Scheduled(cron = "0 0 0/1 * * ? ")
	//@Scheduled(cron = "0 0/5 * * * ?")
	public void syncPlatformDoctorRole() {
		try {
			log.info("=============================================拉取平台医师权限数据开始=============================================");
			long startTime = System.currentTimeMillis();
			medDoctorRoleService.syncPlatformDoctorRole();
			long endTime = System.currentTimeMillis();
			long duration = (endTime - startTime);  
			log.info("=============================================拉取平台医师权限数据结束，耗时：" + duration + "=============================================");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 更新技术职称数
	 */
	@Scheduled(cron = "0 0/2 * * * ?")
	public void updateTechnical() {
		try {
			log.info("=============================================更新技术职称数据开始=============================================");
			long startTime = System.currentTimeMillis();
			medDoctorRoleService.updateTechnical();
			long endTime = System.currentTimeMillis();
			long duration = (endTime - startTime);  
			log.info("=============================================更新技术职称数据结束，耗时：" + duration + "=============================================");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 更新第一学历信息
	 */
	@Scheduled(cron = "0 0/1 * * * ?")
	public void updateFirstEducation() {
		try {
			log.info("=============================================更新第一学历数据开始=============================================");
			medDoctorRoleService.updateFirstEducation();
			log.info("=============================================更新第一学历数据结束，耗时："  + "=============================================");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 更新三级医师授权
	 */
	@Scheduled(cron = "0 0 0/4 * * ? ")
	public void updateTenureStatus() {
		try {
			log.info("=============================================更新三级医师授权开始=============================================");
			long startTime = System.currentTimeMillis();
			medDoctorLevelService.updateTenureStatus();
			long endTime = System.currentTimeMillis();
			long duration = (endTime - startTime);  
			log.info("=============================================更新三级医师授权结束，耗时：" + duration + "=============================================");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Scheduled(cron = "0 0/2 * * * ?")
	public void syncMedCrisisValue() {
		try {
			log.info("=============================================危急值数据同步开始=============================================");
			long startTime = System.currentTimeMillis();
			medCrisisValueService.syncMedCrisisValue();
			long endTime = System.currentTimeMillis();
			long duration = (endTime - startTime);  
			log.info("=============================================危急值数据同步结束，耗时：" + duration + "=============================================");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	//@Scheduled(cron = "0 0/3 * * * ?")
	public void syncInPatientConsultation() {
		try {
			log.info("=============================================拉取住院病人会诊数据开始=============================================");
			long startTime = System.currentTimeMillis();
			csltAppyService.syncInPatientConsultation();
			long endTime = System.currentTimeMillis();
			long duration = (endTime - startTime);  
			log.info("=============================================拉取住院病人会诊数据结束，耗时：" + duration + "=============================================");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 质控专员计算过期状态
	 */
	@Scheduled(cron = "0 20 0 * * ?")
	public void updateExpireStatus() {
		try {
			medQualityApplyService.updateExpireStatus();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}

package cn.trasen.hrms.hlgl.bean;

/**   
 * @ClassName:  HlglBean   
 * @Description:护理管理bean 
 * @author: WZH
 * @date:   2021年11月15日 下午3:35:42      
 * @Copyright:  
 */
/**   
 * @ClassName:  HlglBean   
 * @Description:TODO(描述这个类的作用)   
 * @author: WZH
 * @date:   2021年11月15日 下午3:42:09      
 * @Copyright:  
 */
/**   
 * @ClassName:  HlglBean   
 * @Description:TODO(描述这个类的作用)   
 * @author: WZH
 * @date:   2021年11月15日 下午3:42:13      
 * @Copyright:  
 */
/**   
 * @ClassName:  HlglBean   
 * @Description:护理管理接收bean
 * @author: WZH
 * @date:   2021年11月15日 下午6:51:43      
 * @Copyright:  
 */
public class HlglBean {
	
	private String frequencyName;   //班次名称
	
	private String frequencyId; //班次id
	
	private String frequencyTime;   //考勤时间
	
	private String schedulingDate;  //排班时间
	
	private String employeeNo;   //员工工号 （his）
	
	private String employeeName;  //员工姓名
	
	private String startTime;  //开始时间
	
	private String endTime;  //结束时间
	
	private String idCard; //身份证
	
	private String orgName; //科室名称
	
	private String orgId; //科室id
	
	private String frequencyColor;  //颜色
	
	/**   
	 * @Fields frequencyType : 
	 * 护理班次（ 1 白班2行政班3夜班4晚班5两头班 6休班）
	 * 医务         （1 白班2晚班3夜班4行政班5两头班 6出班）
	 */ 
	private String frequencyType;  //班次类型
	
	private String frequencyTypeName;  //班次类型名称
	
	private String attendance;  //排班时间
	
	
	public String getAttendance() {
		return attendance;
	}

	public void setAttendance(String attendance) {
		this.attendance = attendance;
	}

	public String getFrequencyType() {
		return frequencyType;
	}

	public void setFrequencyType(String frequencyType) {
		this.frequencyType = frequencyType;
	}

	public String getFrequencyTypeName() {
		return frequencyTypeName;
	}

	public void setFrequencyTypeName(String frequencyTypeName) {
		this.frequencyTypeName = frequencyTypeName;
	}

	public String getFrequencyId() {
		return frequencyId;
	}

	public void setFrequencyId(String frequencyId) {
		this.frequencyId = frequencyId;
	}

	public String getIdCard() {
		return idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public String getFrequencyName() {
		return frequencyName;
	}

	public void setFrequencyName(String frequencyName) {
		this.frequencyName = frequencyName;
	}

	public String getFrequencyTime() {
		return frequencyTime;
	}

	public void setFrequencyTime(String frequencyTime) {
		this.frequencyTime = frequencyTime;
	}

	public String getSchedulingDate() {
		return schedulingDate;
	}

	public void setSchedulingDate(String schedulingDate) {
		this.schedulingDate = schedulingDate;
	}

	public String getEmployeeNo() {
		return employeeNo;
	}

	public void setEmployeeNo(String employeeNo) {
		this.employeeNo = employeeNo;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getFrequencyColor() {
		return frequencyColor;
	}

	public void setFrequencyColor(String frequencyColor) {
		this.frequencyColor = frequencyColor;
	}
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.hlgl.mapper.CallHlglMapper">
	<select id="findAllDate"  resultType="cn.trasen.hrms.hlgl.bean.HlglBean" parameterType="java.util.HashMap">
		<![CDATA[
			select t2.user_id as employeeNo,t2.name as employeeName,t5.idcard as idCard ,
			t1.work_date as frequencyTime,t1.classes_v_id,
			t6.structure_name as orgName,t6.id as orgId,
			CASE WHEN 	t4.start_time !='' or t4.start_time  is not null  THEN 	left(t4.start_time,5) ELSE '00:00'  END as startTime,
			CASE WHEN 	t4.end_time !='' or t4.end_time is not null  THEN 	left(t4.end_time,5) ELSE '00:00'  END as endTime,
			CASE WHEN 	t3.scvid !='' or t3.scvid is not null THEN 	t3.scvid ELSE t10.id  END as frequencyId,
			CASE WHEN 	 t3.attendance !='' or t3.attendance is not null   THEN 	t3.attendance ELSE '0'  END as attendance,
			CASE WHEN t3.sname!='' or t3.sname is not null  THEN t3.sname ELSE t10.holiday_name  END as frequencyName,
	 		CASE WHEN t7.font_color!=''  or t7.font_color is not null  THEN t7.font_color ELSE ' rgb(255, 0, 0)'  END as frequencyColor,
			CASE WHEN t8.id!='' or t8.id is not null  THEN t8.id ELSE 6  END as frequencyType,
			CASE WHEN t8.type_name !=''  or t8.type_name is not null THEN t8.type_name ELSE t10.holiday_name  END as frequencyTypeName


			from (
			   SELECT 
			            a.* ,c.value('.', 'varchar(50)') AS classes_v_id
			   FROM     scheduling_info a
			            CROSS APPLY ( SELECT    CAST('<row>' + REPLACE(classes_version_id, ',',
			                                                           '</row><row>')
			                                    + '</row>' AS XML) AS xmlcode
			                        ) C1
			            CROSS APPLY xmlcode.nodes('*') t ( c )
			) t1 
			left join user_info t2 on t1.user_code=t2.user_id
			left join person_info t5 on t5.workid= t2.user_id
			left join (select id scvid,class_id cid,name sname,attendance from scheduling_classes_version) t3 on t1.classes_v_id = t3.scvid
			left join scheduling_classes_time t4 on t1.classes_v_id = t4.classes_version_id
		  	left join org_structure t6 on t2.depart_id =t6.id
		    left join scheduling_classes_info t7 on t3.cid = t7.id
	 		left join scheduling_classes_type t8 on t7.type = t8.id
	 		left join scheduling_official_holiday t10 on t1.holiday_id = t10.id
		    where 1 = 1
		    and  t1.work_date >= #{startDate} and t1.work_date <= #{endDate}

		]]>
	</select>
	
	<select id="selectAllPersonJobDescription"	resultType="Map">
		select a.user_id,a.name,b.nurseclass,b.idcard,c.* from user_info a 
		left join person_info b on a.user_id = b.workid
		left join person_job_description c on b.nurseclass = c.nurseclass and c.wardDept= a.depart_id
		where a.is_use = 1 and idcard is not null
	</select>
	
	<select id="getMaternityLeaveDate" parameterType="java.util.HashMap" resultType="cn.trasen.hrms.hlgl.bean.HlglBean">
		select t1.holidaystart as startTime , t1.holidayend  as endTime,t2.idCard from daily_gravida t1
		left join person_info t2 on t1.person_id=t2.workid
		where  t1.holidayend >=  CONVERT(varchar(100), GETDATE(), 23) 
	</select>
</mapper>
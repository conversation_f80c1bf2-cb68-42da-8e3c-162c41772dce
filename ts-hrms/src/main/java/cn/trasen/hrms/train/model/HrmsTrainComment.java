package cn.trasen.hrms.train.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "hrms_train_comment")
@Setter
@Getter
public class HrmsTrainComment {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;
   
	/**
     * 培训评论id
     */
	@Id
    @Column(name = "train_comment_id")
    @ApiModelProperty(value = "培训评论id")
    private String trainCommentId;

    /**
     * employee_id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "employee_id")
    private String employeeId;

    /**
     * 评论者名称
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "评论者名称")
    private String employeeName;

    /**
     * 评论者图像
     */
    @Column(name = "employee_avatar")
    @ApiModelProperty(value = "评论者图像")
    private String employeeAvatar;

    /**
     * 评论者科室
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "评论者科室")
    private String orgName;

    /**
     * 评论的内容
     */
    @ApiModelProperty(value = "评论的内容")
    private String content;

    /**
     * 培训主题Id
     */
    @Column(name = "train_plan_id")
    @ApiModelProperty(value = "培训主题Id")
    private String trainPlanId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;
    
    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;
    
    /**
     * 父级评论id
     */
    @Column(name = "parent_id")
    @ApiModelProperty(value = "父级评论id")
    private String parentId;
    
    @Transient
    @ApiModelProperty(value = "二级评论集合")
    private List<HrmsTrainComment> children;
  
    @Transient
    @ApiModelProperty(value = "主题")
    private String trainTopic;
    
    @Transient
    @ApiModelProperty(value = "培训讲师")
    private String trainLecturer;
    
    
    @Transient
    @ApiModelProperty(value = "培训开始时间")
    private String trainStartTime; 
    
    @Transient
    @ApiModelProperty(value = "培训结束时间")
    private String trainEndTime; 
   
    @Transient
    @ApiModelProperty(value = "评论开始时间")
    private String commentStartTime;
    @Transient
    @ApiModelProperty(value = "评论结束时间")
    private String commentEndTime;
  
    @Transient
    @ApiModelProperty(value = "培训类型")
    private String trainType;  
    
    @Transient
    @ApiModelProperty(value = "培训类型名称")
    private String trainTypeText;  
    
    @Transient
    @ApiModelProperty(value = "评分")
    private Integer score;
    
    @Transient
    @ApiModelProperty(value = "为1  已点赞")
    private Integer praise;
    
    @Transient
    @ApiModelProperty(value = "点赞总数")
    private Integer praiseSize;
    
    
}
package cn.trasen.hrms.train.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.train.model.HrmsTrainScore;

/**
 * @ClassName HrmsTrainScoreService
 * @Description TODO
 * @date 2023��5��18�� ����5:37:24
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsTrainScoreService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��5��18�� ����5:37:24
	 * <AUTHOR>
	 */
	Integer save(HrmsTrainScore record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��5��18�� ����5:37:24
	 * <AUTHOR>
	 */
	Integer update(HrmsTrainScore record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��5��18�� ����5:37:24
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsTrainScore
	 * @date 2023��5��18�� ����5:37:24
	 * <AUTHOR>
	 */
	HrmsTrainScore selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsTrainScore>
	 * @date 2023��5��18�� ����5:37:24
	 * <AUTHOR>
	 */
	DataSet<HrmsTrainScore> getDataSetList(Page page, HrmsTrainScore record);

	/**
	 * 评分榜
	 * @param page
	 * @param record
	 * @return
	 */
	DataSet<HrmsTrainScore> scoreboard(Page page, HrmsTrainScore record);
}

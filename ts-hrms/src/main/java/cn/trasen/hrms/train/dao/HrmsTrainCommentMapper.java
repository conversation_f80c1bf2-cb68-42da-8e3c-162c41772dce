package cn.trasen.hrms.train.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.train.model.HrmsTrainComment;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsTrainCommentMapper extends Mapper<HrmsTrainComment> {

	/**
	 * 评论管理列表
	 * @param page
	 * @param record
	 * @return
	 */
	List<HrmsTrainComment> managementlist(Page page, HrmsTrainComment record);

	/**
	 * 根据培训计划查询评论
	 * @param page
	 * @param record
	 * @return
	 */
	List<HrmsTrainComment> getDataSetList(Page page, HrmsTrainComment record);

	List<HrmsTrainComment> getChildren(@Param("parentId") String parentId ,@Param("createUser") String createUser);
}
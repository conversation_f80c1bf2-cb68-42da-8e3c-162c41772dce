package cn.trasen.hrms.train.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.train.model.HrmsTrainTemplate;

/**
 * @ClassName HrmsTrainTemplateService
 * @Description TODO
 * @date 2023��6��28�� ����10:23:25
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsTrainTemplateService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��6��28�� ����10:23:25
	 * <AUTHOR>
	 */
	Integer save(HrmsTrainTemplate record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��6��28�� ����10:23:25
	 * <AUTHOR>
	 */
	Integer update(HrmsTrainTemplate record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��6��28�� ����10:23:25
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsTrainTemplate
	 * @date 2023��6��28�� ����10:23:25
	 * <AUTHOR>
	 */
	HrmsTrainTemplate selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsTrainTemplate>
	 * @date 2023��6��28�� ����10:23:25
	 * <AUTHOR>
	 */
	DataSet<HrmsTrainTemplate> getDataSetList(Page page, HrmsTrainTemplate record);
}

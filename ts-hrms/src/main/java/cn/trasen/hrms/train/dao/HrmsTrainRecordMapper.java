package cn.trasen.hrms.train.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.train.model.HrmsTrainRecord;

public interface HrmsTrainRecordMapper extends Mapper<HrmsTrainRecord> {
	
	/**
	 * 
	 * @MethodName: getDataList
	 * @Description: TODO
	 * <AUTHOR>
	 * @param page
	 * @param hrmsTrainRecord
	 * @return List<HrmsTrainRecord>
	 * @date 2023-04-01 02:30:15
	 */
	List<HrmsTrainRecord> getDataList(Page page, HrmsTrainRecord hrmsTrainRecord);

	/**
	 * 培训次数
	 * @param userCode
	 * @param ssoOrgCode
	 * @return
	 */
	Integer pxcs(@Param("userCode") String userCode, @Param("ssoOrgCode") String ssoOrgCode);

	/**
	 * 培训时长
	 * @param userCode
	 * @param ssoOrgCode
	 * @return
	 */
	Double pxsc(@Param("userCode") String userCode, @Param("ssoOrgCode") String ssoOrgCode);

	/**
	 * 培训学时
	 * @param userCode
	 * @param ssoOrgCode
	 * @return
	 */
	Double pxxs(@Param("userCode") String userCode, @Param("ssoOrgCode") String ssoOrgCode);

	/**
	 * 当前积分
	 * @param userCode
	 * @param ssoOrgCode
	 * @return
	 */
	Double dqjf(@Param("userCode") String userCode, @Param("ssoOrgCode") String ssoOrgCode);

	/**
	 * 未参加次数
	 * @param userCode
	 * @param ssoOrgCode
	 * @return
	 */
	Integer cj(@Param("userCode") String userCode, @Param("ssoOrgCode") String ssoOrgCode);

	/**
	 * 待培训
	 * @param userCode
	 * @param ssoOrgCode
	 * @return
	 */
	Integer dpx(@Param("userCode") String userCode, @Param("ssoOrgCode") String ssoOrgCode);


	/**
	 * 签到签退列表
	 * @param page
	 * @param hrmsTrainRecord
	 * @return
	 */
	List<HrmsTrainRecord> getSigninDataList(Page page, HrmsTrainRecord hrmsTrainRecord);
	//二维码右边统计数据
	Double yindao(HrmsTrainRecord hrmsTrainRecord);
	Double yidao(HrmsTrainRecord hrmsTrainRecord);
	Double weidao(HrmsTrainRecord hrmsTrainRecord);
	Double chidao(HrmsTrainRecord hrmsTrainRecord);
	Double qiantui(HrmsTrainRecord hrmsTrainRecord);

	Double weiqiantui(HrmsTrainRecord hrmsTrainRecord);


	List<HrmsTrainRecord> getSignInCountExport( HrmsTrainRecord hrmsTrainRecord);



	Double pxall(String userCode);


	List<HrmsTrainRecord> getSignInAllCountExport(HrmsTrainRecord hrmsTrainRecord);


	List<HrmsTrainRecord> ranking(Page page, HrmsTrainRecord record);


	List<HrmsTrainRecord> findListByEmpId(String id);

	

}
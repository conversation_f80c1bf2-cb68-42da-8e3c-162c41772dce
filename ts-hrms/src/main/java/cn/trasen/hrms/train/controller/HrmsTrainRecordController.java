package cn.trasen.hrms.train.controller;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.train.model.HrmsTrainPlan;
import cn.trasen.hrms.train.model.HrmsTrainRecord;
import cn.trasen.hrms.train.service.HrmsTrainPlanService;
import cn.trasen.hrms.train.service.HrmsTrainRecordService;
import cn.trasen.hrms.utils.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: HrmsTrainRecordController  
 * @Description: 培训记录
 * <AUTHOR>
 * @date 2020年5月23日 上午11:24:22
 */
@Slf4j
@Api(tags = "培训记录Controller")
@RestController
public class HrmsTrainRecordController {

	@Autowired
	HrmsTrainRecordService hrmsTrainRecordService;
	
	@Autowired
	HrmsTrainPlanService hrmsTrainPlanService;
	
	/**
	 * 
	 * @MethodName: signin
	 * @Description: TODO
	 * <AUTHOR>
	 * @param signinType
	 * @return PlatformResult<String>
	 * @date 2023-04-01 11:04:09
	 */
	@ApiOperation(value = "签到签退   1 签到 2签退", notes = "签到签退  1 签到 2签退")
	@PostMapping(value = "/train/record/signin")
	public PlatformResult<String> signin(String signinType,String trainPlanId) {
		try {
			 String msg = hrmsTrainRecordService.signin(signinType,trainPlanId);
			 return PlatformResult.success(msg);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}

	@ApiOperation(value = "添加人员", notes = "添加人员")
	@PostMapping(value = "/train/record/addSignin")
	public PlatformResult<String> addSignin(@RequestBody HrmsTrainRecord record) {
		try {
			String msg = hrmsTrainRecordService.addSignin(record);
			return PlatformResult.success(msg);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}


	@ApiOperation(value = "删除计划信息", notes = "删除计划信息")
	@GetMapping(value = "/train/record/delete/{id}")
	public PlatformResult<String> deleteById(@PathVariable String id) {
		try {
			hrmsTrainRecordService.deleteById(id);
			return PlatformResult.success(null,"删除成功");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure("服务端异常，删除计划失败：" + e.getMessage());
		}
	}

	
	@ApiOperation(value = "积分赋予接口")
	@PostMapping(value = "/train/record/ascribeIntegral")
	public PlatformResult<List<HrmsTrainRecord>> ascribeIntegral(@RequestBody List<HrmsTrainRecord> hrmsTrainRecords) {
		try {
			 hrmsTrainRecordService.ascribeIntegral(hrmsTrainRecords);
			 return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}
	
	@ApiOperation(value = "积分赋予查询接口")
	@PostMapping(value = "/train/record/notInvited")
	public PlatformResult<List<HrmsTrainRecord>> notInvited(@RequestBody HrmsTrainRecord hrmsTrainRecord) {
		try {
			List<HrmsTrainRecord> list = hrmsTrainRecordService.notInvited(hrmsTrainRecord);
			 return PlatformResult.success(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}
	
	
	/**
	 * 
	 * @MethodName: getDataList
	 * @Description: TODO
	 * <AUTHOR>
	 * @param page
	 * @param hrmsTrainRecord
	 * @return DataSet<HrmsTrainRecord>
	 * @date 2023-04-01 04:14:29
	 */
	@ApiOperation(value = "查询培训记录(分页)", notes = "查询培训记录(分页)")
	@PostMapping("/train/record/getDataList")
	public DataSet<HrmsTrainRecord> getDataList(Page page, HrmsTrainRecord hrmsTrainRecord) {
		List<HrmsTrainRecord> list = hrmsTrainRecordService.getDataList(page, hrmsTrainRecord);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}
	
	
	@ApiOperation(value = "培训签到列表", notes = "培训签到列表")
	@PostMapping("/train/record/getSigninDataList") 
	public	DataSet<HrmsTrainRecord> getSigninDataList(Page page, HrmsTrainRecord hrmsTrainRecord) {
		List<HrmsTrainRecord> list = hrmsTrainRecordService.getSigninDataList(page, hrmsTrainRecord);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}


	@ApiOperation(value = "二维码右边明细", notes = "二维码右边明细")
	@PostMapping(value = "/train/record/getSignInCount")
	public PlatformResult<Map<String,Object>> getSignInCount(@RequestBody HrmsTrainRecord hrmsTrainRecord) {
		try {
			Map<String, Object> details = hrmsTrainRecordService.signInDetailsById(hrmsTrainRecord);
			return PlatformResult.success(details);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}
	
	//签到签退导出
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/train/record/getSignInCountExport")
	public void getSignInCountExport(Page page,HttpServletResponse response, HttpServletRequest request,HrmsTrainRecord hrmsTrainRecord) {
		try {
			page.setPageNo(1);
			page.setPageSize(100000);
			
			HrmsTrainPlan trainPlan = hrmsTrainPlanService.selectById(hrmsTrainRecord.getTrainPlanId());
			
			List<HrmsTrainRecord> list = hrmsTrainRecordService.getSignInAllCountExport( hrmsTrainRecord);
			
			if (list != null && list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					list.get(i).setNo(i+1);  //设置序号
					
					//设置迟到数据
					if(1 == list.get(i).getSignStatus()) {
						list.get(i).setSignStatusText("已签到");
					}else if(2 == list.get(i).getSignStatus()) {
						list.get(i).setSignStatusText("迟到");
					}else {
						list.get(i).setSignStatusText("未签到");
					}
					
					//签退
					if( 0== list.get(i).getSignOutStatus()) {
						list.get(i).setSignOutStatusText("未签退");
					}else {
						list.get(i).setSignOutStatusText("已签退");
					}
					
					//处理签到时间签退时间
					
					if(null != list.get(i).getSignTime()) {
						list.get(i).setSignTimeText(DateUtils.getPresentTimeStr(list.get(i).getSignTime()));
					}
					
					if(null != list.get(i).getSignOutTime()) {
						list.get(i).setSignOutTimeText(DateUtils.getPresentTimeStr(list.get(i).getSignOutTime()));
					}
				}
			}
			
			//表头标题
            String name = "签到签退情况表" + DateUtil.format(new Date(),"yyyyMMdd") + ".xlsx";
            // 模板位置
            String templateUrl = "template/hrmsTrainSinginExport.xlsx";
            
            Map<String, Object> map = new HashMap<String, Object>();
		    map.put("list", list);
		    map.put("trainTopic",trainPlan.getTrainTopic());
		    map.put("trainLecturer",trainPlan.getTrainLecturer());
		    map.put("trainStartTime", DateUtil.format(trainPlan.getTrainStartTime(),"yyyy-MM-dd HH:mm"));
			map.put("trainEndTime", DateUtil.format(trainPlan.getTrainEndTime(),"yyyy-MM-dd HH:mm"));
		    map.put("trainContent", trainPlan.getTrainContent());
            cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, map, name, templateUrl);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
	
	
	
	
	@ApiOperation(value = "我的培训右上角明细", notes = "我的培训右上角明细")
	@GetMapping(value = "/train/record/myDetails")
	public PlatformResult<Map<String,Object>> myDetails() {
		try {
			Map<String, Object> myDetails = hrmsTrainRecordService.myDetails();
			return PlatformResult.success(myDetails);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}
	
	
	@ApiOperation(value = "我的培训tab页签上面三个值", notes = "我的培训tab页签上面三个值")
	@GetMapping(value = "/train/record/myParticipate")
	public PlatformResult<Map<String,Object>> myParticipate() {
		try {
			Map<String, Object> map = hrmsTrainRecordService.myParticipate();
			return PlatformResult.success(map);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}
	
	//根据人员id查询所有已参加的培训 未参加的培训
	@ApiOperation(value = "根据人员id查询所有", notes = "根据人员id查询所有")
	@GetMapping(value = "/train/record/findListByEmpId/{id}")
	public PlatformResult<List<HrmsTrainRecord>> findListByEmpId(@PathVariable String id) {
		try {
			List<HrmsTrainRecord> lsit = hrmsTrainRecordService.findListByEmpId(id);
			return PlatformResult.success(lsit);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}
	
	@ApiOperation(value = "积分榜", notes = "积分榜")
	@GetMapping("/train/record/ranking")
	public DataSet<HrmsTrainRecord> ranking(Page page, HrmsTrainRecord record) {
		return hrmsTrainRecordService.ranking(page, record);
	}
	
	@ApiOperation(value = "积分榜导出", notes = "积分榜导出")
	@GetMapping("/train/record/scoreboardexport")
	public void scoreboardexport(HttpServletResponse response, HttpServletRequest request,Page page, HrmsTrainRecord record) {
		page.setPageSize(Integer.MAX_VALUE);
		 try {
			 DataSet<HrmsTrainRecord> scoreboard = hrmsTrainRecordService.ranking(page, record);
			 List<HrmsTrainRecord> list = scoreboard.getRows();
			 if(list != null && list.size() >0) {
				 for (int j = 0; j < list.size(); j++) {
					 list.get(j).setNo(j+1);
				}
			 }
			 
			//表头标题
			String name = "培训管理积分榜" + DateUtil.format(new Date(),"yyyyMMdd") + ".xlsx";
			// 模板位置
			String templateUrl = "template/hrmsTrainRanking.xlsx";
			
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("list", list);
			cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, map, name, templateUrl);
		} catch (UnsupportedEncodingException e) {
			log.error(e.getMessage(), e);
		}
	}
	
	
}

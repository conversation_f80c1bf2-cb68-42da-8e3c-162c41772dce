package cn.trasen.hrms.train.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_train_praise")
@Setter
@Getter
public class HrmsTrainPraise {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    /**
     * 点赞id
     */
    @Id
    @Column(name = "train_praise_id")
    @ApiModelProperty(value = "点赞id")
    private String trainPraiseId;

    /**
     * 评论id
     */
    @Column(name = "train_comment_id")
    @ApiModelProperty(value = "评论id")
    private String trainCommentId;

    /**
     * 1 已点赞 0 取消点赞
     */
    @ApiModelProperty(value = "1 已点赞 0 取消点赞")
    private String praise;

    /**
     * 培训主题Id
     */
    @Column(name = "train_plan_id")
    @ApiModelProperty(value = "培训主题Id")
    private String trainPlanId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;
}
package cn.trasen.hrms.train.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

/**
 * 培训计划
 *
 */
@Table(name = "hrms_train_plan")
@Setter
@Getter
public class HrmsTrainPlan {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * train_plan_id
     */
    @Id
    @Column(name = "train_plan_id")
    @ApiModelProperty(value = "train_plan_id")
    private String trainPlanId;

    /**
     * 培训主题
     */
    @Column(name = "train_topic")
    @ApiModelProperty(value = "培训主题")
    private String trainTopic;

    /**
     * 培训讲师Id
     */
    @Column(name = "train_lecturer_id")
    @ApiModelProperty(value = "培训讲师Id")
    private String trainLecturerId;
    
    /**
     * 培训讲师
     */
    @Column(name = "train_lecturer")
    @ApiModelProperty(value = "培训讲师")
    private String trainLecturer;

    /**
     * 培训开始时间
     */
    @Column(name = "train_start_time")
    @ApiModelProperty(value = "培训开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date trainStartTime;
    
    /**
     * 培训开始时间
     */
    @Column(name = "train_end_time")
    @ApiModelProperty(value = "培训结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date trainEndTime;
    
    /**
     * 培训地点
     */
    @Column(name = "train_place")
    @ApiModelProperty(value = "培训地点")
    private String trainPlace;
    
    @Column(name = "train_place_id")
    @ApiModelProperty(value = "培训地点")
    private String trainPlaceId;
    
    
    
    @Column(name = "apply_id")
    @ApiModelProperty(value = "培训地点")
    private String applyId;
    
    
    /**
     * 培训人数
     */
    @Column(name = "train_numbers")
    @ApiModelProperty(value = "培训人数")
    private String trainNumbers;
    
    
    @Column(name = "credit")
    @ApiModelProperty(value = "学分")
    private String credit;
    
    /**
     * 学分
     */
    @Column(name = "credit_hour")
    @ApiModelProperty(value = "学时")
    private String creditHour;

    /**
     * 培训类别(1:内部培训 2:外部培训)
     */
    @Column(name = "train_type")
    @ApiModelProperty(value = "培训类别(1:内部培训 2:外部培训)")
    private String trainType;
    
    
    /**
     * 预算(元)
     */
    @ApiModelProperty(value = "预算(元)")
    private String budget;

    /**
     * 培训内容
     */
    @Column(name = "train_content")
    @ApiModelProperty(value = "培训内容")
    private String trainContent;
    
    @Column(name = "org_attributes")
    @ApiModelProperty(value = "人员类别")
    private String orgAttributes;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @Column(name = "reminder")
    @ApiModelProperty(value = "提醒事项")
    private String reminder;

    /**
     * 取消原因
     */
    @Column(name = "cancellation_reasons")
    @ApiModelProperty(value = "取消原因")
    private String cancellationReasons;
    
    /**
     * 状态(0:草稿  1:待审批 2:审批通过 待开始  3:审批驳回 4:取消计划 5:已开始  6：已结束)
     */
    @Column(name = "plan_state")
    @ApiModelProperty(value = "状态(0:草稿  1:待审批 2:审批通过 待开始  3:审批驳回 4:取消计划 5:已开始  6：已结束)")
    private Integer planState;
    
    @Column(name = "approval_comment")
    @ApiModelProperty(value = "审批意见")
    private String approvalComment;
    
    @Column(name = "applicant_code")
    @ApiModelProperty(value = "申请人编码")
    private String applicantCode;
    
    @Column(name = "applicant_name")
    @ApiModelProperty(value = "申请人名称")
    private String applicantName;
    
    @Column(name = "applicant_dept_code")
    @ApiModelProperty(value = "申请人部门")
    private String applicantDeptCode;
    
    @Column(name = "applicant_dept_name")
    @ApiModelProperty(value = "申请人部门名称")
    private String applicantDeptName;
    
    
    //添加字段
    //培训层级
    
    
    
    @Column(name = "place_level")
    @ApiModelProperty(value = "培训层级")
    private String placeLevel;   //院级、部级、科级
    
    @Column(name = "place_course_type")
    @ApiModelProperty(value = "课程类型")
    private String placeCourseType;   //必修课、选修课

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;
    
    
    @Column(name = "create_dept_id")
    @ApiModelProperty(value = "创建人部门")
    private String createDeptId;
    
    @Column(name = "create_dept_name")
    @ApiModelProperty(value = "创建人部门名称")
    private String createDeptName;
    
    
    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 培训人员集合
     */
    @Column(name = "train_employee_ids")
    @ApiModelProperty(value = "培训人员集合  多个逗号隔开")
    private String trainEmployeeIds;

    /**
     * 培训人员名称集合
     */
    @Column(name = "train_employee_names")
    @ApiModelProperty(value = "培训人员名称集合 多个逗号隔开")
    private String trainEmployeeNames;
    
    /**
     * 会议时长
     */
    @Column(name = "train_hours")
    @ApiModelProperty(value = "会议时长")
    private String trainHours;
    
    @Column(name = "train_file")
    @ApiModelProperty(value = "培训附件  多个逗号隔开")
    private String trainFile;
    
    
    @Column(name = "canceled_emp")
    @ApiModelProperty(value = "审批人")
    private String canceledEmp;
    
    @Column(name = "canceled_date")
    @ApiModelProperty(value = "审批时间")
    private String   canceledDate;
    
    @Column(name = "huifang_file")
    @ApiModelProperty(value = "附件-回放")
    private String   huifangFile;

    @Column(name = "repeat_compute")
    @ApiModelProperty(value = "1相同主题不重复计算学分，")
    private String   repeatCompute;  // 1  不重复计算学分

    
    /**
     * 关键词-前台
     */
    @Transient
    private String condition;
    
    /**
     * 附件Id-前台
     */
    @Transient
    private String businessId;
    
    /**
     * 导出-培训时间
     */
    @Transient
    private String trainTimeExport;
    
    /**
     * 状态(1:计划中 2:计划完成 3:取消计划)
     */
    @Transient
    private String planStateName;
    
    /**
     * 是否查询取消计划 (1:查询    2:不查询)
     */
    @Transient
    private Integer enquiryCancelPlan;
    
    /**
     * 培训开始时间（查询用）
     */
    @Transient
    private String startDate;
    
    /**
     * 培训结束时间（查询用）
     */
    @Transient
    private String endDate;
    
    /**
     * 申请开始时间（查询用）
     */
    @Transient
    private String applicantStartDate;
    
    /**
     * 申请结束时间（查询用）
     */
    @Transient
    private String applicantEndDate;
    
    
    @Transient
    private int orderNumber;
    
    @Transient
    private String planStateText;
    
    @Transient
    private String canceledStartDate ;  //审批时间（查询）
    @Transient
    private String canceledEndDate ; //审批时间（查询）
    
    @Transient
    private String auditType; // 审批状态 1待审批  2已审批  3 全部
   
    @Transient
    private String searchTrainStartTime;   //培训开始时间  搜搜
     @Transient
    private String searchTrainEndTime;   //培训结束时间  搜搜
     
     @ApiModelProperty(value = "培训人数")
     @Transient
     private String pxrs;  
     
     @ApiModelProperty(value = "参加人数")
     @Transient
     private String cjrs;  
     
     @ApiModelProperty(value = "未参加")
     @Transient
     private String wcj;  
     
     @ApiModelProperty(value = "迟到")
     @Transient
     private String cd;  
     
     @ApiModelProperty(value = "参加率")
     @Transient
     private String canjialv;  
     
     @ApiModelProperty(value = "迟到率")
     @Transient
     private String chidaolv;  
    
     @Transient
     @ApiModelProperty(value = "培训类别文本")
     private String trainTypeText;
     
     @Transient
     private Integer no;
    
     @Transient
     private String dateMerge; //时间合并
     @Transient
     private  String createDateText;
    
     //搜索 申请时间
     @Transient
     private String searchStartCreateDate;
     @Transient
     private String searchEndCreateDate;
     @Transient
     private String score;  //评分
     @Transient
     private String trainTime;  //培训时间

     @Transient
     private String employeeId;  //人员

    @Column(name = "send_ding")
    @ApiModelProperty(value = "1发送通知")
    private String   sendDing;

}


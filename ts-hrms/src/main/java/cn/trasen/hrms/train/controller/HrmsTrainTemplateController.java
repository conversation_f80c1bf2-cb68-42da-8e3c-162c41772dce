package cn.trasen.hrms.train.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.train.model.HrmsTrainTemplate;
import cn.trasen.hrms.train.service.HrmsTrainTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsTrainTemplateController
 * @Description 模版控制器
 * @date 2023��6��28�� ����10:23:25
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsTrainTemplateController")
public class HrmsTrainTemplateController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsTrainTemplateController.class);

	@Autowired
	private HrmsTrainTemplateService hrmsTrainTemplateService;

	/**
	 * @Title saveHrmsTrainTemplate
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��6��28�� ����10:23:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/trainTemplate/save")
	public PlatformResult<String> saveHrmsTrainTemplate(@RequestBody HrmsTrainTemplate record) {
		try {
			hrmsTrainTemplateService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsTrainTemplate
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��6��28�� ����10:23:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/trainTemplate/update")
	public PlatformResult<String> updateHrmsTrainTemplate(@RequestBody HrmsTrainTemplate record) {
		try {
			hrmsTrainTemplateService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsTrainTemplateById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsTrainTemplate>
	 * @date 2023��6��28�� ����10:23:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/trainTemplate/{id}")
	public PlatformResult<HrmsTrainTemplate> selectHrmsTrainTemplateById(@PathVariable String id) {
		try {
			HrmsTrainTemplate record = hrmsTrainTemplateService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsTrainTemplateById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��6��28�� ����10:23:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/trainTemplate/delete/{id}")
	public PlatformResult<String> deleteHrmsTrainTemplateById(@PathVariable String id) {
		try {
			hrmsTrainTemplateService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsTrainTemplateList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsTrainTemplate>
	 * @date 2023��6��28�� ����10:23:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/trainTemplate/list")
	public DataSet<HrmsTrainTemplate> selectHrmsTrainTemplateList(Page page, HrmsTrainTemplate record) {
		return hrmsTrainTemplateService.getDataSetList(page, record);
	}
	
	
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/trainTemplate/getAllList")
	public PlatformResult<List<HrmsTrainTemplate>> getAllList(HrmsTrainTemplate record) {
		try {
			Page page  = new Page();
			page.setPageSize(Integer.MAX_VALUE);
			DataSet<HrmsTrainTemplate> dataSetList = hrmsTrainTemplateService.getDataSetList(page,record);
			return PlatformResult.success(dataSetList.getRows());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
}

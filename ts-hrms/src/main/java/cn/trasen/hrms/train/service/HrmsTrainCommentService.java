package cn.trasen.hrms.train.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.train.model.HrmsTrainComment;

/**
 * @ClassName HrmsTrainCommentService
 * @Description TODO
 * @date 2023��5��18�� ����4:30:31
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsTrainCommentService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��5��18�� ����4:30:31
	 * <AUTHOR>
	 */
	Integer save(HrmsTrainComment record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��5��18�� ����4:30:31
	 * <AUTHOR>
	 */
	Integer update(HrmsTrainComment record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��5��18�� ����4:30:31
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsTrainComment
	 * @date 2023��5��18�� ����4:30:31
	 * <AUTHOR>
	 */
	HrmsTrainComment selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsTrainComment>
	 * @date 2023��5��18�� ����4:30:31
	 * <AUTHOR>
	 */
	DataSet<HrmsTrainComment> getDataSetList(Page page, HrmsTrainComment record);

	/**
	 * 评论管理列表
	 * @param page
	 * @param record
	 * @return
	 */
	DataSet<HrmsTrainComment> managementlist(Page page, HrmsTrainComment record);
}

package cn.trasen.hrms.train.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.train.dao.HrmsTrainPraiseMapper;
import cn.trasen.hrms.train.model.HrmsTrainPraise;
import cn.trasen.hrms.train.service.HrmsTrainPraiseService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsTrainPraiseServiceImpl
 * @Description TODO
 * @date 2023��5��29�� ����9:43:43
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsTrainPraiseServiceImpl implements HrmsTrainPraiseService {

	@Autowired
	private HrmsTrainPraiseMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsTrainPraise record) {
		
		
		Assert.hasText(record.getTrainCommentId(), "评论id不能为空");
		//  有就新增 没有就修改
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		//根据员工id和培训计划id
		Example example = new Example(HrmsTrainPraise.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andEqualTo("trainCommentId", record.getTrainCommentId());
		criteria.andEqualTo("createUser",user.getUsercode());
		List<HrmsTrainPraise> score = mapper.selectByExample(example);
		if(score != null && score.size() > 0) {  //有就修改
			HrmsTrainPraise hrmsTrainPraise = score.get(0);
			if("0".equals(hrmsTrainPraise.getPraise())) {
				hrmsTrainPraise.setPraise("1");
			}else {
				hrmsTrainPraise.setPraise("0");
			}
			return update(hrmsTrainPraise);
		}else {  //没有评价过就新增
			record.setTrainPraiseId(String.valueOf(IdWork.id.nextId()));
			record.setCreateDate(new Date());
			record.setUpdateDate(new Date());
			record.setPraise("1");
			record.setIsDeleted("N");
			if (user != null) {
				record.setCreateUser(user.getUsercode());
				record.setCreateUserName(user.getUsername());
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
				record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			}
			return mapper.insertSelective(record);
		}
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsTrainPraise record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsTrainPraise record = new HrmsTrainPraise();
		record.setTrainPraiseId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsTrainPraise selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsTrainPraise> getDataSetList(Page page, HrmsTrainPraise record) {
		Example example = new Example(HrmsTrainPraise.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsTrainPraise> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

package cn.trasen.hrms.train.controller;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Maps;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.train.model.HrmsTrainPlan;
import cn.trasen.hrms.train.service.HrmsTrainPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: HrmsTrainPlanController  
 * @Description: 培训计划Controller
 * <AUTHOR>
 * @date 2020年5月23日 上午11:24:22
 */
@Slf4j
@Api(tags = "培训计划")
@RestController
public class HrmsTrainPlanController {

	@Autowired
	HrmsTrainPlanService hrmsTrainPlanService;
	
	@Resource
    private ResourceLoader resourceLoader;

    @Autowired
    DictItemFeignService dictItemFeignService;
    
	/**
	 * @Title: insert  
	 * @Description: 新增计划
	 * @param hrmsTrainPlan
	 * @return    参数  
	 * PlatformResult<String>    返回类型  
	 * @throws
	 */
	@ApiOperation(value = "新增计划", notes = "新增计划")
	@PostMapping("/train/plan/save")
	public PlatformResult<String> insert(@RequestBody HrmsTrainPlan hrmsTrainPlan) {
		try {
			 hrmsTrainPlanService.insert(hrmsTrainPlan);
			 return PlatformResult.success(null,"新增计划成功");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure("服务端异常，新增计划失败：" + e.getMessage());
		}
	}

	/**
	 * @Title: update  
	 * @Description: 修改计划信息
	 * @param hrmsTrainPlan
	 * @return    参数  
	 * PlatformResult<String>    返回类型  
	 * @throws
	 */
	@ApiOperation(value = "修改计划信息", notes = "修改计划信息")
	@PostMapping(value = "/train/plan/update")
	public PlatformResult<String> update(@RequestBody HrmsTrainPlan hrmsTrainPlan) {
		try {
			hrmsTrainPlanService.update(hrmsTrainPlan);
			return PlatformResult.success(null,"修改计划成功");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure("服务端异常，修改计划失败：" + e.getMessage());
		}
		
	}
	
	
	/**
	 * 
	 * @MethodName: selectDiFaultInfoById
	 * @Description: TODO
	 * <AUTHOR>
	 * @param id
	 * @return PlatformResult<HrmsTrainPlan>
	 * @date 2023-03-30 03:03:40
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/train/plan/{id}")
	public PlatformResult<HrmsTrainPlan> selectDiFaultInfoById(@PathVariable String id) {
		try {
			HrmsTrainPlan record = hrmsTrainPlanService.selectById(id);
			return PlatformResult.success(record,"查询成功");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title: deleteById  
	 * @Description: 删除计划信息
	 * @param trainPlanId
	 * @return    参数  
	 * PlatformResult<String>    返回类型  
	 * @throws
	 */
	@ApiOperation(value = "删除计划信息", notes = "删除计划信息")
	@PostMapping(value = "/train/plan/delete/{id}")
	public PlatformResult<String> deleteById(@PathVariable String id) {
		try {
			hrmsTrainPlanService.deleteById(id);
			return PlatformResult.success(null,"删除计划成功");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure("服务端异常，删除计划失败：" + e.getMessage());
		}
	}
	
	/**
	 * @Title: getDataList  
	 * @Description: 查询计划信息列表(分页)
	 * @param page
	 * @param hrmsTrainPlan
	 * @return    参数  
	 * DataSet<HrmsContract>    返回类型  
	 * @throws
	 */
	@ApiOperation(value = "查询计划信息列表(分页)", notes = "查询计划信息列表(分页)")
	@PostMapping("/train/plan/getDataList")
	public DataSet<HrmsTrainPlan> getDataList(Page page, HrmsTrainPlan hrmsTrainPlan) {
		List<HrmsTrainPlan> list = hrmsTrainPlanService.getDataList(page, hrmsTrainPlan);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}
	
	/**
	 * 
	 * @MethodName: export
	 * @Description: TODO
	 * <AUTHOR>
	 * @param page
	 * @param response
	 * @param request
	 * @param record void
	 * @date 2023-03-30 03:45:03
	 */
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/train/plan/export")
	public void export(Page page,HttpServletResponse response, HttpServletRequest request,HrmsTrainPlan record) {
		try {
			page.setPageNo(1);
			page.setPageSize(100000);
			
			List<HrmsTrainPlan> list = hrmsTrainPlanService.getDataList(page, record);
			
			int i = 1;
			for (HrmsTrainPlan hrmsTrainPlan : list) {
				hrmsTrainPlan.setOrderNumber(i);
				//处理时间
				hrmsTrainPlan.setTrainTime(DateUtil.format(hrmsTrainPlan.getTrainStartTime(), "yyyy-MM-dd HH:mm")  + " - " + DateUtil.format(hrmsTrainPlan.getTrainEndTime(), "yyyy-MM-dd HH:mm") );
				if(0 == hrmsTrainPlan.getPlanState()){
					hrmsTrainPlan.setPlanStateText("草稿");
				}
				if(1 == hrmsTrainPlan.getPlanState()){
					hrmsTrainPlan.setPlanStateText("待审批");
				}
				if(2 == hrmsTrainPlan.getPlanState()){
					hrmsTrainPlan.setPlanStateText("待开始");		
				}
				if(3 == hrmsTrainPlan.getPlanState()){
					hrmsTrainPlan.setPlanStateText("审批驳回");
				}
				if(4 == hrmsTrainPlan.getPlanState()){
					hrmsTrainPlan.setPlanStateText("取消计划");
				}
				if(5 == hrmsTrainPlan.getPlanState()){
					hrmsTrainPlan.setPlanStateText("已开始");
				}
				if(6 == hrmsTrainPlan.getPlanState()){
					hrmsTrainPlan.setPlanStateText("已结束");
				}
				i++;
			}
			
			//表头标题
            String name = "培训计划汇总" + DateUtil.format(new Date(),"yyyyMMdd") + ".xlsx";
            // 模板位置
            String templateUrl = "template/hrmsTrainPlanExport.xlsx";
            
            Map<String, Object> map = new HashMap<String, Object>();
		    map.put("list", list);
		    map.put("exportDate", DateUtil.format(new Date(),"yyyy-MM-dd"));
		    map.put("exportUserName", UserInfoHolder.getCurrentUserName());
            
            cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, map, name, templateUrl);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
	
	
	
	@ApiOperation(value = "到会率接口", notes = "到会率接口")
	@GetMapping("/train/plan/getAttendanceRate")
	public PlatformResult<List<Map<String, Object>>> getAttendanceRate(String type,String date) {
		try {
			List<Map<String, Object>> retMap  = hrmsTrainPlanService.getAttendanceRate(type,date);
			return PlatformResult.success(retMap);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "培训类型top10", notes = "培训类型top10")
	@GetMapping("/train/plan/getTypeTop10")
	public PlatformResult<List<Map<String, Object>>> getTypeTop10() {
		try {
			List<Map<String, Object>> retMap  = hrmsTrainPlanService.getTypeTop10();
			return PlatformResult.success(retMap);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "积分排行榜", notes = "积分排行榜")
	@GetMapping("/train/plan/getIntegralTop")
	public PlatformResult<List<Map<String, Object>>> getIntegralTop(String type,String date) {
		try {
			List<Map<String, Object>> retMap  = hrmsTrainPlanService.getIntegralTop(type,date);
			return PlatformResult.success(retMap);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	@ApiOperation(value = "培训统计报表列表", notes = "培训统计报表列表")
	@GetMapping("/train/plan/getStatistics")
	public DataSet<HrmsTrainPlan> getStatistics(Page page, HrmsTrainPlan hrmsTrainPlan) {
		List<HrmsTrainPlan> list = hrmsTrainPlanService.getStatistics(page, hrmsTrainPlan);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}

	@ApiOperation(value = "培训统计报表列表导出", notes = "培训统计报表列表导出")
	@GetMapping("/train/plan/getStatisticsExport")
	public void getStatisticsExport(Page page,HttpServletResponse response, HttpServletRequest request, HrmsTrainPlan hrmsTrainPlan) {
		try {
			page.setPageNo(1);
			page.setPageSize(100000);
			List<HrmsTrainPlan> listBean = hrmsTrainPlanService.getStatistics(page, hrmsTrainPlan);
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			//学历
			Map<String, String> trainingTypeConvertDictMap = convertDictMap("TRAINING_TYPE");
			//培训类型 字典
			for (int i = 0; i < listBean.size(); i++) {
				listBean.get(i).setNo(i+1);
				listBean.get(i).setDateMerge(sdf.format(listBean.get(i).getTrainStartTime()) + " - " + sdf.format(listBean.get(i).getTrainEndTime()));
				listBean.get(i).setCreateDateText(sdf.format(listBean.get(i).getCreateDate()));
				// 处理培训类型	trainTypeText
				listBean.get(i).setTrainTypeText(trainingTypeConvertDictMap.get(listBean.get(i).getTrainType()));
			}

			//表头标题
            String name = "培训情况汇总" + DateUtil.format(new Date(),"yyyyMMdd") + ".xlsx";
            // 模板位置
            String templateUrl = "template/hrmsTrainPlanStatisticsExport.xlsx";
            
            Map<String, Object> map = new HashMap<String, Object>();
		    map.put("list", listBean);
            cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, map, name, templateUrl);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
	
	
    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }
	
}

package cn.trasen.hrms.train.service.impl;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.google.common.collect.Maps;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUserResp;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.DataBaseProvider;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import cn.trasen.hrms.train.dao.HrmsTrainPlanMapper;
import cn.trasen.hrms.train.dao.HrmsTrainRecordMapper;
import cn.trasen.hrms.train.model.HrmsTrainPlan;
import cn.trasen.hrms.train.model.HrmsTrainRecord;
import cn.trasen.hrms.train.service.HrmsTrainPlanService;
import cn.trasen.hrms.utils.DateUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * @ClassName: HrmsTrainPlanServiceImpl  
 * @Description: 培训计划
 * <AUTHOR>
 * @date 2020年5月23日 上午8:56:55
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsTrainPlanServiceImpl implements HrmsTrainPlanService {

	@Autowired
	private HrmsTrainPlanMapper hrmsTrainPlanMapper;
	
	@Autowired
	private HrmsTrainRecordMapper hrmsTrainRecordMapper;
	
	@Autowired
	private InformationFeignService informationFeignService;
	
	@Autowired
	private SystemUserFeignService systemUserFeignService;
	
    @Autowired
    DictItemFeignService dictItemFeignService;

	@Override
	@Transactional(readOnly = false)
	public void insert(HrmsTrainPlan hrmsTrainPlan) {
		
		Assert.hasText(hrmsTrainPlan.getCredit(), "积分不能为空");
		//积分为负数或者超过	10  提示
		if(Double.valueOf(hrmsTrainPlan.getCredit()) >10  || Double.valueOf(hrmsTrainPlan.getCredit())  < 0) {
			throw new IllegalArgumentException("积分不能小于0 不能大于10");
		}
		hrmsTrainPlan.setTrainPlanId(String.valueOf(IdWork.id.nextId()));
		hrmsTrainPlan.setIsDeleted(Contants.IS_DELETED_FALSE);
		hrmsTrainPlan.setCreateUser(UserInfoHolder.getCurrentUserCode());
		hrmsTrainPlan.setCreateUserName(UserInfoHolder.getCurrentUserName());
		hrmsTrainPlan.setCreateDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
		hrmsTrainPlan.setCreateDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		hrmsTrainPlan.setCreateDate(new Date());
		
		//如果是培训管理员申请则不需要审批
		Boolean right = UserInfoHolder.getRight("TRAIN_PLAN");
		if(right){
			
			if(0 == hrmsTrainPlan.getPlanState()){
				hrmsTrainPlan.setPlanState(0);
			}else{
				hrmsTrainPlan.setPlanState(2);
				hrmsTrainPlan.setCanceledEmp(UserInfoHolder.getCurrentUserName());
				hrmsTrainPlan.setCanceledDate(DateUtils.getPresentTimeStr());
				insertTrainRecord(hrmsTrainPlan, hrmsTrainPlan);
				//补录不发通知
				if(checkDate(hrmsTrainPlan.getTrainStartTime())) {
					if("1".equals(hrmsTrainPlan.getSendDing())){
						sendTrainMessage(hrmsTrainPlan);
					}

				}
				
			}
			
		}else{
			if(0 == hrmsTrainPlan.getPlanState()){
				hrmsTrainPlan.setPlanState(0);
			}else{
				hrmsTrainPlan.setPlanState(1);
				if(checkDate(hrmsTrainPlan.getTrainStartTime())) {
					sendApproveMessage(hrmsTrainPlan);
				}
			}
		}
		hrmsTrainPlan.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsTrainPlanMapper.insertSelective(hrmsTrainPlan);
	}
	
	

	@Override
	@Transactional(readOnly = false)
	public void update(HrmsTrainPlan hrmsTrainPlan) {
		
		Assert.hasText(hrmsTrainPlan.getTrainPlanId(), "ID不能为空.");
		
		HrmsTrainPlan record = hrmsTrainPlanMapper.selectByPrimaryKey(hrmsTrainPlan.getTrainPlanId());
		
		hrmsTrainPlan.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		hrmsTrainPlan.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		hrmsTrainPlan.setUpdateDate(new Date());
		
		if(1 == hrmsTrainPlan.getPlanState()){//待审批 提醒审批人
			if(checkDate(hrmsTrainPlan.getTrainStartTime())) {
				sendApproveMessage(hrmsTrainPlan);
			}
			
		}
		if(2 == hrmsTrainPlan.getPlanState() || 4 == hrmsTrainPlan.getPlanState()){//审批通过  取消计划  提醒参加培训人员
			
			if(2 == hrmsTrainPlan.getPlanState()){
				insertTrainRecord(hrmsTrainPlan, record);
			}
			if(4 == hrmsTrainPlan.getPlanState()) {
				hrmsTrainPlan.setCanceledEmp("");
				hrmsTrainPlan.setCanceledDate("");
			}
			if(checkDate(hrmsTrainPlan.getTrainStartTime())) {
				if("1".equals(hrmsTrainPlan.getSendDing())){
					sendTrainMessage(hrmsTrainPlan);
				}

			}
		}
		
		if(2 == hrmsTrainPlan.getPlanState() || 3 == hrmsTrainPlan.getPlanState()) {
			hrmsTrainPlan.setCanceledEmp(UserInfoHolder.getCurrentUserName());
			hrmsTrainPlan.setCanceledDate(DateUtils.getPresentTimeStr());
		}
		
		
		hrmsTrainPlanMapper.updateByPrimaryKeySelective(hrmsTrainPlan);
	}



	private void insertTrainRecord(HrmsTrainPlan hrmsTrainPlan,
			HrmsTrainPlan record) {
		String trainEmployeeIds = hrmsTrainPlan.getTrainEmployeeIds();
		String trainEmployeeNames = hrmsTrainPlan.getTrainEmployeeNames();
		
		String[] trainEmployeeIdsArray = trainEmployeeIds.split(",");
		String[] trainEmployeeNamesArray = trainEmployeeNames.split(",");
		
		for (int i = 0; i < trainEmployeeIdsArray.length; i++) {
			HrmsTrainRecord  hrmsTrainRecord = new HrmsTrainRecord();
			hrmsTrainRecord.setTrainRecordId(String.valueOf(IdWork.id.nextId()));
			hrmsTrainRecord.setTrainPlanId(hrmsTrainPlan.getTrainPlanId());
			hrmsTrainRecord.setEmployeeId(trainEmployeeIdsArray[i]);
			hrmsTrainRecord.setEmployeeName(trainEmployeeNamesArray[i]);
			hrmsTrainRecord.setInvited(1);
			hrmsTrainRecord.setTrainTopic(record.getTrainTopic());
			hrmsTrainRecord.setRecordCredit("0");
			hrmsTrainRecord.setRecordCreditHour("0");
			hrmsTrainRecord.setSignStatus(0);
			hrmsTrainRecord.setSignOutStatus(0);
			hrmsTrainRecord.setCreateDate(new Date());
			hrmsTrainRecord.setCreateUser(UserInfoHolder.getCurrentUserCode());
			hrmsTrainRecord.setCreateUserName(UserInfoHolder.getCurrentUserName());
			hrmsTrainRecord.setUpdateDate(new Date());
			hrmsTrainRecord.setUpdateUser(UserInfoHolder.getCurrentUserCode());
			hrmsTrainRecord.setUpdateUserName(UserInfoHolder.getCurrentUserName());
			hrmsTrainRecord.setIsDeleted(Contants.IS_DELETED_FALSE);
			hrmsTrainRecord.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			hrmsTrainRecordMapper.insertSelective(hrmsTrainRecord);
		}
	}



	private void sendTrainMessage(HrmsTrainPlan hrmsTrainPlan) {
		
		if(StringUtils.isNotBlank(hrmsTrainPlan.getTrainEmployeeIds())){
			StringBuffer content = new StringBuffer();
			
			if(2 == hrmsTrainPlan.getPlanState()){
				content.append(hrmsTrainPlan.getApplicantDeptName()).append("-").append(hrmsTrainPlan.getApplicantName());
				content.append("发起的[").append(hrmsTrainPlan.getTrainTopic()).append("]培训计划等待您的参与\n ");
				content.append("培训地点：").append(hrmsTrainPlan.getTrainPlace()).append("\n");
				content.append("培训时间：").append(DateUtils.getPresentTimeStr(hrmsTrainPlan.getTrainStartTime())).append("~").append(DateUtils.getPresentTimeStr(hrmsTrainPlan.getTrainEndTime())).append("\n");
				if(StringUtils.isNotBlank(hrmsTrainPlan.getReminder())){
					content.append("提醒事项：").append(hrmsTrainPlan.getReminder()).append("\n");
				}
				content.append("请知悉！");
			}
			if(4 == hrmsTrainPlan.getPlanState()){
				content.append(hrmsTrainPlan.getApplicantDeptName()).append("-").append(hrmsTrainPlan.getApplicantName());
				content.append("发起的[").append(hrmsTrainPlan.getTrainTopic()).append("]培训计划已取消，取消原因：")
				.append(hrmsTrainPlan.getCancellationReasons()).append("，请知悉！");
			}
				
			NoticeReq notice =
					NoticeReq.builder()
						.content(content.toString())
						.noticeType("3")
						.receiver(hrmsTrainPlan.getTrainEmployeeIds())
						.sender(UserInfoHolder.getCurrentUserCode())
						.senderName(UserInfoHolder.getCurrentUserName())
						.subject("培训计划")
						.wxSendType("2")
						.source("培训管理")
						.toUrl("/ts-web-hrm/train-management/my-train")
						.businessId(hrmsTrainPlan.getTrainPlanId())
						.build();
			informationFeignService.sendNotice(notice);
		}
	}

	private void sendApproveMessage(HrmsTrainPlan hrmsTrainPlan) {
		
		StringBuffer content = new StringBuffer();
		content.append(hrmsTrainPlan.getApplicantDeptName()).append("-").append(hrmsTrainPlan.getApplicantName());
		content.append("申请的[").append(hrmsTrainPlan.getTrainTopic()).append("]培训计划等待您的审批，请及时处理！");
		
		PlatformResult<List<ThpsUserResp>> result = systemUserFeignService.selectUserListBySysRoleCode("TRAIN_PLAN");
		
		List<ThpsUserResp> thpsUserRespList = result.getObject();
		
		if(CollectionUtils.isNotEmpty(thpsUserRespList)){
			List<String> empCodeList = new ArrayList<>();
			for (ThpsUserResp thpsUserResp : thpsUserRespList) {
				empCodeList.add(thpsUserResp.getUsercode());
			}
			
			NoticeReq notice =
					NoticeReq.builder()
						.content(content.toString())
						.noticeType("3")
						.receiver(StringUtils.join(empCodeList, ","))
						.sender(UserInfoHolder.getCurrentUserCode())
						.senderName(UserInfoHolder.getCurrentUserName())
						.subject("培训计划")
						.wxSendType("2")
						.source("培训管理")
						.businessId(hrmsTrainPlan.getTrainPlanId())
						.toUrl("/ts-web-hrm/train-management/train-approve")
						.build();
			informationFeignService.sendNotice(notice);
		}
			
	}
	
	@Override
	@Transactional(readOnly = false)
	public void deleteById(String trainPlanId) {
		Assert.hasText(trainPlanId, "ID不能为空.");
		
		HrmsTrainPlan hrmsTrainPlan = new HrmsTrainPlan();
		hrmsTrainPlan.setTrainPlanId(trainPlanId);
		hrmsTrainPlan.setIsDeleted(Contants.IS_DELETED_TURE);
		hrmsTrainPlan.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		hrmsTrainPlan.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		hrmsTrainPlan.setUpdateDate(new Date());
		hrmsTrainPlanMapper.updateByPrimaryKeySelective(hrmsTrainPlan);
	}

	@Override
	public List<HrmsTrainPlan> getDataList(Page page, HrmsTrainPlan hrmsTrainPlan) {
		Example example = new Example(HrmsTrainPlan.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		//根据当前登录账号机构编码过滤查询数据
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		Boolean right = UserInfoHolder.getRight("TRAIN_PLAN");
		if(!UserInfoHolder.ISADMIN() ) {
			if(!right) {
				criteria.andEqualTo("applicantCode", UserInfoHolder.getCurrentUserCode());
			}
		}
		
		//状态(0:草稿  1:待审批 2:审批通过 待开始  3:审批驳回 4:取消计划 5:已开始  6：已结束)")
				//审批条件
				if(!StringUtil.isEmpty(hrmsTrainPlan.getAuditType() )){
					//待审批
					if("1".equals(hrmsTrainPlan.getAuditType())) {
						List<Integer> list = new ArrayList<>();
						list.add(1);
						criteria.andIn("planState", list);
						page.setSidx("create_date");
						page.setSord("desc");
					}else if("2".equals(hrmsTrainPlan.getAuditType())) {
						//已审批
						List<Integer> list = new ArrayList<>();
						list.add(2);
						list.add(3);
						list.add(5);
						list.add(6);
						criteria.andIn("planState", list);
						page.setSidx("create_date");
						page.setSord("desc");
					}else if("3".equals(hrmsTrainPlan.getAuditType())){
						//3 全部
						List<Integer> list = new ArrayList<>();
						list.add(1);
						list.add(2);
						list.add(3);
						list.add(5);
						list.add(6);
						criteria.andIn("planState", list);
						page.setSidx("create_date");
						page.setSord("desc");
					}
				}
		
		if(StringUtils.isNotBlank(hrmsTrainPlan.getTrainTopic())) {
			criteria.andLike("trainTopic","%"+hrmsTrainPlan.getTrainTopic()+"%");
		}
		
		if(StringUtils.isNotBlank(hrmsTrainPlan.getStartDate()) && StringUtils.isNotBlank(hrmsTrainPlan.getEndDate())) {
			criteria.andCondition("'" + hrmsTrainPlan.getStartDate() +" 00:00:00" + "' < train_end_time and train_start_time < '" + hrmsTrainPlan.getEndDate() +" 23:59:59" + "'");
		}
		
		//申请时间
		if(StringUtils.isNotBlank(hrmsTrainPlan.getSearchStartCreateDate()) && StringUtils.isNotBlank(hrmsTrainPlan.getSearchEndCreateDate())) {
			criteria.andCondition("create_date >= '" + hrmsTrainPlan.getSearchStartCreateDate() + " 00:00:00'" + "  and create_date <= '" + hrmsTrainPlan.getSearchEndCreateDate() + " 23:59:59'");
		}
		
		
		//审批人
		if(StringUtils.isNotBlank(hrmsTrainPlan.getCanceledEmp())) {
			criteria.andLike("canceledEmp","%"+hrmsTrainPlan.getCanceledEmp()+"%");
		}
		//审批意见
		if(StringUtils.isNotBlank(hrmsTrainPlan.getApprovalComment())) {
			if("3".equals(hrmsTrainPlan.getApprovalComment())) {
				criteria.andEqualTo("planState", "3");
			}else {
				List<Integer> list = new ArrayList<>();
				list.add(2);
				list.add(5);
				list.add(6);
				criteria.andIn("planState", list);
			}
		}
		
		if(StringUtils.isNotBlank(hrmsTrainPlan.getApplicantName())) {
			criteria.andLike("applicantName","%"+hrmsTrainPlan.getApplicantName()+"%");
		}
		
		if(StringUtils.isNotBlank(hrmsTrainPlan.getApplicantDeptName())) {
			criteria.andLike("applicantDeptName","%"+hrmsTrainPlan.getApplicantDeptName()+"%");
		}
		
		if(StringUtils.isNotBlank(hrmsTrainPlan.getTrainType())) {
			criteria.andLike("trainType","%"+hrmsTrainPlan.getTrainType()+"%");
		}
		
		if(StringUtils.isNotBlank(hrmsTrainPlan.getTrainLecturer())) {
			criteria.andLike("trainLecturer","%"+hrmsTrainPlan.getTrainLecturer()+"%");
		}
		//培训层级
		if(StringUtils.isNotBlank(hrmsTrainPlan.getPlaceLevel())) {
			criteria.andEqualTo("placeLevel", hrmsTrainPlan.getPlaceLevel());
		}

		
		if(null != hrmsTrainPlan.getPlanState()) {
			criteria.andEqualTo("planState", hrmsTrainPlan.getPlanState());
		}
		
		if(StringUtils.isNotBlank(hrmsTrainPlan.getApplicantStartDate()) && StringUtils.isNotBlank(hrmsTrainPlan.getApplicantEndDate())) {
			criteria.andCondition("create_date >= '" + hrmsTrainPlan.getSearchStartCreateDate() + " 00:00:00'" + "  and create_date <= '" + hrmsTrainPlan.getSearchEndCreateDate() + " 23:59:59'");
		}
		
		//审批时间
		if(StringUtils.isNotBlank(hrmsTrainPlan.getCanceledStartDate()) && StringUtils.isNotBlank(hrmsTrainPlan.getCanceledEndDate())) {
			if (DataBaseProvider.databaseId.equalsIgnoreCase("mysql")) {
				criteria.andCondition("DATE_FORMAT(canceled_date,'%Y-%m-%d %H:%i:%s') >= '" 
						+ hrmsTrainPlan.getCanceledStartDate() + " 00:00:00'" + "  and DATE_FORMAT(canceled_date,'%Y-%m-%d %H:%i:%s') <= '" 
						+ hrmsTrainPlan.getCanceledEndDate() + " 23:59:59'");
			}
			if (DataBaseProvider.databaseId.equalsIgnoreCase("kingbase")){
				criteria.andCondition("to_char(canceled_date) >= '" 
						+ hrmsTrainPlan.getCanceledStartDate() + " 00:00:00'" + "  and to_char(canceled_date) <= '" 
						+ hrmsTrainPlan.getCanceledEndDate() + " 23:59:59'");
			}
		}
		List<HrmsTrainPlan> dataList = hrmsTrainPlanMapper.selectByExampleAndRowBounds(example, page);
		


		Map<String, String> trainingTypeConvertDictMap = convertDictMap("TRAINING_TYPE");  //学历
		
		for (int i = 0; i < dataList.size(); i++) {
			
			dataList.get(i).setTrainTypeText(trainingTypeConvertDictMap.get(dataList.get(i).getTrainType()));  //培训类型字典
			
			if(0 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("草稿");
			}
			if(1 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("待审批");
			}
			if(2 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("待开始");		
			}
			if(3 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("审批驳回");
			}
			if(4 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("取消计划");
			}
			if(5 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("已开始");
			}
			if(6 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("已结束");
			}
		}
		return dataList;
	}



	@Override
	public HrmsTrainPlan selectById(String id) {
		HrmsTrainPlan bean = hrmsTrainPlanMapper.selectByPrimaryKey(id);
		Map<String, String> trainingTypeConvertDictMap = convertDictMap("TRAINING_TYPE");  
		bean.setTrainTypeText(trainingTypeConvertDictMap.get(bean.getTrainType()));
		return bean;
	}



	@Override
	@Transactional(readOnly = false)
	public void updateTrainPlanStauts() {
		Example example = new Example(HrmsTrainPlan.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<Integer> list = new ArrayList<>();
		list.add(0);
		list.add(3);
		list.add(4);
		list.add(6);
		criteria.andNotIn("planState",list);
		List<HrmsTrainPlan> hrmsTrainPlanList = hrmsTrainPlanMapper.selectByExample(example);
		if(CollectionUtils.isNotEmpty(hrmsTrainPlanList)){
			for (HrmsTrainPlan hrmsTrainPlan : hrmsTrainPlanList) {
				Date trainStartTime = hrmsTrainPlan.getTrainStartTime();
				Date trainEndTime = hrmsTrainPlan.getTrainEndTime();
				
				if(DateUtil.compare(new Date(),trainStartTime) >=  0){
					hrmsTrainPlan.setPlanState(5);
				}
				
				if(DateUtil.compare(new Date() ,trainEndTime) >= 0){
					hrmsTrainPlan.setPlanState(6);
				}
				
				hrmsTrainPlanMapper.updateByPrimaryKeySelective(hrmsTrainPlan);
			}
		}
		
	}


	@Transactional(readOnly = false)
	@Override
	public List<Map<String, Object>> getAttendanceRate(String type, String date) {
		if("3".equals(type)) {  //查询本年
			date = DateUtil.format(new Date(), "yyyy");
		}else if("4".equals(type)) {  //查询本月
			date = DateUtil.format(new Date(), "yyyy-MM");
		}
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		return hrmsTrainPlanMapper.getAttendanceRate(date,ssoOrgCode);
	}



	@Override
	@Transactional(readOnly = false)
	public List<Map<String, Object>> getIntegralTop(String type, String date) {
		//查询本年
		if("3".equals(type)) {
			date = DateUtil.format(new Date(), "yyyy");
		}else if("4".equals(type)) {
			//查询本月
			date = DateUtil.format(new Date(), "yyyy-MM");
		}
		//根据当前登录账号机构编码过滤查询数据
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		return hrmsTrainPlanMapper.getIntegralTop(date,ssoOrgCode);
	}


	//培训统计报表
	@Override
	@Transactional(readOnly = false)
	public List<HrmsTrainPlan> getStatistics(Page page, HrmsTrainPlan hrmsTrainPlan) {
		if(StringUtil.isEmpty(page.getSidx())) {
			page.setSidx("t2.train_start_time");
			page.setSord(" desc");
		}
		// TODO Auto-generated method stub
		//根据当前登录账号机构编码过滤查询数据
		hrmsTrainPlan.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsTrainPlanMapper.getStatistics(page,hrmsTrainPlan);
	}
	
	
    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }



	@Override
	public List<Map<String, Object>> getTypeTop10() {
		//根据当前登录账号机构编码过滤查询数据
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		//		Map<String, String> trainingTypeConvertDictMap = convertDictMap("TRAINING_TYPE");  //学历
//		if(typeTop10 != null && typeTop10.size() >0) {
//			for(int i=0;i<typeTop10.size();i++) {
//				String string = trainingTypeConvertDictMap.get(typeTop10.get(i).get("train_type"));
//				typeTop10.get(i).put("trainTypeText", string);
//			}
//		}
		return hrmsTrainPlanMapper.getTypeTop10(ssoOrgCode);
	}

	//判断 是否发送消息   会议小于当前时间不发送消息
	private static boolean checkDate(Date date) {
		boolean retVal = false;
		LocalDateTime now = LocalDateTime.now(); // 获取当前日期时间
		Instant instant = date.toInstant();
		LocalDateTime target = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
		// 比较当前日期时间和指定日期时间
		if (now.isAfter(target)) {
			retVal =  false;
		} else {
			retVal =  true;
		}
		return retVal;
	}
}

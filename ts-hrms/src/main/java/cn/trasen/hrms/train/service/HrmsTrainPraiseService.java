package cn.trasen.hrms.train.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.train.model.HrmsTrainPraise;

/**
 * @ClassName HrmsTrainPraiseService
 * @Description TODO
 * @date 2023��5��29�� ����9:43:43
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsTrainPraiseService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��5��29�� ����9:43:43
	 * <AUTHOR>
	 */
	Integer save(HrmsTrainPraise record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��5��29�� ����9:43:43
	 * <AUTHOR>
	 */
	Integer update(HrmsTrainPraise record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��5��29�� ����9:43:43
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsTrainPraise
	 * @date 2023��5��29�� ����9:43:43
	 * <AUTHOR>
	 */
	HrmsTrainPraise selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsTrainPraise>
	 * @date 2023��5��29�� ����9:43:43
	 * <AUTHOR>
	 */
	DataSet<HrmsTrainPraise> getDataSetList(Page page, HrmsTrainPraise record);
}

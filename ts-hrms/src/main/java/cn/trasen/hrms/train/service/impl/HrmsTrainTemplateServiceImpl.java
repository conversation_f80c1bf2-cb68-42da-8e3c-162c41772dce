package cn.trasen.hrms.train.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.train.dao.HrmsTrainTemplateMapper;
import cn.trasen.hrms.train.model.HrmsTrainTemplate;
import cn.trasen.hrms.train.service.HrmsTrainTemplateService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsTrainTemplateServiceImpl
 * @Description TODO
 * @date 2023��6��28�� ����10:23:25
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsTrainTemplateServiceImpl implements HrmsTrainTemplateService {

	@Autowired
	private HrmsTrainTemplateMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsTrainTemplate record) {
		
		//先验证是否存在相同名称模版
		
		Example example = new Example(HrmsTrainTemplate.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andEqualTo("title", record.getTitle().trim());
		List<HrmsTrainTemplate> records = mapper.selectByExample(example);
		if(records != null && records.size()>0) {
			record.setId(records.get(0).getId());
			return update(record);
		}else {
			record.setId(String.valueOf(IdWork.id.nextId()));
			record.setCreateDate(new Date());
			record.setUpdateDate(new Date());
			record.setIsDeleted("N");
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				record.setCreateUser(user.getUsercode());
				record.setCreateUserName(user.getUsername());
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			return mapper.insertSelective(record);	
		}
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsTrainTemplate record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsTrainTemplate record = new HrmsTrainTemplate();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsTrainTemplate selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsTrainTemplate> getDataSetList(Page page, HrmsTrainTemplate record) {
		Example example = new Example(HrmsTrainTemplate.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsTrainTemplate> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

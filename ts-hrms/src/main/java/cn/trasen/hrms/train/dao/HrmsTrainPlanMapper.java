package cn.trasen.hrms.train.dao;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.train.model.HrmsTrainPlan;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsTrainPlanMapper extends Mapper<HrmsTrainPlan> {

	List<Map<String, Object>> getAttendanceRate(@Param("date") String date, @Param("ssoOrgCode") String ssoOrgCode);

	/**
	 * 积分排行榜
	 * @param date
	 * @param ssoOrgCode
	 * @return
	 */
	List<Map<String, Object>> getIntegralTop(@Param("date") String date, @Param("ssoOrgCode") String ssoOrgCode);

	/**
	 * 培训统计报表列表
	 * @param page
	 * @param hrmsTrainPlan
	 * @return
	 */
	List<HrmsTrainPlan> getStatistics(Page page, HrmsTrainPlan hrmsTrainPlan);

	/**
	 * 培训类型top10
	 * @param ssoOrgCode
	 * @return
	 */
	List<Map<String, Object>> getTypeTop10(@Param("ssoOrgCode") String ssoOrgCode);

    List<HrmsTrainPlan> selectByUserCode(HrmsTrainPlan selectZhuti);
}
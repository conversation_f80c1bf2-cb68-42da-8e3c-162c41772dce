package cn.trasen.hrms.train.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.train.model.HrmsTrainPraise;
import cn.trasen.hrms.train.service.HrmsTrainPraiseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsTrainPraiseController
 * @Description TODO
 * @date 2023��5��29�� ����9:43:43
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsTrainPraiseController")
public class HrmsTrainPraiseController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsTrainPraiseController.class);

	@Autowired
	private HrmsTrainPraiseService hrmsTrainPraiseService;

	/**
	 * @Title saveHrmsTrainPraise
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��5��29�� ����9:43:43
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/trainpraise/trainpraise/save")
	public PlatformResult<String> saveHrmsTrainPraise(@RequestBody HrmsTrainPraise record) {
		try {
			hrmsTrainPraiseService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsTrainPraise
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��5��29�� ����9:43:43
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/trainpraise/trainpraise/update")
	public PlatformResult<String> updateHrmsTrainPraise(@RequestBody HrmsTrainPraise record) {
		try {
			hrmsTrainPraiseService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsTrainPraiseById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsTrainPraise>
	 * @date 2023��5��29�� ����9:43:43
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/trainpraise/trainpraise/{id}")
	public PlatformResult<HrmsTrainPraise> selectHrmsTrainPraiseById(@PathVariable String id) {
		try {
			HrmsTrainPraise record = hrmsTrainPraiseService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsTrainPraiseById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��5��29�� ����9:43:43
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/trainpraise/trainpraise/delete/{id}")
	public PlatformResult<String> deleteHrmsTrainPraiseById(@PathVariable String id) {
		try {
			hrmsTrainPraiseService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsTrainPraiseList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsTrainPraise>
	 * @date 2023��5��29�� ����9:43:43
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/trainpraise/trainpraise/list")
	public DataSet<HrmsTrainPraise> selectHrmsTrainPraiseList(Page page, HrmsTrainPraise record) {
		return hrmsTrainPraiseService.getDataSetList(page, record);
	}
}

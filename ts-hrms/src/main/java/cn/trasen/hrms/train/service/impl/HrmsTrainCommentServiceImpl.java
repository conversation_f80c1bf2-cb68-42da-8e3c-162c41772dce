package cn.trasen.hrms.train.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.train.dao.HrmsTrainCommentMapper;
import cn.trasen.hrms.train.model.HrmsTrainComment;
import cn.trasen.hrms.train.service.HrmsTrainCommentService;

/**
 * @ClassName HrmsTrainCommentServiceImpl
 * @Description TODO
 * @date 2023��5��18�� ����4:30:31
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsTrainCommentServiceImpl implements HrmsTrainCommentService {

	@Autowired
	private HrmsTrainCommentMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsTrainComment record) {
		record.setTrainCommentId(String.valueOf(IdWork.id.nextId()));
		record.setCreateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsTrainComment record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsTrainComment record = new HrmsTrainComment();
		record.setTrainCommentId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//有子评论就先删除子评论
		HrmsTrainComment delRecord = new HrmsTrainComment();
		delRecord.setParentId(id);
		mapper.delete(delRecord);
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsTrainComment selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsTrainComment> getDataSetList(Page page, HrmsTrainComment record) {
		page.setSidx("create_date");
		page.setSord("desc");
		Assert.hasText(record.getTrainPlanId(), "培训id不能为空");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		record.setCreateUser(user.getUsercode());
		//根据当前登录账号机构编码过滤查询数据
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsTrainComment> records = mapper.getDataSetList(page,record);
		if (records != null && records.size() > 0) {
			for (int i = 0; i < records.size(); i++) {
				List<HrmsTrainComment> children = getChildren(records.get(i).getTrainCommentId(),user.getUsercode());
				records.get(i).setChildren(children);
			}
		}
		
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	private List<HrmsTrainComment> getChildren(String parentId,String createUser) {
		return mapper.getChildren(parentId,createUser);
	}

	/**
	 *评论管理列表
	 */
	@Override
	public DataSet<HrmsTrainComment> managementlist(Page page, HrmsTrainComment record) {
		if(StringUtil.isEmpty(page.getSidx())) {
			page.setSidx("t1.create_date");
			page.setSord(" desc");
		}
		//根据当前登录账号机构编码过滤查询数据
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsTrainComment> records = mapper.managementlist(page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

package cn.trasen.hrms.train.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.train.model.HrmsTrainPlan;

/**
 * @ClassName: HrmsTrainPlanService  
 * @Description: 培训计划
 * <AUTHOR>
 * @date 2020年5月23日 上午8:44:12
 */
public interface HrmsTrainPlanService {

	/**
	 * @Title: insert  
	 * @Description: 新增计划
	 * @param hrmsTrainPlan
	 * @return    参数  
	 * PlatformResult<String>    返回类型  
	 * @throws
	 */
	void insert(HrmsTrainPlan hrmsTrainPlan);

	/**
	 * @Title: update  
	 * @Description: 修改计划信息
	 * @param hrmsTrainPlan
	 * @return    参数  
	 * int    返回类型  
	 * @throws
	 */
	void update(HrmsTrainPlan hrmsTrainPlan);
	
	/**
	 * @Title: deleteById  
	 * @Description: 通过Id删除数据 
	 * @param trainPlanId
	 * @return    参数  
	 * int    返回类型  
	 * @throws
	 */
	void deleteById(String trainPlanId);

	/**
	 * @Title: getDataList  
	 * @Description: 查询计划信息列表(分页)
	 * @param page
	 * @param hrmsTrainPlan
	 * @return    参数  
	 * List<HrmsTrainPlan>    返回类型  
	 * @throws
	 */
	List<HrmsTrainPlan> getDataList(Page page, HrmsTrainPlan hrmsTrainPlan);

	/**
	 * 
	 * @MethodName: selectById
	 * @Description: TODO
	 * <AUTHOR>
	 * @param id
	 * @return HrmsTrainPlan
	 * @date 2023-03-30 03:04:27
	 */
	HrmsTrainPlan selectById(String id);

	/**
	 * 
	 * @MethodName: updateTrainPlanStauts
	 * @Description: TODO
	 * <AUTHOR> void
	 * @date 2023-04-01 03:35:18
	 */
	void updateTrainPlanStauts();

	/**
	 * 到会率
	 * @param type
	 * @param date
	 * @return
	 */
	List<Map<String, Object>> getAttendanceRate(String type, String date);
	
	/**
	 * 积分排行榜
	 * @param type
	 * @param date
	 * @return
	 */
	List<Map<String, Object>> getIntegralTop(String type, String date);

	/**
	 * 培训统计报表
	 * @param page
	 * @param hrmsTrainPlan
	 * @return
	 */
	List<HrmsTrainPlan> getStatistics(Page page, HrmsTrainPlan hrmsTrainPlan);

	/**
	 * 培训类型top10
	 * @return
	 */
	List<Map<String, Object>> getTypeTop10();
	
}

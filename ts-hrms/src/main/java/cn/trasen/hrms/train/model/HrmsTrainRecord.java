package cn.trasen.hrms.train.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Table(name = "hrms_train_record")
@Setter
@Getter
@ToString
public class HrmsTrainRecord {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    /**
     * train_record_id
     */
    @Id
    @Column(name = "train_record_id")
    @ApiModelProperty(value = "train_record_id")
    private String trainRecordId;

    /**
     * employee_id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "employee_id")
    private String employeeId;


    /**
     * employee_name
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "employee_name")
    private String employeeName;
    
    @Column(name = "invited")
    @ApiModelProperty(value = "是否受邀人员  0否  1是")
    private Integer invited;
    

    /**
     * org_id
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "org_id")
    private String orgId;

    /**
     * org_name
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "org_name")
    private String orgName;

    /**
     * 培训主题Id
     */
    @Column(name = "train_plan_id")
    @ApiModelProperty(value = "培训主题Id")
    private String trainPlanId;

    /**
     * 培训主题
     */
    @Column(name = "train_topic")
    @ApiModelProperty(value = "培训主题")
    private String trainTopic;
    
    
    @Column(name = "sign_status")
    @ApiModelProperty(value = "签到状态  0未签到  1已签到")
    private Integer signStatus;
    
    /**
     * 签到时间
     */
    @Column(name = "sign_time")
    @ApiModelProperty(value = "签到时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date signTime;
    
    
    @Column(name = "sign_out_status")
    @ApiModelProperty(value = "签退状态  0未签退  1已签退")
    private Integer signOutStatus;
    
    /**
     * 签退时间
     */
    @Column(name = "sign_out_time")
    @ApiModelProperty(value = "签退时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date signOutTime;
    

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;
    
    @Column(name = "record_credit")
    @ApiModelProperty(value = "学分")
    private String recordCredit;
    
    /**
     * 学时
     */
    @Column(name = "record_credit_hour")
    @ApiModelProperty(value = "学时")
    private String recordCreditHour;
    
    

    /**
     * 考核结果(1:未考核 2:通过 3:不通过)
     */
    @Column(name = "evaluation_result")
    @ApiModelProperty(value = "考核结果(1:未考核 2:通过 3:不通过)")
    private Integer evaluationResult;
    
    /**
     * 结果
     */
    @Column(name = "result")
    @ApiModelProperty(value = "结果")
    private String result;
    
    /**
     * 结果
     */
    @Column(name = "sex")
    @ApiModelProperty(value = "结果")
    private String sex;
    
    @Transient
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date trainStartTime;
    
    @Transient
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date trainEndTime;
    
    @Transient
    private String trainLecturer;
    
    @Transient
    private String trainType;
    
    @Transient
    private String trainPlace;
    
    @Transient
    private String trainNumbers;
    
    @Transient
    private Integer planState;
    
    @Transient
    private String planStateText;
    
    @Transient
    @ApiModelProperty(value = "查询我的培训传Y")
    private String myPlan;
    
    
    @Transient
    @ApiModelProperty(value = "培训状态查询  1待培训  2已培训")
    private String recordStatus;
    
    /**
     * 培训开始时间（查询用）
     */
    @Transient
    private String startDate;
    
    /**
     * 培训结束时间（查询用）
     */
    @Transient
    private String endDate;
    
    @Transient
    @ApiModelProperty(value = "1 签到  2 签退")
    private String signType;  //1 签到  2 签退
    
    @Transient
    private Integer no;  //序号
    
    @Transient
    private String signStatusText;  //签到状态
    
    @Transient
    private String signOutStatusText;  //签退状态
   
    @Transient
    private String applicantName;
   
    @Transient
    private String applicantDeptName;
   
    @Transient
    private String trainTypeText;
  
    @Transient
    private String signTimeText;
    
    @Transient
    private String signOutTimeText;
   
    @Transient
    private String zjf;
    
    @Transient
    private String byjf;
   
    @Transient
    private String isComment; // 是不是已评论 1 已评论
    @Transient
    private String rankingType;
    
    @Transient
    private String placeLevel;  //培训等级
    
    @Transient
    private String placeCourseType;//课程类型
    
    @Transient
    private String wcbl;//

    @Transient
    private String wcbljf;//完成比例 场次



    @Transient
    private String excelType;

    @Transient
    private String searchSignStatus;

    @Transient
    private String searchSignOutStatus;
    @Transient
    private String deptName;


    
    
}
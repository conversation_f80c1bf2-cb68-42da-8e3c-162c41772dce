package cn.trasen.hrms.train.controller;

import java.util.Arrays;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.train.model.HrmsTrainComment;
import cn.trasen.hrms.train.service.HrmsTrainCommentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsTrainCommentController
 * @Description TODO
 * @date 2023��5��18�� ����4:30:31
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "培训管理评论HrmsTrainCommentController")
public class HrmsTrainCommentController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsTrainCommentController.class);

	@Autowired
	private HrmsTrainCommentService hrmsTrainCommentService;

	/**
	 * @Title saveHrmsTrainComment
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��5��18�� ����4:30:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/traincomment/save")
	public PlatformResult<String> saveHrmsTrainComment(@RequestBody HrmsTrainComment record) {
		try {
			hrmsTrainCommentService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsTrainComment
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��5��18�� ����4:30:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/traincomment/update")
	public PlatformResult<String> updateHrmsTrainComment(@RequestBody HrmsTrainComment record) {
		try {
			hrmsTrainCommentService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsTrainCommentById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsTrainComment>
	 * @date 2023��5��18�� ����4:30:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/traincomment/{id}")
	public PlatformResult<HrmsTrainComment> selectHrmsTrainCommentById(@PathVariable String id) {
		try {
			HrmsTrainComment record = hrmsTrainCommentService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsTrainCommentById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��5��18�� ����4:30:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/traincomment/delete/{id}")
	public PlatformResult<String> deleteHrmsTrainCommentById(@PathVariable String id) {
		try {
			hrmsTrainCommentService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/traincomment/delete/multiple/{ids}")
	public PlatformResult<String> deleteHrmsTrainCommentMultiple(@PathVariable String ids) {
		try {
			
			List<String> asList = Arrays.asList(ids.split(","));
			asList.forEach(item->{
				hrmsTrainCommentService.deleteById(item);
			});
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsTrainCommentList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsTrainComment>
	 * @date 2023��5��18�� ����4:30:31
	 * <AUTHOR>
	 */
	@ApiOperation(value = "根据培训计划查询评论", notes = "根据培训计划查询评论")
	@GetMapping("/api/traincomment/list")
	public DataSet<HrmsTrainComment> selectHrmsTrainCommentList(Page page, HrmsTrainComment record) {
		return hrmsTrainCommentService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "评论管理列表", notes = "评论管理列表")
	@GetMapping("/api/traincomment/managementlist")
	public DataSet<HrmsTrainComment> managementlist(Page page, HrmsTrainComment record) {
		return hrmsTrainCommentService.managementlist(page, record);
	}
}

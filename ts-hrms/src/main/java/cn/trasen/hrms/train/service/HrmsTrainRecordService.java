package cn.trasen.hrms.train.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.train.model.HrmsTrainRecord;

/**
 * @ClassName: HrmsTrainRecordService  
 * @Description: 培训记录
 * <AUTHOR>
 * @date 2020年5月23日 上午8:48:23
 */
public interface HrmsTrainRecordService {


	/**
	 * 
	 * @MethodName: signin
	 * @Description: TODO
	 * <AUTHOR>
	 * @param signinType
	 * @param trainPlanId void
	 * @date 2023-04-01 11:05:25
	 */
	String signin(String signinType, String trainPlanId);

	/**
	 * 
	 * @MethodName: getDataList
	 * @Description: TODO
	 * <AUTHOR>
	 * @param page
	 * @param hrmsTrainRecord
	 * @return List<HrmsTrainRecord>
	 * @date 2023-04-01 11:45:54
	 */
	List<HrmsTrainRecord> getDataList(Page page, HrmsTrainRecord hrmsTrainRecord);

	Map<String, Object> myDetails();

	/**
	 * 签到签退列表
	 * @param page
	 * @param hrmsTrainRecord
	 * @return
	 */
	List<HrmsTrainRecord> getSigninDataList(Page page, HrmsTrainRecord hrmsTrainRecord);

	/**
	 * 签到签退  二维码右边数字
	 * @param hrmsTrainRecord
	 * @return
	 */
	Map<String, Object> signInDetailsById(HrmsTrainRecord hrmsTrainRecord);

	/**
	 * 签到签退导出数据查询
	 * @param page
	 * @param hrmsTrainRecord
	 * @return
	 */
	List<HrmsTrainRecord> getSignInCountExport( HrmsTrainRecord hrmsTrainRecord);

	/**
	 * 我的培训三个统计
	 * @return
	 */
	Map<String, Object> myParticipate();

	List<HrmsTrainRecord> getSignInAllCountExport(HrmsTrainRecord hrmsTrainRecord);

	/**
	 * 查询未受邀人员
	 * @param hrmsTrainRecord
	 * @return
	 */
	List<HrmsTrainRecord> notInvited(HrmsTrainRecord hrmsTrainRecord);

	/**
	 * 赋予积分
	 * @param ids
	 * @param recordCredit
	 * @return
	 */
	int ascribeIntegral(List<HrmsTrainRecord> hrmsTrainRecords);

	/**
	 * 积分榜
	 * @param page
	 * @param record
	 * @return
	 */
	DataSet<HrmsTrainRecord> ranking(Page page, HrmsTrainRecord record);

	/**
	 * 根据id查询所有记录
	 * @param id
	 * @return
	 */
	List<HrmsTrainRecord> findListByEmpId(String id);

	//新增人员的接口
	String addSignin(HrmsTrainRecord record);

	void deleteById(String id);
}

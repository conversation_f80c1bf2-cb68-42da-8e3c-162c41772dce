package cn.trasen.hrms.train.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_train_score")
@Setter
@Getter
public class HrmsTrainScore {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    /**
     * 培训评论id
     */
	@Id
    @Column(name = "train_score_id")
    @ApiModelProperty(value = "培训评论id")
    private String trainScoreId;


    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    private Integer score;

    /**
     * 培训主题Id
     */
    @Column(name = "train_plan_id")
    @ApiModelProperty(value = "培训主题Id")
    private String trainPlanId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 评论者科室
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "评论者科室")
    private String orgName;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;
   
    @Transient
    @ApiModelProperty(value = "培训主题")
    private String trainTopic;  //查询条件
    
    @Transient
    @ApiModelProperty(value = "评分")
    private String pf;  
    
    @Transient
    @ApiModelProperty(value = "评论")
    private String pl; 
    
    @Transient
    @ApiModelProperty(value = "序号")
    private String no; 
    
    
}
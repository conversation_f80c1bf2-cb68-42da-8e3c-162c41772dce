//package cn.trasen.hrms.config;
//
//import com.zaxxer.hikari.HikariDataSource;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//import tk.mybatis.spring.annotation.MapperScan;
//
//import javax.sql.DataSource;
//import java.util.Properties;
//
///**
// * 数据源动态切换
// * <AUTHOR>
// *
// */
//
//@Configuration
//@MapperScan(basePackages = "cn.trasen.hrms.hlgl.mapper", sqlSessionFactoryRef = "hlglSqlSessionFactory")
//public class HlglDataSourceConfig {
//
//    @Bean(name = "hlglDataSource")
//    @ConfigurationProperties(prefix = "spring.hlgldatasource.hikari")
//    @RefreshScope
//    public DataSource getHlglDataSource() {
//        return new HikariDataSource();
//    }
//
//
//    /**
//     * @param datasource 数据源
//     */
//    @Bean(name = "hlglSqlSessionFactory")
//    public SqlSessionFactory hlglSqlSessionFactory(@Qualifier("hlglDataSource") DataSource datasource) throws Exception {
//        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
//        factoryBean.setDataSource(datasource);
//        factoryBean.setConfigLocation(new PathMatchingResourcePatternResolver().getResource("classpath:mybatis-config.xml"));
//        //下边两句仅仅用于*.xml文件，如果整个持久层操作不需要使用到xml文件的话（只用注解就可以搞定），则不加
//        factoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:cn/trasen/hrms/hlgl/mapper/*Mapper.xml"));
//        Properties properties=factoryBean.getObject().getConfiguration().getVariables();
//        properties.setProperty("dbType","mssql");
//        factoryBean.setConfigurationProperties(properties);
//        return factoryBean.getObject();
//    }
//
//    @Bean("hlglSqlSessionTemplate")
//    public SqlSessionTemplate hlglSqlSessionTemplate(@Qualifier("hlglSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//
//    @Bean(name = "hlglTransactionManager")
//    public DataSourceTransactionManager transactionManager(@Qualifier("hlglDataSource") DataSource dataSource){
//        return new DataSourceTransactionManager(dataSource);
//    }
//}
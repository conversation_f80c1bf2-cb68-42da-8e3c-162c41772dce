/*package cn.trasen.hrms.aspect;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


*//**
 * @ClassName: GetRequestModelAspect
 * @Description:获取请求参数模型
 * @author: WZH
 * @date: 2021年8月5日 上午11:25:49
 * @Copyright:
 *//*
@Aspect
@Component
public class GetRequestModelAspect {

	private final Logger logger = LoggerFactory.getLogger(GetRequestModelAspect.class);

	@Pointcut("execution(public * cn.trasen.hrms.controller..*.*(..))") // 切入点描述 这个是controller包的切入点
	public void controllerGetRequestModel() {
	}// 签名，可以理解成这个切入点的一个名称

	// 切入点描述，这个是uiController包的切入点
	@Pointcut("execution(public * com.stuPayment.uiController..*.*(..))") 
	public void uiControllerLog() {
	}

	@Before("controllerGetRequestModel()") // 在切入点的方法run之前要干的  多个切入点用 ( function1() || function2())
	public void getRequestModelBeforeController(JoinPoint joinPoint) {

		RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();// 这个RequestContextHolder是Springmvc提供来获得请求的东西
		HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();

		
		// 记录下请求内容
		logger.info("################requestBody : " + request.getAttribute("requestBody"));
		//只能拿到有requestBody 注解的对象  
		//拿到类之后 反射拿到所有属性 和注解的值
		
		
		logger.info("################URL : " + request.getRequestURL().toString());
		logger.info("################HTTP_METHOD : " + request.getMethod());
		logger.info("################IP : " + request.getRemoteAddr());
		logger.info("################THE ARGS OF THE CONTROLLER : " + Arrays.toString(joinPoint.getArgs()));

		// 下面这个getSignature().getDeclaringTypeName()是获取包+类名的
		// 然后后面的joinPoint.getSignature.getName()获取了方法名
		logger.info("################CLASS_METHOD : " + joinPoint.getSignature().getDeclaringTypeName() + "."
				+ joinPoint.getSignature().getName());
		// logger.info("################TARGET: " +
		// joinPoint.getTarget());//返回的是需要加强的目标类的对象
		// logger.info("################THIS: " +
		// joinPoint.getThis());//返回的是经过加强后的代理类的对象

	}
}
*/
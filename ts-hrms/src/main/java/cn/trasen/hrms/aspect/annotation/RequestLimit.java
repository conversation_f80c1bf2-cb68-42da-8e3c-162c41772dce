//package cn.trasen.hrms.aspect.annotation;
//
//import java.lang.annotation.Documented;
//import java.lang.annotation.Retention;
//import java.lang.annotation.Target;
//import java.lang.annotation.ElementType;
//import java.lang.annotation.RetentionPolicy;
//
//
//@Documented  
//@Target(ElementType.METHOD)  
//@Retention(RetentionPolicy.RUNTIME)  
//public @interface RequestLimit {  
//
//    // 限制时间 单位：秒(默认值：一分钟）  
//    long period() default 5;  
//
//    // 允许请求的次数(默认值：5次）  
//    long count() default 3;  
//    
//    //使用  @RequestLimit(count = 3)    直接打在接口上
//
//}

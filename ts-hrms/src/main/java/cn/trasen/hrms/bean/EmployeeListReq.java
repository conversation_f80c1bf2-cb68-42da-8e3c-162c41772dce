package cn.trasen.hrms.bean;

import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @createTime 2021/5/27 10:24
 * @description
 */
public class EmployeeListReq {

    @ApiModelProperty(value = "机构ID")
    String orgId;
    /**
     * 员工工号
     */
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 员工姓名
     */
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getEmployeeNo() {
        return employeeNo;
    }

    public void setEmployeeNo(String employeeNo) {
        this.employeeNo = employeeNo;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }
}

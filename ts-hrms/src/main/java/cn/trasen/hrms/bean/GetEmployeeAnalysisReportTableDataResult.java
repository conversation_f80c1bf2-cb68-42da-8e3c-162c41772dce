package cn.trasen.hrms.bean;


/**
 * 人员分析
 * <AUTHOR>
 *
 */
public class GetEmployeeAnalysisReportTableDataResult
{
	Integer num;
	String nationality;
	String gender;
	String politicalStatus;
	String educationType;
	Integer age;
	String postId;
	String postCategory;
	
	//兼职
	String concurrentPosition;
	String operationOrg;
	String jobDescriptionType;
	
	
	
	public String getJobDescriptionType() {
		return jobDescriptionType;
	}
	public void setJobDescriptionType(String jobDescriptionType) {
		this.jobDescriptionType = jobDescriptionType;
	}
	public String getOperationOrg() {
		return operationOrg;
	}
	public void setOperationOrg(String operationOrg) {
		this.operationOrg = operationOrg;
	}
	public String getConcurrentPosition() {
		return concurrentPosition;
	}
	public void setConcurrentPosition(String concurrentPosition) {
		this.concurrentPosition = concurrentPosition;
	}
	public Integer getNum() {
		return num;
	}
	public void setNum(Integer num) {
		this.num = num;
	}
	public String getNationality() {
		return nationality;
	}
	public void setNationality(String nationality) {
		this.nationality = nationality;
	}
	public String getGender() {
		return gender;
	}
	public void setGender(String gender) {
		this.gender = gender;
	}
	public String getPoliticalStatus() {
		return politicalStatus;
	}
	public void setPoliticalStatus(String politicalStatus) {
		this.politicalStatus = politicalStatus;
	}
	public String getEducationType() {
		return educationType;
	}
	public void setEducationType(String educationType) {
		this.educationType = educationType;
	}
	public Integer getAge() {
		return age;
	}
	public void setAge(Integer age) {
		this.age = age;
	}
	public String getPostId() {
		return postId;
	}
	public void setPostId(String postId) {
		this.postId = postId;
	}
	public String getPostCategory() {
		return postCategory;
	}
	public void setPostCategory(String postCategory) {
		this.postCategory = postCategory;
	}
	
	
}
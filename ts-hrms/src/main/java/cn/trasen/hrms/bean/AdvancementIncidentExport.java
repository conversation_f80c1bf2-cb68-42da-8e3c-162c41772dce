package cn.trasen.hrms.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 岗位晋级事件
 * @TableName hrms_advancement_incident
 */
@Table(name = "hrms_advancement_incident")
@Setter
@Getter
public class AdvancementIncidentExport implements Serializable {
    /**
     * 主键ID
     */
    @Id
    private String id;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String identityNumber;

    /**
     * 员工名字
     */
    @Excel(name = "姓名")
    private String employeeName;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 旧的岗位类别id
     */
    private String oldGwdj;
    /**
     * 新的岗位等级id
     */
    private String newGwdj;
    /**
     * 旧的薪级类别id
     */
    private String oldSalaryLevelId;
    /**
     * 新的薪级等级id
     */
    private String newSalaryLevelId;

    /**
     * 旧的岗位等级
     */
    @Excel(name = "原聘岗位")
    private String oldPlgw;

    /**
     * 新的岗位等级
     */
    @Excel(name = "现聘岗位")
    private String newPlgw;

    /**
     * 新的岗位工资
     */
    @Excel(name = "新的岗位工资")
    private BigDecimal newPostWage;

    /**
     * 旧的岗位工资
     */
    @Excel(name = "旧的岗位工资")
    private BigDecimal oldPostWage;

    /**
     * 新的绩效工资
     */
    @Excel(name = "新的绩效工资")
    private BigDecimal newPerformanceWage;

    /**
     * 旧的绩效工资
     */
    @Excel(name = "旧的绩效工资")
    private BigDecimal oldPerformanceWage;

    /**
     * 新的奖励性绩效工资
     */
    @Excel(name = "新的奖励性绩效工资")
    private BigDecimal newAwardWage;

    /**
     * 旧的奖励性绩效工资
     */
    @Excel(name = "旧的奖励性绩效工资")
    private BigDecimal oldAwardWage;

    @Excel(name = "新的薪级工资")
    @ApiModelProperty(value = "新的薪级工资")
    private BigDecimal newSalaryLevelWage;

    @Excel(name = "旧的薪级工资")
    @ApiModelProperty(value = "旧的薪级工资")
    private BigDecimal oldSalaryLevelWage;

    @Excel(name = "月增值")
    @ApiModelProperty(value = "月增值")
    private BigDecimal addSum;

    /**
     * 生效日期
     */
    private String effectiveDate;

    /**
     * 任职时间
     */
    private String jobDeionTypeTime;

    /**
     * 备注
     */
    private String remark;

    private String text;

    /**
     * 新的岗位工资
     */
    @Excel(name = "新的提高工资部分")
    private BigDecimal newTggz;

    /**
     * 旧的岗位工资
     */
    @Excel(name = "旧的提高工资部分")
    private BigDecimal oldTggz;

    /**
     * 新的岗位工资
     */
    @Excel(name = "新的护龄津贴")
    private BigDecimal newHljt;

    /**
     * 旧的岗位工资
     */
    @Excel(name = "旧的护龄津贴")
    private BigDecimal oldHljt;

    /**
     * 变动原因
     */
    private String reason;

    private static final long serialVersionUID = 1L;
}
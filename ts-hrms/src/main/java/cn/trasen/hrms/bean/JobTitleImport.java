package cn.trasen.hrms.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/11/10 14:02
 */

@Setter
@Getter
public class JobTitleImport {


    @Excel(name = "员工工号")
    String employeeNo;


    @Excel(name = "职称级别")
    String jobtitleCategory;


    @Excel(name = "职称等级")
    String jobtitleLevel;


    @Excel(name = "职称名称")
    String jobtitleName;

    @Excel(name = "取得时间")
    String acquisitionDate;

    @Excel(name = "是否最高")
    String highestLevel;
}
package cn.trasen.hrms.bean;


/**
 * 人员分布情况报表
 * <AUTHOR>
 *
 */
public class GetEmployeeDistributionReportTableDataResult
{
	Integer employeeCount;
	String orgId;
	Integer establishmentType;
	Integer jobDescriptionType;
	String jobtitleBasicName;
	public Integer getEmployeeCount() {
		return employeeCount;
	}
	public void setEmployeeCount(Integer employeeCount) {
		this.employeeCount = employeeCount;
	}
	public String getOrgId() {
		return orgId;
	}
	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	public Integer getEstablishmentType() {
		return establishmentType;
	}
	public void setEstablishmentType(Integer establishmentType) {
		this.establishmentType = establishmentType;
	}
	public Integer getJobDescriptionType() {
		return jobDescriptionType;
	}
	public void setJobDescriptionType(Integer jobDescriptionType) {
		this.jobDescriptionType = jobDescriptionType;
	}
	public String getJobtitleBasicName() {
		return jobtitleBasicName;
	}
	public void setJobtitleBasicName(String jobtitleBasicName) {
		this.jobtitleBasicName = jobtitleBasicName;
	}
	
}
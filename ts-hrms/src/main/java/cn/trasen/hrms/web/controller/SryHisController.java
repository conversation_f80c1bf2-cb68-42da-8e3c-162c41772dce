package cn.trasen.hrms.web.controller;

import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.hrms.med.cslt.controller.CsltAppyController;
import cn.trasen.hrms.web.model.CsltActVo;
import cn.trasen.hrms.web.model.CsltAppyVo;
import cn.trasen.hrms.web.model.CsltFnsDscrVo;
import cn.trasen.hrms.web.model.CsltFnsVo;
import cn.trasen.hrms.web.model.CsltScduDocVo;
import cn.trasen.hrms.web.model.CsltScduLvVo;
import cn.trasen.hrms.web.model.CsltScduVo;
import cn.trasen.hrms.web.model.WsCsltDto;
import cn.trasen.hrms.web.model.WsCsltLvDto;
import cn.trasen.hrms.web.model.WsDocDto;
import cn.trasen.hrms.web.model.WsResult;
import cn.trasen.hrms.web.service.SryHisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@Api(tags = "省人医HIS接口")
public class SryHisController {
	
	private transient static final Logger logger = LoggerFactory.getLogger(CsltAppyController.class);
	
	@Autowired
	private SryHisService sryHisService;
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据科室id + 会诊日期，获取会诊排班信息
	  -- 作者: GW
	  -- 创建时间: 2024年12月24日
	  -- @param record
	  -- @param bindingResult
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "根据科室id + 会诊日期，获取会诊排班信息列表", notes = "根据科室id + 会诊日期，获取会诊排班信息列表")
	@PostMapping("/api/sryHis/getCsltScdu")
	public WsResult<List<WsCsltDto>> getCsltScdu(@RequestBody @Valid CsltScduVo record,BindingResult bindingResult) {
		if (bindingResult.hasErrors()) {
			List<String> messages = new ArrayList<String>();
			bindingResult.getAllErrors().forEach(i -> {
				messages.add(i.getDefaultMessage());
			});
            return WsResult.failure(StrUtil.format("[{}]", CollUtil.join(messages, "]、[")));
        }
		try {
			return WsResult.success(sryHisService.getCsltScdu(record),"获取会诊排班信息成功！");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return WsResult.failure(e.getMessage());
		}
	}
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据会诊时间、会诊类别、会诊类型、被邀科室、院区，获取会诊级别的下拉列表
	  -- 作者: GW
	  -- 创建时间: 2024年11月25日
	  -- @param record
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "获取会诊级别下拉列表", notes = "获取会诊级别下拉列表")
	@PostMapping("/api/sryHis/getCsltScduLv")
	public WsResult<List<WsCsltLvDto>> getCsltScduLv(@RequestBody @Valid CsltScduLvVo record,BindingResult bindingResult) {
		if (bindingResult.hasErrors()) {
			List<String> messages = new ArrayList<String>();
			bindingResult.getAllErrors().forEach(i -> {
				messages.add(i.getDefaultMessage());
			});
            return WsResult.failure(StrUtil.format("[{}]", CollUtil.join(messages, "]、[")));
        }
		try {
			return WsResult.success(sryHisService.getCsltScduLv(record),"获取会诊级别下拉列表成功！");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return WsResult.failure(e.getMessage());
		}
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据会诊时间、会诊类别、会诊类型、被邀科室、院区、会诊级别，获取被邀医生的下拉列表
	  -- 作者: GW
	  -- 创建时间: 2024年11月25日
	  -- @param record
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "获取医生下拉列表", notes = "获取医生下拉列表")
	@PostMapping("/api/sryHis/getCsItScduDoc")
	public WsResult<List<WsDocDto>> getCsItScduDoc(@RequestBody @Valid CsltScduDocVo record,BindingResult bindingResult) {
		if (bindingResult.hasErrors()) {
			List<String> messages = new ArrayList<String>();
			bindingResult.getAllErrors().forEach(i -> {
				messages.add(i.getDefaultMessage());
			});
            return WsResult.failure(StrUtil.format("[{}]", CollUtil.join(messages, "]、[")));
        }
		try {
			return WsResult.success(sryHisService.getCsItScduDoc(record),"获取医生下拉列表成功！");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return WsResult.failure(e.getMessage());
		}
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 接收推送的单个会诊申请单信息
	  -- 作者: GW
	  -- 创建时间: 2024年11月25日
	  -- @param record
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "接收会诊申请信息", notes = "接收会诊申请信息")
	@PostMapping("/api/sryHis/receiveCsltAppyInfo")
	public WsResult<?> receiveCsltAppyInfo(@RequestBody @Valid CsltAppyVo record,BindingResult bindingResult) {
		if(!ReUtil.isMatch("0|1", record.getType())){
			return WsResult.failure("操作类型必须是 '0' 或 '1' ！");
		}
		if("1".equals(record.getType())){
			if (bindingResult.hasErrors()) {
				List<String> messages = new ArrayList<String>();
				bindingResult.getAllErrors().forEach(i -> {
					messages.add(i.getDefaultMessage());
				});
				return WsResult.failure(StrUtil.format("[{}]", CollUtil.join(messages, "]、[")));
			}
		}
		try {
			sryHisService.receiveCsltAppyInfo(record);
			return WsResult.success(StrUtil.format("{}会诊申请信息成功！","1".equals(record.getType()) ? "接收" : "撤销"));
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return WsResult.failure(e.getMessage());
		}
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据申请单ID，接收推送的单个会诊安排信息
	  -- 作者: GW
	  -- 创建时间: 2024年11月25日
	  -- @param record
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "接收会诊安排信息", notes = "接收会诊安排信息")
	@PostMapping("/api/sryHis/receiveCsltActInfo")
	public WsResult<?> receiveCsltActInfo(@RequestBody @Valid CsltActVo record,BindingResult bindingResult) {
		if(!ReUtil.isMatch("0|1", record.getType())){
			return WsResult.failure("操作类型必须是 '0' 或 '1' ！");
		}
		if("1".equals(record.getType())){
			if (bindingResult.hasErrors()) {
				List<String> messages = new ArrayList<String>();
				bindingResult.getAllErrors().forEach(i -> {
					messages.add(i.getDefaultMessage());
				});
				return WsResult.failure(StrUtil.format("[{}]", CollUtil.join(messages, "]、[")));
			}
		}
		try {
			sryHisService.receiveCsltActInfo(record);
			return WsResult.success(StrUtil.format("{}会诊安排信息成功！","1".equals(record.getType()) ? "接收" : "撤销"));
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return WsResult.failure(e.getMessage());
		}
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据会诊申请单ID，接收推送的单个会诊结果信息
	  -- 作者: GW
	  -- 创建时间: 2024年11月25日
	  -- @param record
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "接收会诊完成信息", notes = "接收会诊完成信息")
	@PostMapping("/api/sryHis/receiveCsltFnsInfo")
	public WsResult<?> receiveCsltFnsInfo(@RequestBody @Valid CsltFnsVo record,BindingResult bindingResult) {
		if(!ReUtil.isMatch("0|1", record.getType())){
			return WsResult.failure("操作类型必须是 '0' 或 '1' ！");
		}
		if("1".equals(record.getType())){
			if (bindingResult.hasErrors()) {
				List<String> messages = new ArrayList<String>();
				bindingResult.getAllErrors().forEach(i -> {
					messages.add(i.getDefaultMessage());
				});
				return WsResult.failure(StrUtil.format("[{}]", CollUtil.join(messages, "]、[")));
			}
		}
		try {
			sryHisService.receiveCsltFnsInfo(record);
			return WsResult.success(StrUtil.format("{}会诊完成信息成功！","1".equals(record.getType()) ? "接收" : "撤销"));
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return WsResult.failure(e.getMessage());
		}
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 接收会诊评价
	  -- 作者: GW
	  -- 创建时间: 2024年12月10日
	  -- @param record
	  -- @param bindingResult
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "接收会诊评价", notes = "接收会诊评价")
	@PostMapping("/api/sryHis/receiveCsltFnsDscr")
	public WsResult<?> receiveCsltFnsDscr(@RequestBody @Valid CsltFnsDscrVo record,BindingResult bindingResult) {
		if (bindingResult.hasErrors()) {
			List<String> messages = new ArrayList<String>();
			bindingResult.getAllErrors().forEach(i -> {
				messages.add(i.getDefaultMessage());
			});
			return WsResult.failure(StrUtil.format("[{}]", CollUtil.join(messages, "]、[")));
		}
		try {
			sryHisService.receiveCsltFnsDscr(record);
			return WsResult.success("接收会诊评价成功!");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return WsResult.failure(e.getMessage());
		}
	}
}

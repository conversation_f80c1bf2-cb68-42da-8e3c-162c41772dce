package cn.trasen.hrms.web.model;

import java.io.Serializable;

public class WsResult<T> implements Serializable{
	private static final long serialVersionUID = 8968766969795754248L;
	
	private String message;
	
	private int statusCode;
	
	private boolean success;
	
	private T wsData;
	
	public void setMessage(String message) {
		this.message = message;
	}
	
	public void setStatusCode(int statusCode) {
		this.statusCode = statusCode;
	}
	
	public void setSuccess(boolean success) {
		this.success = success;
	}
	
	public void setWsData(T wsData) {
		this.wsData = wsData;
	}
	
	public String toString() {
		return "PlatformResult(message=" + getMessage() + ", statusCode=" + getStatusCode() + ", success=" + isSuccess() + ", wsData=" + getWsData() + ")";
	}
	
	public String getMessage() {
		return this.message;
	}
	
	public int getStatusCode() {
		return this.statusCode;
	}
	
	public boolean isSuccess() {
		return this.success;
	}
	
	public T getWsData() {
		return this.wsData;
	}
	
	public static <T> WsResult<T> success() {
		WsResult<T> result = new WsResult<>();
		result.setSuccess(true);
		result.setStatusCode(200);
		return result;
	}
	
	public static <T> WsResult<T> success(String message) {
		WsResult<T> result = new WsResult<>();
		result.setSuccess(true);
		result.setStatusCode(200);
		result.setMessage(message);
		return result;
	}
	
	public static <T> WsResult<T> success(T data, String message) {
		WsResult<T> result = new WsResult<>();
		result.setSuccess(true);
		result.setStatusCode(200);
		result.setWsData(data);
		result.setMessage(message);
		return result;
	}
	
	public static <T> WsResult<T> failure() {
		WsResult<T> result = new WsResult<>();
		result.setSuccess(false);
		return result;
	}
	
	public static <T> WsResult<T> failure(String message) {
		WsResult<T> result = new WsResult<>();
		result.setSuccess(false);
		result.setMessage(message);
		return result;
	}
	
	public static <T> WsResult<T> failure(String message, T data) {
		WsResult<T> result = new WsResult<>();
		result.setSuccess(false);
		result.setMessage(message);
		result.setWsData(data);
		return result;
	}
}

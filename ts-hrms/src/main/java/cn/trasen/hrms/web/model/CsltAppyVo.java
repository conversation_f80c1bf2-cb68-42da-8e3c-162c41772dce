package cn.trasen.hrms.web.model;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CsltAppyVo {
	
	@ApiModelProperty(value = "会诊申请ID")
	@NotEmpty(message="会诊申请ID不能为空！")
	@Size(min = 1, max = 50, message = "会诊申请ID长度必须在1-50个字符之间！")
	private String csltAppyId;
	
	@ApiModelProperty(value = "操作类型：0取消 1申请")
	@NotEmpty(message="操作类型不能为空！")
	@Pattern(regexp = "0|1", message = "操作类型必须是 '0' 或 '1' ！")
	private String type;
	
	@ApiModelProperty(value = "会诊申请单号")
	@NotEmpty(message="会诊申请单号不能为空！")
	@Size(min = 1, max = 50, message = "会诊申请单号长度必须在1-50个字符之间！")
	private String appyNo;
	
	@ApiModelProperty(value = "会诊类别")
	@NotEmpty(message="会诊类别不能为空！")
	@Size(min = 1, max = 50, message = "会诊类别长度必须在1-50个字符之间！")
	private String csltType ;
	
	@ApiModelProperty(value = "会诊类型")
	@NotEmpty(message="会诊类型不能为空！")
	@Size(min = 1, max = 50, message = "会诊类型长度必须在1-50个字符之间！")
	private String csltMold ;
	
	@ApiModelProperty(value = "会诊级别")
	@NotEmpty(message="会诊级别不能为空！")
	@Size(min = 1, max = 50, message = "会诊级别长度必须在1-50个字符之间！")
	private String csltLv ;
	
	@ApiModelProperty(value = "院区")
	@NotEmpty(message="院区不能为空！")
	@Size(min = 1, max = 50, message = "院区长度必须在1-50个字符之间！")
	private String hospArea ;
	
	@ApiModelProperty(value = "科室ID")
	@NotEmpty(message="科室ID不能为空！")
	@Size(min = 1, max = 50, message = "科室ID长度必须在1-50个字符之间！")
	private String orgId ;
	
	@ApiModelProperty(value = "医生ID")
	@Size(max = 50, message = "医生ID长度不能超过50个字符！")
	private String employeeId ;
	
	@ApiModelProperty(value = "申请会诊时间")
	@NotEmpty(message="申请会诊时间不能为空！")
	//@Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "申请会诊时间格式必须为 YYYY-MM-DD")
	@Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "申请时间格式必须为 YYYY-MM-DD HH:MM:SS")
	private String csltTime ;
	
	@ApiModelProperty(value = "患者ID")
	@NotEmpty(message="患者ID不能为空！")
	@Size(min = 1, max = 50, message = "患者ID长度必须在1-50个字符之间！")
	private String patnId;
	
	@ApiModelProperty(value = "患者姓名")
	@Size(max = 50, message = "患者姓名长度不能超过50个字符！")
	private String patnName;
	
	@ApiModelProperty(value = "患者性别")
	@Size(max = 20, message = "性别长度不能超过20个字符！")
	private String patnGend;
	
	@ApiModelProperty(value = "患者年龄")
	@Size(max = 8, message = "患者年龄长度不能超过8个字符！")
	private String patnAge;
	
	@ApiModelProperty(value = "患者床号")
	@Size(max = 8, message = "患者床号长度不能超过8个字符！")
	private String patnBedno;
	
	@ApiModelProperty(value = "住院号")
	@Size(max = 20, message = "住院号长度不能超过20个字符！")
	private String patnInpNo;
	
	@ApiModelProperty(value = "患者所属科室")
	@Size(max = 100, message = "患者所属科室长度不能超过100个字符！")
	private String patnOrgName;
	
	@ApiModelProperty(value = "患者诊断")
	@Size(max = 200, message = "患者诊断长度不能超过200个字符！")
	private String patnIcdName;
	
	@ApiModelProperty(value = "申请时间")
	@NotEmpty(message="申请时间不能为空！")
	@Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "申请时间格式必须为 YYYY-MM-DD HH:MM:SS")
	private String appyTime;
	
	@ApiModelProperty(value = "申请科室名称")
	@NotEmpty(message="申请科室名称不能为空！")
	@Size(min = 1, max = 100, message = "申请科室名称长度必须在1-100个字符之间！")
	private String appyOrgName;
	
	@ApiModelProperty(value = "申请人姓名")
	@NotEmpty(message="申请人姓名不能为空！")
	@Size(min = 1, max = 50, message = "申请人姓名长度必须在1-50个字符之间！")
	private String appyEmpName;
	
	@ApiModelProperty(value = "申请人联系电话")
	@Size(max = 20, message = "申请人联系电话长度不能超过20个字符！")
	private String appyTel;
	
	@ApiModelProperty(value = "病史及检查")
	@Size(max = 1000, message = "病史及检查长度不能超过1000个字符！")
	private String illhis;
	
	@ApiModelProperty(value = "会诊目的")
	@Size(max = 1000, message = "会诊目的长度不能超过1000个字符！")
	private String pup;
	
	@ApiModelProperty(value = "操作人账号")
	@NotEmpty(message="操作人账号不能为空！")
	@Size(min = 1, max = 50, message = "操作人账号长度必须在1-50个字符之间！")
	private String oprtUser;
	
	@ApiModelProperty(value = "操作人姓名")
	@NotEmpty(message="操作人姓名不能为空！")
	@Size(min = 1, max = 50, message = "操作人姓名长度必须在1-50个字符之间！")
	private String oprtUserName;
	
	@ApiModelProperty(value = "操作人所属科室")
	@NotEmpty(message="操作人所属科室不能为空！")
	@Size(min = 1, max = 50, message = "操作人所属科室长度必须在1-50个字符之间！")
	private String oprtDept;
	
	@ApiModelProperty(value = "操作人所属科室名称")
	@NotEmpty(message="操作人所属科室名称不能为空！")
	@Size(min = 1, max = 100, message = "操作人所属科室名称长度必须在1-100个字符之间！")
	private String oprtDeptName;
	
	@ApiModelProperty(value = "申请科室ID")
	@NotEmpty(message="申请科室ID不能为空！")
	@Size(min = 1, max = 50, message = "申请科室ID长度必须在1-50个字符之间！")
	private String appyOrgId ;
	
	@ApiModelProperty(value = "申请科室院区")
	@NotEmpty(message="申请科室院区不能为空！")
	@Size(min = 1, max = 20, message = "申请科室院区长度必须在1-20个字符之间！")
	private String appyHospArea ;
	
	@ApiModelProperty(value = "申请人ID")
	@NotEmpty(message="申请人ID不能为空！")
	@Size(min = 1, max = 50, message = "申请人ID长度必须在1-50个字符之间！")
	private String appyEmpId ;
	
}

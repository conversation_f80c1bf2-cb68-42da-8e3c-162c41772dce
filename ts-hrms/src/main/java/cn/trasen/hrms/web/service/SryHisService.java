package cn.trasen.hrms.web.service;

import java.util.List;

import cn.trasen.hrms.web.model.CsltActVo;
import cn.trasen.hrms.web.model.CsltAppyVo;
import cn.trasen.hrms.web.model.CsltFnsDscrVo;
import cn.trasen.hrms.web.model.CsltFnsVo;
import cn.trasen.hrms.web.model.CsltScduDocVo;
import cn.trasen.hrms.web.model.CsltScduLvVo;
import cn.trasen.hrms.web.model.CsltScduVo;
import cn.trasen.hrms.web.model.WsCsltDto;
import cn.trasen.hrms.web.model.WsCsltLvDto;
import cn.trasen.hrms.web.model.WsDocDto;

/**
 * 
 -- =============================================
 --文件说明：湖南省人民医院医务管理系统 提供给HIS的接口服务
 --类名称: SryHisService
 --创建时间：2024年11月22日 
 --作者：GW
 -- =============================================
 */
public interface SryHisService {
	
	List<WsCsltDto> getCsltScdu(CsltScduVo vo);
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据会诊时间、会诊类别、会诊类型、被邀科室、院区，获取会诊级别的下拉列表
	  -- 作者: GW
	  -- 创建时间: 2024年11月22日
	  -- @return
	  -- =============================================
	 */
	List<WsCsltLvDto> getCsltScduLv(CsltScduLvVo vo);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据会诊时间、会诊类别、会诊类型、被邀科室、院区、会诊级别，获取被邀医生的下拉列表
	  -- 作者: GW
	  -- 创建时间: 2024年11月22日
	  -- @return
	  -- =============================================
	 */
	List<WsDocDto> getCsItScduDoc(CsltScduDocVo vo);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 接收推送的单个会诊申请单信息
	  -- 作者: GW
	  -- 创建时间: 2024年11月22日
	  -- @return
	  -- =============================================
	 */
	void receiveCsltAppyInfo(CsltAppyVo vo);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据申请单ID，接收推送的单个会诊安排信息
	  -- 作者: GW
	  -- 创建时间: 2024年11月22日
	  -- @return
	  -- =============================================
	 */
	void receiveCsltActInfo(CsltActVo vo);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据会诊申请单ID，接收推送的单个会诊结果信息
	  -- 作者: GW
	  -- 创建时间: 2024年11月22日
	  -- @return
	  -- =============================================
	 */
	void receiveCsltFnsInfo(CsltFnsVo vo);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 接收会诊评价
	  -- 作者: GW
	  -- 创建时间: 2024年12月10日
	  -- @param vo
	  -- =============================================
	 */
	void receiveCsltFnsDscr(CsltFnsDscrVo vo);
}

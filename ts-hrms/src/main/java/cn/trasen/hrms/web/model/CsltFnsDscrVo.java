package cn.trasen.hrms.web.model;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CsltFnsDscrVo {
	
	@ApiModelProperty(value = "会诊申请ID")
	@NotEmpty(message="会诊申请ID不能为空！")
	@Size(min = 1, max = 50, message = "会诊申请ID长度必须在1-50个字符之间！")
	private String csltAppyId;
	
    @ApiModelProperty(value = "会诊意见(申请科室)")
    @Size( max = 1000, message = "会诊意见(申请科室)不能超过1000个字符！")
    private String appyOrgDscr;

    @ApiModelProperty(value = "会诊意见(会诊科室)")
    @Size( max = 1000, message = "会诊意见(会诊科室)不能超过1000个字符！")
    private String csltOrgDscr;
    
	@ApiModelProperty(value = "会诊目的答复评分")
    @Size( max = 20, message = "会诊目的答复评分不能超过20个字符！")
    private String hzmddfScore;
	
	@ApiModelProperty(value = "会诊意见评分")
    @Size( max = 20, message = "会诊意见评分不能超过20个字符！")
    private String hzyjScore;
	
    @ApiModelProperty(value = "会诊目的评分")
    @Size( max = 20, message = "会诊目的评分不能超过20个字符！")
    private String hzmdScore;
    
    @ApiModelProperty(value = "会诊资料评分")
    @Size( max = 20, message = "会诊资料评分不能超过20个字符！")
    private String hzzlScore;
    
    @ApiModelProperty(value = "会诊必要性评分")
    @Size( max = 20, message = "会诊必要性评分不能超过20个字符！")
    private String hzbyxScore;
}

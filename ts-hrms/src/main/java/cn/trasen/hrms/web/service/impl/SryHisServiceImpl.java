package cn.trasen.hrms.web.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.DataBaseProvider;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.dao.HrmsOrganizationMapper;
import cn.trasen.hrms.med.comm.model.FieldMapping;
import cn.trasen.hrms.med.comm.service.FieldMappingService;
import cn.trasen.hrms.med.cslt.dao.CsltAppyMapper;
import cn.trasen.hrms.med.cslt.dao.CsltScduMapper;
import cn.trasen.hrms.med.cslt.model.CsltAppy;
import cn.trasen.hrms.med.cslt.model.CsltScdu;
import cn.trasen.hrms.model.CustEmpBase;
import cn.trasen.hrms.model.HrmsOrganization;
import cn.trasen.hrms.service.CustEmpBaseService;
import cn.trasen.hrms.web.model.CsltActVo;
import cn.trasen.hrms.web.model.CsltAppyVo;
import cn.trasen.hrms.web.model.CsltFnsDscrVo;
import cn.trasen.hrms.web.model.CsltFnsVo;
import cn.trasen.hrms.web.model.CsltScduDocVo;
import cn.trasen.hrms.web.model.CsltScduLvVo;
import cn.trasen.hrms.web.model.CsltScduVo;
import cn.trasen.hrms.web.model.WsCsltDto;
import cn.trasen.hrms.web.model.WsCsltLvDto;
import cn.trasen.hrms.web.model.WsDocDto;
import cn.trasen.hrms.web.service.SryHisService;
import tk.mybatis.mapper.entity.Example;
/**
 * 
 -- =============================================
 --文件说明：湖南省人民医院医务管理系统 提供给HIS的接口服务
 --类名称: SryHisServiceImpl
 --创建时间：2024年11月23日 
 --作者：GW
 -- =============================================
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class SryHisServiceImpl implements SryHisService {
	
	@Autowired
	DictItemFeignService dictItemFeignService;
	@Autowired
	private CsltScduMapper csltScduMapper;
	@Autowired
	private CsltAppyMapper csltAppyMapper;
	@Autowired
	HrmsOrganizationMapper hrmsOrganizationMapper;
	@Autowired
	CustEmpBaseService custEmpBaseService;
	@Autowired
	FieldMappingService fieldMappingService;
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据会诊时间、会诊类别、会诊类型、被邀科室、院区，获取会诊级别的下拉列表
	  -- 作者: GW
	  -- 创建时间: 2024年11月22日
	  -- @return
	  -- =============================================
	 */
	@Override
	public List<WsCsltLvDto> getCsltScduLv(CsltScduLvVo vo) {
		List<WsCsltLvDto> ls = new ArrayList<WsCsltLvDto>();
		try{
			//将传入的科室ID转换为本系统的orgId
			/*
			List<String> orgIds = hrmsOrganizationMapper.convertOrg(vo.getOrgId());
			if(CollUtil.isNotEmpty(orgIds) && orgIds.size() > 1){
				throw new RuntimeException(StrUtil.format("传入的科室ID→{}在HRMS中匹配了多条科室信息，请联系管理员！", vo.getOrgId()));
			}
			String orgId = CollUtil.isEmpty(orgIds) ? vo.getOrgId() : orgIds.get(0);
			*/
			List<String> orgIds = hrmsOrganizationMapper.convertOrgBusiSystem(vo.getOrgId(),"HIS","2");
			String orgId = CollUtil.isEmpty(orgIds) ? vo.getOrgId() : orgIds.get(0);
			Example example = new Example(CsltScdu.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("csltType", vo.getCsltType());
			criteria.andEqualTo("csltMold", vo.getCsltMold());
			criteria.andEqualTo("hospArea", vo.getHospArea());
			criteria.andEqualTo("orgId", orgId);
			criteria.andCondition(StrUtil.format(" exists(select 1 from med_cslt_scdu_detl d "
					+ "where med_cslt_scdu.id = d.scdu_id "
					+ "and d.is_deleted = 'n' "
					+ "and {} = '{}')", DataBaseProvider.databaseId.contains("mysql") ? "DATE_FORMAT(d.scdu_date,'%Y-%m-%d')" : "TO_CHAR(d.scdu_date,'yyyy-MM-dd')",vo.getScduDate()));
			List<CsltScdu> list = csltScduMapper.selectByExample(example);
			if(CollUtil.isNotEmpty(list)){
				List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
				List<String> csltLvs = list.stream().map(i -> i.getCsltLv()).distinct().collect(Collectors.toList());
				if(CollUtil.isNotEmpty(csltLvs)){
					csltLvs.forEach(i -> {
						WsCsltLvDto dto = new WsCsltLvDto();
						DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(i, j.getItemCode())).findFirst().orElse(null);
						dto.setCsltLv(i);
						dto.setCsltLvName(null == lv ? i : lv.getItemName());
						ls.add(dto);
					});
				}
			}
		}catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException("【HRMS】获取会诊级别列表失败，原因：" + e.getMessage());
		}
		return ls;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据会诊时间、会诊类别、会诊类型、被邀科室、院区、会诊级别，获取被邀医生的下拉列表
	  -- 作者: GW
	  -- 创建时间: 2024年11月22日
	  -- @return
	  -- =============================================
	 */
	@Override
	public List<WsDocDto> getCsItScduDoc(CsltScduDocVo vo) {
		List<WsDocDto> ls = new ArrayList<WsDocDto>();
		try{
			//将传入的科室ID转换为本系统的orgId
			/*
			List<String> orgIds = hrmsOrganizationMapper.convertOrg(vo.getOrgId());
			if(CollUtil.isNotEmpty(orgIds) && orgIds.size() > 1){
				throw new RuntimeException(StrUtil.format("传入的科室ID→{}在HRMS中匹配了多条科室信息，请联系管理员！", vo.getOrgId()));
			}
			String orgId = CollUtil.isEmpty(orgIds) ? vo.getOrgId() : orgIds.get(0);
			*/
			List<String> orgIds = hrmsOrganizationMapper.convertOrgBusiSystem(vo.getOrgId(),"HIS","2");
			String orgId = CollUtil.isEmpty(orgIds) ? vo.getOrgId() : orgIds.get(0);
			Example example = new Example(CsltScdu.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("csltType", vo.getCsltType());
			criteria.andEqualTo("csltMold", vo.getCsltMold());
			criteria.andEqualTo("csltLv", vo.getCsltLv());
			criteria.andEqualTo("hospArea", vo.getHospArea());
			criteria.andEqualTo("orgId", orgId);
			criteria.andCondition(StrUtil.format(" exists(select 1 from med_cslt_scdu_detl d "
					+ "where med_cslt_scdu.id = d.scdu_id "
					+ "and d.is_deleted = 'n' "
					+ "and {} = '{}')", DataBaseProvider.databaseId.contains("mysql") ? "DATE_FORMAT(d.scdu_date,'%Y-%m-%d')" : "TO_CHAR(d.scdu_date,'yyyy-MM-dd')",vo.getScduDate()));
			List<CsltScdu> list = csltScduMapper.selectByExample(example);
			if(CollUtil.isNotEmpty(list)){
//				List<String> empIds = list.stream().filter(i -> StrUtil.isNotEmpty(i.getEmployeeId())).map(i -> i.getEmployeeId()).distinct().collect(Collectors.toList());
//				if(CollUtil.isNotEmpty(empIds)){
//					empIds.forEach(i -> {
//						CustEmpBase emp = custEmpBaseService.selectById(i);
//						if(null != emp){
//							String hisEmployeeId = fieldMappingService.getMappingVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",emp.getEmployeeId()));
//							if(StrUtil.isNotEmpty(hisEmployeeId)){
//								WsDocDto dto = new WsDocDto();
//								dto.setEmployeeId(hisEmployeeId);
//								dto.setEmployeeNo(emp.getEmployeeNo()); //HIS没使用这个字段，直接传本系统的工号
//								dto.setEmployeeName(emp.getEmployeeName());
//								dto.setPyCode(StrUtil.isNotEmpty(emp.getEmployeeName()) ? PinyinUtil.getFirstLetter(emp.getEmployeeName(), "").toUpperCase() : "");
//								dto.setWbCode(StrUtil.isNotEmpty(emp.getEmployeeName()) ? StrConvertPinyin.getWBCode(emp.getEmployeeName()).toUpperCase() : "");
//								dto.setPhone(emp.getPhoneNumber());
//								ls.add(dto);
//							}
//						}
//					});
//				}
			}
		}catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException("【HRMS】获取医生列表失败，原因：" + e.getMessage());
		}
		return ls;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 接收推送的单个会诊申请单信息  如果传入重复的ID，则是更新操作
	  -- 作者: GW
	  -- 创建时间: 2024年11月22日
	  -- @return
	  -- =============================================
	 */
	@Transactional(readOnly = false)
	@Override
	public void receiveCsltAppyInfo(CsltAppyVo vo) {
		CsltAppy appy = csltAppyMapper.selectByPrimaryKey(vo.getCsltAppyId());
		if("0".equals(vo.getType()) && null != appy){
			if("1".equals(appy.getActStatus())){
				throw new RuntimeException("【HRMS】撤销会诊申请信息失败，原因：当前会诊申请信息已安排！");
			}
			if("1".equals(appy.getCsltStatus())){
				throw new RuntimeException("【HRMS】撤销会诊申请信息失败，原因：当前会诊申请信息已会诊！");
			}
			appy.setIsDeleted("Y");
			csltAppyMapper.updateByPrimaryKey(appy);
		}else{
			//将传入的科室ID转换为本系统的orgId
			/*
			List<String> orgIds = hrmsOrganizationMapper.convertOrg(vo.getOrgId());
			if(CollUtil.isNotEmpty(orgIds) && orgIds.size() > 1){
				throw new RuntimeException(StrUtil.format("传入的科室ID→{}在HRMS中匹配了多条科室信息，请联系管理员！", vo.getOrgId()));
			}
			String orgId = CollUtil.isEmpty(orgIds) ? vo.getOrgId() : orgIds.get(0);
			*/
			List<String> orgIds = hrmsOrganizationMapper.convertOrgBusiSystem(vo.getOrgId(),"HIS","2");
			String orgId = CollUtil.isEmpty(orgIds) ? vo.getOrgId() : orgIds.get(0);
			
			HrmsOrganization org = hrmsOrganizationMapper.selectByPrimaryKey(orgId);
			if(null == org){
				throw new RuntimeException(StrUtil.format("【HRMS】接收会诊申请单信息失败，原因：传入的科室ID{}未找到对应的科室信息！",vo.getOrgId()));
			}
			
			//将传入的申请科室ID转换为本系统的orgId
			/*
			List<String> appyOrgIds = hrmsOrganizationMapper.convertOrg(vo.getAppyOrgId());
			String appyOrgId = CollUtil.isEmpty(appyOrgIds) ? vo.getAppyOrgId() : appyOrgIds.get(0);
			*/
			List<String> appyOrgIds = hrmsOrganizationMapper.convertOrgBusiSystem(vo.getAppyOrgId(),"HIS","2");
			String appyOrgId = CollUtil.isEmpty(appyOrgIds) ? vo.getAppyOrgId() : appyOrgIds.get(0);
			HrmsOrganization appyOrg = hrmsOrganizationMapper.selectByPrimaryKey(appyOrgId);
//			CustEmpBase emp = null;
//			if(StrUtil.isNotEmpty(vo.getEmployeeId())){
//				//employee_id 使用映射转换成本系统的
//				String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",vo.getEmployeeId()));
//				if(StrUtil.isEmpty(currentEmployeeId)){
//					throw new RuntimeException(StrUtil.format("【HRMS】接收会诊申请单信息失败，原因：传入的医生ID:{}未找到映射关系！",vo.getEmployeeId()));
//				}
//				emp = custEmpBaseService.selectById(currentEmployeeId);
//				if(null == emp){
//					throw new RuntimeException(StrUtil.format("【HRMS】接收会诊申请单信息失败，原因：传入的医生ID:{}->{}未找到对应的医生信息！",vo.getEmployeeId(),currentEmployeeId));
//				}
//			}
			if(null != appy){
				if("0".equals(appy.getActStatus()) && "0".equals(appy.getCsltStatus())){
					BeanUtil.copyProperties(vo, appy);
					appy.setCsltOrgId(org.getOrganizationId());
					appy.setCsltOrgName(org.getName());
					if(appyOrg !=null) {
						appy.setAppyOrgId(appyOrg.getOrganizationId());
						appy.setAppyOrgName(appyOrg.getName());
					}else {
						appy.setAppyOrgId(vo.getAppyOrgId());
						appy.setAppyOrgName(vo.getAppyOrgName());
					}
//					appy.setCsltEmployeeNo(null == emp ? null : emp.getEmployeeNo());
//					appy.setCsltEmployeeId(null == emp ? null : emp.getEmployeeId());
//					appy.setCsltEmployeeName(null == emp ? null : emp.getEmployeeName());
//					appy.setCsltEmployeeTel(null == emp ? null : emp.getPhoneNumber());
					appy.setCsltTime(DateUtil.parse(vo.getCsltTime()));
					appy.setAppyTime(DateUtil.parse(vo.getAppyTime()));
					appy.setUpdateDate(DateUtil.date());
					appy.setUpdateUser(vo.getOprtUser());
					appy.setUpdateUserName(vo.getOprtUserName());
					appy.setIsDeleted("N");
					csltAppyMapper.updateByPrimaryKey(appy);
				}else{
					throw new RuntimeException(StrUtil.format("【HRMS】更新会诊申请单信息失败，原因：该会诊申请单{}！", !"0".equals(appy.getActStatus()) ? "已安排" : "已会诊"));
				}
			}else{
//				Example example = new Example(CsltScdu.class);
//				Example.Criteria criteria = example.createCriteria();
//				criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
//				criteria.andEqualTo("csltType", vo.getCsltType());
//				criteria.andEqualTo("csltMold", vo.getCsltMold());
//				criteria.andEqualTo("csltLv", vo.getCsltLv());
//				criteria.andEqualTo("hospArea", vo.getHospArea());
//				criteria.andEqualTo("orgId", orgId);
//				criteria.andCondition(StrUtil.format(" exists(select 1 from med_cslt_scdu_detl d "
//						+ "where med_cslt_scdu.id = d.scdu_id "
//						+ "and d.is_deleted = 'n' "
//						+ "and {} = '{}')", DataBaseProvider.databaseId.contains("mysql") ? "DATE_FORMAT(d.scdu_date,'%Y-%m-%d')" : "TO_CHAR(d.scdu_date,'yyyy-MM-dd')",vo.getCsltTime()));
//				if(StrUtil.isNotEmpty(vo.getEmployeeId())){
//					criteria.andEqualTo("employeeId",emp.getEmployeeId());
//				}
//				if(csltScduMapper.selectCountByExample(example) > 0){
					appy = new CsltAppy();
					BeanUtil.copyProperties(vo, appy);
					appy.setId(vo.getCsltAppyId());
					appy.setCsltOrgId(org.getOrganizationId());
					appy.setCsltOrgName(org.getName());
					if(appyOrg !=null) {
						appy.setAppyOrgId(appyOrg.getOrganizationId());
						appy.setAppyOrgName(appyOrg.getName());
					}else {
						appy.setAppyOrgId(vo.getAppyOrgId());
						appy.setAppyOrgName(vo.getAppyOrgName());
					}
					appy.setCsltTime(DateUtil.parse(vo.getCsltTime()));
//					appy.setCsltEmployeeNo(null == emp ? null : emp.getEmployeeNo());
//					appy.setCsltEmployeeId(null == emp ? null : emp.getEmployeeId());
//					appy.setCsltEmployeeName(null == emp ? null : emp.getEmployeeName());
//					appy.setCsltEmployeeTel(null == emp ? null : emp.getPhoneNumber());
					appy.setCreateDate(DateUtil.date());
					appy.setCreateUser(vo.getOprtUser());
					appy.setCreateUserName(vo.getOprtUserName());
					appy.setCreateDept(vo.getOprtDept());
					appy.setCreateDeptName(vo.getOprtDeptName());
					appy.setUpdateDate(DateUtil.date());
					appy.setUpdateUser(vo.getOprtUser());
					appy.setUpdateUserName(vo.getOprtUserName());
					appy.setIsDeleted("N");
					appy.setActStatus("0");
					appy.setIsActOt("0");
					appy.setIsActAbn("0");
					appy.setCsltStatus("0");
					appy.setIsCsltOt("0");
					appy.setIsCsltAbn("0");
					csltAppyMapper.insert(appy);
//				}else{
//					throw new RuntimeException("【HRMS】接收会诊申请单信息失败，原因：未找到会诊资源信息！");
//				}
			}
		}
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据申请单ID，接收推送的单个会诊安排信息
	  -- 作者: GW
	  -- 创建时间: 2024年11月22日
	  -- @return
	  -- =============================================
	 */
	@Transactional(readOnly = false)
	@Override
	public void receiveCsltActInfo(CsltActVo vo) {
		CsltAppy appy = csltAppyMapper.selectByPrimaryKey(vo.getCsltAppyId());
		if(null == appy){
			throw new RuntimeException(StrUtil.format("【HRMS】{}会诊安排信息失败，原因：传入的会诊申请ID{}未找到对应的申请信息！","1".equals(vo.getType()) ? "接收" : "撤销", vo.getCsltAppyId()));
		}
		//取消安排
		if("0".equals(vo.getType())){
			if("1".equals(appy.getActStatus())){
				appy.setActStatus("0");
				appy.setIsActOt("0");
				appy.setIsActAbn("0");
				appy.setActTime(null);
				appy.setActUser(null);
				appy.setActUserName(null);
				appy.setActEmployeeId(null);
				appy.setActEmployeeNo(null);
				appy.setActEmployeeName(null);
				appy.setActJobtitle(null);
				appy.setActTel(null);
				appy.setActStartTime(null);
				appy.setActEndTime(null);
				appy.setNotcFlag(null);
			}
			//安排
		}else{
			if(!"0".equals(appy.getActStatus())){
				throw new RuntimeException("【HRMS】接收会诊安排信息失败，原因：当前的会诊申请单状态不为未安排！");
			}
//			if(DateUtil.compare(DateUtil.parse(vo.getActTime()),appy.getAppyTime()) < 0){
//				throw new RuntimeException(StrUtil.format("【HRMS】接收会诊安排信息失败，原因：安排时间{}不能早于申请时间{}！",vo.getActTime(),DateUtil.format(appy.getAppyTime(),"yyyy-MM-dd HH:mm:ss")));
//			}
//			if(DateUtil.compare(DateUtil.parse(vo.getActStartTime()),appy.getCsltTime()) < 0){
//				throw new RuntimeException(StrUtil.format("【HRMS】接收会诊安排信息失败，原因：计划会诊时间{}不能早于申请会诊时间{}！",vo.getActStartTime(),DateUtil.format(appy.getCsltTime(),"yyyy-MM-dd HH:mm:ss")));
//			}
//			if(DateUtil.compare(DateUtil.parse(vo.getActEndTime()),appy.getCsltTime()) < 0){
//				throw new RuntimeException(StrUtil.format("【HRMS】接收会诊安排信息失败，原因：计划会诊时间{}不能早于申请会诊时间{}！",vo.getActEndTime(),DateUtil.format(appy.getCsltTime(),"yyyy-MM-dd HH:mm:ss")));
//			}
			//act_employee_id 使用映射转换成本系统的
			String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",vo.getActEmployeeId()));
			if(StrUtil.isEmpty(currentEmployeeId)){
				throw new RuntimeException(StrUtil.format("【HRMS】接收会诊安排信息失败，原因：传入的医生ID:{}未找到映射关系！",vo.getActEmployeeId()));
			}
			CustEmpBase emp = custEmpBaseService.selectById(currentEmployeeId);
			if(null == emp){
				throw new RuntimeException(StrUtil.format("【HRMS】接收会诊安排信息失败，原因：传入的医生ID:{}->{}未找到对应的医生信息！",vo.getActEmployeeId(),currentEmployeeId));
			}
			//申请时间  和 安排时间  相差的小时数
			Long l = DateUtil.between(appy.getAppyTime(),DateUtil.parse(vo.getActTime()), DateUnit.HOUR,false);
			BeanUtil.copyProperties(vo, appy);
			appy.setActEmployeeId(emp.getEmployeeId()); //ID 存本系统的，因为要统计被邀、安排、完成的是不是同一个医生， 其他的 HIS传什么就存什么
			appy.setActStatus("1");
			appy.setIsActOt(l > 24 ? "1" : "0");
			List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
			if(CollUtil.isNotEmpty(lvs)){
				DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(appy.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
				if(null != lv && StrUtil.isNotEmpty(lv.getItemName())){
					//是否异常会诊  排班资源中的会诊级别  与 完成医生的技术职称进行比较   申请的是主任医生，实际完成的是  主治医生
					appy.setIsActAbn(StrUtil.startWith(lv.getItemName(), vo.getActJobtitle()) ? "0" : "1"); 
				}
			}
		}
		appy.setUpdateDate(DateUtil.date());
		appy.setUpdateUser(vo.getOprtUser());
		appy.setUpdateUserName(vo.getOprtUserName());
		csltAppyMapper.updateByPrimaryKey(appy);
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据会诊申请单ID，接收推送的单个会诊结果信息
	  -- 作者: GW
	  -- 创建时间: 2024年11月22日
	  -- @return
	  -- =============================================
	 */
	@Transactional(readOnly = false)
	@Override
	public void receiveCsltFnsInfo(CsltFnsVo vo) {
		CsltAppy appy = csltAppyMapper.selectByPrimaryKey(vo.getCsltAppyId());
		if(null == appy){
			throw new RuntimeException(StrUtil.format("【HRMS】{}会诊结果信息失败，原因：传入的会诊申请ID{}未找到对应的申请信息！","1".equals(vo.getType()) ? "接收" : "撤销", vo.getCsltAppyId()));
		}
		//取消会诊
		if("0".equals(vo.getType())){
			if("1".equals(appy.getCsltStatus())){
				appy.setCsltStatus("0");
				appy.setIsCsltOt("0");
				appy.setIsCsltAbn("0");
				appy.setFnsTime(null);
				appy.setFnsEmployeeId(null);
				appy.setFnsEmployeeName(null);
				appy.setFnsEmployeeNo(null);
				appy.setFnsJobtitle(null);
				appy.setFnsTel(null);
				appy.setAppyOrgDscr(null);
				appy.setCsltOrgDscr(null);
			}
			//会诊
		}else{
			/*if(!"0".equals(appy.getCsltStatus())){
				throw new RuntimeException("【HRMS】接收会诊结果信息失败，原因：当前会诊申请单状态不为未会诊！");
			}*/
//			if(DateUtil.compare(DateUtil.parse(vo.getFnsTime()),appy.getAppyTime()) < 0){
//				throw new RuntimeException(StrUtil.format("【HRMS】接收会诊结果信息失败，原因：完成会诊时间{}不能早于申请时间{}！",vo.getFnsTime(),DateUtil.format(appy.getAppyTime(),"yyyy-MM-dd HH:mm:ss")));
//			}
//			if(DateUtil.compare(DateUtil.parse(vo.getFnsTime()),appy.getCsltTime()) < 0){
//				throw new RuntimeException(StrUtil.format("【HRMS】接收会诊结果信息失败，原因：完成会诊时间{}不能早于申请会诊时间{}！", vo.getFnsTime(),DateUtil.format(appy.getCsltTime(),"yyyy-MM-dd HH:mm:ss")));
//			}
			//Fns_employee_id 使用映射转换成本系统的
			String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",vo.getFnsEmployeeId()));
			if(StrUtil.isEmpty(currentEmployeeId)){
				throw new RuntimeException(StrUtil.format("【HRMS】接收会诊结果信息失败，原因：传入的医生ID:{}未找到映射关系！",vo.getFnsEmployeeId()));
			}
			CustEmpBase emp = custEmpBaseService.selectById(currentEmployeeId);
			if(null == emp){
				throw new RuntimeException(StrUtil.format("【HRMS】接收会诊结果信息失败，原因：传入的医生ID:{}->{}未找到对应的医生信息！",vo.getFnsEmployeeId(),currentEmployeeId));
			}
			//申请时间  和 完成会诊时间  相差的小时数
			Long l = DateUtil.between(appy.getAppyTime(),DateUtil.parse(vo.getFnsTime()), DateUnit.HOUR,false);
			BeanUtil.copyProperties(vo, appy);
			appy.setFnsEmployeeId(emp.getEmployeeId()); //ID 存本系统的，因为要统计被邀、安排、完成的是不是同一个医生， 其他的 HIS传什么就存什么
			appy.setCsltStatus("1");
			appy.setIsCsltOt(l > 24 ? "1" : "0");
			List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
			if(CollUtil.isNotEmpty(lvs)){
				DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(appy.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
				if(null != lv && StrUtil.isNotEmpty(lv.getItemName())){
					//是否异常会诊  排班资源中的会诊级别  与 完成医生的技术职称进行比较   申请的是主任医生，实际完成的是  主治医生
					appy.setIsCsltAbn(StrUtil.startWith(lv.getItemName(), StrUtil.replace(vo.getFnsJobtitle(), "正主任", "主任")) ? "0" : "1"); 
				}
			}
		}
		appy.setUpdateDate(DateUtil.date());
		appy.setUpdateUser(vo.getOprtUser());
		appy.setUpdateUserName(vo.getOprtUserName());
		//csltAppyMapper.updateByPrimaryKey(appy);
		csltAppyMapper.updateByPrimaryKeySelective(appy);
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 接收会诊评价
	  -- 作者: GW
	  -- 创建时间: 2024年12月10日
	  -- @param vo
	  -- =============================================
	 */
	@Transactional(readOnly = false)
	@Override
	public void receiveCsltFnsDscr(CsltFnsDscrVo vo) {
		CsltAppy appy = csltAppyMapper.selectByPrimaryKey(vo.getCsltAppyId());
		if(null != appy){
			appy.setAppyOrgDscr(vo.getAppyOrgDscr());
			appy.setCsltOrgDscr(vo.getCsltOrgDscr());
			appy.setHzmdScore(vo.getHzmdScore());
			appy.setHzzlScore(vo.getHzzlScore());
			appy.setHzbyxScore(vo.getHzbyxScore());
			appy.setHzmddfScore(vo.getHzmddfScore());
			appy.setHzyjScore(vo.getHzyjScore());
			//csltAppyMapper.updateByPrimaryKey(appy);
			csltAppyMapper.updateByPrimaryKeySelective(appy);
		}
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据科室id + 会诊日期，获取会诊排班信息
	  -- 作者: GW
	  -- 创建时间: 2024年12月24日
	  -- @param vo
	  -- @return
	  -- =============================================
	 */
	@Override
	public List<WsCsltDto> getCsltScdu(CsltScduVo vo) {
		List<WsCsltDto> ls = new ArrayList<WsCsltDto>();
		try{
			//将传入的科室ID转换为本系统的orgId
			/*
			List<String> orgIds = hrmsOrganizationMapper.convertOrg(vo.getOrgId());
			if(CollUtil.isNotEmpty(orgIds) && orgIds.size() > 1){
				throw new RuntimeException(StrUtil.format("传入的科室ID→{}在HRMS中匹配了多条科室信息，请联系管理员！", vo.getOrgId()));
			}
			String orgId = CollUtil.isEmpty(orgIds) ? vo.getOrgId() : orgIds.get(0);
			*/
			List<String> orgIds = hrmsOrganizationMapper.convertOrgBusiSystem(vo.getOrgId(), "HIS","2");
			String orgId = CollUtil.isEmpty(orgIds) ? vo.getOrgId() : orgIds.get(0);
			Example example = new Example(CsltScdu.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("orgId", orgId);
			criteria.andCondition(StrUtil.format(" exists(select 1 from med_cslt_scdu_detl d "
					+ "where med_cslt_scdu.id = d.scdu_id "
					+ "and d.is_deleted = 'n' "
					+ "and {} = '{}')", DataBaseProvider.databaseId.contains("mysql") ? "DATE_FORMAT(d.scdu_date,'%Y-%m-%d')" : "TO_CHAR(d.scdu_date,'yyyy-MM-dd')",vo.getScduDate()));
			List<CsltScdu> list = csltScduMapper.selectByExample(example);
			if(CollUtil.isNotEmpty(list)){
				List<DictItemResp> types = dictItemFeignService.getDictItemByTypeCode("cslt_type").getObject();
				List<DictItemResp> molds = dictItemFeignService.getDictItemByTypeCode("cslt_mold").getObject();
				List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
				List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
				orgIds = list.stream().filter(i -> StrUtil.isNotEmpty(i.getOrgId())).map(i -> i.getOrgId()).distinct().collect(Collectors.toList());
				Example e = new Example(HrmsOrganization.class);
				Example.Criteria c = e.createCriteria();
				c.andIn("organizationId", orgIds);
				List<HrmsOrganization> orgs = hrmsOrganizationMapper.selectByExample(e);
				list.forEach(i -> {
					DictItemResp type = types.stream().filter(j -> StrUtil.equals(i.getCsltType(), j.getItemCode())).findFirst().orElse(null);
					i.setCsltTypeName(null == type ? i.getCsltType() : type.getItemName());
					DictItemResp mold = molds.stream().filter(j -> StrUtil.equals(i.getCsltMold(), j.getItemCode())).findFirst().orElse(null);
					i.setCsltMoldName(null == mold ? i.getCsltMold() : mold.getItemName());
					DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(i.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
					i.setCsltLvName(null == lv ? i.getCsltLv() : lv.getItemName());
					DictItemResp area = areas.stream().filter(j -> StrUtil.equals(i.getHospArea(), j.getItemCode())).findFirst().orElse(null);
					i.setHospAreaName(null == area ? i.getHospArea() : area.getItemName());
					//转换为HIS的ID
//					HrmsOrganization org = orgs.stream().filter(j -> StrUtil.equals(i.getOrgId(), j.getOrganizationId())).findFirst().orElse(null);
//					i.setOrgId(null != org && StrUtil.isNotEmpty(org.getParentId()) ? org.getPlatformId() : i.getId());
					WsCsltDto dto = new WsCsltDto();
					BeanUtil.copyProperties(i, dto);
					ls.add(dto);
				});
			}
		}catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException("【HRMS】获取会诊级别列表失败，原因：" + e.getMessage());
		}
		return ls;
	}
}

package cn.trasen.hrms.web.model;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CsltScduVo {

	@ApiModelProperty(value = "科室ID")
	@NotEmpty(message="科室ID不能为空！")
	@Size(min = 1, max = 50, message = "科室ID长度必须在1-50个字符之间！")
	private String orgId ;
	
	@ApiModelProperty(value = "申请会诊时间")
	@NotEmpty(message="申请会诊时间不能为空！")
	@Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "申请会诊时间格式必须为 YYYY-MM-DD")
	private String scduDate ;
}

package cn.trasen.hrms.web.model;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CsltScduDocVo {
	
	@ApiModelProperty(value = "会诊类别")
	@NotEmpty(message="会诊类别不能为空！")
	@Size(min = 1, max = 50, message = "会诊类别长度必须在1-50个字符之间！")
	private String csltType ;
	
	@ApiModelProperty(value = "会诊类型")
	@NotEmpty(message="会诊类型不能为空！")
	@Size(min = 1, max = 50, message = "会诊类型长度必须在1-50个字符之间！")
	private String csltMold ;
	
	@ApiModelProperty(value = "院区")
	@NotEmpty(message="院区不能为空！")
	@Size(min = 1, max = 50, message = "院区长度必须在1-50个字符之间！")
	private String hospArea ;
	
	@ApiModelProperty(value = "申请会诊时间")
	@NotEmpty(message="申请会诊时间不能为空！")
	@Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "申请会诊时间格式必须为 YYYY-MM-DD")
	private String scduDate ;
	
	@ApiModelProperty(value = "科室ID")
	@NotEmpty(message="科室ID不能为空！")
	@Size(min = 1, max = 50, message = "科室ID长度必须在1-50个字符之间！")
	private String orgId ;
	
	@ApiModelProperty(value = "会诊级别")
	@NotEmpty(message="会诊级别不能为空！")
	@Size(min = 1, max = 50, message = "会诊级别长度必须在1-50个字符之间！")
	private String csltLv ;
}

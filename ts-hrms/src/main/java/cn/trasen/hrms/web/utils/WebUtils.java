package cn.trasen.hrms.web.utils;

import java.sql.SQLException;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.trasen.hrms.med.qua.model.QuaAuthCfg;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WebUtils {
	
	public static List<String> parseResult(String str){
		if(StrUtil.isEmpty(str)) return null;
		try {
			str = StrUtil.replace(str, "<?xml version=\"1.0\" encoding=\"utf-8\"?>", "");
			str = StrUtil.replace(str, "<string xmlns=\"http://tempuri.org/\">", "");
			str = StrUtil.replace(str, "</string>", "");
			JSONObject obj = JSONUtil.parseObj(str);
			JSONArray array = JSONUtil.parseArray(obj.get("SuccessDt"));
			if(CollUtil.isNotEmpty(array)) {
				return array.stream().map(i -> Convert.toStr(JSONUtil.parseObj(i).get("id"))).collect(Collectors.toList());
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("解析失败，原因："+e.getMessage(),e);
		}
		return null;
	}
	
	
	public static void main(String[] args) {
		try {
			List<QuaAuthCfg> result = Db.use().query("select * from MED_QUA_AUTH_CFG where id = '675d359dabc16e16187376c1' ", QuaAuthCfg.class);
			result.forEach(i -> {
				System.out.println(i.getItemName());
			});
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
}

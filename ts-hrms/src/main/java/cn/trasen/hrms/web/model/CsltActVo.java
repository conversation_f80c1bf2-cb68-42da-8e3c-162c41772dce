package cn.trasen.hrms.web.model;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CsltActVo {
	
	@ApiModelProperty(value = "会诊申请ID")
	@NotEmpty(message="会诊申请ID不能为空！")
	@Size(min = 1, max = 50, message = "会诊申请ID长度必须在1-50个字符之间！")
	private String csltAppyId;
	
	@ApiModelProperty(value = "操作类型：0取消 1安排")
	@NotEmpty(message="操作类型不能为空！")
	@Pattern(regexp = "0|1", message = "操作类型必须是 '0' 或 '1' ！")
	private String type;
	
	@ApiModelProperty(value = "安排时间")
	@NotEmpty(message="安排时间不能为空！")
	@Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "安排时间格式必须为 YYYY-MM-DD HH:MM:SS")
	private String actTime;
	
	@ApiModelProperty(value = "安排人账号")
	@NotEmpty(message="安排人账号不能为空！")
	@Size(min = 1, max = 50, message = "安排人账号长度必须在1-50个字符之间！")
	private String actUser;
	
	@ApiModelProperty(value = "安排人姓名")
	@NotEmpty(message="安排人姓名不能为空！")
	@Size(min = 1, max = 50, message = "安排人姓名长度必须在1-50个字符之间！")
	private String actUserName;
	
	@ApiModelProperty(value = "安排会诊医生ID")
	@Size(min = 1, max = 50, message = "安排会诊医生ID长度必须在1-50个字符之间！")
    private String actEmployeeId;

    @ApiModelProperty(value = "安排会诊医生姓名")
    @NotEmpty(message="安排会诊医生姓名不能为空！")
	@Size(min = 1, max = 50, message = "安排会诊医生姓名长度必须在1-50个字符之间！")
    private String actEmployeeName;

    @ApiModelProperty(value = "安排会诊医生工号")
    @NotEmpty(message="安排会诊医生工号不能为空！")
	@Size(min = 1, max = 20, message = "安排会诊医生工号长度必须在1-20个字符之间！")
    private String actEmployeeNo;

    @ApiModelProperty(value = "安排医生技术职称")
    @Size( max = 50, message = "安排医生技术职称不能超过50个字符！")
    private String actJobtitle;

    @ApiModelProperty(value = "安排医生手机号码")
    @Size( max = 20, message = "安排医生手机号码不能超过20个字符！")
    private String actTel;

    @ApiModelProperty(value = "计划会诊开始时间")
	@Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "计划会诊开始时间格式必须为 YYYY-MM-DD HH:MM:SS")
    private String actStartTime;
    
    @ApiModelProperty(value = "计划会诊结束时间")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "计划会诊结束时间格式必须为 YYYY-MM-DD HH:MM:SS")
    private String actEndTime;

    @ApiModelProperty(value = "是否发送通知0否1是")
    @Size( max = 1, message = "是否发送通知不能超过1个字符0否1是！")
    private String notcFlag;
    
	@ApiModelProperty(value = "操作人账号")
	@NotEmpty(message="操作人账号不能为空！")
	@Size(min = 1, max = 50, message = "操作人账号长度必须在1-50个字符之间！")
	private String oprtUser;
	
	@ApiModelProperty(value = "操作人姓名")
	@NotEmpty(message="操作人姓名不能为空！")
	@Size(min = 1, max = 50, message = "操作人姓名长度必须在1-50个字符之间！")
	private String oprtUserName;
}

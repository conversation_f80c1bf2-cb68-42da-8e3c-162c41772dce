package cn.trasen.hrms.web.Client;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Base64;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;



import com.alibaba.fastjson.JSONObject;

import lombok.extern.log4j.Log4j2;

/**
**********************************************   
 * @Description: 接口中心客户端demo
 * @Author:huangkui  
 * @Date: 2020/3/30 09:35:29
 * @Copyright: Copyright (c)  2006 - 2019
 * @Company: trasen.cn
 * @Version: 1.0.0
***********************************************
 */
@Log4j2
public class TrasenIfCentClientV2 {
	
	public static void main(String[] args) throws Exception {
		TrasenIfCentClientV2 client = TrasenIfCentClientV2.getInstance();

		//授权ID
		String pid = "khel255rv";
		//秘钥
		String secret = "lVntbHG&JYG05o0novQV%Ubt$gWe1I5F";
		
		long ttString = System.currentTimeMillis();
		System.out.println(ttString);
//		System.out.println(client.encryptAES(pid+"|"+ttString+"|6ae7a616a", secret));
		//接口ID，！！！注意此接口实际效果你发送什么就返回什么内容
		String interfaceId = "857819778897682432";
		
		Map<String, List<String>> data = new HashMap<>();
		List<String> dataList = new ArrayList<>();
		dataList.add("{\"Request\":{\"ywbh\":\"SaveSchdule\",\"doctor_id\":11275,\"SCHDULEDT\":[{\"orgId\":2117,\"scduDate\":\"2025-01-02\",\"csltLv\":29464,\"csltType\":0,\"csltMold\":0},{\"orgId\":2117,\"scduDate\":\"2024-01-02\",\"csltLv\":28841,\"csltType\":0,\"csltMold\":0}]}}");
		data.put("PostString", dataList);
		String result = client.postParameterUseToken("http://192.168.10.166:8888", interfaceId, pid, secret, data);
		System.out.println("\r\n令牌调用post json返回：\r\n"+result);
 
	}
	
	private static TrasenIfCentClientV2 instance = new TrasenIfCentClientV2();	
	
	private final RestTemplate restTemplate = new RestTemplate();
	private final ConcurrentMap<String, Long> tokenTimeMap = new ConcurrentHashMap<String, Long>();	
	private final ConcurrentMap<String, String> tokenMap = new ConcurrentHashMap<String, String>();	
	private TrasenIfCentClientV2() {
		
	}
	
	public static TrasenIfCentClientV2 getInstance() {
		return instance;
	}
	
	/**
	 * 使用令牌发送请求体
	 * @param urlHead 示例：https://**************:8888，即根据情况http或者https打头，后面跟服务IP即端口信息
	 * @param ifId 接口ID
	 * @param pid 授权ID
	 * @param secret 秘钥
	 * @param queryString 请求字符串，将加在url后面，示例：name=用户名&status=1
	 * @param body 请求体内容，可以是json、xml
	 * @return
	 * @throws Exception
	 *
	 * <AUTHOR>
	 * @datetime 2020年3月30日 下午3:01:20
	 */
	public String postBodyUseToken(String urlHead,String ifId,String pid,String secret,String queryString,String body) throws Exception {
		String token = checkToken(urlHead,pid,secret,ifId);
		System.out.println("token:" +token);
		return callInterface(urlHead, ifId, token, true, queryString, body,s->{
			return changeUrl(s,token,forceRequestToken(urlHead,pid,secret,ifId));
		});
	}

	private String changeUrl(String url,String oldToken, String token) {
		
		System.out.println("url:" + url);
		log.info("url:"+ url);
		
		System.out.println("oldToken:" + oldToken);
		log.info("oldToken:"+ oldToken);
		
		System.out.println("token:" + token);
		log.info("token:"+ token);
		
		int idx = url.lastIndexOf('/');
		
		System.out.println("idx:" + idx);
		log.info("idx:"+ idx);
		
		idx = url.indexOf(oldToken,idx+1);
		if(idx != -1){
			return url.substring(0,idx)+token+url.substring(idx+oldToken.length());
		}
		return url;
	}

	private String forceRequestToken(String urlHead, String pid, String secret,String ifId) {
		tokenTimeMap.remove(urlHead+pid);
		try {
			return checkToken(urlHead, pid, secret,ifId);
		}catch (Exception ex){
			System.out.println("尝试强转刷新令牌失败！-->"+ex.getMessage());
			ex.printStackTrace();
			return "notoken";
		}
	}


	/**
	 * 直接发送请求体
	 * @param urlHead 示例：https://**************:8888，即根据情况http或者https打头，后面跟服务IP即端口信息
	 * @param ifId 接口ID
	 * @param queryString 请求字符串，将加在url后面，示例：name=用户名&status=1
	 * @param body 请求体内容，可以是json、xml
	 * @return
	 *
	 * <AUTHOR>
	 * @datetime 2020年3月30日 下午3:05:00
	 */
	public String postBodyDirect(String urlHead,String ifId,String pid,String queryString,String body) {
		return callInterface(urlHead,ifId,pid+"@d",true,queryString,body,null);
	}
	
	/**
	 * 令牌post参数
	 * @param urlHead 示例：https://**************:8888，即根据情况http或者https打头，后面跟服务IP即端口信息
	 * @param ifId 接口ID
	 * @param pid 授权ID
	 * @param secret 秘钥
	 * @param parmMap 请求参数
	 * @return
	 * @throws Exception
	 *
	 * <AUTHOR>
	 * @datetime 2020年3月30日 下午3:05:28
	 */
	public String postParameterUseToken(String urlHead,String ifId,String pid,String secret,Map<String, List<String>> parmMap) throws Exception {
		String token = checkToken(urlHead,pid,secret,ifId);
		return callInterface(urlHead,ifId,token,true,null,parmMap,s->{
			return changeUrl(s,token,forceRequestToken(urlHead, pid, secret,ifId));
		});
	}
	
	/**
	 * 直接post参数
	 * @param urlHead https://**************:8888，即根据情况http或者https打头，后面跟服务IP即端口信息
	 * @param ifId 接口ID
	 * @param parmMap 请求参数
	 * @return
	 *
	 * <AUTHOR>
	 * @datetime 2020年3月30日 下午3:06:16
	 */
	public String postParameterDirect(String urlHead,String ifId,String pid,Map<String, List<String>> parmMap) {
		return callInterface(urlHead,ifId,pid+"@d",true,null,parmMap,null);
	}
	
	/**
	 * 令牌Get调用
	 * @param urlHead 示例：https://**************:8888，即根据情况http或者https打头，后面跟服务IP即端口信息
	 * @param ifId 接口ID
	 * @param pid 授权ID
	 * @param secret 秘钥
	 * @param queryString 请求字符串，用来传递参数，示例：name=用户名&status=1
	 * @return
	 * @throws Exception
	 *
	 * <AUTHOR>
	 * @datetime 2020年3月30日 下午3:06:41
	 */
	public String getUseToken(String urlHead,String ifId,String pid,String secret,String queryString) throws Exception {
		String token = checkToken(urlHead,pid,secret,ifId);
		return callInterface(urlHead,ifId,token,false,null,queryString,s->{
			return changeUrl(s,token,forceRequestToken(urlHead, pid, secret,ifId));
		});
	}
	
	/**
	 * 直接Get调用
	 * @param urlHead 示例：https://**************:8888，即根据情况http或者https打头，后面跟服务IP即端口信息
	 * @param ifId 接口ID
	 * @param queryString 请求字符串，用来传递参数，示例：name=用户名&status=1
	 * @return
	 *
	 * <AUTHOR>
	 * @datetime 2020年3月30日 下午3:07:32
	 */
	public String getDirect(String urlHead,String ifId,String pid,String queryString) {
		return callInterface(urlHead,ifId,pid+"@d",false,null,queryString,null);
	}
	


	private String callInterface(String urlHead, String ifId, String token, boolean post, String queryString, Object obj, Function<String,String> urlFun) {
		String url = urlHead;
		if(url.endsWith("/")) {
			url += "ts-ifcent/api/ifcall/";
		}else {
			url += "/ts-ifcent/api/ifcall/";
		}
		url += ifId+"/"+token;		
		if(post) {
			if(StringUtils.hasText(queryString)) {
				if(queryString.startsWith("?")) {
					url += queryString;
				}else {
					url += "?"+queryString;
				}
			}
			HttpEntity<?> requestEntity = null;
			if(obj instanceof String) {
				//post body
				String body = (String)obj;
				body = body.trim();
				char ch = body.charAt(0);

				//
				MediaType mediaType = MediaType.TEXT_PLAIN;
				if(ch == '{' || ch == '[') {
					//json
					HttpHeaders headers = new HttpHeaders();
					headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
					requestEntity = new HttpEntity<>(body.getBytes(Charset.forName("UTF-8")), headers);
				}else if(ch == '<') {
					//xml
					HttpHeaders headers = new HttpHeaders();
					headers.setContentType( MediaType.APPLICATION_XML);
					requestEntity = new HttpEntity<>(body.getBytes(Charset.forName("UTF-8")), headers);
				}else {
					HttpHeaders headers = new HttpHeaders();
					headers.setContentType(mediaType);
					requestEntity = new HttpEntity<>(body.getBytes(Charset.forName("UTF-8")), headers);
				}
			}else {
				//post parameter
				Map<String, List<String>> parameters = (Map<String, List<String>>)obj;
				MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
				for(String key:parameters.keySet()) {
					multiValueMap.put(key, parameters.get(key));
				}
				MediaType mediaType = MediaType.APPLICATION_FORM_URLENCODED;
				HttpHeaders headers = new HttpHeaders();
				headers.setContentType(mediaType);
				requestEntity = new HttpEntity<>(multiValueMap, headers);
			}
			try {
				return restTemplate.postForObject(url, requestEntity, String.class);
			}catch (HttpClientErrorException ex){
				if(ex.getStatusCode().value() == 401){
					if(urlFun != null){
						//再尝试一次
						System.out.println("授权问题，将尝试再次获取令牌后重试！！");
						return restTemplate.postForObject(urlFun.apply(url), requestEntity, String.class);
					}
				}
				throw ex;
			}
		}else {
			//get
			String qs = (String)obj;
			if(qs.startsWith("?")) {
				url += qs;
			}else {
				url += "?"+qs;
			}
			try {
				return restTemplate.getForObject(url, String.class);
			}catch (HttpClientErrorException ex){
				if(ex.getStatusCode().value() == 401){
					if(urlFun != null){
						//再尝试一次
						return restTemplate.getForObject(urlFun.apply(url), String.class);
					}
				}
				throw ex;
			}
		}
	}
	
	private String checkToken(String urlHead, String pid, String aesKey, String ifId) throws Exception {
		String key = urlHead+pid;
		String token = null;
		MultiValueMap<String, Object> multiValueMap = null;
		String method = null;
		if("1".length() == 2) {
			/*method = "refresh";
			token = "3n6y3adk";
			multiValueMap = new LinkedMultiValueMap<>();
			multiValueMap.add("pid", pid);		
			multiValueMap.add("token", token);
			multiValueMap.add("secret", encryptAES(pid+"|"+System.currentTimeMillis()+"|"+token, aesKey));
			multiValueMap.add("interfaceId", ifId);
			
			String url = urlHead;
			if(url.endsWith("/")) {
				url += "ts-ifcent/api/ifcall/token/";
			}else {
				url += "/ts-ifcent/api/ifcall/token/";
			}
			url += method;
			MediaType mediaType = MediaType.APPLICATION_FORM_URLENCODED;
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(mediaType);
			HttpEntity<?> requestEntity = new HttpEntity<>(multiValueMap, headers);
			String response = restTemplate.postForObject(url, requestEntity, String.class);
			if(response != null) {
				JSONObject json = JSONObject.parseObject(response);
				if(json.containsKey("object")) {
					token = json.getString("object");
					if(token != null) {
					tokenMap.put(key, token);
					tokenTimeMap.put(key, System.currentTimeMillis());
					}
				}
			}
			return token;
			*/
		}
		
		long time = Long.MAX_VALUE;
		if(tokenTimeMap.containsKey(key)) {
			time = tokenTimeMap.get(key);
			time = System.currentTimeMillis()-time;
		}
		if(time > 72*3600*1000L) {
			//超过了3天，说明访问频率不高，需要重新申请令牌
			method = "request";
			String random = Integer.toString(new Random().nextInt(900000)+100000);
			multiValueMap = new LinkedMultiValueMap<>();
			multiValueMap.add("pid", pid);		
			multiValueMap.add("random", random);
			multiValueMap.add("secret", encryptAES(pid+"|"+System.currentTimeMillis()+"|"+random, aesKey));
			multiValueMap.add("interfaceId", ifId);
		}else if(time > 70*3600*1000L) {
			//过期前两小时刷新令牌
			method = "refresh";
			token = tokenMap.get(key);
			multiValueMap = new LinkedMultiValueMap<>();
			multiValueMap.add("pid", pid);		
			multiValueMap.add("token", token);
			multiValueMap.add("secret", encryptAES(pid+"|"+System.currentTimeMillis()+"|"+token, aesKey));
			multiValueMap.add("interfaceId", ifId);
		}else {
			//70小时内直接复用token
			return tokenMap.get(key);
		}
		String url = urlHead;
		if(url.endsWith("/")) {
			url += "ts-ifcent/api/ifcall/token/";
		}else {
			url += "/ts-ifcent/api/ifcall/token/";
		}
		url += method;
		MediaType mediaType = MediaType.APPLICATION_FORM_URLENCODED;
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(mediaType);
		HttpEntity<?> requestEntity = new HttpEntity<>(multiValueMap, headers);
		String response = restTemplate.postForObject(url, requestEntity, String.class);
		if(response != null) {
			JSONObject json = JSONObject.parseObject(response);
			if(json.containsKey("object")) {
				token = json.getString("object");
				if(token != null) {
				tokenMap.put(key, token);
				tokenTimeMap.put(key, System.currentTimeMillis());
				}
			}
		}
		return token;
	}
	
	private String encryptAES(String data, String aesKey) throws Exception {
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            int blockSize = cipher.getBlockSize();
            byte[] dataBytes = data.getBytes();
            int plaintextLength = dataBytes.length;
            if (plaintextLength % blockSize != 0) {
                plaintextLength += blockSize - plaintextLength % blockSize;
            }

            byte[] plaintext = new byte[plaintextLength];
            System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);
            byte[] key = new byte[16];
            byte[] iv = new byte[16];
            decodeKeyIv(aesKey, key, iv);
            SecretKeySpec keyspec = new SecretKeySpec(key, "AES");
            IvParameterSpec ivspec = new IvParameterSpec(iv);
            cipher.init(1, keyspec, ivspec);
            byte[] encrypted = cipher.doFinal(plaintext);
            return Base64.encodeBase64String(encrypted);
        } catch (Exception var12) {
            var12.printStackTrace();
            return null;
        }
    }

    private static void decodeKeyIv(String aesKey, byte[] key, byte[] iv) {
        byte[] keyByes = aesKey.getBytes(Charset.forName("UTF-8"));
        if (keyByes.length >= 16) {
            System.arraycopy(keyByes, 0, key, 0, 16);
            if (keyByes.length >= 32) {
                System.arraycopy(keyByes, 16, iv, 0, 16);
            } else {
                System.arraycopy(keyByes, 16, iv, 0, keyByes.length - 16);
            }
        } else {
            System.arraycopy(keyByes, 0, key, 0, keyByes.length);
        }

    }

}

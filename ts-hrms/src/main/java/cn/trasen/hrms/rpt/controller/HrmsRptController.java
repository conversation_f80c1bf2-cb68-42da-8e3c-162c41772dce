package cn.trasen.hrms.rpt.controller;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.trasen.hrms.utils.ExcelStyleUtil;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.rpt.service.HrmsRptService;
import cn.trasen.hrms.utils.LmsoUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 -- =============================================
 --文件说明：人事报表服务类
 --类名称: HrmsRptController
 --创建时间：2024年10月21日 
 --作者：GW
 -- =============================================
 */
@Slf4j
@Api(tags = "人事报表")
@RestController
@RequestMapping("/hrmsRpt")
public class HrmsRptController {
	@Autowired
	HrmsRptService hrmsRptService;
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取人力资源分布  labor  resource distribute
	  -- 作者: GW
	  -- 创建时间: 2024年10月21日
	  -- @param 
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "人力资源分布", notes = "人力资源分布")
	@GetMapping(value = "/getLabrResuDistr")
	public PlatformResult<Map<String,Object>> getLabrResuDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			String archivesType = request.getParameter("archivesType");
			return PlatformResult.success(this.hrmsRptService.getLabrResuDistr(orgIds,employeeStatus, archivesType));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	@ApiOperation(value = "人力资源分布导出", notes = "人力资源分布导出")
	@GetMapping("/exportLabrResuDistr")
	public void exportLabrResuDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			String archivesType = request.getParameter("archivesType");
			Map<String,Object> result = this.hrmsRptService.getLabrResuDistr(orgIds,employeeStatus, archivesType);
			if (ObjectUtil.isNotEmpty(result)) {
				String filename = "人力资源分布表";
				String fileSheetName = "人力资源分布表";
				List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
				colList.add(new ExcelExportEntity("人员类别", "personal_identity_text",20));
				colList.add(new ExcelExportEntity("人数", "psncnt",14));
				Object fileds = result.get("fileds");
				colList.addAll(LmsoUtils.getExcelExportEntityByObj(fileds));//生成二级表头
				List<Map<String,Object>> dataList = (List<Map<String,Object>>)result.get("dataList");
				
				// 文件数据
				List<Map<String, Object>> list = new ArrayList<>();
				if(CollUtil.isNotEmpty(dataList)){
					Map<String,Object> filedMap = Convert.toMap(String.class, Object.class, fileds);
					dataList.forEach(i -> {
						Map<String,Object> vMap = new HashMap<String,Object>();
						vMap.put("personal_identity_text", i.get("personal_identity_text"));
						vMap.put("psncnt", i.get("psncnt"));
						filedMap.forEach((k,v) -> {
							List<Map<String,Object>> l = new ArrayList<Map<String,Object>>();
							Map<String,Object> m = new HashMap<String,Object>();
							if(ObjectUtil.isNotEmpty(v)){
								List<Map<String,Object>> t = (List<Map<String,Object>>)v;
								t.forEach(j -> {
									m.put(Convert.toStr(j.get("item_code")),i.get(Convert.toStr(j.get("item_code"))));
								});
							}
							l.add(m);
							vMap.put(k, l);
						});
						list.add(vMap);
					});
				}
				ExportParams exportParams = new ExportParams(filename, fileSheetName, ExcelType.XSSF);
				exportParams.setStyle(ExcelStyleUtil.class);
				Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, list);
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-disposition", "attachment; filename=" + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
                OutputStream fos = response.getOutputStream();
                workbook.write(fos);
                fos.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
		
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取年龄分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月22日
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "年龄分布", notes = "年龄分布")
	@GetMapping(value = "/getAgeDistr")
	public PlatformResult<Map<String,Object>> getAgeDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			return PlatformResult.success(this.hrmsRptService.getAgeDistr(orgIds,employeeStatus));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	@ApiOperation(value = "年龄段分布导出", notes = "年龄段分布导出")
	@GetMapping(value = "/exportAgeDistr")
	public void exportAgeDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			Map<String,Object> result = this.hrmsRptService.getAgeDistr(orgIds,employeeStatus);
			if (ObjectUtil.isNotEmpty(result)) {
				String filename = "年龄段分布表";
				String fileSheetName = "年龄段分布表";
				List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
//				colList.add(new ExcelExportEntity("序号", "no", 14));
				colList.add(new ExcelExportEntity("科室名称", "org_name", 30));
				List<Map<String,String>> fileds = (List<Map<String,String>>)result.get("fileds");
				if(CollUtil.isNotEmpty(fileds)){
					fileds.forEach(i -> {
						colList.add(new ExcelExportEntity(i.get("item_name"), i.get("item_code")));
					});
				}
				List<Map<String,String>> dataList = (List<Map<String,String>>)result.get("dataList");
				ExportParams exportParams = new ExportParams(filename, fileSheetName, ExcelType.XSSF);
				exportParams.setStyle(ExcelStyleUtil.class);
				Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, dataList);
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-disposition", "attachment; filename=" + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
                OutputStream fos = response.getOutputStream();
                workbook.write(fos);
                fos.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取性别分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月22日
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "性别分布", notes = "性别分布")
	@GetMapping(value = "/getGenderDistr")
	public PlatformResult<Map<String,Object>> getGenderDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			return PlatformResult.success(this.hrmsRptService.getGenderDistr(orgIds,employeeStatus));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	@ApiOperation(value = "性别分布导出", notes = "性别分布导出")
	@GetMapping(value = "/exportGenderDistr")
	public void exportGenderDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			Map<String,Object> result = this.hrmsRptService.getGenderDistr(orgIds,employeeStatus);
			if (ObjectUtil.isNotEmpty(result)) {
				String filename = "性别分布表";
				String fileSheetName = "性别分布表";
				List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
//				colList.add(new ExcelExportEntity("序号", "no", 14));
				colList.add(new ExcelExportEntity("科室名称", "org_name", 30));
				List<Map<String,String>> fileds = (List<Map<String,String>>)result.get("fileds");
				if(CollUtil.isNotEmpty(fileds)){
					fileds.forEach(i -> {
						colList.add(new ExcelExportEntity(i.get("item_name"), i.get("item_code")));
					});
				}
				List<Map<String,String>> dataList = (List<Map<String,String>>)result.get("dataList");
				ExportParams exportParams = new ExportParams(filename, fileSheetName, ExcelType.XSSF);
				exportParams.setStyle(ExcelStyleUtil.class);
				Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, dataList);
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-disposition", "attachment; filename=" + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
                OutputStream fos = response.getOutputStream();
                workbook.write(fos);
                fos.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取学历分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月22日
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "学历分布", notes = "学历分布")
	@GetMapping(value = "/getEducationDistr")
	public PlatformResult<Map<String,Object>> getEducationDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			String archivesType = request.getParameter("archivesType");
			return PlatformResult.success(this.hrmsRptService.getEducationDistr(orgIds,employeeStatus, archivesType));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	@ApiOperation(value = "学历分布导出", notes = "学历分布导出")
	@GetMapping(value = "/exportEducationDistr")
	public void exportEducationDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			String archivesType = request.getParameter("archivesType");
			Map<String,Object> result = this.hrmsRptService.getEducationDistr(orgIds,employeeStatus, archivesType);
			if (ObjectUtil.isNotEmpty(result)) {
				String filename = "学历分布表";
				String fileSheetName = "学历分布表";
				List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
//				colList.add(new ExcelExportEntity("序号", "no", 14));
				colList.add(new ExcelExportEntity("科室名称", "org_name", 30));
				List<Map<String,String>> fileds = (List<Map<String,String>>)result.get("fileds");
				if(CollUtil.isNotEmpty(fileds)){
					fileds.forEach(i -> {
						colList.add(new ExcelExportEntity(i.get("item_name"), i.get("item_code")));
					});
				}
				List<Map<String,String>> dataList = (List<Map<String,String>>)result.get("dataList");
				ExportParams exportParams = new ExportParams(filename, fileSheetName, ExcelType.XSSF);
				exportParams.setStyle(ExcelStyleUtil.class);
				Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, dataList);
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-disposition", "attachment; filename=" + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
                OutputStream fos = response.getOutputStream();
                workbook.write(fos);
                fos.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取员工类别分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月22日
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "员工类别分布", notes = "员工类别分布")
	@GetMapping(value = "/getEstablishmentDistr")
	public PlatformResult<Map<String,Object>> getEstablishmentDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			return PlatformResult.success(this.hrmsRptService.getEstablishmentDistr(orgIds,employeeStatus));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	@ApiOperation(value = "员工类别分布导出", notes = "员工类别分布导出")
	@GetMapping(value = "/exportEstablishmentDistr")
	public void exportEstablishmentDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			Map<String,Object> result = this.hrmsRptService.getEstablishmentDistr(orgIds,employeeStatus);
			if (ObjectUtil.isNotEmpty(result)) {
				String filename = "员工类别分布表";
				String fileSheetName = "员工类别分布表";
				List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
//				colList.add(new ExcelExportEntity("序号", "no", 14));
				colList.add(new ExcelExportEntity("科室名称", "org_name", 30));
				List<Map<String,String>> fileds = (List<Map<String,String>>)result.get("fileds");
				if(CollUtil.isNotEmpty(fileds)){
					fileds.forEach(i -> {
						colList.add(new ExcelExportEntity(i.get("item_name"), i.get("item_code")));
					});
				}
				List<Map<String,String>> dataList = (List<Map<String,String>>)result.get("dataList");
				ExportParams exportParams = new ExportParams(filename, fileSheetName, ExcelType.XSSF);
				exportParams.setStyle(ExcelStyleUtil.class);
				Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, dataList);
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-disposition", "attachment; filename=" + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
                OutputStream fos = response.getOutputStream();
                workbook.write(fos);
                fos.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取员工职称等级分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月22日
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "职称等级分布", notes = "职称等级分布")
	@GetMapping(value = "/getJobtitleDistr")
	public PlatformResult<Map<String,Object>> getJobtitleDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			String archivesType = request.getParameter("archivesType");
			return PlatformResult.success(this.hrmsRptService.getJobtitleDistr(orgIds,employeeStatus, archivesType));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	@ApiOperation(value = "职称等级分布导出", notes = "职称等级分布导出")
	@GetMapping(value = "/exportJobtitleDistr")
	public void exportJobtitleDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			String archivesType = request.getParameter("archivesType");
			Map<String,Object> result = this.hrmsRptService.getJobtitleDistr(orgIds,employeeStatus, archivesType);
			if (ObjectUtil.isNotEmpty(result)) {
				String filename = "职称分布表";
				String fileSheetName = "职称分布表";
				List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
//				colList.add(new ExcelExportEntity("序号", "no", 14));
				colList.add(new ExcelExportEntity("科室名称", "org_name", 30));
				List<Map<String,String>> fileds = (List<Map<String,String>>)result.get("fileds");
				if(CollUtil.isNotEmpty(fileds)){
					fileds.forEach(i -> {
						colList.add(new ExcelExportEntity(i.get("item_name"), i.get("item_code")));
					});
				}
				List<Map<String,String>> dataList = (List<Map<String,String>>)result.get("dataList");
				ExportParams exportParams = new ExportParams(filename, fileSheetName, ExcelType.XSSF);
				exportParams.setStyle(ExcelStyleUtil.class);
				Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, dataList);
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-disposition", "attachment; filename=" + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
                OutputStream fos = response.getOutputStream();
                workbook.write(fos);
                fos.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取员工个人身份分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月22日
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "个人身份分布", notes = "个人身份分布")
	@GetMapping(value = "/getIdentityDistr")
	public PlatformResult<Map<String,Object>> getIdentityDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			return PlatformResult.success(this.hrmsRptService.getIdentityDistr(orgIds,employeeStatus));
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	@ApiOperation(value = "个人身份分布导出", notes = "个人身份分布导出")
	@GetMapping(value = "/exportIdentityDistr")
	public void exportIdentityDistr(HttpServletRequest request,HttpServletResponse response) {
		try {
			String orgIds = request.getParameter("orgIds");
			String employeeStatus = request.getParameter("employeeStatus");
			Map<String,Object> result = this.hrmsRptService.getIdentityDistr(orgIds,employeeStatus);
			if (ObjectUtil.isNotEmpty(result)) {
				String filename = "个人身份分布表";
				String fileSheetName = "个人身份分布表";
				List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
//				colList.add(new ExcelExportEntity("序号", "no", 14));
				colList.add(new ExcelExportEntity("科室名称", "org_name", 30));
				List<Map<String,String>> fileds = (List<Map<String,String>>)result.get("fileds");
				if(CollUtil.isNotEmpty(fileds)){
					fileds.forEach(i -> {
						colList.add(new ExcelExportEntity(i.get("item_name"), i.get("item_code")));
					});
				}
				List<Map<String,String>> dataList = (List<Map<String,String>>)result.get("dataList");
				ExportParams exportParams = new ExportParams(filename, fileSheetName, ExcelType.XSSF);
				exportParams.setStyle(ExcelStyleUtil.class);
				Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, dataList);
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-disposition", "attachment; filename=" + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
                OutputStream fos = response.getOutputStream();
                workbook.write(fos);
                fos.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
}

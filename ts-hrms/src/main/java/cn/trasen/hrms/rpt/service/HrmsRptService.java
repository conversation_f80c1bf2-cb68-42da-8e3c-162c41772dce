package cn.trasen.hrms.rpt.service;

import java.util.Map;

public interface HrmsRptService {
	
	Map<String,Object> getLabrResuDistr(String orgIds,String employeeStatus, String archivesType);//人力资源分布
	Map<String,Object> getAgeDistr(String orgIds,String employeeStatus);//年龄分布
	Map<String,Object> getGenderDistr(String orgIds,String employeeStatus);//性别分布
	Map<String,Object> getEducationDistr(String orgIds,String employeeStatus, String archivesType);//学历分布
	Map<String,Object> getEstablishmentDistr(String orgIds,String employeeStatus);//员工类别分布
	Map<String,Object> getJobtitleDistr(String orgIds,String employeeStatus, String archivesType);//职称等级分布
	Map<String,Object> getIdentityDistr(String orgIds,String employeeStatus);//个人身份分布
}

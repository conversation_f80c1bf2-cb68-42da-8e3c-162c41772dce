package cn.trasen.hrms.rpt.service.impl;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import cn.trasen.homs.core.utils.UserInfoHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.hrms.rpt.dao.HrmsRptMapper;
import cn.trasen.hrms.rpt.service.HrmsRptService;

@Service
public class HrmsRptServiceImpl implements HrmsRptService {
	@Autowired
	HrmsRptMapper hrmsRptMapper;
	@Autowired
	DictItemFeignService dictItemFeignService;
	@Autowired
	private HrmsOrganizationFeignService hrmsOrganizationService;
	/**
	 * 
	  -- =============================================
	  -- 功能描述:  获取人力资源分布  
	  -- 作者: GW
	  -- 创建时间: 2024年10月21日
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String,Object> getLabrResuDistr(String orgIds,String employeeStatus, String archivesType) {
		Map<String,Object> params = new HashMap<String,Object>();
		if(StrUtil.isNotEmpty(orgIds)){
			//这里  不能加  this 
//			params.put("orgIdList", this.hrmsOrganizationService.getHrmsOrganizationAndNextList(orgIds).getObject());
			params.put("orgIdList", hrmsOrganizationService.getHrmsOrganizationAndNextList(orgIds).getObject());
		}
		if(StrUtil.isNotEmpty(employeeStatus)){
			params.put("employeeStatus", Arrays.asList(employeeStatus.split(",")));
		}
		//医务
		if(StrUtil.isNotEmpty(archivesType)){
			params.put("archivesType", archivesType);
		}
		//根据当前登录账号机构编码过滤查询数据
		params.put("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		Map<String,Object> result = new HashMap<String,Object>();
		List<DictItemResp> educationDicts = dictItemFeignService.getDictItemByTypeCode("education_type").getObject();
		Map<String,Object> fileds = new HashMap<String,Object>();
		List<Map<Object,Object>> educationFileds = new ArrayList<Map<Object,Object>>();
		List<String> sqls = new ArrayList<String>();
		if(CollUtil.isNotEmpty(educationDicts)){
			educationDicts.forEach(i -> {
				educationFileds.add(MapUtil.of(new Object[][] {
				     {"item_code", StrUtil.format("education_{}", i.getItemCode())},
				     {"item_name", i.getItemName()},
				     {"sort", null == i.getSort() ? i.getItemCode() : i.getSort()}
				}));
				sqls.add(StrUtil.format("count(case when education_type = '{}' then 1 else null end ) education_{},", i.getItemCode(),i.getItemCode()));
			});
			sqls.add(StrUtil.format("count(case when education_type is not null and education_type not in ('{}') then 1 else null end ) education_qt,", educationDicts.stream().map(i -> Convert.toStr(i.getItemCode())).distinct().collect(Collectors.joining("','")))); 
			sqls.add(StrUtil.format("count(case when education_type is null then 1 else null end ) education_is_null,")); 
		}
		//性别表头
		List<Map<Object,Object>> genderFileds = new ArrayList<Map<Object,Object>>();
		genderFileds.add(MapUtil.of(new Object[][] {{"item_code", "gender_0"},{"item_name", "男"},{"sort", 0}}));
		genderFileds.add(MapUtil.of(new Object[][] {{"item_code", "gender_1"}, {"item_name", "女"}, {"sort", 1} }));
		//职称表头
		List<Map<Object,Object>> jobtitleFileds = new ArrayList<Map<Object,Object>>();
		jobtitleFileds.add(MapUtil.of(new Object[][] {{"item_code", "jobtitle_cj"}, {"item_name", "初级"}, {"sort", 1} })); 
		jobtitleFileds.add(MapUtil.of(new Object[][] {{"item_code", "jobtitle_zj"}, {"item_name", "中级"}, {"sort", 2} })); 
		jobtitleFileds.add(MapUtil.of(new Object[][] {{"item_code", "jobtitle_fgj"}, {"item_name", "副高级"}, {"sort", 3} })); 
		jobtitleFileds.add(MapUtil.of(new Object[][] {{"item_code", "jobtitle_zgj"}, {"item_name", "正高级"}, {"sort", 4} })); 
		//年龄表头
		List<Map<Object,Object>> ageFileds = new ArrayList<Map<Object,Object>>();
		ageFileds.add(MapUtil.of(new Object[][] {{"item_code", "age_lt_20"}, {"item_name", "20岁以下"}, {"sort", 1} })); 
		ageFileds.add(MapUtil.of(new Object[][] {{"item_code", "age_20_30"}, {"item_name", "20-29"}, {"sort", 2} })); 
		ageFileds.add(MapUtil.of(new Object[][] {{"item_code", "age_30_40"}, {"item_name", "30-39"}, {"sort", 3} })); 
		ageFileds.add(MapUtil.of(new Object[][] {{"item_code", "age_40_50"}, {"item_name", "40-49"}, {"sort", 4} })); 
		ageFileds.add(MapUtil.of(new Object[][] {{"item_code", "age_50_60"}, {"item_name", "50-59"}, {"sort", 5} })); 
		ageFileds.add(MapUtil.of(new Object[][] {{"item_code", "age_gt_60"}, {"item_name", "60岁及以上"}, {"sort", 6} })); 
		params.put("sqls", sqls);
		List<Map<String, Object>> dataList = this.hrmsRptMapper.getLabrResuDistr(params);
		//计算合计栏、占比栏
		if(CollUtil.isNotEmpty(dataList)){
			Map<String,Object> sumMap = new HashMap<String,Object>();
			BigDecimal totalPsncnt = Convert.toBigDecimal(dataList.stream().filter(i -> null != i.get("psncnt")).mapToInt(i -> Convert.toInt(i.get("psncnt"))).sum());//总人数
			sumMap.put("personal_identity_text", "合计");
			sumMap.put("psncnt", totalPsncnt); 
			sumMap.put("gender_0", dataList.stream().filter(i -> null != i.get("gender_0")).mapToInt(i -> Convert.toInt(i.get("gender_0"))).sum()); //男性人数
			sumMap.put("gender_1", dataList.stream().filter(i -> null != i.get("gender_1")).mapToInt(i -> Convert.toInt(i.get("gender_1"))).sum()); //女性人数
			sumMap.put("gender_qt", dataList.stream().filter(i -> null != i.get("gender_qt")).mapToInt(i -> Convert.toInt(i.get("gender_qt"))).sum()); 
			sumMap.put("gender_is_null", dataList.stream().filter(i -> null != i.get("gender_is_null")).mapToInt(i -> Convert.toInt(i.get("gender_is_null"))).sum());
			
			sumMap.put("age_lt_20", dataList.stream().filter(i -> null != i.get("age_lt_20")).mapToInt(i -> Convert.toInt(i.get("age_lt_20"))).sum());
			sumMap.put("age_20_30", dataList.stream().filter(i -> null != i.get("age_20_30")).mapToInt(i -> Convert.toInt(i.get("age_20_30"))).sum());
			sumMap.put("age_30_40", dataList.stream().filter(i -> null != i.get("age_30_40")).mapToInt(i -> Convert.toInt(i.get("age_30_40"))).sum());
			sumMap.put("age_40_50", dataList.stream().filter(i -> null != i.get("age_40_50")).mapToInt(i -> Convert.toInt(i.get("age_40_50"))).sum());
			sumMap.put("age_50_60", dataList.stream().filter(i -> null != i.get("age_50_60")).mapToInt(i -> Convert.toInt(i.get("age_50_60"))).sum());
			sumMap.put("age_gt_60", dataList.stream().filter(i -> null != i.get("age_gt_60")).mapToInt(i -> Convert.toInt(i.get("age_gt_60"))).sum());
			sumMap.put("age_qt", dataList.stream().filter(i -> null != i.get("age_qt")).mapToInt(i -> Convert.toInt(i.get("age_qt"))).sum());
			//填充学历合计信息
			educationDicts.forEach(i -> {
				sumMap.put(StrUtil.format("education_{}", i.getItemCode()), 
						dataList.stream().filter(j -> null != j.get(StrUtil.format("education_{}", i.getItemCode()))).mapToInt(j -> Convert.toInt(j.get(StrUtil.format("education_{}", i.getItemCode())))).sum());
			});
			sumMap.put("education_qt", dataList.stream().filter(i -> null != i.get("education_qt")).mapToInt(i -> Convert.toInt(i.get("education_qt"))).sum());//其它
			sumMap.put("education_is_null", dataList.stream().filter(i -> null != i.get("education_is_null")).mapToInt(i -> Convert.toInt(i.get("education_is_null"))).sum());//无学历
			
			sumMap.put("jobtitle_cj", dataList.stream().filter(i -> null != i.get("jobtitle_cj")).mapToInt(i -> Convert.toInt(i.get("jobtitle_cj"))).sum());//初级
			sumMap.put("jobtitle_zj", dataList.stream().filter(i -> null != i.get("jobtitle_zj")).mapToInt(i -> Convert.toInt(i.get("jobtitle_zj"))).sum());//中级
			sumMap.put("jobtitle_zgj", dataList.stream().filter(i -> null != i.get("jobtitle_zgj")).mapToInt(i -> Convert.toInt(i.get("jobtitle_zgj"))).sum());//正高级
			sumMap.put("jobtitle_fgj", dataList.stream().filter(i -> null != i.get("jobtitle_fgj")).mapToInt(i -> Convert.toInt(i.get("jobtitle_fgj"))).sum());//副高级
			sumMap.put("jobtitle_qt", dataList.stream().filter(i -> null != i.get("jobtitle_qt")).mapToInt(i -> Convert.toInt(i.get("jobtitle_qt"))).sum());//其它
			sumMap.put("jobtitle_is_null", dataList.stream().filter(i -> null != i.get("jobtitle_is_null")).mapToInt(i -> Convert.toInt(i.get("jobtitle_is_null"))).sum());//无职称
			dataList.add(sumMap);
			Map<String,Object> propMap = new HashMap<String,Object>();
			DecimalFormat f = new DecimalFormat("##.##");
			propMap.put("personal_identity_text", "占比");
			propMap.put("psncnt", "100%"); //总人数
			propMap.put("gender_0", f.format(Convert.toBigDecimal(sumMap.get("gender_0")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100))) + "%"); //男性占比
			propMap.put("gender_1", f.format(Convert.toBigDecimal(sumMap.get("gender_1")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); //女性占比
			propMap.put("gender_qt", f.format(Convert.toBigDecimal(sumMap.get("gender_qt")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%");
			propMap.put("gender_is_null", f.format(Convert.toBigDecimal(sumMap.get("gender_is_null")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%");
			
			propMap.put("age_lt_20", f.format(Convert.toBigDecimal(sumMap.get("age_lt_20")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); 
			propMap.put("age_20_30", f.format(Convert.toBigDecimal(sumMap.get("age_20_30")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); 
			propMap.put("age_30_40", f.format(Convert.toBigDecimal(sumMap.get("age_30_40")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); 
			propMap.put("age_40_50", f.format(Convert.toBigDecimal(sumMap.get("age_40_50")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); 
			propMap.put("age_50_60", f.format(Convert.toBigDecimal(sumMap.get("age_50_60")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); 
			propMap.put("age_gt_60", f.format(Convert.toBigDecimal(sumMap.get("age_gt_60")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); 
			propMap.put("age_qt", f.format(Convert.toBigDecimal(sumMap.get("age_qt")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); 
			//填充学历占比信息
			educationDicts.forEach(i -> {
				propMap.put(StrUtil.format("education_{}", i.getItemCode()), 
						f.format(Convert.toBigDecimal(sumMap.get(StrUtil.format("education_{}", i.getItemCode()))).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); 
			});
			propMap.put("education_qt", f.format(Convert.toBigDecimal(sumMap.get("education_qt")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); //其它
			propMap.put("education_is_null", f.format(Convert.toBigDecimal(sumMap.get("education_is_null")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); //无学历
			
			propMap.put("jobtitle_cj", f.format(Convert.toBigDecimal(sumMap.get("jobtitle_cj")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); //初级
			propMap.put("jobtitle_zj", f.format(Convert.toBigDecimal(sumMap.get("jobtitle_zj")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); //中级
			propMap.put("jobtitle_zgj", f.format(Convert.toBigDecimal(sumMap.get("jobtitle_zgj")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); //正高级
			propMap.put("jobtitle_fgj", f.format(Convert.toBigDecimal(sumMap.get("jobtitle_fgj")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); //副高级
			propMap.put("jobtitle_qt", f.format(Convert.toBigDecimal(sumMap.get("jobtitle_qt")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); //其它
			propMap.put("jobtitle_is_null", f.format(Convert.toBigDecimal(sumMap.get("jobtitle_is_null")).divide(totalPsncnt,4,BigDecimal.ROUND_HALF_UP).multiply(Convert.toBigDecimal(100)))+ "%"); //无职称
			dataList.add(propMap);
			//如果有为空的，则添加为空字段显示
			if(Convert.toBigDecimal(sumMap.get("gender_is_null")).compareTo(BigDecimal.ZERO) > 0){
				genderFileds.add(MapUtil.of(new Object[][] {{"item_code", "gender_is_null"},{"item_name", "未知的性别"},{"sort", 998}}));
			}
			if(Convert.toBigDecimal(sumMap.get("gender_qt")).compareTo(BigDecimal.ZERO) > 0){
				genderFileds.add(MapUtil.of(new Object[][] {{"item_code", "gender_qt"},{"item_name", "其它"},{"sort", 999}}));
			}
			if(Convert.toBigDecimal(sumMap.get("age_qt")).compareTo(BigDecimal.ZERO) > 0){
				ageFileds.add(MapUtil.of(new Object[][] {{"item_code", "age_qt"},{"item_name", "其它"},{"sort", 999}}));
			}
			if(Convert.toBigDecimal(sumMap.get("education_is_null")).compareTo(BigDecimal.ZERO) > 0){
				educationFileds.add(MapUtil.of(new Object[][] {{"item_code", "education_is_null"},{"item_name", "无学历"},{"sort", 998}}));
			}
			if(Convert.toBigDecimal(sumMap.get("education_qt")).compareTo(BigDecimal.ZERO) > 0){
				educationFileds.add(MapUtil.of(new Object[][] {{"item_code", "education_qt"},{"item_name", "其它"},{"sort", 999}}));
			}
			if(Convert.toBigDecimal(sumMap.get("jobtitle_is_null")).compareTo(BigDecimal.ZERO) > 0){
				jobtitleFileds.add(MapUtil.of(new Object[][] {{"item_code", "jobtitle_is_null"},{"item_name", "无职称"},{"sort", 998}}));
			}
			if(Convert.toBigDecimal(sumMap.get("jobtitle_qt")).compareTo(BigDecimal.ZERO) > 0){
				jobtitleFileds.add(MapUtil.of(new Object[][] {{"item_code", "jobtitle_qt"},{"item_name", "其它"},{"sort", 999}}));
			}
		}
		result.put("dataList", dataList);
		fileds.put("genderFileds", genderFileds.stream().sorted(Comparator.comparing(i -> Convert.toInt(i.get("sort")))).collect(Collectors.toList()));
		fileds.put("ageFileds", ageFileds.stream().sorted(Comparator.comparing(i -> Convert.toInt(i.get("sort")))).collect(Collectors.toList()));
		fileds.put("educationFileds", educationFileds.stream().sorted(Comparator.comparing(i -> Convert.toInt(i.get("sort")))).collect(Collectors.toList()));
		fileds.put("jobtitleFileds", jobtitleFileds.stream().sorted(Comparator.comparing(i -> Convert.toInt(i.get("sort")))).collect(Collectors.toList()));
		result.put("fileds", fileds);
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取年龄分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月22日
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String,Object> getAgeDistr(String orgIds,String employeeStatus) {
		Map<String,Object> result = new HashMap<String,Object>();
		List<Map<Object,Object>> fileds = new ArrayList<Map<Object,Object>>();
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "cnt"},{"item_name", "总人数"},{"sort", 0}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "age_lt_20"},{"item_name", "20岁及以下"},{"sort", 1}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "age_20_30"},{"item_name", "20-29"},{"sort", 2}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "age_30_40"},{"item_name", "30-39"},{"sort", 3}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "age_40_50"},{"item_name", "40-49"},{"sort", 4}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "age_50_60"},{"item_name", "50-59"},{"sort", 5}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "age_gt_60"},{"item_name", "60岁及以上"},{"sort", 6}}));
		Map<String,Object> param = new HashMap<String,Object>();
		if(StrUtil.isNotEmpty(orgIds)){
//			param.put("orgIdList", this.hrmsOrganizationService.getHrmsOrganizationAndNextList(orgIds).getObject());
			param.put("orgIdList", Arrays.asList(orgIds.split(",")));
		}
		if(StrUtil.isNotEmpty(employeeStatus)){
			param.put("employeeStatus", Arrays.asList(employeeStatus.split(",")));
		}
		//根据当前登录账号机构编码过滤查询数据
		param.put("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		param.put("countSql", "count(case when age < 20 then 1 else null end ) age_lt_20, "
				+ "count(case when age >= 20 and age < 30 then 1 else null end ) age_20_30, "
				+ "count(case when age >= 30 and age < 40 then 1 else null end ) age_30_40,"
				+ "count(case when age >= 40 and age < 50 then 1 else null end ) age_40_50, "
				+ "count(case when age >= 50 and age < 60 then 1 else null end ) age_50_60, "
				+ "count(case when age >= 60 then 1 else null end ) age_gt_60,"
				+ "count(case when age is null then 1 else null end ) age_is_null,");
		param.put("columSql", "emp_age age,");
		List<Map<String, Object>> dataList =this.hrmsRptMapper.getEmpDistrGroupbyOrg(param); 
		//填充合计栏
		if(CollUtil.isNotEmpty(dataList)){
			Map<String,Object> m = new HashMap<String,Object>();
			m.put("org_name", "合计");
			m.put("cnt", dataList.stream().filter(i -> null != i.get("cnt")).mapToInt(i -> Convert.toInt(i.get("cnt"))).sum());
			m.put("age_lt_20", dataList.stream().filter(i -> null != i.get("age_lt_20")).mapToInt(i -> Convert.toInt(i.get("age_lt_20"))).sum());
			m.put("age_20_30", dataList.stream().filter(i -> null != i.get("age_20_30")).mapToInt(i -> Convert.toInt(i.get("age_20_30"))).sum());
			m.put("age_30_40", dataList.stream().filter(i -> null != i.get("age_30_40")).mapToInt(i -> Convert.toInt(i.get("age_30_40"))).sum());
			m.put("age_40_50", dataList.stream().filter(i -> null != i.get("age_40_50")).mapToInt(i -> Convert.toInt(i.get("age_40_50"))).sum());
			m.put("age_50_60", dataList.stream().filter(i -> null != i.get("age_50_60")).mapToInt(i -> Convert.toInt(i.get("age_50_60"))).sum());
			m.put("age_gt_60", dataList.stream().filter(i -> null != i.get("age_gt_60")).mapToInt(i -> Convert.toInt(i.get("age_gt_60"))).sum());
			m.put("age_is_null", dataList.stream().filter(i -> null != i.get("age_is_null")).mapToInt(i -> Convert.toInt(i.get("age_is_null"))).sum());
			dataList.add(m);
			//如果有为空的，则添加为空字段显示
			if(Convert.toBigDecimal(m.get("age_is_null")).compareTo(BigDecimal.ZERO) > 0){
				fileds.add(MapUtil.of(new Object[][] {{"item_code", "age_is_null"},{"item_name", "年龄为空"},{"sort", 7}}));
			}
		}
		result.put("dataList", dataList);
		result.put("fileds", fileds);
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 获取性别分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月22日
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String, Object> getGenderDistr(String orgIds,String employeeStatus) {
		Map<String,Object> result = new HashMap<String,Object>();
		List<Map<Object,Object>> fileds = new ArrayList<Map<Object,Object>>();
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "cnt"},{"item_name", "总人数"},{"sort", 0}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "male"},{"item_name", "女"},{"sort", 1}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "fml"},{"item_name", "男"},{"sort", 2}}));
		Map<String,Object> param = new HashMap<String,Object>();
		if(StrUtil.isNotEmpty(orgIds)){
//			param.put("orgIdList", this.hrmsOrganizationService.getHrmsOrganizationAndNextList(orgIds).getObject());
			param.put("orgIdList", Arrays.asList(orgIds.split(",")));
		}
		if(StrUtil.isNotEmpty(employeeStatus)){
			param.put("employeeStatus", Arrays.asList(employeeStatus.split(",")));
		}
		//根据当前登录账号机构编码过滤查询数据
		param.put("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		param.put("countSql", "count(case when gender = '0' then 1 else null end ) fml,"
				+ "count(case when gender = '1' then 1 else null end ) male,"
				+ "count(case when gender is not null and gender not in('0','1') then 1 else null end ) gender_qt,"
				+ "count(case when gender is null then 1 else null end ) gender_is_null,");
		param.put("columSql", "gender,");
		List<Map<String, Object>> dataList =this.hrmsRptMapper.getEmpDistrGroupbyOrg(param); 
		//填充合计栏
		if(CollUtil.isNotEmpty(dataList)){
			Map<String,Object> m = new HashMap<String,Object>();
			m.put("org_name", "合计");
			m.put("cnt", dataList.stream().filter(i -> null != i.get("cnt")).mapToInt(i -> Convert.toInt(i.get("cnt"))).sum());
			m.put("male", dataList.stream().filter(i -> null != i.get("male")).mapToInt(i -> Convert.toInt(i.get("male"))).sum());
			m.put("fml", dataList.stream().filter(i -> null != i.get("fml")).mapToInt(i -> Convert.toInt(i.get("fml"))).sum());
			m.put("gender_is_null", dataList.stream().filter(i -> null != i.get("gender_is_null")).mapToInt(i -> Convert.toInt(i.get("gender_is_null"))).sum());
			//如果有为空的，则添加为空字段显示
			if(Convert.toBigDecimal(m.get("gender_is_null")).compareTo(BigDecimal.ZERO) > 0){
				fileds.add(MapUtil.of(new Object[][] {{"item_code", "gender_is_null"},{"item_name", "未知的性别"},{"sort", 3}}));
			}
			if(ObjectUtil.isNotNull(m.get("gender_qt")) && Convert.toBigDecimal(m.get("gender_qt")).compareTo(BigDecimal.ZERO) > 0){
				fileds.add(MapUtil.of(new Object[][] {{"item_code", "gender_qt"},{"item_name", "其它"},{"sort", 3}}));
			}
			dataList.add(m);
		}
		result.put("dataList", dataList);
		result.put("fileds", fileds);
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 学历分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月22日
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String, Object> getEducationDistr(String orgIds,String employeeStatus, String archivesType) {
		Map<String,Object> param = new HashMap<String,Object>();
		if(StrUtil.isNotEmpty(orgIds)){
//			param.put("orgIdList", this.hrmsOrganizationService.getHrmsOrganizationAndNextList(orgIds).getObject());
			param.put("orgIdList", Arrays.asList(orgIds.split(",")));
		}
		if(StrUtil.isNotEmpty(employeeStatus)){
			param.put("employeeStatus", Arrays.asList(employeeStatus.split(",")));
		}
		if(StrUtil.isNotEmpty(archivesType)){
			param.put("archivesType", archivesType);
		}
		//根据当前登录账号机构编码过滤查询数据
		param.put("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		Map<String,Object> result = new HashMap<String,Object>();
		List<DictItemResp> dicts = dictItemFeignService.getDictItemByTypeCode("education_type").getObject();
		List<Map<Object,Object>> fileds = new ArrayList<Map<Object,Object>>();
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "cnt"},{"item_name", "总人数"},{"sort", 0}}));
		StringBuffer sqls = new StringBuffer();
		if(CollUtil.isNotEmpty(dicts)){
			dicts.forEach(i -> {
				Map<Object,Object> m = new HashMap<Object,Object>();
				m.put("item_code", StrUtil.format("education_{}", i.getItemCode()));
				m.put("item_name", i.getItemName());
				m.put("sort", i.getSort());
				fileds.add(m);
				sqls.append(StrUtil.format("count(case when education_type = '{}' then 1 else null end ) education_{},", i.getItemCode(),i.getItemCode()));
			});
			sqls.append("count(case when education_type is null then 1 else null end ) education_is_null,");//统计 为空的
			sqls.append(StrUtil.format("count(case when education_type is not null and education_type not in ('{}') then 1 else null end ) education_qt,"
							, dicts.stream().map(i -> Convert.toStr(i.getItemCode())).distinct().collect(Collectors.joining("','"))));// 统计不在字典里的
			param.put("countSql", sqls.toString());
//			param.put("columSql", "(select education_type from hrms_education_info t2 where t1.employee_id = t2.employee_id and t2.is_deleted = 'N' and t2.approval_status = '4' order by IFNULL(highest_level, '2') LIMIT 1) education_type,");
			//先去掉审核状态
			param.put("columSql", "(select education_type from hrms_education_info t2 where t1.employee_id = t2.employee_id and t2.is_deleted = 'N' order by IFNULL(highest_level, '2') LIMIT 1) education_type,");
			List<Map<String, Object>> dataList =this.hrmsRptMapper.getEmpDistrGroupbyOrg(param); 
			//填充合计栏
			if(CollUtil.isNotEmpty(dataList)){
				Map<String,Object> m = new HashMap<String,Object>();
				m.put("org_name", "合计");
				m.put("cnt", dataList.stream().filter(i -> null != i.get("cnt")).mapToInt(i -> Convert.toInt(i.get("cnt"))).sum());
				dicts.forEach(i -> {
					m.put(StrUtil.format("education_{}", i.getItemCode()), 
							dataList.stream().filter(j -> null != j.get(StrUtil.format("education_{}", i.getItemCode()))).mapToInt(j -> Convert.toInt(j.get(StrUtil.format("education_{}", i.getItemCode())))).sum());
				});
				m.put("education_is_null", dataList.stream().filter(i -> null != i.get("education_is_null")).mapToInt(i -> Convert.toInt(i.get("education_is_null"))).sum());
				m.put("education_qt", dataList.stream().filter(i -> null != i.get("education_qt")).mapToInt(i -> Convert.toInt(i.get("education_qt"))).sum());
				dataList.add(m);
				//如果有为空的，则添加为空字段显示
				if(Convert.toBigDecimal(m.get("education_qt")).compareTo(BigDecimal.ZERO) > 0){
					fileds.add(MapUtil.of(new Object[][] {{"item_code", "education_qt"},{"item_name", "其它"},{"sort", 998}}));
				}
				if(Convert.toBigDecimal(m.get("education_is_null")).compareTo(BigDecimal.ZERO) > 0){
					fileds.add(MapUtil.of(new Object[][] {{"item_code", "education_is_null"},{"item_name", "无学历"},{"sort", 999}}));
				}
			}
			result.put("dataList", dataList);
			result.put("fileds",fileds);
		}
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 员工类别分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月22日
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String, Object> getEstablishmentDistr(String orgIds,String employeeStatus) {
		Map<String,Object> param = new HashMap<String,Object>();
		if(StrUtil.isNotEmpty(orgIds)){
//			param.put("orgIdList", this.hrmsOrganizationService.getHrmsOrganizationAndNextList(orgIds).getObject());
			param.put("orgIdList", Arrays.asList(orgIds.split(",")));
		}
		if(StrUtil.isNotEmpty(employeeStatus)){
			param.put("employeeStatus", Arrays.asList(employeeStatus.split(",")));
		}
		//根据当前登录账号机构编码过滤查询数据
		param.put("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		Map<String,Object> result = new HashMap<String,Object>();
		List<DictItemResp> dicts = dictItemFeignService.getDictItemByTypeCode("establishment_type").getObject();
		List<Map<Object,Object>> fileds = new ArrayList<Map<Object,Object>>();
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "cnt"},{"item_name", "总人数"},{"sort", 0}}));
		StringBuffer sqls = new StringBuffer();
		if(CollUtil.isNotEmpty(dicts)){
			dicts.forEach(i -> {
				Map<Object,Object> m = new HashMap<Object,Object>();
				m.put("item_code", StrUtil.format("establishment_{}", i.getItemCode()));
				m.put("item_name", i.getItemName());
				m.put("sort", i.getSort());
				fileds.add(m);
				sqls.append(StrUtil.format("count(case when establishment_type = '{}' then 1 else null end ) establishment_{},", i.getItemCode(),i.getItemCode()));
			});
			sqls.append("count(case when establishment_type is null then 1 else null end ) establishment_is_null,");//统计 为空的
			sqls.append(StrUtil.format("count(case when establishment_type is not null and establishment_type not in ('{}') then 1 else null end ) establishment_qt,"
							, dicts.stream().map(i -> Convert.toStr(i.getItemCode())).distinct().collect(Collectors.joining("','"))));// 统计不在字典里的
			param.put("countSql", sqls.toString());
			param.put("columSql", "(select establishment_type from cust_emp_info t2 where t1.employee_id = t2.info_id LIMIT 1) establishment_type,");
			List<Map<String, Object>> dataList =this.hrmsRptMapper.getEmpDistrGroupbyOrg(param); 
			//填充合计栏
			if(CollUtil.isNotEmpty(dataList)){
				Map<String,Object> m = new HashMap<String,Object>();
				m.put("org_name", "合计");
				m.put("cnt", dataList.stream().filter(i -> null != i.get("cnt")).mapToInt(i -> Convert.toInt(i.get("cnt"))).sum());
				dicts.forEach(i -> {
					m.put(StrUtil.format("establishment_{}", i.getItemCode()), 
							dataList.stream().filter(j -> null != j.get(StrUtil.format("establishment_{}", i.getItemCode()))).mapToInt(j -> Convert.toInt(j.get(StrUtil.format("establishment_{}", i.getItemCode())))).sum());
				});
				m.put("establishment_is_null", dataList.stream().filter(i -> null != i.get("establishment_is_null")).mapToInt(i -> Convert.toInt(i.get("establishment_is_null"))).sum());
				m.put("establishment_qt", dataList.stream().filter(i -> null != i.get("establishment_qt")).mapToInt(i -> Convert.toInt(i.get("establishment_qt"))).sum());
				dataList.add(m);
				//如果有为空的，则添加为空字段显示
				if(Convert.toBigDecimal(m.get("establishment_qt")).compareTo(BigDecimal.ZERO) > 0){
					fileds.add(MapUtil.of(new Object[][] {{"item_code", "establishment_qt"},{"item_name", "其它"},{"sort", 998}}));
				}
				if(Convert.toBigDecimal(m.get("establishment_is_null")).compareTo(BigDecimal.ZERO) > 0){
					fileds.add(MapUtil.of(new Object[][] {{"item_code", "establishment_is_null"},{"item_name", "未知的类别"},{"sort", 999}}));
				}
			}
			result.put("dataList", dataList);
			result.put("fileds",fileds);
		}
		return result;
	}
	/**
	  -- =============================================
	  -- 功能描述: 职称等级分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月22日
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String, Object> getJobtitleDistr(String orgIds,String employeeStatus, String archivesType) {
		Map<String,Object> result = new HashMap<String,Object>();
		List<Map<Object,Object>> fileds = new ArrayList<Map<Object,Object>>();
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "cnt"},{"item_name", "总人数"},{"sort", 0}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "jobtitle_cj"},{"item_name", "初级"},{"sort", 1}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "jobtitle_zj"},{"item_name", "中级"},{"sort", 2}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "jobtitle_fgj"},{"item_name", "副高级"},{"sort", 3}}));
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "jobtitle_zgj"},{"item_name", "正高级"},{"sort", 4}}));
		Map<String,Object> param = new HashMap<String,Object>();
		if(StrUtil.isNotEmpty(orgIds)){
//			param.put("orgIdList", this.hrmsOrganizationService.getHrmsOrganizationAndNextList(orgIds).getObject());
			param.put("orgIdList", Arrays.asList(orgIds.split(",")));
		}
		if(StrUtil.isNotEmpty(employeeStatus)){
			param.put("employeeStatus", Arrays.asList(employeeStatus.split(",")));
		}
		if(StrUtil.isNotEmpty(archivesType)){
			param.put("archivesType", archivesType);
		}
		//根据当前登录账号机构编码过滤查询数据
		param.put("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		param.put("countSql", "count(case when jobtitle_basic_name = '初级' then 1 else null end ) jobtitle_cj, "
				+ "count(case when jobtitle_basic_name = '中级' then 1 else null end ) jobtitle_zj, "
				+ "count(case when jobtitle_basic_name = '正高级' then 1 else null end ) jobtitle_zgj, "
				+ "count(case when jobtitle_basic_name = '副高级' then 1 else null end ) jobtitle_fgj, "
				+ "count(case when jobtitle_basic_name is not null and jobtitle_basic_name not in('初级','中级','正高级','副高级') then 1 else null end ) jobtitle_qt, "
				+ "count(case when jobtitle_basic_name is null then 1 else null end ) jobtitle_is_null,");
		param.put("columSql", "(select jobtitle_basic_name from (select t1.employee_id, t2.jobtitle_basic_name "
				+ "from hrms_jobtitle_info t1, comm_jobtitle_basic t2 "
				+ "where t1.jobtitle_level = t2.jobtitle_basic_id "
				+ "and t1.highest_level = '1' "
				+ "and t1.is_deleted = 'N' "
				+ "and t2.is_deleted = 'N' "
				+ "and t2.is_enable = '1' "
				+ "and t2.jobtitle_basic_pid <> '254491496962453504') t2 "
				+ "where t1.employee_id = t2.employee_id LIMIT 1) jobtitle_basic_name,");
		List<Map<String, Object>> dataList =this.hrmsRptMapper.getEmpDistrGroupbyOrg(param); 
		//填充合计栏
		if(CollUtil.isNotEmpty(dataList)){
			Map<String,Object> m = new HashMap<String,Object>();
			m.put("org_name", "合计");
			m.put("cnt", dataList.stream().filter(i -> null != i.get("cnt")).mapToInt(i -> Convert.toInt(i.get("cnt"))).sum());
			m.put("jobtitle_cj", dataList.stream().filter(i -> null != i.get("jobtitle_cj")).mapToInt(i -> Convert.toInt(i.get("jobtitle_cj"))).sum());
			m.put("jobtitle_zj", dataList.stream().filter(i -> null != i.get("jobtitle_zj")).mapToInt(i -> Convert.toInt(i.get("jobtitle_zj"))).sum());
			m.put("jobtitle_zgj", dataList.stream().filter(i -> null != i.get("jobtitle_zgj")).mapToInt(i -> Convert.toInt(i.get("jobtitle_zgj"))).sum());
			m.put("jobtitle_fgj", dataList.stream().filter(i -> null != i.get("jobtitle_fgj")).mapToInt(i -> Convert.toInt(i.get("jobtitle_fgj"))).sum());
			m.put("jobtitle_qt", dataList.stream().filter(i -> null != i.get("jobtitle_qt")).mapToInt(i -> Convert.toInt(i.get("jobtitle_qt"))).sum());
			m.put("jobtitle_is_null", dataList.stream().filter(i -> null != i.get("jobtitle_is_null")).mapToInt(i -> Convert.toInt(i.get("jobtitle_is_null"))).sum());
			dataList.add(m);
			//如果有为空的，则添加为空字段显示
			if(Convert.toBigDecimal(m.get("jobtitle_is_null")).compareTo(BigDecimal.ZERO) > 0){
				fileds.add(MapUtil.of(new Object[][] {{"item_code", "jobtitle_is_null"},{"item_name", "无职称"},{"sort", 6}}));
			}
			if(Convert.toBigDecimal(m.get("jobtitle_qt")).compareTo(BigDecimal.ZERO) > 0){
				fileds.add(MapUtil.of(new Object[][] {{"item_code", "jobtitle_qt"},{"item_name", "其它"},{"sort", 6}}));
			}
		}
		result.put("fileds",fileds);
		result.put("dataList", dataList);
		return result;
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 个人身份分布
	  -- 作者: GW
	  -- 创建时间: 2024年10月22日
	  -- @return
	  -- =============================================
	 */
	@Override
	public Map<String, Object> getIdentityDistr(String orgIds,String employeeStatus) {
		Map<String,Object> param = new HashMap<String,Object>();
		if(StrUtil.isNotEmpty(orgIds)){
//			param.put("orgIdList", this.hrmsOrganizationService.getHrmsOrganizationAndNextList(orgIds).getObject());
			param.put("orgIdList", Arrays.asList(orgIds.split(",")));
		}
		if(StrUtil.isNotEmpty(employeeStatus)){
			param.put("employeeStatus", Arrays.asList(employeeStatus.split(",")));
		}
		//根据当前登录账号机构编码过滤查询数据
		param.put("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		Map<String,Object> result = new HashMap<String,Object>();
		List<DictItemResp> dicts = dictItemFeignService.getDictItemByTypeCode("personal_identity").getObject();
		List<Map<Object,Object>> fileds = new ArrayList<Map<Object,Object>>();
		fileds.add(MapUtil.of(new Object[][] {{"item_code", "cnt"},{"item_name", "总人数"},{"sort", 0}}));
		StringBuffer sqls = new StringBuffer();
		if(CollUtil.isNotEmpty(dicts)){
			dicts.forEach(i -> {
				Map<Object,Object> m = new HashMap<Object,Object>();
				m.put("item_code", StrUtil.format("identity_{}", i.getItemCode()));
				m.put("item_name", i.getItemName());
				m.put("sort", i.getSort());
				fileds.add(m);
				sqls.append(StrUtil.format("count(case when personal_identity = '{}' then 1 else null end ) identity_{},", i.getItemCode(),i.getItemCode()));
			});
			sqls.append("count(case when personal_identity is null then 1 else null end ) identity_is_null,");//统计 为空的
			sqls.append(StrUtil.format("count(case when personal_identity is not null and personal_identity not in ('{}') then 1 else null end ) identity_qt,"
							, dicts.stream().map(i -> Convert.toStr(i.getItemCode())).distinct().collect(Collectors.joining("','"))));// 统计不在字典里的
			param.put("countSql", sqls.toString());
			param.put("columSql", "personal_identity,");
			List<Map<String, Object>> dataList =this.hrmsRptMapper.getEmpDistrGroupbyOrg(param); 
			//填充合计栏
			if(CollUtil.isNotEmpty(dataList)){
				Map<String,Object> m = new HashMap<String,Object>();
				m.put("org_name", "合计");
				m.put("cnt", dataList.stream().filter(i -> null != i.get("cnt")).mapToInt(i -> Convert.toInt(i.get("cnt"))).sum()); 
				dicts.forEach(i -> {
					m.put(StrUtil.format("identity_{}", i.getItemCode()), 
							dataList.stream().filter(j -> null != j.get(StrUtil.format("identity_{}", i.getItemCode()))).mapToInt(j -> Convert.toInt(j.get(StrUtil.format("identity_{}", i.getItemCode())))).sum());
				});
				m.put("identity_is_null", dataList.stream().filter(i -> null != i.get("identity_is_null")).mapToInt(i -> Convert.toInt(i.get("identity_is_null"))).sum());
				m.put("identity_qt", dataList.stream().filter(i -> null != i.get("identity_qt")).mapToInt(i -> Convert.toInt(i.get("identity_qt"))).sum());
				dataList.add(m);
				//如果有为空的，则添加为空字段显示
				if(Convert.toBigDecimal(m.get("identity_qt")).compareTo(BigDecimal.ZERO) > 0){
					fileds.add(MapUtil.of(new Object[][] {{"item_code", "identity_qt"},{"item_name", "其它"},{"sort", 998}}));
				}
				if(Convert.toBigDecimal(m.get("identity_is_null")).compareTo(BigDecimal.ZERO) > 0){
					fileds.add(MapUtil.of(new Object[][] {{"item_code", "identity_is_null"},{"item_name", "未知的身份"},{"sort", 999}}));
				}
			}
			result.put("fileds",fileds);
			result.put("dataList", dataList);
		}
		return result;
	}
}

package cn.trasen.hrms.zpgl.vo.outVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Setter
@Getter
public class HrRecruitmentTalentPoolTreeOutVo {

    @ApiModelProperty(value = "招聘-人才库主键")
    private String  id;

    @ApiModelProperty(value = "招聘-人才库编号")
    private String  code;

    @ApiModelProperty(value = "招聘-人才库名称")
    private String name;

    @ApiModelProperty(value = "招聘-上级机构人才库编号")
    private String parentTalentPoolNo;

    @ApiModelProperty(value = "招聘-上级机构人才库id")
    private String pid;

    @ApiModelProperty(value = "状态（N停用Y启用）")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "子节点信息")
    private List<HrRecruitmentTalentPoolTreeOutVo> children;

}
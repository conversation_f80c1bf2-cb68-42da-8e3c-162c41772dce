package cn.trasen.hrms.zpgl.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrRecruitmentTalentPoolMapper;
import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPool;
import cn.trasen.hrms.zpgl.service.HrRecruitmentTalentPoolService;
import cn.trasen.hrms.zpgl.util.ChineseToFirstLetterUtils;
import cn.trasen.hrms.zpgl.util.TreesUtils;
import cn.trasen.hrms.zpgl.vo.inputVo.HrRecruitmentTalentPoolInputVo;
import cn.trasen.hrms.zpgl.vo.inputVo.HrRecruitmentTalentPoolListInputVo;
import cn.trasen.hrms.zpgl.vo.outVo.HrRecruitmentTalentPoolOutVo;
import cn.trasen.hrms.zpgl.vo.outVo.HrRecruitmentTalentPoolTreeOutVo;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrRecruitmentTalentPoolServiceImpl
 * @Description TODO
 */
@Service
public class HrRecruitmentTalentPoolServiceImpl implements HrRecruitmentTalentPoolService {

    @Autowired
    private HrRecruitmentTalentPoolMapper mapper;
    @Resource
    private HrRecruitmentTalentPoolService recruitmentTalentPoolService;

    /**
     * 保存、编辑
     *
     * @param recruitmentTalentPoolInputVo
     * @return
     */
    @Override
    public Integer saveOrUpdate(HrRecruitmentTalentPoolInputVo recruitmentTalentPoolInputVo) {
        HrRecruitmentTalentPool recruitmentTalentPool = BeanUtil.copyProperties(recruitmentTalentPoolInputVo, HrRecruitmentTalentPool.class);
        return Optional.ofNullable(recruitmentTalentPool.getHrRecruitmentTalentPoolId())
                .map(temp -> update(recruitmentTalentPool))
                .orElseGet(() -> save(recruitmentTalentPool));
    }

    /**
     * 启用禁用
     *
     * @param recruitmentTalentPoolId 主键
     * @param status                  状态（N停用Y启用）
     * @return
     */
    @Override
    public Integer enableDisable(String recruitmentTalentPoolId, String status) {
        HrRecruitmentTalentPool recruitmentTalentPool = new HrRecruitmentTalentPool();
        recruitmentTalentPool.setHrRecruitmentTalentPoolId(recruitmentTalentPoolId);
        recruitmentTalentPool.setStatus(status);
        return update(recruitmentTalentPool);
    }

    @Transactional
    @Override
    public Integer save(HrRecruitmentTalentPool record) {
        // 名称转编号
        record.setTalentPoolNo(ChineseToFirstLetterUtils.getFirstSpell(record.getTalentPoolName()));
        // 编号处理
        String talentPoolNo = Optional.ofNullable(selectByNo(record.getTalentPoolNo()))
                .map(temp -> record.getTalentPoolNo() + (CollectionUtil.isEmpty(temp) ? "" : temp.size()))
                .orElseGet(() -> record.getTalentPoolNo());
        record.setTalentPoolNo(talentPoolNo);

        record.setHrRecruitmentTalentPoolId(IdUtil.getId());
        record.setStatus(Contants.IS_DELETED_TURE);
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        }
        return mapper.insertSelective(record);
    }

    @Transactional
    @Override
    public Integer update(HrRecruitmentTalentPool record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据编号查询
     *
     * @param talentPoolNo
     * @return
     */
    public List<HrRecruitmentTalentPool> selectByNo(String talentPoolNo) {
        Example example = new Example(HrRecruitmentTalentPool.class);
        example.createCriteria()
                .andLike("talentPoolNo", talentPoolNo + "%")
                .andEqualTo(Contants.IS_DELETED_FIELD, "N");
        return mapper.selectByExample(example);
    }

    @Transactional
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        HrRecruitmentTalentPool record = new HrRecruitmentTalentPool();
        record.setHrRecruitmentTalentPoolId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public HrRecruitmentTalentPool selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    /**
     * 根据人才库管理名称查询
     *
     * @param name 人才库管理名称
     * @return
     */
    @Override
    public HrRecruitmentTalentPool selectByName(String name) {
        return mapper.selectByName(name);
    }

    @Override
    public List<HrRecruitmentTalentPoolOutVo> getDataSetList(Page page, HrRecruitmentTalentPoolListInputVo record) {

        if (!StringUtils.isEmpty(record.getParentId())) {
            List<String> subNodes = recruitmentTalentPoolService.queryQllSubNodes(record.getParentId());
            subNodes.add(record.getParentId());
            record.setParentIds(subNodes);
        }
        //根据当前登录账号机构编码过滤
        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<HrRecruitmentTalentPool> records = mapper.selectPageList(page,record);

        return BeanUtil.copyToList(records, HrRecruitmentTalentPoolOutVo.class)
                .stream()
                .map(temp -> {
                    if (Contants.IS_DELETED_FALSE.equals(temp.getStatus())) {
                        temp.setStatusValue("停用");
                    } else {
                        temp.setStatusValue("启用");
                    }
                    return temp;
                })
                .collect(Collectors.toList());
    }

    /**
     * 树结构
     *
     * @param record
     * @return
     */
    @Override
    public List<HrRecruitmentTalentPoolTreeOutVo> getTree(HrRecruitmentTalentPoolListInputVo record) {
        Example example = new Example(HrRecruitmentTalentPool.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("status", Contants.IS_DELETED_TURE);
        if (!StringUtil.isEmpty(record.getTalentPoolName())) {
            criteria.andLike("talentPoolName", "%" + record.getTalentPoolName() + "%");
        }
        if (!StringUtil.isEmpty(record.getCreateUser())) {
            criteria.andLike("createUser", "%" + record.getCreateUser() + "%");
        }
        if (!StringUtil.isEmpty(record.getParentId())) {
            criteria.andLike("parentTalentPoolId", record.getParentId());
        }
        //根据当前登录账号机构编码过滤查询数据
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        Page page = new Page();
        page.setPageSize(Integer.MAX_VALUE);
        page.setSidx("create_date");
        page.setSord("desc");
        List<HrRecruitmentTalentPool> records =  mapper.selectByExampleAndRowBounds(example, page);
        List<HrRecruitmentTalentPoolTreeOutVo> tree = Lists.newArrayList();
        records.forEach(temp -> {
            HrRecruitmentTalentPoolTreeOutVo recruitmentTalentPoolTreeOutVo = new HrRecruitmentTalentPoolTreeOutVo();
            recruitmentTalentPoolTreeOutVo.setId(temp.getHrRecruitmentTalentPoolId());
            recruitmentTalentPoolTreeOutVo.setCode(temp.getTalentPoolNo());
            recruitmentTalentPoolTreeOutVo.setName(temp.getTalentPoolName());
            recruitmentTalentPoolTreeOutVo.setPid(temp.getParentId());
            recruitmentTalentPoolTreeOutVo.setParentTalentPoolNo(temp.getParentTalentPoolNo());
            recruitmentTalentPoolTreeOutVo.setCreateDate(temp.getCreateDate());
            recruitmentTalentPoolTreeOutVo.setCreateUser(temp.getCreateUser());
            recruitmentTalentPoolTreeOutVo.setStatus(temp.getStatus());
            tree.add(recruitmentTalentPoolTreeOutVo);
        });
//        List<HrRecruitmentTalentPoolOutVo> recruitmentTalentPoolOutVos = BeanUtil.copyToList(records, HrRecruitmentTalentPoolOutVo.class);
        return TreesUtils.listToTree(
                tree,
                "Pid",
                "Id",
                null,
                "Children"
        );
    }

    /**
     * 查询人才库类型下所有子类型
     *
     * @param pid 父id
     * @return
     */
    @Override
    public List<String> queryQllSubNodes(String pid) {
        Example example = new Example(HrRecruitmentTalentPool.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<HrRecruitmentTalentPool> children = Lists.newArrayList();
        List<HrRecruitmentTalentPool> records = mapper.selectByExample(example);
        TreesUtils.queryQllSubNodes(records, pid, children, "getParentId", "getHrRecruitmentTalentPoolId");
        return Optional.ofNullable(children)
                .map(temp ->
                        temp.stream()
                        .map(HrRecruitmentTalentPool::getHrRecruitmentTalentPoolId)
                        .collect(Collectors.toList())
                ).orElseGet(() -> null);
    }

	@Override
	public List<HrRecruitmentTalentPoolTreeOutVo> treeSecondLevel(
			HrRecruitmentTalentPoolListInputVo record) {
	        List<HrRecruitmentTalentPool> records = mapper.treeSecondLevel(record);
	        List<HrRecruitmentTalentPoolTreeOutVo> tree = Lists.newArrayList();
	        records.forEach(temp -> {
	            HrRecruitmentTalentPoolTreeOutVo recruitmentTalentPoolTreeOutVo = new HrRecruitmentTalentPoolTreeOutVo();
	            recruitmentTalentPoolTreeOutVo.setId(temp.getHrRecruitmentTalentPoolId());
	            recruitmentTalentPoolTreeOutVo.setCode(temp.getTalentPoolNo());
	            recruitmentTalentPoolTreeOutVo.setName(temp.getTalentPoolName());
	            recruitmentTalentPoolTreeOutVo.setPid(temp.getParentId());
	            recruitmentTalentPoolTreeOutVo.setParentTalentPoolNo(temp.getParentTalentPoolNo());
	            recruitmentTalentPoolTreeOutVo.setCreateDate(temp.getCreateDate());
	            recruitmentTalentPoolTreeOutVo.setCreateUser(temp.getCreateUser());
	            recruitmentTalentPoolTreeOutVo.setStatus(temp.getStatus());
	            tree.add(recruitmentTalentPoolTreeOutVo);
	        });
	        return TreesUtils.listToTree(
	                tree,
	                "Pid",
	                "Id",
	                null,
	                "Children"
	        );
	}

	@Override
	public List<HrRecruitmentTalentPoolTreeOutVo> treeThreeLevel(
			HrRecruitmentTalentPoolListInputVo record) {
	     	Example example = new Example(HrRecruitmentTalentPool.class);
	        Example.Criteria criteria = example.createCriteria();
	        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
	        criteria.andEqualTo("status", Contants.IS_DELETED_TURE);
	        if (!StringUtil.isEmpty(record.getParentId())) {
	            criteria.andEqualTo("parentId", record.getParentId());
	        }
	        example.setOrderByClause(" seq_no");
	        List<HrRecruitmentTalentPool> records = mapper.selectByExample(example);
	        List<HrRecruitmentTalentPoolTreeOutVo> tree = Lists.newArrayList();
	        records.forEach(temp -> {
	            HrRecruitmentTalentPoolTreeOutVo recruitmentTalentPoolTreeOutVo = new HrRecruitmentTalentPoolTreeOutVo();
	            recruitmentTalentPoolTreeOutVo.setId(temp.getHrRecruitmentTalentPoolId());
	            recruitmentTalentPoolTreeOutVo.setCode(temp.getTalentPoolNo());
	            recruitmentTalentPoolTreeOutVo.setName(temp.getTalentPoolName());
	            recruitmentTalentPoolTreeOutVo.setPid(temp.getParentId());
	            recruitmentTalentPoolTreeOutVo.setParentTalentPoolNo(temp.getParentTalentPoolNo());
	            recruitmentTalentPoolTreeOutVo.setCreateDate(temp.getCreateDate());
	            recruitmentTalentPoolTreeOutVo.setCreateUser(temp.getCreateUser());
	            recruitmentTalentPoolTreeOutVo.setStatus(temp.getStatus());
	            tree.add(recruitmentTalentPoolTreeOutVo);
	        });
	        
	        return tree;
	}
}

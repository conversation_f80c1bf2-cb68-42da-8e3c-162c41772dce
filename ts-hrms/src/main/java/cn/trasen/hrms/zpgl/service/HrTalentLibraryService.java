package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPoolLabelTalentLibrary;
import cn.trasen.hrms.zpgl.vo.inputVo.HrTalentLibraryInputVo;

/**
 * @ClassName HrTalentLibraryService
 * @Description TODO
 * @date 2021年6月19日 下午2:21:17
 * <AUTHOR>
 * @version 1.0
 */
public interface HrTalentLibraryService {


	/**
	 * 批量打标签
	 * @param talentLibraryInputVo
	 * @return
	 */
	Integer batchMakeTag(HrTalentLibraryInputVo talentLibraryInputVo);

	List<HrRecruitmentTalentPoolLabelTalentLibrary> talentLibraryByZpglid(String zpglempid);

	Integer delLalentLibraryByZpglid(String id);


}

package cn.trasen.hrms.zpgl.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.zpgl.dao.HrContactInformationMapper;
import cn.trasen.hrms.zpgl.model.HrContactInformation;
import cn.trasen.hrms.zpgl.service.HrContactInformationService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrContactInformationServiceImpl
 * @Description TODO
 * @date 2021年6月19日 下午4:48:35
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrContactInformationServiceImpl implements HrContactInformationService {

	@Autowired
	private HrContactInformationMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrContactInformation record) {
		record.setContactInformationId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrContactInformation record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrContactInformation record = new HrContactInformation();
		record.setContactInformationId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrContactInformation selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrContactInformation> getDataSetList(Page page, HrContactInformation record) {
		Example example = new Example(HrContactInformation.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrContactInformation> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	/**
	 * @Title findByTalentId
	 * @Description 根据id查询所有联系人信息（人才库或简历）
	 * @param id
	 * @return List<HrContactInformation>
	 * @date 2021/6/19 17:01
	 * <AUTHOR>
	 */
	@Override
	public List<HrContactInformation> findByTalentId(String id) {
		Example example = new Example(HrContactInformation.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("talentLibraryId",id);
		List<HrContactInformation> informationList = mapper.selectByExample(example);
		if(CollectionUtils.isEmpty(informationList)){
			informationList= Collections.emptyList();
		}
		return informationList;
	}
}

package cn.trasen.hrms.zpgl.vo.outVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

//人员明细返回对象
@Data
public class EmpDetailsOutVo {

    private String zpglempid;
    private String zpglEmployeeStatusText;
    private String interviewPath;
    private String msgInterviewPath;
    private String resInterviewPath;

    private String applicantDept;
    private String applicantDeptname;
    private String applicantPost;
    private String applicantPosttext;
    private String addTalentPool;

    private String messageId;
    private String conform;
    private String interviewDept;
    private String interviewDepttext;
    private String interviewJob;
    private String interviewJobtext;
    private String writtenEmpNo;
    private String writtenName;
    private String writtenDate;
    private String operationEmpNo;
    private String operationName;
    private String operationDate;
    private String interviewEmpNo;
    private String interviewName;
    private String interviewDate;

    private String remark;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "entry_date")
    @ApiModelProperty(value = "入职时间")
    private Date comeEntryDate;

    private String  interviewResultStatus;
    private String studyoutDept;
    private String studyoutDepttext;
    private String studyoutJob;
    private String studyoutJobtext;
    private String resultId;
    private String writtenResult;
    private String operationResult;
    private String interviewResult;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "entry_date")
    @ApiModelProperty(value = "入职时间")
    private Date entryDate;

}

package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglProfessional;

/**
 * @ClassName HrmsZpglProfessionalService
 * @Description TODO
 * @date 2023��2��8�� ����4:13:48
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglProfessionalService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����4:13:48
	 * <AUTHOR>
	 */
	Integer save(HrmsZpglProfessional record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����4:13:48
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglProfessional record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��8�� ����4:13:48
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglProfessional
	 * @date 2023��2��8�� ����4:13:48
	 * <AUTHOR>
	 */
	HrmsZpglProfessional selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglProfessional>
	 * @date 2023��2��8�� ����4:13:48
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglProfessional> getDataSetList(Page page, HrmsZpglProfessional record);

	Integer deleteByEmpId(String employeeId);

	List<HrmsZpglProfessional> findByEmpId(String employeeId);

	String selectZhicheng(String zhichengmingcheng);
}

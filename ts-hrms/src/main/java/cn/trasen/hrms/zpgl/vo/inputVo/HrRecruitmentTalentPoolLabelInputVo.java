package cn.trasen.hrms.zpgl.vo.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

@Setter
@Getter
public class HrRecruitmentTalentPoolLabelInputVo {

    @ApiModelProperty(value = "招聘管理-人才库标签主键")
    private String hrRecruitmentTalentPoolLabelId;

    @NotEmpty(message = "标签名称")
    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @ApiModelProperty(value = "人才库类型id")
    private List<String> talentLibraryId;
}
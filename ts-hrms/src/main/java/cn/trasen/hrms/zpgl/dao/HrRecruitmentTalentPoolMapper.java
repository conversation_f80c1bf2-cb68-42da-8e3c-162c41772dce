package cn.trasen.hrms.zpgl.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPool;
import cn.trasen.hrms.zpgl.vo.inputVo.HrRecruitmentTalentPoolListInputVo;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface HrRecruitmentTalentPoolMapper extends Mapper<HrRecruitmentTalentPool> {

    /**
     * 根据人才库管理名称查询
     * @param name 人才库管理名称
     * @return
     */
    HrRecruitmentTalentPool selectByName(String name);

    List<HrRecruitmentTalentPool> selectPageList(Page page, HrRecruitmentTalentPoolListInputVo record);
    
    //只查询一二级
	List<HrRecruitmentTalentPool> treeSecondLevel(HrRecruitmentTalentPoolListInputVo record);
}
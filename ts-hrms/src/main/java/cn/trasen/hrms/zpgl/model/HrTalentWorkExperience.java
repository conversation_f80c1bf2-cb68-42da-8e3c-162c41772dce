package cn.trasen.hrms.zpgl.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hr_talent_work_experience")
@Setter
@Getter
public class HrTalentWorkExperience {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Id
    @Column(name = "work_experience_id")
    private String workExperienceId;

    @Column(name = "talent_library_id")
    private String talentLibraryId;

    /**
     * 工作开始时间
     */
    @Column(name = "work_start_date")
    @ApiModelProperty(value = "工作开始时间")
    private String workStartDate;

    /**
     * 工作结束时间
     */
    @Column(name = "work_end_date")
    @ApiModelProperty(value = "工作结束时间")
    private String workEndDate;

    /**
     * 工作单位及任职部门
     */
    @Column(name = "work_and_dept")
    @ApiModelProperty(value = "工作单位及任职部门")
    private String workAndDept;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String position;

    /**
     * 离职原因
     */
    @Column(name = "leaving_reason")
    @ApiModelProperty(value = "离职原因")
    private String leavingReason;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "create_date")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_date")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_user_name")
    private String updateUserName;
}
package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPoolLabelTalentLibrary;

/**
 * @ClassName HrRecruitmentTalentPoolLabelTalentLibraryService
 * @Description TODO
 * <AUTHOR>
 * @version 1.0
 */
public interface HrRecruitmentTalentPoolLabelTalentLibraryService {


	/**
	 * 人才库标签，批量打标签
	 * @param talentLibraryId 人才库id
	 * @param recruitmentTalentPoolLabelId 人才库标签id
	 * @return
	 */
	Integer save(List<String> talentLibraryId,String recruitmentTalentPoolLabelId);

	/**
	 * 人才库标签，批量打标签
	 * @param talentLibraryId 人才库id
	 * @param recruitmentTalentPoolLabelId 人才库标签id
	 * @return
	 */
	Integer save(String talentLibraryId, List<String> recruitmentTalentPoolLabelId);


	/**
	 * 逻辑删除
	 * @param recruitmentTalentPoolLabelId
	 * @return
	 */
	Integer deleteById(String recruitmentTalentPoolLabelId);

	/**
	 * 逻辑删除
	 * @param talentLibraryId 人才库管理id
	 * @return
	 */
	Integer deleteByTalentLibraryId(String talentLibraryId);


	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2022��8��11�� ����5:42:00
	 * <AUTHOR>
	 */
	Integer save(HrRecruitmentTalentPoolLabelTalentLibrary record);

	HrRecruitmentTalentPoolLabelTalentLibrary selectById(String talentLibraryId, String recruitmentTalentPoolLabelId);

}

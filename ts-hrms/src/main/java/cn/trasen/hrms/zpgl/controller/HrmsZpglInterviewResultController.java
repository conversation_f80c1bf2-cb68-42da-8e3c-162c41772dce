package cn.trasen.hrms.zpgl.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.hrms.zpgl.enums.ZpglEmployeeStatusEnum;
import cn.trasen.hrms.zpgl.model.HrmsZpglEmployee;
import cn.trasen.hrms.zpgl.model.HrmsZpglInterviewMessage;
import cn.trasen.hrms.zpgl.model.HrmsZpglInterviewResult;
import cn.trasen.hrms.zpgl.service.HrmsZpglEmployeeService;
import cn.trasen.hrms.zpgl.service.HrmsZpglInterviewMessageService;
import cn.trasen.hrms.zpgl.service.HrmsZpglInterviewResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsZpglInterviewResultController
 * @Description 面试结果
 * @date 2023��2��16�� ����6:09:54
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglInterviewResultController")
public class HrmsZpglInterviewResultController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglInterviewResultController.class);

	@Autowired
	private HrmsZpglInterviewResultService hrmsZpglInterviewResultService;
	@Autowired
	private HrmsZpglEmployeeService hrmsZpglEmployeeService;
	
	@Autowired
	private HrmsZpglInterviewMessageService hrmsZpglInterviewMessageService;

	/**
	 * @Title saveHrmsZpglInterviewResult
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��16�� ����6:09:54
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/zpglInterviewResult/save")
	public PlatformResult<String> saveHrmsZpglInterviewResult(@RequestBody HrmsZpglInterviewResult record) {
		try {
			hrmsZpglInterviewResultService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsZpglInterviewResult
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��16�� ����6:09:54
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/zpglInterviewResult/update")
	public PlatformResult<String> updateHrmsZpglInterviewResult(@RequestBody HrmsZpglInterviewResult record) {
		try {
			hrmsZpglInterviewResultService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsZpglInterviewResultById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsZpglInterviewResult>
	 * @date 2023��2��16�� ����6:09:54
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/zpglInterviewResult/{resultId}")
	public PlatformResult<HrmsZpglInterviewResult> selectHrmsZpglInterviewResultById(@PathVariable String resultId) {
		try {
			HrmsZpglInterviewResult record = hrmsZpglInterviewResultService.selectById(resultId);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/zpglInterviewResult/byEmpId/{zpglempid}")
	public PlatformResult<HrmsZpglInterviewResult> selectHrmsZpglInterviewResultbyEmpId(@PathVariable String zpglempid) {
		try {
			HrmsZpglInterviewResult record = hrmsZpglInterviewResultService.selectByEmpId(zpglempid);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsZpglInterviewResultById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��2��16�� ����6:09:54
	 * <AUTHOR>
	 */
	@ApiOperation(value = "面试不通过删除", notes = "面试不通过删除")
	@PostMapping("/api/zpglInterviewResult/delete/{zpglempid}")
	public PlatformResult<String> deleteHrmsZpglInterviewResultById(@PathVariable String zpglempid) {
		try {
			hrmsZpglInterviewResultService.deleteById(zpglempid);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsZpglInterviewResultList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglInterviewResult>
	 * @date 2023��2��16�� ����6:09:54
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglInterviewResult/list")
	public DataSet<HrmsZpglInterviewResult> selectHrmsZpglInterviewResultList(Page page, HrmsZpglInterviewResult record) {
		return hrmsZpglInterviewResultService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "面试通过提交", notes = "面试通过提交")
	@GetMapping("/api/zpglInterviewResult/submit/{zpglempid}")
	public PlatformResult<HrmsZpglInterviewResult> selectHrmsZpglInterviewResultSubmit(@PathVariable String zpglempid) {
		try {
			HrmsZpglEmployee record = new HrmsZpglEmployee();
			record.setId(zpglempid);
			//设置为待入职
			record.setZpglEmployeeStatus(ZpglEmployeeStatusEnum.ZPGL_EMPLOYEE_STATUS_3.getKey());
			record.setAddTalentPool("2");  //从人才库移除
			hrmsZpglEmployeeService.updateBasic(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	//入职管理 存档接口
	@ApiOperation(value = "存档接口", notes = "存档接口")
	@PostMapping("/api/zpglInterviewResult/entry/{zpglempid}")
	public PlatformResult<String> entryId(@PathVariable String zpglempid) {
		try {
			hrmsZpglInterviewResultService.entryId(zpglempid);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	//继续面试
	@ApiOperation(value = "继续面试", notes = "继续面试")
	@PostMapping("/api/zpglInterviewResult/continue")
	public PlatformResult<HrmsZpglInterviewResult> continuetInterview(@RequestBody HrmsZpglInterviewResult record) {
		
		HrmsZpglInterviewResult selectById = hrmsZpglInterviewResultService.selectById(record.getResultId());
		try {
			HrmsZpglInterviewResult bean = new HrmsZpglInterviewResult();
			bean.setId(record.getResultId());
			bean.setInterviewResultStatus("4"); //为继续面试人员 
			bean.setZpglempid(record.getZpglempid());
			hrmsZpglInterviewResultService.update(bean);
			//修改面试信息的 某个值
			if(!StringUtil.isEmpty(record.getMessageId())) {
				HrmsZpglInterviewMessage msg = new HrmsZpglInterviewMessage();
				msg.setId(record.getMessageId());
				msg.setMsgInterviewPath(record.getInterviewPath());
				msg.setZpglempid(record.getZpglempid());
				msg.setInterviewDept(selectById.getStudyoutDept());
				msg.setInterviewDepttext(selectById.getStudyoutDepttext());
				msg.setInterviewJob(selectById.getStudyoutJob());
				msg.setInterviewJobtext(selectById.getStudyoutJobtext());
				hrmsZpglInterviewMessageService.update(msg);
			}
			
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}

package cn.trasen.hrms.zpgl.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrmsZpglTraceNexttime;
import cn.trasen.hrms.zpgl.service.HrmsZpglTraceNexttimeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsZpglTraceNexttimeController
 * @Description TODO
 * @date 2023��6��26�� ����5:00:19
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglTraceNexttimeController")
public class HrmsZpglTraceNexttimeController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglTraceNexttimeController.class);

	@Autowired
	private HrmsZpglTraceNexttimeService hrmsZpglTraceNexttimeService;

	/**
	 * @Title saveHrmsZpglTraceNexttime
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��6��26�� ����5:00:19
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/trainnexttime/save")
	public PlatformResult<String> saveHrmsZpglTraceNexttime(@RequestBody HrmsZpglTraceNexttime record) {
		try {
			hrmsZpglTraceNexttimeService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsZpglTraceNexttime
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��6��26�� ����5:00:19
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/trainnexttime/update")
	public PlatformResult<String> updateHrmsZpglTraceNexttime(@RequestBody HrmsZpglTraceNexttime record) {
		try {
			hrmsZpglTraceNexttimeService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsZpglTraceNexttimeById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsZpglTraceNexttime>
	 * @date 2023��6��26�� ����5:00:19
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/trainnexttime/{id}")
	public PlatformResult<HrmsZpglTraceNexttime> selectHrmsZpglTraceNexttimeById(@PathVariable String id) {
		try {
			HrmsZpglTraceNexttime record = hrmsZpglTraceNexttimeService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsZpglTraceNexttimeById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��6��26�� ����5:00:19
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/trainnexttime/delete/{id}")
	public PlatformResult<String> deleteHrmsZpglTraceNexttimeById(@PathVariable String id) {
		try {
			hrmsZpglTraceNexttimeService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsZpglTraceNexttimeList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglTraceNexttime>
	 * @date 2023��6��26�� ����5:00:19
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/trainnexttime/list")
	public DataSet<HrmsZpglTraceNexttime> selectHrmsZpglTraceNexttimeList(Page page, HrmsZpglTraceNexttime record) {
		return hrmsZpglTraceNexttimeService.getDataSetList(page, record);
	}
}

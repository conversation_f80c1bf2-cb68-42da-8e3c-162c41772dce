package cn.trasen.hrms.zpgl.vo.outVo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class HrmsZpglTraceResultOutListVo {

	@ApiModelProperty(value = "被跟踪人")
	private String zpglempid;
	

	@ApiModelProperty(value = "1继续跟踪 2 结束跟踪")
	private String traceStatus;

	@ApiModelProperty(value = "跟踪结果")
	private String traceResult;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createDate;

	private String employeeName; // 姓名

	@ApiModelProperty(value = "性别")
	private String gender;

	@ApiModelProperty(value = "性别文本")
	private String gendertext;

	@ApiModelProperty(value = "拟定岗位")
	private String studyoutJob;

	@ApiModelProperty(value = "拟定岗位名称")
	private String studyoutJobtext;
	private String iphone; // 电话
	private String age;
	private String zhichengmingcheng;
	private String zhichengmingchengtext;
	private String zhuenye;
	private String xueli;
	private String biyeyuanxiao;
	private String entryDate;
	private String  resInterviewPath;
	private String createUserName;
    private String zpglEmployeeStatus;
    private String conform;
    private String interviewResultStatus;
    private String employeeStatusText;  //显示的人员状态
    private String numberCount;  //跟踪次数
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date interviewDate; //面试通过时间
    private String  entryDateText;
    
    private String interviewPath;
    private String xuelitext;
}

package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglWorkrecord;

/**
 * @ClassName HrmsZpglWorkrecordService
 * @Description TODO
 * @date 2023��2��8�� ����4:11:35
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglWorkrecordService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����4:11:35
	 * <AUTHOR>
	 */
	Integer save(HrmsZpglWorkrecord record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����4:11:35
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglWorkrecord record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��8�� ����4:11:35
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglWorkrecord
	 * @date 2023��2��8�� ����4:11:35
	 * <AUTHOR>
	 */
	HrmsZpglWorkrecord selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglWorkrecord>
	 * @date 2023��2��8�� ����4:11:35
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglWorkrecord> getDataSetList(Page page, HrmsZpglWorkrecord record);

	Integer deleteByEmpId(String employeeId);

	List<HrmsZpglWorkrecord> findByEmpId(String employeeId);

    String getOneData(String zpglempid);
}

package cn.trasen.hrms.zpgl.task;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.zpgl.model.HrmsZpglTraceResult;
import cn.trasen.hrms.zpgl.service.HrmsZpglTraceResultService;
import lombok.extern.slf4j.Slf4j;

/** 
* @ClassName: TraceMsg 
* @Description:跟踪消息提示
* <AUTHOR>  
* @date 2023年3月3日 上午11:01:38 
*  
*/
@Slf4j
@Component
public class TraceMsg {
	
	@Autowired
	private HrmsZpglTraceResultService hrmsZpglTraceResultService;
	
	@Autowired
	InformationFeignService informationFeignService;
	
	@Value("${traceMsgEmployeeNo:}")
	private String traceMsgEmployeeNo;
	
	/** 
	* @Title: sendMsg 
	* @Description: 每天
	* @param     设定文件 
	* @return void    返回类型 
	* @throws 
	*/
	@Scheduled(cron = "0 0 8 * * ?")
	public void sendMsg() {
		try {
			//获取要发送消息的人员
			Page page = new Page();
			DataSet<HrmsZpglTraceResult> dataSetList = hrmsZpglTraceResultService.getDataSetList(page, new HrmsZpglTraceResult());
			List<HrmsZpglTraceResult> rows = dataSetList.getRows();
			if(rows != null && rows.size() > 0) {
				
				String msg = "您有 " +rows.size() + " 个面试人员需要进行跟踪, 请您合理安排时间进行跟踪！";
				sendMessage(msg,traceMsgEmployeeNo);  
				
			}
			log.error("###########定时任务跟踪提醒#############"+DateUtils.getPresentTimeStr());
		} catch (Exception e) {
			log.error("定时任务跟踪提醒执行失败："+e.getMessage(),e);
		}
	}
	
    private void sendMessage(String content,String receiver ) {
        
    	if(StringUtils.isNotBlank(receiver)) {
    		NoticeReq noticeVo = NoticeReq.builder()
    				.content(content)
    				.noticeType("3")
    				.subject("招聘管理")
    				.sender(UserInfoHolder.getCurrentUserInfo().getUsername())
    				.senderName(UserInfoHolder.getCurrentUserInfo().getUsername())
    				.receiver(receiver)
    				.wxSendType("2")
    				.source("招聘管理")
    				.build();
        	
    		 informationFeignService.sendNotice(noticeVo);
    	}
        		
    }
}

package cn.trasen.hrms.zpgl.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

@Table(name = "hrms_zpgl_employee")
@Setter
@Getter
public class HrmsZpglEmployee {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;


    /**
     * 主键ID
     */
	@Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 应聘岗位
     */
    @Column(name = "applicant_post")
    @ApiModelProperty(value = "应聘岗位")
    private String applicantPost;

    /**
     * 姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "姓名")
    private String employeeName;
    
    /**
     * 姓名
     */
    @Column(name = "trase_type")
    @ApiModelProperty(value = "跟踪状态")
    private String traseType;
    
    

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String gender;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private String age;

    /**
     * 身份证
     */
    @Column(name = "identity_number")
    @ApiModelProperty(value = "身份证")
    private String identityNumber;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String iphone;

    /**
     * 外语等级
     */
    @ApiModelProperty(value = "外语等级")
    private String waiyudengji;

    /**
     * 身高
     */
    @ApiModelProperty(value = "身高")
    private String shenggao;

    /**
     * 体重
     */
    @ApiModelProperty(value = "体重")
    private String tizhong;

    /**
     * 参加工作时间
     */
    @Column(name = "entry_date")
    @ApiModelProperty(value = "参加工作时间")
    private String entryDate;

    /**
     * 是否有驾驶证
     */
    @Column(name = "driving_licence")
    @ApiModelProperty(value = "是否有驾驶证")
    private String drivingLicence;

    /**
     * 目前职业状态
     */
    @ApiModelProperty(value = "目前职业状态")
    private String zhiyezhuangtai;

    /**
     * 期望月薪
     */
    @ApiModelProperty(value = "期望月薪")
    private String qiwangyuexin;

    /**
     * 最快入职时间
     */
    @ApiModelProperty(value = "最快入职时间")
    private String zuikuairuzhi;

    /**
     * 政治面貌
     */
    @Column(name = "political_status")
    @ApiModelProperty(value = "政治面貌")
    private String politicalStatus;

    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    private String birthplace;

    /**
     * 婚育状态
     */
    @Column(name = "marriage_status")
    @ApiModelProperty(value = "婚育状态")
    private String marriageStatus;

    /**
     * 子女个数
     */
    @ApiModelProperty(value = "子女个数")
    private String offspring;

    /**
     * 户口所在地
     */
    @ApiModelProperty(value = "户口所在地")
    private String hukousuozaidi;

    /**
     * 户籍类型
     */
    @ApiModelProperty(value = "户籍类型")
    private String hujileixing;

    /**
     * 通信地址
     */
    @ApiModelProperty(value = "通信地址")
    private String address;

    /**
     * 名族
     */
    @ApiModelProperty(value = "名族")
    private String nationality;

    /**
     * email
     */
    @ApiModelProperty(value = "email")
    private String email;

    /**
     * 紧急联人
     */
    @ApiModelProperty(value = "紧急联人")
    private String jinjilianxiren;

    /**
     * 紧急联系人电话
     */
    @ApiModelProperty(value = "紧急联系人电话")
    private String renxirendianhua;

    /**
     * 个人专长/业余爱好
     */
    @ApiModelProperty(value = "个人专长/业余爱好")
    private String gerenzhuanchang;

    /**
     * 在校期间获奖情况
     */
    @ApiModelProperty(value = "在校期间获奖情况")
    private String zaixiaohuojiang;

    /**
     * 自我评价-获奖情况
     */
    @ApiModelProperty(value = "自我评价-获奖情况")
    private String ziwopingjia;

    /**
     * 你是否有亲友在汇一集团工作
     */
    @Column(name = "is_hhyongzuo")
    @ApiModelProperty(value = "你是否有亲友在汇一集团工作")
    private String isHhyongzuo;

    /**
     * --关系和姓名
     */
    @Column(name = "is_hhyongzuotext")
    @ApiModelProperty(value = "--关系和姓名")
    private String isHhyongzuotext;

    /**
     * 你是否有重大疾病/手术记录？
     */
    @Column(name = "is_shoushu")
    @ApiModelProperty(value = "你是否有重大疾病/手术记录？")
    private String isShoushu;

    /**
     * --说明
     */
    @Column(name = "is_shoushutext")
    @ApiModelProperty(value = "--说明")
    private String isShoushutext;

    /**
     * 你是否有可能影响你完成所申请工作的健康缺陷或其他缺陷？
     */
    @Column(name = "is_quexian")
    @ApiModelProperty(value = "你是否有可能影响你完成所申请工作的健康缺陷或其他缺陷？")
    private String isQuexian;

    /**
     * --说明
     */
    @Column(name = "is_quexiantext")
    @ApiModelProperty(value = "--说明")
    private String isQuexiantext;

    /**
     * 你是否受到其它单位记过、察看、开除或其他严重处分？
     */
    @Column(name = "is_chufeng")
    @ApiModelProperty(value = "你是否受到其它单位记过、察看、开除或其他严重处分？")
    private String isChufeng;

    /**
     * --说明
     */
    @Column(name = "is_chufengtext")
    @ApiModelProperty(value = "--说明")
    private String isChufengtext;

    /**
     * 你是否曾因触犯法律受到刑事处罚或治安处罚？
     */
    @Column(name = "is_weifa")
    @ApiModelProperty(value = "你是否曾因触犯法律受到刑事处罚或治安处罚？")
    private String isWeifa;

    /**
     * --说明
     */
    @Column(name = "is_weifatext")
    @ApiModelProperty(value = "--说明")
    private String isWeifatext;

    /**
     * 你从何处得知本招聘信息？
     */
    @Column(name = "is_xinxilaiyuan")
    @ApiModelProperty(value = "你从何处得知本招聘信息？")
    private String isXinxilaiyuan;

    /**
     * 是否接受公司以考察你个人素质为目的相关测试（结果保密）
     */
    @Column(name = "is_xiangguanceshi")
    @ApiModelProperty(value = "是否接受公司以考察你个人素质为目的相关测试（结果保密）")
    private String isXiangguanceshi;

    /**
     * 是否接受工作调配？
     */
    @Column(name = "is_gongzuodaodong")
    @ApiModelProperty(value = "是否接受工作调配？")
    private String isGongzuodaodong;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;
    
    
    
    @Column(name = "create_date2")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate2;
    
    

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;
	
	@Column(name = "zpgl_employee_status")
	@ApiModelProperty(value = "招聘管理人员状态")
	private String zpglEmployeeStatus ;  //招聘管理人员状态
	
	@Column(name = "add_talent_pool")
	@ApiModelProperty(value = "1:加入了人才库")
	private String addTalentPool;
	
	@Column(name = "add_talent_pool_date")
	@ApiModelProperty(value = "加入了人才库时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date addTalentPoolDate;
	
	
	@Column(name = "add_interview")
	@ApiModelProperty(value = "1:已设置面试信息")
	private String addInterview;
	
	
	@ApiModelProperty(value = " 籍贯描述")
	private String birthplacetext;
	
	@ApiModelProperty(value = "户口所在地编码")
	private String hukousuozaidicode;
	
	@ApiModelProperty(value = "户口所在地描述")
	private String hukousuozaiditext;
	
	@ApiModelProperty(value = "通讯地址编码")
	private String addresscode;
	
	@ApiModelProperty(value = "通讯地址描述")
	private String addresstext;
	
	@Column(name = "applicant_dept")
	@ApiModelProperty(value = "应聘科室")
	private String applicantDept ; 
	
	@Column(name = "applicant_deptname")
	@ApiModelProperty(value = "应聘科室")
	private String applicantDeptname ; 
	
	@Column(name = "applicant_posttext")
	@ApiModelProperty(value = "应聘岗位文字")
	private String applicantPosttext ; 
	
	@Column(name = "establish_date")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(value = "应聘岗位文字")
	private Date establishDate ; 
	
	@Column(name = "add_source")
	@ApiModelProperty(value = "数据来源 1人才库加入的")
	private String addSource ; 
	
	@Column(name = "interview_path")
	@ApiModelProperty(value = "面试岗位三级路径")
	private String  interviewPath ; 
	
	@Column(name = "driving_licence_level")
	@ApiModelProperty(value = "驾驶证等级")
	private String  drivingLicenceLevel ; 
	
	@ApiModelProperty(value = "附件集合")
	private String fujian; //附件集合
	
	@ApiModelProperty(value = "简历来源")
	private String jianlilaiyuan; //简历来源
	
	@ApiModelProperty(value = "简历来源")
	private String jianlilaiyuantext; //简历来源
	
	@ApiModelProperty(value = "推荐人")
	@Column(name = "referrer_name")
	private String referrerName;
	//学历经历

	private List<HrmsZpglEducation> hrmsEducationInfo;
	
	//工作经历
	@Transient
	private List<HrmsZpglWorkrecord> hrmsZpglWorkrecord;
	
	//学术团体
	@Transient
	private List<HrmsZpglAcademy> hrmsZpglAcademy;
	
	//进修培训
	@Transient
	private List<HrmsZpglContinueLearning> hrmsZpglContinueLearning;
	
	//职称晋级
	@Transient
	private List<HrmsZpglProfessional> hrmsZpglProfessional;
	
	//家庭关系
	@Transient
	private List<HrmsZpglFamily> hrmsZpglFamily;
	
	//查询条件
	
	@Transient
	private String zhichengmingcheng;  //职称名称
	@Transient
	private String xueli;  //学历
	@Transient
	private String zhuanye;  //专业
	@Transient
	private String biyeyuanxiao; //毕业学校
	@Transient
	private String searchStartDate;  //开始时间
	@Transient
	private String searchEndDate;  //结束时间
	@Transient
	private String conform;  //初审结果
	@Transient
	private String interviewResultStatus; //面试结果
	
	@Transient
	private String allPath;   //搜索岗位
	
	@Column(name = "signature_path")
	@ApiModelProperty(value = "签名路径")
	private String signaturePath; //签名路径

    @Column(name = "avatar")
    @ApiModelProperty(value = "头像")
    private String avatar; //签名路径

    @Transient
    private String searchDsx;   //搜索岗位
    @Transient
    private String zpglEmployeeStatusText;

    @Transient
    private String resultId; //结果id
    @Transient
    private String messageId; //面试评价id

    /**
     * 人员类别
     */
    private String personnelCategory;

    /**
     * 人员类别列表
     */
    @Transient
    private List<String> personnelCategoryList;

    /**
     * 面试结果列表
     */
    @Transient
    private List<String> interviewResultStatusList;
} 
package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrTalentWorkExperience;

/**
 * @ClassName HrTalentWorkExperienceService
 * @Description TODO
 * @date 2021年6月19日 下午4:43:24
 * <AUTHOR>
 * @version 1.0
 */
public interface HrTalentWorkExperienceService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2021年6月19日 下午4:43:24
	 * <AUTHOR>
	 */
	Integer save(HrTalentWorkExperience record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2021年6月19日 下午4:43:24
	 * <AUTHOR>
	 */
	Integer update(HrTalentWorkExperience record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2021年6月19日 下午4:43:24
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrTalentWorkExperience
	 * @date 2021年6月19日 下午4:43:24
	 * <AUTHOR>
	 */
	HrTalentWorkExperience selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrTalentWorkExperience>
	 * @date 2021年6月19日 下午4:43:24
	 * <AUTHOR>
	 */
	DataSet<HrTalentWorkExperience> getDataSetList(Page page, HrTalentWorkExperience record);
	/**
	 * @Title findByTalentId
	 * @Description 根据id查询所有联系人信息（人才库或简历）
	 * @param id
	 * @return List<HrTalentWorkExperience>
	 * @date 2021/6/19 17:01
	 * <AUTHOR>
	 */
	List<HrTalentWorkExperience> findByTalentId(String id);
}

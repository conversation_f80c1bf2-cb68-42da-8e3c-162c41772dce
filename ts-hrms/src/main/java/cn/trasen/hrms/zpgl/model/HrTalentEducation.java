package cn.trasen.hrms.zpgl.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hr_talent_education")
@Setter
@Getter
public class HrTalentEducation {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Id
    @Column(name = "education_id")
    private String educationId;

    @Column(name = "talent_library_id")
    private String talentLibraryId;

    /**
     * 受教育开始时间
     */
    @Column(name = "education_start_date")
    @ApiModelProperty(value = "受教育开始时间")
    private String educationStartDate;

    /**
     * 受教育结束时间
     */
    @Column(name = "education_end_date")
    @ApiModelProperty(value = "受教育结束时间")
    private String educationEndDate;

    /**
     * 学校名称
     */
    @Column(name = "school_name")
    @ApiModelProperty(value = "学校名称")
    private String schoolName;

    /**
     * 所学专业
     */
    @ApiModelProperty(value = "所学专业")
    private String professional;

    /**
     * 学制/学历
     */
    @Column(name = "academic_info")
    @ApiModelProperty(value = "学制/学历")
    private String academicInfo;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "create_date")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_date")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_user_name")
    private String updateUserName;
}
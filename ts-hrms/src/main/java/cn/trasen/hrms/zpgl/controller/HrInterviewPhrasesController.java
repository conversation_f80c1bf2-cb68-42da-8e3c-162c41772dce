package cn.trasen.hrms.zpgl.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.entity.Result;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrInterviewPhrases;
import cn.trasen.hrms.zpgl.service.HrInterviewPhrasesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrInterviewPhrasesController
 * @Description 面试常用语
 * @date 2021年7月1日 下午1:49:09
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "面试常用语设置Controller")
public class HrInterviewPhrasesController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrInterviewPhrasesController.class);

	@Autowired
	private HrInterviewPhrasesService hrInterviewPhrasesService;

	/**
	 * @Title saveHrInterviewPhrases
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2021年7月1日 下午1:49:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/HrInterviewPhrases/save")
	public PlatformResult<String> saveHrInterviewPhrases(@RequestBody HrInterviewPhrases record) {
		try {
			hrInterviewPhrasesService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrInterviewPhrases
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2021年7月1日 下午1:49:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/HrInterviewPhrases/update")
	public PlatformResult<String> updateHrInterviewPhrases(@RequestBody HrInterviewPhrases record) {
		try {
			hrInterviewPhrasesService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrInterviewPhrasesById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrInterviewPhrases>
	 * @date 2021年7月1日 下午1:49:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/HrInterviewPhrases/{id}")
	public PlatformResult<HrInterviewPhrases> selectHrInterviewPhrasesById(@PathVariable String id) {
		try {
			HrInterviewPhrases record = hrInterviewPhrasesService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrInterviewPhrasesById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2021年7月1日 下午1:49:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/HrInterviewPhrases/delete/{id}")
	public PlatformResult<String> deleteHrInterviewPhrasesById(@PathVariable String id) {
		try {
			hrInterviewPhrasesService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrInterviewPhrasesList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrInterviewPhrases>
	 * @date 2021年7月1日 下午1:49:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/HrInterviewPhrases/list")
	public DataSet<String> selectHrInterviewPhrasesList(Page page, HrInterviewPhrases record) {
		return hrInterviewPhrasesService.getDataSetList(page, record);
	}
 /**
   * @Title cancelLabel
   * @Description X去掉当前阶段的常用语
   * @param label
   * @return Result
   * @date 2021/7/1 15:06
   * <AUTHOR>
   */
	@PostMapping("/api/HrInterviewPhrases/cancelLabel")
	@ApiOperation(value = "X去掉当前阶段的常用语", notes = "X去掉当前阶段的常用语")
	public Result cancelLabel(@RequestBody HrInterviewPhrases label){
		Result result = new Result();
		try {
			hrInterviewPhrasesService.cancelLabel(label);
			result.setSuccess(true);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			result.setSuccess(false);
			result.setMessage(e.getMessage());
		}
		return result;
	}
}

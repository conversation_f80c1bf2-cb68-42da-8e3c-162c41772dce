package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglOperation;

/**
 * @ClassName HrmsZpglOperationService
 * @Description TODO
 * @date 2023��2��23�� ����10:09:45
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglOperationService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��23�� ����10:09:45
	 * <AUTHOR>
	 */
	Integer save(HrmsZpglOperation record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��23�� ����10:09:45
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglOperation record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��23�� ����10:09:45
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglOperation
	 * @date 2023��2��23�� ����10:09:45
	 * <AUTHOR>
	 */
	HrmsZpglOperation selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglOperation>
	 * @date 2023��2��23�� ����10:09:45
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglOperation> getDataSetList(Page page, HrmsZpglOperation record);

	List<HrmsZpglOperation> getListByEmpId(String zpglempid);
}

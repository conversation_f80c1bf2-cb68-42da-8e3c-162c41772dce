package cn.trasen.hrms.zpgl.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.Map;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "hrms_zpgl_operation")
@Setter
@Getter
public class HrmsZpglOperation {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
	@Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 面试者id
     */
    @ApiModelProperty(value = "面试者id")
    private String zpglempid;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    private String title;

    /**
     * 消息内容（key,val）
     */
    @ApiModelProperty(value = "消息内容（key,val）")
    private String msg;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否")
    private String isDeleted;

	/** 
	* <p>Title:操作日志 </p> 
	* <p>Description: </p> 
	* @param zpglempid
	* @param title
	* @param msg 
	*/
	public HrmsZpglOperation(String zpglempid, String title, String msg) {
		super();
		this.zpglempid = zpglempid;
		this.title = title;
		this.msg = msg;
	}
	
	public HrmsZpglOperation(String zpglempid, String title) {
		super();
		this.zpglempid = zpglempid;
		this.title = title;
	}

	public HrmsZpglOperation() {
		super();
	}
}
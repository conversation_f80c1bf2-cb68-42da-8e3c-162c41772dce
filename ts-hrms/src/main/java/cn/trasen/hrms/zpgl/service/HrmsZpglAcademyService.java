package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglAcademy;

/**
 * @ClassName HrmsZpglAcademyService
 * @Description TODO
 * @date 2023��2��8�� ����4:12:18
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglAcademyService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����4:12:18
	 * <AUTHOR>
	 */
	Integer save(HrmsZpglAcademy record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����4:12:18
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglAcademy record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��8�� ����4:12:18
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglAcademy
	 * @date 2023��2��8�� ����4:12:18
	 * <AUTHOR>
	 */
	HrmsZpglAcademy selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglAcademy>
	 * @date 2023��2��8�� ����4:12:18
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglAcademy> getDataSetList(Page page, HrmsZpglAcademy record);

	Integer deleteByEmpId(String employeeId);

	List<HrmsZpglAcademy> findByEmpId(String employeeId);
}

package cn.trasen.hrms.zpgl.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrmsZpglOperation;
import cn.trasen.hrms.zpgl.service.HrmsZpglOperationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsZpglOperationController
 * @Description 操作记录controller
 * @date 2023��2��23�� ����10:09:45
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglOperationController")
public class HrmsZpglOperationController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglOperationController.class);

	@Autowired
	private HrmsZpglOperationService hrmsZpglOperationService;

	/**
	 * @Title saveHrmsZpglOperation
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��23�� ����10:09:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/zpglOperation/save")
	public PlatformResult<String> saveHrmsZpglOperation(@RequestBody HrmsZpglOperation record) {
		try {
			hrmsZpglOperationService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsZpglOperation
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��23�� ����10:09:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/zpglOperation/update")
	public PlatformResult<String> updateHrmsZpglOperation(@RequestBody HrmsZpglOperation record) {
		try {
			hrmsZpglOperationService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsZpglOperationById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsZpglOperation>
	 * @date 2023��2��23�� ����10:09:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/zpglOperation/{id}")
	public PlatformResult<HrmsZpglOperation> selectHrmsZpglOperationById(@PathVariable String id) {
		try {
			HrmsZpglOperation record = hrmsZpglOperationService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsZpglOperationById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��2��23�� ����10:09:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/zpglOperation/delete/{id}")
	public PlatformResult<String> deleteHrmsZpglOperationById(@PathVariable String id) {
		try {
			hrmsZpglOperationService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsZpglOperationList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglOperation>
	 * @date 2023��2��23�� ����10:09:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglOperation/list/{zpglempid}")
	public List<HrmsZpglOperation> selectHrmsZpglOperationList(@PathVariable String zpglempid) {
		return hrmsZpglOperationService.getListByEmpId(zpglempid);
	}
}

package cn.trasen.hrms.zpgl.vo.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2022/8/24 14:42
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Data
public class HrTalentLibraryInputVo {

    @ApiModelProperty(value = "人才库id")
    private List<String> zpglempid;

    @ApiModelProperty(value = "人才库标签id")
    private List<String> poolLabelId;
}

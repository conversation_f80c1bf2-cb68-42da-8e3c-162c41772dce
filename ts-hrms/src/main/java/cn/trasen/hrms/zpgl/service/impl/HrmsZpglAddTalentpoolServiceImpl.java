package cn.trasen.hrms.zpgl.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.google.common.collect.Maps;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrmsZpglAddTalentpoolMapper;
import cn.trasen.hrms.zpgl.enums.ZpglEmployeeStatusEnum;
import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPoolLabel;
import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPoolLabelTalentLibrary;
import cn.trasen.hrms.zpgl.model.HrmsZpglAddTalentpool;
import cn.trasen.hrms.zpgl.model.HrmsZpglEmployee;
import cn.trasen.hrms.zpgl.model.HrmsZpglInterviewMessage;
import cn.trasen.hrms.zpgl.model.HrmsZpglInterviewResult;
import cn.trasen.hrms.zpgl.model.HrmsZpglOperation;
import cn.trasen.hrms.zpgl.model.HrmsZpglProfessional;
import cn.trasen.hrms.zpgl.service.HrRecruitmentTalentPoolLabelService;
import cn.trasen.hrms.zpgl.service.HrRecruitmentTalentPoolLabelTalentLibraryService;
import cn.trasen.hrms.zpgl.service.HrmsZpglAddTalentpoolService;
import cn.trasen.hrms.zpgl.service.HrmsZpglEmployeeService;
import cn.trasen.hrms.zpgl.service.HrmsZpglInterviewMessageService;
import cn.trasen.hrms.zpgl.service.HrmsZpglInterviewResultService;
import cn.trasen.hrms.zpgl.service.HrmsZpglOperationService;
import cn.trasen.hrms.zpgl.service.HrmsZpglWorkrecordService;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglAddTalentpoolOutListVo;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsZpglAddTalentpoolServiceImpl
 * @Description TODO
 * @date 2023��2��16�� ����1:59:23
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsZpglAddTalentpoolServiceImpl implements HrmsZpglAddTalentpoolService {

	@Autowired
	private HrmsZpglAddTalentpoolMapper mapper;
	@Autowired
	HrmsZpglEmployeeService hrmsZpglEmployeeService;

	@Autowired
	HrmsZpglInterviewMessageService hrmsZpglInterviewMessageService;
	@Autowired
	HrmsZpglInterviewResultService hrmsZpglInterviewResultService;
	
    @Autowired
    DictItemFeignService dictItemFeignService;
    
	@Autowired
	HrmsZpglOperationService hrmsZpglOperationService;
	
	@Autowired
	HrRecruitmentTalentPoolLabelService hrRecruitmentTalentPoolLabelService;
	@Autowired
	HrRecruitmentTalentPoolLabelTalentLibraryService hrRecruitmentTalentPoolLabelTalentLibraryService;
	@Autowired
	HrmsZpglWorkrecordService hrmsZpglWorkrecordService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsZpglAddTalentpool record) {
		
		
		//如果人才库表有就update
		Example example = new Example(HrmsZpglAddTalentpool.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("zpglempid", record.getZpglempid());
		example.setOrderByClause("create_date desc");
		 List<HrmsZpglAddTalentpool> selectByExample = mapper.selectByExample(example);
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		 if(selectByExample != null && selectByExample.size() >0) {
			 	HrmsZpglAddTalentpool oldBean = selectByExample.get(0);
				oldBean.setUpdateDate(new Date());
				
				oldBean.setAffiliationDept(record.getAffiliationDept());
				oldBean.setAffiliationDepttext(record.getAffiliationDepttext());
				oldBean.setAffiliationJob(record.getAffiliationJob());
				oldBean.setAffiliationJobtext(record.getAffiliationJobtext());
				oldBean.setInterviewPool(record.getInterviewPool());
				ThpsUser user = UserInfoHolder.getCurrentUserInfo();
				if (user != null) {
					oldBean.setUpdateUser(user.getUsercode());
					oldBean.setUpdateUserName(user.getUsername());
				}
				try {
					mapper.updateByPrimaryKeySelective(oldBean);
					//修改人员主表 加入人才库 
					HrmsZpglEmployee emp = new HrmsZpglEmployee();
					emp.setAddTalentPool("1");
					emp.setId(record.getZpglempid());
					emp.setAddTalentPoolDate(new Date());
					hrmsZpglEmployeeService.updateBasic(emp);
				} catch (Exception e) {
					log.error("加入人才库异常"+e.getMessage());
					throw new BusinessException("加入人才库异常"+e.getMessage());
				}
				
		 }else {
				
				record.setId(IdUtil.getId());
				record.setCreateDate(new Date());
				record.setUpdateDate(new Date());
				record.setIsDeleted("N");
				ThpsUser user = UserInfoHolder.getCurrentUserInfo();
				if (user != null) {
					record.setCreateUser(user.getUsercode());
					record.setCreateUserName(user.getUsername());
					record.setUpdateUser(user.getUsercode());
					record.setUpdateUserName(user.getUsername());
				}
				try {
					record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
					mapper.insertSelective(record);
					//修改人员主表 加入人才库 
					HrmsZpglEmployee emp = new HrmsZpglEmployee();
					emp.setAddTalentPool("1");
					emp.setId(record.getZpglempid());
					emp.setAddTalentPoolDate(new Date());
					hrmsZpglEmployeeService.updateBasic(emp);
				} catch (Exception e) {
					log.error("加入人才库异常"+e.getMessage());
					throw new BusinessException("加入人才库异常"+e.getMessage());
				}
				 
		 }
		
			List<HrRecruitmentTalentPoolLabel> allList = hrRecruitmentTalentPoolLabelService.getAllList();
			if(allList != null & allList.size() >0) {
				for (int k = 0; k < allList.size(); k++) {
					if(!StringUtil.isEmpty(allList.get(k).getLabelJob()) && allList.get(k).getLabelJob().contains(record.getAffiliationJob())) {
						 HrRecruitmentTalentPoolLabelTalentLibrary tpltlBean = new HrRecruitmentTalentPoolLabelTalentLibrary();
						 tpltlBean.setHrRecruitmentTalentPoolLabelId(allList.get(k).getHrRecruitmentTalentPoolLabelId());
						 tpltlBean.setTalentLibraryId(record.getZpglempid());
						 tpltlBean.setType(1);
				         hrRecruitmentTalentPoolLabelTalentLibraryService.save(tpltlBean);
					}
				}
			}
		return  1;
	}
	
	//人才库修改
	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsZpglAddTalentpool record) {
		
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//删除人员   删除人才库 重新添加
		
		hrmsZpglEmployeeService.deleteById(record.getZpglempid(),"1");
		String employeeId = IdUtil.getId();
		HrmsZpglEmployee employee = new HrmsZpglEmployee();
		employee.setId(employeeId);
		employee.setIphone(record.getIphone());
		employee.setAddTalentPool("1");
		employee.setAddTalentPoolDate(new Date());
		employee.setEmployeeName(record.getEmployeeName());
		employee.setAddSource("1");  //由人才库加入的
		List<HrmsZpglProfessional> listPro = new ArrayList<HrmsZpglProfessional>();
		HrmsZpglProfessional pro = new HrmsZpglProfessional();
		pro.setZpglempid(employeeId);
		pro.setZhichengmingcheng(record.getZhichengmingcheng());
		listPro.add(pro);
		employee.setHrmsZpglProfessional(listPro);
		//添加人员主表
		hrmsZpglEmployeeService.save(employee);
		record.setZpglempid(employeeId);
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String zpglempid) {
		
		try {
			//修改人员主表 加入人才库 
			HrmsZpglEmployee emp = new HrmsZpglEmployee();
			emp.setAddTalentPool("2");
			emp.setId(zpglempid);
			emp.setUpdateDate(new Date());
			hrmsZpglEmployeeService.updateBasic(emp);
			
			
			Example example = new Example(HrmsZpglInterviewResult.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("zpglempid", zpglempid);
			
			HrmsZpglAddTalentpool record = new HrmsZpglAddTalentpool();
			record.setUpdateDate(new Date());
			record.setIsDeleted("Y");
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			mapper.updateByExampleSelective(record, example);
		} catch (Exception e) {
			log.error("人才库删除异常"+e.getMessage());
			throw new BusinessException(e.getMessage());
		}
		
		return 1;
	}

	@Override
	public HrmsZpglAddTalentpool selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}
	
	//人才库列表
	@Override
	public DataSet<HrmsZpglAddTalentpoolOutListVo> getDataSetList(Page page, HrmsZpglAddTalentpool record) {
		//根据当前登录账号机构编码过滤查询数据
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsZpglAddTalentpoolOutListVo> records = mapper.getPageList(page,record);
		 //处理工作年限  性别 学历
		Map<String, String> sexConvertDictMap = convertDictMap("SEX_TYPE");
		Map<String, String> educationConvertDictMap = convertDictMap("education_type");
		
		if (records != null && records.size() > 0) {
			for (int i = 0; i < records.size(); i++) {
				records.get(i).setNo(String.valueOf(i+1));
				records.get(i).setGendertext(sexConvertDictMap.get(records.get(i).getGender()));
				records.get(i).setXuelitext(educationConvertDictMap.get(records.get(i).getXueli()));
				String _workunit = hrmsZpglWorkrecordService.getOneData(records.get(i).getZpglempid());
				//处理工作单位
				records.get(i).setWorkUnit(_workunit);

				if("1".equals(records.get(i).getZpglEmployeeStatus())){
					if("1".equals(records.get(i).getAddInterview())){
						records.get(i).setZpglEmployeeStatusText("待面试");
					}else if("2".equals(records.get(i).getAddInterview())){
						records.get(i).setZpglEmployeeStatusText("筛选不通过");
					}else{
						records.get(i).setZpglEmployeeStatusText("待筛选");
					}

				}else if("2".equals(records.get(i).getZpglEmployeeStatus())){
					//records.get(i)
					if("1".equals(records.get(i).getInterviewResultStatus())){
						records.get(i).setZpglEmployeeStatusText("面试通过");
					}else if("2".equals(records.get(i).getInterviewResultStatus())){
						records.get(i).setZpglEmployeeStatusText("面试不通过");
					}else if("3".equals(records.get(i).getInterviewResultStatus())){
						records.get(i).setZpglEmployeeStatusText("面试待定");
					}else if("4".equals(records.get(i).getInterviewResultStatus())){
						//继续面试
						records.get(i).setZpglEmployeeStatusText("待面试");
					}else{
						records.get(i).setZpglEmployeeStatusText("待面试");
					}
				}else{
					records.get(i).setZpglEmployeeStatusText(ZpglEmployeeStatusEnum.getValByKey(records.get(i).getZpglEmployeeStatus()));
				}
				//处理工作年限 
				if(!StringUtil.isEmpty(records.get(i).getEntryDate())) {
					try {
						String yearCompare = DateUtils.yearCompare(DateUtils.getStringToDate(records.get(i).getEntryDate()),new Date());
						records.get(i).setEntryDateText(yearCompare + "年");
					} catch (Exception e) {
						log.error("工作年限转换异常");
					}
				}
			}
		}
	 
		
		
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }
	
	//人才库新增人员
	@Transactional(readOnly = false)
	@Override
	public Integer add(HrmsZpglAddTalentpool record) {
		
		
		
		String employeeId = IdUtil.getId();
		
		HrmsZpglEmployee employee = new HrmsZpglEmployee();
		employee.setId(employeeId);
		employee.setIphone(record.getIphone());
		employee.setAddTalentPool("1");
		employee.setAddTalentPoolDate(new Date());
		employee.setEmployeeName(record.getEmployeeName());
		employee.setAddSource("1");  //由人才库加入的
		List<HrmsZpglProfessional> listPro = new ArrayList<HrmsZpglProfessional>();
		HrmsZpglProfessional pro = new HrmsZpglProfessional();
		pro.setZpglempid(employeeId);
		pro.setZhichengmingcheng(record.getZhichengmingcheng());
		pro.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		listPro.add(pro);
		employee.setHrmsZpglProfessional(listPro);
		employee.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		//添加人员主表
		hrmsZpglEmployeeService.save(employee);
		//添加人才库表
	
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
		}	
		record.setCreateDate(new Date());
		record.setIsDeleted("N");
		record.setId(IdUtil.getId());
		record.setZpglempid(employeeId);
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		mapper.insertSelective(record);
		
		return 1;
	}

	//进入待入职
	@Transactional(readOnly = false)
	@Override
	public Integer addEntery(String id) {
		
		//设置一条面试信息
		HrmsZpglAddTalentpool bean = mapper.selectByPrimaryKey(id);  //人才库信息
		//根据人员id 如果有 就update 没有就insert
		HrmsZpglInterviewMessage oldInMsgBean = hrmsZpglInterviewMessageService.selectByEmpId(bean.getZpglempid());
		if(oldInMsgBean != null) {
			oldInMsgBean.setConform("1");
			oldInMsgBean.setInterviewDept(bean.getAffiliationDept());
			oldInMsgBean.setInterviewDepttext(bean.getAffiliationDepttext());
			oldInMsgBean.setInterviewJob(bean.getAffiliationJob());
			oldInMsgBean.setInterviewJobtext(bean.getAffiliationJobtext());
			oldInMsgBean.setMsgInterviewPath(bean.getInterviewPool());
			hrmsZpglInterviewMessageService.update(oldInMsgBean);
		}else {
			HrmsZpglInterviewMessage inMsg = new HrmsZpglInterviewMessage();
			inMsg.setZpglempid(bean.getZpglempid());
			inMsg.setConform("1");
			inMsg.setInterviewDept(bean.getAffiliationDept());
			inMsg.setInterviewDepttext(bean.getAffiliationDepttext());
			inMsg.setInterviewJob(bean.getAffiliationJob());
			inMsg.setInterviewJobtext(bean.getAffiliationJobtext());
			inMsg.setMsgInterviewPath(bean.getInterviewPool());
			inMsg.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());

			//根据  人员id 查询人才库信息
			hrmsZpglInterviewMessageService.save(inMsg);
		}
		
		//有面试评价就改 没有就增
		HrmsZpglInterviewResult inResBean = hrmsZpglInterviewResultService.selectByEmpId(bean.getZpglempid());
		
		if(inResBean != null) {
			inResBean.setInterviewResultStatus("1");
			inResBean.setStudyoutDept(bean.getAffiliationDept());
			inResBean.setStudyoutDepttext(bean.getAffiliationDepttext());
			inResBean.setStudyoutJob(bean.getAffiliationJob());
			inResBean.setStudyoutJobtext(bean.getAffiliationJobtext());
			inResBean.setResInterviewPath(bean.getInterviewPool());
			hrmsZpglInterviewResultService.update(inResBean);
			//设置一条面试评价
		}else {
			HrmsZpglInterviewResult inRes = new HrmsZpglInterviewResult();
			inRes.setZpglempid(bean.getZpglempid());
			inRes.setInterviewResultStatus("1");
			inRes.setStudyoutDept(bean.getAffiliationDept());
			inRes.setStudyoutDepttext(bean.getAffiliationDepttext());
			inRes.setStudyoutJob(bean.getAffiliationJob());
			inRes.setStudyoutJobtext(bean.getAffiliationJobtext());
			inRes.setResInterviewPath(bean.getInterviewPool());
			inRes.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			hrmsZpglInterviewResultService.save(inRes);
		}
		
		HrmsZpglEmployee record = new HrmsZpglEmployee();
		record.setId(bean.getZpglempid());
		//设置为待入职
		record.setZpglEmployeeStatus(ZpglEmployeeStatusEnum.ZPGL_EMPLOYEE_STATUS_3.getKey());
		record.setAddTalentPool("2");
		hrmsZpglEmployeeService.updateBasic(record);
		
		HrmsZpglOperation operation = new HrmsZpglOperation();
		operation.setTitle("进入待入职");
		operation.setZpglempid(bean.getZpglempid());
		operation.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsZpglOperationService.save(operation);
		
		return 1;
	}

	@Override
	public List<HrmsZpglAddTalentpoolOutListVo> export(HrmsZpglAddTalentpool record) {
		Page page = new Page();
		page.setPageSize(Integer.MAX_VALUE);
		DataSet<HrmsZpglAddTalentpoolOutListVo> dataSetList = getDataSetList(page,record);
		return dataSetList.getRows();
	}

	@Override
	public HrmsZpglAddTalentpool selectByEmpId(String zpglempid) {
		
		Example example = new Example(HrmsZpglAddTalentpool.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("zpglempid", zpglempid);
		example.setOrderByClause("create_date desc");
		List<HrmsZpglAddTalentpool> list = mapper.selectByExample(example);
		if(list!= null && list.size() >0) {
			return list.get(0);
		}
		return null;
	}

	@Override
	public List<HrmsZpglAddTalentpool> getListByAffiliationJob(String affiliationJob) {
		Example example = new Example(HrmsZpglAddTalentpool.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("affiliationJob", affiliationJob);
		example.setOrderByClause("create_date desc");
		return mapper.selectByExample(example);
	}
}

package cn.trasen.hrms.zpgl.vo.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;


@Setter
@Getter
public class HrRecruitmentTalentPoolInputVo {

    @ApiModelProperty(value = "招聘-人才库主键")
    private String hrRecruitmentTalentPoolId;

    @ApiModelProperty(value = "招聘-人才库编号")
    private String talentPoolNo;
    
    @ApiModelProperty(value = "备注")
    private String remark;

    @NotEmpty(message = "人才库名称不能为空")
    @ApiModelProperty(value = "招聘-人才库名称")
    private String talentPoolName;

    @ApiModelProperty(value = "招聘-人才库上级机构id")
    private String parentId;

    @ApiModelProperty(value = "招聘-人才库上级机构名称")
    private String parentName;

    @ApiModelProperty(value = "招聘-人才库上级机构编号")
    private String parentTalentPoolNo;
    
    @ApiModelProperty(value = "序号")
    private Integer seqNo; //v
    
    @ApiModelProperty(value = "全路径")
    private String fullpath; //

}
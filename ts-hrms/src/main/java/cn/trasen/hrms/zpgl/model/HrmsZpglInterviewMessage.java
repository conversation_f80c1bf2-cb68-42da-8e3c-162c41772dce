package cn.trasen.hrms.zpgl.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

/**
 * 面试管理表
 *
 */
@Table(name = "hrms_zpgl_interview_message")
@Setter
@Getter
public class HrmsZpglInterviewMessage {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;


    /**
     * id
     */
	@Id
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 面试者id
     */
    @ApiModelProperty(value = "面试者id")
    private String zpglempid;

    /**
     * 是否符合 1:符合 2不符合
     */
    @ApiModelProperty(value = "是否符合 1:符合 2不符合")
    private String conform;

    /**
     * 面试科室
     */
    @Column(name = "interview_dept")
    @ApiModelProperty(value = "面试科室")
    private String interviewDept;

    /**
     * 面试岗位
     */
    @Column(name = "interview_job")
    @ApiModelProperty(value = "面试岗位")
    private String interviewJob;

    /**
     * 笔试考核人
     */
    @Column(name = "written_name")
    @ApiModelProperty(value = "笔试考核人")
    private String writtenName;

    /**
     * 笔试时间
     */
    @Column(name = "written_date")
    @ApiModelProperty(value = "笔试时间")
    private String writtenDate;

    /**
     * 实操考核人
     */
    @Column(name = "operation_name")
    @ApiModelProperty(value = "实操考核人")
    private String operationName;

    /**
     * 实操时间
     */
    @Column(name = "operation_date")
    @ApiModelProperty(value = "实操时间")
    private String operationDate;

    /**
     * 面试考核人
     */
    @Column(name = "interview_name")
    @ApiModelProperty(value = "面试考核人")
    private String interviewName;

    /**
     * 面试时间
     */
    @Column(name = "interview_date")
    @ApiModelProperty(value = "面试时间")
    private String interviewDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateUserName;

    /**
     * 删除标识(N存在，Y删除)
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识(N存在，Y删除)")
    private String isDeleted;
    
    /**
     *面试科室名称  
     */
    @Column(name = "interview_depttext")
    @ApiModelProperty(value = "面试科室名称  ")
    private String interviewDepttext;
    
    /**
     *面试岗位名称  
     */
    @Column(name = "interview_jobtext")
    @ApiModelProperty(value = "面试岗位名称 ")
    private String interviewJobtext;
    
    @Column(name = "interview_status")
    @ApiModelProperty(value = "面试状态 ")
    private String  interviewStatus;  //面试状态  1 待面试 2面试通过3不通过 4待定 
    
    @Column(name = "written_emp_no")
    @ApiModelProperty(value = "笔试考核人empNO")
    private String writtenEmpNo;
    
    @Column(name = "operation_emp_no")
    @ApiModelProperty(value = "实操考核人empNo")
    private String operationEmpNo;
    
    @Column(name = "interview_emp_no")
    @ApiModelProperty(value = "面试考核人empNo")
    private String interviewEmpNo;
    
    @Column(name = "sign_in")
    @ApiModelProperty(value = "是否签到 1 已签到")
    private String signIn;
    
    @Column(name = "sign_in_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "签到时间")
    private String signInDate;
    
    @Column(name = "msg_interview_path")
    @ApiModelProperty(value = "岗位全路径")
    private String msgInterviewPath;
    
	@Transient
	private String searchStartDate;  //开始时间
	@Transient
	private String searchEndDate;  //结束时间
   
    @Transient
    private String gender;  //性别
  
    @Transient
	private String employeeName;  //姓名
    
	@Transient
	private String zhichengmingcheng;  //职称名称
	@Transient
	private String xueli;  //学历
	@Transient
	private String zhuanye;  //专业
	@Transient
	private String biyeyuanxiao; //毕业学校
	
	@Transient
	@ApiModelProperty(value = "参加工作时间")
	private String searchEntryDate;
	
	@Transient
	private String zpglEmployeeStatus;  //人员状态
	
	@Transient
	private String identityNumber;   //身份证
	
	@Transient
	private List<String> zpglempidList;
	@Transient
	private String allPath;  //岗位模糊搜索
    
}
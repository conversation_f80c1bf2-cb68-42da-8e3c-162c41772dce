package cn.trasen.hrms.zpgl.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglInterviewResult;

/**
 * @ClassName HrmsZpglInterviewResultService
 * @Description TODO
 * @date 2023��2��16�� ����6:09:54
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglInterviewResultService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��16�� ����6:09:54
	 * <AUTHOR>
	 */
	Integer save(HrmsZpglInterviewResult record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��16�� ����6:09:54
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglInterviewResult record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��16�� ����6:09:54
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglInterviewResult
	 * @date 2023��2��16�� ����6:09:54
	 * <AUTHOR>
	 */
	HrmsZpglInterviewResult selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglInterviewResult>
	 * @date 2023��2��16�� ����6:09:54
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglInterviewResult> getDataSetList(Page page, HrmsZpglInterviewResult record);

	void entryId(String id);
	
	/** 
	* @Title: selectByEmpId 
	* @Description: 根据人员id查询面试评价
	* @param @param zpglempid
	* @param @return    设定文件 
	* @return HrmsZpglInterviewResult    返回类型 
	* @throws 
	*/
	HrmsZpglInterviewResult selectByEmpId(String zpglempid);

	Integer deleteAllByEmpId(String empId);
}

package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPoolLabel;

/**
 * @ClassName HrRecruitmentTalentPoolLabelService
 * @Description 人才库标签
 * <AUTHOR>
 * @version 1.0
 */
public interface HrRecruitmentTalentPoolLabelService {


	/**
	 * 启用禁用
	 * @param recruitmentTalentPoolLabelId 主键
	 * @param status 状态（N停用Y启用）
	 * @return
	 */
	Integer enableDisable(String recruitmentTalentPoolLabelId,String status);

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2022��8��11�� ����5:30:22
	 * <AUTHOR>
	 */
	Integer save(HrRecruitmentTalentPoolLabel record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2022��8��11�� ����5:30:22
	 * <AUTHOR>
	 */
	Integer update(HrRecruitmentTalentPoolLabel record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2022��8��11�� ����5:30:22
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrRecruitmentTalentPoolLabel
	 * @date 2022��8��11�� ����5:30:22
	 * <AUTHOR>
	 */
	HrRecruitmentTalentPoolLabel selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrRecruitmentTalentPoolLabel>
	 * @date 2022��8��11�� ����5:30:22
	 * <AUTHOR>
	 */
	DataSet<HrRecruitmentTalentPoolLabel> getDataSetList(Page page, HrRecruitmentTalentPoolLabel record);


	/**
	 * 获取人才库的标签
	 * @param talentLibraryId 人才库主键
	 * @return
	 */
	List<HrRecruitmentTalentPoolLabel> getTalentLibraryLableById(String talentLibraryId);
	
	/** 
	* @Title: getAllList 
	* @Description: 获取所有标签
	* @param @return    设定文件 
	* @return List<HrRecruitmentTalentPoolLabel>    返回类型 
	* @throws 
	*/
	List<HrRecruitmentTalentPoolLabel> getAllList();
	
}

package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglFamily;

/**
 * @ClassName HrmsZpglFamilyService
 * @Description TODO
 * @date 2023��2��8�� ����4:15:03
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglFamilyService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����4:15:03
	 * <AUTHOR>
	 */
	Integer save(HrmsZpglFamily record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����4:15:03
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglFamily record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��8�� ����4:15:03
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglFamily
	 * @date 2023��2��8�� ����4:15:03
	 * <AUTHOR>
	 */
	HrmsZpglFamily selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglFamily>
	 * @date 2023��2��8�� ����4:15:03
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglFamily> getDataSetList(Page page, HrmsZpglFamily record);

	Integer deleteByEmpId(String employeeId);

	List<HrmsZpglFamily> findByEmpId(String employeeId);
}

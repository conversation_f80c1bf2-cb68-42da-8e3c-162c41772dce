package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglTraceSetting;

/**
 * @ClassName HrmsZpglTraceSettingService
 * @Description TODO
 * @date 2023��2��9�� ����3:28:45
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglTraceSettingService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��9�� ����3:28:45
	 * <AUTHOR>
	 */
	Integer save(List<HrmsZpglTraceSetting> record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��9�� ����3:28:45
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglTraceSetting record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��9�� ����3:28:45
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglTraceSetting
	 * @date 2023��2��9�� ����3:28:45
	 * <AUTHOR>
	 */
	HrmsZpglTraceSetting selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglTraceSetting>
	 * @date 2023��2��9�� ����3:28:45
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglTraceSetting> getDataSetList(Page page, HrmsZpglTraceSetting record);

	/** 
	* @Title: getListAll 
	* @Description: 获取所有跟踪配置
	* @param @return    设定文件 
	* @return List<HrmsZpglTraceSetting>    返回类型 
	* @throws 
	*/
	List<HrmsZpglTraceSetting> getListAll();
}

package cn.trasen.hrms.zpgl.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrmsZpglFamily;
import cn.trasen.hrms.zpgl.service.HrmsZpglFamilyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsZpglFamilyController
 * @Description TODO
 * @date 2023��2��8�� ����4:15:03
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglFamilyController")
public class HrmsZpglFamilyController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglFamilyController.class);

	@Autowired
	private HrmsZpglFamilyService hrmsZpglFamilyService;

	/**
	 * @Title saveHrmsZpglFamily
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:15:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/zpglFamily/save")
	public PlatformResult<String> saveHrmsZpglFamily(@RequestBody HrmsZpglFamily record) {
		try {
			hrmsZpglFamilyService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsZpglFamily
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:15:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/zpglFamily/update")
	public PlatformResult<String> updateHrmsZpglFamily(@RequestBody HrmsZpglFamily record) {
		try {
			hrmsZpglFamilyService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsZpglFamilyById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsZpglFamily>
	 * @date 2023��2��8�� ����4:15:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/zpglFamily/{id}")
	public PlatformResult<HrmsZpglFamily> selectHrmsZpglFamilyById(@PathVariable String id) {
		try {
			HrmsZpglFamily record = hrmsZpglFamilyService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsZpglFamilyById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:15:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/zpglFamily/delete/{id}")
	public PlatformResult<String> deleteHrmsZpglFamilyById(@PathVariable String id) {
		try {
			hrmsZpglFamilyService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsZpglFamilyList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglFamily>
	 * @date 2023��2��8�� ����4:15:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglFamily/list")
	public DataSet<HrmsZpglFamily> selectHrmsZpglFamilyList(Page page, HrmsZpglFamily record) {
		return hrmsZpglFamilyService.getDataSetList(page, record);
	}
}

package cn.trasen.hrms.zpgl.dao;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.zpgl.model.HrmsZpglEmployee;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglEmployeeOutListVo;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsZpglEmployeeMapper extends Mapper<HrmsZpglEmployee> {


	List<HrmsZpglEmployeeOutListVo> getPageList(Page page, HrmsZpglEmployee record);

    List<HrmsZpglEmployeeOutListVo> getDscPageList(Page page, HrmsZpglEmployee record);
}
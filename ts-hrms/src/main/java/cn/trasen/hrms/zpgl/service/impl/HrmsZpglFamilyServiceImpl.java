package cn.trasen.hrms.zpgl.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrmsZpglFamilyMapper;
import cn.trasen.hrms.zpgl.model.HrmsZpglFamily;
import cn.trasen.hrms.zpgl.service.HrmsZpglFamilyService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsZpglFamilyServiceImpl
 * @Description TODO
 * @date 2023��2��8�� ����4:15:03
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsZpglFamilyServiceImpl implements HrmsZpglFamilyService {

	@Autowired
	private HrmsZpglFamilyMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsZpglFamily record) {
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsZpglFamily record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsZpglFamily record = new HrmsZpglFamily();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsZpglFamily selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsZpglFamily> getDataSetList(Page page, HrmsZpglFamily record) {
		Example example = new Example(HrmsZpglFamily.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsZpglFamily> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public Integer deleteByEmpId(String employeeId) {
		HrmsZpglFamily record = new HrmsZpglFamily();
		record.setZpglempid(employeeId);
		return mapper.delete(record);
	}

	@Override
	public List<HrmsZpglFamily> findByEmpId(String employeeId) {
		Example example = new Example(HrmsZpglFamily.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("zpglempid", employeeId);
		return mapper.selectByExample(example);
	}
}

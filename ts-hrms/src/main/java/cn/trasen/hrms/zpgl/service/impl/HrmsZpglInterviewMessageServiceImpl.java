package cn.trasen.hrms.zpgl.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrmsZpglInterviewMessageMapper;
import cn.trasen.hrms.zpgl.enums.ZpglEmployeeStatusEnum;
import cn.trasen.hrms.zpgl.enums.ZpglInterviewStatusEnum;
import cn.trasen.hrms.zpgl.model.HrmsZpglEmployee;
import cn.trasen.hrms.zpgl.model.HrmsZpglInterviewMessage;
import cn.trasen.hrms.zpgl.model.HrmsZpglOperation;
import cn.trasen.hrms.zpgl.service.HrmsZpglEmployeeService;
import cn.trasen.hrms.zpgl.service.HrmsZpglInterviewMessageService;
import cn.trasen.hrms.zpgl.service.HrmsZpglOperationService;
import cn.trasen.hrms.zpgl.service.HrmsZpglWorkrecordService;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglInterviewMessageResultOutListVo;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsZpglInterviewMessageServiceImpl
 * @Description TODO
 * @date 2023��2��9�� ����4:20:25
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsZpglInterviewMessageServiceImpl implements HrmsZpglInterviewMessageService {

	@Autowired
	private HrmsZpglInterviewMessageMapper mapper;
	@Autowired
	private HrmsZpglEmployeeService hrmsZpglEmployeeService;
    @Autowired
    DictItemFeignService dictItemFeignService;
    
	@Autowired
	HrmsZpglOperationService hrmsZpglOperationService;
	
	@Autowired
	InformationFeignService informationFeignService;
	@Autowired
	HrmsZpglWorkrecordService hrmsZpglWorkrecordService;
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsZpglInterviewMessage record) {
		
		HrmsZpglEmployee selectById = hrmsZpglEmployeeService.selectById(record.getZpglempid());
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
		}
		
		try {
			
			//符合要求 设置为 待面试
			if("1".equals(record.getConform())) {
				record.setInterviewStatus(ZpglInterviewStatusEnum.ZPGL_INTERVIEW_STATUS_1.getKey());//设置待面试
			}
			
			HrmsZpglInterviewMessage oldBean = selectByEmpId(record.getZpglempid());
			if(oldBean != null) {
				oldBean.setConform("1");
				record.setId(oldBean.getId());
				update(record);
			}else {
				record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				mapper.insertSelective(record);
				
				HrmsZpglOperation operation = new HrmsZpglOperation();
				operation.setTitle("设置面试信息");
				operation.setZpglempid(record.getZpglempid());
				JSONObject msg = new JSONObject();
				
				if("1".equals(record.getConform())) {
					msg.put("是否符合", "符合");
				}else {
					msg.put("是否符合", "不符合");
				}
				msg.put("面试科室", record.getInterviewDepttext());
				msg.put("面试岗位", record.getInterviewJobtext());
				msg.put("面试时间", record.getInterviewDate()); 
				operation.setMsg(msg.toJSONString());
				operation.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				hrmsZpglOperationService.save(operation);
			}
			HrmsZpglEmployee emp = new HrmsZpglEmployee();
			emp.setId(record.getZpglempid());
			if("1".equals(record.getConform())) {
				emp.setAddInterview("1");  //
			}else{
				emp.setAddInterview("2");  // 面试筛查不合格
			}

			hrmsZpglEmployeeService.updateBasic(emp);
		} catch (Exception e) {
			log.error("设置面试信息"+e.getMessage());
			throw new BusinessException("设置面试信息"+e.getMessage());
		}
		
		
		
		try {
			if("1".equals(record.getConform())) {
				//发送通知
				String bishiMsg = selectById.getEmployeeName()+" 于 " +record.getWrittenDate() + " 日进行笔试，请注意合理安排时间！" ;
				sendMessage(bishiMsg,record.getWrittenEmpNo());  //笔试通知
				//实操通知 
				String shicaoMsg = selectById.getEmployeeName()+" 于 " +record.getOperationDate() + " 日进行实操，请注意合理安排时间！" ;
				sendMessage(shicaoMsg,record.getOperationEmpNo());  //笔试通知
				//面试通知
				String mianshiMsg = selectById.getEmployeeName()+" 于 " +record.getInterviewDate() + " 日进行面试，请注意合理安排时间！" ;
				sendMessage(mianshiMsg,record.getInterviewEmpNo());  //笔试通知
			}
		} catch (Exception e) {
			log.error("面试信息通知发送失败"+e.getMessage());
		}
	
		return 1;
	}
	
    private void sendMessage(String content,String receiver ) {
        
    	NoticeReq noticeVo = NoticeReq.builder()
				.content(content)
				.noticeType("3")
				.subject("招聘管理")
				.sender(UserInfoHolder.getCurrentUserInfo().getUsername())
				.senderName(UserInfoHolder.getCurrentUserInfo().getUsername())
				.receiver(receiver)
				.wxSendType("2")
				.source("招聘管理")
				.build();
		 informationFeignService.sendNotice(noticeVo);
        		
    }

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsZpglInterviewMessage record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		HrmsZpglEmployee selectById = hrmsZpglEmployeeService.selectById(record.getZpglempid());
		
		try {
			if("1".equals(record.getConform())) {
				//发送通知
				String bishiMsg = selectById.getEmployeeName()+" 于 " +record.getWrittenDate() + " 日进行笔试，请注意合理安排时间！" ;
				sendMessage(bishiMsg,record.getWrittenEmpNo());  //笔试通知
				//实操通知 
				String shicaoMsg = selectById.getEmployeeName()+" 于 " +record.getOperationDate() + " 日进行实操，请注意合理安排时间！" ;
				sendMessage(shicaoMsg,record.getOperationEmpNo());  //笔试通知
				//面试通知
				String mianshiMsg = selectById.getEmployeeName()+" 于 " +record.getInterviewDate() + " 日进行面试，请注意合理安排时间！" ;
				sendMessage(mianshiMsg,record.getInterviewEmpNo());  //笔试通知
			}
		} catch (Exception e) {
			log.error("面试通知发送失败"+e.getMessage());
		}
		
		HrmsZpglOperation operation = new HrmsZpglOperation();
		operation.setTitle("变更面试信息");
		operation.setZpglempid(record.getZpglempid());
		JSONObject msg = new JSONObject();
		
		if("1".equals(record.getConform())) {
			record.setInterviewStatus("1");
			msg.put("是否符合", "符合");
		}else {
			msg.put("是否符合", "不符合");
		}
		msg.put("面试科室", record.getInterviewDepttext());
		msg.put("面试岗位", record.getInterviewJobtext());
		msg.put("面试时间", record.getInterviewDate()); 
		operation.setMsg(msg.toJSONString());
		operation.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsZpglOperationService.save(operation);

		//修改最后操作时间
		HrmsZpglEmployee zpglemp = new HrmsZpglEmployee();
		zpglemp.setId(record.getZpglempid());
		if("1".equals(record.getConform())) {
			zpglemp.setAddInterview("1");
		}else {
			zpglemp.setAddInterview("2");
		}
		hrmsZpglEmployeeService.updateBasic(zpglemp);
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsZpglInterviewMessage record = new HrmsZpglInterviewMessage();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		HrmsZpglOperation operation = new HrmsZpglOperation();
		operation.setTitle("删除面试信息");
		operation.setZpglempid(record.getZpglempid());
		JSONObject msg = new JSONObject();
		
		if("1".equals(record.getConform())) {
			msg.put("是否符合", "符合");
		}else {
			msg.put("是否符合", "不符合");
		}
		msg.put("面试科室", record.getInterviewDepttext());
		msg.put("面试岗位", record.getInterviewJobtext());
		msg.put("面试时间", record.getInterviewDate()); 
		operation.setMsg(msg.toJSONString());
		operation.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsZpglOperationService.save(operation);
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsZpglInterviewMessage selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}
	
	// 面试管理 四个列表
	@Override
	public DataSet<HrmsZpglInterviewMessageResultOutListVo> getDataSetList(Page page, HrmsZpglInterviewMessage record) {
		//如果为空 查询待面试成员
		if(StringUtil.isEmpty(record.getInterviewStatus())) {
			record.setInterviewStatus(ZpglInterviewStatusEnum.ZPGL_INTERVIEW_STATUS_1.getKey());   //待面试 
		}
		page.setSord("desc");
		if("1".equals(record.getInterviewStatus())) {
			page.setSidx("tt.create_date");
		}else {
			page.setSidx("re.create_date");
		}
		//根据当前账号机构编码过滤
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsZpglInterviewMessageResultOutListVo> records = mapper.getPageList(page,record);

		//处理工作年限  性别 学历
		Map<String, String> sexConvertDictMap = convertDictMap("SEX_TYPE");
		Map<String, String> educationConvertDictMap = convertDictMap("education_type");
		if (records != null && records.size() > 0) {
			for (HrmsZpglInterviewMessageResultOutListVo hrmsZpglInterviewMessageResultOutListVo : records) {
				//处理签到
				hrmsZpglInterviewMessageResultOutListVo.setGendertext(sexConvertDictMap.get(hrmsZpglInterviewMessageResultOutListVo.getGender()));
				hrmsZpglInterviewMessageResultOutListVo.setXuelitext(educationConvertDictMap.get(hrmsZpglInterviewMessageResultOutListVo.getXueli()));
				String _workunit = hrmsZpglWorkrecordService.getOneData(hrmsZpglInterviewMessageResultOutListVo.getZpglempid());
				//处理工作单位
				hrmsZpglInterviewMessageResultOutListVo.setWorkUnit(_workunit);
				if ("1".equals(hrmsZpglInterviewMessageResultOutListVo.getZpglEmployeeStatus())) {
					if ("1".equals(hrmsZpglInterviewMessageResultOutListVo.getAddInterview())) {
						hrmsZpglInterviewMessageResultOutListVo.setZpglEmployeeStatusText("待面试");
					} else if ("2".equals(hrmsZpglInterviewMessageResultOutListVo.getAddInterview())) {
						hrmsZpglInterviewMessageResultOutListVo.setZpglEmployeeStatusText("筛选不通过");
					} else if ("3".equals(hrmsZpglInterviewMessageResultOutListVo.getAddInterview())) {
						hrmsZpglInterviewMessageResultOutListVo.setZpglEmployeeStatusText("已终止");
					} else {
						hrmsZpglInterviewMessageResultOutListVo.setZpglEmployeeStatusText("待筛选");
					}
				} else if ("2".equals(hrmsZpglInterviewMessageResultOutListVo.getZpglEmployeeStatus())) {
					//records.get(i)
					if ("1".equals(hrmsZpglInterviewMessageResultOutListVo.getInterviewResultStatus())) {
						hrmsZpglInterviewMessageResultOutListVo.setZpglEmployeeStatusText("面试通过");
					} else if ("2".equals(hrmsZpglInterviewMessageResultOutListVo.getInterviewResultStatus())) {
						hrmsZpglInterviewMessageResultOutListVo.setZpglEmployeeStatusText("面试不通过");
					} else if ("3".equals(hrmsZpglInterviewMessageResultOutListVo.getInterviewResultStatus())) {
						hrmsZpglInterviewMessageResultOutListVo.setZpglEmployeeStatusText("面试待定");
					} else if ("4".equals(hrmsZpglInterviewMessageResultOutListVo.getInterviewResultStatus())) {
						//继续面试
						hrmsZpglInterviewMessageResultOutListVo.setZpglEmployeeStatusText("待面试");
					} else {
						hrmsZpglInterviewMessageResultOutListVo.setZpglEmployeeStatusText("待面试");
					}
				} else {
					hrmsZpglInterviewMessageResultOutListVo.setZpglEmployeeStatusText(ZpglEmployeeStatusEnum.getValByKey(hrmsZpglInterviewMessageResultOutListVo.getZpglEmployeeStatus()));
				}
				//处理工作年限
				if (!StringUtil.isEmpty(hrmsZpglInterviewMessageResultOutListVo.getEntryDate())) {
					try {
						String yearCompare = DateUtils.yearCompare(DateUtils.getStringToDate(hrmsZpglInterviewMessageResultOutListVo.getEntryDate()), new Date());
						hrmsZpglInterviewMessageResultOutListVo.setEntryDateText(yearCompare + "年");
					} catch (Exception e) {
						log.error("工作年限转换异常");
					}
				}
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }

	@Override
	public DataSet<HrmsZpglInterviewMessageResultOutListVo> getHrmsZpglEntryList(Page page, HrmsZpglInterviewMessage record) {
		page.setSidx("re.create_date");
		page.setSord("desc");
		//默认待入职
		if(StringUtil.isEmpty(record.getZpglEmployeeStatus())) {
			record.setZpglEmployeeStatus(ZpglEmployeeStatusEnum.ZPGL_EMPLOYEE_STATUS_3.getKey());  //默认待入职
		}
		//根据当前登录账号机构编码过滤查询数据
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsZpglInterviewMessageResultOutListVo> records = mapper.getEntryPageList(page,record);

		//处理工作年限  性别 学历
		Map<String, String> sexConvertDictMap = convertDictMap("SEX_TYPE");
		Map<String, String> educationConvertDictMap = convertDictMap("education_type");
		if (records != null && records.size() > 0) {
			for (int i = 0; i < records.size(); i++) {
				records.get(i).setNo(String.valueOf(i+1));
				records.get(i).setGendertext(sexConvertDictMap.get(records.get(i).getGender()));
				records.get(i).setXuelitext(educationConvertDictMap.get(records.get(i).getXueli()));
				String _workunit = hrmsZpglWorkrecordService.getOneData(records.get(i).getZpglempid());
				//处理工作单位
				records.get(i).setWorkUnit(_workunit);
				if("1".equals(records.get(i).getZpglEmployeeStatus())){
					if("1".equals(records.get(i).getAddInterview())){
						records.get(i).setZpglEmployeeStatusText("待面试");
					}else if("2".equals(records.get(i).getAddInterview())){
						records.get(i).setZpglEmployeeStatusText("筛选不通过");
					}else if("3".equals(records.get(i).getAddInterview())){
						records.get(i).setZpglEmployeeStatusText("已终止");
					}else{
						records.get(i).setZpglEmployeeStatusText("待筛选");
					}
				}else if("2".equals(records.get(i).getZpglEmployeeStatus())){
					//records.get(i)
					if("1".equals(records.get(i).getInterviewResultStatus())){
						records.get(i).setZpglEmployeeStatusText("面试通过");
					}else if("2".equals(records.get(i).getInterviewResultStatus())){
						records.get(i).setZpglEmployeeStatusText("面试不通过");
					}else if("3".equals(records.get(i).getInterviewResultStatus())){
						records.get(i).setZpglEmployeeStatusText("面试待定");
					}else if("4".equals(records.get(i).getInterviewResultStatus())){
						//继续面试
						records.get(i).setZpglEmployeeStatusText("待面试");
					}else{
						records.get(i).setZpglEmployeeStatusText("待面试");
					}
				}else{
					records.get(i).setZpglEmployeeStatusText(ZpglEmployeeStatusEnum.getValByKey(records.get(i).getZpglEmployeeStatus()));
				}
				//处理工作年限
				if(!StringUtil.isEmpty(records.get(i).getEntryDate())) {
					try {
						String yearCompare = DateUtils.yearCompare(DateUtils.getStringToDate(records.get(i).getEntryDate()),new Date());
						records.get(i).setEntryDateText(yearCompare + "年");
					} catch (Exception e) {
						log.error("工作年限转换异常");
					}
				}
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsZpglInterviewMessageResultOutListVo> export(HrmsZpglInterviewMessage record) {
		Page page = new Page();
		page.setPageSize(Integer.MAX_VALUE);
		record.setZpglEmployeeStatus(ZpglEmployeeStatusEnum.ZPGL_EMPLOYEE_STATUS_4.getKey());
		//查询已入职
		DataSet<HrmsZpglInterviewMessageResultOutListVo> hrmsZpglEntryList = getHrmsZpglEntryList(page,record);
		return hrmsZpglEntryList.getRows();
	}
	
	

	@Override
	public Integer deleteByEmpId(String zpglempid) {
		Assert.hasText(zpglempid, "ID不能为空.");
		HrmsZpglOperation operation = new HrmsZpglOperation();
		operation.setTitle("删除--面试不通过");
		operation.setZpglempid(zpglempid);
		hrmsZpglOperationService.save(operation);
		
		HrmsZpglInterviewMessage record = new HrmsZpglInterviewMessage();
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		Example example = new Example(HrmsZpglInterviewMessage.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("zpglempid", zpglempid);
		return mapper.updateByExampleSelective(record, example);
	}

	@Override
	public HrmsZpglInterviewMessage selectByEmpId(String id) {
		Example example = new Example(HrmsZpglInterviewMessage.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("zpglempid", id);
		example.setOrderByClause(" create_date desc");
		List<HrmsZpglInterviewMessage> selectByExample = mapper.selectByExample(example);
		if(selectByExample != null && selectByExample.size() >0) {
			return selectByExample.get(0);
		}
		return null;
	}
	
	// 二维码签到功能
	@Transactional(readOnly = false)
	@Override
	public Integer signIn(HrmsZpglInterviewMessage record) {

		// 先验证手机号和身份证号码是否存在
		Assert.hasText(record.getIdentityNumber(), "身份证不能为空");
		// 根据省份证查询人员是否存在 是否有面试信息
		HrmsZpglEmployee bean = hrmsZpglEmployeeService.findByIdentityNumber(record.getIdentityNumber());
		if (bean != null) {
			// 查询是否有面试信息
			HrmsZpglInterviewMessage msgBean = selectByEmpId(bean.getId());
			if (null != msgBean) {
				// 修改面试状态
				msgBean.setSignIn("1");
				msgBean.setSignInDate(DateUtils.getPresentTimeStr());
				mapper.updateByPrimaryKeySelective(msgBean);
			} else {
				throw new BusinessException("未设置面试信息");
			}
		} else {
			throw new BusinessException("未查询到登记信息");
		}
		return 1;
	}
	@Transactional(readOnly = false)
	@Override
	public Integer batchSave(HrmsZpglInterviewMessage record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<String> zpglempidList = record.getZpglempidList();
		
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
		}
		
		StringBuilder _sb = new StringBuilder();
		
		if(zpglempidList != null && zpglempidList.size() > 0) {
			
			try {
				for (int i = 0; i < zpglempidList.size(); i++) {
					HrmsZpglEmployee selectById = hrmsZpglEmployeeService.selectById(zpglempidList.get(i));
					
					_sb.append(selectById.getEmployeeName()).append(",");
					
					record.setId(IdUtil.getId());
					record.setCreateDate(new Date());
					record.setUpdateDate(new Date());
					record.setIsDeleted("N");
					//符合要求 设置为 待面试
					if("1".equals(record.getConform())) {
						record.setInterviewStatus(ZpglInterviewStatusEnum.ZPGL_INTERVIEW_STATUS_1.getKey());//设置待面试
					}
					
					HrmsZpglInterviewMessage oldBean = selectByEmpId(zpglempidList.get(i));
					record.setZpglempid(zpglempidList.get(i));
					if(oldBean != null) {
						oldBean.setConform("1");
						record.setId(oldBean.getId());
						update(record);
					}else {
						record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
						mapper.insertSelective(record);
						HrmsZpglOperation operation = new HrmsZpglOperation();
						operation.setTitle("设置面试信息");
						operation.setZpglempid(zpglempidList.get(i));
						JSONObject msg = new JSONObject();
						
						if("1".equals(record.getConform())) {
							msg.put("是否符合", "符合");
						}else {
							msg.put("是否符合", "不符合");
						}
						msg.put("面试科室", record.getInterviewDepttext());
						msg.put("面试岗位", record.getInterviewJobtext());
						msg.put("面试时间", record.getInterviewDate()); 
						operation.setMsg(msg.toJSONString());
						hrmsZpglOperationService.save(operation);
					}
					
					HrmsZpglEmployee emp = new HrmsZpglEmployee();
					emp.setId(zpglempidList.get(i));
					if("1".equals(record.getConform())) {
						emp.setAddInterview("1");  //已设置面试信息
					}else{
						emp.setAddInterview("2");  //已设置面试信息
					}
					emp.setAddTalentPool("2");  //从人才库移除
					hrmsZpglEmployeeService.updateBasic(emp);
				}
			} catch (Exception e) {
				log.error("批量设置面试信息异常"+e.getMessage(),e);
				throw new BusinessException("批量设置面试信息异常");
			}
		}else {
			throw new BusinessException("请选择要设置面试信息的人员");
		}
		
		try {
			if("1".equals(record.getConform())) {
				//发送通知
				String bishiMsg = _sb.deleteCharAt(_sb.length()-1).toString()+" 于 " +record.getWrittenDate() + " 日进行笔试，请注意合理安排时间！" ;
				sendMessage(bishiMsg,record.getWrittenEmpNo());  //笔试通知
				//实操通知 
				String shicaoMsg = _sb.deleteCharAt(_sb.length()-1).toString()+" 于 " +record.getOperationDate() + " 日进行实操，请注意合理安排时间！" ;
				sendMessage(shicaoMsg,record.getOperationEmpNo());  //笔试通知
				//面试通知
				String mianshiMsg = _sb.deleteCharAt(_sb.length()-1).toString()+" 于 " +record.getInterviewDate() + " 日进行面试，请注意合理安排时间！" ;
				sendMessage(mianshiMsg,record.getInterviewEmpNo());  //笔试通知
			}
		} catch (Exception e) {
			log.error("面试信息通知发送失败"+e.getMessage());
		}
		
		return 1;
	}

	@Override
	public Integer deleteAllByEmpId(String zpglempid) {
		HrmsZpglInterviewMessage record = new HrmsZpglInterviewMessage();
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		Example example = new Example(HrmsZpglInterviewMessage.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("zpglempid", zpglempid);
		return mapper.updateByExampleSelective(record, example);
	}
}

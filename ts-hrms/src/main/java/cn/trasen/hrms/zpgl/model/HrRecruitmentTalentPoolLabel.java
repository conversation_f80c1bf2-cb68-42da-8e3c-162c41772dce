package cn.trasen.hrms.zpgl.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hr_recruitment_talent_pool_label")
@Setter
@Getter
public class HrRecruitmentTalentPoolLabel {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 招聘管理-人才库标签
     */
    @Id
    @Column(name = "hr_recruitment_talent_pool_label_id")
    @ApiModelProperty(value = "招聘管理-人才库标签")
    private String hrRecruitmentTalentPoolLabelId;

    /**
     * 标签名称
     */
    @Column(name = "label_name")
    @ApiModelProperty(value = "标签名称")
    private String labelName;

    /**
     * 状态（N停用Y启用）
     */
    @ApiModelProperty(value = "状态（N停用Y启用）")
    private String status;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateUserName;

    /**
     * 删除标识(N存在，Y删除)
     */
    @Column(name = "label_job")
    @ApiModelProperty(value = "标签对应岗位")
    private String labelJob;
    
    @Column(name = "label_jobtext")
    @ApiModelProperty(value = "标签对应岗位")
    private String labelJobtext;
    
    /**
     * 删除标识(N存在，Y删除)
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识(N存在，Y删除)")
    private String isDeleted;
}
package cn.trasen.hrms.zpgl.dao;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.zpgl.model.HrmsZpglInterviewMessage;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglInterviewMessageResultOutListVo;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsZpglInterviewMessageMapper extends Mapper<HrmsZpglInterviewMessage> {

	/** 
	* @Title: getPageList 
	* @Description: 待面试
	* @param @param page
	* @param @param record
	* @param @return    设定文件 
	* @return List<HrmsZpglInterviewMessageOutListVo>    返回类型 
	* @throws 
	*/
	List<HrmsZpglInterviewMessageResultOutListVo> getPageList(Page page, HrmsZpglInterviewMessage record);

	/** 
	* @Title: getEntryPageList 
	* @Description: 入职管理列表
	* @param @param page
	* @param @param record
	* @param @return    设定文件 
	* @return List<HrmsZpglInterviewMessageResultOutListVo>    返回类型 
	* @throws 
	*/
	List<HrmsZpglInterviewMessageResultOutListVo> getEntryPageList(Page page, HrmsZpglInterviewMessage record);

}
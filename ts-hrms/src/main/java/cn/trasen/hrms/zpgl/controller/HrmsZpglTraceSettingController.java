package cn.trasen.hrms.zpgl.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrmsZpglTraceSetting;
import cn.trasen.hrms.zpgl.service.HrmsZpglTraceSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsZpglTraceSettingController
 * @Description 跟踪管理设置
 * @date 2023��2��9�� ����3:28:45
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglTraceSettingController")
public class HrmsZpglTraceSettingController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglTraceSettingController.class);

	@Autowired
	private HrmsZpglTraceSettingService hrmsZpglTraceSettingService;

	/**
	 * @Title saveHrmsZpglTraceSetting
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��9�� ����3:28:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/zpglTraceSetting/save")
	public PlatformResult<String> saveHrmsZpglTraceSetting(@RequestBody List<HrmsZpglTraceSetting> record) {
		try {
			hrmsZpglTraceSettingService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsZpglTraceSetting
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��9�� ����3:28:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/zpglTraceSetting/update")
	public PlatformResult<String> updateHrmsZpglTraceSetting(@RequestBody HrmsZpglTraceSetting record) {
		try {
			hrmsZpglTraceSettingService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsZpglTraceSettingById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsZpglTraceSetting>
	 * @date 2023��2��9�� ����3:28:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/zpglTraceSetting/{id}")
	public PlatformResult<HrmsZpglTraceSetting> selectHrmsZpglTraceSettingById(@PathVariable String id) {
		try {
			HrmsZpglTraceSetting record = hrmsZpglTraceSettingService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsZpglTraceSettingById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��2��9�� ����3:28:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/zpglTraceSetting/delete/{id}")
	public PlatformResult<String> deleteHrmsZpglTraceSettingById(@PathVariable String id) {
		try {
			hrmsZpglTraceSettingService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsZpglTraceSettingList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglTraceSetting>
	 * @date 2023��2��9�� ����3:28:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglTraceSetting/list")
	public DataSet<HrmsZpglTraceSetting> selectHrmsZpglTraceSettingList(Page page, HrmsZpglTraceSetting record) {
		return hrmsZpglTraceSettingService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglTraceSetting/listAll")
	public List<HrmsZpglTraceSetting> getListAll() {
		return hrmsZpglTraceSettingService.getListAll();
	}
}

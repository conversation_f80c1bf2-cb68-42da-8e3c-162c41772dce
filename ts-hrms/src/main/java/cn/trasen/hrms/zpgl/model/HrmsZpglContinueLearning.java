package cn.trasen.hrms.zpgl.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "hrms_zpgl_continue_learning")
@Setter
@Getter
public class HrmsZpglContinueLearning {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
	@Id
    @ApiModelProperty(value = "主键ID")
    private String id;
	
   
	/**
     * 面试者id
     */
    @ApiModelProperty(value = "面试者id")
    private String zpglempid;

    /**
     * 起止时间
     */
    @ApiModelProperty(value = "起止时间")
    private String qizhishijian;

    /**
     * 培训项目
     */
    @ApiModelProperty(value = "培训项目")
    private String peixunxiangmu;

    /**
     * 培训机构
     */
    @ApiModelProperty(value = "培训机构")
    private String peixunjigou;

    /**
     * 授课老师
     */
    @ApiModelProperty(value = "授课老师")
    private String laoshi;

    /**
     * 取得执业资格
     */
    @ApiModelProperty(value = "取得执业资格")
    private String zhiyezige;

    /**
     * 认证附件
     */
    @ApiModelProperty(value = "认证附件")
    private String fujian;
    

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;
}
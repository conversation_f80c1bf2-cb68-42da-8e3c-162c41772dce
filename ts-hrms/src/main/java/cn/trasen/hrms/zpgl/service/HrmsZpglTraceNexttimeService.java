package cn.trasen.hrms.zpgl.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglTraceNexttime;

/**
 * @ClassName HrmsZpglTraceNexttimeService
 * @Description TODO
 * @date 2023��6��26�� ����5:00:19
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglTraceNexttimeService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��6��26�� ����5:00:19
	 * <AUTHOR>
	 */
	Integer save(HrmsZpglTraceNexttime record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��6��26�� ����5:00:19
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglTraceNexttime record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��6��26�� ����5:00:19
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglTraceNexttime
	 * @date 2023��6��26�� ����5:00:19
	 * <AUTHOR>
	 */
	HrmsZpglTraceNexttime selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglTraceNexttime>
	 * @date 2023��6��26�� ����5:00:19
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglTraceNexttime> getDataSetList(Page page, HrmsZpglTraceNexttime record);
}

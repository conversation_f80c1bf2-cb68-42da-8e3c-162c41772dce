package cn.trasen.hrms.zpgl.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.*;

@Table(name = "hr_talent_library")
@Setter
@Getter
@ToString
public class HrTalentLibrary {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Id
    @Column(name = "talent_library_id")
    private String talentLibraryId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sex;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 意向岗位
     */
    @Column(name = "intentional_post")
    @ApiModelProperty(value = "意向岗位")
    private String intentionalPost;

    /**
     * 工作年限
     */
    @Column(name = "experience_year")
    @ApiModelProperty(value = "工作年限")
    private String experienceYear;



    /**
     * 学历
     */
    @ApiModelProperty(value = "学历 1 本科 2 硕士 3 博士 4 专科 5 职高 6 其它")
    private Integer education;

    /**
     * 岗位类别
     */
    @Column(name = "post_type")
    @ApiModelProperty(value = "岗位类别 对应字典ID")
    private String postType;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 来源渠道
     */
    @Column(name = "source_type")
    @ApiModelProperty(value = "来源渠道 1日常招聘 2人才地图 3离职员工")
    private Integer sourceType;



    /**
     * 是否进入面试
     */
    @Column(name = "enter_interview")
    @ApiModelProperty(value = "是否进入面试 Y 是 N 否")
    private String enterInterview;

    /**
     * 出生年月
     */
    @Column(name = "birth_date")
    @ApiModelProperty(value = "出生年月")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date birthDate;

    /**
     * 健康状况
     */
    @Column(name = "health_condition")
    @ApiModelProperty(value = "健康状况 1 较差 2 良好 3 优秀 4 其它")
    private String healthCondition;

    /**
     * 毕业院校
     */
    @Column(name = "graduated_school")
    @ApiModelProperty(value = "毕业院校")
    private String graduatedSchool;

    /**
     * 专业
     */
    @ApiModelProperty(value = "专业")
    private String professional;

    /**
     * 现住地址
     */
    @ApiModelProperty(value = "现住地址")
    private String address;

    /**
     * 现住地址电话
     */
    @Column(name = "address_phone")
    @ApiModelProperty(value = "现住地址电话")
    private String addressPhone;

    /**
     * 身份证号码
     */
    @Column(name = "id_number")
    @ApiModelProperty(value = "身份证号码")
    private String idNumber;

    /**
     * 籍贯
     */
    @Column(name = "place_origin")
    @ApiModelProperty(value = "籍贯")
    private String placeOrigin;

    /**
     * 爱好
     */
    @ApiModelProperty(value = "爱好")
    private String hobby;

    /**
     * 最快到岗时间
     */
    @Column(name = "fast_work_day")
    @ApiModelProperty(value = "最快到岗时间")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date fastWorkDay;

    /**
     * 婚姻状况(1: 已婚 2 未婚 3 保密)
     */
    @Column(name = "marital_status")
    @ApiModelProperty(value = "婚姻状况(1: 已婚 2 未婚 3 保密 4 离异)")
    private Integer maritalStatus;

    /**
     * 期望薪资
     */
    @Column(name = "expect_salary")
    @ApiModelProperty(value = "期望薪资")
    private String expectSalary;

    /**
     * 现薪资
     */
    @Column(name = "now_salary")
    @ApiModelProperty(value = "现薪资")
    private String nowSalary;

    /**
     * 现工作状态
     */
    @Column(name = "now_work_status")
    @ApiModelProperty(value = "现工作状态 1 在职 2 已离职 3 其它")
    private Integer nowWorkStatus;

    /**
     * 档案存放地点
     */
    @Column(name = "archives_location")
    @ApiModelProperty(value = "档案存放地点")
    private String archivesLocation;

    @ApiModelProperty(value = "人才库管理主键")
    private String hrRecruitmentTalentPoolId;

    @ApiModelProperty(value = "人才库名称")
    private String talentPoolName;

    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(value = "入库时间")
    private Date talentPoolCreateTime;


    /**
     * 照片地址
     */
    @Column(name = "pic_url")
    @ApiModelProperty(value = "现在用于简历附件的业务ID保存")
    private String picUrl;

    /**
     * 简历附件
     */
    @Column(name = "resume_attachment")
    @ApiModelProperty(value = "简历附件")
    private String resumeAttachment;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "create_date")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_date")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    /**
     * 是否曾经在创星工作过
     */
    @Column(name = "have_worked_before")
    @ApiModelProperty(value = "是否曾经在创星工作过 Y 是 N 否")
    private String haveWorkedBefore;

    /**
     * 是否有朋友或亲友在我单位或重要业务客户处工作
     */
    @Column(name = "have_friend_work")
    @ApiModelProperty(value = "是否有朋友或亲友在我单位或重要业务客户处工作")
    private String haveFriendWork;

    /**
     * 是否有个人身体严重缺陷或严重疾病/传染病
     */
    @Column(name = "have_sick")
    @ApiModelProperty(value = "是否有个人身体严重缺陷或严重疾病/传染病")
    private String haveSick;

    /**
     * 是否接收过工伤伤残赔偿
     */
    @Column(name = "have_disability")
    @ApiModelProperty(value = "是否接收过工伤伤残赔偿")
    private String haveDisability;

    /**
     * 是否曾因缺勤或违纪而接受过纪律处分或被解雇
     */
    @ApiModelProperty(value = "是否曾因缺勤或违纪而接受过纪律处分或被解雇")
    @Column(name = "have_fired")
    private String haveFired;

    /**
     * 是否有犯罪记录
     */
    @Column(name = "have_crime")
    @ApiModelProperty(value = "是否有犯罪记录")
    private String haveCrime;

    @ApiModelProperty(value = "是否离职")
    private String dimission;

    @ApiModelProperty(value = "是否接受加班")
    private String workOvertime;

    @ApiModelProperty(value = "是否接受出差")
    private String businessTrip;
    /**
     * 岗位类别名称
     */
    @Column(name = "post_type_name")
    @ApiModelProperty(value = "岗位类别名称 管理类 产品类等 对应字典")
    private String postTypeName;

    @Column(name = "create_user_name")
    private String createUserName;


    @Column(name = "update_user_name")
    private String updateUserName;

    @Transient
    @ApiModelProperty(value = "业务ID")
    private String businessId;

    @Transient
    @ApiModelProperty(value = " 联系人列表 用于列表展示和编辑")
    private List<HrContactInformation>hrContactInformationList;

    @Transient
    @ApiModelProperty(value = "工作经历列表")
    private List<HrTalentWorkExperience>hrTalentWorkExperienceList;

    @Transient
    @ApiModelProperty(value = "受教育情况列表")
    private List<HrTalentEducation> hrTalentEducationList;


    @Transient
    @ApiModelProperty(value = "前端筛选条件 工作单位")
    private String workPlace;

    @Transient
    @ApiModelProperty(value = "工作年限(新)")
    private String experienceYearNew;

    @Transient
    @ApiModelProperty(value = "无")
    private List<String>idList; //根据工作单位筛选出的满足条件talentLibraryId

    @Transient
    @ApiModelProperty(value = "工作时间起始区间")
    private Integer workYearStart; // 筛选条件

    @Transient
    @ApiModelProperty(value = "工作时间起始区间")
    private Integer workYearEnd; // 筛选条件

    @Transient
    @ApiModelProperty(value = "前端筛选条件 工作年限输入")
    private String workYearStr;

    @Transient
    @ApiModelProperty(value = "来源渠道名称 页面展示")
    private String sourceTypeName;

    @Transient
    @ApiModelProperty(value = "树查询id")
    private String treeTalentPoolId;

    @Transient
    @ApiModelProperty(value = "树查询id")
    private List<String> treeTalentPoolIds;

    @Transient
    @ApiModelProperty(value = "模糊查询")
    private String fuzzy;

    @Transient
    @ApiModelProperty(value = "模糊查询")
    private List<String> fuzzys;

    @Transient
    @ApiModelProperty(value = "学历名称 页面展示")
    private String educationName;
    @Transient
    @ApiModelProperty(value = "现工作状态名称 页面展示")
    private String nowWorkStatusName;

    @Transient
    @ApiModelProperty(value = "现婚姻状态名称 页面展示")
    private String maritalStatusName;

    @Transient
    @ApiModelProperty(value = "标签")
    private String label;

    @Column(name = "hrbp_code")
    private String hrbpCode;

    @Column(name = "hrbp_name")
    private String hrbpName;

    @Column(name = "content")
    private String content;

    @Column(name = "have_medical_experience")
    @ApiModelProperty(value = "是否有医疗信息化行业经验")
    private String haveMedicalExperience;

    @Column(name = "education_nature")
    @ApiModelProperty(value = "是否全日制学历")
    private String educationNature;

    @Column(name = "application_source")
    @ApiModelProperty(value = "应聘来源")
    private String applicationSource;

    @Transient
    @ApiModelProperty(value = "保存至人才库时 保存附件")
    private String resumeId;


    @Transient
    @ApiModelProperty(value = "年龄")
    private String age;

    @Transient
    @ApiModelProperty(value = "个人工作台跳转参数")
    private String talentLibraryIds;

    @Transient
    @ApiModelProperty(value = "个人工作台跳转参数")
    private List<String> talentLibraryIdList;


    /**
     * 开始工作时间
     */
    @ApiModelProperty(value = "开始工作时间")
    @Column(name = "start_work_date")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date startWorkDate;

    @Transient
    @ApiModelProperty(value = "工作时间起始区间")
    private BigDecimal startWorkYear; // 筛选条件

    @Transient
    @ApiModelProperty(value = "工作时间起始区间")
    private BigDecimal endWorkYear; // 筛选条件
}
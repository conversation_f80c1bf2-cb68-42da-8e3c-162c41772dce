package cn.trasen.hrms.zpgl.controller;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPoolLabel;
import cn.trasen.hrms.zpgl.service.HrRecruitmentTalentPoolLabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;


/** 
* @ClassName: HrRecruitmentTalentPoolLabelController 
* @Description: 人才库标签
* <AUTHOR>  
* @date 2023年2月6日 下午3:11:51 
*  
*/
@RestController
@Api(tags = "招聘-人才库标签")
public class HrRecruitmentTalentPoolLabelController {

    private transient static final Logger logger = LoggerFactory.getLogger(HrRecruitmentTalentPoolLabelController.class);

    @Autowired
    private HrRecruitmentTalentPoolLabelService hrRecruitmentTalentPoolLabelService;

    /**
     * 保存、编辑
     *
     * @param recruitmentTalentPoolLabelInputVo
     * @return
     */
    @ApiOperation(value = "保存、编辑", notes = "保存、编辑")
    @PostMapping("/api/HrRecruitmentTalentPoolLabel/save")
    public PlatformResult<Integer> save(@RequestBody HrRecruitmentTalentPoolLabel bean) {
    	
    	   try {
    		   hrRecruitmentTalentPoolLabelService.save(bean);
               return PlatformResult.success();
           } catch (Exception e) {
               logger.error(e.getMessage(), e);
               return PlatformResult.failure(e.getMessage());
           }

    }
    
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/HrRecruitmentTalentPoolLabel/update")
    public PlatformResult<Integer> update(@RequestBody HrRecruitmentTalentPoolLabel bean) {
    	
    	   try {
    		   hrRecruitmentTalentPoolLabelService.update(bean);
               return PlatformResult.success();
           } catch (Exception e) {
               logger.error(e.getMessage(), e);
               return PlatformResult.failure(e.getMessage());
           }
    }

    @ApiOperation(value = "启用禁用", notes = "启用禁用")
    @PutMapping("/api/HrRecruitmentTalentPoolLabel/{id}/{status}")
    public PlatformResult<Integer> getOne(@PathVariable @ApiParam("主键") String id,
                                          @PathVariable @ApiParam("状态（N停用Y启用）") String status) {
        return PlatformResult.success(hrRecruitmentTalentPoolLabelService.enableDisable(id, status));
    }


    /**
     * @param id
     * @return PlatformResult<HrRecruitmentTalentPoolLabel>
     * @Title selectHrRecruitmentTalentPoolLabelById
     * @Description 根据ID查询
     * @date 2022��8��11�� ����5:30:22
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/HrRecruitmentTalentPoolLabel/{id}")
    public PlatformResult<HrRecruitmentTalentPoolLabel> selectHrRecruitmentTalentPoolLabelById(@PathVariable String id) {
        try {
            HrRecruitmentTalentPoolLabel record = hrRecruitmentTalentPoolLabelService.selectById(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<HrRecruitmentTalentPoolLabel>
     * @Title selectHrRecruitmentTalentPoolLabelList
     * @Description 查询列表
     * @date 2022��8��11�� ����5:30:22
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/HrRecruitmentTalentPoolLabel/list")
    public DataSet<HrRecruitmentTalentPoolLabel> selectHrRecruitmentTalentPoolLabelList(Page page, HrRecruitmentTalentPoolLabel record) {
        return hrRecruitmentTalentPoolLabelService.getDataSetList(page, record);
    }

    @ApiOperation(value = "获取人才库标签", notes = "获取人才库标签")
    @GetMapping("/api/HrRecruitmentTalentPoolLabel/getTalentLibraryLableById/{id}")
    public PlatformResult<List<HrRecruitmentTalentPoolLabel>> getTalentLibraryLableById(@ApiParam(name = "人才库主键")
                                                                                        @PathVariable String id) {
        try {
            List<HrRecruitmentTalentPoolLabel> talentLibraryLableById = hrRecruitmentTalentPoolLabelService.getTalentLibraryLableById(id);
            return PlatformResult.success(talentLibraryLableById);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
}

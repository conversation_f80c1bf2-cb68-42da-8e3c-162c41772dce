package cn.trasen.hrms.zpgl.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrmsZpglProfessional;
import cn.trasen.hrms.zpgl.service.HrmsZpglProfessionalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsZpglProfessionalController
 * @Description TODO
 * @date 2023��2��8�� ����4:13:48
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglProfessionalController")
public class HrmsZpglProfessionalController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglProfessionalController.class);

	@Autowired
	private HrmsZpglProfessionalService hrmsZpglProfessionalService;

	/**
	 * @Title saveHrmsZpglProfessional
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:13:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/zpglProfessional/save")
	public PlatformResult<String> saveHrmsZpglProfessional(@RequestBody HrmsZpglProfessional record) {
		try {
			hrmsZpglProfessionalService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsZpglProfessional
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:13:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/zpglProfessional/update")
	public PlatformResult<String> updateHrmsZpglProfessional(@RequestBody HrmsZpglProfessional record) {
		try {
			hrmsZpglProfessionalService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsZpglProfessionalById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsZpglProfessional>
	 * @date 2023��2��8�� ����4:13:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/zpglProfessional/{id}")
	public PlatformResult<HrmsZpglProfessional> selectHrmsZpglProfessionalById(@PathVariable String id) {
		try {
			HrmsZpglProfessional record = hrmsZpglProfessionalService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsZpglProfessionalById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:13:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/zpglProfessional/delete/{id}")
	public PlatformResult<String> deleteHrmsZpglProfessionalById(@PathVariable String id) {
		try {
			hrmsZpglProfessionalService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsZpglProfessionalList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglProfessional>
	 * @date 2023��2��8�� ����4:13:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglProfessional/list")
	public DataSet<HrmsZpglProfessional> selectHrmsZpglProfessionalList(Page page, HrmsZpglProfessional record) {
		return hrmsZpglProfessionalService.getDataSetList(page, record);
	}
}

package cn.trasen.hrms.zpgl.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrTalentWorkExperienceMapper;
import cn.trasen.hrms.zpgl.model.HrTalentWorkExperience;
import cn.trasen.hrms.zpgl.service.HrTalentWorkExperienceService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrTalentWorkExperienceServiceImpl
 * @Description TODO
 * @date 2021年6月19日 下午4:43:24
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrTalentWorkExperienceServiceImpl implements HrTalentWorkExperienceService {

	@Autowired
	private HrTalentWorkExperienceMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrTalentWorkExperience record) {
		record.setWorkExperienceId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrTalentWorkExperience record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrTalentWorkExperience record = new HrTalentWorkExperience();
		record.setWorkExperienceId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrTalentWorkExperience selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrTalentWorkExperience> getDataSetList(Page page, HrTalentWorkExperience record) {
		Example example = new Example(HrTalentWorkExperience.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrTalentWorkExperience> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	/**
	 * @Title findByTalentId
	 * @Description 根据id查询所有联系人信息（人才库或简历）
	 * @param id
	 * @return List<HrTalentWorkExperience>
	 * @date 2021/6/19 17:01
	 * <AUTHOR>
	 */
	@Override
	public List<HrTalentWorkExperience> findByTalentId(String id) {
		Example example = new Example(HrTalentWorkExperience.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("talentLibraryId",id);
		List<HrTalentWorkExperience> informationList = mapper.selectByExample(example);
		if(CollectionUtils.isEmpty(informationList)){
			informationList= Collections.emptyList();
		}
		return informationList;
	}
}

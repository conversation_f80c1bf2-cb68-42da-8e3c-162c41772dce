package cn.trasen.hrms.zpgl.service.impl;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrmsZpglEmployeeMapper;
import cn.trasen.hrms.zpgl.dao.HrmsZpglInterviewMessageMapper;
import cn.trasen.hrms.zpgl.dao.HrmsZpglInterviewResultMapper;
import cn.trasen.hrms.zpgl.enums.ZpglEmployeeStatusEnum;
import cn.trasen.hrms.zpgl.model.*;
import cn.trasen.hrms.zpgl.service.*;
import cn.trasen.hrms.zpgl.vo.outVo.EmpDetailsOutVo;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglEmployeeOutListVo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

/**
 * @ClassName HrmsZpglEmployeeServiceImpl
 * @Description TODO
 * @date 2023��2��8�� ����3:18:46
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Slf4j
public class HrmsZpglEmployeeServiceImpl implements HrmsZpglEmployeeService {
	
    @Autowired
    DictItemFeignService dictItemFeignService;

	@Autowired
	private HrmsZpglEmployeeMapper mapper;

	@Autowired
	private HrmsZpglInterviewMessageMapper ziMapper;
	@Autowired
	HrmsZpglInterviewResultMapper zrMappr;
	
	@Autowired
	private HrmsZpglAcademyService hrmsZpglAcademyService;
	@Autowired
	private HrmsZpglContinueLearningService hrmsZpglContinueLearningService;
	@Autowired
	private HrmsZpglEducationService hrmsZpglEducationService;
	@Autowired
	private HrmsZpglFamilyService hrmsZpglFamilyService;
	@Autowired
	private HrmsZpglProfessionalService hrmsZpglProfessionalService;
	@Autowired
	private HrmsZpglWorkrecordService hrmsZpglWorkrecordService;
	
	@Autowired
	HrmsZpglOperationService hrmsZpglOperationService;
	@Autowired
	HrRecruitmentTalentPoolLabelService hrRecruitmentTalentPoolLabelService;
	@Autowired
	HrRecruitmentTalentPoolService hrRecruitmentTalentPoolService;
	@Autowired
	HrmsZpglInterviewMessageService hrmsZpglInterviewMessageService;
	@Autowired
	HrmsZpglInterviewResultService hrmsZpglInterviewResultService;


	/* (non-Javadoc)
	 * 人员新增
	 */
	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsZpglEmployee record) {
		
		//根据身份证号码查询人员
		String identityNumber = record.getIdentityNumber();
		if(!StringUtil.isEmpty(identityNumber)) {
			Example example = new Example(HrmsZpglEmployee.class);
			Example.Criteria criteria = example.createCriteria();
//			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("identityNumber", identityNumber);
			criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
			List<HrmsZpglEmployee> selectByExample = mapper.selectByExample(example);
			if (selectByExample != null && selectByExample.size() > 0) {
				String id = selectByExample.get(0).getId();
				deleteById(id,"1");
				log.error("删除原来的数据 重新保存"+selectByExample.get(0).getIdentityNumber());
//				throw new BusinessException("您不能投递简历");
			}
			
		}
		
		String employeeId = record.getId();
		if(StringUtil.isEmpty(employeeId)) {
			employeeId = IdUtil.getId();  //主键id
		}
		record.setId(employeeId);
		record.setCreateDate(new Date());
		record.setCreateDate2(new Date());
		record.setEstablishDate(new Date()); //实际创建时间
		record.setIsDeleted("N");
		//白名单接口无用户信息
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
		}else {  //外网本人创建
			record.setCreateUser(employeeId);
			record.setCreateUserName(record.getEmployeeName());
		}
		
		try {
			record.setZpglEmployeeStatus(ZpglEmployeeStatusEnum.ZPGL_EMPLOYEE_STATUS_1.getKey());
			//手机端来的数据 手动处理应聘科室 和科室名称

			if(!StringUtil.isEmpty(record.getApplicantPost())) {
				HrRecruitmentTalentPool talentPool = hrRecruitmentTalentPoolService.selectById(record.getApplicantPost());
				if(talentPool != null) {
					record.setApplicantDept(talentPool.getParentId());
					record.setApplicantDeptname(talentPool.getParentName());
				}
			}
			mapper.insertSelective(record);
		} catch (Exception e) {
			log.error("主数据保存异常 "+e.getMessage());
			throw new BusinessException("新增简历异常"+e.getMessage());
		}
		try {
			 saveAuxiliary(employeeId,record);
		} catch (Exception e) {
			log.error("主数据保存异常 "+e.getMessage());
			throw new BusinessException("新增简历异常");
		}

		//操作记录
		HrmsZpglOperation operation = new HrmsZpglOperation();
		operation.setTitle("创建简历");
		operation.setZpglempid(employeeId);
		operation.setSsoOrgCode(record.getSsoOrgCode());
		hrmsZpglOperationService.save(operation);
		return 1;
	}
	
	//保存附属信息
	private void saveAuxiliary(String employeeId,HrmsZpglEmployee record) {
		List<HrmsZpglEducation> hrmsEducationInfo = record.getHrmsEducationInfo();
		if (null != hrmsEducationInfo && hrmsEducationInfo.size() > 0) {
			hrmsEducationInfo.forEach(item -> {
				item.setZpglempid(employeeId);
				hrmsZpglEducationService.save(item);
			});
		}
		
		List<HrmsZpglWorkrecord> hrmsZpglWorkrecord = record.getHrmsZpglWorkrecord();
		if (null != hrmsZpglWorkrecord && hrmsZpglWorkrecord.size() > 0) {
			hrmsZpglWorkrecord.forEach(item -> {
				item.setZpglempid(employeeId);
				if(item.getLizhiyuanyingList() != null && item.getLizhiyuanyingList().size() > 0) {
					String result = StringUtils.join(item.getLizhiyuanyingList().toArray(), ",");
					item.setLizhiyuanying(result);
				}
				
				
				hrmsZpglWorkrecordService.save(item);
			});
		}
		
		List<HrmsZpglAcademy> hrmsZpglAcademy = record.getHrmsZpglAcademy();
		if (null != hrmsZpglAcademy && hrmsZpglAcademy.size() > 0) {
			hrmsZpglAcademy.forEach(item -> {
				item.setZpglempid(employeeId);
				
				hrmsZpglAcademyService.save(item);
			});
		}
		
		List<HrmsZpglContinueLearning> hrmsZpglContinueLearning = record.getHrmsZpglContinueLearning();
		if (null != hrmsZpglContinueLearning && hrmsZpglContinueLearning.size() > 0) {
			hrmsZpglContinueLearning.forEach(item -> {
				item.setZpglempid(employeeId);
				hrmsZpglContinueLearningService.save(item);
			});
		}
		
		List<HrmsZpglProfessional> hrmsZpglProfessional = record.getHrmsZpglProfessional();
		if (null != hrmsZpglProfessional && hrmsZpglProfessional.size() > 0) {
			hrmsZpglProfessional.forEach(item -> {
				item.setZpglempid(employeeId);
				hrmsZpglProfessionalService.save(item);
			});
		}
		
		List<HrmsZpglFamily> hrmsZpglFamily = record.getHrmsZpglFamily();
		if (null != hrmsZpglFamily && hrmsZpglFamily.size() > 0) {
			hrmsZpglFamily.forEach(item -> {
				item.setZpglempid(employeeId);
				hrmsZpglFamilyService.save(item);
			});
		}
	}
	
	

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsZpglEmployee record) {
		String employeeId = record.getId();
		
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}else {  //外网本人创建
			record.setCreateUser(employeeId);
			record.setCreateUserName(record.getEmployeeName());
		}
		try {
			//先删除所有子表 然后重新保存
			hrmsZpglEducationService.deleteByEmpId(employeeId);
			hrmsZpglWorkrecordService.deleteByEmpId(employeeId);
			hrmsZpglAcademyService.deleteByEmpId(employeeId);
			hrmsZpglContinueLearningService.deleteByEmpId(employeeId);
			hrmsZpglProfessionalService.deleteByEmpId(employeeId);
			hrmsZpglFamilyService.deleteByEmpId(employeeId);
			
			saveAuxiliary(employeeId,record);
			mapper.updateByPrimaryKeySelective(record);
		} catch (Exception e) {
			log.error("主数据保存异常 "+e.getMessage());
			throw new BusinessException("面试信息修改异常");
		}
		
		//操作记录
		HrmsZpglOperation operation = new HrmsZpglOperation();
		operation.setTitle("编辑简历");
		operation.setZpglempid(employeeId);
		hrmsZpglOperationService.save(operation);
		return 1;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id,String type) {

		//判断是不是已入职
		HrmsZpglEmployee hrmsZpglEmployee = mapper.selectByPrimaryKey(id);

		if("4".equals(hrmsZpglEmployee.getZpglEmployeeStatus())){
			throw new BusinessException("已入职的人员不能删除操作");
		}

		if(!"1".equals(type)){
			HrmsZpglEmployee updateData = new HrmsZpglEmployee();
			updateData.setId(id);
			updateData.setZpglEmployeeStatus(ZpglEmployeeStatusEnum.ZPGL_EMPLOYEE_STATUS_1.getKey());
			updateData.setAddInterview("3");
			updateBasic(updateData);
			//删除 筛选信息和面试信息
			hrmsZpglInterviewMessageService.deleteAllByEmpId(id);
			hrmsZpglInterviewResultService.deleteAllByEmpId(id);
			return 1;
		}else{
			Assert.hasText(id, "ID不能为空.");
			HrmsZpglEmployee record = new HrmsZpglEmployee();
			record.setId(id);
			record.setUpdateDate(new Date());
			record.setIsDeleted("Y");
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			HrmsZpglOperation operation = new HrmsZpglOperation();
			operation.setTitle("删除简历");
			operation.setZpglempid(id);
			hrmsZpglOperationService.save(operation);
			return mapper.updateByPrimaryKeySelective(record);
		}

	}

	@Override
	public HrmsZpglEmployee selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsZpglEmployee zpglEmployee = mapper.selectByPrimaryKey(id);
		//查询附属信息
		String employeeId = id;
		List<HrmsZpglEducation> zpglEducation = hrmsZpglEducationService.findByEmpId(employeeId);
		zpglEmployee.setHrmsEducationInfo(zpglEducation);
		List<HrmsZpglWorkrecord> zpglWorkrecord =  hrmsZpglWorkrecordService.findByEmpId(employeeId);
		if (zpglWorkrecord != null && zpglWorkrecord.size() > 0) {
			zpglWorkrecord.forEach(item->{
				if(!StringUtil.isEmpty(item.getLizhiyuanying())) {
					 List<String> result = Arrays.asList(item.getLizhiyuanying().split(","));
					item.setLizhiyuanyingList(result);
				}
			});
		}
		
		zpglEmployee.setHrmsZpglWorkrecord(zpglWorkrecord);
		List<HrmsZpglAcademy> zpglAcademy = hrmsZpglAcademyService.findByEmpId(employeeId);
		zpglEmployee.setHrmsZpglAcademy(zpglAcademy);
		List<HrmsZpglContinueLearning> zpglContinueLearning = hrmsZpglContinueLearningService.findByEmpId(employeeId);
		zpglEmployee.setHrmsZpglContinueLearning(zpglContinueLearning);
		List<HrmsZpglProfessional> zpglProfessional = hrmsZpglProfessionalService.findByEmpId(employeeId);
		zpglEmployee.setHrmsZpglProfessional(zpglProfessional);
		List<HrmsZpglFamily> zpglFamily = hrmsZpglFamilyService.findByEmpId(employeeId);
		zpglEmployee.setHrmsZpglFamily(zpglFamily);
		return zpglEmployee;
	}

	@Override
	public DataSet<HrmsZpglEmployeeOutListVo> getDataSetList(Page page, HrmsZpglEmployee record) {
		page.setSidx(" t1.create_date2");
		page.setSord(" desc");
		//根据当前登录账号机构编码过滤查询数据
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsZpglEmployeeOutListVo> records = mapper.getPageList(page,record);
		
		Map<String, String> sexConvertDictMap = convertDictMap("SEX_TYPE");  //性别
		Map<String, String> educationConvertDictMap = convertDictMap("education_type");  //学历
		
		if (records != null && records.size() > 0) {
			for (int i = 0; i < records.size(); i++) {
				records.get(i).setGendertext(sexConvertDictMap.get(records.get(i).getGender()));
				records.get(i).setXuelitext(educationConvertDictMap.get(records.get(i).getXueli()));

				String _workunit = hrmsZpglWorkrecordService.getOneData(records.get(i).getZpglempid());
				//处理工作单位
				records.get(i).setWorkUnit(_workunit);
				if("1".equals(records.get(i).getZpglEmployeeStatus())){
					if("1".equals(records.get(i).getAddInterview())){
						records.get(i).setZpglEmployeeStatusText("待面试");
					}else if("2".equals(records.get(i).getAddInterview())){
						records.get(i).setZpglEmployeeStatusText("筛选不通过");
					}else if("3".equals(records.get(i).getAddInterview())){
						records.get(i).setZpglEmployeeStatusText("已终止");
					}else{
						records.get(i).setZpglEmployeeStatusText("待筛选");
					}
				}else if("2".equals(records.get(i).getZpglEmployeeStatus())){
					//records.get(i)
					if("1".equals(records.get(i).getInterviewResultStatus())){
						records.get(i).setZpglEmployeeStatusText("面试通过");
					}else if("2".equals(records.get(i).getInterviewResultStatus())){
						records.get(i).setZpglEmployeeStatusText("面试不通过");
					}else if("3".equals(records.get(i).getInterviewResultStatus())){
						records.get(i).setZpglEmployeeStatusText("面试待定");
					}else if("4".equals(records.get(i).getInterviewResultStatus())){  //继续面试
						records.get(i).setZpglEmployeeStatusText("待面试");
					}else{
						records.get(i).setZpglEmployeeStatusText("待面试");
					}
				}else{
					records.get(i).setZpglEmployeeStatusText(ZpglEmployeeStatusEnum.getValByKey(records.get(i).getZpglEmployeeStatus()));
				}

				
				//处理工作年限 
				if(!StringUtil.isEmpty(records.get(i).getEntryDate())) {
					try {
						String yearCompare = DateUtils.yearCompare(DateUtils.getStringToDate(records.get(i).getEntryDate()),new Date());
						records.get(i).setEntryDateText(yearCompare + "年");
					} catch (Exception e) {
						log.error("工作年限转换异常");
					}	
					
				}
					
				
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }
  
    @Transactional(readOnly = false)
	@Override
	public Integer updateBasic(HrmsZpglEmployee record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsZpglEmployee findByIdentityNumber(String identityNumber) {
		Example example = new Example(HrmsZpglEmployee.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("identityNumber", identityNumber);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsZpglEmployee> listBean = mapper.selectByExample(example);
		if (listBean != null && listBean.size() > 0) {
			return listBean.get(0);
		}
		return null;
	}

	/**
	 * 根据身份证号查询所有信息
	 * @param identityNumber
	 * @return
	 */
	@Override
	public HrmsZpglEmployee findIdentityNumberAllData(String identityNumber) {
		Example example = new Example(HrmsZpglEmployee.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andCondition(" LOWER(identity_number) = LOWER('"+identityNumber+"')" );
		List<HrmsZpglEmployee> listBean = mapper.selectByExample(example);
		if (listBean != null && listBean.size() > 0) {
			HrmsZpglEmployee bean = listBean.get(0);
			//根据id查询其它信息
			HrmsZpglEmployee hrmsZpglEmployee = selectById(bean.getId());
			return hrmsZpglEmployee;
		}
		return null;
	}

	@Override
	public DataSet<HrmsZpglEmployeeOutListVo> getDsaList(Page page, HrmsZpglEmployee record) {
		page.setSidx(" t1.create_date2");
		page.setSord(" desc");
		//根据当前登录账号机构编码过滤查询数据
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsZpglEmployeeOutListVo> records = mapper.getDscPageList(page,record);
		//性别
		Map<String, String> sexConvertDictMap = convertDictMap("SEX_TYPE");
		//学历
		Map<String, String> educationConvertDictMap = convertDictMap("education_type");

		if (records != null && records.size() > 0) {
			for (HrmsZpglEmployeeOutListVo hrmsZpglEmployeeOutListVo : records) {
				hrmsZpglEmployeeOutListVo.setGendertext(sexConvertDictMap.get(hrmsZpglEmployeeOutListVo.getGender()));
				hrmsZpglEmployeeOutListVo.setXuelitext(educationConvertDictMap.get(hrmsZpglEmployeeOutListVo.getXueli()));
				String _workunit = hrmsZpglWorkrecordService.getOneData(hrmsZpglEmployeeOutListVo.getZpglempid());
				//处理工作单位
				hrmsZpglEmployeeOutListVo.setWorkUnit(_workunit);
				if ("1".equals(hrmsZpglEmployeeOutListVo.getZpglEmployeeStatus())) {
					if ("1".equals(hrmsZpglEmployeeOutListVo.getAddInterview())) {
						hrmsZpglEmployeeOutListVo.setZpglEmployeeStatusText("待面试");
					} else if ("2".equals(hrmsZpglEmployeeOutListVo.getAddInterview())) {
						hrmsZpglEmployeeOutListVo.setZpglEmployeeStatusText("筛选不通过");
					} else if ("3".equals((hrmsZpglEmployeeOutListVo.getAddInterview()))) {
						hrmsZpglEmployeeOutListVo.setZpglEmployeeStatusText("已终止");
					} else {
						hrmsZpglEmployeeOutListVo.setZpglEmployeeStatusText("待筛选");
					}

				} else if ("2".equals(hrmsZpglEmployeeOutListVo.getZpglEmployeeStatus())) {
					//records.get(i)
					if ("1".equals(hrmsZpglEmployeeOutListVo.getInterviewResultStatus())) {
						hrmsZpglEmployeeOutListVo.setZpglEmployeeStatusText("面试通过");
					} else if ("2".equals(hrmsZpglEmployeeOutListVo.getInterviewResultStatus())) {
						hrmsZpglEmployeeOutListVo.setZpglEmployeeStatusText("面试不通过");
					} else if ("3".equals(hrmsZpglEmployeeOutListVo.getInterviewResultStatus())) {
						hrmsZpglEmployeeOutListVo.setZpglEmployeeStatusText("面试待定");
					} else if ("4".equals(hrmsZpglEmployeeOutListVo.getInterviewResultStatus())) {  //继续面试
						hrmsZpglEmployeeOutListVo.setZpglEmployeeStatusText("待面试");
					} else {
						hrmsZpglEmployeeOutListVo.setZpglEmployeeStatusText("待面试");
					}
				} else {
					hrmsZpglEmployeeOutListVo.setZpglEmployeeStatusText(ZpglEmployeeStatusEnum.getValByKey(hrmsZpglEmployeeOutListVo.getZpglEmployeeStatus()));
				}
				//处理工作年限
				if (!StringUtil.isEmpty(hrmsZpglEmployeeOutListVo.getEntryDate())) {
					try {
						String yearCompare = DateUtils.yearCompare(DateUtils.getStringToDate(hrmsZpglEmployeeOutListVo.getEntryDate()), new Date());
						hrmsZpglEmployeeOutListVo.setEntryDateText(yearCompare + "年");
					} catch (Exception e) {
						log.error("工作年限转换异常");
					}
				}
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public EmpDetailsOutVo findLinkZpglempId(String zpglempid) {
		//查询基础表信息
		HrmsZpglEmployee hrmsZpglEmployee = mapper.selectByPrimaryKey(zpglempid);
		//查询筛选信息
		HrmsZpglInterviewMessage him = new HrmsZpglInterviewMessage();
		him.setZpglempid(zpglempid);
		List<HrmsZpglInterviewMessage> himList = ziMapper.select(him);
		HrmsZpglInterviewMessage himBean = new HrmsZpglInterviewMessage();
		if(himList != null && himList.size() >0){
			himBean =himList.get(0);
		}

		HrmsZpglInterviewResult zrm = new HrmsZpglInterviewResult();
		zrm.setZpglempid(zpglempid);
		List<HrmsZpglInterviewResult> zrmList = zrMappr.select(zrm);
		HrmsZpglInterviewResult zrmBean = new HrmsZpglInterviewResult();
		if(zrmList != null && zrmList.size() >0){
			zrmBean =zrmList.get(0);
		}

		Map<String,Object> retMap = new HashMap<>();

		if("1".equals(hrmsZpglEmployee.getZpglEmployeeStatus())){
			if("1".equals(hrmsZpglEmployee.getAddInterview())){
				hrmsZpglEmployee.setZpglEmployeeStatusText("待面试");
			}else if("2".equals(hrmsZpglEmployee.getAddInterview())){
				hrmsZpglEmployee.setZpglEmployeeStatusText("筛选不通过");
			}else if("3".equals(hrmsZpglEmployee.getAddInterview())){
				hrmsZpglEmployee.setZpglEmployeeStatusText("已终止");
			}else{
				hrmsZpglEmployee.setZpglEmployeeStatusText("待筛选");
			}
		}else if("2".equals(hrmsZpglEmployee.getZpglEmployeeStatus())){
			if("1".equals(zrmBean.getInterviewResultStatus())){
				hrmsZpglEmployee.setZpglEmployeeStatusText("面试通过");
			}else if("2".equals(zrmBean.getInterviewResultStatus())){
				hrmsZpglEmployee.setZpglEmployeeStatusText("面试不通过");
			}else if("3".equals(zrmBean.getInterviewResultStatus())){
				hrmsZpglEmployee.setZpglEmployeeStatusText("面试待定");
			}else if("4".equals(zrmBean.getInterviewResultStatus())){  //继续面试
				hrmsZpglEmployee.setZpglEmployeeStatusText("待面试");
			}else{
				hrmsZpglEmployee.setZpglEmployeeStatusText("待面试");
			}
		}else{
			hrmsZpglEmployee.setZpglEmployeeStatusText(ZpglEmployeeStatusEnum.getValByKey(hrmsZpglEmployee.getZpglEmployeeStatus()));
		}


		EmpDetailsOutVo base = new EmpDetailsOutVo();
		base.setZpglempid(hrmsZpglEmployee.getId());
		base.setZpglEmployeeStatusText(hrmsZpglEmployee.getZpglEmployeeStatusText());
		base.setInterviewPath(hrmsZpglEmployee.getInterviewPath());
		base.setAddTalentPool(hrmsZpglEmployee.getAddTalentPool());
		base.setApplicantDeptname(hrmsZpglEmployee.getApplicantDeptname());
		base.setApplicantPost(hrmsZpglEmployee.getApplicantPost());
		base.setApplicantPosttext(hrmsZpglEmployee.getApplicantPosttext());
		base.setAddTalentPool(hrmsZpglEmployee.getAddTalentPool());

		base.setMessageId(himBean.getId());
		base.setConform(himBean.getConform());
		base.setInterviewDept(himBean.getInterviewDept());
		base.setInterviewDepttext(himBean.getInterviewDepttext());
		base.setInterviewJob(himBean.getInterviewJob());
		base.setInterviewJobtext(himBean.getInterviewJobtext());
		base.setWrittenEmpNo(himBean.getWrittenEmpNo());
		base.setWrittenName(himBean.getWrittenName());
		base.setWrittenDate(himBean.getWrittenDate());
		base.setOperationEmpNo(himBean.getOperationEmpNo());
		base.setOperationName(himBean.getOperationName());
		base.setOperationDate(himBean.getOperationDate());
		base.setInterviewEmpNo(himBean.getInterviewEmpNo());
		base.setInterviewName(himBean.getInterviewName());
		base.setInterviewDate(himBean.getInterviewDate());

		base.setComeEntryDate(zrmBean.getEntryDate());
		base.setStudyoutDept(zrmBean.getStudyoutDept());
		base.setStudyoutDepttext(zrmBean.getStudyoutDepttext());
		base.setStudyoutJob(zrmBean.getStudyoutJob());
		base.setStudyoutJobtext(zrmBean.getStudyoutJobtext());
		base.setResultId(zrmBean.getId());
		base.setWrittenResult(zrmBean.getWrittenResult());
		base.setOperationResult(zrmBean.getOperationResult());
		base.setInterviewResult(zrmBean.getInterviewResult());
		base.setEntryDate(zrmBean.getEntryDate());
		base.setRemark(zrmBean.getRemark());
		base.setInterviewResultStatus(zrmBean.getInterviewResultStatus());

		base.setMsgInterviewPath(himBean.getMsgInterviewPath());
		base.setResInterviewPath(zrmBean.getResInterviewPath());
		return base;
	}
}

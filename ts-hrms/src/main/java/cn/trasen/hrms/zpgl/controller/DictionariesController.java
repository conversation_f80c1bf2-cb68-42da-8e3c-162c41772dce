package cn.trasen.hrms.zpgl.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.zp.bean.DictJobtitle;
import cn.trasen.hrms.zp.service.RecruitPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/** 
* @ClassName: DictionariesController 
* @Description: 招聘管理外网字典
* <AUTHOR>  
* @date 2023年2月9日 下午5:30:54 
*  
*/
@RestController
@Api(tags = "招聘管理字典Controller")
public class DictionariesController {
	
	private transient static final Logger logger = LoggerFactory.getLogger(DictionariesController.class);


	@Autowired
	DictItemFeignService dictItemFeignService;
	
	@Autowired
	RecruitPlanService recruitPlanService;
	
	@Value("${zpglDomainName}")
	private String zpglDomainName;  //二维码前缀
	
	@Value("${zpglDomainNameSignIn}")
	private String zpglDomainNameSignIn;  //二维码前缀
	

	@ApiOperation(value = "人才类型", notes = "人才类型") 
	@PostMapping("/getDictionariesByType/{type}")
	public PlatformResult<List<DictItemResp>> getDictionariesByType(@PathVariable String type) {
		
		//性别  SEX_TYPE    外语等级 WAIYU_TYPE           政治面貌 political_status		户籍类型	 HUJI_TYPE   
		//名族 nationality_name		学习阶段  XUEXIJIEDUAN		家庭关系  personnel_relationship
		List<DictItemResp> dictItemRespList = dictItemFeignService.getDictItemByTypeCode(type).getObject();
		if(null == dictItemRespList) {
			dictItemRespList = new ArrayList<DictItemResp>();
		}
		return PlatformResult.success(dictItemRespList);
	}
	
	@ApiOperation(value = "职称", notes = "职称") 
	@PostMapping("/getJobtitleByType/list")
	public PlatformResult<List<DictJobtitle>> getJobtitleByTypeList() {
		 return PlatformResult.success(recruitPlanService.getjobtitleList());
	}
	
	@ApiOperation(value = "点击分享链接生成带参二维码", notes = "点击分享链接生成带参二维码")
	@GetMapping("/api/HrInductionInfo/qrCode")
	public PlatformResult<Map<String,String>> generateQRCode(){
		Map<String,String> map = new HashedMap<>();
		map.put("zpglDomainName",zpglDomainName);
		map.put("zpglDomainNameSignIn",zpglDomainNameSignIn);
		return PlatformResult.success(map);
	}
}

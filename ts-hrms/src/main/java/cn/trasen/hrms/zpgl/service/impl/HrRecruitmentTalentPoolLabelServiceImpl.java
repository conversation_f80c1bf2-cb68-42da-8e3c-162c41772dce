package cn.trasen.hrms.zpgl.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrRecruitmentTalentPoolLabelMapper;
import cn.trasen.hrms.zpgl.dao.HrRecruitmentTalentPoolLabelTalentLibraryMapper;
import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPoolLabel;
import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPoolLabelTalentLibrary;
import cn.trasen.hrms.zpgl.model.HrmsZpglAddTalentpool;
import cn.trasen.hrms.zpgl.service.HrRecruitmentTalentPoolLabelService;
import cn.trasen.hrms.zpgl.service.HrRecruitmentTalentPoolLabelTalentLibraryService;
import cn.trasen.hrms.zpgl.service.HrRecruitmentTalentPoolService;
import cn.trasen.hrms.zpgl.service.HrmsZpglAddTalentpoolService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrRecruitmentTalentPoolLabelServiceImpl
 * @Description TODO
 */
@Service
public class HrRecruitmentTalentPoolLabelServiceImpl implements HrRecruitmentTalentPoolLabelService {

    @Autowired
    private HrRecruitmentTalentPoolLabelMapper mapper;
    

    @Autowired
    private HrRecruitmentTalentPoolLabelTalentLibraryMapper talentLibrarymapper;

    
    @Autowired
    private HrRecruitmentTalentPoolLabelTalentLibraryService talentPoolLabelTalentLibraryService;
    
    @Autowired
    HrmsZpglAddTalentpoolService hrmsZpglAddTalentpoolService;
    /**
     * 保存、编辑
     *
     * @param recruitmentTalentPoolLabelInputVo
     * @return
     */
    
    @Transactional
    @Override
    public Integer update(HrRecruitmentTalentPoolLabel record) {
    	
    	
    	if(StringUtil.isEmpty(record.getLabelName())&& !StringUtil.isEmpty(record.getStatus())) {
    		return mapper.updateByPrimaryKeySelective(record);
    	}
    	
    	Example example = new Example(HrRecruitmentTalentPoolLabel.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("labelName", record.getLabelName());
		criteria.andNotEqualTo("hrRecruitmentTalentPoolLabelId", record.getHrRecruitmentTalentPoolLabelId());
		List<HrRecruitmentTalentPoolLabel> selList = mapper.selectByExample(example);
		// 判断标签名称是否存在
		if (selList != null && selList.size() > 0) {
			Assert.hasText(null, "标签已存在");
		} else {

			
			// 人才库对应岗位的人全部添加上标签
			if (!StringUtil.isEmpty(record.getLabelJob())) {
				List<String> result = Arrays.asList(record.getLabelJob().split(","));
				for (int k = 0; k < result.size(); k++) {
					List<HrmsZpglAddTalentpool> list = hrmsZpglAddTalentpoolService
							.getListByAffiliationJob(result.get(k));
					if (null != list && list.size() > 0) {
						list.stream().forEach(item -> {
							// 这些人全部添加上标签
							
							HrRecruitmentTalentPoolLabelTalentLibrary selectById = talentPoolLabelTalentLibraryService.selectById(item.getZpglempid(), record.getHrRecruitmentTalentPoolLabelId());
							if(selectById == null ) {
								HrRecruitmentTalentPoolLabelTalentLibrary insertBean = new HrRecruitmentTalentPoolLabelTalentLibrary();
								insertBean.setHrRecruitmentTalentPoolLabelId(record.getHrRecruitmentTalentPoolLabelId()); // 标签id
								insertBean.setTalentLibraryId(item.getZpglempid()); // 人员id
								insertBean.setType(1);
								insertBean.setCreateDate(new Date());
								insertBean.setUpdateDate(new Date());
								insertBean.setIsDeleted("N");
								insertBean.setId(IdUtil.getId());
								ThpsUser user = UserInfoHolder.getCurrentUserInfo();
								if (user != null) {
									insertBean.setCreateUser(user.getUsercode());
									insertBean.setCreateUserName(user.getUsername());
								}
								insertBean.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
								talentLibrarymapper.insertSelective(insertBean);
							}
						
						});
					}
				}
			}

		}
        return mapper.updateByPrimaryKeySelective(record);
    }

	@Transactional
	@Override
	public Integer save(HrRecruitmentTalentPoolLabel record) {
		Example example = new Example(HrRecruitmentTalentPoolLabel.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("labelName", record.getLabelName());
		List<HrRecruitmentTalentPoolLabel> selList = mapper.selectByExample(example);
		// 判断标签名称是否存在
		if (selList != null && selList.size() > 0) {
			Assert.hasText(null, "标签已存在");
		} else {

			String id = IdUtil.getId();
			record.setHrRecruitmentTalentPoolLabelId(id);
			record.setCreateDate(new Date());
			record.setUpdateDate(new Date());
			record.setIsDeleted("N");
			record.setStatus(Contants.IS_DELETED_TURE);
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				record.setCreateUser(user.getUsercode());
				record.setCreateUserName(user.getUsername());
			}
//			mapper.insertSelective(record);

			// 人才库对应岗位的人全部添加上标签
			if (!StringUtil.isEmpty(record.getLabelJob())) {
				List<String> result = Arrays.asList(record.getLabelJob().split(","));
				for (int k = 0; k < result.size(); k++) {
					List<HrmsZpglAddTalentpool> list = hrmsZpglAddTalentpoolService
							.getListByAffiliationJob(result.get(k));
					if (null != list && list.size() > 0) {
						list.stream().forEach(item -> {
							// 这些人全部添加上标签
							HrRecruitmentTalentPoolLabelTalentLibrary insertBean = new HrRecruitmentTalentPoolLabelTalentLibrary();
							insertBean.setHrRecruitmentTalentPoolLabelId(id); // 标签id
							insertBean.setTalentLibraryId(item.getZpglempid()); // 人员id
							insertBean.setType(1);
							insertBean.setCreateDate(new Date());
							insertBean.setUpdateDate(new Date());
							insertBean.setIsDeleted("N");
							insertBean.setId(IdUtil.getId());
							if (user != null) {
								insertBean.setCreateUser(user.getUsercode());
								insertBean.setCreateUserName(user.getUsername());
							}
							insertBean.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
							talentLibrarymapper.insertSelective(insertBean);
						});
					}
				}
			}

		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}


    /**
     * 启用禁用
     *
     * @param recruitmentTalentPoolLabelId 主键
     * @param status                       状态（N停用Y启用）
     * @return
     */
    @Override
    public Integer enableDisable(String recruitmentTalentPoolLabelId, String status) {
        HrRecruitmentTalentPoolLabel talentPoolLabel = new HrRecruitmentTalentPoolLabel();
        talentPoolLabel.setHrRecruitmentTalentPoolLabelId(recruitmentTalentPoolLabelId);
        talentPoolLabel.setStatus(status);
        talentPoolLabelTalentLibraryService.deleteById(recruitmentTalentPoolLabelId);
        return update(talentPoolLabel);
    }



    @Transactional
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        HrRecruitmentTalentPoolLabel record = new HrRecruitmentTalentPoolLabel();
        record.setHrRecruitmentTalentPoolLabelId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public HrRecruitmentTalentPoolLabel selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<HrRecruitmentTalentPoolLabel> getDataSetList(Page page, HrRecruitmentTalentPoolLabel record) {
        Example example = new Example(HrRecruitmentTalentPoolLabel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		//根据当前登录账号机构编码过滤
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (!StringUtils.isEmpty(record.getLabelName())) {
            criteria.andLike("labelName", "%" + record.getLabelName() + "%");
        }
        List<HrRecruitmentTalentPoolLabel> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    /**
     * 获取人才库的标签
     *
     * @param talentLibraryId 人才库主键
     * @return
     */
    @Override
    public List<HrRecruitmentTalentPoolLabel> getTalentLibraryLableById(String talentLibraryId) {
        return mapper.getTalentLibraryLableById(talentLibraryId);
    }

	@Override
	public List<HrRecruitmentTalentPoolLabel> getAllList() {
		Example example = new Example(HrRecruitmentTalentPoolLabel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return  mapper.selectByExample(example);
	}
}

package cn.trasen.hrms.zpgl.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrInterviewPhrasesMapper;
import cn.trasen.hrms.zpgl.model.HrInterviewPhrases;
import cn.trasen.hrms.zpgl.service.HrInterviewPhrasesService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrInterviewPhrasesServiceImpl
 * @Description TODO
 * @date 2021年7月1日 下午1:49:09
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrInterviewPhrasesServiceImpl implements HrInterviewPhrasesService {

	@Autowired
	private HrInterviewPhrasesMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrInterviewPhrases record) {
		Assert.hasText(record.getContent(),"请不要添加空的标签");
		// 如果已经对该问题进行了保存 则是新增
		Example example = new Example(HrInterviewPhrases.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("content",record.getContent() );
		List<HrInterviewPhrases> labelList = mapper.selectByExample(example);  //查询出所有的 
		
		
		if (labelList != null && labelList.size() > 0) {
			Assert.hasText(record.getContent(),"请不要重复添加");
		}else {
			record.setInterviewPhrasesId(IdUtil.getId());
			record.setCreateDate(new Date());
			record.setIsDeleted("N");
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				record.setCreateUser(user.getUsercode());
				record.setCreateUserName(user.getUsername());
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
				record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			}
			  mapper.insertSelective(record);
		}
		
		
		return 1;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrInterviewPhrases record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrInterviewPhrases record = new HrInterviewPhrases();
		record.setInterviewPhrasesId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrInterviewPhrases selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<String> getDataSetList(Page page, HrInterviewPhrases record) {
		Example example = new Example(HrInterviewPhrases.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrInterviewPhrases> labelList = mapper.selectByExample(example);  //查询出所有的 
		List<String> listStr = new ArrayList<>();
		labelList.forEach(item->{
			listStr.add(item.getContent());
		});
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), listStr);
	}
	/**
	 * @Title cancelLabel
	 * @Description X去掉当前阶段的常用语
	 * @param label
	 * @return
	 * @date 2021/7/1 15:06
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public void cancelLabel(HrInterviewPhrases label) {
		// 获取到当前问题对应的所有标签
		Example example = new Example(HrInterviewPhrases.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andEqualTo("content", label.getContent());
		mapper.deleteByExample(example);

	}

}

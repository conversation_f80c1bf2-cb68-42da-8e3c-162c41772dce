package cn.trasen.hrms.zpgl.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hr_recruitment_talent_pool")
@Setter
@Getter
public class HrRecruitmentTalentPool {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 招聘-人才库主键
     */
    @Id
    @Column(name = "hr_recruitment_talent_pool_id")
    @ApiModelProperty(value = "招聘-人才库主键")
    private String hrRecruitmentTalentPoolId;

    /**
     * 招聘-人才库编号
     */
    @Column(name = "talent_pool_no")
    @ApiModelProperty(value = "招聘-人才库编号")
    private String talentPoolNo;
    
    @Column(name = "seq_no")
    @ApiModelProperty(value = "序号")
    private Integer seqNo;
    
    @Column(name = "fullpath")
    @ApiModelProperty(value = "序号")
    private String fullpath;
    
    

    /**
     * 招聘-人才库名称
     */
    @Column(name = "talent_pool_name")
    @ApiModelProperty(value = "招聘-人才库名称")
    private String talentPoolName;

    /**
     * 招聘-人才库上级机构id
     */
    @Column(name = "parent_id")
    @ApiModelProperty(value = "招聘-人才库上级机构id")
    private String parentId;


    @ApiModelProperty(value = "招聘-人才库上级机构名称")
    private String parentName;

    /**
     * 招聘-人才库编号
     */
    @Column(name = "parent_talent_pool_no")
    @ApiModelProperty(value = "招聘-人才库编号")
    private String parentTalentPoolNo;
    
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    

    /**
     * 状态（N停用Y启用）
     */
    @ApiModelProperty(value = "状态（N停用Y启用）")
    private String status;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateUserName;

    /**
     * 删除标识(N存在，Y删除)
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识(N存在，Y删除)")
    private String isDeleted;
}
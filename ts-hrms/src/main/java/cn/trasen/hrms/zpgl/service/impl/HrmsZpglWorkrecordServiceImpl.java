package cn.trasen.hrms.zpgl.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrmsZpglWorkrecordMapper;
import cn.trasen.hrms.zpgl.model.HrmsZpglWorkrecord;
import cn.trasen.hrms.zpgl.service.HrmsZpglWorkrecordService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsZpglWorkrecordServiceImpl
 * @Description TODO
 * @date 2023��2��8�� ����4:11:35
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsZpglWorkrecordServiceImpl implements HrmsZpglWorkrecordService {

	@Autowired
	private HrmsZpglWorkrecordMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsZpglWorkrecord record) {
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsZpglWorkrecord record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsZpglWorkrecord record = new HrmsZpglWorkrecord();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsZpglWorkrecord selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsZpglWorkrecord> getDataSetList(Page page, HrmsZpglWorkrecord record) {
		Example example = new Example(HrmsZpglWorkrecord.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsZpglWorkrecord> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public Integer deleteByEmpId(String employeeId) {
		Assert.hasText(employeeId, "ID不能为空.");
		HrmsZpglWorkrecord record = new HrmsZpglWorkrecord();
		record.setZpglempid(employeeId);
		return mapper.delete(record);
	}

	@Override
	public List<HrmsZpglWorkrecord> findByEmpId(String employeeId) {
		Example example = new Example(HrmsZpglWorkrecord.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("zpglempid", employeeId);
		return mapper.selectByExample(example);
	}

	@Override
	public String getOneData(String zpglempid) {
		String workunit = mapper.getOneData(zpglempid);
		return workunit;
	}
}

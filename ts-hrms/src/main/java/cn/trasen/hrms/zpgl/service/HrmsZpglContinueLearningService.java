package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglContinueLearning;

/**
 * @ClassName HrmsZpglContinueLearningService
 * @Description TODO
 * @date 2023��2��8�� ����4:13:03
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglContinueLearningService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����4:13:03
	 * <AUTHOR>
	 */
	Integer save(HrmsZpglContinueLearning record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����4:13:03
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglContinueLearning record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��8�� ����4:13:03
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglContinueLearning
	 * @date 2023��2��8�� ����4:13:03
	 * <AUTHOR>
	 */
	HrmsZpglContinueLearning selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglContinueLearning>
	 * @date 2023��2��8�� ����4:13:03
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglContinueLearning> getDataSetList(Page page, HrmsZpglContinueLearning record);

	Integer deleteByEmpId(String employeeId);

	List<HrmsZpglContinueLearning> findByEmpId(String employeeId);
}

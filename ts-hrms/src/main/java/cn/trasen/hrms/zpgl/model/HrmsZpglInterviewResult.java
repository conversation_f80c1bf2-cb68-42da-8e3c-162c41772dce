package cn.trasen.hrms.zpgl.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "hrms_zpgl_interview_result")
@Setter
@Getter
public class HrmsZpglInterviewResult {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * id
     */
	@Id
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 面试者id
     */
    @ApiModelProperty(value = "面试者id")
    private String zpglempid;

    /**
     * 面试结果 1 通过 2不通过 3待定
     */
    @Column(name = "interview_result_status")
    @ApiModelProperty(value = "面试结果 1 通过 2不通过 3待定")
    private String interviewResultStatus;

    /**
     * 笔试成绩
     */
    @Column(name = "written_result")
    @ApiModelProperty(value = "笔试成绩")
    private String writtenResult;

    /**
     * 实操成绩
     */
    @Column(name = "operation_result")
    @ApiModelProperty(value = "实操成绩")
    private String operationResult;

    /**
     * 面试成绩
     */
    @Column(name = "interview_result")
    @ApiModelProperty(value = "面试成绩")
    private String interviewResult;

    /**
     * 拟定科室
     */
    @Column(name = "studyout_dept")
    @ApiModelProperty(value = "拟定科室")
    private String studyoutDept;

    /**
     * 拟定科室名称
     */
    @Column(name = "studyout_depttext")
    @ApiModelProperty(value = "拟定科室名称")
    private String studyoutDepttext;

    /**
     * 拟定岗位
     */
    @Column(name = "studyout_job")
    @ApiModelProperty(value = "拟定岗位")
    private String studyoutJob;

    /**
     * 拟定岗位名称
     */
    @Column(name = "studyout_jobtext")
    @ApiModelProperty(value = "拟定岗位名称")
    private String studyoutJobtext;

    /**
     * 评价备注
     */
    @ApiModelProperty(value = "评价备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 修改时间
     */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateUserName;

    /**
     * 删除标识(N存在，Y删除)
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识(N存在，Y删除)")
    private String isDeleted;
    
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "entry_date")
    @ApiModelProperty(value = "入职时间")
    private Date entryDate; 
	

    /**
     * 面试结果 岗位全路径
     */
    @Column(name = "res_interview_path")
    @ApiModelProperty(value = "面试结果 岗位全路径")
    private String resInterviewPath;
    
    @Transient
    private String addResultType; 
	
	@Transient
	private String resultId;  //人才库来的 传1
	
	@Transient
	private String messageId;  //人才库来的 传1
	@Transient
	private String interviewPath;  //人才库来的 传1
	

}
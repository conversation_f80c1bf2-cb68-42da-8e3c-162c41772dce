package cn.trasen.hrms.zpgl.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrRecruitmentTalentPoolLabelTalentLibraryMapper;
import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPoolLabelTalentLibrary;
import cn.trasen.hrms.zpgl.service.HrRecruitmentTalentPoolLabelTalentLibraryService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrRecruitmentTalentPoolLabelTalentLibraryServiceImpl
 * @Description TODO
 */
@Service
public class HrRecruitmentTalentPoolLabelTalentLibraryServiceImpl implements HrRecruitmentTalentPoolLabelTalentLibraryService {

    @Autowired
    private HrRecruitmentTalentPoolLabelTalentLibraryMapper mapper;

    /**
     * 人才库标签，批量打标签
     *
     * @param talentLibraryId              人才库id
     * @param recruitmentTalentPoolLabelId 人才库标签id
     * @return
     */
    @Override
    public Integer save(List<String> talentLibraryId, String recruitmentTalentPoolLabelId) {
        talentLibraryId.forEach(talentLibraryIdtemp -> {
            // 存在则跳过
            if(!CollectionUtil.isEmpty(select(talentLibraryIdtemp,recruitmentTalentPoolLabelId))){
                return;
            }
            HrRecruitmentTalentPoolLabelTalentLibrary talentPoolLabelTalentLibrary = new HrRecruitmentTalentPoolLabelTalentLibrary();
            talentPoolLabelTalentLibrary.setHrRecruitmentTalentPoolLabelId(recruitmentTalentPoolLabelId);
            talentPoolLabelTalentLibrary.setTalentLibraryId(talentLibraryIdtemp);
            talentPoolLabelTalentLibrary.setType(1);
            save(talentPoolLabelTalentLibrary);
        });
        return 1;
    }

    /**
     * 人才库标签，批量打标签
     *
     * @param talentLibraryId              人才库id
     * @param recruitmentTalentPoolLabelId 人才库标签id
     * @return
     */
    @Override
    public Integer save(String talentLibraryId, List<String> recruitmentTalentPoolLabelId) {
        deleteByTalentLibraryId(talentLibraryId);
        recruitmentTalentPoolLabelId.forEach(talentLibraryIdtemp -> {
            HrRecruitmentTalentPoolLabelTalentLibrary talentPoolLabelTalentLibrary = new HrRecruitmentTalentPoolLabelTalentLibrary();
            talentPoolLabelTalentLibrary.setHrRecruitmentTalentPoolLabelId(talentLibraryIdtemp);
            talentPoolLabelTalentLibrary.setTalentLibraryId(talentLibraryId);
            talentPoolLabelTalentLibrary.setType(1);
            save(talentPoolLabelTalentLibrary);
        });
        return 1;
    }

    /**
     * 根据人才库id，人才库标签id查询数据
     *
     * @param talentLibraryId 人才库id
     * @param recruitmentTalentPoolLabelId 人才库标签id
     * @return
     */
    private List<HrRecruitmentTalentPoolLabelTalentLibrary> select(String talentLibraryId, String recruitmentTalentPoolLabelId) {
        Example example = new Example(HrRecruitmentTalentPoolLabelTalentLibrary.class);
        example.createCriteria()
                .andEqualTo("talentLibraryId", talentLibraryId)
                .andEqualTo("hrRecruitmentTalentPoolLabelId", recruitmentTalentPoolLabelId)
                .andEqualTo(Contants.IS_DELETED_FIELD, "N");
        return mapper.selectByExample(example);
    }

    /**
     * @param talentLibraryId              人才库id
     * @param recruitmentTalentPoolLabelId 人才库标签id
     * @return
     */
    @Override
    public HrRecruitmentTalentPoolLabelTalentLibrary selectById(String talentLibraryId, String recruitmentTalentPoolLabelId) {
        Example example = new Example(HrRecruitmentTalentPoolLabelTalentLibrary.class);
        example.createCriteria()
                .andEqualTo("talentLibraryId", talentLibraryId)
                .andEqualTo("hrRecruitmentTalentPoolLabelId", recruitmentTalentPoolLabelId)
//                .andEqualTo("type", 1)
                .andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<HrRecruitmentTalentPoolLabelTalentLibrary> lsit = mapper.selectByExample(example);
        if(lsit != null && lsit.size() >0) {
        	return lsit.get(0);
        }else {
        	return null;
        }
    }


    /**
     * 逻辑删除
     *
     * @param recruitmentTalentPoolLabelId
     * @return
     */
    @Override
    @Transactional(readOnly = false)
    public Integer deleteById(String recruitmentTalentPoolLabelId) {
        HrRecruitmentTalentPoolLabelTalentLibrary record = new HrRecruitmentTalentPoolLabelTalentLibrary();
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        Example example = new Example(HrRecruitmentTalentPoolLabelTalentLibrary.class);
        example.createCriteria()
                .andEqualTo("hrRecruitmentTalentPoolLabelId", recruitmentTalentPoolLabelId)
                .andEqualTo("type", 1)
                .andEqualTo(Contants.IS_DELETED_FIELD, "N");

        return mapper.updateByExampleSelective(record, example);
    }


    /**
     * 逻辑删除
     * @param talentLibraryId 人才库管理id
     * @return
     */
    @Transactional(readOnly = false)
    @Override
    public Integer deleteByTalentLibraryId(String talentLibraryId) {
        HrRecruitmentTalentPoolLabelTalentLibrary record = new HrRecruitmentTalentPoolLabelTalentLibrary();
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        Example example = new Example(HrRecruitmentTalentPoolLabelTalentLibrary.class);
        example.createCriteria()
                .andEqualTo("talentLibraryId", talentLibraryId)
                .andEqualTo("type", 1)
                .andEqualTo(Contants.IS_DELETED_FIELD, "N");
        return mapper.updateByExampleSelective(record, example);
    }


    @Transactional(readOnly = false)
    @Override
    public Integer save(HrRecruitmentTalentPoolLabelTalentLibrary record) {
    	record.setId(IdUtil.getId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return mapper.insertSelective(record);
    }

}

package cn.trasen.hrms.zpgl.vo.outVo;

import java.util.Date;

import javax.persistence.Column;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName: HrmsZpglAddTalentpoolOutListVo
 * @Description: 人才库列表vo
 * <AUTHOR>
 * @date 2023年2月20日 下午4:59:16
 * 
 */

@Setter
@Getter
public class HrmsZpglAddTalentpoolOutListVo {

	/**
	 * id
	 */
	@ApiModelProperty(value = "id")
	private String id; // 人员id

	private String zpglempid; // 人员id
	private String messageId; // 面试信息id
	private String resultId; // 面试结果iD

	private String addInterview;

	private String conform;

	private String  interviewResultStatus;
	private String  zpglEmployeeStatus;

	private String interviewPool; 

	private String employeeName; // 姓名
	
	private String no;

	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别")
	private String gender;

	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别文本")
	private String gendertext;

	@ApiModelProperty(value = "籍贯")
	private String birthplace;
	
	private String addSource;  //数据添加来源  1 人才库添加的
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "联系方式")
	private String iphone;

	/**
	 * 参加工作时间
	 */
	@ApiModelProperty(value = "参加工作时间")
	private String entryDate;

	/**
	 * 工作年限
	 */
	@ApiModelProperty(value = "工作年限")
	private String entryDateText;

	@ApiModelProperty(value = "职称名称")
	private String zhichengmingcheng;// 职称名称

	@ApiModelProperty(value = "职称名称文本")
	private String zhichengmingchengtext;// 职称名称文本

	@ApiModelProperty(value = "职称专业")
	private String zhuenye; // 职称专业

	@ApiModelProperty(value = "学历")
	private String xueli; // 学历

	@ApiModelProperty(value = "学历文本")
	private String xuelitext; // 学历文本

	@ApiModelProperty(value = "毕业学校")
	private String biyeyuanxiao; // 毕业学校

	private String fullpath; // 列表所属人才库 全路径
	private String email; // 邮箱
	private String age; // 年龄

	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private String createDate;
	
	private String affiliationJob;  // 归属岗位
	
	private String affiliationJobtext;  // 归属岗位名称
	
	private String addType;
	
    @ApiModelProperty(value = "归属人才库科室id")
    private String affiliationDept;

    @ApiModelProperty(value = "归属人才库科室名称")
    private String affiliationDepttext;

	private String zpglEmployeeStatusText;  //状态中文

	@ApiModelProperty(value = "更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date updateDate;

	private String workUnit;   //工作单位

	private String addTalentPool;
	
}

package cn.trasen.hrms.zpgl.controller;

import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.service.HrmsZpglReportService;
import cn.trasen.hrms.zpgl.vo.outVo.ReportOutVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @ClassName HrmsZpglEmployeeController
 * @Description TODO
 * @date 2024 0730
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglReportController")
public class HrmsZpglReportController {

	@Autowired
	private HrmsZpglReportService hrmsZpglReportService;

	/**
	 * @Title selectHrmsZpglEmployeeList
	 * @Description 查询列表
	 * @return DataSet<HrmsZpglEmployee>
	 * @date 2024 0730
	 * <AUTHOR>
	 */
	@ApiOperation(value = "数据列表", notes = "列表")
	@GetMapping("/api/zpglReport/dataList")
	public PlatformResult<Map<String, ReportOutVo>> selectHrmsZpglReportDataList(@RequestParam("personnelCategory") List<String> personnelCategory , String year) {
		return hrmsZpglReportService.getReportDataList(personnelCategory,year);
	}

	@ApiOperation(value = "招聘管理报表导出", notes = "报表导出")
	@GetMapping("/api/zpglReport/dataExport")
	public void reportDataExport(HttpServletRequest request, HttpServletResponse response,@RequestParam("personnelCategory") List<String> personnelCategory, String year) {
	     hrmsZpglReportService.reportDataExort(request,response,personnelCategory,year);
	}
}

package cn.trasen.hrms.zpgl.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hr_interview_phrases")
@Setter
@Getter
public class HrInterviewPhrases {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;


    @Id
    @Column(name = "interview_phrases_id")
    private String interviewPhrasesId;

    /**
     * 常用语
     */
    @ApiModelProperty(value = "常用语")
    private String content;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "create_date")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_date")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_user_name")
    private String updateUserName;
}
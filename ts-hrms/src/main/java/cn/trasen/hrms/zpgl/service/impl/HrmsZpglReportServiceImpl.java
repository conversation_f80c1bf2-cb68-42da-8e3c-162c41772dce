package cn.trasen.hrms.zpgl.service.impl;

import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.model.HrmsDictInfo;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.zpgl.dao.HrmsZpglInterviewResultMapper;
import cn.trasen.hrms.zpgl.service.HrmsZpglReportService;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglReportOutListVo;
import cn.trasen.hrms.zpgl.vo.outVo.ReportOutVo;
import com.alibaba.nacos.common.utils.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrmsZpglEmployeeServiceImpl
 * @Description TODO
 * @date 2023��2��8�� ����3:18:46
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Slf4j
public class HrmsZpglReportServiceImpl implements HrmsZpglReportService {

    private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglReportServiceImpl.class);

    @Autowired
    HrmsZpglInterviewResultMapper zrMappr;
    @Autowired
    private HrmsDictInfoService hrmsDictInfoService;

    @Override
    public PlatformResult<Map<String, ReportOutVo>> getReportDataList(List<String> personnelCategory, String year) {
        Map<String, ReportOutVo> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(personnelCategory)) {
            ReportOutVo reportOutVo = new ReportOutVo();
            List<HrmsZpglReportOutListVo> dataSetList = zrMappr.getDataSetList(personnelCategory, year, UserInfoHolder.getCurrentUserCorpCode());
            List<List<String>> dataList = convertToListOfLists(dataSetList);
            List<HrmsZpglReportOutListVo> reportList = zrMappr.getReportIndexList(personnelCategory, year, UserInfoHolder.getCurrentUserCorpCode());
            List<List<String>> reportIndexList = convertToList(reportList);
            reportOutVo.setReportDataList(dataList);
            reportOutVo.setReportIndexList(reportIndexList);
            map.put(personnelCategory.toString(), reportOutVo);
        } else {
            List<HrmsDictInfo> list = hrmsDictInfoService.getDictInfoListByDictType("org_attributes");
            List<String> collect = list.stream().map(HrmsDictInfo::getDictValue).collect(Collectors.toList());
            ReportOutVo reportOutVo = new ReportOutVo();
            List<HrmsZpglReportOutListVo> dataSetList = zrMappr.getDataSetList(collect, year, UserInfoHolder.getCurrentUserCorpCode());
            List<List<String>> dataList = convertToListOfLists(dataSetList);
            List<HrmsZpglReportOutListVo> reportList = zrMappr.getReportIndexList(collect, year, UserInfoHolder.getCurrentUserCorpCode());
            List<List<String>> reportIndexList = convertToList(reportList);
            reportOutVo.setReportDataList(dataList);
            reportOutVo.setReportIndexList(reportIndexList);
            map.put(collect.toString(), reportOutVo);
        }

        return PlatformResult.success(map);
    }

    @Override
    public PlatformResult<List<List<String>>> getReportIndexList(List<String> personnelCategory, String year) {
        List<HrmsZpglReportOutListVo> dataSetList = zrMappr.getReportIndexList(personnelCategory, year, UserInfoHolder.getCurrentUserCorpCode());
        List<List<String>> lists = convertToList(dataSetList);
        return PlatformResult.success(lists);
    }

    @Override
    public void reportDataExort(HttpServletRequest request, HttpServletResponse response, List<String> personnelCategory, String year) {
        String filename = "招聘管理数据统计表";
        Resource resource = new ClassPathResource("template/zpsjbbb.xlsx");
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(resource.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
        XSSFWorkbook finalWorkbook = workbook;
        XSSFSheet sheet = finalWorkbook.getSheetAt(0);
        Row row0 = sheet.getRow(0);
        Cell cell = row0.getCell(0);
        cell.setCellValue(year + "年招聘管理数据统计表");


        List<HrmsDictInfo> list = hrmsDictInfoService.getDictInfoListByDictType("org_attributes");
        Map<String, String> map = list.stream().collect(Collectors.toMap(HrmsDictInfo::getDictValue, HrmsDictInfo::getDictName));

//        int count = 0;
        AtomicInteger j = new AtomicInteger();
        if (CollectionUtils.isNotEmpty(list)) {
            List<HrmsZpglReportOutListVo> reportDataList = getReportAllList(personnelCategory, year);
            StringBuilder sb = new StringBuilder();
            int b = 0;
            for (String category:personnelCategory){
                sb.append(map.get(category));
                if (personnelCategory.size() > b+1){
                    sb.append(",");
                    b++;
                }
            }
            try {
                Row row = sheet.getRow(1 + j.get());
                Cell cell1 = row.getCell(0);
                cell1.setCellValue("表" +  "：招聘数据（" + sb.toString() + "）");

                for (int i = 0; i < reportDataList.size(); i++) {
                    Row newRow = sheet.getRow(i + 4 + j.get());
                    newRow.getCell(2).setCellValue(reportDataList.get(i).getJanuary());
                    newRow.getCell(3).setCellValue(reportDataList.get(i).getFebruary());
                    newRow.getCell(4).setCellValue(reportDataList.get(i).getMarch());
                    newRow.getCell(5).setCellValue(reportDataList.get(i).getApril());
                    newRow.getCell(6).setCellValue(reportDataList.get(i).getMay());
                    newRow.getCell(7).setCellValue(reportDataList.get(i).getJune());
                    newRow.getCell(8).setCellValue(reportDataList.get(i).getJuly());
                    newRow.getCell(9).setCellValue(reportDataList.get(i).getAugust());
                    newRow.getCell(10).setCellValue(reportDataList.get(i).getSeptember());
                    newRow.getCell(11).setCellValue(reportDataList.get(i).getOctober());
                    newRow.getCell(12).setCellValue(reportDataList.get(i).getNovember());
                    newRow.getCell(13).setCellValue(reportDataList.get(i).getDecember());
                    newRow.getCell(14).setCellValue(reportDataList.get(i).getFirstQuarter());
                    newRow.getCell(15).setCellValue(reportDataList.get(i).getTwoQuarter());
                    newRow.getCell(16).setCellValue(reportDataList.get(i).getThreeQuarter());
                    newRow.getCell(17).setCellValue(reportDataList.get(i).getFourQuarter());
                    newRow.getCell(18).setCellValue(reportDataList.get(i).getYear());
//                    count = i + 4 + j.get();
                }
                j.set(j.get() + 15);
            } catch (Exception e) {
                logger.error("导出数据异常" + e.getMessage(), e);
                PlatformResult.failure("导出数据异常" + e.getMessage());
            }
//            if (count != 88) {
//                int rowCount = count + 2;
//                for (int k = 0; k < 67; k++) {
//                    rowCount = rowCount + 1;
//                    sheet.createRow(rowCount).createCell(k).setCellValue("");
//                }
//            }

            try {
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-disposition", "attachment; filename="
                        + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
                OutputStream fos = response.getOutputStream();
                finalWorkbook.write(fos);
                fos.close();
            } catch (Exception e) {
                logger.error("导出数据异常" + e.getMessage(), e);
                PlatformResult.failure("导出数据异常" + e.getMessage());
            }
        }

    }


    public List<List<String>> convertToListOfLists(List<HrmsZpglReportOutListVo> list) {
        List<List<String>> result = new ArrayList<>();
        for (HrmsZpglReportOutListVo reportOutListVo : list) {
            List<String> personAttributes = new ArrayList<>();
            personAttributes.add(reportOutListVo.getName());
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getJanuary()) ? reportOutListVo.getJanuary() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getFebruary()) ? reportOutListVo.getFebruary() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getMarch()) ? reportOutListVo.getMarch() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getApril()) ? reportOutListVo.getApril() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getMay()) ? reportOutListVo.getMay() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getJune()) ? reportOutListVo.getJune() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getJuly()) ? reportOutListVo.getJuly() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getAugust()) ? reportOutListVo.getAugust() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getSeptember()) ? reportOutListVo.getSeptember() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getOctober()) ? reportOutListVo.getOctober() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getNovember()) ? reportOutListVo.getNovember() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getDecember()) ? reportOutListVo.getDecember() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getFirstQuarter()) ? reportOutListVo.getFirstQuarter() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getTwoQuarter()) ? reportOutListVo.getTwoQuarter() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getThreeQuarter()) ? reportOutListVo.getThreeQuarter() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getFourQuarter()) ? reportOutListVo.getFourQuarter() : "0");
            personAttributes.add(StringUtils.isNotEmpty(reportOutListVo.getYear()) ? reportOutListVo.getYear() : "0");
            result.add(personAttributes);
        }
        return result;
    }

    public List<List<String>> convertToList(List<HrmsZpglReportOutListVo> list) {
        List<List<String>> result = new ArrayList<>();
        for (HrmsZpglReportOutListVo reportOutListVo : list) {
            List<String> personAttributes = new ArrayList<>();
            NumberFormat nf = NumberFormat.getPercentInstance();
            personAttributes.add(reportOutListVo.getName());
            personAttributes.add(Objects.nonNull(reportOutListVo.getJanuary()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getJanuary()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getFebruary()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getFebruary()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getMarch()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getMarch()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getApril()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getApril()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getMay()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getMay()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getJune()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getJune()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getJuly()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getJuly()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getAugust()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getAugust()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getSeptember()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getSeptember()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getOctober()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getOctober()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getNovember()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getNovember()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getDecember()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getDecember()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getFirstQuarter()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getFirstQuarter()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getTwoQuarter()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getTwoQuarter()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getThreeQuarter()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getThreeQuarter()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getFourQuarter()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getFourQuarter()) * 100) : "0%");
            personAttributes.add(Objects.nonNull(reportOutListVo.getYear()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getYear()) * 100) : "0%");
            result.add(personAttributes);
        }
        return result;
    }

    public List<HrmsZpglReportOutListVo> getReportAllList(List<String> personnelCategory, String year) {
        List<HrmsZpglReportOutListVo> reportList = zrMappr.getDataSetList(personnelCategory, year, UserInfoHolder.getCurrentUserCorpCode());
        List<HrmsZpglReportOutListVo> dataSetList = zrMappr.getReportIndexList(personnelCategory, year, UserInfoHolder.getCurrentUserCorpCode());
        for (HrmsZpglReportOutListVo reportOutListVo : dataSetList) {
            reportOutListVo.setJanuary(Objects.nonNull(reportOutListVo.getJanuary()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getJanuary()) * 100) : "0%");
            reportOutListVo.setFebruary(Objects.nonNull(reportOutListVo.getFebruary()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getFebruary()) * 100) : "0%");
            reportOutListVo.setMarch(Objects.nonNull(reportOutListVo.getMarch()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getMarch()) * 100) : "0%");
            reportOutListVo.setApril(Objects.nonNull(reportOutListVo.getApril()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getApril()) * 100) : "0%");
            reportOutListVo.setMay(Objects.nonNull(reportOutListVo.getMay()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getMay()) * 100) : "0%");
            reportOutListVo.setJune(Objects.nonNull(reportOutListVo.getJune()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getJune()) * 100) : "0%");
            reportOutListVo.setJuly(Objects.nonNull(reportOutListVo.getJuly()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getJuly()) * 100) : "0%");
            reportOutListVo.setAugust(Objects.nonNull(reportOutListVo.getAugust()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getAugust()) * 100) : "0%");
            reportOutListVo.setSeptember(Objects.nonNull(reportOutListVo.getSeptember()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getSeptember()) * 100) : "0%");
            reportOutListVo.setOctober(Objects.nonNull(reportOutListVo.getOctober()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getOctober()) * 100) : "0%");
            reportOutListVo.setNovember(Objects.nonNull(reportOutListVo.getNovember()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getNovember()) * 100) : "0%");
            reportOutListVo.setDecember(Objects.nonNull(reportOutListVo.getDecember()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getDecember()) * 100) : "0%");
            reportOutListVo.setFirstQuarter(Objects.nonNull(reportOutListVo.getFirstQuarter()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getFirstQuarter()) * 100) : "0%");
            reportOutListVo.setTwoQuarter(Objects.nonNull(reportOutListVo.getTwoQuarter()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getTwoQuarter()) * 100) : "0%");
            reportOutListVo.setThreeQuarter(Objects.nonNull(reportOutListVo.getThreeQuarter()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getThreeQuarter()) * 100) : "0%");
            reportOutListVo.setFourQuarter(Objects.nonNull(reportOutListVo.getFourQuarter()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getFourQuarter()) * 100) : "0%");
            reportOutListVo.setYear(Objects.nonNull(reportOutListVo.getYear()) ? String.format("%.2f%%", Double.parseDouble(reportOutListVo.getYear()) * 100) : "0%");
        }
        reportList.addAll(dataSetList);
        return reportList;
    }
}

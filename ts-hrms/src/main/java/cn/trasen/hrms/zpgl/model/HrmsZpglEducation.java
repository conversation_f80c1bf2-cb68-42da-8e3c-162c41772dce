package cn.trasen.hrms.zpgl.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "hrms_zpgl_education")
@Setter
@Getter
public class HrmsZpglEducation {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;



    /**
     * 主键ID
     */
	@Id
    @ApiModelProperty(value = "主键ID")
    private String id;
	   
	/**
     * 面试者id
     */
    @ApiModelProperty(value = "面试者id")
    private String zpglempid;


    /**
     * 阶段
     */
    @ApiModelProperty(value = "阶段")
    private String jieduan;

    /**
     * 起止时间
     */
    @ApiModelProperty(value = "起止时间")
    private String qizhishijian;

    /**
     * 毕业院校
     */
    @ApiModelProperty(value = "毕业院校")
    private String biyeyuanxiao;

    /**
     * 专业
     */
    @ApiModelProperty(value = "专业")
    private String zhuanye;

    /**
     * 学历
     */
    @ApiModelProperty(value = "学历")
    private String xueli;

    /**
     * 毕业时间
     */
    @ApiModelProperty(value = "毕业时间")
    private String biyeshijian;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String fujian;
    

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;
    
    /**
     *是否全日志
     */
    @Column(name = "full_time")
    @ApiModelProperty(value = "是否全日志")
    private String fullTime;
    

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;
}
package cn.trasen.hrms.zpgl.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrRecruitmentTalentPoolLabelTalentLibraryMapper;
import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPoolLabelTalentLibrary;
import cn.trasen.hrms.zpgl.service.HrTalentLibraryService;
import cn.trasen.hrms.zpgl.vo.inputVo.HrTalentLibraryInputVo;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrTalentLibraryServiceImpl
 * @Description TODO
 * @date 2021年6月19日 下午2:21:17
 */
@Slf4j
@Service
public class HrTalentLibraryServiceImpl implements HrTalentLibraryService {

    @Autowired
    private HrRecruitmentTalentPoolLabelTalentLibraryMapper mapper;

    /**
     * 批量打标签
     *
     * @param talentLibraryInputVo
     * @return
     */
    @Override
    public Integer batchMakeTag(HrTalentLibraryInputVo talentLibraryInputVo) {
        if (CollectionUtils.isEmpty(talentLibraryInputVo.getZpglempid()) || CollectionUtils.isEmpty(talentLibraryInputVo.getPoolLabelId())) {
            throw new BusinessException("参数异常");
        }
        //先删除所有的标签 然后重新添加
        talentLibraryInputVo.getZpglempid().forEach(item->{
        	
        	HrRecruitmentTalentPoolLabelTalentLibrary  del = new HrRecruitmentTalentPoolLabelTalentLibrary ();
        	del.setTalentLibraryId(item);
        	mapper.delete(del);   //先删除
        	
        	talentLibraryInputVo.getPoolLabelId().forEach(i ->{
        		 
        		HrRecruitmentTalentPoolLabelTalentLibrary insertBean = new HrRecruitmentTalentPoolLabelTalentLibrary();
            	 insertBean.setHrRecruitmentTalentPoolLabelId(i);
            	 insertBean.setTalentLibraryId(item);  //人员id
            	 insertBean.setType(1);
            	 insertBean.setCreateDate(new Date());
            	 insertBean.setUpdateDate(new Date());
            	 insertBean.setIsDeleted("N");
            	 insertBean.setId(IdUtil.getId());
                 ThpsUser user = UserInfoHolder.getCurrentUserInfo();
                 if (user != null) {
                	 insertBean.setCreateUser(user.getUsercode());
                	 insertBean.setCreateUserName(user.getUsername());
                 }
				 insertBean.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                  mapper.insertSelective(insertBean);
        	});
        });
        return 1;
    }




    //根据员工id查询标签
	@Override
	public List<HrRecruitmentTalentPoolLabelTalentLibrary> talentLibraryByZpglid(String zpglempid) {
		return mapper.talentLibraryByZpglid(zpglempid);
	}




	@Override
	public Integer delLalentLibraryByZpglid(String id) {
		return mapper.deleteByPrimaryKey(id);
	}
}

package cn.trasen.hrms.zpgl.controller;

import java.util.Collections;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPoolLabelTalentLibrary;
import cn.trasen.hrms.zpgl.model.HrmsZpglAddTalentpool;
import cn.trasen.hrms.zpgl.model.HrmsZpglOperation;
import cn.trasen.hrms.zpgl.service.HrTalentLibraryService;
import cn.trasen.hrms.zpgl.service.HrmsZpglAddTalentpoolService;
import cn.trasen.hrms.zpgl.service.HrmsZpglOperationService;
import cn.trasen.hrms.zpgl.vo.inputVo.HrTalentLibraryInputVo;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglAddTalentpoolOutListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsZpglAddTalentpoolController
 * @Description 加入人才库controller
 * @date 2023��2��16�� ����1:59:23
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglAddTalentpoolController")
public class HrmsZpglAddTalentpoolController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglAddTalentpoolController.class);

	@Autowired
	private HrmsZpglAddTalentpoolService hrmsZpglAddTalentpoolService;
	@Autowired
	private HrTalentLibraryService hrTalentLibraryService;
	
	@Autowired
	HrmsZpglOperationService hrmsZpglOperationService;

	/**
	 * @Title saveHrmsZpglAddTalentpool
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��16�� ����1:59:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/zpglAddTalentpool/save")
	public PlatformResult<String> saveHrmsZpglAddTalentpool(@RequestBody HrmsZpglAddTalentpool record) {
		try {
			hrmsZpglAddTalentpoolService.save(record);
			hrmsZpglOperationService.save( new HrmsZpglOperation(record.getZpglempid(),"加入人才库",""));
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsZpglAddTalentpool
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��16�� ����1:59:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/zpglAddTalentpool/update")
	public PlatformResult<String> updateHrmsZpglAddTalentpool(@RequestBody HrmsZpglAddTalentpool record) {
		try {
			hrmsZpglAddTalentpoolService.update(record);
			hrmsZpglOperationService.save(new HrmsZpglOperation(record.getZpglempid(),"修改人才库"));
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsZpglAddTalentpoolById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsZpglAddTalentpool>
	 * @date 2023��2��16�� ����1:59:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/zpglAddTalentpool/{id}")
	public PlatformResult<HrmsZpglAddTalentpool> selectHrmsZpglAddTalentpoolById(@PathVariable String id) {
		try {
			HrmsZpglAddTalentpool record = hrmsZpglAddTalentpoolService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsZpglAddTalentpoolById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��2��16�� ����1:59:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "人才库删除", notes = "人才库删除")
	@PostMapping("/api/zpglAddTalentpool/delete/{zpglempid}")
	public PlatformResult<String> deleteHrmsZpglAddTalentpoolById(@PathVariable String zpglempid) {
		try {
			hrmsZpglAddTalentpoolService.deleteById(zpglempid);
			hrmsZpglOperationService.save(new HrmsZpglOperation(zpglempid,"人才库移除"));
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsZpglAddTalentpoolList
	 * @Description 人才库查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglAddTalentpool>
	 * @date 2023��2��16�� ����1:59:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglAddTalentpool/list")
	public DataSet<HrmsZpglAddTalentpoolOutListVo> selectHrmsZpglAddTalentpoolList(Page page, HrmsZpglAddTalentpool record) {
		return hrmsZpglAddTalentpoolService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "人才库新增人员", notes = "人才库新增")
	@PostMapping("/api/zpglAddTalentpool/add")
	public PlatformResult<String> addHrmsZpglAddTalentpool(@RequestBody HrmsZpglAddTalentpool record) {
		try {
			hrmsZpglAddTalentpoolService.add(record);
			
			hrmsZpglOperationService.save(new HrmsZpglOperation(record.getZpglempid(),"人才库新增"));
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	@ApiOperation(value = "进入待入职", notes = "进入待入职")
	@PostMapping("/api/zpglAddTalentpool/addEntery/{id}")
	public PlatformResult<String> addEntery(@PathVariable String id) {
		try {
			hrmsZpglAddTalentpoolService.addEntery(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "批量打标签", notes = "批量打标签")
	@PostMapping("/api/zpglAddTalentpool/batchMakeTag")
	public PlatformResult<Integer> batchMakeTag(@RequestBody HrTalentLibraryInputVo talentLibraryInputVose)  {
		return PlatformResult.success(hrTalentLibraryService.batchMakeTag(talentLibraryInputVose));
	}
	
	//根据zpglempid 查询人员标签
	@ApiOperation(value = "根据zpglempid 查询人员标签", notes = "根据zpglempid 查询人员标签")
	@GetMapping("/api/zpglAddTalentpool/talentLibraryByZpglid/{zpglempid}")
	public PlatformResult<List<HrRecruitmentTalentPoolLabelTalentLibrary>> talentLibraryByZpglid(@PathVariable String zpglempid) {
		try {
			return PlatformResult.success(hrTalentLibraryService.talentLibraryByZpglid(zpglempid));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	//删除某个人的标签
	@ApiOperation(value = "根据id删除标签", notes = "根据id删除标签")
	@PostMapping("/api/zpglAddTalentpool/delLalentLibraryByZpglid/{id}")
	public PlatformResult<String> delLalentLibraryByZpglid(@PathVariable String id) {
		try {
			hrTalentLibraryService.delLalentLibraryByZpglid(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	//导出已入职列表导出
    @GetMapping("/api/zpglAddTalentpool/exportAll")
    public void export(Page page, HttpServletRequest request,
            HttpServletResponse response,  HrmsZpglAddTalentpool record) {
    	
    	
    	if("null".equals(record.getSearchStartDate())) {
    		record.setSearchStartDate(null);
    	}
    	if("null".equals(record.getSearchEndDate())) {
    		record.setSearchEndDate(null);
    	}
    	
        // 导出文件名称
        String name = "人才库.xls";
        // 模板位置
        String templateUrl = "template/hrmsZpglTalentpoolList.xls";
        // 导出数据列表
        try {
        		List<HrmsZpglAddTalentpoolOutListVo> hrmsZpglEntryList = hrmsZpglAddTalentpoolService.export(record);
        	if(hrmsZpglEntryList != null && hrmsZpglEntryList.size() >0) {
                ExportUtil.export(request, response, hrmsZpglEntryList, name, templateUrl);
            }else {
            	ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
            }
        } catch (Exception e) {
        	logger.error(e.getMessage(), e);
        }
    }
	
}

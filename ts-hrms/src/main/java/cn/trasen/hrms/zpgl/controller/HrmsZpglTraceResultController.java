package cn.trasen.hrms.zpgl.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrmsZpglTraceResult;
import cn.trasen.hrms.zpgl.service.HrmsZpglTraceResultService;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglTraceResultOutListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsZpglTraceResultController
 * @Description 设置跟踪结果
 * @date 2023��2��17�� ����1:57:17
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglTraceResultController")
public class HrmsZpglTraceResultController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglTraceResultController.class);

	@Autowired
	private HrmsZpglTraceResultService hrmsZpglTraceResultService;

	/**
	 * @Title saveHrmsZpglTraceResult
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��17�� ����1:57:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/zpglTraceResult/save")
	public PlatformResult<String> saveHrmsZpglTraceResult(@RequestBody HrmsZpglTraceResult record) {
		try {
			hrmsZpglTraceResultService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsZpglTraceResult
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��17�� ����1:57:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/zpglTraceResult/update")
	public PlatformResult<String> updateHrmsZpglTraceResult(@RequestBody HrmsZpglTraceResult record) {
		try {
			hrmsZpglTraceResultService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsZpglTraceResultById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsZpglTraceResult>
	 * @date 2023��2��17�� ����1:57:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/zpglTraceResult/{id}")
	public PlatformResult<HrmsZpglTraceResult> selectHrmsZpglTraceResultById(@PathVariable String id) {
		try {
			HrmsZpglTraceResult record = hrmsZpglTraceResultService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "查询某个人的跟踪记录", notes = "查询某个人的跟踪记录")
	@GetMapping("/api/zpglTraceResult/getListByName/{zpglempid}")
	public PlatformResult<List<HrmsZpglTraceResult>> getListByName(@PathVariable String zpglempid) {
		try {
			List<HrmsZpglTraceResult> record = hrmsZpglTraceResultService.getListByName(zpglempid);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsZpglTraceResultById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��2��17�� ����1:57:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/zpglTraceResult/delete/{id}")
	public PlatformResult<String> deleteHrmsZpglTraceResultById(@PathVariable String id) {
		try {
			hrmsZpglTraceResultService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsZpglTraceResultList
	 * @Description 所有跟踪查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglTraceResult>
	 * @date 2023��2��17�� ����1:57:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglTraceResult/allList")
	public DataSet<HrmsZpglTraceResult> selectHrmsZpglTraceResultList(Page page, HrmsZpglTraceResult record) {
		return hrmsZpglTraceResultService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglTraceResult/getPageList")
	public DataSet<HrmsZpglTraceResultOutListVo> getPageList(Page page, HrmsZpglTraceResult record) {
		return hrmsZpglTraceResultService.getPageList(page, record);
	}
}

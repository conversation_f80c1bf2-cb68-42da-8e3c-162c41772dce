package cn.trasen.hrms.zpgl.controller;

import java.util.Collections;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrmsZpglInterviewMessage;
import cn.trasen.hrms.zpgl.service.HrmsZpglInterviewMessageService;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglInterviewMessageResultOutListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsZpglInterviewMessageController
 * @Description TODO
 * @date 2023��2��9�� ����4:20:25
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglInterviewMessageController")
public class HrmsZpglInterviewMessageController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglInterviewMessageController.class);

	@Autowired
	private HrmsZpglInterviewMessageService hrmsZpglInterviewMessageService;

	/**
	 * @Title saveHrmsZpglInterviewMessage
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��9�� ����4:20:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/zpglInterviewMessage/save")
	public PlatformResult<String> saveHrmsZpglInterviewMessage(@RequestBody HrmsZpglInterviewMessage record) {
		try {
			hrmsZpglInterviewMessageService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "新增批量处理", notes = "新增")
	@PostMapping("/api/zpglInterviewMessage/batchSave")
	public PlatformResult<String> batchSave(@RequestBody HrmsZpglInterviewMessage record) {
		try {
			hrmsZpglInterviewMessageService.batchSave(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsZpglInterviewMessage
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��9�� ����4:20:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/zpglInterviewMessage/update")
	public PlatformResult<String> updateHrmsZpglInterviewMessage(@RequestBody HrmsZpglInterviewMessage record) {
		try {
			hrmsZpglInterviewMessageService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "签到", notes = "签到")
	@PostMapping("/api/zpglInterviewMessage/signIn")
	public PlatformResult<String> signIn(@RequestBody HrmsZpglInterviewMessage record) {
		try {
			hrmsZpglInterviewMessageService.signIn(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsZpglInterviewMessageById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsZpglInterviewMessage>
	 * @date 2023��2��9�� ����4:20:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/zpglInterviewMessage/{messageId}")
	public PlatformResult<HrmsZpglInterviewMessage> selectHrmsZpglInterviewMessageById(@PathVariable String messageId) {
		try {
			HrmsZpglInterviewMessage record = hrmsZpglInterviewMessageService.selectById(messageId);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsZpglInterviewMessageById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��2��9�� ����4:20:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/zpglInterviewMessage/delete/{id}")
	public PlatformResult<String> deleteHrmsZpglInterviewMessageById(@PathVariable String id) {
		try {
			hrmsZpglInterviewMessageService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsZpglInterviewMessageList
	 * @Description 查询列表（面试管理 四个列表）
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglInterviewMessage>
	 * @date 2023��2��9�� ����4:20:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglInterviewMessage/list")
	public DataSet<HrmsZpglInterviewMessageResultOutListVo> getHrmsZpglInterviewMessageList(Page page, HrmsZpglInterviewMessage record) {
		return hrmsZpglInterviewMessageService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "入职管理列表", notes = "入职管理列表")
	@GetMapping("/api/zpglInterviewMessage/entryList")
	public DataSet<HrmsZpglInterviewMessageResultOutListVo>getHrmsZpglEntryList(Page page, HrmsZpglInterviewMessage record) {
		return hrmsZpglInterviewMessageService.getHrmsZpglEntryList(page, record);
	}
	
	//导出已入职列表导出
    @GetMapping("/api/zpglInterviewMessage/exportEntryList")
    public void export(Page page, HttpServletRequest request,
            HttpServletResponse response, HrmsZpglInterviewMessage record) {
        // 导出文件名称
        String name = "已入职人员.xls";
        // 模板位置
        String templateUrl = "template/hrmsZpglEntryList.xls";
        // 导出数据列表
        try {
        		List<HrmsZpglInterviewMessageResultOutListVo> hrmsZpglEntryList = hrmsZpglInterviewMessageService.export(record);
        	if(hrmsZpglEntryList != null && hrmsZpglEntryList.size() >0) {
                ExportUtil.export(request, response, hrmsZpglEntryList, name, templateUrl);
            }else {
            	ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
            }
        } catch (Exception e) {
        	logger.error(e.getMessage(), e);
        }
    }
}

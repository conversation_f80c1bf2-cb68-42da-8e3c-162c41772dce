package cn.trasen.hrms.zpgl.enums;

import lombok.Getter;

/**   
 * @Title: EmployeeStatusEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 面试者状态枚举
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月17日 下午6:06:08 
 * @version V1.0   
 */
@Getter
public enum ZpglEmployeeStatusEnum {
	
	ZPGL_EMPLOYEE_STATUS_1("1", "面试登记"),
	
	ZPGL_EMPLOYEE_STATUS_2("2", "面试通过"),
	
	ZPGL_EMPLOYEE_STATUS_3("3", "待入职"),
	
	ZPGL_EMPLOYEE_STATUS_4("4", "已入职");  // 等于存档
	
	private final String key;
	private final String val;

	private ZpglEmployeeStatusEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (ZpglEmployeeStatusEnum item : ZpglEmployeeStatusEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return "";
	}
}

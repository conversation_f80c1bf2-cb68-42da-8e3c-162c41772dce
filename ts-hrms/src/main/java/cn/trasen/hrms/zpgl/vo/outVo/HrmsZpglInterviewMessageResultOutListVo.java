package cn.trasen.hrms.zpgl.vo.outVo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/** 
* @ClassName: HrmsZpglInterviewMessageOutListVo 
* @Description: 面试管理列表OUTVO
* <AUTHOR>  
* @date 2023年2月16日 下午5:07:32 
*  
*/
@Setter
@Getter
public class HrmsZpglInterviewMessageResultOutListVo {
	
	 /**
     * id
     */
    @ApiModelProperty(value = "zpglempid")
    private String zpglempid;  // 人员id

    @ApiModelProperty(value = "面试信息id")
    private String messageId;
    
    @ApiModelProperty(value = "面试结果id")
    private String resultId;

    
    private String employeeName;  //姓名
    
	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别")
	private String gender;


	private String zpglEmployeeStatus;

	private String zpglEmployeeStatusText;  //状态中文

	private String addInterview;
	
	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别文本")
	private String gendertext;
	
	@ApiModelProperty(value = "籍贯")
	private String birthplace; 
	
    
    /**
     * 是否符合 1:符合 2不符合
     */
    @ApiModelProperty(value = "是否符合 1:符合 2不符合")
    private String conform;

    /**
     * 面试科室
     */
    @ApiModelProperty(value = "面试科室")
    private String interviewDept;

    /**
     * 面试岗位
     */
    @ApiModelProperty(value = "面试岗位")
    private String interviewJob;
    

    /**
     * 面试岗位
     */
    @ApiModelProperty(value = "面试岗位")
    private String interviewJobtext;
    
    
    @ApiModelProperty(value = "面试岗位名称")
    private String applicantPosttext;
    
	@ApiModelProperty(value = "签到")
	private String  signIn;  //签到

	@ApiModelProperty(value = "签到")
	private String  signInText;  //签到



	@ApiModelProperty(value = "更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
	private Date updateDate;

	private String workUnit;   //工作单位





    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    
    

	
	@ApiModelProperty(value = "联系方式")
	private String iphone;

	/**
	 * 参加工作时间
	 */
	@ApiModelProperty(value = "参加工作时间")
	private String entryDate;
	
	/**
	 * 工作年限
	 */
	@ApiModelProperty(value = "工作年限")
	private String entryDateText;
	
	@ApiModelProperty(value = "职称名称")
	private String zhichengmingcheng;// 职称名称
	
	@ApiModelProperty(value = "职称名称文本")
	private String zhichengmingchengtext;// 职称名称文本
	
	@ApiModelProperty(value = "职称专业")
	private String zhuenye ;  //职称专业
	
	@ApiModelProperty(value = "学历")
	private String xueli; //学历
	
	@ApiModelProperty(value = "学历文本")
	private String xuelitext;  //学历文本
	
	@ApiModelProperty(value = "毕业学校")
	private String biyeyuanxiao;  //毕业学校
	
	private String addTalentPool;  //1 加入了人才库

	@ApiModelProperty(value = "拟定岗位（面试通过  不通过 待定 岗位）")
	private String studyoutJob;  //拟定岗位（234）
	
	@ApiModelProperty(value = "拟定岗位名称 （面试通过  不通过 待定 岗位）")
	private String studyoutJobtext;  //拟定岗位名称 （234）
	
	private String writtenDate;  //面试开始时间
	
    /**
     * 面试时间
     */
    @ApiModelProperty(value = "面试时间（面试结束时间）")
    private String interviewDate;
	
	private String email; //邮箱

	private String interviewDepttext; //面试科室名称
	private String studyoutDept; //拟定科室
	private String studyoutDepttext; //拟定科室名称
	
	private String no; //用于排序
	private String age; //年龄
	private String comeEntryDate;  //入职时间
	
	@ApiModelProperty(value = "简历全路径")
	private String interviewPath ; 
	@ApiModelProperty(value = "面试信息全路径")
	private String msgInterviewPath ; 
	@ApiModelProperty(value = "面试结果全路径")
	private String resInterviewPath ; 
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signInDate;

	private String interviewResultStatus;
	private String interviewResultStatusText;

	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date createDate;


}

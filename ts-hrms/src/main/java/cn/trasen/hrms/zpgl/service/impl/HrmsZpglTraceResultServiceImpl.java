package cn.trasen.hrms.zpgl.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.google.common.collect.Maps;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrmsZpglTraceResultMapper;
import cn.trasen.hrms.zpgl.model.HrmsZpglEmployee;
import cn.trasen.hrms.zpgl.model.HrmsZpglTraceNexttime;
import cn.trasen.hrms.zpgl.model.HrmsZpglTraceResult;
import cn.trasen.hrms.zpgl.model.HrmsZpglTraceSetting;
import cn.trasen.hrms.zpgl.service.HrmsZpglEmployeeService;
import cn.trasen.hrms.zpgl.service.HrmsZpglTraceNexttimeService;
import cn.trasen.hrms.zpgl.service.HrmsZpglTraceResultService;
import cn.trasen.hrms.zpgl.service.HrmsZpglTraceSettingService;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglTraceResultOutListVo;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsZpglTraceResultServiceImpl
 * @Description TODO
 * @date 2023��2��17�� ����1:57:17
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsZpglTraceResultServiceImpl implements HrmsZpglTraceResultService {

	@Autowired
	private HrmsZpglTraceResultMapper mapper;
	
	
    @Autowired
    DictItemFeignService dictItemFeignService;
	
	@Autowired
	HrmsZpglTraceSettingService hrmsZpglTraceSettingService;
	@Autowired
	HrmsZpglEmployeeService hrmsZpglEmployeeService;
	@Autowired
	HrmsZpglTraceNexttimeService hrmsZpglTraceNexttimeService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsZpglTraceResult record) {
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
		}
		//若为2  结束跟踪 
		
		if("2".equals(record.getTraceStatus())) {
			HrmsZpglEmployee bean = new HrmsZpglEmployee();
			bean.setId(record.getZpglempid());
			bean.setTraseType("1");
			hrmsZpglEmployeeService.updateBasic(bean);
		}
		
		//下次跟踪日期不为空 添加下次跟踪记录
		if(!StringUtil.isEmpty(record.getNextTime())) {
			HrmsZpglTraceNexttime hn = new HrmsZpglTraceNexttime();
			hn.setNextTime(record.getNextTime());
			hn.setZpglempid(record.getZpglempid());
			hrmsZpglTraceNexttimeService.save(hn);
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsZpglTraceResult record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsZpglTraceResult record = new HrmsZpglTraceResult();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsZpglTraceResult selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsZpglTraceResult> getDataSetList(Page page, HrmsZpglTraceResult record) {
		
		page.setPageSize(Integer.MAX_VALUE);
		//所有跟踪规则
		List<HrmsZpglTraceSetting> settinListAll = hrmsZpglTraceSettingService.getListAll();
		List<Map<String,Integer>> listMap = new ArrayList<>();
		if (settinListAll != null && settinListAll.size() > 0) {
			
			for (int i = 0; i < settinListAll.size(); i++) {
				Map<String,Integer> map = new HashMap<String, Integer>();
				map.put("startDay", settinListAll.get(i).getStartDay());
				map.put("endDay", settinListAll.get(i).getEndDay());
				listMap.add(map);
			}
			record.setListMap(listMap);
		}
		//如果没有规则 查询所有人员
		List<HrmsZpglTraceResult> records = mapper.getAllList(page, record);
		
		if (records != null && records.size() > 0) {
			for (int i = 0; i < records.size(); i++) {
				if("1".equals(records.get(i).getConform())) {
					records.get(i).setEmployeeStatusText("初选合格");
				}
				if("1".equals(records.get(i).getInterviewResultStatus())) {
					records.get(i).setEmployeeStatusText("面试通过");
				}
				if("3".equals(records.get(i).getInterviewResultStatus())) {
					records.get(i).setEmployeeStatusText("待定");
				}
				if("3".equals(records.get(i).getZpglEmployeeStatus())) {
					records.get(i).setEmployeeStatusText("待入职");
				}
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsZpglTraceResult> getListByName(String id) {
		Example example = new Example(HrmsZpglTraceResult.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("zpglempid", id);
		return mapper.selectByExample(example);
	}

	/**
	 * 跟踪列表
	 */
	@Override
	public DataSet<HrmsZpglTraceResultOutListVo> getPageList(Page page, HrmsZpglTraceResult record) {
		page.setSidx("t6.create_date");
		page.setSord("desc");
		if(StringUtil.isEmpty(record.getSearchType())) {
			record.setSearchType("1");
		}
		if("1".equals(record.getSearchType())) {
			List<HrmsZpglTraceSetting> settinListAll = hrmsZpglTraceSettingService.getListAll();
			List<Map<String,Integer>> listMap = new ArrayList<>();
			if (settinListAll != null && settinListAll.size() > 0) {
				for (HrmsZpglTraceSetting hrmsZpglTraceSetting : settinListAll) {
					Map<String, Integer> map = new HashMap<String, Integer>();
					map.put("startDay", hrmsZpglTraceSetting.getStartDay());
					map.put("endDay", hrmsZpglTraceSetting.getEndDay());
					listMap.add(map);
				}
				record.setListMap(listMap);
			}
		}
		//根据当前登录账号机构编码过滤查询数据
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		//学历
		Map<String, String> educationConvertDictMap = convertDictMap("education_type");
		List<HrmsZpglTraceResultOutListVo>  records = mapper.getPageList(page,record);
		if (records != null && records.size() > 0) {
			for (HrmsZpglTraceResultOutListVo hrmsZpglTraceResultOutListVo : records) {
				if ("1".equals(hrmsZpglTraceResultOutListVo.getConform())) {
					hrmsZpglTraceResultOutListVo.setEmployeeStatusText("初选合格");
				}
				if ("1".equals(hrmsZpglTraceResultOutListVo.getInterviewResultStatus())) {
					hrmsZpglTraceResultOutListVo.setEmployeeStatusText("面试通过");
				}
				if ("3".equals(hrmsZpglTraceResultOutListVo.getInterviewResultStatus())) {
					hrmsZpglTraceResultOutListVo.setEmployeeStatusText("待定");
				}
				if ("3".equals(hrmsZpglTraceResultOutListVo.getZpglEmployeeStatus())) {
					hrmsZpglTraceResultOutListVo.setEmployeeStatusText("待入职");
				}
				hrmsZpglTraceResultOutListVo.setXuelitext(educationConvertDictMap.get(hrmsZpglTraceResultOutListVo.getXueli()));
				//处理工作年限
				if (!StringUtil.isEmpty(hrmsZpglTraceResultOutListVo.getEntryDate())) {
					try {
						String yearCompare = DateUtils.yearCompare(DateUtils.getStringToDate(hrmsZpglTraceResultOutListVo.getEntryDate()), new Date());
						hrmsZpglTraceResultOutListVo.setEntryDateText(yearCompare + "年");
					} catch (Exception e) {
						log.error("工作年限转换异常");
					}
				}
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }
}

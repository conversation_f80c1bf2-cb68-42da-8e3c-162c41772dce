package cn.trasen.hrms.zpgl.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 人才库
 *
 */
@Table(name = "hrms_zpgl_add_talentpool")
@Setter
@Getter
public class HrmsZpglAddTalentpool {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
	@Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 面试者id
     */
    @ApiModelProperty(value = "面试者id")
    private String zpglempid;
    
    @ApiModelProperty(value = "加入人才库位置")
    @Column(name = "add_type")
    private String addType;
    

    /**
     * 应聘岗位id
     */
    @Column(name = "applicant_post")
    @ApiModelProperty(value = "应聘岗位id")
    private String applicantPost;

    /**
     * 应聘岗位名称
     */
    @Column(name = "applicant_posttext")
    @ApiModelProperty(value = "应聘岗位名称")
    private String applicantPosttext;

    /**
     * 应聘科室id
     */
    @Column(name = "applicant_dept")
    @ApiModelProperty(value = "应聘科室id")
    private String applicantDept;

    /**
     * 应聘科室名称
     */
    @Column(name = "applicant_depttext")
    @ApiModelProperty(value = "应聘科室名称")
    private String applicantDepttext;

    /**
     * 归属人才库科室id
     */
    @Column(name = "affiliation_dept")
    @ApiModelProperty(value = "归属人才库科室id")
    private String affiliationDept;

    /**
     * 归属人才库科室名称
     */
    @Column(name = "affiliation_depttext")
    @ApiModelProperty(value = "归属人才库科室名称")
    private String affiliationDepttext;

    /**
     * 归属人才库职称id
     */
    @Column(name = "affiliation_job")
    @ApiModelProperty(value = "归属人才库职称id")
    private String affiliationJob;

    /**
     * 归属人才库职称名称
     */
    @Column(name = "affiliation_jobtext")
    @ApiModelProperty(value = "归属人才库职称名称")
    private String affiliationJobtext;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否")
    private String isDeleted;
    
    @Column(name = "interview_pool")
    @ApiModelProperty(value = "加入人才库时候的路径")
    private String interviewPool;
    
    
    //查询参数
    @Transient
    private String treeLevel;  //查询级别
    @Transient
    private String employeeName; // 姓名 (模糊查)
    @Transient
    private String searchStartDate; //   开始时间
    @Transient
    private String searchEndDate; //   结束时间
    @Transient
    private String zhichengmingcheng; //  职称（模糊查）
    @Transient
    private String gender; //  性别 （下拉）
    @Transient
    private String searchEntryDate; //  工作年限（模糊查）
    @Transient
    private String poolLabel; // 标签
    @Transient
	@ApiModelProperty(value = "联系方式")
	private String iphone;
    @Transient
    private String  interviewJob;  //岗位
    @Transient
    private String allPath;
}
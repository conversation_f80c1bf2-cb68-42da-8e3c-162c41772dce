package cn.trasen.hrms.zpgl.enums;

import lombok.Getter;

/**   
 * @Title: EmployeeStatusEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 面试状态
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月17日 下午6:06:08 
 * @version V1.0   
 */
@Getter
public enum ZpglInterviewStatusEnum {
	
	ZPGL_INTERVIEW_STATUS_1("1", "待面试"),
	
	ZPGL_INTERVIEW_STATUS_2("2", "面试通过"),

	ZPGL_INTERVIEW_STATUS_3("3", "面试不通过"),

	ZPGL_INTERVIEW_STATUS_4("4", "待定");
	
	
	private final String key;
	private final String val;

	private ZpglInterviewStatusEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (ZpglInterviewStatusEnum item : ZpglInterviewStatusEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return "";
	}
}

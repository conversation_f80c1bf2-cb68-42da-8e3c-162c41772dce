package cn.trasen.hrms.zpgl.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrmsZpglWorkrecord;
import cn.trasen.hrms.zpgl.service.HrmsZpglWorkrecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsZpglWorkrecordController
 * @Description TODO
 * @date 2023��2��8�� ����4:11:35
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglWorkrecordController")
public class HrmsZpglWorkrecordController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglWorkrecordController.class);

	@Autowired
	private HrmsZpglWorkrecordService hrmsZpglWorkrecordService;

	/**
	 * @Title saveHrmsZpglWorkrecord
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:11:35
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/zpglWorkrecord/save")
	public PlatformResult<String> saveHrmsZpglWorkrecord(@RequestBody HrmsZpglWorkrecord record) {
		try {
			hrmsZpglWorkrecordService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsZpglWorkrecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:11:35
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/zpglWorkrecord/update")
	public PlatformResult<String> updateHrmsZpglWorkrecord(@RequestBody HrmsZpglWorkrecord record) {
		try {
			hrmsZpglWorkrecordService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsZpglWorkrecordById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsZpglWorkrecord>
	 * @date 2023��2��8�� ����4:11:35
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/zpglWorkrecord/{id}")
	public PlatformResult<HrmsZpglWorkrecord> selectHrmsZpglWorkrecordById(@PathVariable String id) {
		try {
			HrmsZpglWorkrecord record = hrmsZpglWorkrecordService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsZpglWorkrecordById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:11:35
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/zpglWorkrecord/delete/{id}")
	public PlatformResult<String> deleteHrmsZpglWorkrecordById(@PathVariable String id) {
		try {
			hrmsZpglWorkrecordService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsZpglWorkrecordList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglWorkrecord>
	 * @date 2023��2��8�� ����4:11:35
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglWorkrecord/list")
	public DataSet<HrmsZpglWorkrecord> selectHrmsZpglWorkrecordList(Page page, HrmsZpglWorkrecord record) {
		return hrmsZpglWorkrecordService.getDataSetList(page, record);
	}
}

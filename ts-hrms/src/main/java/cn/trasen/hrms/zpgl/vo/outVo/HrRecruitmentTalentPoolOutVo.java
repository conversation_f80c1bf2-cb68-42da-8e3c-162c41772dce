package cn.trasen.hrms.zpgl.vo.outVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

@Setter
@Getter
public class HrRecruitmentTalentPoolOutVo {

    @ApiModelProperty(value = "招聘-人才库主键")
    private String hrRecruitmentTalentPoolId;

    @ApiModelProperty(value = "招聘-人才库编号")
    private String talentPoolNo;

    @ApiModelProperty(value = "招聘-人才库名称")
    private String talentPoolName;

    @ApiModelProperty(value = "招聘-上级机构人才库编号")
    private String parentTalentPoolNo;

    @ApiModelProperty(value = "招聘-上级机构人才库id")
    private String parentId;

    @ApiModelProperty(value = "招聘-上级机构人才库名称")
    private String parentName;

    @ApiModelProperty(value = "状态（N停用Y启用）")
    private String status;

    @ApiModelProperty(value = "状态）")
    private String statusValue;

    @JsonFormat(pattern="yyyy-MM-dd hh:mm:ss",timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "子节点信息")
    private List<HrRecruitmentTalentPoolOutVo> children;
  
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "全路径")
    private String fullpath;
    
    @ApiModelProperty(value = "序号")
    private String seqNo;
    
    
    

}
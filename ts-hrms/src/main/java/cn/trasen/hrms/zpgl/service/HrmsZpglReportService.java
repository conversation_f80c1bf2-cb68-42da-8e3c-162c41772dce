package cn.trasen.hrms.zpgl.service;

import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.vo.outVo.ReportOutVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface HrmsZpglReportService {

    PlatformResult<Map<String, ReportOutVo>> getReportDataList(List<String> personnelCategory, String year);

    PlatformResult<List<List<String>>> getReportIndexList(List<String> personnelCategory,String year);

    void reportDataExort(HttpServletRequest request, HttpServletResponse response, List<String> personnelCategory, String year);
}

package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglTraceResult;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglTraceResultOutListVo;

/**
 * @ClassName HrmsZpglTraceResultService
 * @Description TODO
 * @date 2023��2��17�� ����1:57:17
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglTraceResultService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��17�� ����1:57:17
	 * <AUTHOR>
	 */
	Integer save(HrmsZpglTraceResult record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��17�� ����1:57:17
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglTraceResult record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��17�� ����1:57:17
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglTraceResult
	 * @date 2023��2��17�� ����1:57:17
	 * <AUTHOR>
	 */
	HrmsZpglTraceResult selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglTraceResult>
	 * @date 2023��2��17�� ����1:57:17
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglTraceResult> getDataSetList(Page page, HrmsZpglTraceResult record);

	/** 
	* @Title: getListByName 
	* @Description: 人员跟踪记录接口
	* @param @param id
	* @param @return    设定文件 
	* @return List<HrmsZpglTraceResult>    返回类型 
	* @throws 
	*/
	List<HrmsZpglTraceResult> getListByName(String id);

	/** 
	* @Title: getPageList 
	* @Description: 跟踪列表
	* @param @param page
	* @param @param record
	* @param @return    设定文件 
	* @return DataSet<HrmsZpglTraceResult>    返回类型 
	* @throws 
	*/
	DataSet<HrmsZpglTraceResultOutListVo> getPageList(Page page, HrmsZpglTraceResult record);
}

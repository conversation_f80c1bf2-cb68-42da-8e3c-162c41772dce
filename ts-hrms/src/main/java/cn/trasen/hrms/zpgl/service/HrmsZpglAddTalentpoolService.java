package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglAddTalentpool;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglAddTalentpoolOutListVo;

/**
 * @ClassName HrmsZpglAddTalentpoolService
 * @Description TODO
 * @date 2023��2��16�� ����1:59:23
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglAddTalentpoolService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��16�� ����1:59:23
	 * <AUTHOR>
	 */
	Integer save(HrmsZpglAddTalentpool record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��16�� ����1:59:23
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglAddTalentpool record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��16�� ����1:59:23
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglAddTalentpool
	 * @date 2023��2��16�� ����1:59:23
	 * <AUTHOR>
	 */
	HrmsZpglAddTalentpool selectById(String id);
	
	HrmsZpglAddTalentpool selectByEmpId(String zpglempid);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglAddTalentpool>
	 * @date 2023��2��16�� ����1:59:23
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglAddTalentpoolOutListVo> getDataSetList(Page page, HrmsZpglAddTalentpool record);

	Integer add(HrmsZpglAddTalentpool record);


	/** 
	* @Title: addEntery 
	* @Description: 进入带入职 
	* @param @param zpglempid
	* @param @return    设定文件 
	* @return Integer    返回类型 
	* @throws 
	*/
	Integer addEntery(String zpglempid);

	/** 
	* @Title: export 
	* @Description: 人才库人员导出 
	* @param @param record
	* @param @return    设定文件 
	* @return List<HrmsZpglAddTalentpoolOutListVo>    返回类型 
	* @throws 
	*/
	List<HrmsZpglAddTalentpoolOutListVo> export(HrmsZpglAddTalentpool record);

	/** 
	* @Title: getListByAffiliationJob 
	* @Description: 根据岗位查询人才库人员 
	* @param @param string
	* @param @return    设定文件 
	* @return List<HrmsZpglAddTalentpool>    返回类型 
	* @throws 
	*/
	List<HrmsZpglAddTalentpool> getListByAffiliationJob(String string);

}

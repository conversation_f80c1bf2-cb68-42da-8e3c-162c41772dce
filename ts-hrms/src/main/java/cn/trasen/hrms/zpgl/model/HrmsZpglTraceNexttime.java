package cn.trasen.hrms.zpgl.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_zpgl_trace_nexttime")
@Setter
@Getter
public class HrmsZpglTraceNexttime {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * id
     */
    @Id
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 被跟踪人
     */
    @ApiModelProperty(value = "被跟踪人")
    private String zpglempid;

    /**
     * 下次跟踪时间
     */
    @Column(name = "next_time")
    @ApiModelProperty(value = "下次跟踪时间")
    private String nextTime;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateUserName;

    /**
     * 删除标识(N存在，Y删除)
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识(N存在，Y删除)")
    private String isDeleted;
}

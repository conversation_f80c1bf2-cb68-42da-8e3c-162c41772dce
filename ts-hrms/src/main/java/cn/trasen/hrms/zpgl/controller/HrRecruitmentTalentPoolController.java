package cn.trasen.hrms.zpgl.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPool;
import cn.trasen.hrms.zpgl.service.HrRecruitmentTalentPoolService;
import cn.trasen.hrms.zpgl.vo.inputVo.HrRecruitmentTalentPoolInputVo;
import cn.trasen.hrms.zpgl.vo.inputVo.HrRecruitmentTalentPoolListInputVo;
import cn.trasen.hrms.zpgl.vo.outVo.HrRecruitmentTalentPoolOutVo;
import cn.trasen.hrms.zpgl.vo.outVo.HrRecruitmentTalentPoolTreeOutVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@RestController
@Api(tags = "招聘管理-人才库-人才库类型")
public class HrRecruitmentTalentPoolController {

    private transient static final Logger logger = LoggerFactory.getLogger(HrRecruitmentTalentPoolController.class);

    @Autowired
    private HrRecruitmentTalentPoolService hrRecruitmentTalentPoolService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveHrRecruitmentTalentPool
     * @Description 新增
     * @date 2022��8��5�� ����11:35:15
     * <AUTHOR>
     */
    @ApiOperation(value = "保存、编辑", notes = "保存、编辑")
    @PostMapping("/api/HrRecruitmentTalentPool/saveOrUpdate")
    public PlatformResult<Integer> saveOrUpdate(@RequestBody HrRecruitmentTalentPoolInputVo record) {
        return PlatformResult.success(hrRecruitmentTalentPoolService.saveOrUpdate(record));
    }


    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/HrRecruitmentTalentPool/{id}")
    public PlatformResult<HrRecruitmentTalentPool> getOne(@PathVariable String id) {
        return PlatformResult.success(hrRecruitmentTalentPoolService.selectById(id));
    }

    @ApiOperation(value = "启用禁用", notes = "启用禁用")
    @PostMapping("/api/HrRecruitmentTalentPool/{id}/{status}")
    public PlatformResult<Integer> getOne(@PathVariable @ApiParam("主键") String id,
                                                          @PathVariable @ApiParam("状态（N停用Y启用）") String status) {
        return PlatformResult.success(hrRecruitmentTalentPoolService.enableDisable(id,status));
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteHrRecruitmentTalentPoolById
     * @Description 根据ID删除
     * @date 2022��8��5�� ����11:35:15
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/HrRecruitmentTalentPool/delete/{id}")
    public PlatformResult<String> deleteHrRecruitmentTalentPoolById(@PathVariable String id) {
        try {
            hrRecruitmentTalentPoolService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
    /**
     * @param page
     * @param recruitmentTalentPoolListInputVo
     * @return DataSet<HrRecruitmentTalentPool>
     * @Title selectHrRecruitmentTalentPoolList
     * @Description 查询列表
     * @date 2022��8��5�� ����11:35:15
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/HrRecruitmentTalentPool/list")
    public DataSet<HrRecruitmentTalentPoolOutVo> selectHrRecruitmentTalentPoolList(Page page, HrRecruitmentTalentPoolListInputVo recruitmentTalentPoolListInputVo) {
        List<HrRecruitmentTalentPoolOutVo> dataSetList = hrRecruitmentTalentPoolService.getDataSetList(page, recruitmentTalentPoolListInputVo);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), dataSetList);
    }

    @ApiOperation(value = "树结构", notes = "树结构")
    @GetMapping("/api/HrRecruitmentTalentPool/tree")
    public PlatformResult<List<HrRecruitmentTalentPoolTreeOutVo>> getTree(HrRecruitmentTalentPoolListInputVo recruitmentTalentPoolListInputVo) {
        return PlatformResult.success(hrRecruitmentTalentPoolService.getTree(recruitmentTalentPoolListInputVo));
    }
    
    @ApiOperation(value = "树结构", notes = "树结构一二级")
    @GetMapping("/api/HrRecruitmentTalentPool/treeSecondLevel")
    public PlatformResult<List<HrRecruitmentTalentPoolTreeOutVo>> treeSecondLevel(HrRecruitmentTalentPoolListInputVo recruitmentTalentPoolListInputVo) {
        return PlatformResult.success(hrRecruitmentTalentPoolService.treeSecondLevel(recruitmentTalentPoolListInputVo));
    }
    
    @ApiOperation(value = "树结构", notes = "树结构单第三级")
    @GetMapping("/api/HrRecruitmentTalentPool/treeThreeLevel")
    public PlatformResult<List<HrRecruitmentTalentPoolTreeOutVo>> treeThreeLevel(HrRecruitmentTalentPoolListInputVo recruitmentTalentPoolListInputVo) {
        return PlatformResult.success(hrRecruitmentTalentPoolService.treeThreeLevel(recruitmentTalentPoolListInputVo));
    }
}
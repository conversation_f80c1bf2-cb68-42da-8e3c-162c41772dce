package cn.trasen.hrms.zpgl.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;


//人员标签中间表
@Table(name = "hr_recruitment_talent_pool_label_talent_library")
@Setter
@Getter
public class HrRecruitmentTalentPoolLabelTalentLibrary {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;


    @Id
    @ApiModelProperty(value = "数据id")
    private String id;

	
    /**
     * 人才库id
     */
    @Column(name = "talent_library_id")
    @ApiModelProperty(value = "人才库id")
    private String talentLibraryId;

    /**
     * 人才库标签id
     */
    @Column(name = "hr_recruitment_talent_pool_label_id")
    @ApiModelProperty(value = "人才库标签id")
    private String hrRecruitmentTalentPoolLabelId;


    /**
     * 人才库标签类型 1、自动打标签2、手动
     */
    @ApiModelProperty(value = "1、自动打标签2、手动")
    private Integer type;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateUserName;

    /**
     * 删除标识(N存在，Y删除)
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识(N存在，Y删除)")
    private String isDeleted;
    
    @Transient
    private String zpglempid;
    @Transient
    private String labelName;  //标签名称
    @Transient
    private String labelId;  //标签名称
    
}
package cn.trasen.hrms.zpgl.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;


/** 
* @ClassName: HrmsZpglTraceResult 
* @Description: 跟踪记录
* <AUTHOR>  
* @date 2023年2月20日 下午5:56:34 
*  
*/
@Table(name = "hrms_zpgl_trace_result")
@Setter
@Getter
public class HrmsZpglTraceResult {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * id
     */
	@Id
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 被跟踪人
     */
    @ApiModelProperty(value = "被跟踪人")
    private String zpglempid;

    /**
     * 1继续跟踪 2 结束跟踪
     */
    @Column(name = "trace_status")
    @ApiModelProperty(value = "1继续跟踪 2 结束跟踪")
    private String traceStatus;

    /**
     * 跟踪结果
     */
    @Column(name = "trace_result")
    @ApiModelProperty(value = "跟踪结果")
    private String traceResult;
    

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateUserName;

    /**
     * 删除标识(N存在，Y删除)
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识(N存在，Y删除)")
    private String isDeleted;
    
    @Transient
    private String employeeName;  //姓名
    
  
    @Transient
	@ApiModelProperty(value = "性别")
	private String gender;
	
    @Transient
	@ApiModelProperty(value = "性别文本")
	private String gendertext;
    
    @Transient
    @ApiModelProperty(value = "拟定岗位")
    private String studyoutJob;
  
    @Transient
    @ApiModelProperty(value = "拟定岗位名称")
    private String studyoutJobtext;
  
    @Transient
    private String iphone;  //电话
    
    @Transient
    private List<Map<String,Integer>> listMap;
   
    @Transient
    private String  searchType; //1待跟踪 2 最近三十天  3 全部
   
    @Transient
    private String zpglEmployeeStatus;
    @Transient
    private String conform;
    @Transient
    private String interviewResultStatus;
    @Transient
    private String  employeeStatusText;  //显示的人员状态
    
    @Transient
    private String interviewPath;
    @Transient
    private String zhichengmingcheng;
    @Transient
    private String searchStartDate;
    @Transient
    private String searchEndDate;
    @Transient
    private String numberCount;
    
    @Transient
    @ApiModelProperty(value = "下次跟踪时间")
    private String  nextTime;
}
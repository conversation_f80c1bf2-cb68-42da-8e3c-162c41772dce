package cn.trasen.hrms.zpgl.dao;

import cn.trasen.hrms.zpgl.model.HrmsZpglInterviewResult;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglReportOutListVo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface HrmsZpglInterviewResultMapper extends Mapper<HrmsZpglInterviewResult> {

    List<HrmsZpglReportOutListVo> getDataSetList(@Param("personnelCategory") List<String> personnelCategory, @Param("year") String year, @Param("ssoOrgCode") String ssoOrgCode);

    List<HrmsZpglReportOutListVo> getReportIndexList(@Param("personnelCategory") List<String> personnelCategory, @Param("year") String year, @Param("ssoOrgCode") String ssoOrgCode);
}
package cn.trasen.hrms.zpgl.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "hrms_zpgl_family")
@Setter
@Getter
public class HrmsZpglFamily {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;


    /**
     * 主键ID
     */
	@Id
    @ApiModelProperty(value = "主键ID")
    private String id;
	
   
	/**
     * 面试者id
     */
    @ApiModelProperty(value = "面试者id")
    private String zpglempid;


    /**
     * 关系
     */
    @ApiModelProperty(value = "关系")
    private String guanxi;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String xingming;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private String chushengriqi;

    /**
     * 工作单位
     */
    @ApiModelProperty(value = "工作单位")
    private String gongzuodanwei;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String phone;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;
}
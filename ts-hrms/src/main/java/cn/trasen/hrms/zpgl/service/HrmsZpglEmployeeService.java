package cn.trasen.hrms.zpgl.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglEmployee;
import cn.trasen.hrms.zpgl.vo.outVo.EmpDetailsOutVo;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglEmployeeOutListVo;

/**
 * @ClassName HrmsZpglEmployeeService
 * @Description TODO
 * @date 2023��2��8�� ����3:18:46
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglEmployeeService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����3:18:46
	 * <AUTHOR>
	 */
	Integer save(HrmsZpglEmployee record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����3:18:46
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglEmployee record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��8�� ����3:18:46
	 * <AUTHOR>
	 */
	Integer deleteById(String id,String type);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglEmployee
	 * @date 2023��2��8�� ����3:18:46
	 * <AUTHOR>
	 */
	HrmsZpglEmployee selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglEmployee>
	 * @date 2023��2��8�� ����3:18:46
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglEmployeeOutListVo> getDataSetList(Page page, HrmsZpglEmployee record);
	
	/** 
	* @Title: updateBasic 
	* @Description: 修改主表 
	* @param @param record
	* @param @return    设定文件 
	* @return Integer    返回类型 
	* @throws 
	*/
	Integer updateBasic(HrmsZpglEmployee record);

	/** 
	* @Title: findByIdentityNumber 
	* @Description: 根据省份证查询
	* @param @param identityNumber
	* @param @return    设定文件 
	* @return HrmsZpglEmployee    返回类型 
	* @throws 
	*/
	HrmsZpglEmployee findByIdentityNumber(String identityNumber);

	/**
	 * 根据身份证查询所有信息
	 * @param identityNumber
	 * @return
	 */
	HrmsZpglEmployee findIdentityNumberAllData(String identityNumber);


	DataSet<HrmsZpglEmployeeOutListVo> getDsaList(Page page, HrmsZpglEmployee record);

	//返回所有节点信息系
	EmpDetailsOutVo findLinkZpglempId(String zpglempid);
}

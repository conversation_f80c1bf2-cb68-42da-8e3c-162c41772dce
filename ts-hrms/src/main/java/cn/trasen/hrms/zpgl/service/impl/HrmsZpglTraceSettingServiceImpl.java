package cn.trasen.hrms.zpgl.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.model.HrmsHireManagement;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrmsZpglTraceSettingMapper;
import cn.trasen.hrms.zpgl.model.HrmsZpglTraceSetting;
import cn.trasen.hrms.zpgl.service.HrmsZpglTraceSettingService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsZpglTraceSettingServiceImpl
 * @Description 设置功能实现类
 * @date 2023��2��9�� ����3:28:45
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsZpglTraceSettingServiceImpl implements HrmsZpglTraceSettingService {

	@Autowired
	private HrmsZpglTraceSettingMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(List<HrmsZpglTraceSetting> record) {
		
		try {
			Example example = new Example(HrmsHireManagement.class);
			example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
			mapper.deleteByExample(example);
			if(null != record) {
				ThpsUser user = UserInfoHolder.getCurrentUserInfo();
				record.forEach(item ->{
					item.setId(IdUtil.getId());
					item.setCreateDate(new Date());
					item.setIsDeleted("N");
					if (user != null) {
						item.setCreateUser(user.getUsercode());
						item.setCreateUserName(user.getUsername());
						//根据当前登录账号机构编码过滤
						item.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
					}
					 mapper.insertSelective(item);
				});
			}
		} catch (Exception e) {
			throw new BusinessException("新增异常"+e.getMessage());
		}
		return 1;
	}
	

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsZpglTraceSetting record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsZpglTraceSetting record = new HrmsZpglTraceSetting();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsZpglTraceSetting selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsZpglTraceSetting> getDataSetList(Page page, HrmsZpglTraceSetting record) {
		Example example = new Example(HrmsZpglTraceSetting.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsZpglTraceSetting> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsZpglTraceSetting> getListAll() {
		Example example = new Example(HrmsZpglTraceSetting.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		//根据当前登录账号机构编码过滤
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsZpglTraceSetting> records = mapper.selectByExample(example);
		return records;
	}
}

package cn.trasen.hrms.zpgl.vo.outVo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName: HrmsZpglEmployeeOutVo
 * @Description: 简历列表
 * <AUTHOR>
 * @date 2023年2月15日 上午10:02:56
 * 
 */
@Setter
@Getter
public class HrmsZpglEmployeeOutListVo {

	
	private String zpglempid; 
	
	private String resultId;
	
	private String messageId;

	/**
	 * 应聘岗位
	 */
	@ApiModelProperty(value = "应聘岗位")
	private String applicantPost;
	
	@ApiModelProperty(value = "应聘岗位文字")
	private String applicantPosttext;

	@ApiModelProperty(value = "签到")
	private String  signIn;  //签到

	@ApiModelProperty(value = "签到")
	private String  signInText;  //签到

	/**
	 * 姓名
	 */
	@ApiModelProperty(value = "姓名")
	private String employeeName;

	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别")
	private String gender;
	
	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别文本")
	private String gendertext;

	/**
	 * 年龄
	 */
	@ApiModelProperty(value = "年龄")
	private String age;

	/**
	 * 身份证
	 */
	@ApiModelProperty(value = "身份证")
	private String identityNumber;

	/**
	 * 联系方式
	 */
	@ApiModelProperty(value = "联系方式")
	private String iphone;

	/**
	 * 参加工作时间
	 */
	@ApiModelProperty(value = "参加工作时间")
	private String entryDate;
	
	/**
	 * 工作年限
	 */
	@ApiModelProperty(value = "工作年限")
	private String entryDateText;
	
	
	@ApiModelProperty(value = "籍贯")
	private String birthplace; 

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date createDate;
	
	@ApiModelProperty(value = "职称名称")
	private String zhichengmingcheng;// 职称名称
	
	@ApiModelProperty(value = "职称名称文本")
	private String zhichengmingchengtext;// 职称名称文本
	
	@ApiModelProperty(value = "职称专业")
	private String zhuenye ;  //职称专业
	
	@ApiModelProperty(value = "学历")
	private String xueli; //学历
	
	@ApiModelProperty(value = "学历文本")
	private String xuelitext;  //学历文本
	
	@ApiModelProperty(value = "毕业学校")
	private String biyeyuanxiao;  //毕业学校
	
	private String addTalentPool;  //1 加入了人才库
	
	private String addInterview;  //1 已设置面试信息
	
	@ApiModelProperty(value = "应聘科室")
	private String applicantDept;
	
	@ApiModelProperty(value = "应聘科室")
	private String applicantDeptname;
	@ApiModelProperty(value = "1符合 2不符合")
	private String conform ;  //1符合 2不符合
	
	@ApiModelProperty(value = "简历全路径")
	private String interviewPath ; 
	@ApiModelProperty(value = "面试信息全路径")
	private String msgInterviewPath ; 
	@ApiModelProperty(value = "面试结果全路径")
	private String resInterviewPath ; 
	
	private String interviewResultStatus;  //面试结果
	
	private String zpglEmployeeStatus;

	private String zpglEmployeeStatusText;  //状态中文

	private String avatar;

	@ApiModelProperty(value = "更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
	private Date updateDate;

	private String workUnit;   //工作单位


}

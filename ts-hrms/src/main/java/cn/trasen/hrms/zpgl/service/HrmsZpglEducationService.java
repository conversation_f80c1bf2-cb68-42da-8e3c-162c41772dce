package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglEducation;

/**
 * @ClassName HrmsZpglEducationService
 * @Description TODO
 * @date 2023��2��8�� ����4:10:23
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglEducationService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����4:10:23
	 * <AUTHOR>
	 */
	Integer save(HrmsZpglEducation record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��8�� ����4:10:23
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglEducation record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��8�� ����4:10:23
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglEducation
	 * @date 2023��2��8�� ����4:10:23
	 * <AUTHOR>
	 */
	HrmsZpglEducation selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglEducation>
	 * @date 2023��2��8�� ����4:10:23
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglEducation> getDataSetList(Page page, HrmsZpglEducation record);

	Integer deleteByEmpId(String employeeId);

	List<HrmsZpglEducation> findByEmpId(String employeeId);
}

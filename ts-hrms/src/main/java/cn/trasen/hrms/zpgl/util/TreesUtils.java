package cn.trasen.hrms.zpgl.util;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;

import org.apache.commons.beanutils.PropertyUtils;

import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.StringUtil;

/**
 * List集合转树状结构List
 *
 * <AUTHOR>
 * @date: 2021/7/7 17:50
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
public class TreesUtils {

    /**
     * List集合转树状结构List
     *
     * @param rawDataList          所必要条件，入参集合泛型对象需具有属性名为id、parentId的String类型属性;
     *                             children的List类型属性（泛型类型为T）
     * @param pidPropertyName      实体类当前节点上级id属性名(首字母大写)
     * @param idPropertyName       实体类当前节点id属性名(首字母大写)
     * @param countPropertyName    实体类统计数量属性名(首字母大写)
     * @param childrenPropertyName 实体类子节点数据List集合属性名(首字母大写)
     * @param <T>
     * @return
     */
    public static <T> List<T> listToTree(List<T> rawDataList, String pidPropertyName, String idPropertyName, String countPropertyName, String childrenPropertyName) {
        // 获取对象所有字段名
        List<T> trees = Lists.newArrayList();
        for (T t : rawDataList) {
            try {
                if (null == (t.getClass().getMethod("get" + pidPropertyName).invoke(t))) {
                    // 填充当前节点具有所有数据量（包含所有下级节点）
                    if (!StringUtil.isEmpty(countPropertyName)) {
                        PropertyUtils.setSimpleProperty(
                                t,
                                countPropertyName.toLowerCase(),
                                statisticalQllSubNodeCounts(
                                        rawDataList,
                                        t.getClass().getMethod("get" + idPropertyName).invoke(t) + "",
                                        pidPropertyName,
                                        idPropertyName,
                                        countPropertyName,
                                        Integer.parseInt(t.getClass().getMethod("get" + countPropertyName).invoke(t) + "")
                                ));
                    }
                    trees.add(findChildren(t, rawDataList, pidPropertyName, idPropertyName, countPropertyName, childrenPropertyName));
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }

        }
        return trees;
    }

    /**
     * 递归查找子节点
     *
     * @return
     */
    private static <T> T findChildren(T t, List<T> rawDataList, String pidPropertyName, String idPropertyName, String countPropertyName, String childrenPropertyName) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException, NoSuchFieldException {
        for (T e : rawDataList) {
            Class<?> tClass = t.getClass();
            if ((tClass.getMethod("get" + idPropertyName).invoke(t) + "")
                    .equals((e.getClass().getMethod("get" + pidPropertyName).invoke(e) + ""))) {
                if (null == tClass.getMethod("get" + childrenPropertyName).invoke(t)) {
                    Field children = tClass.getDeclaredField(childrenPropertyName.toLowerCase());
                    children.setAccessible(true);
                    Method setChildren = tClass.getMethod("set" + childrenPropertyName, children.getType());
                    setChildren.invoke(t, Lists.newArrayList());
                }
                List<T> children = (List<T>) tClass.getMethod("get" + childrenPropertyName).invoke(t);
                // 填充当前节点具有所有数据量（包含所有下级节点）
                if (!StringUtil.isEmpty(countPropertyName)) {
                    PropertyUtils.setSimpleProperty(
                            e,
                            countPropertyName.toLowerCase(),
                            statisticalQllSubNodeCounts(
                                    rawDataList,
                                    e.getClass().getMethod("get" + idPropertyName).invoke(e) + "",
                                    pidPropertyName,
                                    idPropertyName,
                                    countPropertyName,
                                    Integer.parseInt(e.getClass().getMethod("get" + countPropertyName).invoke(e) + "")
                            ));
                }
                children.add((findChildren(e, rawDataList, pidPropertyName, idPropertyName, countPropertyName, childrenPropertyName)));
            }
        }
        return t;
    }

    /**
     * 查询节点下所有子节点信息
     *
     * @param allList       所有节点信息
     * @param pid           节点id
     * @param childList     返回所有子节点信息
     * @param pidMethodName 实体类父节点属性get方法名
     * @param idMethodName  实体类子节点属性get方法名
     * @param <T>
     */
    public static <T> void queryQllSubNodes(List<T> allList, String pid, List<T> childList, String pidMethodName, String idMethodName) {
        allList.forEach(temp -> {
            //遍历出父id等于参数的id，add进子节点集合
            try {
                if (pid.equals(temp.getClass().getMethod(pidMethodName).invoke(temp) + "")) {
                    //递归遍历下一级
                    childList.add(temp);
                    queryQllSubNodes(allList, temp.getClass().getMethod(idMethodName).invoke(temp) + "", childList, pidMethodName, idMethodName);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            }
        });
    }


    public static <T> void queryQllSubNodesG(List<TreeModel> allList, String pid, List<TreeModel> childList) {
        allList.forEach(temp -> {
            //遍历出父id等于参数的id，add进子节点集合
//            try {
                if (pid.equals(temp.getPid())) {
                    //递归遍历下一级
                    childList.add(temp);
                    queryQllSubNodesG(allList, temp.getId() , childList);
                }else{
                    if(CollectionUtil.isNotEmpty(temp.getChildren())){
                        queryQllSubNodesG(temp.getChildren(), pid , childList);
                    }
                }
//            } catch (IllegalAccessException e) {
//                e.printStackTrace();
//            } catch (InvocationTargetException e) {
//                e.printStackTrace();
//            } catch (NoSuchMethodException e) {
//                e.printStackTrace();
//            }
        });
    }





    /**
     * @param allList           所有节点信息
     * @param pid               当前节点id
     * @param pidPropertyName   实体类当前节点上级id属性名(首字母大写)
     * @param idPropertyName    实体类当前节点id属性名(首字母大写)
     * @param countPropertyName 实体类统计数量属性名(首字母大写)
     * @param index             当前节点数量
     * @param <T>
     * @return
     */
    public static <T> int statisticalQllSubNodeCounts(List<T> allList, String pid, String pidPropertyName, String idPropertyName, String countPropertyName, int index) {
        for (T temp : allList) {
            //遍历出父id等于参数的id，add进子节点集合
            try {
                if (pid.equals(temp.getClass().getMethod("get" + pidPropertyName).invoke(temp) + "")) {
                    //递归遍历下一级
                    index += Integer.parseInt(temp.getClass().getMethod("get" + countPropertyName).invoke(temp) + "");
                    index = statisticalQllSubNodeCounts(
                            allList,
                            temp.getClass().getMethod("get" + idPropertyName).invoke(temp) + "",
                            pidPropertyName,
                            idPropertyName,
                            countPropertyName,
                            index
                    );
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            }
        }
        return index;
    }
}

package cn.trasen.hrms.zpgl.dao;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.zpgl.model.HrmsZpglAddTalentpool;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglAddTalentpoolOutListVo;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsZpglAddTalentpoolMapper extends Mapper<HrmsZpglAddTalentpool> {

	List<HrmsZpglAddTalentpoolOutListVo> getPageList(Page page, HrmsZpglAddTalentpool record);
}
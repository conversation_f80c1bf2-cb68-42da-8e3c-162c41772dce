package cn.trasen.hrms.zpgl.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrmsZpglContinueLearningMapper;
import cn.trasen.hrms.zpgl.model.HrmsZpglContinueLearning;
import cn.trasen.hrms.zpgl.service.HrmsZpglContinueLearningService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsZpglContinueLearningServiceImpl
 * @Description TODO
 * @date 2023��2��8�� ����4:13:03
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsZpglContinueLearningServiceImpl implements HrmsZpglContinueLearningService {

	@Autowired
	private HrmsZpglContinueLearningMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsZpglContinueLearning record) {
		record.setId(IdUtil.getId());
		record.setCreateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsZpglContinueLearning record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsZpglContinueLearning record = new HrmsZpglContinueLearning();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsZpglContinueLearning selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsZpglContinueLearning> getDataSetList(Page page, HrmsZpglContinueLearning record) {
		Example example = new Example(HrmsZpglContinueLearning.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsZpglContinueLearning> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public Integer deleteByEmpId(String employeeId) {
		Assert.hasText(employeeId, "ID不能为空.");
		HrmsZpglContinueLearning record = new HrmsZpglContinueLearning();
		record.setZpglempid(employeeId);
		return mapper.delete(record);
	}

	@Override
	public List<HrmsZpglContinueLearning> findByEmpId(String employeeId) {
		Example example = new Example(HrmsZpglContinueLearning.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("zpglempid", employeeId);
		return mapper.selectByExample(example);
	}
}

package cn.trasen.hrms.zpgl.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrmsZpglContinueLearning;
import cn.trasen.hrms.zpgl.service.HrmsZpglContinueLearningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsZpglContinueLearningController
 * @Description TODO
 * @date 2023��2��8�� ����4:13:03
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglContinueLearningController")
public class HrmsZpglContinueLearningController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglContinueLearningController.class);

	@Autowired
	private HrmsZpglContinueLearningService hrmsZpglContinueLearningService;

	/**
	 * @Title saveHrmsZpglContinueLearning
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:13:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/zpglContinueLearning/save")
	public PlatformResult<String> saveHrmsZpglContinueLearning(@RequestBody HrmsZpglContinueLearning record) {
		try {
			hrmsZpglContinueLearningService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsZpglContinueLearning
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:13:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/zpglContinueLearning/update")
	public PlatformResult<String> updateHrmsZpglContinueLearning(@RequestBody HrmsZpglContinueLearning record) {
		try {
			hrmsZpglContinueLearningService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsZpglContinueLearningById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsZpglContinueLearning>
	 * @date 2023��2��8�� ����4:13:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/zpglContinueLearning/{id}")
	public PlatformResult<HrmsZpglContinueLearning> selectHrmsZpglContinueLearningById(@PathVariable String id) {
		try {
			HrmsZpglContinueLearning record = hrmsZpglContinueLearningService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsZpglContinueLearningById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:13:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/zpglContinueLearning/delete/{id}")
	public PlatformResult<String> deleteHrmsZpglContinueLearningById(@PathVariable String id) {
		try {
			hrmsZpglContinueLearningService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsZpglContinueLearningList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglContinueLearning>
	 * @date 2023��2��8�� ����4:13:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglContinueLearning/list")
	public DataSet<HrmsZpglContinueLearning> selectHrmsZpglContinueLearningList(Page page, HrmsZpglContinueLearning record) {
		return hrmsZpglContinueLearningService.getDataSetList(page, record);
	}
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.delivery.dao.MedDeliveryDictMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.delivery.model.MedDeliveryDict">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="connotation" jdbcType="VARCHAR" property="connotation" />
    <result column="purpose" jdbcType="VARCHAR" property="purpose" />
    <result column="sample_type" jdbcType="VARCHAR" property="sampleType" />
    <result column="sample_dose" jdbcType="VARCHAR" property="sampleDose" />
    <result column="test_org" jdbcType="VARCHAR" property="testOrg" />
    <result column="test_mean" jdbcType="VARCHAR" property="testMean" />
    <result column="cost" jdbcType="VARCHAR" property="cost" />
    <result column="fee_item" jdbcType="VARCHAR" property="feeItem" />
    <result column="fee_code" jdbcType="VARCHAR" property="feeCode" />
    <result column="fee_details" jdbcType="VARCHAR" property="feeDetails" />
    <result column="fee_price" jdbcType="VARCHAR" property="feePrice" />
    <result column="price_judge" jdbcType="VARCHAR" property="priceJudge" />
    <result column="exc_dept" jdbcType="VARCHAR" property="excDept" />
    <result column="exc_dept_m" jdbcType="VARCHAR" property="excDeptM" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="dict_type" jdbcType="VARCHAR" property="dictType" />
    <result column="create_date" jdbcType="VARCHAR" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
  </resultMap>
</mapper>
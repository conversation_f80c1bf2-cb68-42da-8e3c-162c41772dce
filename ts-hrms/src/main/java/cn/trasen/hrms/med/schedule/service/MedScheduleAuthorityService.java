package cn.trasen.hrms.med.schedule.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.hrms.med.schedule.model.MedScheduleAuthority;
import cn.trasen.hrms.med.schedule.model.MedScheduleRecord;
import cn.trasen.hrms.med.schedule.model.ScheduleDept;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;

/**
 * @ClassName MedScheduleAuthorityService
 * @Description TODO
 * @date 2025��3��29�� ����2:58:34
 * <AUTHOR>
 * @version 1.0
 */
public interface MedScheduleAuthorityService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��3��29�� ����2:58:34
	 * <AUTHOR>
	 */
	Integer save(MedScheduleAuthority record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��3��29�� ����2:58:34
	 * <AUTHOR>
	 */
	Integer update(MedScheduleAuthority record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��3��29�� ����2:58:34
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedScheduleAuthority
	 * @date 2025��3��29�� ����2:58:34
	 * <AUTHOR>
	 */
	MedScheduleAuthority selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleAuthority>
	 * @date 2025��3��29�� ����2:58:34
	 * <AUTHOR>
	 */
	DataSet<MedScheduleAuthority> getDataSetList(Page page, MedScheduleAuthority record);

	DataSet<ScheduleEmployee> getScheduleRecordList(Page page,ScheduleEmployee record, List<ScheduleEmployee> records);

	List<TreeModel> getScheduleZTree(ScheduleEmployee record);

	List<ScheduleEmployee> getScheduleManageEmployeeList(ScheduleEmployee record);
	
	public List<MedScheduleRecord> getScheduleRecordLists(ScheduleEmployee record);
	
	public boolean setQueryData(ScheduleEmployee record);

}

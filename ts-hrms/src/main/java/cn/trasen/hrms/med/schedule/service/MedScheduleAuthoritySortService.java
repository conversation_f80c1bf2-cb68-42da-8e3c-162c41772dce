package cn.trasen.hrms.med.schedule.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.schedule.model.MedScheduleAuthoritySort;

/**
 * @ClassName MedScheduleAuthoritySortService
 * @Description TODO
 * @date 2025��3��31�� ����2:30:24
 * <AUTHOR>
 * @version 1.0
 */
public interface MedScheduleAuthoritySortService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��3��31�� ����2:30:24
	 * <AUTHOR>
	 */
	Integer save(MedScheduleAuthoritySort record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��3��31�� ����2:30:24
	 * <AUTHOR>
	 */
	Integer update(MedScheduleAuthoritySort record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��3��31�� ����2:30:24
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedScheduleAuthoritySort
	 * @date 2025��3��31�� ����2:30:24
	 * <AUTHOR>
	 */
	MedScheduleAuthoritySort selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleAuthoritySort>
	 * @date 2025��3��31�� ����2:30:24
	 * <AUTHOR>
	 */
	DataSet<MedScheduleAuthoritySort> getDataSetList(Page page, MedScheduleAuthoritySort record);
}

package cn.trasen.hrms.med.wheelScience.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.wheelScience.model.MedWheelScience;
import cn.trasen.hrms.med.wheelScience.service.MedWheelScienceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedWheelScienceController
 * @Description TODO
 * @date 2025��1��22�� ����2:57:52
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "医师轮科")
public class MedWheelScienceController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedWheelScienceController.class);

	@Autowired
	private MedWheelScienceService medWheelScienceService;

	/**
	 * @Title saveMedWheelScience
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��22�� ����2:57:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/wheelScience/save")
	public PlatformResult<String> saveMedWheelScience(@RequestBody MedWheelScience record) {
		try {
			medWheelScienceService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedWheelScience
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��22�� ����2:57:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/wheelScience/update")
	public PlatformResult<String> updateMedWheelScience(@RequestBody MedWheelScience record) {
		try {
			medWheelScienceService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedWheelScienceById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedWheelScience>
	 * @date 2025��1��22�� ����2:57:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/wheelScience/{id}")
	public PlatformResult<MedWheelScience> selectMedWheelScienceById(@PathVariable String id) {
		try {
			MedWheelScience record = medWheelScienceService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedWheelScienceById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��1��22�� ����2:57:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/wheelScience/delete/{id}")
	public PlatformResult<String> deleteMedWheelScienceById(@PathVariable String id) {
		try {
			medWheelScienceService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedWheelScienceList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedWheelScience>
	 * @date 2025��1��22�� ����2:57:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/wheelScience/list")
	public DataSet<MedWheelScience> selectMedWheelScienceList(Page page, MedWheelScience record) {
		return medWheelScienceService.getDataSetList(page, record);
	}
}

package cn.trasen.hrms.med.patient.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.patient.dao.PatientOperationMapper;
import cn.trasen.hrms.med.patient.model.PatientOperation;
import cn.trasen.hrms.med.patient.model.PatientOrderrecord;
import cn.trasen.hrms.med.patient.service.PatientOperationService;
import cn.trasen.hrms.utils.HnsrmyyHisJdbcUtil;
import lombok.extern.log4j.Log4j2;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PatientOperationServiceImpl
 * @Description TODO
 * @date 2025��4��8�� ����4:38:18
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Log4j2
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PatientOperationServiceImpl implements PatientOperationService {

	@Autowired
	private PatientOperationMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(PatientOperation record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(PatientOperation record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		PatientOperation record = new PatientOperation();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public PatientOperation selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<PatientOperation> getDataSetList(Page page, PatientOperation record) {
		Example example = new Example(PatientOperation.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<PatientOperation> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public void updateOrSavePatientOperation() {
		// TODO Auto-generated method stub
				try {    		
					StringBuilder sb = new StringBuilder();
					
		    		sb.append(" SELECT  A.ID,A.INPATIENT_ID AS PATN_ID,A.INPATIENT_NO AS PATN_NO,A.YSSSRQ AS OPERATION_DATE,A.YSSS AS OPERATION_NAME,A.SHZD AS OPERATION_AFTER_DIAGNOSIS,A.ZDYS,A.MZYS,A.BDELETE,B.WCBJ,B.WCSJ,B.QKLX,B.YXSSDJ,A.APBJ  FROM  ODSZYV10.SS_APPRECORD A LEFT  JOIN ODSZYV10.SS_ARRRECORD  B ON A.SNO=B.SNO  "
		    				+ " ");
		    		sb.append("   WHERE (A.YSSSRQ  >    TRUNC(SYSDATE -1)  OR B.WCSJ   >    TRUNC(SYSDATE -1))   "
		    				+ " ");
		    		
					//sb.append("select  id as csltAppyId,appy_No as appyNo,patn_Name as patnName from  med_cslt_appy  where id = ？  ");
		    		//List<CsltAppySyncHis> CsltAppyList = 	HnsrmyyHisJdbcUtil.query(sb.toString(),CsltAppySyncHis.class);//执行语句返回结果,反射映射有问题
		    		log.info("===========sql:"+sb.toString());
		    		List<PatientOperation> PatientOperationList = HnsrmyyHisJdbcUtil.queryPatientOperationSyncHis(sb.toString());//执行语句返回结果
		    		log.info("===========PatientOperationList:"+PatientOperationList.size());
		    		if(CollUtil.isNotEmpty(PatientOperationList)){
		    			for(PatientOperation patientOperation : PatientOperationList) {
		    				PatientOperation record = mapper.selectByPrimaryKey(patientOperation.getId());//根据患者id查询是否已经存在患者数据，存在则更新;
		    				if(record != null) {
		    					patientOperation.setUpdateDate(DateUtil.date());
		    					mapper.updateByPrimaryKeySelective(patientOperation);
		    				}else {
		    					patientOperation.setIsDeleted("N");
		    					patientOperation.setCreateDate(DateUtil.date());
		    					patientOperation.setUpdateDate(DateUtil.date());
		    					mapper.insertSelective(patientOperation);
		    				}
		    			}
		    		}
				}catch(Exception e) {
		    		e.printStackTrace();
		    		log.error("获取影子库手术信息数据异常：" + e.getMessage());
		    	}
	}
}

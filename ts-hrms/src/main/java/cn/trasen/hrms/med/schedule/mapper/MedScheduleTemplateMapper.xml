<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.schedule.dao.MedScheduleTemplateMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.schedule.model.MedScheduleTemplate">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="classes_id" jdbcType="VARCHAR" property="classesId" />
    <result column="schedule_week" jdbcType="VARCHAR" property="scheduleWeek" />
    <result column="type_id" jdbcType="VARCHAR" property="typeId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="emp_org_id" jdbcType="VARCHAR" property="empOrgId" />
    <result column="create_date" jdbcType="VARCHAR" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
  </resultMap>
  
  	<insert id="batchInsert">

		INSERT INTO med_schedule_template
		(
			id,
			schedule_week,
			employee_id,
			type_id,
			classes_id,
			emp_org_id,
			create_date,
			create_user,
			create_user_name,
			is_deleted,
			status,
			remark,
			sso_org_code
		)
		VALUES
		<foreach collection="list" item="item" index="index" separator=",">
			(
				#{item.id},
				#{item.scheduleWeek},
				#{item.employeeId},
				#{item.typeId},
				#{item.classesId},
				#{item.empOrgId},
				#{item.createDate},
				#{item.createUser},
				#{item.createUserName},
				#{item.isDeleted},
				#{item.status},
				#{item.remark},
				#{item.ssoOrgCode}
			)
		</foreach>
	</insert>
	
	<update id="deleteMedScheduleTemplateRecord" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee">
		UPDATE med_schedule_template t1
		LEFT JOIN cust_emp_base t2 on t1.employee_id = t2.employee_id
		SET t1.is_deleted = 'Y'
		where t1.create_user = #{currentUserCode}
		
		<if test="employeeIds != null and employeeIds.size() > 0">
        	 and t1.employee_id in
	        <foreach collection="employeeIds" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
      	</if>
	</update>
	
	<select id="selectMedScheduleTemplateRecordList" resultType="cn.trasen.hrms.med.schedule.model.MedScheduleRecord" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee">
  		select t1.*,t2.classes_name,t2.classes_worktime,t2.classes_color,ifnull(t2.classes_hours,0) as classesHours, '5' type
  		 from med_schedule_template t1
		left join med_schedule_classes t2 on t1.classes_id = t2.id
  		where t1.is_deleted = 'N' and t2.classes_status='1' and t1.create_user = #{currentUserCode}
  		<if test="employeeId != null and employeeId != ''">
  			and t1.employee_id = #{employeeId}
  		</if>
  </select>
	
	<select id="selectTemplateExtendByEmp" resultType="cn.trasen.hrms.med.schedule.model.MedScheduleTemplateExtend" parameterType="String">
  		select t1.*
  		 from med_schedule_template_extend t1
  		where t1.employee_no = #{employeeNo}
  			and t1.sso_org_code = #{ssoOrCode}
  </select>
</mapper>
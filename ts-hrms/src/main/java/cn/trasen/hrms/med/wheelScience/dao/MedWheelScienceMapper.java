package cn.trasen.hrms.med.wheelScience.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.med.wheelScience.model.MedWheelScience;
import tk.mybatis.mapper.common.Mapper;

public interface MedWheelScienceMapper extends Mapper<MedWheelScience> {

	List<MedWheelScience> getDataSetList(MedWheelScience record, Page page);

	void updateEmpOrgInfo(@Param("docCode")String docCode, @Param("wheelFinishDept")String wheelFinishDept);

	List<String> selectWheelData(@Param("ssoOrgCode")String ssoOrgCode);

	List<String> selectWheelData();

	Long selectMyWheelSize(@Param("currentUserCode")String currentUserCode);

}
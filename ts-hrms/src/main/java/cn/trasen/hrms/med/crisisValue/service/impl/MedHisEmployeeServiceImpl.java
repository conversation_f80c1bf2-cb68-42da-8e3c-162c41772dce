package cn.trasen.hrms.med.crisisValue.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.crisisValue.dao.MedHisEmployeeMapper;
import cn.trasen.hrms.med.crisisValue.model.MedHisEmployee;
import cn.trasen.hrms.med.crisisValue.service.MedHisEmployeeService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedHisEmployeeServiceImpl
 * @Description TODO
 * @date 2025��5��26�� ����4:18:09
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedHisEmployeeServiceImpl implements MedHisEmployeeService {

	@Autowired
	private MedHisEmployeeMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedHisEmployee record) {
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedHisEmployee record) {
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedHisEmployee record = new MedHisEmployee();
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedHisEmployee selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedHisEmployee> getDataSetList(Page page, MedHisEmployee record) {
		Example example = new Example(MedHisEmployee.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<MedHisEmployee> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public MedHisEmployee selectHisEmployee(String currentUserCode,String hisEmployeeId) {
		return mapper.selectHisEmployee(currentUserCode,hisEmployeeId);
	}

	@Override
	public String selectPlatformOrgType() {
		return mapper.selectPlatformOrgType();
	}
	
	
}

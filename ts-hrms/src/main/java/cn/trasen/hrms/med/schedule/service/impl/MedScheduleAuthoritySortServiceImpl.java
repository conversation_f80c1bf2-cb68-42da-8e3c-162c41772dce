package cn.trasen.hrms.med.schedule.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.schedule.dao.MedScheduleAuthoritySortMapper;
import cn.trasen.hrms.med.schedule.model.MedScheduleAuthoritySort;
import cn.trasen.hrms.med.schedule.service.MedScheduleAuthoritySortService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedScheduleAuthoritySortServiceImpl
 * @Description TODO
 * @date 2025��3��31�� ����2:30:24
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedScheduleAuthoritySortServiceImpl implements MedScheduleAuthoritySortService {

	@Autowired
	private MedScheduleAuthoritySortMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedScheduleAuthoritySort record) {
		record.setId(IdGeneraterUtils.nextId());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedScheduleAuthoritySort record) {
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedScheduleAuthoritySort record = new MedScheduleAuthoritySort();
		record.setId(id);
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedScheduleAuthoritySort selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedScheduleAuthoritySort> getDataSetList(Page page, MedScheduleAuthoritySort record) {
		Example example = new Example(MedScheduleAuthoritySort.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<MedScheduleAuthoritySort> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

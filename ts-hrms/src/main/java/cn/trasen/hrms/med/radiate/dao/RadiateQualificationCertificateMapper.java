package cn.trasen.hrms.med.radiate.dao;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.med.radiate.model.RadiateQualificationCertificate;
import cn.trasen.hrms.med.radiate.vo.RadiateCertificateReqVo;
import tk.mybatis.mapper.common.Mapper;

public interface RadiateQualificationCertificateMapper extends Mapper<RadiateQualificationCertificate> {
	
	List<RadiateQualificationCertificate> selectRadiateQualificationCertificatePagelist(Page page, RadiateCertificateReqVo record);
	
}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.crisisValue.dao.MedHisEmployeeMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.crisisValue.model.MedHisEmployee">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_no" jdbcType="VARCHAR" property="employeeNo" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
  </resultMap>
  
  
  <insert id="batchInsert" parameterType="java.util.List">
   	 INSERT INTO med_his_employee (employee_id, employee_no, employee_name,org_id,org_name,org_code,hosp_code)
        VALUES 
      <foreach collection="list" item="item" index="index" separator=",">
       (#{item.employeeId,jdbcType=VARCHAR}, #{item.employeeNo,jdbcType=VARCHAR}, #{item.employeeName,jdbcType=VARCHAR},
        #{item.orgId,jdbcType=VARCHAR}, #{item.orgName,jdbcType=VARCHAR}, #{item.orgCode,jdbcType=VARCHAR},#{item.hospCode,jdbcType=VARCHAR})
      </foreach>
  </insert>
  
  
  <select id="selectHisEmployee" parameterType="String" resultType="cn.trasen.hrms.med.crisisValue.model.MedHisEmployee">
  		select t1.*,t2.employee_no as oaEmployeeNo,t2.phone_number as phoneNumber,t3.technical from med_his_employee t1
		LEFT JOIN cust_emp_base t2 on t1.employee_no = t2.his_employee_no
		LEFT JOIN cust_emp_info t3 on t2.employee_id = t3.info_id
		where 1=1
		<if test="currentUserCode != null and currentUserCode != ''">
		 	and t2.employee_no = #{currentUserCode}
		</if>
		<if test="hisEmployeeId != null and hisEmployeeId != ''">
			and t1.employee_id = #{hisEmployeeId}
		</if>
  </select>
  
  
  <select id="selectPlatformOrgType" resultType="String">
  		select t2.ITEM_NAME_VALUE from comm_dict_type t1
		left join comm_dict_item t2 on t1.id = t2.DIC_TYPE_ID
		where TYPE_CODE = 'PLATFORM_ORG_TYPE' and t1.sso_org_code = '*PUBLIC*'
  </select>
</mapper>
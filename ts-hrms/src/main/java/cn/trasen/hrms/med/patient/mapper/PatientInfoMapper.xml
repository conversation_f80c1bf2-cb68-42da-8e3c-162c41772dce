<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.patient.dao.PatientInfoMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.patient.model.PatientInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="patn_no" jdbcType="VARCHAR" property="patnNo" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sex" jdbcType="VARCHAR" property="sex" />
    <result column="birthday" jdbcType="TIMESTAMP" property="birthday" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="home_street" jdbcType="VARCHAR" property="homeStreet" />
    <result column="in_date" jdbcType="TIMESTAMP" property="inDate" />
    <result column="out_date" jdbcType="TIMESTAMP" property="outDate" />
    <result column="doctor" jdbcType="VARCHAR" property="doctor" />
    <result column="doctor_name" jdbcType="VARCHAR" property="doctorName" />
    <result column="in_dept" jdbcType="VARCHAR" property="inDept" />
    <result column="in_dept_name" jdbcType="VARCHAR" property="inDeptName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="hosp_area" jdbcType="VARCHAR" property="hospArea" />
    <result column="in_diagnosis" jdbcType="VARCHAR" property="inDiagnosis" />
    <result column="out_diagnosis" jdbcType="VARCHAR" property="outDiagnosis" />
    <result column="diagnose_date" jdbcType="TIMESTAMP" property="diagnoseDate" />
    <result column="flag" jdbcType="VARCHAR" property="flag" />
    <result column="out_mode" jdbcType="VARCHAR" property="outMode" />
    <result column="book_date" jdbcType="TIMESTAMP" property="bookDate" />
    <result column="cancel_bit" jdbcType="VARCHAR" property="cancelBit" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
  
  <select id="getPageList" resultType="cn.trasen.hrms.med.patient.model.PatientInfo" parameterType="cn.trasen.hrms.med.patient.model.PatientInfo">
		  select * from med_patient_info a  
		  where a.is_deleted = 'N' and a.cancel_bit = 0 
		 <if test="patnNo !=null and patnNo !=''">
		  		and a.patn_no like concat('%',#{patnNo},'%')
		 	 </if>
		 	 <if test="idCard !=null and idCard !=''">
		  		and a.id_card like concat('%',#{idCard},'%')
		 	 </if>
		 	 <if test="name !=null and name !=''">
			  	and a.name like concat('%',#{name},'%') 
			  </if>
			   <if test="doctorName !=null and doctorName !=''">
			  	and a.doctor_name like concat('%',#{doctorName},'%') 
			  </if>
			   <if test="deptName !=null and deptName !=''">
			  	and a.dept_name like concat('%',#{deptName},'%') 
			  </if>
		 	 <if test="sex !=null and sex !=''">
		  		and a.sex = #{sex}
		 	 </if>
		 	 <if test="inDateBegin !=null and inDateBegin !=''  and inDateEnd !=null and inDateEnd !=''">
		  		and a.in_date  between  concat(#{inDateBegin},' 00:00:00') and concat(#{inDateEnd},' 23:59:59')
		 	 </if>
		 	 <if test="outDateBegin !=null and outDateBegin !=''  and outDateEnd !=null and outDateEnd !=''">
		  		and a.out_date  between  concat(#{outDateBegin},' 00:00:00') and concat(#{outDateEnd},' 23:59:59')
		 	 </if>
		 	 <if test="hospArea !=null and hospArea !=''">
		  		and a.hosp_area = #{hospArea}
		 	 </if>
		 	 <if test="hospitalizationStatus !=null and hospitalizationStatus !='' and hospitalizationStatus=='1'.toString()">
		  		and a.flag in (1,3)
		 	 </if>
		 	 <if test="hospitalizationStatus !=null and hospitalizationStatus !='' and hospitalizationStatus=='2'.toString()">
		  		and a.flag not in (1,3,10)
		 	 </if>
		 	 <if test="type !=null and type !='' and type=='0'.toString()">
		 	 	AND (a.is_new_patient = '0' OR is_new_patient IS NULL)
					AND (a.is_transfer = '0' OR is_transfer IS NULL)
					AND (a.is_bw = '0' OR is_bw IS NULL)
					AND (a.is_bz = '0' OR is_bz IS NULL)
					AND (a.is_operation = '0' OR is_operation IS NULL)
					AND (a.is_mdt = '0' OR is_mdt IS NULL)
					AND (a.is_birth = '0' OR is_birth IS NULL)
					AND (a.is_death = '0' OR is_death IS NULL)
					AND (a.is_angiography = '0' OR is_angiography IS NULL)
		 	 </if>
		 	 <if test="type !=null and type !='' and type=='1'.toString()">
		  		and a.is_new_patient =  '1'
		 	 </if>
		 	   <if test="type !=null and type !='' and type=='2'.toString()">
		  		and a.is_transfer =  '1'
		 	 </if>
		 	 <if test="type !=null and type !='' and type=='3'.toString()">
		  		and a.is_bw =  '1'
		 	 </if>
		 	   <if test="type !=null and type !='' and type=='4'.toString()">
		  		and a.is_bz =  '1'
		 	 </if>
		 	   <if test="type !=null and type !='' and type=='5'.toString()">
		  		and a.is_operation =  '1'
		 	 </if>
		 	   <if test="type !=null and type !='' and type=='6'.toString()">
		  		and a.is_mdt = '1'
		 	 </if>
		 	   <if test="type !=null and type !='' and type=='7'.toString()">
		  		and a.is_birth = '1'
		 	 </if>
		 	   <if test="type !=null and type !='' and type=='8'.toString()">
		  		and a.is_death = '1'
		 	 </if>
		 	   <if test="type !=null and type !='' and type=='9'.toString()">
		  		and a.is_enhance = '1'
		 	 </if>
		 	   <if test="type !=null and type !='' and type=='10'.toString()">
		  		and a.is_angiography = '1'
		 	 </if>
		 	 <if test="deptIdList != null and deptIdList.size() > 0">
				AND a.dept_id  in
				<foreach collection="deptIdList" index="index" item="item" open="(" separator="," close=")">
					#{item}
			 	</foreach>
			 </if>
	  </select>
	  
	  <select id="getHisDeptIds" resultType="java.lang.String" >
		  select platform_id  from  comm_organization a  where 1=1
		 	 <if test="deptIds != null and deptIds.size() > 0">
				AND a.organization_id  in
				<foreach collection="deptIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
	  </select>
	  
	  <select id="getHisDeptIdsByksys" resultType="java.lang.String" >
		  select  b.his_dept_id
		   from dept_mapping a inner join dept_mapping_child  b on a.id=b.dept_mapping_id 
				   where a.is_deleted='N' and b.is_deleted='N' 
		 	 <if test="deptIds != null and deptIds.size() > 0">
				and a.oa_dept_id  in
				<foreach collection="deptIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			and b.syscode=#{businessSystem} 
			order  by  b.id asc
	  </select>
	  
	  <select id="getHisEmpIdByRyys" resultType="java.lang.String" >
		   select  map_employee_id  from  cust_emp_mapping
				   where  is_deleted='N' AND   syscode = #{businessSystem} 
                                         AND base_employee_no=#{empNo}   
		 	limit 1
	  </select>
	  
	  <select id="getNewPatientList" resultType="Map" parameterType="cn.trasen.hrms.med.patient.model.PatientInfo">
		  select *from  med_patient_info a  where a.is_deleted='N' and a.cancel_bit=0  and flag in (3,4)
		  <choose>
				<when test="_databaseId=='dm' or _databaseId=='oracle' or _databaseId=='kingbase'">
					and to_char(a.in_date, 'YYYY-MM-DD')=to_char(SYSDATE + INTERVAL '0' DAY, 'YYYY-MM-DD')
				</when>
				<otherwise>
					and date_format(a.in_date,'%Y-%m-%d')=date_format(date_add(now(),interval -0 day),'%Y-%m-%d')
				</otherwise>
		    </choose>
	  </select>
	  
	  <select id="getOperPatientList" resultType="Map" parameterType="cn.trasen.hrms.med.patient.model.PatientInfo">
	  	select  *from  (
		  select a.*, ROW_NUMBER() OVER ( PARTITION BY patn_id  ORDER BY operation_date DESC  ) AS rn 
            	 from  med_patient_operation   a inner join  ( select   id from  med_patient_info where is_deleted='N' and cancel_bit=0  and flag in (3,4) )b on a.patn_id=b.id 
		  				where a.is_deleted='N' 
		)z where  rn = 1 
	  </select>
	  
	   <select id="getWzPatientList" resultType="Map" parameterType="cn.trasen.hrms.med.patient.model.PatientInfo">
		select  *from  (
		  select a.*, ROW_NUMBER() OVER ( PARTITION BY patn_id  ORDER BY book_date DESC  ) AS rn 
		  		 from  med_patient_orderrecord  a inner join  ( select   id from  med_patient_info where is_deleted='N' and cancel_bit=0  and flag in (3,4) )b on a.patn_id=b.id 
		  				where a.is_deleted='N' 
		)z where  rn = 1 
	  </select>
	  
	  <update id="updatePatientInfoBj">
	  		update med_patient_info SET is_new_patient = '0';

			update med_patient_info SET is_new_patient = '1'
			WHERE id IN (
				SELECT id FROM med_patient_info
				WHERE  DATE_FORMAT(in_date,'%Y%m%d')  = DATE_FORMAT(now(),'%Y%m%d') 
			);
			
			update med_patient_info SET IS_OPERATION = '0';
			
			update med_patient_info SET IS_OPERATION = '1'
			WHERE id IN (
				SELECT patn_id FROM med_patient_operation
				WHERE APBJ = '1' AND wcbj = '0' AND bdelete = '0' AND YSSSRQ  = DATE_FORMAT(now(),'%Y%m%d') 
			);
			
			update med_patient_info SET is_transfer = '0';
			
			update med_patient_info SET is_transfer = '1'
			WHERE id IN (
				SELECT patn_id FROM med_patient_transfer_dept
				WHERE finish_bit = '1'
			);
			
			update med_patient_info SET is_birth = '0';

			update med_patient_info SET is_birth = '1'
			WHERE id IN (
				SELECT patn_id FROM med_patient_birth
				WHERE DATE_FORMAT(birth_date,'%Y%m%d')  = DATE_FORMAT(now(),'%Y%m%d')
			);
			
			update med_patient_info SET is_death = '0';

			update med_patient_info SET is_death = '1'
			WHERE id IN (
				SELECT id FROM med_patient_info
				WHERE out_mode = '4'
			);
	  </update>
	  
	  <select id="getMedShiftRecordList" resultType="cn.trasen.hrms.med.patient.model.PatientInfo" parameterType="Map">
	  		select 
				t1.id,t1.patn_no,t1.bed_no,t1.id_card,t1.name,t1.sex,t1.birthday,t1.phone_number,t1.home_street,t1.in_date,t1.out_date,
				t1.doctor,t1.doctor_name,t1.in_dept,t1.in_dept_name,t1.dept_id,t1.dept_name,t1.hosp_area,t1.in_diagnosis,
				t1.out_diagnosis,t1.diagnose_date,t1.flag,t1.out_mode,t1.book_date,t1.cancel_bit,t1.create_date,t1.create_user,
				t1.create_user_name,t1.is_deleted,
				CASE WHEN t1.in_date BETWEEN #{startTime} and #{endTime} THEN '1' ELSE '0' END is_new_patient,
				CASE WHEN t2.operations IS NOT NULL THEN '1' ELSE '0' END is_operation,
				CASE WHEN t3.id IS NOT NULL THEN '1' ELSE '0' END is_bw,
				CASE WHEN t4.id IS NOT NULL THEN '1' ELSE '0' END is_bz,
				CASE WHEN t5.id IS NOT NULL THEN '1' ELSE '0' END is_birth,
				CASE WHEN t6.id IS NOT NULL THEN '1' ELSE '0' END is_in_transfer,
				CASE WHEN t7.id IS NOT NULL THEN '1' ELSE '0' END is_out_transfer,
				CASE WHEN t8.id IS NOT null THEN '1' ELSE '0' END is_death,
				t6.transfer_date AS inTransferDate,
				t7.transfer_date AS outTransferDate,
				t2.operations as operationName,
				t8.out_diagnosis AS deathDiagnosis,
				t8.out_date AS deathOutDate
			from med_patient_info t1
			JOIN (
				SELECT patn_no,max(in_date) AS latest_time FROM med_patient_info GROUP BY patn_no
			) temp ON t1.patn_no = temp.patn_no AND t1.in_date = temp.latest_time
			LEFT JOIN(
			 SELECT patn_no,wm_concat(DISTINCT operation_name) AS operations FROM med_patient_operation
			 WHERE bdelete = '0' AND APBJ = '1' AND operation_date BETWEEN #{startTime} and #{endTime}
			 GROUP BY patn_no
			) t2 ON t1.patn_no = t2.patn_no
			LEFT JOIN med_patient_orderrecord t3 on t1.patn_no = t3.patn_no  and t3.delete_bit = '0' AND t3.order_context = '病危'
			AND ((t3.order_bdate &lt;= #{endTime} and t3.order_edate >= #{startTime}) or (t3.status_flag = '2' and t3.order_bdate between #{startTime} and #{endTime}))
			<if test="hisOrgIds != null and hisOrgIds.size() > 0">
				AND t3.dept_id in
				<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			LEFT JOIN med_patient_orderrecord t4 on t1.patn_no = t4.patn_no and t4.delete_bit = '0' AND t4.order_context = '病重'
			AND ((t4.order_bdate &lt;= #{endTime} and t4.order_edate >= #{startTime}) or (t4.status_flag = '2' and t4.order_bdate between #{startTime} and #{endTime}))
			<if test="hisOrgIds != null and hisOrgIds.size() > 0">
				AND t4.dept_id in
				<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			LEFT JOIN med_patient_birth t5 on t1.patn_no = t5.patn_no and t5.birth_date BETWEEN #{startTime} and #{endTime}
			LEFT JOIN med_patient_transfer_dept t6 on t1.patn_no = t6.patn_no and t6.finish_bit = '1' and t6.transfer_date BETWEEN #{startTime} and #{endTime}
			<if test="hisOrgIds != null and hisOrgIds.size() > 0">
				AND t6.in_dept_id in
				<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			LEFT JOIN med_patient_transfer_dept t7 on t1.patn_no = t7.patn_no and t7.finish_bit =  '1' and t7.transfer_date BETWEEN #{startTime} and #{endTime}
			<if test="hisOrgIds != null and hisOrgIds.size() > 0">
				AND t7.in_dept_id not in
				<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			LEFT JOIN med_patient_info t8 ON t1.patn_no = t8.patn_no AND  t8.out_mode = '4' and t8.book_date BETWEEN #{startTime} and #{endTime}
			where t1.is_deleted='N' and t1.cancel_bit = '0' <!-- and t1.flag in ('2','3','4') -->
			and (
				 t1.in_date BETWEEN #{startTime} and #{endTime}
			     OR t2.operations IS NOT NULL 
				 OR t3.id IS NOT NULL 
				 OR t4.id IS NOT NULL 
				 OR t5.id IS NOT NULL 
				 OR t6.id IS NOT NULL 
				 OR t7.id IS NOT null
				 OR t8.id IS NOT null
			)
			<if test="hisOrgIds != null and hisOrgIds.size() > 0">
				AND (t1.dept_id in
				<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
				OR t1.in_dept IN
				<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
				OR t6.in_dept_id IN
				<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
				OR t6.in_dept_id IN
				<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
				OR t7.in_dept_id IN
				<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
				OR t7.out_dept_id IN
				<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
				OR t3.dept_id IN
				<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
				OR t4.dept_id IN
				<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
				)
			</if>
			group by t1.patn_no
	  </select>
	  
	  <select id="selectNowNum" parameterType="Map" resultType="Long">
	  		SELECT count(1) FROM med_patient_info t1
			WHERE t1.is_deleted='N' and t1.cancel_bit = '0' and t1.flag in ('2','3','4')
			<if test="hisOrgIds != null and hisOrgIds.size() > 0">
				AND t1.dept_id  in
				<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
	  </select>
	  
	  <select id="selectOriginalNum">
	  		SELECT * FROM med_shift_record
			WHERE is_deleted = 'N' AND takeover_doctor_id = ''
			ORDER BY takeover_date DESC 
			LIMIT  1
	  </select>
</mapper>
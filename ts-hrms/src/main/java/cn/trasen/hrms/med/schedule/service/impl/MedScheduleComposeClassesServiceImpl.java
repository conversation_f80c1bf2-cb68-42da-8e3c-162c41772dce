package cn.trasen.hrms.med.schedule.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.hrms.med.schedule.dao.MedScheduleComposeClassesMapper;
import cn.trasen.hrms.med.schedule.model.MedScheduleClasses;
import cn.trasen.hrms.med.schedule.model.MedScheduleComposeClasses;
import cn.trasen.hrms.med.schedule.service.MedScheduleClassesService;
import cn.trasen.hrms.med.schedule.service.MedScheduleComposeClassesService;

/**
 * @ClassName MedScheduleComposeClassesServiceImpl
 * @Description TODO
 * @date 2025��4��29�� ����3:19:48
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedScheduleComposeClassesServiceImpl implements MedScheduleComposeClassesService {

	@Autowired
	private MedScheduleComposeClassesMapper mapper;
	
	@Autowired
	private MedScheduleClassesService medScheduleClassesService;
	
	@Autowired
	private HrmsEmployeeFeignService hrmsEmployeeFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedScheduleComposeClasses record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedScheduleComposeClasses record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedScheduleComposeClasses record = new MedScheduleComposeClasses();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedScheduleComposeClasses selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedScheduleComposeClasses> getDataSetList(Page page, MedScheduleComposeClasses record) {
		/**
		 * 设置组合班次-只查看自己创建的数据
		 * 排班标识为"Y"时，查询全院(classesUseOrg = -1)和自己创建的数据
		 */
		record.setCurrentUserCode(UserInfoHolder.getCurrentUserCode());
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		//查询被排班人有哪些班次-用于单元格给个人排班时使用
		String employeeId = record.getEmployeeId();
		if(!ObjectUtils.isEmpty(employeeId)){
			PlatformResult<EmployeeResp> resp = hrmsEmployeeFeignService.findByEmployeeId(employeeId);
			if(resp.isSuccess()){
				EmployeeResp employee = resp.getObject();
				if(null == employee){
					return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), new ArrayList<>());
				}
				record.setEmployeeNo(employee.getEmployeeNo());
				record.setOrgCode(employee.getOrgCode());
			}
		}
		
		List<MedScheduleComposeClasses> records = mapper.getPageList(page, record);
		
		if(CollectionUtils.isNotEmpty(records)) {
			
			for (MedScheduleComposeClasses medScheduleComposeClasses : records) {
				String ids = medScheduleComposeClasses.getComposeContentId();
				StringBuilder typeIds = new StringBuilder();//组合班次的typeId
				List<String> asList = Arrays.asList(ids.split("\\|"));
				StringBuilder sb = new StringBuilder();
				StringBuilder sbFT = new StringBuilder();
				String composeColours = "";
				String composeTime = "";
				String typeId = "";
				if(CollectionUtils.isNotEmpty(asList)) {
					
					for (String id : asList) {
						MedScheduleClasses medScheduleClasses = medScheduleClassesService.selectById(id);
						if(medScheduleClasses != null) {
							sb.append(medScheduleClasses.getClassesColor()).append("|");
							sbFT.append(medScheduleClasses.getClassesWorktime()).append("|");
							typeIds.append(medScheduleClasses.getTypeId()).append("|");
						}
					}
					
					if(sb.length() > 0) {
						composeColours = sb.deleteCharAt(sb.length()-1).toString();
					}
					if(sbFT.length() > 0) {
						composeTime = sbFT.deleteCharAt(sbFT.length()-1).toString();
					}
					if(typeIds.length() > 0) {
						typeId = typeIds.deleteCharAt(typeIds.length()-1).toString();
					}
					medScheduleComposeClasses.setComposeColours(composeColours);
					medScheduleComposeClasses.setComposeTime(composeTime);
					medScheduleComposeClasses.setComposeTypeId(typeId);
				}
			}
		}
		
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

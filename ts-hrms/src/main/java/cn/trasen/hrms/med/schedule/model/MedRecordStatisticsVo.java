package cn.trasen.hrms.med.schedule.model;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 考勤统计查询VO
 * <AUTHOR>
 *
 */
@Setter
@Getter
public class MedRecordStatisticsVo {

    @ApiModelProperty(value = "数据字典项")
	private List<String> itemCodeList;

    @ApiModelProperty(value = "开始时间")
	private String startDate;

    @ApiModelProperty(value = "结束时间")
	private String endDate;
    
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    @ApiModelProperty(value = "员工ID集合")
    private List<String> employeeIdList;
    
    @ApiModelProperty(value = "员工所属科室ID集合")
    private List<String> orgIdList;
    
    @ApiModelProperty(value = "员工工号集合")
    private List<String> employeeNoList;

}

package cn.trasen.hrms.med.patient.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.patient.model.PatientBirth;

/**
 * @ClassName PatientBirthService
 * @Description TODO
 * @date 2025��4��8�� ����4:48:25
 * <AUTHOR>
 * @version 1.0
 */
public interface PatientBirthService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	Integer save(PatientBirth record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	Integer update(PatientBirth record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return PatientBirth
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	PatientBirth selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<PatientBirth>
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	DataSet<PatientBirth> getDataSetList(Page page, PatientBirth record);
	
	void updateOrSavePatientBirth();
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.radiate.dao.RadiateCheckupRegisterMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.radiate.model.RadiateCheckupRegister">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_no" jdbcType="VARCHAR" property="employeeNo" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="checkup_date" jdbcType="VARCHAR" property="checkupDate" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="checkup_result" jdbcType="VARCHAR" property="checkupResult" />
    <result column="files" jdbcType="VARCHAR" property="files" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_dept" jdbcType="VARCHAR" property="createDept" />
    <result column="create_dept_name" jdbcType="VARCHAR" property="createDeptName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>
  
  <select id="selectRadiateCheckupRegisterPageList" resultType="cn.trasen.hrms.med.radiate.model.RadiateCheckupRegister" parameterType="cn.trasen.hrms.med.radiate.vo.RadiateCheckupReqVo">
	 select c.id,c.employee_id,c.employee_no,c.employee_name,c.checkup_date,c.expiration_date,c.status,c.checkup_result,c.files,
	 c.create_date,c.create_user,c.create_user_name,c.update_user,c.update_user_name,c.update_date,c.is_deleted,c.physical_no,c.physical_org,
	 e.gender sex,e.org_id,o.NAME AS org_name,e.employee_status,e.sso_org_code,e.sso_org_name,
	 i.technical technical_title,
	 (select count(a1.id) from med_radiate_checkup_abnormal a1
	  left join med_radiate_checkup_register a2 on a1.checkup_id=a2.id
	  where a1.is_deleted = 'N' and a2.is_deleted = 'N' and a2.employee_id=c.employee_id and c.id=a2.id
	  ) as checkup_abnormal_count,
	 cua.review_result checkup_latest_result
	 
	 from med_radiate_checkup_register c
	 left join  med_radiate_personnel_register  m on m.employee_id=c.employee_id
	 left join cust_emp_base e on e.employee_id=m.employee_id
	 left join cust_emp_info i on e.employee_id = i.info_id
	 LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted = 'N' 
	 LEFT JOIN (
	    SELECT b1.*
	    FROM med_radiate_checkup_abnormal b1
	    LEFT JOIN med_radiate_checkup_abnormal b2 
	        ON b1.checkup_id = b2.checkup_id AND b1.review_date &lt; b2.review_date
	    WHERE b2.checkup_id IS NULL
	) cua on cua.checkup_id=c.id
	
	<if test="(expired !=null and expired !='') || (latestStatus !=null and latestStatus !='')">
		 inner JOIN (
		    SELECT employee_id, MAX(checkup_date) AS max_date
		    FROM med_radiate_checkup_register
			where is_deleted='N'
		    GROUP BY employee_id
		) b 
		ON c.employee_id = b.employee_id AND c.checkup_date = b.max_date
 	 </if>
	
	 where c.is_deleted='N' and m.is_deleted='N' and e.is_deleted ='N'
	 
 	 <if test='expired != null and expired != "" and expired == "0"'>
		 and (c.expiration_date > #{today} or c.expiration_date is null)
 	 </if>
 	 <if test='expired != null and expired != "" and expired == "1"'>
		 and c.expiration_date &lt; #{today}
 	 </if>

	  <if test="latestStatus !=null and latestStatus !=''">
		  and c.status  =   #{latestStatus}
	  </if>
	 
 	 <if test="checkupDateBegin !=null and checkupDateBegin !=''">
  		and c.checkup_date   >=   #{checkupDateBegin}   
 	 </if>
 	  <if test="checkupDateEnd !=null and checkupDateEnd !=''">
  		and c.checkup_date   &lt;=   #{checkupDateEnd}   
 	 </if>
 	 <if test="expirationDateBegin !=null and expirationDateBegin !=''">
  		and c.expiration_date   >=   #{expirationDateBegin}   
 	 </if>
 	  <if test="expirationDateEnd !=null and expirationDateEnd !=''">
  		and c.expiration_date   &lt;=   #{expirationDateEnd}   
 	 </if>
 	  <if test="orgId !=null and orgId !=''">
  		and o.organization_id = #{orgId}
 	 </if>
 	 <if test="employeeNo !=null and employeeNo !=''">
	  	and c.employee_no = #{employeeNo}  
	  </if>
	  <if test="employeeId !=null and employeeId !=''">
	  	and c.employee_id = #{employeeId}  
	  </if>
	 <if test="employeeName !=null and employeeName !=''">
	  	and c.employee_name like concat('%',#{employeeName},'%') 
	 </if>
 	 <if test="employeeStatus !=null and employeeStatus !=''">
  		and e.employee_status = #{employeeStatus}  
 	 </if>
 	 <if test="technicalTitle !=null and technicalTitle !=''">
  		and i.technical like concat('%',#{technicalTitle},'%')  
 	 </if>
	   <if test="status !=null and status !=''">
	  	  	and c.status = #{status}  
	  </if>
 	 <if test="orgIdList != null and orgIdList.size() > 0">
		and (o.organization_id in
		<foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
      )
	 </if>
 	 <if test="condition !=null and condition !=''">
	  	and (c.employee_name  like concat('%',#{condition},'%') or  c.employee_no like concat('%',#{condition},'%')  ) 
	 </if>
	 order by c.update_date desc, c.create_date asc 
  </select>
	  
</mapper>
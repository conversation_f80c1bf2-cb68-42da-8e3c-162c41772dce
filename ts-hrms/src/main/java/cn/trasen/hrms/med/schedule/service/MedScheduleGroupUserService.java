package cn.trasen.hrms.med.schedule.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.schedule.model.MedScheduleGroupUser;

/**
 * @ClassName MedScheduleGroupUserService
 * @Description TODO
 * @date 2025��4��7�� ����2:31:36
 * <AUTHOR>
 * @version 1.0
 */
public interface MedScheduleGroupUserService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��4��7�� ����2:31:36
	 * <AUTHOR>
	 */
	Integer save(MedScheduleGroupUser record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��4��7�� ����2:31:36
	 * <AUTHOR>
	 */
	Integer update(MedScheduleGroupUser record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��4��7�� ����2:31:36
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedScheduleGroupUser
	 * @date 2025��4��7�� ����2:31:36
	 * <AUTHOR>
	 */
	MedScheduleGroupUser selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleGroupUser>
	 * @date 2025��4��7�� ����2:31:36
	 * <AUTHOR>
	 */
	DataSet<MedScheduleGroupUser> getDataSetList(Page page, MedScheduleGroupUser record);

	void deleteByGroupId(String groupId);

	List<String> selectMyGroupUser(String currentUserCode,String groupId);
}

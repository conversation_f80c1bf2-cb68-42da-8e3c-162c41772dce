package cn.trasen.hrms.med.radiate.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.bean.ResultData;
import cn.trasen.hrms.med.radiate.dao.RadiatePersonnelRegisterMapper;
import cn.trasen.hrms.med.radiate.dao.RadiateQualificationCertificateMapper;
import cn.trasen.hrms.med.radiate.model.DictConstant;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;
import cn.trasen.hrms.med.radiate.model.RadiateQualificationCertificate;
import cn.trasen.hrms.med.radiate.service.RadiateQualificationCertificateService;
import cn.trasen.hrms.med.radiate.vo.RadiateCertificateReqVo;
import cn.trasen.hrms.med.radiate.vo.RadiatePersonnelReqVo;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.service.BaseDictItemService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName RadiateQualificationCertificateServiceImpl
 * @Description TODO
 * @date 2025��1��8�� ����11:16:41
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class RadiateQualificationCertificateServiceImpl implements RadiateQualificationCertificateService {

	@Autowired
	private RadiateQualificationCertificateMapper mapper;

	@Autowired
	private BaseDictItemService baseDictItemService;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;
	
	@Resource
	private HrmsEmployeeService hrmsEmployeeService;
	
	@Autowired
	private RadiatePersonnelRegisterMapper radiatePersonnelRegisterMapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(RadiateQualificationCertificate record) {
		Assert.hasText(record.getCertificateNumber(), "证件编码不能为空.");
		//证件唯一性校验
		Example example = new Example(RadiateQualificationCertificate.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", record.getEmployeeId());
		criteria.andEqualTo("certificateNumber", record.getCertificateNumber());
		List<RadiateQualificationCertificate> records = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(records)){
			throw new BusinessException(StrUtil.format("该用户证件编码{}已登记了！", record.getCertificateNumber()));
		}
		
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(RadiateQualificationCertificate record) {
		Assert.hasText(record.getCertificateNumber(), "证件编码不能为空.");
		//证件唯一性校验
		Example example = new Example(RadiateQualificationCertificate.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", record.getEmployeeId());
		criteria.andEqualTo("certificateNumber", record.getCertificateNumber());
		criteria.andNotEqualTo("id", record.getId());
		List<RadiateQualificationCertificate> records = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(records)){
			throw new BusinessException(StrUtil.format("该用户证件编码{}已登记了！", record.getCertificateNumber()));
		}
		
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		RadiateQualificationCertificate record = new RadiateQualificationCertificate();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public RadiateQualificationCertificate selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<RadiateQualificationCertificate> getDataSetList(Page page, RadiateQualificationCertificate record) {
		Example example = new Example(RadiateQualificationCertificate.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<RadiateQualificationCertificate> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public DataSet<RadiateQualificationCertificate> selectRadiateQualificationCertificatePagelist(Page page, RadiateCertificateReqVo record) {
		List<RadiateQualificationCertificate> records = mapper.selectRadiateQualificationCertificatePagelist(page, record);
		Map<String, String>  employeeStatusMap  = baseDictItemService.convertDictMap(DictConstant.EMPLOYEE_STATUS);//员工状态
		Map<String, String>  certificateTypeMap  = baseDictItemService.convertDictMap(DictConstant.CERTIFICATE_TYPE);//证书类型
		for(RadiateQualificationCertificate item : records) {
			item.setEmployeeStatusText(employeeStatusMap.get(item.getEmployeeStatus()));
			item.setCertificateTypeText(certificateTypeMap.get(item.getCertificateType()));
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer saveOrUpdateList(List<RadiateQualificationCertificate> records, boolean isAdd, RadiatePersonnelRegister personnel) {
		if(CollUtil.isEmpty(records)){
			return 0;
		}
		//新增
		if(isAdd){
			for (RadiateQualificationCertificate record : records){
				record.setEmployeeId(personnel.getEmployeeId());
				record.setEmployeeNo(personnel.getEmployeeNo());
				record.setEmployeeName(personnel.getEmployeeName());
				save(record);
			}
		} else {
			//先查询该用户一共有几条数据
			List<RadiateQualificationCertificate> oldRecords = selectByEmployeeId(personnel.getEmployeeId());
			//将更新的数据记录，用于后续排除删除
			Set<String> updateIds = new HashSet<>();
			//遍历新增的数据
			for (RadiateQualificationCertificate record : records){
				//判断该用户是否已经存在该数据
				boolean flag = false;
				for (RadiateQualificationCertificate oldRecord : oldRecords){
					if(record.getId().equals(oldRecord.getId())){
						flag = true;
						record.setId(oldRecord.getId());
						record.setEmployeeId(personnel.getEmployeeId());
						record.setEmployeeNo(personnel.getEmployeeNo());
						record.setEmployeeName(personnel.getEmployeeName());
						update(record);
						updateIds.add(record.getId());
					}
				}
				if(!flag){
					record.setEmployeeId(personnel.getEmployeeId());
					record.setEmployeeNo(personnel.getEmployeeNo());
					record.setEmployeeName(personnel.getEmployeeName());
					save(record);
				}
			}
			//删除多余的数据
			if(CollUtil.isNotEmpty(oldRecords) && oldRecords.size() != updateIds.size()){
				for(RadiateQualificationCertificate oldRecord : oldRecords){
					if(!updateIds.contains(oldRecord.getId())){
						deleteById(oldRecord.getId());
					}
				}
			}
		}
		return records.size();
	}

	@Transactional(readOnly = false)
	@Override
	public List<RadiateQualificationCertificate> selectByEmployeeId(String employeeId) {
		Example example = new Example(RadiateQualificationCertificate.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return mapper.selectByExample(example);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteByEmployeeId(String employeeId) {
		Assert.hasText(employeeId, "人员ID不能为空.");
		Example example = new Example(RadiateQualificationCertificate.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		
		RadiateQualificationCertificate update = new RadiateQualificationCertificate();
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		update.setUpdateDate(new Date());
		update.setIsDeleted(Contants.IS_DELETED_TURE);
		if (user != null) {
			update.setUpdateUser(user.getUsercode());
			update.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByExampleSelective(update, example);
	}

	@Transactional(readOnly = false)
	@Override
	public PlatformResult importData(List<RadiateQualificationCertificate> list) {
		
		List<DictItemResp> certificateTypeDic = dictItemFeignService.getDictItemByTypeCode(DictConstant.CERTIFICATE_TYPE).getObject(); //证件类型
		
		//查询所有的人
		HrmsEmployee  entity = new HrmsEmployee();
		List<HrmsEmployee> employeeList = hrmsEmployeeService.getList(entity);
		Map<String, HrmsEmployee> employeeMap = new HashMap<>();
		if(CollUtil.isNotEmpty(employeeList)){
			employeeMap = employeeList.stream().collect(Collectors.toMap(HrmsEmployee::getEmployeeNo, a -> a, (k1, k2) -> k1));
		}
		
		//查询所有的登记人员-用于去重处理
		List<RadiatePersonnelRegister> personnelList = radiatePersonnelRegisterMapper.getList(new RadiatePersonnelReqVo());
		Map<String, RadiatePersonnelRegister> personnelMap = new HashMap<>();
		if(CollUtil.isNotEmpty(personnelList)){
			personnelMap = personnelList.stream().collect(Collectors.toMap(RadiatePersonnelRegister::getEmployeeNo, a -> a, (k1, k2) -> k1));
		}
		
		//查询所有人的证件，校验证件号唯一性
		List<RadiateQualificationCertificate> certificateList = getList();
		Set<String> certificateSet = new HashSet<>();
		if(CollUtil.isNotEmpty(certificateList)){
			for(RadiateQualificationCertificate c : certificateList){
				certificateSet.add(c.getCertificateNumber());
			}
		}
		
		List<String> fileIsnulls; //为空的字段
		List<String> fileDictNotFunds; //字典不能匹配字段
		
		List<ResultData> badList = new ArrayList<>();//失败信息
		
		Integer successCnt = 0;
		Integer errorCnt = 0;
		
		ResultData badData;
		DictItemResp dict;
		
		int i = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		for (RadiateQualificationCertificate item : list) {
			
			i++;
			
			fileIsnulls = new ArrayList<String>();
			fileDictNotFunds = new ArrayList<String>();
			
			String employeeName = item.getEmployeeName();
			if(!StrUtil.isEmpty(employeeName)){
				item.setEmployeeName(employeeName.trim());
			}
			if(StrUtil.isEmpty(item.getEmployeeName())){
				fileIsnulls.add("姓名");
			}
			
			String employeeNo = item.getEmployeeNo();
			if(!StrUtil.isEmpty(employeeNo)){
				item.setEmployeeNo(employeeNo.trim());
			}
			if(StrUtil.isEmpty(item.getEmployeeNo())){
				fileIsnulls.add("工号");
			}
			
			String certificateType = item.getCertificateType();
			if(!StrUtil.isEmpty(certificateType)){
				item.setCertificateType(certificateType.trim());
			}
			if(StrUtil.isEmpty(item.getCertificateType())){
				fileIsnulls.add("证件类型");
			}
			
			String certificateNumber = item.getCertificateNumber();
			if(!StrUtil.isEmpty(certificateNumber)){
				item.setCertificateNumber(certificateNumber.trim());
			}
			if(StrUtil.isEmpty(item.getCertificateNumber())){
				fileIsnulls.add("证件编码");
			}
			
			String issueDate = item.getIssueDate();
			if(!StrUtil.isEmpty(issueDate)){
				item.setIssueDate(issueDate.trim());
			}
			if(StrUtil.isEmpty(item.getIssueDate())){
				fileIsnulls.add("发证日期");
			}
			
			if(CollUtil.isNotEmpty(fileIsnulls)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】为空!", i + 1,StrUtil.join("、", fileIsnulls)));
				badList.add(badData);
				errorCnt++;
				continue;
			}

			//判断是否已经登记
			employeeNo = item.getEmployeeNo();
			if(!personnelMap.containsKey(employeeNo)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】工号未登记!", i + 1 , employeeNo));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			//校验姓名和工号是否匹配
			if(!employeeMap.containsKey(item.getEmployeeNo())){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】工号不存在!", i + 1 , item.getEmployeeNo()));
				badList.add(badData);
				errorCnt++;
				continue;
			} else {
				HrmsEmployee emp = employeeMap.get(item.getEmployeeNo());
				if(!emp.getEmployeeName().equals(item.getEmployeeName())){
					badData = new ResultData();
					badData.setData(StrUtil.format("第【{}】行的【{}】姓名对应的工号不匹配!", i + 1 , item.getEmployeeName()));
					badList.add(badData);
					errorCnt++;
					continue;
				} else {
					item.setEmployeeId(emp.getEmployeeId());
					item.setEmployeeNo(emp.getEmployeeNo());
					item.setEmployeeName(emp.getEmployeeName());
				}
			}
			
			//校验数据字典
			dict = certificateTypeDic.stream().filter(j -> StrUtil.equals(item.getCertificateType(), j.getItemName())).findFirst().orElse(null);
			if(null == dict){
				fileDictNotFunds.add(StrUtil.format("证件类型->{}", item.getCertificateType()));
			} else {
				item.setCertificateType(dict.getItemNameValue());
			}
			
			if(CollUtil.isNotEmpty(fileDictNotFunds)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】无法匹配字典值!", i + 1,StrUtil.join("、", fileDictNotFunds)));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			//字段长度
			if(item.getCertificateNumber().length() > 30){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】证件编码长度超过最大值30!", i + 1 , item.getIssueDate()));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			//校验证件唯一性
			if(certificateSet.contains(item.getCertificateNumber())){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】证件已登记!", i + 1 , item.getCertificateNumber()));
				badList.add(badData);
				errorCnt++;
				continue;
			} else {
				certificateSet.add(item.getCertificateNumber());
			}
			
			//校验日期格式
			try {
				sdf.parse(item.getIssueDate());
			} catch (ParseException e) {
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的发证日期格式为YYYY-MM-DD，【{}】不正确!", i + 1 , item.getIssueDate()));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			save(item);
			successCnt++;
			
		}
		
		if(CollUtil.isNotEmpty(badList)){
			return PlatformResult.failure(StrUtil.format("信息导入 ,总条数:{}、成功:{}、失败{}", list.size(),successCnt,errorCnt),badList);
		}else{
			return PlatformResult.success(StrUtil.format("信息导入 ,总条数:{}、成功:{}、失败{}", list.size(),successCnt,errorCnt));
		}
	}

	@Override
	public List<RadiateQualificationCertificate> getList() {
		Example example = new Example(RadiateQualificationCertificate.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return mapper.selectByExample(example);
	}
}

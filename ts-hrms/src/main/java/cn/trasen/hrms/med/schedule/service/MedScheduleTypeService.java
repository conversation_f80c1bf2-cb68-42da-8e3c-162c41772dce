package cn.trasen.hrms.med.schedule.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.schedule.model.MedScheduleType;

/**
 * @ClassName MedScheduleTypeService
 * @Description TODO
 * @date 2025��3��29�� ����2:57:10
 * <AUTHOR>
 * @version 1.0
 */
public interface MedScheduleTypeService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��3��29�� ����2:57:10
	 * <AUTHOR>
	 */
	Integer save(MedScheduleType record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��3��29�� ����2:57:10
	 * <AUTHOR>
	 */
	Integer update(MedScheduleType record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��3��29�� ����2:57:10
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedScheduleType
	 * @date 2025��3��29�� ����2:57:10
	 * <AUTHOR>
	 */
	MedScheduleType selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleType>
	 * @date 2025��3��29�� ����2:57:10
	 * <AUTHOR>
	 */
	DataSet<MedScheduleType> getDataSetList(Page page, MedScheduleType record);

	void updateSeq(List<MedScheduleType> records);

	List<MedScheduleType> getScheduleTypeList();
}

package cn.trasen.hrms.med.workplan.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

@Table(name = "med_workplan_record")
@Setter
@Getter
public class MedWorkplanRecord {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 责任人工号
     */
    @Column(name = "workplan_user")
    @ApiModelProperty(value = "责任人工号")
    private String workplanUser;

    /**
     * 责任人名称
     */
    @Column(name = "workplan_user_name")
    @ApiModelProperty(value = "责任人名称")
    private String workplanUserName;

    /**
     * 责任人科室
     */
    @Column(name = "workplan_org")
    @ApiModelProperty(value = "责任人科室")
    private String workplanOrg;
    
    /**
     * 责任人科室
     */
    @Column(name = "workplan_org_name")
    @ApiModelProperty(value = "责任人科室名称")
    private String workplanOrgName;

    /**
     * 责任人岗位
     */
    @Column(name = "workplan_identity")
    @ApiModelProperty(value = "责任人岗位")
    private String workplanIdentity;

    /**
     * 预计开始时间
     */
    @Column(name = "start_time")
    @ApiModelProperty(value = "预计开始时间")
    private String startTime;

    /**
     * 预计结束时间
     */
    @Column(name = "end_time")
    @ApiModelProperty(value = "预计结束时间")
    private String endTime;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priority;

    /**
     * 完成状态
     */
    @Column(name = "complete_status")
    @ApiModelProperty(value = "完成状态")
    private String completeStatus;

    /**
     * 工作计划名称
     */
    @Column(name = "workplan_name")
    @ApiModelProperty(value = "工作计划名称")
    private String workplanName;

    /**
     * 工作内容
     */
    @Column(name = "workplan_content")
    @ApiModelProperty(value = "工作内容")
    private String workplanContent;

    /**
     * 资源需求
     */
    @ApiModelProperty(value = "资源需求")
    private String resource;

    /**
     * 目标和指标
     */
    @ApiModelProperty(value = "目标和指标")
    private String target;

    /**
     * 反馈与改进
     */
    @ApiModelProperty(value = "反馈与改进")
    private String feedback;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String files;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 审核状态 0待审核 1审核通过  2审核不通过
     */
    @ApiModelProperty(value = "审核状态 0待审核 1审核通过  2审核不通过")
    private String status;

    /**
     * 审核人工号
     */
    @Column(name = "examine_user")
    @ApiModelProperty(value = "审核人工号")
    private String examineUser;

    /**
     * 审核人名称
     */
    @Column(name = "examine_user_name")
    @ApiModelProperty(value = "审核人名称")
    private String examineUserName;

    /**
     * 审核时间
     */
    @Column(name = "examine_date")
    @ApiModelProperty(value = "审核时间")
    private Date examineDate;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    @Transient
    private List<MedWorkplanPlayer> playerList;
    
    @Transient
    private List<String> orgIdList;
    
    
}
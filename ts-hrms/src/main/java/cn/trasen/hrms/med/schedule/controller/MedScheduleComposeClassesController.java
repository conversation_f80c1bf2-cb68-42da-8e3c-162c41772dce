package cn.trasen.hrms.med.schedule.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.schedule.model.MedScheduleComposeClasses;
import cn.trasen.hrms.med.schedule.service.MedScheduleComposeClassesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedScheduleComposeClassesController
 * @Description TODO
 * @date 2025��4��29�� ����3:19:48
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "组合班次")
public class MedScheduleComposeClassesController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedScheduleComposeClassesController.class);

	@Autowired
	private MedScheduleComposeClassesService medScheduleComposeClassesService;

	/**
	 * @Title saveMedScheduleComposeClasses
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��29�� ����3:19:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")	
	@PostMapping("/api/medScheduleComposeClasses/save")
	public PlatformResult<String> saveMedScheduleComposeClasses(@RequestBody MedScheduleComposeClasses record) {
		try {
			medScheduleComposeClassesService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedScheduleComposeClasses
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��29�� ����3:19:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/medScheduleComposeClasses/update")
	public PlatformResult<String> updateMedScheduleComposeClasses(@RequestBody MedScheduleComposeClasses record) {
		try {
			medScheduleComposeClassesService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedScheduleComposeClassesById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedScheduleComposeClasses>
	 * @date 2025��4��29�� ����3:19:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/medScheduleComposeClasses/{id}")
	public PlatformResult<MedScheduleComposeClasses> selectMedScheduleComposeClassesById(@PathVariable String id) {
		try {
			MedScheduleComposeClasses record = medScheduleComposeClassesService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedScheduleComposeClassesById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��4��29�� ����3:19:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/medScheduleComposeClasses/delete/{id}")
	public PlatformResult<String> deleteMedScheduleComposeClassesById(@PathVariable String id) {
		try {
			medScheduleComposeClassesService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedScheduleComposeClassesList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleComposeClasses>
	 * @date 2025��4��29�� ����3:19:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/medScheduleComposeClasses/list")
	public DataSet<MedScheduleComposeClasses> selectMedScheduleComposeClassesList(Page page, MedScheduleComposeClasses record) {
		return medScheduleComposeClassesService.getDataSetList(page, record);
	}
}

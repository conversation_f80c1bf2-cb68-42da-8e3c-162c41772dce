<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.supportApply.dao.MedSupportApplyMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.supportApply.model.MedSupportApply">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="patient_num" jdbcType="INTEGER" property="patientNum" />
    <result column="icu_num" jdbcType="INTEGER" property="icuNum" />
    <result column="extrabed_num" jdbcType="INTEGER" property="extrabedNum" />
    <result column="apply_reason" jdbcType="VARCHAR" property="applyReason" />
    <result column="rest_days" jdbcType="INTEGER" property="restDays" />
    <result column="pes" jdbcType="VARCHAR" property="pes" />
    <result column="is_confer" jdbcType="VARCHAR" property="isConfer" />
    <result column="other_require" jdbcType="VARCHAR" property="otherRequire" />
    <result column="workflow_id" jdbcType="VARCHAR" property="workflowId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="create_dept_code" jdbcType="VARCHAR" property="createDeptCode" />
    <result column="create_dept_name" jdbcType="VARCHAR" property="createDeptName" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
  </resultMap>
  
  <select id="getDataSetList" resultType="cn.trasen.hrms.med.supportApply.model.MedSupportApply" parameterType="cn.trasen.hrms.med.supportApply.model.MedSupportApply">
  		select t.* from (
	  			select 
	  			t1.*,
	  			(select count(1) from med_support_employee t2 where t2.apply_id = t1.id) as empNumbers,
	  			<choose>
	            	<when test="_databaseId=='dm' ">
		            	(select WM_CONCAT(DISTINCT(t2.support_user_name))
								from med_support_employee t2
								where t2.apply_id = t1.id
						) supportUserNames
	            	</when>
	            	<otherwise>
		                (select GROUP_CONCAT(DISTINCT(t2.support_user_name))
								from med_support_employee t2
								where t2.apply_id = t1.id
						) supportUserNames
	            	</otherwise>
	            </choose>
	  		from med_support_apply t1
	  		where t1.is_deleted = 'N'
	  		<if test="createUserName != null and createUserName != ''">
	  			 and t1.create_user_name like concat('%',#{createUserName},'%')
	  		</if>
	  		<if test="createDateStart != null and createDateStart != '' and createDateEnd != null and createDateEnd != ''">
	  			 and t1.create_date BETWEEN #{createDateStart} and #{createDateEnd}
	  		</if>
	  		<if test="childsList != null and childsList.size() > 0">
	        	 and t1.create_dept_code in
		        <foreach collection="childsList" index="index" item="item" open="(" separator="," close=")">
		            #{item,jdbcType=VARCHAR}
		        </foreach>
	        </if>
  		) t
  		<if test="supportUserNames != null and supportUserNames != ''">
	  		 where t.supportUserNames like concat('%',#{supportUserNames},'%')
	  	</if>
  </select>
</mapper>
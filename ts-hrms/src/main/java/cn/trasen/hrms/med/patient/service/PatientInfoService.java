package cn.trasen.hrms.med.patient.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.patient.model.PatientInfo;
import cn.trasen.hrms.med.patient.model.PatientInfoInParameter;

/**
 * @ClassName PatientInfoService
 * @Description TODO
 * @date 2025��4��7�� ����3:32:28
 * <AUTHOR>
 * @version 1.0
 */
public interface PatientInfoService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	Integer save(PatientInfo record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	Integer update(PatientInfo record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return PatientInfo
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	PatientInfo selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<PatientInfo>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	DataSet<PatientInfo> getDataSetList(Page page, PatientInfo record);
	
	DataSet<PatientInfo> getPageList(Page page, PatientInfo record);
	
	DataSet<Map<String,Object>> getQueryInPatientOrder(Page page, PatientInfoInParameter record);
	
	List<Map<String,Object>> getExamRptRequestNew(PatientInfoInParameter record);
	
	List<Map<String,Object>> getTestRptItemRequestNew(PatientInfoInParameter record);
	
	List<Map<String, Object>> getTestRptItemRequestNewXdt(PatientInfoInParameter parame);
	
	List<Map<String,Object>> getTestRptItemDetailNew(PatientInfoInParameter record);
	
	List<Map<String,Object>> getQueryInpEmrFileInfo(PatientInfoInParameter record);
	
	List<Map<String,Object>> getQueryInpEmrFileInfoPdfStream(PatientInfoInParameter record);
	
	DataSet<Map<String,Object>> getQueryInPatientAccountInfo(Page page, PatientInfoInParameter record);
	
	Map<String,Object> getQueryInPatFeeItemSum(PatientInfoInParameter record);
	
	DataSet<Map<String,Object>> getQueryInPatientOperationRecord(Page page, PatientInfoInParameter record);
	
	DataSet<Map<String,Object>> getQueryInPatient(Page page, PatientInfoInParameter record);
	
	void updateOrSavePatientInfo();
	
	void syncPlatformPatientInfo();
	
	List<PatientInfo> getPatientInfoDetails(PatientInfo record);

	
}

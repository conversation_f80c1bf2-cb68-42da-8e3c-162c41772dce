package cn.trasen.hrms.med.delivery.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.delivery.model.MedDeliveryDict;

/**
 * @ClassName MedDeliveryDictService
 * @Description TODO
 * @date 2025��5��20�� ����3:31:11
 * <AUTHOR>
 * @version 1.0
 */
public interface MedDeliveryDictService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��5��20�� ����3:31:11
	 * <AUTHOR>
	 */
	Integer save(MedDeliveryDict record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��5��20�� ����3:31:11
	 * <AUTHOR>
	 */
	Integer update(MedDeliveryDict record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��5��20�� ����3:31:11
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedDeliveryDict
	 * @date 2025��5��20�� ����3:31:11
	 * <AUTHOR>
	 */
	MedDeliveryDict selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedDeliveryDict>
	 * @date 2025��5��20�� ����3:31:11
	 * <AUTHOR>
	 */
	DataSet<MedDeliveryDict> getDataSetList(Page page, MedDeliveryDict record);

	PlatformResult importMedDeliveryDict(List<MedDeliveryDict> list);
	
	
}

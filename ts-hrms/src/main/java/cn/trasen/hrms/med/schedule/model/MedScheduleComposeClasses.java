package cn.trasen.hrms.med.schedule.model;

import io.swagger.annotations.*;
import java.util.Date;

import javax.persistence.*;
import lombok.*;

@Table(name = "med_schedule_compose_classes")
@Setter
@Getter
public class MedScheduleComposeClasses {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 组合班次名称
     */
    @Column(name = "compose_name")
    @ApiModelProperty(value = "组合班次名称")
    private String composeName;

    /**
     * 组合班次内容
     */
    @Column(name = "compose_content")
    @ApiModelProperty(value = "组合班次内容")
    private String composeContent;

    /**
     * 组合班次内容id
     */
    @Column(name = "compose_content_id")
    @ApiModelProperty(value = "组合班次内容id")
    private String composeContentId;

    /**
     * 使用范围名称
     */
    @Column(name = "classes_use_names")
    @ApiModelProperty(value = "使用范围名称")
    private String classesUseNames;

    /**
     * 使用范围科室
     */
    @Column(name = "classes_use_org")
    @ApiModelProperty(value = "使用范围科室")
    private String classesUseOrg;

    /**
     * 使用范围人员
     */
    @Column(name = "classes_use_user")
    @ApiModelProperty(value = "使用范围人员")
    private String classesUseUser;
    
    @Deprecated
    @Column(name = "is_hospital")
    @ApiModelProperty(value = "是否全院-Y是，N否，默认“否”")
    private String isHospital;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Transient
    private String composeColours;
    
    @Transient
    private String composeTime;
    
    @Transient
    @ApiModelProperty(value = "组合班次类型ID，多个用|隔开")
    private String composeTypeId;
    
    @Transient
    @ApiModelProperty(value = "是否排班标识-Y是，N否，默认“否”")
    private String isSchedule;
    
    @Transient
    @ApiModelProperty(value = "当前登录人的工号")
    private String currentUserCode;
    
    @Transient
    @ApiModelProperty(value = "被排班人ID")
    private String employeeId;
    
    @Transient
    @ApiModelProperty(value = "被排班人工号")
    private String employeeNo;
    
    @Transient
    @ApiModelProperty(value = "被排班人组织机构编码")
    private String orgCode;
}
package cn.trasen.hrms.med.radiate.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.radiate.model.RadiateCheckupAbnormal;

/**
 * @ClassName MedRadiateCheckupAbnormalService
 * @Description TODO
 * @date 2025��2��12�� ����9:50:28
 * <AUTHOR>
 * @version 1.0
 */
public interface RadiateCheckupAbnormalService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��2��12�� ����9:50:28
	 * <AUTHOR>
	 */
	Integer save(RadiateCheckupAbnormal record);
	Integer batchSave(List<RadiateCheckupAbnormal> records);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��2��12�� ����9:50:28
	 * <AUTHOR>
	 */
	Integer update(RadiateCheckupAbnormal record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��2��12�� ����9:50:28
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedRadiateCheckupAbnormal
	 * @date 2025��2��12�� ����9:50:28
	 * <AUTHOR>
	 */
	RadiateCheckupAbnormal selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedRadiateCheckupAbnormal>
	 * @date 2025��2��12�� ����9:50:28
	 * <AUTHOR>
	 */
	DataSet<RadiateCheckupAbnormal> getDataSetList(Page page, RadiateCheckupAbnormal record);
	
	/**
	 * 根据体检ID查询复查数据
	 * 
	 * @param checkupId
	 * @return
	 */
	List<RadiateCheckupAbnormal> getListByCheckupId(String checkupId);

	/**
	 * @Title saveOrUpdateList
	 * @Description 批量信息
	 * @param records
	 * @param isAdd 新增标识
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	Integer saveOrUpdateList(List<RadiateCheckupAbnormal> records, boolean isAdd);
}

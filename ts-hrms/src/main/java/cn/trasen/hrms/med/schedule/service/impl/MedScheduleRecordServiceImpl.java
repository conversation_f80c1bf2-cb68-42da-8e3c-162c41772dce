package cn.trasen.hrms.med.schedule.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.hutool.core.convert.Convert;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.med.schedule.dao.MedScheduleAuthorityMapper;
import cn.trasen.hrms.med.schedule.dao.MedScheduleRecordMapper;
import cn.trasen.hrms.med.schedule.dao.MedScheduleTemplateMapper;
import cn.trasen.hrms.med.schedule.model.MedScheduleAuthority;
import cn.trasen.hrms.med.schedule.model.MedScheduleRecord;
import cn.trasen.hrms.med.schedule.model.MedScheduleTemplate;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;
import cn.trasen.hrms.med.schedule.service.MedScheduleAuthorityService;
import cn.trasen.hrms.med.schedule.service.MedScheduleRecordService;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.utils.CommonUtils;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedScheduleRecordServiceImpl
 * @Description TODO
 * @date 2025��3��29�� ����2:58:59
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedScheduleRecordServiceImpl implements MedScheduleRecordService {

	@Autowired
	private MedScheduleRecordMapper mapper;
	
	@Autowired
	private MedScheduleAuthorityMapper medScheduleAuthorityMapper;
	
	@Autowired
	private MedScheduleAuthorityService medScheduleAuthorityService;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;

	@Autowired
	private MedScheduleTemplateMapper medScheduleTemplateMapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedScheduleRecord record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedScheduleRecord record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedScheduleRecord record = new MedScheduleRecord();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedScheduleRecord selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedScheduleRecord> getDataSetList(Page page, MedScheduleRecord record) {
		Example example = new Example(MedScheduleRecord.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<MedScheduleRecord> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public int selectCountByTypeId(String typeId) {
		
		Example example = new Example(MedScheduleRecord.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("typeId", typeId);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		return mapper.selectCountByExample(example);
		
	}

	@Override
	public int selectCountByClassesId(String classesId) {
		
		Example example = new Example(MedScheduleRecord.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("classesId", classesId);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		return mapper.selectCountByExample(example);
	}

	@Override
	public List<MedScheduleRecord> selectMedScheduleRecordList(ScheduleEmployee record) {

		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.selectMedScheduleRecordList(record);
		
	}
	
	@Override
	public List<MedScheduleRecord> getPersonMedScheduleRecord(ScheduleEmployee record) {
		
//		List<MedScheduleRecord> list = mapper.selectMedScheduleRecordList(record);
		
		String employeeNo = mapper.selectEmployeeNo(record.getEmployeeId());
		
		//查询请假数据 填充进去
		record.setEmployeeNo(employeeNo);
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return medScheduleAuthorityService.getScheduleRecordLists(record);
		
//		List<LeaveRecordVo> leaveDataList = medScheduleAuthorityMapper.selectLeaveData(record);
//		
//		if(CollectionUtils.isNotEmpty(leaveDataList)) {
//			for (LeaveRecordVo leaveData : leaveDataList) {
//				if(StringUtils.isNotBlank(leaveData.getStartDate()) && StringUtils.isNotBlank(leaveData.getEndDate())) {
//					List<String> days = DateUtils.getDays(leaveData.getStartDate(), leaveData.getEndDate());
//					for (String day : days) {
//						MedScheduleRecord medScheduleRecord = new MedScheduleRecord();
//						medScheduleRecord.setClassesColor("#026329");
//						medScheduleRecord.setClassesId("88888888");
//						medScheduleRecord.setClassesName(leaveData.getLeaveType());
//						medScheduleRecord.setTypeId("88888888");
//						medScheduleRecord.setScheduleDate(day);
//						medScheduleRecord.setEmployeeId(record.getEmployeeId());
//						list.add(medScheduleRecord);
//					}
//				}
//			}
//		}
//		
//		//查询进修、规培、学习会议、下乡等数据 填充进去
//		List<LeaveRecordVo> outRecordDataList = medScheduleAuthorityMapper.selectOutRecordData(record);
//		if(CollectionUtils.isNotEmpty(outRecordDataList)) {
//			for (LeaveRecordVo leaveData : outRecordDataList) {
//				if(StringUtils.isNotBlank(leaveData.getStartDate()) && StringUtils.isNotBlank(leaveData.getEndDate())) {
//					List<String> days = DateUtils.getDays(leaveData.getStartDate(), leaveData.getEndDate());
//					for (String day : days) {
//						MedScheduleRecord medScheduleRecord = new MedScheduleRecord();
//						medScheduleRecord.setClassesColor("#a5cb30");
//						medScheduleRecord.setClassesId("88888888");
//						medScheduleRecord.setClassesName(leaveData.getLeaveType());
//						medScheduleRecord.setTypeId("88888888");
//						medScheduleRecord.setScheduleDate(day);
//						medScheduleRecord.setEmployeeId(record.getEmployeeId());
//						list.add(medScheduleRecord);
//					}
//				}
//			}
//		}
//		
//		//销假数据填充进去
//		List<LeaveRecordVo> cancelLeaveDataList = medScheduleAuthorityMapper.selectCancelLeaveData(record);
//		if(CollectionUtils.isNotEmpty(cancelLeaveDataList)) {
//			for (LeaveRecordVo leaveData : cancelLeaveDataList) {
//				if(StringUtils.isNotBlank(leaveData.getStartDate()) && StringUtils.isNotBlank(leaveData.getEndDate())) {
//					List<String> days = DateUtils.getDays(leaveData.getStartDate(), leaveData.getEndDate());
//					for (String day : days) {
//						MedScheduleRecord medScheduleRecord = new MedScheduleRecord();
//						medScheduleRecord.setClassesColor("#e91242");
//						medScheduleRecord.setClassesId("88888888");
//						medScheduleRecord.setClassesName("销" + leaveData.getLeaveType());
//						medScheduleRecord.setTypeId("88888888");
//						medScheduleRecord.setScheduleDate(day);
//						medScheduleRecord.setEmployeeId(record.getEmployeeId());
//						list.add(medScheduleRecord);
//					}
//				}
//			}
//		}
//		
//		return list;
	}

	@Transactional(readOnly = false)
	@Override
	public void batchInsert(ScheduleEmployee record, boolean saveTemplateRecord) {
		
		Assert.hasText(record.getStartDate(), "日期不能为空.");
		Assert.hasText(record.getEndDate(), "日期不能为空.");
		
		if(!saveTemplateRecord){
			clearScheduleRecord(record);
		}
		
		if(CollectionUtils.isNotEmpty(record.getScheduleRecords())) {
			List<MedScheduleRecord> insertList = new ArrayList<MedScheduleRecord>();
			
			if(StringUtils.isNotBlank(record.getOrgIds())) {
				List<String> orgIds = Arrays.asList(record.getOrgIds().split(","));
				record.setOrgIdList(orgIds);
			}
			
			if(StringUtils.isNotBlank(record.getClassesIds())) {
				List<String> classesIds = Arrays.asList(record.getClassesIds().split(","));
				record.setClassesIdList(classesIds);
			}
			
			for (MedScheduleRecord medScheduleRecord : record.getScheduleRecords()) {
				
				if(StringUtil.isEmpty(medScheduleRecord.getEmpOrgId())) {
					medScheduleRecord.setEmpOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
				}
				//排除 请假、销假、进修，学习会议，下乡等数据
				if(StringUtils.isNotBlank(medScheduleRecord.getClassesId()) && !"88888888".equals(medScheduleRecord.getClassesId())) {
					medScheduleRecord.setId(IdUtil.getId());
					medScheduleRecord.setIsDeleted(Contants.IS_DELETED_FALSE);
					medScheduleRecord.setCreateUser(UserInfoHolder.getCurrentUserCode());
					medScheduleRecord.setCreateUserName(UserInfoHolder.getCurrentUserName());
					medScheduleRecord.setCreateDate(new Date());
					medScheduleRecord.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
					medScheduleRecord.setSync("0");
					insertList.add(medScheduleRecord);
				}
			}
			
			if(CollectionUtils.isNotEmpty(insertList)) {
				
				if(insertList.size() > 1000) {
					List<List<MedScheduleRecord>> saveList = CommonUtils.averageAssign(insertList,1000);
					
					saveList.stream().forEach(ls ->{
						mapper.batchInsert(ls);	
			        });
				}else {
					mapper.batchInsert(insertList);	
				}
				
			}
		}
	}

	@Override
	public List<Map<String, Object>> selectExportScheduleRecord(ScheduleEmployee record) {
		
		if(StringUtils.isBlank(record.getIsAll())){
			Boolean isadmin = UserInfoHolder.ISADMIN();
			Boolean YWGLY = UserInfoHolder.getRight("YWGLY"); //医务管理员
			Boolean isPbSettingAdmin = UserInfoHolder.getRight("SCHEDULER_SETTING_MANAGER"); //排班设置员
			Boolean superAdmin = UserInfoHolder.ISSUPERADMIN();//超级管理员
			
			Example example = new Example(MedScheduleAuthority.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andCondition("find_in_set('" + UserInfoHolder.getCurrentUserCode() + "',manage_user_code)");
			criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
			List<MedScheduleAuthority> list = medScheduleAuthorityMapper.selectByExample(example);
			
			String scheduleOrg = null;
			String scheduleUser = null;
			if(CollectionUtils.isNotEmpty(list)) {
				MedScheduleAuthority medScheduleAuthority = list.get(0);
				scheduleOrg = medScheduleAuthority.getScheduleOrg();
				scheduleUser = medScheduleAuthority.getScheduleUser();
			}
			
			if(StringUtils.isBlank(scheduleOrg) && StringUtils.isBlank(scheduleUser) && !isadmin && !YWGLY && !isPbSettingAdmin && !superAdmin) {
				return ListUtil.empty();
			}
			
			if(StringUtils.isNotBlank(scheduleOrg)) {
				List<String> scheduleOrgs =  ListUtil.of(scheduleOrg.split(","));
				record.setScheduleOrgs(scheduleOrgs);
			}
			if(StringUtils.isNotBlank(scheduleUser)) {
				List<String> scheduleUsers =  ListUtil.of(scheduleUser.split(","));
				record.setScheduleUsers(scheduleUsers);
			}
		}
		
		if(StringUtils.isNotBlank(record.getOrgIds())) {
			List<String> orgIds = Arrays.asList(record.getOrgIds().split(","));
			record.setOrgIdList(orgIds);
		}
		
		if(StringUtils.isNotBlank(record.getClassesIds())) {
			List<String> classesIds = Arrays.asList(record.getClassesIds().split(","));
			record.setClassesIdList(classesIds);
		}
		
		return mapper.selectExportScheduleRecord(record);
	}

	@Override
	@Transactional(readOnly = false)
	public void copyScheduleRecord(ScheduleEmployee record) {
		
		Assert.hasText(record.getStartDate(), "日期不能为空.");
		Assert.hasText(record.getEndDate(), "日期不能为空.");
		Assert.hasText(record.getCopyType(), "复制类型不能为空.");
		
		Page page = new Page();
		page.setPageNo(1);
		page.setPageSize(99999);
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		DataSet<ScheduleEmployee> scheduleRecordList = medScheduleAuthorityService.getScheduleRecordList(page, record, null);
		
		List<ScheduleEmployee> rows = scheduleRecordList.getRows();
		
		List<MedScheduleRecord> copyRecords = new ArrayList<>();
		
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		
		for (ScheduleEmployee scheduleEmployee : rows) {
			List<MedScheduleRecord> scheduleRecords = scheduleEmployee.getScheduleRecords();
			if(CollectionUtils.isNotEmpty(scheduleRecords)) {
				for (MedScheduleRecord medScheduleRecord : scheduleRecords) {
					
					if("1".equals(record.getCopyType())) {//复制周
						LocalDate date = LocalDate.parse(medScheduleRecord.getScheduleDate(), formatter);
					    LocalDate newDate =  date.plusDays(7);  //增加7天
						medScheduleRecord.setScheduleDate(newDate.format(formatter));
					}
					
					if("2".equals(record.getCopyType())) {//复制月
						LocalDate date = LocalDate.parse(medScheduleRecord.getScheduleDate(), formatter);
					    LocalDate newDate = date.plusMonths(1);  //增加一个月
						medScheduleRecord.setScheduleDate(newDate.format(formatter));
					}
					
					medScheduleRecord.setCreateDate(new Date());
					medScheduleRecord.setCreateUser(UserInfoHolder.getCurrentUserCode());
					medScheduleRecord.setCreateUserName(UserInfoHolder.getCurrentUserName());
					medScheduleRecord.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
					copyRecords.add(medScheduleRecord);
				}
			}
		}
		
		
		record.setScheduleRecords(copyRecords);
		
		if("1".equals(record.getCopyType())) {//复制周
			LocalDate startDate = LocalDate.parse(record.getStartDate(), formatter);
		    LocalDate newStartDate = startDate.plusDays(7); 
		    
		    LocalDate endDate = LocalDate.parse(record.getEndDate(), formatter);
		    LocalDate newEndDate = endDate.plusDays(7);
		    
			record.setStartDate(newStartDate.format(formatter));
			record.setEndDate(newEndDate.format(formatter));
		}
		
		if("2".equals(record.getCopyType())) {//复制月
			LocalDate startDate = LocalDate.parse(record.getStartDate(), formatter);
		    LocalDate newStartDate = startDate.plusMonths(1);  //增加一个月
		    
		    LocalDate endDate = LocalDate.parse(record.getEndDate(), formatter);
		    LocalDate newEndDate = endDate.plusMonths(1);  //增加一个月
		    
			record.setStartDate(newStartDate.format(formatter));
			record.setEndDate(newEndDate.format(formatter));
		}
		
		
		batchInsert(record, false);
		
	}

	@Override
	@Transactional(readOnly = false)
	public void clearScheduleRecord(ScheduleEmployee record) {
		
		Assert.hasText(record.getStartDate(), "日期不能为空.");
		Assert.hasText(record.getEndDate(), "日期不能为空.");
		
		Boolean isadmin = UserInfoHolder.ISADMIN();
		Boolean YWGLY = UserInfoHolder.getRight("YWGLY"); //医务管理员
		Boolean isPbSettingAdmin = UserInfoHolder.getRight("SCHEDULER_SETTING_MANAGER"); //排班设置员
		Boolean superAdmin = UserInfoHolder.ISSUPERADMIN();//超级管理员
		
		if(!isadmin  && !YWGLY && !isPbSettingAdmin && !superAdmin) {
			Example example = new Example(MedScheduleAuthority.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andCondition("find_in_set('" + UserInfoHolder.getCurrentUserCode() + "',manage_user_code)");
			List<MedScheduleAuthority> list = medScheduleAuthorityMapper.selectByExample(example);
			
			StringBuffer scheduleOrg = null;
			StringBuffer scheduleUser = null;
			if(CollectionUtils.isNotEmpty(list)) {
				
				scheduleOrg = new StringBuffer();
				scheduleUser = new StringBuffer();
				for (MedScheduleAuthority authority : list) {
					if(StringUtils.isNotBlank(authority.getScheduleOrg())) {
						scheduleOrg.append(authority.getScheduleOrg()).append(",");
					}
					if(StringUtils.isNotBlank(authority.getScheduleUser())) {
						scheduleUser.append(authority.getScheduleUser()).append(",");
					}
				}
				
//				MedScheduleAuthority medScheduleAuthority = list.get(0);
//				scheduleOrg = medScheduleAuthority.getScheduleOrg();
//				scheduleUser = medScheduleAuthority.getScheduleUser();
			}
			
			if(StringUtils.isBlank(scheduleOrg) && StringUtils.isBlank(scheduleUser) && !isadmin && !YWGLY) {
				return;
			}
			
			if(StringUtils.isNotBlank(scheduleOrg)) {
				List<String> scheduleOrgs =  ListUtil.of(scheduleOrg.toString().split(","));
				record.setScheduleOrgs(scheduleOrgs);
			}
			if(StringUtils.isNotBlank(scheduleUser)) {
				List<String> scheduleUsers =  ListUtil.of(scheduleUser.toString().split(","));
				record.setScheduleUsers(scheduleUsers);
			}
		}else {
			Example example = new Example(MedScheduleAuthority.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			List<MedScheduleAuthority> list = medScheduleAuthorityMapper.selectByExample(example);
			
			StringBuffer scheduleOrg = new StringBuffer();
			StringBuffer scheduleUser = new StringBuffer();
			if(CollectionUtils.isNotEmpty(list)) {
				
				for (MedScheduleAuthority authority : list) {
					if(StringUtils.isNotBlank(authority.getScheduleOrg())) {
						scheduleOrg.append(authority.getScheduleOrg()).append(",");
					}
					if(StringUtils.isNotBlank(authority.getScheduleUser())) {
						scheduleUser.append(authority.getScheduleUser()).append(",");
					}
				}
			}
			
			if(StringUtils.isBlank(scheduleOrg) && StringUtils.isBlank(scheduleUser)) {
				return;
			}
			
			if(StringUtils.isNotBlank(scheduleOrg)) {
				List<String> scheduleOrgs =  ListUtil.of(scheduleOrg.toString().split(","));
				record.setScheduleOrgs(scheduleOrgs);
			}
			if(StringUtils.isNotBlank(scheduleUser)) {
				List<String> scheduleUsers =  ListUtil.of(scheduleUser.toString().split(","));
				record.setScheduleUsers(scheduleUsers);
			}
		}
		
		
		if(StringUtils.isNotBlank(record.getOrgIds())) {
			List<String> orgIds = Arrays.asList(record.getOrgIds().split(","));
			record.setOrgIdList(orgIds);
		}
		if(StringUtils.isNotBlank(record.getClassesIds())) {
			List<String> classesIds = Arrays.asList(record.getClassesIds().split(","));
			record.setClassesIdList(classesIds);
		}
		if(StringUtils.isNotBlank(record.getEmployeeStatus())) {
			List<String> employeeStatusList = Arrays.asList(record.getEmployeeStatus().split(","));
			record.setEmployeeStatusList(employeeStatusList);
		}

		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		mapper.deleteMedScheduleRecord(record);
	}

	@Override
	@Transactional(readOnly = false)
	public void updateGroupId(String groupId, List<String> updateRecordIds) {
		
		Example example = new Example(MedScheduleRecord.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andIn("id", updateRecordIds);
		
		MedScheduleRecord record = new MedScheduleRecord();
		record.setGroupId(groupId);
		mapper.updateByExampleSelective(record, example);
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteByGroupId(String groupId) {
		mapper.deleteByGroupId(groupId);
	}

	@Override
	public List<Map<String,Object>> statisticsScheduleRecordM(ScheduleEmployee record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.statisticsScheduleRecordM(record);
	}

	@Override
	public List<Map<String, Object>> finishRateScheduleRecordM(ScheduleEmployee record) {
		
		Assert.hasText(record.getStartDate(), "日期不能为空.");
		Assert.hasText(record.getEndDate(), "日期不能为空.");
		Assert.hasText(record.getCopyType(), "类型不能为空.");
		
		//应排天数
		int days = 0;
		
		//copyType 1是周 2是月
		if("1".equals(record.getCopyType())) {
			days = 7;
		}else {
			String startDate = record.getStartDate();
			 LocalDate date = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		     // 获取该月天数
		     days = date.lengthOfMonth();
		}
		
		List<Map<String, Object>> orgBaseInfo = mapper.getOrgBaseInfo(record);
		
		for (Map<String, Object> map : orgBaseInfo) {
			//完成率=(总人数人数*已排班天数/总人数*应排班天数)*100% 
			
			Long empNumbers = (Long)map.get("empNumbers"); //科室总人数
//			Long scheduleEmpNumbers = (Long)map.get("scheduleEmpNumbers"); //科室排班人数
			
			record.setOrgId((String)map.get("organization_id"));
			List<Map<String,Object>> totalList = mapper.selectTotalScheduleRecord(record); //已排班人数
			Long scheduleNum = totalList.stream().mapToLong(vo->Convert.toLong(vo.get("scheduleNum"))).sum();
			
			double finishRate = (((double)scheduleNum) / (empNumbers * days)) * 100;
			
			String finishRateStr = String.format("%.2f", finishRate);  // 小数位设置
			map.put("finishRate", finishRateStr + "%");
            map.put("rate",BigDecimal.valueOf(finishRate).setScale(2,BigDecimal.ROUND_HALF_UP));
		}
        orgBaseInfo.sort((m1,m2)-> Convert.toBigDecimal(m2.get("rate")).compareTo(Convert.toBigDecimal(m1.get("rate"))));
		return orgBaseInfo;
	}

	@Override
	public List<Map<String, String>> unfinishScheduleDareM(ScheduleEmployee record) {
		
		Assert.hasText(record.getStartDate(), "日期不能为空.");
		Assert.hasText(record.getEndDate(), "日期不能为空.");
		Assert.hasText(record.getOrgId(), "科室id不能为空.");
		
		List<Map<String, String>> unfinishScheduleDareM = mapper.unfinishScheduleDareM(record);
		List<String> missingDays = null;
		for (Map<String, String> map : unfinishScheduleDareM) {
			
			String finishDate = map.get("finishDate");
			
			List<String> finishDateList = new ArrayList<>();
			if(StringUtils.isNotBlank(finishDate)) {
				finishDateList = ListUtil.of(finishDate.split(","));
			}
			
			List<String> missingDates = DateUtils.getMissingDates(finishDateList,record.getStartDate(),record.getEndDate());
			if(CollectionUtils.isNotEmpty(missingDates)) {
				missingDays = new ArrayList<>();
				for (String missingDate : missingDates) {
					String[] split = missingDate.split("-");
					missingDays.add(split[2]);
				}
				map.put("missingDays",String.join("/",missingDays));
			}
		}
		
		return unfinishScheduleDareM;
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteScheduleByOutofdate() {
		// 获取人员类型为 “学生”“进修生”的人员
		List<String> orgAttributes = new ArrayList<>();
		List<DictItemResp> orgAttributesList = dictItemFeignService.getDictItemByTypeCode("ORG_ATTRIBUTES").getObject();
		DictItemResp orgAttribute = orgAttributesList.stream().filter(j -> StrUtil.equals("研究生", j.getItemName())).findFirst().orElse(null);
		orgAttributes.add(null == orgAttribute ? "研究生" : orgAttribute.getItemNameValue());

		orgAttribute = orgAttributesList.stream().filter(j -> StrUtil.equals("规培生", j.getItemName())).findFirst().orElse(null);
		orgAttributes.add(null == orgAttribute ? "规培生" : orgAttribute.getItemNameValue());
		
		orgAttribute = orgAttributesList.stream().filter(j -> StrUtil.equals("进修生", j.getItemName())).findFirst().orElse(null);
		orgAttributes.add(null == orgAttribute ? "进修生" : orgAttribute.getItemNameValue());
		
		//员工状态非 “离职”的
		List<String> employeeStatuses = new ArrayList<>();
		List<DictItemResp> employeeStatusList = dictItemFeignService.getDictItemByTypeCode("ORG_ATTRIBUTES").getObject();
		DictItemResp employeeStatus = employeeStatusList.stream().filter(j -> StrUtil.equals("离职", j.getItemName())).findFirst().orElse(null);
		if(null == employeeStatus){
			return;
		}
		employeeStatuses.add(employeeStatus.getItemNameValue());
		
		//培养结束时间-昨天
		SimpleDateFormat sdf = new SimpleDateFormat();
		Calendar ca = Calendar.getInstance();
		ca.add(Calendar.DAY_OF_YEAR, -1);
		String pyjssj = sdf.format(ca.getTime());
		List<HrmsEmployee> employeeList = mapper.selectEmployeeList(employeeStatuses, orgAttributes, pyjssj, UserInfoHolder.getCurrentUserCorpCode());
		if(CollUtil.isNotEmpty(employeeList)){
			List<String> employeeIdList = new ArrayList<>();
			for(HrmsEmployee employee : employeeList){
				employeeIdList.add(employee.getEmployeeId());
			}
			//更新员工状态为“离职”
			mapper.updateEmployeeeStatus(employeeIdList, employeeStatus.getItemNameValue());
			
			//删除今天及以后的排班
			Example exampleM = new Example(MedScheduleRecord.class);
			Example.Criteria criteriaM = exampleM.createCriteria();
			criteriaM.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
			criteriaM.andIn("employeeId", employeeIdList);
			MedScheduleRecord updateMedScheduleRecord = new MedScheduleRecord();
			updateMedScheduleRecord.setIsDeleted(Contants.IS_DELETED_TURE);
			mapper.updateByExampleSelective(updateMedScheduleRecord, exampleM);
			
			//删除模板的排班
			Example example = new Example(MedScheduleTemplate.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
			criteria.andIn("employeeId", employeeIdList);
			MedScheduleTemplate updateMedScheduleTemplate = new MedScheduleTemplate();
			updateMedScheduleTemplate.setIsDeleted(Contants.IS_DELETED_TURE);
			medScheduleTemplateMapper.updateByExampleSelective(updateMedScheduleTemplate, example);
		}
	}

	@Transactional(readOnly = false)
	@Override
	public void saveMedScheduleTemplateRecord(ScheduleEmployee record) {
		Assert.hasText(record.getStartDate(), "日期不能为空.");
		Assert.hasText(record.getEndDate(), "日期不能为空.");
		
		//获取每个用户的模板排班数据
		Page page = new Page();
		page.setPageSize(Integer.MAX_VALUE);
		record.setStartSchedule(true);
		DataSet<ScheduleEmployee> schduleDataSet = medScheduleAuthorityService.getScheduleRecordList(page, record, null);
		if(schduleDataSet.getRows() != null && schduleDataSet.getRows().size() > 0 ){
			List<ScheduleEmployee> scheduleEmployeeList = schduleDataSet.getRows();
			//拿到排班ID为null的即模板排班数据
			List<MedScheduleRecord> insertList = new ArrayList<>();
			for(ScheduleEmployee se : scheduleEmployeeList){
				List<MedScheduleRecord> seRecordList = se.getScheduleRecords();
				if(null != seRecordList && seRecordList.size() > 0){
					for(MedScheduleRecord seRecord : seRecordList){
						if(ObjectUtils.isEmpty(seRecord.getId())){
							insertList.add(seRecord);
						}
					}
				}
			}
			if(insertList.size() > 0){
				record.setScheduleRecords(insertList);
				batchInsert(record, true);
			}
		}
	}
}

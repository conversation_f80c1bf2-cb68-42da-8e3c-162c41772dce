package cn.trasen.hrms.med.qualityApply.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.dao.HrmsOrganizationMapper;
import cn.trasen.hrms.med.qua.dao.QuaAuthItemDetlMapper;
import cn.trasen.hrms.med.qualityApply.dao.MedQualityApplyMapper;
import cn.trasen.hrms.med.qualityApply.model.MedQualityApply;
import cn.trasen.hrms.med.qualityApply.model.MedQualityPunish;
import cn.trasen.hrms.med.qualityApply.service.MedQualityApplyService;
import cn.trasen.hrms.med.qualityApply.service.MedQualityPunishService;
import cn.trasen.hrms.med.risk.model.RiskOperationDiscuss;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.utils.HnsrmyyEmrJdbcUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedQualityApplyServiceImpl
 * @Description TODO
 * @date 2025��2��27�� ����11:15:06
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedQualityApplyServiceImpl implements MedQualityApplyService {

	@Autowired
	private MedQualityApplyMapper mapper;
	
	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;
	
	@Autowired
	private MedQualityPunishService medQualityPunishService;
	
	@Autowired
	private QuaAuthItemDetlMapper quaAuthItemDetlMapper;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;
	
	@Autowired
	private HrmsOrganizationMapper hrmsOrganizationMapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedQualityApply record) {
		
		Example example = new Example(MedQualityApply.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("applyStatus", "0");
		criteria.andEqualTo("applyCode", record.getApplyCode());
		
		List<MedQualityApply> list = mapper.selectByExample(example);
		
		if(CollUtil.isNotEmpty(list)) {
			
			String orgName = mapper.selectOrgName(list.get(0).getApplyOrgId());
			
			Assert.isTrue(false, "【" + record.getApplyName() + "】已担任【" + orgName + "】质控专员！");
		}
		
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		List<Date> punishEndDateList = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(record.getQualityPunishList())) {
			for (MedQualityPunish medQualityPunish : record.getQualityPunishList()) {
				medQualityPunish.setQualityId(record.getId());
				punishEndDateList.add(medQualityPunish.getPunishEndDate());
				medQualityPunishService.save(medQualityPunish);
			}
		}
		
		record.setApplyStatus("0");
		
		if(null != record.getApplyEndDate()) {
			record.setApplySearchDate(record.getApplyEndDate());
		}
		
		if(null != record.getApplyRealDate()) {
			record.setApplySearchDate(record.getApplyRealDate());
		}
		
		calculateExpireStatus(record,punishEndDateList);
		
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedQualityApply record) {
		
		Example example = new Example(MedQualityApply.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("applyStatus", "0");
		criteria.andEqualTo("applyCode", record.getApplyCode());
		criteria.andNotEqualTo("id", record.getId());
		
		List<MedQualityApply> list = mapper.selectByExample(example);
		
		if(CollUtil.isNotEmpty(list)) {
			
			String orgName = mapper.selectOrgName(list.get(0).getApplyOrgId());
			
			Assert.isTrue(false, "【" + record.getApplyName() + "】已担任【" + orgName + "】质控专员！");
		}
		
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		medQualityPunishService.deleteByQualityId(record.getId());
		
		List<Date> punishEndDateList = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(record.getQualityPunishList())) {
			for (MedQualityPunish medQualityPunish : record.getQualityPunishList()) {
				punishEndDateList.add(medQualityPunish.getPunishEndDate());
				medQualityPunish.setQualityId(record.getId());
				medQualityPunishService.save(medQualityPunish);
			}
		}
		
		if(null != record.getApplyEndDate()) {
			record.setApplySearchDate(record.getApplyEndDate());
		}
		
		if(null != record.getApplyRealDate()) {
			record.setApplySearchDate(record.getApplyRealDate());
		}
		
		calculateExpireStatus(record,punishEndDateList);
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedQualityApply record = new MedQualityApply();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedQualityApply selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		
		MedQualityApply medQualityApply = mapper.selectByPrimaryKey(id);
		
		List<MedQualityPunish> qualityPunishList = medQualityPunishService.selectByQualityId(id);
		
		List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
		
		DictItemResp area = areas.stream().filter(j -> StrUtil.equals(medQualityApply.getApplyArea(), j.getItemCode())).findFirst().orElse(null);
		medQualityApply.setApplyAreaText(null == area ? medQualityApply.getApplyArea() : area.getItemName());
		
		medQualityApply.setQualityPunishList(qualityPunishList);
		
		String orgName = mapper.selectOrgName(medQualityApply.getApplyOrgId());
		medQualityApply.setOrgName(orgName);
		
//		String holdOrgName = mapper.selectOrgName(medQualityApply.getHoldOrgId());
//		medQualityApply.setHoldOrgName(holdOrgName);
		
		return medQualityApply;
	}

	@Override
	public DataSet<MedQualityApply> getDataSetList(Page page, MedQualityApply record) {
		
		Boolean isadmin = UserInfoHolder.ISADMIN();
		Boolean YWGLY = UserInfoHolder.getRight("YWGLY"); // 医务管理员
		Boolean ZKGLY = UserInfoHolder.getRight("ZKGLY"); //质控管理员
		Boolean KSGLY = false;
		
		if(!isadmin && !ZKGLY && !YWGLY) {
			List<String> orgIdList = quaAuthItemDetlMapper.selectManageDept(UserInfoHolder.getCurrentUserCode());
			if(CollectionUtils.isNotEmpty(orgIdList)) {
				KSGLY = true;
				record.setOrgIdList(orgIdList);  //科主任看自己科室的
			}else {
				record.setApplyCode(UserInfoHolder.getCurrentUserCode()); //医生看自己的
			}
		}
		
		if(StringUtils.isNoneBlank(record.getOrgId())) {
			List<String> orgIds = Arrays.asList(record.getOrgId().split(","));
			record.setOrgIds(orgIds);
		}
		
//		if(StringUtils.isNoneBlank(record.getHoldOrgId())) {
//			List<String> orgIds = Arrays.asList(record.getHoldOrgId().split(","));
//			record.setHoldOrgIds(orgIds);
//		}
		
		List<MedQualityApply> records = mapper.getDataSetList(record, page);
		
		List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
		
		
		for (MedQualityApply medQualityApply : records) {
			
			medQualityApply.setRoleName("0");//普通用户
			
			if(KSGLY) {
				medQualityApply.setRoleName("1");//科室管理员
			}
			
			if(ZKGLY) {
				medQualityApply.setRoleName("2");//质控管理员
			}
			
			if(isadmin || YWGLY) {
				medQualityApply.setRoleName("3"); //超级管理员
			}
			
			DictItemResp area = areas.stream().filter(j -> StrUtil.equals(medQualityApply.getApplyArea(), j.getItemCode())).findFirst().orElse(null);
			medQualityApply.setApplyAreaText(null == area ? medQualityApply.getApplyArea() : area.getItemName());
			
//			DictItemResp area2 = areas.stream().filter(j -> StrUtil.equals(medQualityApply.getHoldArea(), j.getItemCode())).findFirst().orElse(null);
//			medQualityApply.setHoldAreaText(null == area2 ? medQualityApply.getHoldArea() : area2.getItemName());
			
		}
		
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	@Transactional(readOnly = false)
	public void saveOrUpdate(MedQualityApply record) {
		
		HrmsEmployee hrmsEmployee = hrmsEmployeeService.findByEmployeeNo(record.getApplyCode());
		
		record.setApplyArea(hrmsEmployee.getHospCode());
		record.setApplyOrgId(hrmsEmployee.getOrgId());
		record.setApplyIdcard(hrmsEmployee.getIdentityNumber());
		record.setApplyTechnical(hrmsEmployee.getTechnical());
		record.setApplyPhone(hrmsEmployee.getPhoneNumber());

		Date applyStartDate = record.getApplyStartDate();
		Date applyEndDate = record.getApplyEndDate();
		
		long betweenMonth = DateUtil.betweenDay(applyStartDate, applyEndDate, false);
		
		record.setApplyTermTime((int) Math.ceil((float)betweenMonth/30) + "");
		
		record.setApplyResult("0");
		
		save(record);
	}

	@Override
	@Transactional(readOnly = false)
	public void examine(MedQualityApply record) {
		
		record.setApplyStatus("1");
		mapper.updateByPrimaryKeySelective(record);
		
		//超期任职 需要新增一条待考核数据
		if("1".equals(record.getIsOverdue())) {
			MedQualityApply medQualityApply = new MedQualityApply();
			BeanUtil.copyProperties(record, medQualityApply, false);
			
			medQualityApply.setApplyStartDate(null);
			medQualityApply.setApplyEndDate(null);
			medQualityApply.setApplyTermTime(null);
			medQualityApply.setApplyRealTime(null);
			medQualityApply.setApplyRealDate(null);
			medQualityApply.setApplyFiles(null);
			medQualityApply.setApplySearchDate(null);
			medQualityApply.setApplyResult(null);
			medQualityApply.setWorkflowId(null);
			medQualityApply.setOptDate(null);
			
			calculateExpireStatus(medQualityApply,null);
			
			save(medQualityApply);
		}
		
	}

	@Override
	@Transactional(readOnly = false)
	public void cancel(MedQualityApply record) {
		record.setApplyStatus("2");
		mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public List<MedQualityApply> getMyQualityApply(String applyStatus,String userCode) {
		Example example = new Example(MedQualityApply.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("applyStatus", applyStatus);
		criteria.andEqualTo("applyCode", userCode);
		example.orderBy("applyEndDate").desc();
		
		 List<MedQualityApply> list = mapper.selectByExample(example);
		 for (MedQualityApply medQualityApply : list) {
			 List<MedQualityPunish> punishList = medQualityPunishService.selectByQualityId(medQualityApply.getId());
			 if(CollUtil.isNotEmpty(punishList)) {
				 medQualityApply.setPunishEndDate(punishList.get(0).getPunishEndDate());
			 }
		}
		
		return list;
	}
	
	@Override
	public String getApplyInfo(String userCode) {
		
		StringBuffer str = new StringBuffer();
		
		if(StrUtil.isBlank(userCode)) {
			str.append("应任期时长0个月，已担任0个月，待担任0个月");
			return str.toString();
		}
		
		//1已考核  2已终止
		Example example = new Example(MedQualityApply.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andIn("applyStatus", ListUtil.of("1","2"));
		criteria.andEqualTo("applyCode", userCode);
		criteria.andEqualTo("isOverdue", "0");
		example.orderBy("applyEndDate").desc();
		
		List<MedQualityApply> list = mapper.selectByExample(example);
		
		if(CollUtil.isNotEmpty(list)) {
			MedQualityApply myQualityApply = list.get(0);
			
			String applyTermTime = myQualityApply.getApplyTermTime();//应任期时长
			String applyRealTime = myQualityApply.getApplyRealTime();//已担任时长
			
			int applyTermTimeInt = 0;
			int applyRealTimeInt = 0;
			if(StrUtil.isNotBlank(applyTermTime)) {
				applyTermTimeInt = Integer.valueOf(applyTermTime);
			}else {
				applyTermTime = "0";
			}
			if(StrUtil.isNotBlank(applyRealTime)) {
				applyRealTimeInt = Integer.valueOf(applyRealTime);
			}else {
				applyTermTime = "0";
			}
			
			str.append("应任期时长").append(applyTermTime).append("个月，已担任").append(applyRealTime).append("个月，待担任")
			.append(applyTermTimeInt - applyRealTimeInt > 0 ? applyTermTimeInt - applyRealTimeInt : 0).append("个月");
			
		}
		
		return str.toString();
	}

	@Override
	@Transactional(readOnly = false)
	public void updateExpireStatus() {
		//计算是否过期 expireStatus
		Example example = new Example(MedQualityApply.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<MedQualityApply> medQualityApplyList = mapper.selectByExample(example);
		for (MedQualityApply medQualityApply : medQualityApplyList) {
			List<MedQualityPunish> punishList = medQualityPunishService.selectByQualityId(medQualityApply.getId());
			List<Date> punishEndDateList = new ArrayList<>();
			for (MedQualityPunish medQualityPunish : punishList) {
				punishEndDateList.add(medQualityPunish.getPunishEndDate());
			}
			calculateExpireStatus(medQualityApply,punishEndDateList);
			mapper.updateByPrimaryKeySelective(medQualityApply);
		}
	}

	private void calculateExpireStatus(MedQualityApply medQualityApply,List<Date> punishEndDateList) {
		
		Date applyEndDate = medQualityApply.getApplyEndDate();
		Date applyRealDate = medQualityApply.getApplyRealDate();
		
		if(CollUtil.isNotEmpty(punishEndDateList)) {
			applyEndDate = CollUtil.max(punishEndDateList);
		}
		
		medQualityApply.setExpireStatus("0");
		if(null != applyRealDate) {
			if(DateUtil.compare(new Date(), applyRealDate, "yyyy-MM-dd") > 0) {
				medQualityApply.setExpireStatus("1");
			}
		}
		if(null != applyEndDate) {
			if(DateUtil.compare(new Date(), applyEndDate, "yyyy-MM-dd") > 0) {
				medQualityApply.setExpireStatus("1");
			}
		}
	}

	@Override
	public List<Map<String, Object>> getNoDataApplyDept() {
		return mapper.getNoDataApplyDept();
	}

	@Override
	@Transactional(readOnly = false)
	public void queryQualityApplyEmr() {
		
		Example example = new Example(MedQualityApply.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("applyStatus", "0");
		
		List<MedQualityApply> list = mapper.selectByExample(example);
		
		for (MedQualityApply medQualityApply : list) {
			
			if(null != medQualityApply.getApplyStartDate() && null != medQualityApply.getApplyEndDate()) {
				
				//拿到映射科室id
				List<String> hisOrgIds = hrmsOrganizationMapper.selectHisOrgId(ListUtil.of(medQualityApply.getApplyOrgId()));
				
				String[] ids = new String[hisOrgIds.size()];
	    		
	    		for (int j = 0; j < hisOrgIds.size(); j++) {
	    			ids[j] = hisOrgIds.get(j);
				}
				
				String idsStr = Arrays.stream(ids).map(s -> "'" + s.replace("\\", "\\\\").replace("'", "\\'") + "'")
						.collect(Collectors.joining(","));
				
				//查询质控率
				StringBuilder sb = new StringBuilder();
				
				sb.append("select a.dept_code,a.dept_name, a.cyrs,case when b.zkrs is null then 0 else b.zkrs end as zkrs ");
				sb.append(" from ( select dd.dept_code, dd.dept_name,count(*) cyrs from jhemr.pat_visit pv");
				sb.append(" join jhemr.dept_dict dd on pv.dept_discharge_from=dd.dept_code and dd.dept_name != '口腔二科单病种'");
				sb.append(" where pv.discharge_date_time > = to_date('");
				sb.append(DateUtil.format(medQualityApply.getApplyStartDate(), "yyyy-MM-dd HH:mm"));
				sb.append("','yyyy-mm-dd hh24:mi:ss') and pv.discharge_date_time < to_date('");
				sb.append(DateUtil.format(medQualityApply.getApplyEndDate(), "yyyy-MM-dd HH:mm"));
				sb.append("','yyyy-mm-dd hh24:mi:ss')");
				sb.append(" and dd.dept_code in (").append(idsStr).append(")");
				sb.append(" and pv.baby_flag = '0' and dd.clinic_attr = 0 group by dd.dept_name,dd.dept_code) a");
				sb.append(" left join ( select dd.dept_code, dd.dept_name,count(*) as zkrs from jhemr.pat_visit pv");
				sb.append(" join jhemr.dept_dict dd on pv.dept_discharge_from=dd.dept_code ");
				sb.append(" where pv.discharge_date_time >= to_date( '");
				sb.append(DateUtil.format(medQualityApply.getApplyStartDate(), "yyyy-MM-dd HH:mm"));
				sb.append("','yyyy-mm-dd hh24:mi:ss') and pv.discharge_date_time <to_date( '");
				sb.append(DateUtil.format(medQualityApply.getApplyEndDate(), "yyyy-MM-dd HH:mm"));
				sb.append("','yyyy-mm-dd hh24:mi:ss')");
				sb.append(" and dd.dept_code in (").append(idsStr).append(")");
				sb.append(" and exists ( select 1 from jhemr.jhmr_rectification r where pv.patient_id=r.patient_id and pv.visit_id=r.visit_id");
				sb.append(" and r.qc_dept_name not in ('质控办','医务办(岳)','质控办(马王堆)','口腔二科单病种')) group by dd.dept_name,dd.dept_code");
				sb.append(") b  on a.dept_name=b.dept_name");
				
				List<Map<String,Object>> qualityApplyEmrList = HnsrmyyEmrJdbcUtil.queryQualityApplyEmr(sb.toString());//执行语句返回结果
				
				if(CollUtil.isNotEmpty(qualityApplyEmrList)) {
					
					if(qualityApplyEmrList.size() == 1) {
						Map<String, Object> map = qualityApplyEmrList.get(0);
						Double cyrs = Double.valueOf((String)map.get("cyrs")); //出院人数
						Double zkrs = Double.valueOf((String)map.get("zkrs")); //质控人数
						Double qualityRate = 0.0;
						if (cyrs != 0) {
						    double percentage = (double) zkrs / cyrs * 100;
						    qualityRate = Double.parseDouble(String.format("%.2f", percentage));
						} 
						
						medQualityApply.setQualityRate(qualityRate.toString() + "%");
						
						mapper.updateByPrimaryKeySelective(medQualityApply);
						
					}else {
						
						StringBuffer qualityRateSb = new StringBuffer();
						
						for (Map<String,Object> map : qualityApplyEmrList) {
							
							String deptName = (String)map.get("deptName");
							qualityRateSb.append(deptName).append(":");
							
							Double cyrs = Double.valueOf((String)map.get("cyrs")); //出院人数
							Double zkrs = Double.valueOf((String)map.get("zkrs")); //质控人数
							
							Double qualityRate = 0.0;
							if (cyrs != 0) {
							    double percentage = (double) zkrs / cyrs * 100;
							    qualityRate = Double.parseDouble(String.format("%.2f", percentage));
							} 
							
							qualityRateSb.append(qualityRate).append(";");
						}
						
						medQualityApply.setQualityRate(qualityRateSb.toString());
						
						mapper.updateByPrimaryKeySelective(medQualityApply);
					}
				}
			}
		}
		
	}
	
}

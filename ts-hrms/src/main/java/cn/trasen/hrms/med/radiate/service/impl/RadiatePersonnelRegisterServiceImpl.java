package cn.trasen.hrms.med.radiate.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.trasen.hrms.med.radiate.service.*;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.bean.ResultData;
import cn.trasen.hrms.med.radiate.dao.RadiatePersonnelRegisterMapper;
import cn.trasen.hrms.med.radiate.emun.DoseTypeEmun;
import cn.trasen.hrms.med.radiate.emun.MonitorCycleEmun;
import cn.trasen.hrms.med.radiate.emun.MonitoringPeriodEmun;
import cn.trasen.hrms.med.radiate.emun.RegisterCategoryEmun;
import cn.trasen.hrms.med.radiate.emun.TrainingTypeEmun;
import cn.trasen.hrms.med.radiate.model.DictConstant;
import cn.trasen.hrms.med.radiate.model.RadiateMonitor;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;
import cn.trasen.hrms.med.radiate.vo.RadiatePersonnelReqVo;
import cn.trasen.hrms.med.radiate.vo.RadiatePersonnelVo;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.service.BaseDictItemService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName RadiatePersonnelRegisterServiceImpl
 * @Description TODO
 * @date 2025��1��8�� ����11:21:29
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class RadiatePersonnelRegisterServiceImpl implements RadiatePersonnelRegisterService {

	@Autowired
	private RadiatePersonnelRegisterMapper mapper;

	@Autowired
	private BaseDictItemService baseDictItemService;

	@Autowired
	private RadiateMonitorService radiateMonitorService;

	@Autowired
	private RadiateQualificationCertificateService radiateQualificationCertificateService;

	@Autowired
	private RadiateCheckupRegisterService radiateCheckupRegisterService;

	@Autowired
	private RadiateTrainingRegisterService radiateTrainingRegisterService;
	
	@Autowired
	private RadiateDoseMonitoringService radiateDoseMonitoringService;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;
	
	@Resource
	private HrmsEmployeeService hrmsEmployeeService;
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(RadiatePersonnelRegister record) {
		//校验employeeId唯一性
		Example example = new Example(RadiatePersonnelRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", record.getEmployeeId());
		List<RadiatePersonnelRegister> records = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(records)){
			throw new BusinessException("该用户已登记了！");
		}
		
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<RadiateMonitor> monitorList = record.getMonitorList();
		if(CollUtil.isNotEmpty(monitorList)){
			for(RadiateMonitor mm : monitorList){
				mm.setEmployeeId(record.getEmployeeId());
				radiateMonitorService.save(mm);
			}
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(RadiatePersonnelRegister record) {
		//校验employeeId唯一性
		Example example = new Example(RadiatePersonnelRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", record.getEmployeeId());
		criteria.andNotEqualTo("id", record.getId());
		List<RadiatePersonnelRegister> records = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(records)){
			throw new BusinessException("该用户已登记了！");
		}
				
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		RadiatePersonnelRegister record = mapper.selectByPrimaryKey(id);
		if(record == null){
			throw new BusinessException("该用户不存在!");
		}
		record.setUpdateDate(new Date());
		record.setIsDeleted(Contants.IS_DELETED_TURE);
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//删除放射监测
		radiateMonitorService.deleteByEmployeeId(record.getEmployeeId());
		//删除证件资质
		radiateQualificationCertificateService.deleteByEmployeeId(record.getEmployeeId());
		//删除体检状况
		radiateCheckupRegisterService.deleteByEmployeeId(record.getEmployeeId());
		//删除培训状况
		radiateTrainingRegisterService.deleteByEmployeeId(record.getEmployeeId());
		//删除剂量检测
		radiateDoseMonitoringService.deleteByEmployeeId(record.getEmployeeId());
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public RadiatePersonnelRegister selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public List<RadiatePersonnelRegister> getList(RadiatePersonnelReqVo record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.getList(record);
	}

	@Override
	public DataSet<RadiatePersonnelRegister> selectRadiatePersonnelRegisterPageList(Page page, RadiatePersonnelReqVo record) {
		SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-dd");
		record.setToday(sdf.format(new Date()));
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());;
		List<RadiatePersonnelRegister> records = mapper.selectRadiatePersonnelRegisterPageList(page, record);
		Map<String, String>  sexMap  = baseDictItemService.convertDictMap(DictConstant.SEX);//获取性别字典Map
		Map<String, String>  educationMap  = baseDictItemService.convertDictMap(DictConstant.EDUCATION_TYPE);//学历
		Map<String, String>  employeeStatusMap  = baseDictItemService.convertDictMap(DictConstant.EMPLOYEE_STATUS);//员工状态
		Map<String, String>  radiationTypeMap  = baseDictItemService.convertDictMap(DictConstant.RADIATION_TYPE);//放射类别
		for(RadiatePersonnelRegister personnelRegister : records) {
			personnelRegister.setSexText(sexMap.get(personnelRegister.getSex()));//根据key,获取字典values
			personnelRegister.setEducationText(educationMap.get(personnelRegister.getEducation()));
			personnelRegister.setEmployeeStatusText(employeeStatusMap.get(personnelRegister.getEmployeeStatus()));
			//放射类别多选
			String radiationType = personnelRegister.getRadiationType();
			if(!ObjectUtils.isEmpty(radiationType)){
				String[] radiationTypeArr = radiationType.split(",");
				String radiationTypeText = "";
				for(String at : radiationTypeArr){
					if(radiationTypeMap.containsKey(at)){
						radiationTypeText += radiationTypeMap.get(at) + ",";
					}
				}
				if(!"".equals(radiationTypeText)){
					radiationTypeText = radiationTypeText.substring(0, radiationTypeText.length() - 1);
				}
				personnelRegister.setRadiationTypeText(radiationTypeText);
			}
		}
		
		//导出数据添加监测设置信息
//		if(CollUtil.isNotEmpty(records) && record.isExport()){
//			List<RadiatePersonnelRegister> exports = new ArrayList<>();
//			//先获取所有监测数据
//			List<RadiateMonitor> monitorList = radiateMonitorService.selectAll();
//			if(CollUtil.isNotEmpty(monitorList)){
//				Map<String, List<RadiateMonitor>> monitorMap = monitorList.stream().collect(Collectors.groupingBy(RadiateMonitor::getEmployeeId));
//				for(RadiatePersonnelRegister item : records){
//					if(monitorMap.containsKey(item.getEmployeeId())){
//						List<RadiateMonitor> list = monitorMap.get(item.getEmployeeId());
//						for(RadiateMonitor mm : list){
//							item.setRadiationTypeText(mm.getRegisterTypeText());
//							item.setRegisterCategoryText(mm.getRegisterCategoryText());
//							item.setMonitorCycleText(mm.getMonitorCycleText());
//							exports.add(item);
//						}
//					} else {
//						exports.add(item);
//					}
//				}
//			}
//			return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), exports);
//		}
		//TODO 计算到期天数
		if(!record.isExport()){
			
		}
		
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer saveInfo(RadiatePersonnelVo record) {
		//保存基本信息
		save(record.getPersonnel());
		
		List<RadiateMonitor> monitorList = record.getMonitorList();
		
		//保存放射监测
		radiateMonitorService.saveOrUpdateList(monitorList, true, record.getPersonnel());
		//保存证件资质
		radiateQualificationCertificateService.saveOrUpdateList(record.getCertificateList(), true, record.getPersonnel());
		
		RadiateMonitor checkupMonitor = null; //体检监测
		List<RadiateMonitor> trainingMonitor = new ArrayList<>();//培训监测
		List<RadiateMonitor> doseMonitor = new ArrayList<>();//剂量监测
		if(CollUtil.isNotEmpty(monitorList)){
			for(RadiateMonitor monitor : monitorList){
				String registerCategory = monitor.getRegisterCategory();
				if(!ObjectUtils.isEmpty(registerCategory)){
					if(registerCategory.equals(RegisterCategoryEmun.CHECKUP.getCode())){
						checkupMonitor = monitor;
					} else if(registerCategory.equals(RegisterCategoryEmun.TRAINING.getCode())){
						trainingMonitor.add(monitor);
					} else if(registerCategory.equals(RegisterCategoryEmun.DOSE.getCode())){
						doseMonitor.add(monitor);
					}
				}
			}
		}
		//保存体检状况
		radiateCheckupRegisterService.saveOrUpdateList(record.getCheckupList(), checkupMonitor, true, record.getPersonnel());
		//保存培训状况
		radiateTrainingRegisterService.saveOrUpdateList(record.getTrainingList(), trainingMonitor, true, record.getPersonnel());
		//保存剂量检测
		radiateDoseMonitoringService.saveOrUpdateList(record.getDoseList(), doseMonitor, true, record.getPersonnel());
		return 1;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer updateInfo(RadiatePersonnelVo record) {
		//保存基本信息
		update(record.getPersonnel());
		
		List<RadiateMonitor> monitorList = record.getMonitorList();
		
		//保存放射监测
		radiateMonitorService.saveOrUpdateList(monitorList, false, record.getPersonnel());
		//保存证件资质
		radiateQualificationCertificateService.saveOrUpdateList(record.getCertificateList(), false, record.getPersonnel());
		
		RadiateMonitor checkupMonitor = null; //体检监测
		List<RadiateMonitor> trainingMonitor = new ArrayList<>();//培训监测
		List<RadiateMonitor> doseMonitor = new ArrayList<>();//剂量监测
		if(CollUtil.isNotEmpty(monitorList)){
			for(RadiateMonitor monitor : monitorList){
				String registerCategory = monitor.getRegisterCategory();
				if(!ObjectUtils.isEmpty(registerCategory)){
					if(registerCategory.equals(RegisterCategoryEmun.CHECKUP.getCode())){
						checkupMonitor = monitor;
					} else if(registerCategory.equals(RegisterCategoryEmun.TRAINING.getCode())){
						trainingMonitor.add(monitor);
					} else if(registerCategory.equals(RegisterCategoryEmun.DOSE.getCode())){
						doseMonitor.add(monitor);
					}
				}
			}
		}
		
		//保存体检状况
		radiateCheckupRegisterService.saveOrUpdateList(record.getCheckupList(), checkupMonitor, false, record.getPersonnel());
		//保存培训状况
		radiateTrainingRegisterService.saveOrUpdateList(record.getTrainingList(), trainingMonitor, false, record.getPersonnel());
		//保存剂量检测
		radiateDoseMonitoringService.saveOrUpdateList(record.getDoseList(), doseMonitor, false, record.getPersonnel());
		return 1;
	}

	@Override
	public RadiatePersonnelVo selectByEmployeeId(String employeeId) {
		Assert.hasText(employeeId, "员工ID不能为空.");
		RadiatePersonnelReqVo reqVo = new RadiatePersonnelReqVo();
		reqVo.setEmployeeId(employeeId);
		List<RadiatePersonnelRegister> records = mapper.selectRadiatePersonnelRegisterPageList(new Page(), reqVo);
		if(CollUtil.isNotEmpty(records) && records.size() > 1){
			throw new BusinessException("该用户登记了两次！");
		}

		Map<String, String>  sexMap  = baseDictItemService.convertDictMap(DictConstant.SEX);//获取性别字典Map
		Map<String, String>  educationMap  = baseDictItemService.convertDictMap(DictConstant.EDUCATION_TYPE);//学历
		Map<String, String>  employeeStatusMap  = baseDictItemService.convertDictMap(DictConstant.EMPLOYEE_STATUS);//员工状态
		Map<String, String>  radiationTypeMap  = baseDictItemService.convertDictMap(DictConstant.RADIATION_TYPE);//放射类别
		
		RadiatePersonnelVo vo = new RadiatePersonnelVo();
		RadiatePersonnelRegister personnelRegister = records.get(0);
		//基本信息
		personnelRegister.setSexText(sexMap.get(personnelRegister.getSex()));//根据key,获取字典values
		personnelRegister.setEducationText(educationMap.get(personnelRegister.getEducation()));
		personnelRegister.setEmployeeStatusText(employeeStatusMap.get(personnelRegister.getEmployeeStatus()));
		//放射类别多选
		String radiationType = personnelRegister.getRadiationType();
		if(!ObjectUtils.isEmpty(radiationType)){
			String[] radiationTypeArr = radiationType.split(",");
			String radiationTypeText = "";
			for(String at : radiationTypeArr){
				if(radiationTypeMap.containsKey(at)){
					radiationTypeText += radiationTypeMap.get(at) + ",";
				}
			}
			if(!"".equals(radiationTypeText)){
				radiationTypeText = radiationTypeText.substring(0, radiationTypeText.length() - 1);
			}
			personnelRegister.setRadiationTypeText(radiationTypeText);
		}
		vo.setPersonnel(personnelRegister);
		//放射监测
		vo.setMonitorList(radiateMonitorService.selectByEmployeeId(employeeId));
		//证件资质
		vo.setCertificateList(radiateQualificationCertificateService.selectByEmployeeId(employeeId));
		//体检状况
		vo.setCheckupList(radiateCheckupRegisterService.selectByEmployeeId(employeeId));
		//培训状况
		vo.setTrainingList(radiateTrainingRegisterService.selectByEmployeeId(employeeId));
		//剂量检测
		vo.setDoseList(radiateDoseMonitoringService.selectByEmployeeId(employeeId));
		
		return vo;
	}

	@Transactional(readOnly = false)
	@Override
	public PlatformResult importData(List<RadiatePersonnelRegister> list) {
		
		List<DictItemResp> radiationTypeDic = dictItemFeignService.getDictItemByTypeCode(DictConstant.RADIATION_TYPE).getObject(); //放射类别
		//查询所有的人
		HrmsEmployee  entity = new HrmsEmployee();
		List<HrmsEmployee> employeeList = hrmsEmployeeService.getList(entity);
		Map<String, HrmsEmployee> employeeMap = new HashMap<>();
		if(CollUtil.isNotEmpty(employeeList)){
			employeeMap = employeeList.stream().collect(Collectors.toMap(HrmsEmployee::getEmployeeNo, a -> a, (k1, k2) -> k1));
		}
		
		//查询所有的登记人员-用于去重处理
		List<RadiatePersonnelRegister> personnelList = getList(new RadiatePersonnelReqVo());
		Map<String, RadiatePersonnelRegister> personnelMap = new HashMap<>();
		if(CollUtil.isNotEmpty(personnelList)){
			personnelMap = personnelList.stream().collect(Collectors.toMap(RadiatePersonnelRegister::getEmployeeNo, a -> a, (k1, k2) -> k1));
		}
		//本次导入的工号
		Set<String> personnelSet = new HashSet<>();
		
		List<String> fileIsnulls; //为空的字段
		List<String> fileDictNotFunds; //字典不能匹配字段
		
		List<ResultData> badList = new ArrayList<>();//失败信息
		
		Integer successCnt = 0;
		Integer errorCnt = 0;
		
		ResultData badData;
		DictItemResp dict;
		
		int i = 0;
		for (RadiatePersonnelRegister item : list) {
			
			i++;
			
			fileIsnulls = new ArrayList<String>();
			fileDictNotFunds = new ArrayList<String>();
			
			String employeeName = item.getEmployeeName();
			if(!StrUtil.isEmpty(employeeName)){
				item.setEmployeeName(employeeName.trim());
			}
			if(StrUtil.isEmpty(item.getEmployeeName())){
				fileIsnulls.add("姓名");
			}
			
			String employeeNo = item.getEmployeeNo();
			if(!StrUtil.isEmpty(employeeNo)){
				item.setEmployeeNo(employeeNo.trim());
			}
			if(StrUtil.isEmpty(item.getEmployeeNo())){
				fileIsnulls.add("工号");
			}
			
			String radiationType = item.getRadiationType();
			if(!StrUtil.isEmpty(radiationType)){
				item.setRadiationType(radiationType.trim());
			}
			if(StrUtil.isEmpty(item.getRadiationType())){
				fileIsnulls.add("拟从事放射类别");
			}
			
			if(CollUtil.isNotEmpty(fileIsnulls)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】为空!", i + 1,StrUtil.join("、", fileIsnulls)));
				badList.add(badData);
				errorCnt++;
				continue;
			}

			//判断是否已经登记
			employeeNo = item.getEmployeeNo();
			if(personnelMap.containsKey(employeeNo) || personnelSet.contains(employeeNo)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】工号已经登记!", i + 1 , employeeNo));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			//校验姓名和工号是否匹配
			if(!employeeMap.containsKey(employeeNo)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】工号不存在!", i + 1 , employeeNo));
				badList.add(badData);
				errorCnt++;
				continue;
			} else {
				HrmsEmployee emp = employeeMap.get(employeeNo);
				if(!emp.getEmployeeName().equals(item.getEmployeeName())){
					badData = new ResultData();
					badData.setData(StrUtil.format("第【{}】行的【{}】姓名对应的工号不匹配!", i + 1 , item.getEmployeeName()));
					badList.add(badData);
					errorCnt++;
					continue;
				} else {
					item.setEmployeeId(emp.getEmployeeId());
					item.setEmployeeNo(emp.getEmployeeNo());
					item.setEmployeeName(emp.getEmployeeName());
				}
			}
			
			
			//校验数据字典
			String[] radiationTypeArr = item.getRadiationType().split(",");
			radiationType = "";
			for(String rt : radiationTypeArr){
				dict = radiationTypeDic.stream().filter(j -> StrUtil.equals(rt, j.getItemName())).findFirst().orElse(null);
				if(null == dict){
					fileDictNotFunds.add(StrUtil.format("拟从事放射类别->{}", rt));
				} else {
					radiationType += "," + dict.getItemNameValue();
				}
			}
			
			if(CollUtil.isNotEmpty(fileDictNotFunds)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】无法匹配拟从事放射类别字典值!", i + 1,StrUtil.join("、", fileDictNotFunds)));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			if(!ObjectUtils.isEmpty(radiationType)){
				radiationType = radiationType.substring(1);
				item.setRadiationType(radiationType);
			}
			
			//保存监测设置
			List<RadiateMonitor> monitorList = getAutoMonitorList();
			item.setMonitorList(monitorList);
			
			
			save(item);
			successCnt++;
			
			personnelSet.add(item.getEmployeeNo());
		}
		
		if(CollUtil.isNotEmpty(badList)){
			return PlatformResult.failure(StrUtil.format("信息导入 ,总条数:{}、成功:{}、失败{}", list.size(),successCnt,errorCnt),badList);
		}else{
			return PlatformResult.success(StrUtil.format("信息导入 ,总条数:{}、成功:{}、失败{}", list.size(),successCnt,errorCnt));
		}
	}
	
	/**
	 * 获取默认的监控设置
	 * @return
	 */
	private static List<RadiateMonitor> getAutoMonitorList(){
		List<RadiateMonitor> monitorList = new ArrayList<RadiateMonitor>();
		RadiateMonitor mm = new RadiateMonitor();
		//体检
		mm.setRegisterCategory(RegisterCategoryEmun.CHECKUP.getCode());
		mm.setMonitorCycle(MonitorCycleEmun.ONE_YEAR.getCode());
		mm.setMonitorTimes(1);
		monitorList.add(mm);
		//培训-放射防护类
		mm = new RadiateMonitor();
		mm.setRegisterCategory(RegisterCategoryEmun.TRAINING.getCode());
		mm.setRegisterType(TrainingTypeEmun.RADIATION_PROTECTION.getCode());
		mm.setMonitorCycle(MonitorCycleEmun.TWO_YEAR.getCode());
		mm.setMonitorTimes(1);
		monitorList.add(mm);
		//培训-辐射安全类
		mm = new RadiateMonitor();
		mm.setRegisterCategory(RegisterCategoryEmun.TRAINING.getCode());
		mm.setRegisterType(TrainingTypeEmun.RADIATION_SAFETY.getCode());
		mm.setMonitorCycle(MonitorCycleEmun.TWO_YEAR.getCode());
		mm.setMonitorTimes(1);
		monitorList.add(mm);
		//计量监测-腕式
		mm = new RadiateMonitor();
		mm.setRegisterCategory(RegisterCategoryEmun.DOSE.getCode());
		mm.setRegisterType(DoseTypeEmun.WRIST_TYPE.getCode());
		mm.setMonitorCycle(MonitoringPeriodEmun.EVERY_QUARTER.getCode());
		mm.setMonitorTimes(1);
		monitorList.add(mm);
		//计量监测-腕式
		mm = new RadiateMonitor();
		mm.setRegisterCategory(RegisterCategoryEmun.DOSE.getCode());
		mm.setRegisterType(DoseTypeEmun.BADGE_TYPE.getCode());
		mm.setMonitorCycle(MonitoringPeriodEmun.EVERY_YEAR.getCode());
		mm.setMonitorTimes(1);
		monitorList.add(mm);
		return monitorList;
	}
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.deptHonor.dao.MedDeptHonorWinnerMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.deptHonor.model.MedDeptHonorWinner">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="honor_id" jdbcType="VARCHAR" property="honorId" />
    <result column="user_code" jdbcType="VARCHAR" property="userCode" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="post_name" jdbcType="VARCHAR" property="postName" />
    <result column="sex" jdbcType="VARCHAR" property="sex" />
  </resultMap>
</mapper>
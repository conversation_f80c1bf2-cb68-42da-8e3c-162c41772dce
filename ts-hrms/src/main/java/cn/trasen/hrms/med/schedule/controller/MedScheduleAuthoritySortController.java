package cn.trasen.hrms.med.schedule.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.schedule.model.MedScheduleAuthoritySort;
import cn.trasen.hrms.med.schedule.service.MedScheduleAuthoritySortService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedScheduleAuthoritySortController
 * @Description TODO
 * @date 2025��3��31�� ����2:30:24
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "排班人员排序-新")
public class MedScheduleAuthoritySortController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedScheduleAuthoritySortController.class);

	@Autowired
	private MedScheduleAuthoritySortService medScheduleAuthoritySortService;

	/**
	 * @Title saveMedScheduleAuthoritySort
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��31�� ����2:30:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/authoritySort/save")
	public PlatformResult<String> saveMedScheduleAuthoritySort(@RequestBody MedScheduleAuthoritySort record) {
		try {
			medScheduleAuthoritySortService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedScheduleAuthoritySort
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��31�� ����2:30:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/authoritySort/update")
	public PlatformResult<String> updateMedScheduleAuthoritySort(@RequestBody MedScheduleAuthoritySort record) {
		try {
			medScheduleAuthoritySortService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedScheduleAuthoritySortById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedScheduleAuthoritySort>
	 * @date 2025��3��31�� ����2:30:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/authoritySort/{id}")
	public PlatformResult<MedScheduleAuthoritySort> selectMedScheduleAuthoritySortById(@PathVariable String id) {
		try {
			MedScheduleAuthoritySort record = medScheduleAuthoritySortService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedScheduleAuthoritySortById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��3��31�� ����2:30:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/authoritySort/delete/{id}")
	public PlatformResult<String> deleteMedScheduleAuthoritySortById(@PathVariable String id) {
		try {
			medScheduleAuthoritySortService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedScheduleAuthoritySortList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleAuthoritySort>
	 * @date 2025��3��31�� ����2:30:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/authoritySort/list")
	public DataSet<MedScheduleAuthoritySort> selectMedScheduleAuthoritySortList(Page page, MedScheduleAuthoritySort record) {
		return medScheduleAuthoritySortService.getDataSetList(page, record);
	}
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.crisisValue.dao.MedCrisisValueMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.crisisValue.model.MedCrisisValue">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="visit_id" jdbcType="VARCHAR" property="visitId" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
    <result column="visit_no" jdbcType="VARCHAR" property="visitNo" />
    <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sex_name" jdbcType="VARCHAR" property="sexName" />
    <result column="age" jdbcType="VARCHAR" property="age" />
    <result column="bed_no" jdbcType="VARCHAR" property="bedNo" />
    <result column="tel" jdbcType="VARCHAR" property="tel" />
    <result column="request_dept_name" jdbcType="VARCHAR" property="requestDeptName" />
    <result column="diagnosis_name" jdbcType="VARCHAR" property="diagnosisName" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="order_item_name" jdbcType="VARCHAR" property="orderItemName" />
    <result column="panic_value" jdbcType="VARCHAR" property="panicValue" />
    <result column="report_date" jdbcType="VARCHAR" property="reportDate" />
    <result column="sign_user_name" jdbcType="VARCHAR" property="signUserName" />
    <result column="sign_dept_name" jdbcType="VARCHAR" property="signDeptName" />
    <result column="bed_doctor_id" jdbcType="VARCHAR" property="bedDoctorId" />
    <result column="bed_doctor_name" jdbcType="VARCHAR" property="bedDoctorName" />
    <result column="main_doctor_id" jdbcType="VARCHAR" property="mainDoctorId" />
    <result column="main_doctor_name" jdbcType="VARCHAR" property="mainDoctorName" />
    <result column="chief_doctor_id" jdbcType="VARCHAR" property="chiefDoctorId" />
    <result column="chief_doctor_name" jdbcType="VARCHAR" property="chiefDoctorName" />
    <result column="receive_status" jdbcType="VARCHAR" property="receiveStatus" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="process_date" jdbcType="VARCHAR" property="processDate" />
    <result column="process_desc" jdbcType="VARCHAR" property="processDesc" />
    <result column="process_user_id" jdbcType="VARCHAR" property="processUserId" />
    <result column="process_user_name" jdbcType="VARCHAR" property="processUserName" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
  </resultMap>
  
  <select id="getDataSetList" resultType="cn.trasen.hrms.med.crisisValue.model.MedCrisisValue" parameterType="cn.trasen.hrms.med.crisisValue.model.MedCrisisValue">
  		select t1.* from med_crisis_value t1
		left join med_his_employee t2 on t1.bed_doctor_id = t2.employee_id
		LEFT JOIN cust_emp_base t3 on t2.employee_no = t3.his_employee_no 
		where t1.is_deleted = 'N' and t1.sso_org_code = #{ssoOrgCode}
		<if test="index != null and index != '' and index == '1'.toString()">
			and t3.org_id = #{orgId}
		</if>
		<if test="index != null and index != '' and index == '2'.toString()">
			and t3.org_id = #{orgId} and t1.status = '0'
		</if>
		<if test="index != null and index != '' and index == '3'.toString()">
			and t3.org_id = #{orgId} and t1.status = '1'
		</if>
		<if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
			and t1.report_date between concat(#{startDate},' 00:00:00') and concat(#{endDate},' 23:59:59')
		</if>
		<if test="status != null and status != ''">
			and t1.status = #{status}
		</if>
		<if test="visitType != null and visitType != ''">
			and t1.visit_type = #{visitType}
		</if>
		<if test="requestDeptName != null and requestDeptName != ''">
			and t1.request_dept_name like concat('%',#{requestDeptName},'%')
		</if>
		<if test="signUserName != null and signUserName != ''">
			and t1.sign_user_name like concat('%',#{signUserName},'%')
		</if>
		<if test="signDeptName != null and signDeptName != ''">
			and t1.sign_dept_name like concat('%',#{signDeptName},'%')
		</if>
		<if test="bedDoctorName != null and bedDoctorName != ''">
			and t1.bed_doctor_name like concat('%',#{bedDoctorName},'%')
		</if>
		<if test="processUserName != null and processUserName != ''">
			and t1.process_user_name like concat('%',#{processUserName},'%')
		</if>
		<if test="searchKey != null and searchKey != ''">
			 and (
			 	t1.name like concat('%',#{searchKey},'%')  
			 	or t1.visit_no like concat('%',#{searchKey},'%')
			 	or t1.bed_no like concat('%',#{searchKey},'%')
			 )
		</if>
  </select>
  
  <select id="indexStatistics" resultType="Map" parameterType="cn.trasen.hrms.med.crisisValue.model.MedCrisisValue">
  		select 
		(
		select count(1) from med_crisis_value
		where is_deleted = 'N' and sso_org_code = #{ssoOrgCode}
		<if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
			and report_date between concat(#{startDate},' 00:00:00') and concat(#{endDate},' 23:59:59')
		</if> 
		) as reportNumbers,
		(
		select count(1) from med_crisis_value
		where is_deleted = 'N' and status = '1'  and sso_org_code = #{ssoOrgCode}
		<if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
			and report_date between concat(#{startDate},' 00:00:00') and concat(#{endDate},' 23:59:59')
		</if>
		) as handleNumbers,
		(
		select count(1) from med_crisis_value
		where is_deleted = 'N' and status in ('-1','0')  and sso_org_code = #{ssoOrgCode}
		<if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
			and report_date between concat(#{startDate},' 00:00:00') and concat(#{endDate},' 23:59:59')
		</if>
		) as unHandleNumbers
		from dual
  </select>
  
  <select id="tableStatistics" resultType="Map" parameterType="cn.trasen.hrms.med.crisisValue.model.MedCrisisValue">
  		select 
			(
			select count(1) from med_crisis_value t1
				left join med_his_employee t2 on t1.bed_doctor_id = t2.employee_id
				LEFT JOIN cust_emp_base t3 on t2.employee_no = t3.his_employee_no 
				where 1=1 and t3.org_id = #{orgId}
			) as reportNumbers,
			(
			select count(1) from med_crisis_value t1
				left join med_his_employee t2 on t1.bed_doctor_id = t2.employee_id
				LEFT JOIN cust_emp_base t3 on t2.employee_no = t3.his_employee_no 
				where 1=1 and t3.org_id = #{orgId} and t1.status = '0'
			) as unHandleNumbers,
			(
			select count(1) from med_crisis_value t1
				left join med_his_employee t2 on t1.bed_doctor_id = t2.employee_id
				LEFT JOIN cust_emp_base t3 on t2.employee_no = t3.his_employee_no 
				where 1=1 and t3.org_id = #{orgId} and t1.status = '1'
			) as handleNumbers
			from dual
  </select>
  
  <select id="selectMyCrisisValue" resultType="Long" parameterType="String">
  			select count(1) from med_crisis_value t1
			left join med_his_employee t2 on t1.bed_doctor_id = t2.employee_id
			LEFT JOIN cust_emp_base t3 on t2.employee_no = t3.his_employee_no 
			where t3.employee_no = #{currentUserCode}
  </select>
</mapper>
package cn.trasen.hrms.med.radiate.controller;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.core.date.DateUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.radiate.model.RadiateTrainingRegister;
import cn.trasen.hrms.med.radiate.service.RadiateTrainingRegisterService;
import cn.trasen.hrms.med.radiate.vo.RadiateTrainingReqVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName RadiateTrainingRegisterController
 * @Description TODO
 * @date 2025��1��8�� ����11:17:02培训状况登记
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "放射培训状况登记")
public class RadiateTrainingRegisterController {

	private transient static final Logger logger = LoggerFactory.getLogger(RadiateTrainingRegisterController.class);

	@Autowired
	private RadiateTrainingRegisterService radiateTrainingRegisterService;

	/**
	 * @Title saveRadiateTrainingRegister
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:17:02
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/RadiateTrainingRegister/save")
	public PlatformResult<String> saveRadiateTrainingRegister(@RequestBody RadiateTrainingRegister record) {
		try {
			radiateTrainingRegisterService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateRadiateTrainingRegister
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:17:02
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/RadiateTrainingRegister/update")
	public PlatformResult<String> updateRadiateTrainingRegister(@RequestBody RadiateTrainingRegister record) {
		try {
			radiateTrainingRegisterService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectRadiateTrainingRegisterById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<RadiateTrainingRegister>
	 * @date 2025��1��8�� ����11:17:02
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/RadiateTrainingRegister/{id}")
	public PlatformResult<RadiateTrainingRegister> selectRadiateTrainingRegisterById(@PathVariable String id) {
		try {
			RadiateTrainingRegister record = radiateTrainingRegisterService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteRadiateTrainingRegisterById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:17:02
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/RadiateTrainingRegister/delete/{id}")
	public PlatformResult<String> deleteRadiateTrainingRegisterById(@PathVariable String id) {
		try {
			radiateTrainingRegisterService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectRadiateTrainingRegisterList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RadiateTrainingRegister>
	 * @date 2025��1��8�� ����11:17:02
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/RadiateTrainingRegister/list")
	public DataSet<RadiateTrainingRegister> selectRadiateTrainingRegisterList(Page page, RadiateTrainingRegister record) {
		return radiateTrainingRegisterService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectRadiateTrainingRegisterPageList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RadiateTrainingRegister>
	 * @date 2025��1��8�� ����11:17:02
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/RadiateTrainingRegister/PageList")
	public DataSet<RadiateTrainingRegister> selectRadiateTrainingRegisterPageList(Page page, RadiateTrainingReqVo record) {
		String orgIds = record.getOrgIds();
		if(!ObjectUtils.isEmpty(orgIds)){
			record.setOrgIdList(Arrays.asList(orgIds.split(",")));
		}
		return radiateTrainingRegisterService.selectRadiateTrainingRegisterPageList(page, record);
	}
	
	/**
	 * 
	 * @param request
	 * @param response
	 * @param page
	 * @param record
	 */
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/RadiateTrainingRegister/export")
    public void export(HttpServletRequest request, HttpServletResponse response, Page page, RadiateTrainingReqVo record) {
		String orgIds = record.getOrgIds();
		if(!ObjectUtils.isEmpty(orgIds)){
			record.setOrgIdList(Arrays.asList(orgIds.split(",")));
		}
		page.setPageNo(1);
		page.setPageSize(Integer.MAX_VALUE);
		String name = "培训状况信息表" + DateUtil.format(new Date(),"yyyyMMdd") + ".xls";
		String templateUrl = "template/radiate/trainingRegisterExport.xls";
		try {
			DataSet<RadiateTrainingRegister> dataSetList = radiateTrainingRegisterService.selectRadiateTrainingRegisterPageList(page, record);
			List<RadiateTrainingRegister> list = dataSetList.getRows();
            if (CollectionUtils.isNotEmpty(list)) {
				ExportUtil.export(request, response, list, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
	
	/**
	 * 导入
	 * @param file
	 * @return
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "导入数据", notes = "导入数据")
	@PostMapping(value = "/api/RadiateTrainingRegister/import")
	public PlatformResult importDate(@RequestParam("file") MultipartFile file) {
		 try {
			 	List<RadiateTrainingRegister> list = (List<RadiateTrainingRegister>) ImportExcelUtil.getExcelDatas(file, RadiateTrainingRegister.class);

	            if (!list.isEmpty()) {
	            	
	            	return radiateTrainingRegisterService.importData(list);
	            	
	            } else {
	                return PlatformResult.failure("数据为空");
	            }

	        } catch (Exception e) {
	            e.printStackTrace();
	            logger.error(e.getMessage(), e);
	            return PlatformResult.failure("导入数据失败，失败原因:" + e.getMessage());
	        }
	}
}

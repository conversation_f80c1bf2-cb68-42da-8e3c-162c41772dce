package cn.trasen.hrms.med.schedule.model;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ScheduleDept {
	
	@ApiModelProperty(value = "所属科室ID")
	private String deptId;
	
	@ApiModelProperty(value = "所属科室编码")
	private String deptCode;
	
	@ApiModelProperty(value = "所属科室名称")
	private String deptName;
	
//	@ApiModelProperty(value = "出勤人数")
//	private Integer attendanceUserCount;
//	
//	@ApiModelProperty(value = "排班集合")
//	private List<MedScheduleRecord> scheduleRecords;
//    
//    @ApiModelProperty(value = "实际出勤天数")
//    private BigDecimal actualAttendance;

}

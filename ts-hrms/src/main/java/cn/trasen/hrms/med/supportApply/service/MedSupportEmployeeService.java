package cn.trasen.hrms.med.supportApply.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.supportApply.model.MedSupportEmployee;

/**
 * @ClassName MedSupportEmployeeService
 * @Description TODO
 * @date 2025��1��4�� ����5:27:58
 * <AUTHOR>
 * @version 1.0
 */
public interface MedSupportEmployeeService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��1��4�� ����5:27:58
	 * <AUTHOR>
	 */
	Integer save(MedSupportEmployee record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��1��4�� ����5:27:58
	 * <AUTHOR>
	 */
	Integer update(MedSupportEmployee record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��1��4�� ����5:27:58
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedSupportEmployee
	 * @date 2025��1��4�� ����5:27:58
	 * <AUTHOR>
	 */
	MedSupportEmployee selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedSupportEmployee>
	 * @date 2025��1��4�� ����5:27:58
	 * <AUTHOR>
	 */
	DataSet<MedSupportEmployee> getDataSetList(Page page, MedSupportEmployee record);

	List<MedSupportEmployee> selectByWorkflowId(String transferRecordId);

	void deleteByApplyId(String applyId);

	List<MedSupportEmployee> selectByApplyId(String applyId);
}

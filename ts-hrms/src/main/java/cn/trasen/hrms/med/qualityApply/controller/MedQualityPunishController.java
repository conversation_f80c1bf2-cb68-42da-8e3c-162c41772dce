package cn.trasen.hrms.med.qualityApply.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.qualityApply.model.MedQualityPunish;
import cn.trasen.hrms.med.qualityApply.service.MedQualityPunishService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedQualityPunishController
 * @Description TODO
 * @date 2025��2��27�� ����11:15:32
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "质控专员处罚信息")
public class MedQualityPunishController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedQualityPunishController.class);

	@Autowired
	private MedQualityPunishService medQualityPunishService;

	/**
	 * @Title saveMedQualityPunish
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��2��27�� ����11:15:32
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/qualityPunis/save")
	public PlatformResult<String> saveMedQualityPunish(@RequestBody MedQualityPunish record) {
		try {
			medQualityPunishService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedQualityPunish
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��2��27�� ����11:15:32
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/qualityPunis/update")
	public PlatformResult<String> updateMedQualityPunish(@RequestBody MedQualityPunish record) {
		try {
			medQualityPunishService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedQualityPunishById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedQualityPunish>
	 * @date 2025��2��27�� ����11:15:32
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/qualityPunis/{id}")
	public PlatformResult<MedQualityPunish> selectMedQualityPunishById(@PathVariable String id) {
		try {
			MedQualityPunish record = medQualityPunishService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedQualityPunishById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��2��27�� ����11:15:32
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/qualityPunis/delete/{id}")
	public PlatformResult<String> deleteMedQualityPunishById(@PathVariable String id) {
		try {
			medQualityPunishService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedQualityPunishList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedQualityPunish>
	 * @date 2025��2��27�� ����11:15:32
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/qualityPunis/list")
	public DataSet<MedQualityPunish> selectMedQualityPunishList(Page page, MedQualityPunish record) {
		return medQualityPunishService.getDataSetList(page, record);
	}
}

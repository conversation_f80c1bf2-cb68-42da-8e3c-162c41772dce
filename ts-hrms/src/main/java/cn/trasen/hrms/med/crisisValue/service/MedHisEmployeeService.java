package cn.trasen.hrms.med.crisisValue.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.crisisValue.model.MedHisEmployee;

/**
 * @ClassName MedHisEmployeeService
 * @Description TODO
 * @date 2025��5��26�� ����4:18:09
 * <AUTHOR>
 * @version 1.0
 */
public interface MedHisEmployeeService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��5��26�� ����4:18:09
	 * <AUTHOR>
	 */
	Integer save(MedHisEmployee record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��5��26�� ����4:18:09
	 * <AUTHOR>
	 */
	Integer update(MedHisEmployee record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��5��26�� ����4:18:09
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedHisEmployee
	 * @date 2025��5��26�� ����4:18:09
	 * <AUTHOR>
	 */
	MedHisEmployee selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedHisEmployee>
	 * @date 2025��5��26�� ����4:18:09
	 * <AUTHOR>
	 */
	DataSet<MedHisEmployee> getDataSetList(Page page, MedHisEmployee record);

	/**
	 * 
	 * @param currentUserCode
	 * @return
	 */
	MedHisEmployee selectHisEmployee(String currentUserCode,String hisEmployeeId);

	String selectPlatformOrgType();
}

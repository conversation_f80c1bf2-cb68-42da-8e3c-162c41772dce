<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.qualityApply.dao.MedQualityApplyMapper">
  
  
  <select id="getDataSetList" resultType="cn.trasen.hrms.med.qualityApply.model.MedQualityApply" parameterType="cn.trasen.hrms.med.qualityApply.model.MedQualityApply">
  			SELECT
			    t1.*,
			    t2.name as orgName,
			    COUNT(t4.id) as punishNumbers,
			    MAX(t4.punish_end_date) as punishEndDate,
			    <choose>
					<when test="_databaseId=='dm' or _databaseId=='oracle' or _databaseId=='kingbase'">
						 WM_CONCAT(t4.punish_result) as punishResultText
					</when>
					<otherwise>
				         group_concat(t4.punish_result) as punishResultText
					</otherwise>
			   </choose>
			FROM
			    med_quality_apply t1
			LEFT JOIN comm_organization t2 ON t1.apply_org_id = t2.organization_id
			LEFT JOIN med_quality_punish t4 ON t4.quality_id = t1.id
			WHERE
			    t1.is_deleted = 'N'
			<if test="applyName != null and applyName != ''">
				and (
					apply_name like concat('%',#{applyName},'%')
					or apply_idcard like concat('%',#{applyName},'%')
				)  
			</if>
			<if test="applyCode != null and applyCode != ''">
				and apply_code = #{applyCode}
			</if>
			<if test="applyStatus != null and applyStatus != ''">
				and apply_status = #{applyStatus}
			</if>
			<if test="expireStatus != null and expireStatus != ''">
				and expire_status = #{expireStatus}
			</if>
			<if test="isOverdue != null and isOverdue != ''">
				and is_overdue = #{isOverdue}
			</if>
			<if test="applyArea != null and applyArea != ''">
				and apply_area = #{applyArea}
			</if>
			<if test="applyResult != null and applyResult != ''">
				and apply_result = #{applyResult}
			</if>
			<if test="applyTechnical != null and applyTechnical != ''">
				and apply_technical like concat('%',#{applyTechnical},'%')
			</if>
			<!-- <if test="holdArea != null and holdArea != ''">
				and hold_area = #{holdArea}
			</if>
			<if test="holdOrgId != null and holdOrgId != ''">
				and hold_org_id like concat('%',#{holdOrgId},'%')
			</if> -->
			<if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
				and apply_start_date &lt;= #{endDate} and apply_search_date >= #{startDate}
  			</if>
  			<if test="optStartDate != null and optStartDate != '' and optEndDate != null and optEndDate != ''">
				and opt_date between concat(#{optStartDate},' 00:00') and concat(#{optEndDate},' 23:59')
  			</if>
  			<!-- <if test="realStartDate != null and realStartDate != '' and realEndDate != null and realEndDate != ''">
				and apply_real_date between concat(#{realStartDate},' 00:00') and concat(#{realEndDate},' 23:59')
  			</if> -->
  			<if test="orgIdList != null and orgIdList.size() > 0">
	        	 and apply_org_id in
		        <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
        	</if>
  			<if test="orgIds != null and orgIds.size() > 0">
	        	 and apply_org_id in
		        <foreach collection="orgIds" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
        	</if>
        	<!-- <if test="holdOrgIds != null and holdOrgIds.size() > 0">
	        	 and hold_org_id in
		        <foreach collection="holdOrgIds" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
        	</if> -->
        	GROUP BY
			    t1.id
  </select>
  
  <select id="selectOrgName" parameterType="String" resultType="String"> 
  		select name FROM comm_organization
  		WHERE organization_id = #{applyOrgId}
  </select>
  
  <select id="getNoDataApplyDept" resultType="Map">
  		SELECT organization_id,name,parent_id,
			CASE 
				 WHEN t1.HOSP_CODE = '1' THEN '天心阁院区'
				 WHEN t1.HOSP_CODE = '2' THEN '岳麓区院区'
				 WHEN t1.HOSP_CODE = '3' THEN '马王堆院区'
				 WHEN t1.HOSP_CODE = '4' THEN '蓉园院区'
			END hospName
			FROM comm_organization t1
			WHERE is_deleted = 'N' AND is_enable = '1' AND ORG_TYPE IN ('001','004')
			AND NOT EXISTS (
			      SELECT 1 
			      FROM comm_organization t2 
			      WHERE t2.parent_id = t1.organization_id AND t2.is_deleted = 'N' AND t2.is_enable = '1'     
			)
			AND t1.organization_id NOT IN  (
				SELECT apply_org_id FROM  med_quality_apply t2
				WHERE is_deleted = 'N' AND apply_status = '0'
			)
		ORDER BY name
  </select>
</mapper>
package cn.trasen.hrms.med.delivery.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.core.date.DateUtil;
import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.delivery.model.MedDeliveryDict;
import cn.trasen.hrms.med.delivery.service.MedDeliveryDictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedDeliveryDictController
 * @Description TODO
 * @date 2025��5��20�� ����3:31:11
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "外送检验项目字典")
public class MedDeliveryDictController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedDeliveryDictController.class);

	@Autowired
	private MedDeliveryDictService medDeliveryDictService;

	/**
	 * @Title saveMedDeliveryDict
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��5��20�� ����3:31:11
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/medDeliveryDict/save")
	public PlatformResult<String> saveMedDeliveryDict(@RequestBody MedDeliveryDict record) {
		try {
			medDeliveryDictService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedDeliveryDict
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��5��20�� ����3:31:11
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/medDeliveryDict/update")
	public PlatformResult<String> updateMedDeliveryDict(@RequestBody MedDeliveryDict record) {
		try {
			medDeliveryDictService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedDeliveryDictById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedDeliveryDict>
	 * @date 2025��5��20�� ����3:31:11
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/medDeliveryDict/{id}")
	public PlatformResult<MedDeliveryDict> selectMedDeliveryDictById(@PathVariable String id) {
		try {
			MedDeliveryDict record = medDeliveryDictService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedDeliveryDictById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��5��20�� ����3:31:11
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/medDeliveryDict/delete/{id}")
	public PlatformResult<String> deleteMedDeliveryDictById(@PathVariable String id) {
		try {
			medDeliveryDictService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedDeliveryDictList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedDeliveryDict>
	 * @date 2025��5��20�� ����3:31:11
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/medDeliveryDict/list")
	public DataSet<MedDeliveryDict> selectMedDeliveryDictList(Page page, MedDeliveryDict record) {
		return medDeliveryDictService.getDataSetList(page, record);
	}
	
	@GetMapping(value = "/api/medDeliveryDict/downloadTemplate")
    @ApiOperation(value = "导入模版下载", notes = "导入模版下载")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            String filename = "外送检验项目导入模板.xlsx";
            String template = "template/importMedDeliveryDict.xlsx";
            ClassPathResource resource = new ClassPathResource(template);
            exportExcelUtil.downloadExportExcel(filename, response, resource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
	
	@ApiOperation(value = "导入模板数据", notes = "导入模板数据")
	@PostMapping(value = "/api/medDeliveryDict/importMedDeliveryDict")
	public PlatformResult importMedDeliveryDict(@RequestParam("file") MultipartFile file) {
		
		 try {
			 	List<MedDeliveryDict> list = (List<MedDeliveryDict>) ImportExcelUtil.getExcelDatas(file, MedDeliveryDict.class);

	            if (!list.isEmpty()) {
	            	
	            	return medDeliveryDictService.importMedDeliveryDict(list);
	            	
	            } else {
	                return PlatformResult.failure("数据为空");
	            }

	        } catch (Exception e) {
	            e.printStackTrace();
	            logger.error(e.getMessage(), e);
	            return PlatformResult.failure("导入数据失败，失败原因:" + e.getMessage());
	        }
	}
	
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/medDeliveryDict/export")
	public void export(Page page,HttpServletResponse response, HttpServletRequest request,MedDeliveryDict record) {
		try {
			page.setPageNo(1);
			page.setPageSize(Integer.MAX_VALUE);
			
			DataSet<MedDeliveryDict> dataSet = medDeliveryDictService.getDataSetList(page, record);
			List<MedDeliveryDict> list = dataSet.getRows();
			
			int i = 1;
			for (MedDeliveryDict medDeliveryDict : list) {
				medDeliveryDict.setOrderNumber(i);
				i++;
			}
			
			//表头标题
            String name = "外送检验项目信息表" + DateUtil.format(new Date(),"yyyyMMdd") + ".xlsx";
            // 模板位置
            String templateUrl = "template/medDeliveryDictExport.xlsx";
            
            Map<String, Object> map = new HashMap<String, Object>();
		    map.put("list", list);
		    map.put("exportDate", DateUtil.format(new Date(),"yyyy-MM-dd"));
		    map.put("exportUserName", UserInfoHolder.getCurrentUserName());
            
            cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, map, name, templateUrl);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}

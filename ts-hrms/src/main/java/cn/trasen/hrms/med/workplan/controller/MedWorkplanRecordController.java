package cn.trasen.hrms.med.workplan.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.deptHonor.model.MedDeptHonorWinner;
import cn.trasen.hrms.med.integral.model.MedIntegralRecord;
import cn.trasen.hrms.med.workplan.model.MedWorkplanPlayer;
import cn.trasen.hrms.med.workplan.model.MedWorkplanRecord;
import cn.trasen.hrms.med.workplan.service.MedWorkplanRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedWorkplanRecordController
 * @Description TODO
 * @date 2024��11��12�� ����11:52:20
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "工作计划管理")
public class MedWorkplanRecordController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedWorkplanRecordController.class);

	@Autowired
	private MedWorkplanRecordService medWorkplanRecordService;

	/**
	 * @Title saveMedWorkplanRecord
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��11��12�� ����11:52:20
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/workplanRecord/save")
	public PlatformResult<String> saveMedWorkplanRecord(@RequestBody MedWorkplanRecord record) {
		try {
			medWorkplanRecordService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedWorkplanRecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��11��12�� ����11:52:20
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/workplanRecord/update")
	public PlatformResult<String> updateMedWorkplanRecord(@RequestBody MedWorkplanRecord record) {
		try {
			medWorkplanRecordService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "审核", notes = "审核")
	@PostMapping("/api/workplanRecord/examine")
	public PlatformResult<String> examine(@RequestBody List<MedWorkplanRecord> record) {
		try {
			medWorkplanRecordService.examine(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedWorkplanRecordById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedWorkplanRecord>
	 * @date 2024��11��12�� ����11:52:20
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/workplanRecord/{id}")
	public PlatformResult<MedWorkplanRecord> selectMedWorkplanRecordById(@PathVariable String id) {
		try {
			MedWorkplanRecord record = medWorkplanRecordService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedWorkplanRecordById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��11��12�� ����11:52:20
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/workplanRecord/delete/{id}")
	public PlatformResult<String> deleteMedWorkplanRecordById(@PathVariable String id) {
		try {
			medWorkplanRecordService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedWorkplanRecordList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedWorkplanRecord>
	 * @date 2024��11��12�� ����11:52:20
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/workplanRecord/list")
	public DataSet<MedWorkplanRecord> selectMedWorkplanRecordList(Page page, MedWorkplanRecord record) {
		return medWorkplanRecordService.getDataSetList(page, record);
	}
	
	/**
	 * 
	 * @param response
	 */
	@GetMapping(value = "/api/workplanRecord/downloadTemplate")
    @ApiOperation(value = "导入模版下载", notes = "导入模版下载")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            String filename = "参与人员导入模板.xlsx";
            String template = "template/importMedWorkplanPlayer.xlsx";
            ClassPathResource resource = new ClassPathResource(template);
            exportExcelUtil.downloadExportExcel(filename, response, resource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
	
	@ApiOperation(value = "获取模板数据", notes = "获取模板数据")
	@PostMapping(value = "/api/workplanRecord/importMedWorkplanPlayer")
	public PlatformResult<List<MedWorkplanPlayer>> importMedWorkplanPlayer(@RequestParam("file") MultipartFile file) {
		
		 try {
			 	List<MedWorkplanPlayer> list = (List<MedWorkplanPlayer>) ImportExcelUtil.getExcelDatas(file, MedWorkplanPlayer.class);

	            if (!list.isEmpty()) {
	                return PlatformResult.success(list);
	            } else {
	                return PlatformResult.failure("数据为空");
	            }

	        } catch (Exception e) {
	            e.printStackTrace();
	            logger.error(e.getMessage(), e);
	            return PlatformResult.failure("导入数据失败，失败原因:" + e.getMessage());
	        }
	}
	
	/**
	 * 
	 * @param request
	 * @param response
	 * @param page
	 * @param record
	 */
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/workplanRecord/export")
    public void export(HttpServletRequest request, HttpServletResponse response,Page page,MedWorkplanRecord record) {
		
		page.setPageSize(Integer.MAX_VALUE);

		String name = "工作计划管理.xls";
		
		String templateUrl = "template/workplanRecord.xls";
		
		try {
			DataSet<MedWorkplanRecord> dataSetList = medWorkplanRecordService.getDataSetList(page, record);
            
            List<MedWorkplanRecord> list = dataSetList.getRows();
            
            ExportUtil.export(request, response, list, name, templateUrl);
            
		} catch (Exception e) {
			e.printStackTrace();
		}

    }
}

package cn.trasen.hrms.med.patient.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "med_patient_operation")
@Setter
@Getter
public class PatientOperation {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 患者主键
     */
    @Column(name = "patn_id")
    @ApiModelProperty(value = "患者主键")
    private String patnId;

    /**
     * 患者住院号
     */
    @Column(name = "patn_no")
    @ApiModelProperty(value = "患者住院号")
    private String patnNo;
    
    @Column(name = "ysssrq")
    @ApiModelProperty(value = "拟施手术日期")
    private Date ysssrq;

    /**
     * 手术日期
     */
    @Column(name = "operation_date")
    @ApiModelProperty(value = "手术日期")
    private Date operationDate;

    /**
     * 手术名称
     */
    @Column(name = "operation_name")
    @ApiModelProperty(value = "手术名称")
    private String operationName;

    /**
     * 术后诊断
     */
    @Column(name = "operation_after_diagnosis")
    @ApiModelProperty(value = "术后诊断")
    private String operationAfterDiagnosis;

    /**
     * 主刀医生
     */
    @ApiModelProperty(value = "主刀医生")
    private String zdys;

    /**
     * 麻醉医生
     */
    @ApiModelProperty(value = "麻醉医生")
    private String mzys;
    
    /**
     * 完成标记
     */
    @ApiModelProperty(value = "安排标记")
    private String apbj;
    
    /**
     * 完成标记
     */
    @ApiModelProperty(value = "完成标记")
    private String wcbj;
    
    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private Date wcsj;
    
    /**
     * 切口类型
     */
    @ApiModelProperty(value = "切口类型")
    private String qklx;
    
    /**
     * 手术等级
     */
    @ApiModelProperty(value = "手术等级")
    private String yxssdj;
    
    /**
     * 删除标记
     */
    @ApiModelProperty(value = "删除标记")
    private String bdelete;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.delivery.dao.MedDeliveryRecordMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.delivery.model.MedDeliveryRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="workflow_id" jdbcType="VARCHAR" property="workflowId" />
    <result column="dict_id" jdbcType="VARCHAR" property="dictId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="apply_name" jdbcType="VARCHAR" property="applyName" />
    <result column="apply_tel" jdbcType="VARCHAR" property="applyTel" />
    <result column="in_patient_no" jdbcType="VARCHAR" property="inPatientNo" />
    <result column="sex" jdbcType="VARCHAR" property="sex" />
    <result column="age" jdbcType="VARCHAR" property="age" />
    <result column="diagnosis" jdbcType="VARCHAR" property="diagnosis" />
    <result column="illness" jdbcType="VARCHAR" property="illness" />
    <result column="sample_type" jdbcType="VARCHAR" property="sampleType" />
    <result column="sample_dose" jdbcType="VARCHAR" property="sampleDose" />
    <result column="test_org" jdbcType="VARCHAR" property="testOrg" />
    <result column="cost" jdbcType="VARCHAR" property="cost" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="connotation" jdbcType="VARCHAR" property="connotation" />
    <result column="apply_date" jdbcType="VARCHAR" property="applyDate" />
    <result column="apply_org" jdbcType="VARCHAR" property="applyOrg" />
    <result column="apply_area" jdbcType="VARCHAR" property="applyArea" />
    <result column="create_date" jdbcType="VARCHAR" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
  </resultMap>
  
  <select id= "getDataSetList" parameterType="cn.trasen.hrms.med.delivery.model.MedDeliveryRecord" resultType="cn.trasen.hrms.med.delivery.model.MedDeliveryRecord">
  		select t1.* from med_delivery_record t1
		left join  cust_emp_base t2  ON t1.create_user  = t2.employee_no
		where t1.is_deleted = 'N'
  		<if test="projectName != null and projectName != ''">
  			and t1.project_name like concat('%',#{projectName},'%')
  		</if>
  		<if test="searchKey != null and searchKey != ''">
  			and (
				t1.in_patient_no like concat('%',#{searchKey},'%')
				or t1.patient_name like concat('%',#{searchKey},'%')
			) 
  		</if>
  		<if test="applyArea != null and applyArea != ''">
  			and t1.apply_area like concat('%',#{applyArea},'%')
  		</if>
  		<if test="applyName != null and applyName != ''">
  			and t1.apply_name like concat('%',#{applyName},'%')
  		</if>
  		<if test="applyDateStart != null and applyDateStart != '' and applyDateEnd != null and applyDateEnd != ''">
  			and t1.apply_date between #{applyDateStart} and #{applyDateEnd}
  		</if>
  		<if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
  			and t1.create_date between concat(#{startDate},' 00:00:00') and concat(#{endDate},' 23:59:59')
  		</if>
  		<if test="testOrg != null and testOrg != ''">
  			and t1.test_org like concat('%',#{testOrg},'%')
  		</if>
  		<if test="sampleType != null and sampleType != ''">
  			and t1.sample_type like concat('%',#{sampleType},'%')
  		</if>
  		<if test="orgIdList != null and orgIdList.size() > 0">
        	 and t2.org_id in
	        <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
      	</if>
  </select>
</mapper>
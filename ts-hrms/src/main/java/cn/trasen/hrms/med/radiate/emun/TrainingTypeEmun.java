package cn.trasen.hrms.med.radiate.emun;

/**
 * 培训类型
 * <AUTHOR>
 *
 */
public enum TrainingTypeEmun {
	
	RADIATION_PROTECTION("1", "放射防护培训"),  
	RADIATION_SAFETY ("2", "辐射安全培训");
	
	private String name;

	private String code;

	TrainingTypeEmun(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	

	/**
	 * 根据编码获取name
	 * 
	 * @param code
	 * @return
	 */
	public static String convert(String code) {
		for (TrainingTypeEmun emun : values()) {
			if (emun.getCode().equals(code))
				return emun.name;
		}
		return null;
	}

}

package cn.trasen.hrms.med.radiate.vo;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 放射诊疗人员查询参照
 * @date 2025-05-22 11:52:53
 * <AUTHOR> @version 1.0
 */
@Setter
@Getter
public class RadiatePersonnelReqVo {

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "拟从事放射类别")
    private String radiationType;
    
    @ApiModelProperty(value = "状态")
    private String employeeStatus;
    
    @ApiModelProperty(value = "学历")
    private String education;

    @ApiModelProperty(value = "技术职称")
    private String technicalTitle;
    
    @ApiModelProperty(value = "人员科室id")
    private String orgId;

    @ApiModelProperty(value = "人员工号")
    private String employeeNo;

    @ApiModelProperty(value = "人员id")
    private String employeeId;

    @ApiModelProperty(value = "人员姓名")
    private String employeeName;
    
    @ApiModelProperty(value = "拟从事介入手术类别")
    private String operationType;

    @ApiModelProperty(value = "体检结果:0异常,1正常")
    private String checkupStatus;

    @ApiModelProperty(value = "体检到期状态：0-未到期，1-已到期")
    private String checkupEndStatus;

    @ApiModelProperty(value = "所属科室ID多个逗号隔开")
    private String orgIds;

    @ApiModelProperty(value = "所属科室ID集合")
    private List<String> orgIdList;
    
    @ApiModelProperty(value = "搜索关键字-工号/姓名")
    private String condition;
    
    @ApiModelProperty(value = "是否导出，默认false")
    private boolean export;
    
    @ApiModelProperty(value = "系统当前时间：yyyy-MM-dd")
    private String today;
    
    private String ssoOrgCode;
    
}

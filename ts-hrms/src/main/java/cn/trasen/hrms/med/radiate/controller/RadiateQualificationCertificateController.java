package cn.trasen.hrms.med.radiate.controller;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.core.date.DateUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.radiate.model.RadiateQualificationCertificate;
import cn.trasen.hrms.med.radiate.service.RadiateQualificationCertificateService;
import cn.trasen.hrms.med.radiate.vo.RadiateCertificateReqVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName RadiateQualificationCertificateController
 * @Description TODO
 * @date 2025��1��8�� ����11:16:41资质证件管理
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "放射资质证件登记")
public class RadiateQualificationCertificateController {

	private transient static final Logger logger = LoggerFactory.getLogger(RadiateQualificationCertificateController.class);

	@Autowired
	private RadiateQualificationCertificateService radiateQualificationCertificateService;

	/**
	 * @Title saveRadiateQualificationCertificate
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:16:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/RadiateQualificationCertificate/save")
	public PlatformResult<String> saveRadiateQualificationCertificate(@RequestBody RadiateQualificationCertificate record) {
		try {
			radiateQualificationCertificateService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateRadiateQualificationCertificate
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:16:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/RadiateQualificationCertificate/update")
	public PlatformResult<String> updateRadiateQualificationCertificate(@RequestBody RadiateQualificationCertificate record) {
		try {
			radiateQualificationCertificateService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectRadiateQualificationCertificateById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<RadiateQualificationCertificate>
	 * @date 2025��1��8�� ����11:16:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/RadiateQualificationCertificate/{id}")
	public PlatformResult<RadiateQualificationCertificate> selectRadiateQualificationCertificateById(@PathVariable String id) {
		try {
			RadiateQualificationCertificate record = radiateQualificationCertificateService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteRadiateQualificationCertificateById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:16:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/RadiateQualificationCertificate/delete/{id}")
	public PlatformResult<String> deleteRadiateQualificationCertificateById(@PathVariable String id) {
		try {
			radiateQualificationCertificateService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectRadiateQualificationCertificateList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RadiateQualificationCertificate>
	 * @date 2025��1��8�� ����11:16:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/RadiateQualificationCertificate/list")
	public DataSet<RadiateQualificationCertificate> selectRadiateQualificationCertificateList(Page page, RadiateQualificationCertificate record) {
		return radiateQualificationCertificateService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectRadiateQualificationCertificatePageList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RadiateQualificationCertificate>
	 * @date 2025��1��8�� ����11:16:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/RadiateQualificationCertificate/PageList")
	public DataSet<RadiateQualificationCertificate> selectRadiateQualificationCertificatePageList(Page page, RadiateCertificateReqVo record) {
		String orgIds = record.getOrgIds();
		if(!ObjectUtils.isEmpty(orgIds)){
			record.setOrgIdList(Arrays.asList(orgIds.split(",")));
		}
		return radiateQualificationCertificateService.selectRadiateQualificationCertificatePagelist(page, record);
	}
	
	/**
	 * 
	 * @param request
	 * @param response
	 * @param page
	 * @param record
	 */
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/RadiateQualificationCertificate/export")
    public void export(HttpServletRequest request, HttpServletResponse response, Page page, RadiateCertificateReqVo record) {
		String orgIds = record.getOrgIds();
		if(!ObjectUtils.isEmpty(orgIds)){
			record.setOrgIdList(Arrays.asList(orgIds.split(",")));
		}
		page.setPageNo(1);
		page.setPageSize(Integer.MAX_VALUE);
		String name = "资质证件信息表" + DateUtil.format(new Date(),"yyyyMMdd") + ".xls";
		String templateUrl = "template/radiate/qualificationCertificateExport.xls";
		try {
			DataSet<RadiateQualificationCertificate> dataSetList = radiateQualificationCertificateService.selectRadiateQualificationCertificatePagelist(page, record);
            List<RadiateQualificationCertificate> list = dataSetList.getRows();
            if (CollectionUtils.isNotEmpty(list)) {
				ExportUtil.export(request, response, list, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

    }
	
	/**
	 * 导入
	 * @param file
	 * @return
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "导入数据", notes = "导入数据")
	@PostMapping(value = "/api/RadiateQualificationCertificate/import")
	public PlatformResult importDate(@RequestParam("file") MultipartFile file) {
		 try {
			 	List<RadiateQualificationCertificate> list = (List<RadiateQualificationCertificate>) ImportExcelUtil.getExcelDatas(file, RadiateQualificationCertificate.class);

	            if (!list.isEmpty()) {
	            	
	            	return radiateQualificationCertificateService.importData(list);
	            	
	            } else {
	                return PlatformResult.failure("数据为空");
	            }

	        } catch (Exception e) {
	            e.printStackTrace();
	            logger.error(e.getMessage(), e);
	            return PlatformResult.failure("导入数据失败，失败原因:" + e.getMessage());
	        }
	}
}

package cn.trasen.hrms.med.qualityApply.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.qualityApply.dao.MedQualityPunishMapper;
import cn.trasen.hrms.med.qualityApply.model.MedQualityPunish;
import cn.trasen.hrms.med.qualityApply.service.MedQualityPunishService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedQualityPunishServiceImpl
 * @Description TODO
 * @date 2025��2��27�� ����11:15:32
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedQualityPunishServiceImpl implements MedQualityPunishService {

	@Autowired
	private MedQualityPunishMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedQualityPunish record) {
		record.setId(IdGeneraterUtils.nextId());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedQualityPunish record) {
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedQualityPunish record = new MedQualityPunish();
		record.setId(id);
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedQualityPunish selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedQualityPunish> getDataSetList(Page page, MedQualityPunish record) {
		Example example = new Example(MedQualityPunish.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<MedQualityPunish> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteByQualityId(String qualityId) {
		MedQualityPunish medQualityPunish = new MedQualityPunish();
		medQualityPunish.setQualityId(qualityId);
		mapper.delete(medQualityPunish);
	}

	@Override
	public List<MedQualityPunish> selectByQualityId(String qualityId) {
		Example example = new Example(MedQualityPunish.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("qualityId", qualityId);
		example.orderBy("punishEndDate").desc();
		return mapper.selectByExample(example);
	}
}

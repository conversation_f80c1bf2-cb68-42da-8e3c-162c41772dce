<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.schedule.dao.MedScheduleRecordMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.schedule.model.MedScheduleRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="schedule_date" jdbcType="VARCHAR" property="scheduleDate" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="classes_id" jdbcType="VARCHAR" property="classesId" />
    <result column="emp_org_id" jdbcType="VARCHAR" property="empOrgId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="sync" jdbcType="VARCHAR" property="sync" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>
  
  <select id="selectMedScheduleRecordList" resultType="cn.trasen.hrms.med.schedule.model.MedScheduleRecord" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee">
  		select t1.*,t2.classes_color,ifnull(t2.classes_hours,0) as classesHours, '1' type,t2.schedule_statistics scheduleStatistics,
  		t2.holiday_classes, t2.classes_days, t3.employee_name
  		
  		 from med_schedule_record t1
		left join med_schedule_classes t2 on t1.classes_id = t2.id
		left join cust_emp_base t3 on t1.employee_id = t3.employee_id
  		where t1.is_deleted = 'N'
  		<if test="employeeId != null and employeeId != ''">
  			and t1.employee_id = #{employeeId}
  		</if>
  		<if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
  			and t1.schedule_date between #{startDate} and #{endDate}
  		</if>
  		<if test="holidayClasses != null and holidayClasses != ''">
  			and t2.holiday_classes = #{holidayClasses}
  		</if>
  		<if test="scheduleStatistics != null and scheduleStatistics != ''">
  			and t2.schedule_statistics = #{scheduleStatistics}
  		</if>
  		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and t1.sso_org_code = #{ssoOrgCode}
  		</if>
  </select>
  
  <insert id="batchInsert">
		INSERT INTO med_schedule_record
		(
			id,
			schedule_date,
			employee_id,
			type_id,
			classes_id,
			classes_name,
			classes_worktime,
			emp_org_id,
			remark,
			create_date,
			create_user,
			create_user_name,
			is_deleted,
			sync,
			sso_org_code
		)
		VALUES
		<foreach collection="list" item="item" index="index" separator=",">
			(
				#{item.id},
				#{item.scheduleDate},
				#{item.employeeId},
				#{item.typeId},
				#{item.classesId},
				#{item.classesName},
				#{item.classesWorktime},
				#{item.empOrgId},
				#{item.remark},
				#{item.createDate},
				#{item.createUser},
				#{item.createUserName},
				#{item.isDeleted},
				#{item.sync},
				#{item.ssoOrgCode}
			)
		</foreach>
	</insert>
	
	<select id="selectExportScheduleRecord" resultType="Map" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee">
			SELECT 
				tt1.hospCode,
				tt1.orgName,
				tt1.employeeNo,
				tt1.employeeName,
				tt1.technical,
				<if test="dateList != null and dateList.size() > 0">
					<choose>
		            	<when test="_databaseId=='dm' ">
		            		<foreach collection="dateList" index="index" item="item" open="" separator="," close=",">
								TO_CHAR(MAX(CASE tt1.schedule_date WHEN SUBSTRING_INDEX(#{item}, ' ', 1) THEN tt1.classes_name ELSE '' END)) AS "${item}"
							</foreach>
			            	
		            	</when>
		            	<otherwise>
		            		<foreach collection="dateList" index="index" item="item" open="" separator="," close=",">
								CONVERT(MAX(CASE tt1.schedule_date WHEN SUBSTRING_INDEX(#{item}, ' ', 1) THEN tt1.classes_name ELSE '' END),CHAR) AS "${item}"
							</foreach>
		            	</otherwise>
	            	</choose>
				</if>
				tt1.phoneNumber
			FROM (
				SELECT 
					t3.name as orgName,
					t4.employee_name AS employeeName,
					<choose>
						<when test="_databaseId=='dm'">
							CONCAT(WM_CONCAT(t2.classes_name),',备注：', (CASE WHEN t1.remark IS NULL  THEN '-' ELSE t1.remark END))  AS classes_name,
						</when>
						<otherwise>
							CONCAT(group_concat(t2.classes_name),',备注：', (CASE WHEN t1.remark IS NULL  THEN '-' ELSE t1.remark END) )  AS classes_name1, 
						</otherwise>
					</choose>
					t1.schedule_date,
					t4.employee_no as employeeNo,
					t4.phone_number AS phoneNumber,
				 	t5.technical,
				 	t4.hosp_code as hospCode
				FROM med_schedule_record  t1
				LEFT JOIN med_schedule_classes t2 ON t1.classes_id = t2.id
				LEFT JOIN cust_emp_base t4 ON t1.employee_id=t4.employee_id 
				LEFT JOIN cust_emp_info t5 ON t4.employee_id=t5.info_id 
				LEFT JOIN comm_organization t3 ON t4.org_id = t3.organization_id
				WHERE t2.is_deleted ='N'  AND t1.is_deleted = 'N'
				
				<if test="startDate !=null and startDate != '' and endDate != null and endDate != ''">
					AND t1.schedule_date between #{startDate} and #{endDate}
				</if>
					
				<if test="employeeName != null and employeeName != ''">
		  			and (
						t4.employee_name like concat('%',#{employeeName},'%')
						or t4.phone_number like concat('%',#{employeeName},'%')
					) 
	  			</if>
	  			
	  			<if test="hospCode != null and hospCode != ''">
		  			and t4.hosp_code = #{hospCode}
	  			</if>
	  			
	  			<if test="technical != null and technical != ''">
		  			and t5.technical = #{technical}
	  			</if>
	  			
	  			<if test="classesIds != null and classesIds != ''">
		  			and (
			  			select count(1) from med_schedule_record tt 
			  			where tt.is_deleted = 'N' 
			  			<if test="classesIdList != null and classesIdList.size() > 0">
			        	 and tt.classes_id in
					        <foreach collection="classesIdList" index="index" item="item" open="(" separator="," close=")">
					            #{item}
					        </foreach>
				        </if>
			  			and tt.employee_id = t1.employee_id
		  				and tt.schedule_date between #{startDate} and #{endDate}
		  			) > 0
	  			</if>
				
				<if test="scheduleOrgs != null and scheduleUsers != null">
		        	 and (t4.org_id in
			        <foreach collection="scheduleOrgs" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
			        or t4.employee_no in
			        <foreach collection="scheduleUsers" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
			        )
		        </if>
				
				<if test="scheduleOrgs != null and scheduleUsers == null">
		        	 and t4.org_id in
			        <foreach collection="scheduleOrgs" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
		        </if>
		      
		        <if test="scheduleUsers != null and scheduleOrgs == null">
		        	and t4.employee_no in
			        <foreach collection="scheduleUsers" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
		        </if>
		        
		        <if test="orgIdList != null and orgIdList.size() > 0">
		        	 and t4.org_id in
			        <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
	        	</if>	
					
				GROUP BY t4.employee_no,t1.schedule_date,t3.name , t1.emp_org_id 
				ORDER BY t1.emp_org_id, t4.employee_name
			) tt1 
			GROUP BY tt1.employeeName,tt1.orgName,tt1.employeeNo,tt1.phoneNumber
	</select>
	
	<update id="deleteMedScheduleRecord" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee">
	
		UPDATE med_schedule_record t1
		LEFT JOIN cust_emp_base t2 on t1.employee_id = t2.employee_id
		LEFT JOIN cust_emp_info t3 on t2.employee_id = t3.info_id
		LEFT JOIN med_schedule_group_user t4 on t4.employee_id = t1.employee_id 
  		LEFT JOIN med_schedule_group t5 on t5.id = t4.group_id
		SET t1.is_deleted = 'Y'
		where 1=1
		<if test="employeeName != null and employeeName != ''">
  			and (
				t2.employee_name like concat('%',#{employeeName},'%')
				or t2.phone_number like concat('%',#{employeeName},'%')
			) 
		</if>
		
		<if test="employeeIds != null and employeeIds.size() > 0">
        	 and t2.employee_id in
	        <foreach collection="employeeIds" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
      	</if>
			
		<if test="hospCode != null and hospCode != ''">
 			and t2.hosp_code = #{hospCode}
		</if>
			
		<if test="technical != null and technical != ''">
	  		and t3.technical like concat('%',#{technical},'%')
  		</if>
  		
  		<if test="groupName != null and groupName != ''">
	  		and t5.group_name like concat('%',#{groupName},'%')
  		</if>
		
		<if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
			and t1.schedule_date between #{startDate} and #{endDate}
		</if>
			
		<if test="classesIds != null and classesIds != ''">
  			and (
	  			select count(1) from med_schedule_record tt 
	  			where tt.is_deleted = 'N' 
	  			<if test="classesIdList != null and classesIdList.size() > 0">
	        	 and tt.classes_id in
			        <foreach collection="classesIdList" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
		        </if>
	  			and tt.employee_id = t1.employee_id
  				and tt.schedule_date between #{startDate} and #{endDate}
  			) > 0
		</if>
       
       	<if test="orgIdList != null and orgIdList.size() > 0">
        	 and t2.org_id in
	        <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
      	</if>
      	
      	<if test="scheduleOrgs != null and scheduleUsers != null">
        	 and (t2.org_id in
	        <foreach collection="scheduleOrgs" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
	        or t2.employee_no in
	        <foreach collection="scheduleUsers" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
	        )
        </if>
		
		<if test="scheduleOrgs != null and scheduleUsers == null">
        	 and t2.org_id in
	        <foreach collection="scheduleOrgs" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
        </if>
      
        <if test="scheduleUsers != null and scheduleOrgs == null">
        	and t2.employee_no in
	        <foreach collection="scheduleUsers" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
        </if>
        
        <if test="employeeStatusList != null and employeeStatusList.size() > 0">
        	 and t2.employee_status in
	        <foreach collection="employeeStatusList" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
        </if>
  		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and t1.sso_org_code = #{ssoOrgCode}
  		</if>
	</update>
	
	<update id="deleteByGroupId" parameterType="String">
		update med_schedule_record set group_id = null
		where group_id = #{groupId}
	</update>
	
	<select id="statisticsScheduleRecordM" resultType="Map" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee">
			SELECT
				t2.classes_name as classesName,
				t2.classes_color as classesColor,
				count(t1.id) as classesDays
			FROM
				med_schedule_record t1
				LEFT JOIN cust_emp_base e on t1.employee_id = e.employee_id
				LEFT JOIN med_schedule_classes t2 ON t1.classes_id = t2.id 
			WHERE
				t1.is_deleted = 'N' 
			<if test="archivesType != null and archivesType != ''">
	  			and e.archives_type = #{archivesType}
  			</if>
			<if test="orgId != null and orgId != ''">
				and e.org_id = #{orgId}
			</if>
			<if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
				and t1.schedule_date between #{startDate} and #{endDate}
			</if>
	  		<if test="ssoOrgCode != null and ssoOrgCode != ''">
	  			and t1.sso_org_code = #{ssoOrgCode}
	  		</if>
			GROUP BY
				t1.classes_id
	</select>
	
	<select id="getOrgBaseInfo" resultType="Map" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee">
		select org.organization_id,org.name,
		(select count(1) from cust_emp_base t2 where t2.is_deleted = 'N' and t2.org_id = org.organization_id) as empNumbers,
		count(DISTINCT r.employee_id)  as scheduleEmpNumbers
		from comm_organization org
		left join (select t1.employee_id,t2.org_id from med_schedule_record t1 left join cust_emp_base t2 on t1.employee_id = t2.employee_id WHERE
		t1.is_deleted = 'N'
			<if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
				and t1.schedule_date between #{startDate} and #{endDate}
			</if>
		) r on org.organization_id = r.org_id
		group by org.organization_id
		having empNumbers>0
		order by org.org_level asc, org.seq_no asc, org.organization_id, org.create_date desc
	</select>
	
	<select id="selectTotalScheduleRecord" resultType="Map" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee">
		select t1.schedule_date,count(DISTINCT t1.employee_id) as scheduleNum from med_schedule_record t1
		left join cust_emp_base t2 on t1.employee_id = t2.employee_id
		where t1.is_deleted = 'N' and t2.org_id = #{orgId}
		<if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
			and t1.schedule_date between #{startDate} and #{endDate}
		</if>
		 GROUP BY t1.schedule_date
	</select>
	
	<select id="unfinishScheduleDareM" resultType="Map" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee">
			select t1.employee_name,t1.employee_no,
			(select group_concat(schedule_date) from med_schedule_record t2 where t2.is_deleted = 'N' 
			and t1.employee_id = t2.employee_id and t2.schedule_date between #{startDate} and #{endDate}) as finishDate
			from cust_emp_base t1
			where is_deleted = 'N'
			and t1.org_id = #{orgId}
	</select>
	
	<select id="selectEmployeeNo" resultType="String" parameterType="String">
		select employee_no from cust_emp_base
		where employee_id = #{employeeId}
	</select>
		
	<select id="selectTodayScheduleRecord" resultType="String" parameterType="String">
		select t1.employee_id from med_schedule_record t1
		left join med_schedule_classes t2 on t1.classes_id = t2.id
		LEFT JOIN cust_emp_info t3 on t1.employee_id = t3.info_id
		left join cust_emp_base t4 on t1.employee_id = t4.employee_id
		where t1.is_deleted = 'N' and t4.sso_org_code = #{ssoOrgCode}
		<if test="holiday != '' and holiday == 'N'.toString">
			and (t2.holiday_classes = '0' or t2.holiday_classes is null)
		</if>
		<if test="holiday != '' and holiday == 'Y'.toString">
			and t2.holiday_classes = '1'
		</if>
		and schedule_date = DATE_FORMAT(now(),'%Y-%m-%d') and (t3.technical like '%医师%' or t3.technical like '%医生%')
		GROUP BY t1.employee_id
	</select>
		
	<select id="selectEmployeeList" resultType="cn.trasen.hrms.model.HrmsEmployee" parameterType="String">
		select t1.* ,t2.pykssj,t2.pyjssj
		from cust_emp_base t1
		LEFT JOIN cust_emp_info t2 on t1.employee_id=t2.info_id
		where t1.is_deleted = 'N' 
		<if test="employeeStatus != null and employeeStatus.size() > 0">
        	 and t1.employee_status not in
	        <foreach collection="employeeStatus" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
        </if> 
		<if test="orgAttributes != null and orgAttributes.size() > 0">
        	 and t1.org_attributes in
	        <foreach collection="orgAttributes" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
        </if>
		<if test="pyjssj != null and pyjssj != ''">
			and t2.pyjssj=#{pyjssj}
		</if>
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
			and t1.sso_org_code=#{ssoOrgCode}
		</if>
	</select>
	
	<update id="updateEmployeeeStatus" parameterType="String">
		update cust_emp_base set employee_status = #{employeeStatus}
		where is_deleted = 'N' 
		<if test="employeeIdList != null and employeeIdList.size() > 0">
        	 and employee_id not in
	        <foreach collection="employeeIdList" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
        </if> 
	</update>
	
	<select id="selectShiftScheduleData" resultType="Map" parameterType="String">
		SELECT
			t1.id,
			t1.schedule_date,
			t2.classes_worktime
		FROM
			med_schedule_record t1
			LEFT JOIN med_schedule_classes t2 ON t1.classes_id = t2.id 
		WHERE
			t1.is_deleted = 'N' 
			AND t2.classes_type = '交接班' 
			AND t1.employee_id = #{currentUserId}
			AND t1.schedule_date between #{scheduleStartDate} and #{scheduleEndDate}
	</select>
	
	<select id="getTakeoverDoctor" resultType="Map" parameterType="String">
		SELECT
			t1.id,
			t1.schedule_date,
			t2.classes_worktime,
			t3.employee_name as employeeName,
			t3.employee_id as employeeId,
			t3.employee_no as employeeNo,
			t3.org_id as orgId,
			t4.name AS orgName
		FROM
			med_schedule_record t1
			LEFT JOIN med_schedule_classes t2 ON t1.classes_id = t2.id 
			LEFT JOIN cust_emp_base t3 ON t3.employee_id = t1.employee_id
			LEFT JOIN comm_organization t4 ON  t3.org_id = t4.organization_id
		WHERE
			t1.is_deleted = 'N' 
			AND t2.classes_type = '交接班' 
			AND schedule_date = #{today}
			AND t3.org_id = #{deptId}
			<if test="employeeName != null and employeeName != ''">
				and t3.employee_name like concat('%',#{employeeName},'%')
			</if>
	</select>
	
	
	<select id="selectEmpByOrgId" resultType="Map" parameterType="String">
		SELECT
			t1.employee_id as employeeId,
			t1.employee_name as employeeName,
			t1.employee_no as employeeNo,
			t1.org_id as orgId,
			t2.name AS orgName
		FROM
			cust_emp_base t1
			LEFT JOIN comm_organization t2 ON t1.org_id = t2.organization_id
		WHERE
			t1.is_deleted = 'N' and t1.is_enable = '1'
			AND t1.org_id = #{deptId}
		<if test="employeeName != null and employeeName != ''">
			and t1.employee_name like concat('%',#{employeeName},'%')
		</if>
	</select>
</mapper>
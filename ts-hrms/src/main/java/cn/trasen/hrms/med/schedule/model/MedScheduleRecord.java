package cn.trasen.hrms.med.schedule.model;

import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 排班记录
 *
 */
@Table(name = "med_schedule_record")
@Setter
@Getter
public class MedScheduleRecord {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 排班日期
     */
    @Column(name = "schedule_date")
    @ApiModelProperty(value = "排班日期")
    private String scheduleDate;

    /**
     * 员工id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    /**
     * 类别id
     */
    @Column(name = "type_id")
    @ApiModelProperty(value = "类别id")
    private String typeId;
    
    
    /**
     * 班次id
     */
    @Column(name = "classes_id")
    @ApiModelProperty(value = "班次id")
    private String classesId;


    /**
     * 人员机构id
     */
    @Column(name = "emp_org_id")
    @ApiModelProperty(value = "人员机构id")
    private String empOrgId;

    /**
     * 分组id
     */
    @Column(name = "group_id")
    @ApiModelProperty(value = "分组id")
    private String groupId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否同步数据
     */
    @ApiModelProperty(value = "是否同步数据 0否 1是")
    private String sync;
    
    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
    
    @Column(name = "classes_name")
    @ApiModelProperty(value = "班次名称")
    private String classesName;
    
    @Column(name = "classes_worktime")
    @ApiModelProperty(value = "考勤时间 多个逗号隔开")
    private String classesWorktime;
    
    @Transient
    private String classesColor;

    @Transient
    @ApiModelProperty(value = "班次时长")
    private String classesHours;
    
    @Transient
    private String employeeNo;
    
    @Transient
    private String employeeName;
	
    @Transient
    @ApiModelProperty(value = "模板排班周次：1-星期一，2-星期二，3-星期三，4-星期四，5-星期五，6-星期六，7-星期日")
    private String scheduleWeek;
    
    @Transient
    @ApiModelProperty(value = "模板启用状态：0-禁用，1-启用")
    private String status;
    
    @Transient
    @ApiModelProperty(value = "排班数据类型：1-排班数据，2-请假数据，3-进修/规培/学习/下乡等，4-销假，5-模板数据")
    private String type;
    
    @Transient
    @ApiModelProperty(value = "请假，进修/规培/学习/下乡，销假的天数")
    private BigDecimal days;
    
    @Transient
    @ApiModelProperty(value = "排班出勤天数")
    private BigDecimal classesDays;
    
    @Transient
    @ApiModelProperty(value = "排班出勤天数")
    private String classesDaysText;
    
    @Transient
    @ApiModelProperty(value = "科室ID")
    private String orgId;
    
    @Transient
    @ApiModelProperty(value = "序号")
    private String index;
    
    @Transient
    @ApiModelProperty(value = "具体开始时间:1-上午开始，2-下午开始")
    private String startDateValue;

    @Transient
    @ApiModelProperty(value = "具体结束时间:1-上午结束，2-下午结束")
    private String endDateValue;
    
}
package cn.trasen.hrms.med.supportApply.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

@Table(name = "med_support_apply")
@Setter
@Getter
public class MedSupportApply {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 现有患者
     */
    @Column(name = "patient_num")
    @ApiModelProperty(value = "现有患者")
    private String patientNum;

    /**
     * 现有危重患者
     */
    @Column(name = "icu_num")
    @ApiModelProperty(value = "现有危重患者")
    private String icuNum;

    /**
     * 加床
     */
    @Column(name = "extrabed_num")
    @ApiModelProperty(value = "加床")
    private String extrabedNum;

    /**
     * 申请理由
     */
    @Column(name = "apply_reason")
    @ApiModelProperty(value = "申请理由")
    private String applyReason;

    /**
     * 每周休息天数
     */
    @Column(name = "rest_days")
    @ApiModelProperty(value = "每周休息天数")
    private String restDays;

    /**
     * 潜力挖掘情况
     */
    @ApiModelProperty(value = "潜力挖掘情况")
    private String pes;

    /**
     * 是否协商 0未协商  1已协商
     */
    @Column(name = "is_confer")
    @ApiModelProperty(value = "是否协商 0未协商  1已协商")
    private String isConfer;

    /**
     * 其它要求
     */
    @Column(name = "other_require")
    @ApiModelProperty(value = "其它要求")
    private String otherRequire;

    /**
     * 流程id
     */
    @Column(name = "workflow_id")
    @ApiModelProperty(value = "流程id")
    private String workflowId;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态  0待审批  1已审批")
    private String status;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 申请科室id
     */
    @Column(name = "create_dept_code")
    @ApiModelProperty(value = "申请科室id")
    private String createDeptCode;

    /**
     * 申请科室名称
     */
    @Column(name = "create_dept_name")
    @ApiModelProperty(value = "申请科室名称")
    private String createDeptName;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    @Transient
    private List<MedSupportEmployee> empList; //支援人员集合
    
    @Transient
    private String empNumbers;
    
    @Transient
    private String supportUserNames;
    
    @Transient
    private String orgId;
    
    @Transient
    private List<String> childsList;
    
    @Transient
    private String createDateStart;
    
    @Transient
    private String createDateEnd;
    
    
}
package cn.trasen.hrms.med.supportApply.controller;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.supportApply.model.MedSupportApply;
import cn.trasen.hrms.med.supportApply.model.MedSupportEmployee;
import cn.trasen.hrms.med.supportApply.service.MedSupportApplyService;
import cn.trasen.hrms.med.supportApply.service.MedSupportEmployeeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedSupportApplyController
 * @Description TODO
 * @date 2025��1��4�� ����5:27:35
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "人力支援调配管理")
public class MedSupportApplyController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedSupportApplyController.class);

	@Autowired
	private MedSupportApplyService medSupportApplyService;
	
	@Autowired
	private MedSupportEmployeeService medSupportEmployeeService;

	/**
	 * @Title saveMedSupportApply
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��4�� ����5:27:35
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/supportApply/save")
	public PlatformResult<String> saveMedSupportApply(@RequestBody MedSupportApply record) {
		try {
			medSupportApplyService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedSupportApply
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��4�� ����5:27:35
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/supportApply/update")
	public PlatformResult<String> updateMedSupportApply(@RequestBody MedSupportApply record) {
		try {
			medSupportApplyService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedSupportApplyById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedSupportApply>
	 * @date 2025��1��4�� ����5:27:35
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/supportApply/{id}")
	public PlatformResult<MedSupportApply> selectMedSupportApplyById(@PathVariable String id) {
		try {
			MedSupportApply record = medSupportApplyService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedSupportApplyById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��1��4�� ����5:27:35
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/supportApply/delete/{id}")
	public PlatformResult<String> deleteMedSupportApplyById(@PathVariable String id) {
		try {
			medSupportApplyService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedSupportApplyList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedSupportApply>
	 * @date 2025��1��4�� ����5:27:35
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/supportApply/list")
	public DataSet<MedSupportApply> selectMedSupportApplyList(Page page, MedSupportApply record) {
		return medSupportApplyService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "流程回调", notes = "流程回调")
	@PostMapping("/api/supportApply/finishExamine")
	public void finishExamine(HttpServletRequest request) {
		try {
			
			logger.info("========人力资源调配管理回调==========");
			Map<String, Object> formData = new HashMap<>();
			Enumeration<String> enu = request.getParameterNames();
			while (enu.hasMoreElements()) {
				String key = (String) enu.nextElement();
				formData.put(key, request.getParameter(key));
			}
			// 业务id
			String transferRecordId = formData.get("L_BusinessId").toString(); //业务id
		    String L_LaunchUserCode = (String) formData.get("L_LaunchUserCode");//流程发起人编码
		    String L_LaunchUserName = (String) formData.get("L_LaunchUserName");//流程发起人名称
		    String L_LaunchDeptCode = (String) formData.get("L_LaunchDeptCode");//流程发起人部门编码
		    String L_LaunchDeptName = (String) formData.get("L_LaunchDeptName");//流程发起人部门名称
			
		    MedSupportApply medSupportApply = new MedSupportApply();
		    medSupportApply.setPatientNum((String)formData.get("patientNum"));
		    medSupportApply.setIcuNum((String)formData.get("icuNum"));
		    medSupportApply.setExtrabedNum((String)formData.get("extrabedNum"));
		    medSupportApply.setApplyReason((String)formData.get("applyReason"));
		    medSupportApply.setRestDays((String)formData.get("restDays"));
		    medSupportApply.setPes((String)formData.get("pes"));
		   // medSupportApply.setIsConfer((String)formData.get("isConfer"));
		    medSupportApply.setOtherRequire((String)formData.get("otherRequire"));
		    medSupportApply.setCreateDate(DateUtil.parse((String)formData.get("applyDate"), "yyyy-MM-dd"));
		    medSupportApply.setCreateUser(L_LaunchUserCode);
		    medSupportApply.setCreateUserName(L_LaunchUserName);
		    medSupportApply.setCreateDeptCode(L_LaunchDeptCode);
		    medSupportApply.setCreateDeptName(L_LaunchDeptName);
		    medSupportApply.setWorkflowId(transferRecordId);
		    
		    //支援人员需要查询子表单数据
		    List<MedSupportEmployee> empList = medSupportEmployeeService.selectByWorkflowId(transferRecordId);
		    
		    logger.info("========人力资源调配管理回调empList==========" + empList.size());
		    
		    medSupportApply.setEmpList(empList);
		    
		    medSupportApplyService.save(medSupportApply);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}

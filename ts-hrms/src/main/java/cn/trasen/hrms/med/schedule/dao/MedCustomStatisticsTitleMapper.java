package cn.trasen.hrms.med.schedule.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.hrms.med.schedule.model.MedCustomStatisticsTitle;
import cn.trasen.hrms.med.schedule.model.MedRecordStatisticsVo;
import tk.mybatis.mapper.common.Mapper;

public interface MedCustomStatisticsTitleMapper extends Mapper<MedCustomStatisticsTitle> {
	
	/**
	 * 查询自定义班次-表头
	 * @param ssoOrgCode
	 * @return
	 */
	List<MedCustomStatisticsTitle> selectCustomStatisticsTitle(@Param ("ssoOrgCode") String ssoOrgCode);
	
	/**
	 * 查询个人请假数据统计
	 * @param vo
	 * @return
	 */
	List<Map<String, Object>> selectLeaveRecordStatistics(MedRecordStatisticsVo vo);
	
	
	/**
	 * 查询个人销假数据统计
	 * @param vo
	 * @return
	 */
	List<Map<String, Object>> selectCancelLeaveRecordStatistics(MedRecordStatisticsVo vo);
	
	
	/**
	 * 查询个人排班按统计类型数据统计
	 * @param vo
	 * @return
	 */
	List<Map<String, Object>> selectScheduleRecordStatistics(MedRecordStatisticsVo vo);
	
	
	/**
	 * 查询个人进修、规培、外出会议、下乡数据统计
	 * @param vo
	 * @return
	 */
	List<Map<String, Object>> selectOutRecordStatistics(MedRecordStatisticsVo vo);
	
}

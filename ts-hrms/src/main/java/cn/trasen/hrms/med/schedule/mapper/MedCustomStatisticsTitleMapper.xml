<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.schedule.dao.MedCustomStatisticsTitleMapper">
	<select id="selectCustomStatisticsTitle" resultType="cn.trasen.hrms.med.schedule.model.MedCustomStatisticsTitle" parameterType="String">
  		select t1.id, t1.classes_id,t2.classes_name
  		from med_custom_statistics_title t1
  		left join med_schedule_classes t2 on t1.classes_id=t2.id
  		where t1.is_deleted='N' and t2.is_deleted='N' AND t2.classes_status='1'
  			
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and t1.sso_org_code = #{ssoOrgCode}
		</if>
    </select>
    
    <!--个人请假数据统计-->
    <select id="selectLeaveRecordStatistics" resultType="java.util.Map" parameterType="cn.trasen.hrms.med.schedule.model.MedRecordStatisticsVo">
  		select 
  		<if test="itemCodeList != null and itemCodeList.size() > 0">
          <foreach collection="itemCodeList" index="index"
                   item="item" open="" separator="," close="">
          	sum(case when r.leave_type=#{item} then r.days else 0 end) '${item}'
          </foreach>
          ,
        </if>
  		r.employee_code employeeCode,
  		max(e.org_id) orgId
		from hrms_leave_report r
		left join cust_emp_base e on e.employee_no=r.employee_code and e.sso_org_code=r.sso_org_code
		where r.is_deleted = 'N'
  		and r.start_date &lt;= #{endDate} and r.end_date >= #{startDate}
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and r.sso_org_code = #{ssoOrgCode}
		</if>
		<if test="employeeNoList != null and employeeNoList.size() > 0">
		    and r.employee_code in 
		        <foreach collection="employeeNoList" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
        </if>
		group by r.employee_code
    </select>
    
    <!--个人销假数据统计-->
    <select id="selectCancelLeaveRecordStatistics" resultType="java.util.Map" parameterType="cn.trasen.hrms.med.schedule.model.MedRecordStatisticsVo">
  		select 
  		<if test="itemCodeList != null and itemCodeList.size() > 0">
          <foreach collection="itemCodeList" index="index"
                   item="item" open="" separator="," close="">
          	sum(case when r.cancel_leave_type=#{item} then r.days else 0 end) '${item}'
          </foreach>
          ,
        </if>
  		r.employee_code employeeCode,
  		max(e.org_id) orgId
		from hrms_cancel_leave_report  r
		left join cust_emp_base e on e.employee_no=r.employee_code and e.sso_org_code=r.sso_org_code
		where r.is_deleted = 'N'
  		and r.start_date &lt;= #{endDate} and r.end_date >= #{startDate}
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and r.sso_org_code = #{ssoOrgCode}
		</if>
		<if test="employeeNoList != null and employeeNoList.size() > 0">
		    and r.employee_code in 
		        <foreach collection="employeeNoList" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
        </if>
		group by r.employee_code
    </select>
    
    <!--个人排班按统计类型数据统计-->
    <select id="selectScheduleRecordStatistics" resultType="java.util.Map" parameterType="cn.trasen.hrms.med.schedule.model.MedRecordStatisticsVo">
  		select 
  		<if test="itemCodeList != null and itemCodeList.size() > 0">
          <foreach collection="itemCodeList" index="index"
                   item="item" open="" separator="," close="">
          	sum(case when t2.id=#{item} then CAST(t2.classes_days AS DECIMAL(10, 1)) else 0 end) '${item}'
          </foreach>
          ,
        </if>
  		t3.employee_no employeeCode,
  		max(t3.org_id) orgId
		from med_schedule_record t1
		left join med_schedule_classes t2 on t1.classes_id = t2.id
		left join cust_emp_base t3 on t1.employee_id = t3.employee_id
		where t1.is_deleted = 'N' and t2.is_deleted = 'N'
  		and t1.schedule_date &lt;= #{endDate} and t1.schedule_date >= #{startDate}
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and t1.sso_org_code = #{ssoOrgCode}
		</if>
		<if test="employeeIdList != null and employeeIdList.size() > 0">
		    and t1.employee_id in 
		        <foreach collection="employeeIdList" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
        </if>
		group by t3.employee_no
    </select>
    
    <!--个人进修、规培、外出会议、下乡数据统计-->
    <select id="selectOutRecordStatistics" resultType="java.util.Map" parameterType="cn.trasen.hrms.med.schedule.model.MedRecordStatisticsVo">
  		select 
  		<if test="itemCodeList != null and itemCodeList.size() > 0">
          <foreach collection="itemCodeList" index="index"
                   item="item" open="" separator="," close="">
          	sum(case when t1.out_type=#{item} then t1.out_days else 0 end)  '${item}'
          </foreach>
          ,
        </if>
  		t2.employee_no employeeCode,
  		max(t2.org_id) orgId
		from (
			select employee_id ,out_type, out_days
  			from hrms_out_record
				where is_deleted = 'N' and out_type is not null and start_time is not null and end_time is not null 
				<if test="employeeIdList != null and employeeIdList.size() > 0">
				    and employee_id in 
				        <foreach collection="employeeIdList" index="index" item="item" open="(" separator="," close=")">
				            #{item}
				        </foreach>
		        </if>
				and start_time &lt;= #{endDate} and end_time >= #{startDate}
			UNION ALL
			select employee_id ,out_type, out_days
  			from hrms_out_record_gp
				where is_deleted = 'N' and out_type is not null and start_time is not null and end_time is not null 
				<if test="employeeIdList != null and employeeIdList.size() > 0">
				    and employee_id in 
				        <foreach collection="employeeIdList" index="index" item="item" open="(" separator="," close=")">
				            #{item}
				        </foreach>
		        </if>
				and start_time &lt;= #{endDate} and end_time >= #{startDate}
			UNION ALL
			select employee_id ,out_type, out_days
  			from hrms_out_record_hy
				where is_deleted = 'N' and out_type is not null and start_time is not null and end_time is not null 
				<if test="employeeIdList != null and employeeIdList.size() > 0">
				    and employee_id in 
				        <foreach collection="employeeIdList" index="index" item="item" open="(" separator="," close=")">
				            #{item}
				        </foreach>
		        </if>
				and start_time &lt;= #{endDate} and end_time >= #{startDate}
			UNION ALL
			select employee_id ,out_type, out_days
  			from hrms_out_record_xx
				where is_deleted = 'N' and out_type is not null and start_time is not null and end_time is not null
				<if test="employeeIdList != null and employeeIdList.size() > 0">
				    and employee_id in 
				        <foreach collection="employeeIdList" index="index" item="item" open="(" separator="," close=")">
				            #{item}
				        </foreach>
		        </if>
				and start_time &lt;= #{endDate} and end_time >= #{startDate}
		) t1
		left join cust_emp_base t2 on t1.employee_id = t2.employee_id
		where 1=1
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and t2.sso_org_code = #{ssoOrgCode}
		</if>
		group by t2.employee_no
    </select>
</mapper>
package cn.trasen.hrms.med.schedule.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.schedule.model.MedScheduleAuthority;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;
import cn.trasen.hrms.med.schedule.service.MedScheduleAuthorityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedScheduleAuthorityController
 * @Description TODO
 * @date 2025��3��29�� ����2:58:34
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "排班管理员设置-新")
public class MedScheduleAuthorityController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedScheduleAuthorityController.class);

	@Autowired
	private MedScheduleAuthorityService medScheduleAuthorityService;

	/**
	 * @Title saveMedScheduleAuthority
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��29�� ����2:58:34
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/scheduleAuthority/save")
	public PlatformResult<String> saveMedScheduleAuthority(@RequestBody MedScheduleAuthority record) {
		try {
			medScheduleAuthorityService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedScheduleAuthority
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��29�� ����2:58:34
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/scheduleAuthority/update")
	public PlatformResult<String> updateMedScheduleAuthority(@RequestBody MedScheduleAuthority record) {
		try {
			medScheduleAuthorityService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedScheduleAuthorityById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedScheduleAuthority>
	 * @date 2025��3��29�� ����2:58:34
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/scheduleAuthority/{id}")
	public PlatformResult<MedScheduleAuthority> selectMedScheduleAuthorityById(@PathVariable String id) {
		try {
			MedScheduleAuthority record = medScheduleAuthorityService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedScheduleAuthorityById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��3��29�� ����2:58:34
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/scheduleAuthority/delete/{id}")
	public PlatformResult<String> deleteMedScheduleAuthorityById(@PathVariable String id) {
		try {
			medScheduleAuthorityService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedScheduleAuthorityList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleAuthority>
	 * @date 2025��3��29�� ����2:58:34
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/scheduleAuthority/list")
	public DataSet<MedScheduleAuthority> selectMedScheduleAuthorityList(Page page, MedScheduleAuthority record) {
		return medScheduleAuthorityService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "查询排班管理权限树", notes = "查询排班管理权限树")
    @PostMapping("/api/scheduleAuthority/getScheduleZTree")
    public PlatformResult<List<TreeModel>> getScheduleZTree(ScheduleEmployee record) {
        try {
        	return PlatformResult.success(medScheduleAuthorityService.getScheduleZTree(record));
        }catch(Exception e) {
        	logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
	
	
	@ApiOperation(value = "查询排班管理权限人员", notes = "查询排班管理权限人员")
    @PostMapping("/api/scheduleAuthority/getScheduleManageEmployeeList")
    public PlatformResult<List<ScheduleEmployee>> getScheduleManageEmployeeList(@RequestBody ScheduleEmployee record) {
        try {
        	return PlatformResult.success(medScheduleAuthorityService.getScheduleManageEmployeeList(record));
        }catch(Exception e) {
        	logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }
	
	
}

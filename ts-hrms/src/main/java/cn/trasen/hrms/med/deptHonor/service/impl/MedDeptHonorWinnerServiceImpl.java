package cn.trasen.hrms.med.deptHonor.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.deptHonor.dao.MedDeptHonorWinnerMapper;
import cn.trasen.hrms.med.deptHonor.model.MedDeptHonorWinner;
import cn.trasen.hrms.med.deptHonor.service.MedDeptHonorWinnerService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedDeptHonorWinnerServiceImpl
 * @Description TODO
 * @date 2025��1��3�� ����3:29:57
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedDeptHonorWinnerServiceImpl implements MedDeptHonorWinnerService {

	@Autowired
	private MedDeptHonorWinnerMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedDeptHonorWinner record) {
		record.setId(IdGeneraterUtils.nextId());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedDeptHonorWinner record) {
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedDeptHonorWinner record = new MedDeptHonorWinner();
		record.setId(id);
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedDeptHonorWinner selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedDeptHonorWinner> getDataSetList(Page page, MedDeptHonorWinner record) {
		Example example = new Example(MedDeptHonorWinner.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<MedDeptHonorWinner> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteByHonorId(String honorId) {
		MedDeptHonorWinner medDeptHonorWinner = new MedDeptHonorWinner();
		medDeptHonorWinner.setHonorId(honorId);
		mapper.delete(medDeptHonorWinner);
	}

	@Override
	public List<MedDeptHonorWinner> selectByHonorId(String honorId) {
		Example example = new Example(MedDeptHonorWinner.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("honorId", honorId);
		
		return mapper.selectByExample(example);
	}
	
}

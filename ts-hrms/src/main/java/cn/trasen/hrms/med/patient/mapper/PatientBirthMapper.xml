<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.patient.dao.PatientBirthMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.patient.model.PatientBirth">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="patn_id" jdbcType="VARCHAR" property="patnId" />
    <result column="patn_no" jdbcType="VARCHAR" property="patnNo" />
    <result column="birth_date" jdbcType="TIMESTAMP" property="birthDate" />
    <result column="gestational_weeks" jdbcType="VARCHAR" property="gestationalWeeks" />
    <result column="diagnosis" jdbcType="VARCHAR" property="diagnosis" />
    <result column="exceptional_case" jdbcType="VARCHAR" property="exceptionalCase" />
    <result column="book_date" jdbcType="TIMESTAMP" property="bookDate" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
</mapper>
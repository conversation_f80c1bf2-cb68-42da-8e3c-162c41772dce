package cn.trasen.hrms.med.radiate.service.impl;

import java.util.*;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.core.contants.Contants;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.radiate.dao.RadiateMonitorMapper;
import cn.trasen.hrms.med.radiate.emun.RegisterCategoryEmun;
import cn.trasen.hrms.med.radiate.model.DictConstant;
import cn.trasen.hrms.med.radiate.model.RadiateMonitor;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;
import cn.trasen.hrms.med.radiate.service.RadiateMonitorService;
import cn.trasen.hrms.service.BaseDictItemService;

import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName RadiateMonitorServiceImpl
 * @Description TODO
 * @date 2025-05-21 15:15:27
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class RadiateMonitorServiceImpl implements RadiateMonitorService {

	@Autowired
	private RadiateMonitorMapper mapper;

	@Autowired
	private BaseDictItemService baseDictItemService;
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(RadiateMonitor record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(RadiateMonitor record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		RadiateMonitor record = new RadiateMonitor();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer saveOrUpdateList(List<RadiateMonitor> records, boolean isAdd, RadiatePersonnelRegister personnel) {
		if(CollUtil.isEmpty(records)){
			return 0;
		}
		//新增
		if(isAdd){
			for (RadiateMonitor record : records){
				record.setEmployeeId(personnel.getEmployeeId());
				save(record);
			}
		} else {
			//先查询该用户一共有几条数据
			List<RadiateMonitor> oldRecords = selectByEmployeeId(records.get(0).getEmployeeId());
			//将更新的数据记录，用于后续排除删除
			Set<String> updateIds = new HashSet<>();
			//遍历新增的数据
			for (RadiateMonitor record : records){
				//判断该用户是否已经存在该数据
				boolean flag = false;
				for (RadiateMonitor oldRecord : oldRecords){
					//如果是体检则不需要校验类型
					if(ObjectUtils.isNotEmpty(record.getRegisterCategory()) && record.getRegisterCategory().equals(RegisterCategoryEmun.CHECKUP.getCode())
							&& record.getRegisterCategory().equals(oldRecord.getRegisterCategory())){
						flag = true;
						record.setId(oldRecord.getId());
						record.setEmployeeId(personnel.getEmployeeId());
						update(record);
						updateIds.add(record.getId());
						continue;
					}
					if(ObjectUtils.isNotEmpty(record.getRegisterType()) && ObjectUtils.isNotEmpty(oldRecord.getRegisterType())
							&& ObjectUtils.isNotEmpty(record.getRegisterCategory()) && ObjectUtils.isNotEmpty(oldRecord.getRegisterCategory())
							&& record.getRegisterType().equals(oldRecord.getRegisterType())
							&& record.getRegisterCategory().equals(oldRecord.getRegisterCategory())){
						flag = true;
						record.setId(oldRecord.getId());
						record.setEmployeeId(personnel.getEmployeeId());
						update(record);
						updateIds.add(record.getId());
					}
				}
				if(!flag){
					record.setEmployeeId(personnel.getEmployeeId());
					save(record);
				}
			}
			//删除多余的数据
			if(CollUtil.isNotEmpty(oldRecords) && oldRecords.size() != updateIds.size()){
				for(RadiateMonitor oldRecord : oldRecords){
					if(!updateIds.contains(oldRecord.getId())){
						deleteById(oldRecord.getId());
					}
				}
			}
		}
		return records.size();
	}

	@Override
	public List<RadiateMonitor> selectByEmployeeId(String employeeId) {
		Example example = new Example(RadiateMonitor.class);
		Example.Criteria criteria = example.createCriteria();
		if(!ObjectUtils.isEmpty(employeeId)){
			criteria.andEqualTo("employeeId", employeeId);
		}
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return mapper.selectByExample(example);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteByEmployeeId(String employeeId) {
		Assert.hasText(employeeId, "人员ID不能为空.");
		Example example = new Example(RadiateMonitor.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		
		RadiateMonitor update = new RadiateMonitor();
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		update.setUpdateDate(new Date());
		update.setIsDeleted(Contants.IS_DELETED_TURE);
		if (user != null) {
			update.setUpdateUser(user.getUsercode());
			update.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByExampleSelective(update, example);
	}

	@Override
	public List<RadiateMonitor> selectAll() {
		Example example = new Example(RadiateMonitor.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<RadiateMonitor> list = mapper.selectByExample(example);

		Map<String, String>  radiationTypeMap  = baseDictItemService.convertDictMap(DictConstant.RADIATION_TYPE);//放射类型
		Map<String, String>  radiationCategoryMap  = baseDictItemService.convertDictMap(DictConstant.RADIATE_REGISTER_CATEGORY);//放射类别
		Map<String, String>  doseMonitorCycleMap  = baseDictItemService.convertDictMap(DictConstant.RADIATE_DOSE_MONITOR_CYCLE);//放射剂量监测周期
		for(RadiateMonitor monitor : list){
			monitor.setRegisterTypeText(radiationTypeMap.get(monitor.getRegisterType()));
			monitor.setRegisterCategoryText(radiationCategoryMap.get(monitor.getRegisterCategory()));
			monitor.setMonitorCycleText(doseMonitorCycleMap.get(monitor.getMonitorCycle()));
		}
		return list;
	}

}

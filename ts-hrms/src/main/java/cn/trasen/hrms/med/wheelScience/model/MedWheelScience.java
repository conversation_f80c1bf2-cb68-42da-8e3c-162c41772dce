package cn.trasen.hrms.med.wheelScience.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "med_wheel_science")
@Setter
@Getter
public class MedWheelScience {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**
     *  流程状态
     */
    @Column(name = "status")
    @ApiModelProperty(value = "流程状态  1待考核  2待轮科 3已完结")
    private String status;

    /**
     * 医师工号
     */
    @Column(name = "doc_code")
    @ApiModelProperty(value = "医师工号")
    private String docCode;

    /**
     * 医师姓名
     */
    @Column(name = "doc_name")
    @ApiModelProperty(value = "医师姓名")
    private String docName;

    /**
     * 性别
     */
    @Column(name = "doc_gender")
    @ApiModelProperty(value = "性别")
    private String docGender;

    /**
     * 出生年月
     */
    @Column(name = "doc_birthday")
    @ApiModelProperty(value = "出生年月")
    private String docBirthday;

    /**
     * 院区
     */
    @Column(name = "doc_area")
    @ApiModelProperty(value = "院区")
    private String docArea;

    /**
     * 科室
     */
    @Column(name = "doc_org_id")
    @ApiModelProperty(value = "科室")
    private String docOrgId;

    /**
     * 身份证号
     */
    @Column(name = "doc_idnum")
    @ApiModelProperty(value = "身份证号")
    private String docIdnum;

    /**
     * 联系方式
     */
    @Column(name = "doc_phone")
    @ApiModelProperty(value = "联系方式")
    private String docPhone;

    /**
     * 职务
     */
    @Column(name = "doc_post")
    @ApiModelProperty(value = "职务")
    private String docPost;

    /**
     * 技术职称
     */
    @Column(name = "doc_title")
    @ApiModelProperty(value = "技术职称")
    private String docTitle;

    /**
     * 取得该职称时间
     */
    @Column(name = "doc_title_date")
    @ApiModelProperty(value = "取得该职称时间")
    private String docTitleDate;

    /**
     * 最高学历
     */
    @Column(name = "doc_education")
    @ApiModelProperty(value = "最高学历")
    private String docEducation;
    

    /**
     * 轮科开始日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "wheel_start_date")
    @ApiModelProperty(value = "轮科开始日期")
    private Date wheelStartDate;

    /**
     * 轮科结束日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "wheel_end_date")
    @ApiModelProperty(value = "轮科结束日期")
    private Date wheelEndDate;

    /**
     * 轮科院区
     */
    @Column(name = "wheel_area")
    @ApiModelProperty(value = "轮科院区")
    private String wheelArea;

    /**
     * 轮科科室
     */
    @Column(name = "wheel_org_id")
    @ApiModelProperty(value = "轮科科室")
    private String wheelOrgId;

    /**
     * 轮科周期
     */
    @Column(name = "wheel_cycle")
    @ApiModelProperty(value = "轮科周期")
    private String wheelCycle;
    
    /**
     * 职责描述
     */
    @Column(name = "wheel_remark")
    @ApiModelProperty(value = "职责描述")
    private String wheelRemark;

    /**
     * 特殊要求
     */
    @Column(name = "wheel_require")
    @ApiModelProperty(value = "特殊要求")
    private String wheelRequire;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    
    /**
     * 考核结果
     */
    @Column(name = "wheel_result")
    @ApiModelProperty(value = "考核结果 0不合格  1合格")
    private String wheelResult;
    
    @ApiModelProperty(value = "考核附件")
    @Column(name = "wheel_result_files")
    private String wheelResultFiles;
    
    @ApiModelProperty(value = "考核提交人")
    @Column(name = "wheel_result_user")
    private String wheelResultUser;
    
    @ApiModelProperty(value = "考核提交人名称")
    @Column(name = "wheel_result_user_name")
    private String wheelResultUserName;
    
    @ApiModelProperty(value = "考核时间")
    @Column(name = "wheel_result_date")
    private Date wheelResultDate;
    
    /**
     * 轮科结果
     */
    @Column(name = "wheel_finish")
    @ApiModelProperty(value = "轮科结果 1定科  2继续轮科 3终止轮科")
    private String wheelFinish;
    
    /**
     * 轮科结果院区
     */
    @Column(name = "wheel_finish_area")
    @ApiModelProperty(value = "轮科结果院区")
    private String wheelFinishArea;

    /**
     * 轮科结果科室
     */
    @Column(name = "wheel_finish_dept")
    @ApiModelProperty(value = "轮科结果科室")
    private String wheelFinishDept;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "wheel_finish_start_date")
    @ApiModelProperty(value = "轮科结果开始时间")
    private Date wheelFinishStartDate;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "wheel_finish_end_date")
    @ApiModelProperty(value = "轮科结果结束时间")
    private Date wheelFinishEndDate;
    
    @Column(name = "wheel_finish_cycle")
    @ApiModelProperty(value = "轮科结果周期")
    private String wheelFinishCycle;
    
    /**
     * 轮科结果备注
     */
    @Column(name = "wheel_finish_remark")
    @ApiModelProperty(value = "轮科结果备注")
    private String wheelFinishRemark;
    
    /**
     * 轮科结果附件
     */
    @Column(name = "wheel_finish_files")
    @ApiModelProperty(value = "轮科结果附件")
    private String wheelFinishFiles;
    
    @Column(name = "wheel_finish_user")
    @ApiModelProperty(value = "轮科结果提交人")
    private String wheelFinishUser;
    
    @Column(name = "wheel_finish_user_name")
    @ApiModelProperty(value = "轮科结果提交人名称")
    private String wheelFinishUserName;

    @Column(name = "wheel_finish_date")
    @ApiModelProperty(value = "轮科结果时间")
    private Date wheelFinishDate;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    @Transient
    private List<String> orgIdList;
    
    @Transient
    private String startDate;
    
    @Transient
    private String endDate;
    
    @Transient
    private String orgId;
    
    @Transient
    private List<String> orgIds;
    
    @Transient
    private String roleName;
    
    @Transient
    private String docOrgName;
    
    @Transient
    private String wheelOrgName;
    
    @Transient
    private String wheelFinishDeptName;
    
    @Transient
    private String docAreaText;
    
    @Transient
    private String wheelAreaText;
    
    @Transient
    private String docEducationText;
    
    @Transient
    private String docGenderText;
    
    @Transient
    private String docPostName;
    
    
}
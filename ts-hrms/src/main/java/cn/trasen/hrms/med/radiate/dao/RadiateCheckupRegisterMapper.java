package cn.trasen.hrms.med.radiate.dao;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.med.radiate.model.RadiateCheckupRegister;
import cn.trasen.hrms.med.radiate.vo.RadiateCheckupReqVo;
import tk.mybatis.mapper.common.Mapper;

public interface RadiateCheckupRegisterMapper extends Mapper<RadiateCheckupRegister> {
	
	List<RadiateCheckupRegister> selectRadiateCheckupRegisterPageList(Page page, RadiateCheckupReqVo record);
	
}
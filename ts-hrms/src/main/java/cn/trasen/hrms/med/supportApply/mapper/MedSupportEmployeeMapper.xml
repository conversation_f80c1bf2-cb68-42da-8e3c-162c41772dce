<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.supportApply.dao.MedSupportEmployeeMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.supportApply.model.MedSupportEmployee">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="apply_id" jdbcType="VARCHAR" property="applyId" />
    <result column="support_user" jdbcType="VARCHAR" property="supportUser" />
    <result column="support_user_name" jdbcType="VARCHAR" property="supportUserName" />
    <result column="support_start_date" jdbcType="DATE" property="supportStartDate" />
    <result column="support_end_date" jdbcType="DATE" property="supportEndDate" />
  </resultMap>
  
  <select id="selectByWorkflowId" resultType="cn.trasen.hrms.med.supportApply.model.MedSupportEmployee" parameterType="String">
  			select * from zt_med_support_employee
  			where IS_DELETED = 'N' and BUSINESS_ID = #{workflowId}
  </select>
  
  <select id="selectSupportData" resultType="String">
  		select t1.id from med_support_employee t1
		LEFT JOIN med_support_apply app on t1.apply_id = app.id
		LEFT JOIN cust_emp_base t2 on t1.support_user = t2.employee_no
		LEFT JOIN cust_emp_info t3 on t2.employee_id = t3.info_id
		where app.is_deleted = 'N' and t2.is_deleted = 'N' and t2.sso_org_code = #{ssoOrgCode}
		and (t3.technical like '%医师%' or t3.technical like '%医生%') 
		and t1.support_start_date &lt;= CONCAT(CURDATE(), ' 23:59:59') 
		and t1.support_end_date >= CONCAT(CURDATE(), ' 00:00:00')
  </select>
  
  <select id="selectMySupportSize" parameterType="String" resultType="Long">
  		select count(1) from med_support_employee t1
		LEFT JOIN med_support_apply app on t1.apply_id = app.id
		where app.is_deleted = 'N' and support_user = #{currentUserCode}
  </select>
</mapper>
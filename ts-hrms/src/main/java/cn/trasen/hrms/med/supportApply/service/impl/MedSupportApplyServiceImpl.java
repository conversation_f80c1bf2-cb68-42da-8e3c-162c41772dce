package cn.trasen.hrms.med.supportApply.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.hrms.med.supportApply.dao.MedSupportApplyMapper;
import cn.trasen.hrms.med.supportApply.model.MedSupportApply;
import cn.trasen.hrms.med.supportApply.model.MedSupportEmployee;
import cn.trasen.hrms.med.supportApply.service.MedSupportApplyService;
import cn.trasen.hrms.med.supportApply.service.MedSupportEmployeeService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedSupportApplyServiceImpl
 * @Description TODO
 * @date 2025��1��4�� ����5:27:35
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedSupportApplyServiceImpl implements MedSupportApplyService {

	@Autowired
	private MedSupportApplyMapper mapper;
	
	@Autowired
	private MedSupportEmployeeService medSupportEmployeeService;
	
	@Autowired
	private HrmsOrganizationFeignService hrmsOrganizationFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedSupportApply record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		record.setStatus("1");
		
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setCreateDeptCode(user.getDeptcode());
			record.setCreateDeptName(user.getDeptname());
		}
		
		if(CollectionUtils.isNotEmpty(record.getEmpList())) {
			for (MedSupportEmployee medSupportEmployee : record.getEmpList()) {
				medSupportEmployee.setApplyId(record.getId());
				medSupportEmployeeService.save(medSupportEmployee);
			}
		}
		
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedSupportApply record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		medSupportEmployeeService.deleteByApplyId(record.getId());
		
		if(CollectionUtils.isNotEmpty(record.getEmpList())) {
			for (MedSupportEmployee medSupportEmployee : record.getEmpList()) {
				medSupportEmployee.setApplyId(record.getId());
				medSupportEmployeeService.save(medSupportEmployee);
			}
		}
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedSupportApply record = new MedSupportApply();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedSupportApply selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedSupportApply medSupportApply = mapper.selectByPrimaryKey(id);
		
		List<MedSupportEmployee> empList = medSupportEmployeeService.selectByApplyId(id);
		
		medSupportApply.setEmpList(empList);
		
		return medSupportApply;
	}

	@Override
	public DataSet<MedSupportApply> getDataSetList(Page page, MedSupportApply record) {
		
		if(StringUtils.isNotBlank(record.getOrgId())) {
			 PlatformResult<List<String>> result = hrmsOrganizationFeignService.getHrmsOrganizationAndNextList(record.getOrgId());
	          if(result.isSuccess()) {
	        	  List<String> childsList = result.getObject();
	        	  record.setChildsList(childsList);
	          }
		}
		
		if(StringUtils.isNotBlank(record.getCreateDateStart())) {
			record.setCreateDateStart(record.getCreateDateStart() + " 00:00:00");
		}
		if(StringUtils.isNotBlank(record.getCreateDateEnd())) {
			record.setCreateDateEnd(record.getCreateDateEnd() + " 23:59:59");
		}
		
		List<MedSupportApply> records = mapper.getDataSetList(record, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

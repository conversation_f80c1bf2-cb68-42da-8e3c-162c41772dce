<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.radiate.dao.RadiateTrainingRegisterMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.radiate.model.RadiateTrainingRegister">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_no" jdbcType="VARCHAR" property="employeeNo" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="training_type" jdbcType="VARCHAR" property="trainingType" />
    <result column="expiration_date" jdbcType="VARCHAR" property="expirationDate" />
    <result column="files" jdbcType="VARCHAR" property="files" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_dept" jdbcType="VARCHAR" property="createDept" />
    <result column="create_dept_name" jdbcType="VARCHAR" property="createDeptName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>
  
   <select id = "selectRadiateTrainingRegisterPageList"  resultType="cn.trasen.hrms.med.radiate.model.RadiateTrainingRegister" parameterType="cn.trasen.hrms.med.radiate.vo.RadiateTrainingReqVo" >
	 select c.id,c.employee_id,c.employee_no,c.employee_name,c.training_type,c.training_date,c.expiration_date,c.files,
	 c.create_date,c.create_user,c.create_user_name,c.update_user,c.update_user_name,c.update_date,c.is_deleted,
	 e.gender sex,e.org_id,o.NAME AS org_name,e.employee_status,e.sso_org_code,e.sso_org_name,
	 i.technical technical_title
	 from med_radiate_training_register c
	 left join  med_radiate_personnel_register  m on m.employee_id=c.employee_id
	 left join cust_emp_base e on e.employee_id=m.employee_id
	 left join cust_emp_info i on e.employee_id = i.info_id
	 LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted = 'N' 
	
	<if test="expired !=null and expired !=''">
		 inner JOIN (
		    SELECT employee_id,training_type, MAX(training_date) AS max_date
		    FROM med_radiate_training_register
		    where is_deleted='N'
		    GROUP BY employee_id,training_type
		) b 
		ON c.employee_id = b.employee_id and c.training_type=b.training_type AND c.training_date = b.max_date
 	 </if>
	 where c.is_deleted='N' and m.is_deleted='N' and e.is_deleted ='N'
	 
	 <if test='expired != null and expired != "" and expired == "0"'>
		 and (c.expiration_date > #{today} or c.expiration_date is null)
 	 </if>
 	 <if test='expired != null and expired != "" and expired == "1"'>
		 and c.expiration_date &lt; #{today}
 	 </if>
	 
 	 <if test="trainingDateBegin !=null and trainingDateBegin !=''">
  		and c.training_date   >=   #{trainingDateBegin}   
 	 </if>
	 <if test="trainingDateEnd !=null and trainingDateEnd !=''">
  		and c.training_date  &lt;=    #{trainingDateEnd}   
 	 </if>
 	 <if test="expirationDateBegin !=null and expirationDateBegin !=''">
  		and c.expiration_date   >=   #{expirationDateBegin}   
 	 </if>
 	  <if test="expirationDateEnd !=null and expirationDateEnd !=''">
  		and c.expiration_date   &lt;=   #{expirationDateEnd}   
 	 </if>
 	  <if test="orgId !=null and orgId !=''">
  		and o.organization_id = #{orgId}
 	 </if>
 	 <if test="orgIdList != null and orgIdList.size() > 0">
		and (o.organization_id in
		<foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
      )
	 </if>
 	 <if test="employeeNo !=null and employeeNo !=''">
	  	and c.employee_no = #{employeeNo}  
	  </if>
	  <if test="employeeId !=null and employeeId !=''">
	  	and c.employee_id = #{employeeId}  
	  </if>
	 <if test="employeeName !=null and employeeName !=''">
	  	and c.employee_name like concat('%',#{employeeName},'%') 
	 </if>
 	 <if test="employeeStatus !=null and employeeStatus !=''">
  		and e.employee_status = #{employeeStatus}  
 	 </if>
 	 <if test="technicalTitle !=null and technicalTitle !=''">
  		and i.technical like concat('%',#{technicalTitle},'%')  
 	 </if>
	   <if test="trainingType !=null and trainingType !=''">
	  	  	and c.training_type = #{trainingType} 
	  </if>
 	 <if test="condition !=null and condition !=''">
	  	and (c.employee_name  like concat('%',#{condition},'%') or  c.employee_no like concat('%',#{condition},'%')  ) 
	 </if>
	 order by c.update_date desc, c.create_date asc 
  </select>
	  
	  
</mapper>
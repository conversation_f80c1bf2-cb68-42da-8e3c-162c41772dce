package cn.trasen.hrms.med.integral.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.integral.model.MedIntegralRecord;

/**
 * @ClassName MedIntegralRecordService
 * @Description TODO
 * @date 2024��11��12�� ����11:14:05
 * <AUTHOR>
 * @version 1.0
 */
public interface MedIntegralRecordService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��11��12�� ����11:14:05
	 * <AUTHOR>
	 */
	Integer save(MedIntegralRecord record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��11��12�� ����11:14:05
	 * <AUTHOR>
	 */
	Integer update(MedIntegralRecord record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��11��12�� ����11:14:05
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedIntegralRecord
	 * @date 2024��11��12�� ����11:14:05
	 * <AUTHOR>
	 */
	MedIntegralRecord selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedIntegralRecord>
	 * @date 2024��11��12�� ����11:14:05
	 * <AUTHOR>
	 */
	DataSet<MedIntegralRecord> getDataSetList(Page page, MedIntegralRecord record);

	/**
	 * 
	 * @param record
	 */
	void examine(List<MedIntegralRecord> record);

	List<MedIntegralRecord> selectByUseCode(String currentUserCode);
}

package cn.trasen.hrms.med.patient.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "med_patient_info")
@Setter
@Getter
public class PatientInfo {
    /**
     * 患者主键
     */
    @Id
    @ApiModelProperty(value = "患者主键")
    private String id;

    /**
     * 患者住院号
     */
    @Column(name = "patn_no")
    @ApiModelProperty(value = "患者住院号")
    private String patnNo;

    /**
     * 身份证号
     */
    @Column(name = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 患者姓名
     */
    @ApiModelProperty(value = "患者姓名")
    private String name;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sex;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    /**
     * 联系方式
     */
    @Column(name = "phone_number")
    @ApiModelProperty(value = "联系方式")
    private String phoneNumber;

    /**
     * 家庭街道地址
     */
    @Column(name = "home_street")
    @ApiModelProperty(value = "家庭街道地址")
    private String homeStreet;

    /**
     * 入院时间
     */
    @Column(name = "in_date")
    @ApiModelProperty(value = "入院时间")
    private Date inDate;

    /**
     * 出院时间
     */
    @Column(name = "out_date")
    @ApiModelProperty(value = "出院时间")
    private Date outDate;

    /**
     * 主治医生
     */
    @ApiModelProperty(value = "主治医生")
    private String doctor;

    /**
     * 医生姓名
     */
    @Column(name = "doctor_name")
    @ApiModelProperty(value = "医生姓名")
    private String doctorName;

    /**
     * 入院科室
     */
    @Column(name = "in_dept")
    @ApiModelProperty(value = "入院科室")
    private String inDept;

    /**
     * 入院科室名称
     */
    @Column(name = "in_dept_name")
    @ApiModelProperty(value = "入院科室名称")
    private String inDeptName;

    /**
     * 当前科室id
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "当前科室id")
    private String deptId;

    /**
     * 科室名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "科室名称")
    private String deptName;

    /**
     * 院区信息
     */
    @Column(name = "hosp_area")
    @ApiModelProperty(value = "院区信息")
    private String hospArea;

    /**
     * 入院诊断
     */
    @Column(name = "in_diagnosis")
    @ApiModelProperty(value = "入院诊断")
    private String inDiagnosis;

    /**
     * 出院诊断
     */
    @Column(name = "out_diagnosis")
    @ApiModelProperty(value = "出院诊断")
    private String outDiagnosis;

    /**
     * 诊断日期
     */
    @Column(name = "diagnose_date")
    @ApiModelProperty(value = "诊断日期")
    private Date diagnoseDate;

    /**
     * 病人标志1=入院2=中途结算3=在床4=出区5=出院6=结算10=销号
     */
    @ApiModelProperty(value = "病人标志1=入院2=中途结算3=在床4=出区5=出院6=结算10=销号")
    private String flag;

    /**
     * 出院方式：1治愈、2好转、3未愈、4死亡、5其他
     */
    @Column(name = "out_mode")
    @ApiModelProperty(value = "出院方式：1治愈、2好转、3未愈、4死亡、5其他")
    private String outMode;

    /**
     * 操作日期
     */
    @Column(name = "book_date")
    @ApiModelProperty(value = "操作日期")
    private Date bookDate;

    /**
     * 取消标志
     */
    @Column(name = "cancel_bit")
    @ApiModelProperty(value = "取消标志")
    private String cancelBit;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    /**
     *床号
     */
    @Column(name = "bed_no")
    @ApiModelProperty(value = "床号")
    private String bedNo;
    
    /**
     *是否新入
     */
    @Column(name = "is_new_patient")
    @ApiModelProperty(value = "是否新入")
    private String isNewPatient;
    
    /**
     * 是否转科
     */
    @Column(name = "is_transfer")
    @ApiModelProperty(value = "是否转科")
    private String isTransfer;
    
    /**
     * 是否病危
     */
    @Column(name = "is_bw")
    @ApiModelProperty(value = "是否病危")
    private String isBw;
    
    /**
     * 是否病重
     */
    @Column(name = "is_bz")
    @ApiModelProperty(value = "是否病重")
    private String isBz;
    
    /**
     * 是否手术
     */
    @Column(name = "is_operation")
    @ApiModelProperty(value = "是否手术")
    private String isOperation;
    
    /**
     * 是否MDT
     */
    @Column(name = "is_mdt")
    @ApiModelProperty(value = "是否MDT")
    private String isMdt;
    
    /**
     * 是否分娩
     */
    @Column(name = "is_birth")
    @ApiModelProperty(value = "是否分娩")
    private String isBirth;
    
    /**
     * 是否死亡
     */
    @Column(name = "is_death")
    @ApiModelProperty(value = "是否死亡")
    private String isDeath;
    
    /**
     * 是否增强
     */
    @Column(name = "is_enhance")
    @ApiModelProperty(value = "是否增强")
    private String isEnhance;
    
    /**
     * 是否血管成像
     */
    @Column(name = "is_angiography")
    @ApiModelProperty(value = "是否血管成像")
    private String isAngiography;
    
    /**
     * 是否特殊
     */
    @Column(name = "is_special")
    @ApiModelProperty(value = "是否特殊")
    private String isSpecial;
    
    /**
     * 入院开始时间
     */
    @Transient
    @ApiModelProperty(value = "入院开始时间")
    private String inDateBegin;

    /**
     * 入院结束时间
     */
    @Transient
    @ApiModelProperty(value = "入院结束时间")
    private String inDateEnd;
    
    /**
     * 出院开始时间
     */
    @Transient
    @ApiModelProperty(value = "出院开始时间")
    private String outDateBegin;
    
    /**
     * 出院结束时间
     */
    @Transient
    @ApiModelProperty(value = "出院结束时间")
    private String outDateEnd;
    
    @Transient
    @ApiModelProperty(value = "住院状态：1在院,2已出院")
    private String hospitalizationStatus;
    
    @Transient
    @ApiModelProperty(value = "院区信息文本")
    private String hospAreaText;
    
    @Transient
    @ApiModelProperty(value = "患者类型")
    private String type;//类型:0普通,1新入,2转科,3病危,4病重,5手术,6MDT,7分娩,8死亡,9增强,10血管成像
    
    @Transient
    private String deptIds;
    
    @Transient
    @ApiModelProperty(value = "归属科室")
    private String oaOrgName;
    
    @Transient
    @ApiModelProperty(value = "是否转入")
    private String isInTransfer;
    
    @Transient
    @ApiModelProperty(value = "是否转出")
    private String isOutTransfer;
    
    @Transient
    private List<String> deptIdList;
    
    @Transient
    private Date inTransferDate;
    
    @Transient
    private Date outTransferDate;
    
    @Transient
    private String operationName;
    
    @Transient
    private String deathDiagnosis;
    
    @Transient
    private Date deathOutDate;
    
    @Transient
    private String shiftRecordId;
}
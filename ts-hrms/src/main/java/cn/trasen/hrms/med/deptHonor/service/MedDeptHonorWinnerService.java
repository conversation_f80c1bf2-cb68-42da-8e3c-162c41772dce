package cn.trasen.hrms.med.deptHonor.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.deptHonor.model.MedDeptHonorWinner;

/**
 * @ClassName MedDeptHonorWinnerService
 * @Description TODO
 * @date 2025��1��3�� ����3:29:57
 * <AUTHOR>
 * @version 1.0
 */
public interface MedDeptHonorWinnerService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��1��3�� ����3:29:57
	 * <AUTHOR>
	 */
	Integer save(MedDeptHonorWinner record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��1��3�� ����3:29:57
	 * <AUTHOR>
	 */
	Integer update(MedDeptHonorWinner record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��1��3�� ����3:29:57
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedDeptHonorWinner
	 * @date 2025��1��3�� ����3:29:57
	 * <AUTHOR>
	 */
	MedDeptHonorWinner selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedDeptHonorWinner>
	 * @date 2025��1��3�� ����3:29:57
	 * <AUTHOR>
	 */
	DataSet<MedDeptHonorWinner> getDataSetList(Page page, MedDeptHonorWinner record);

	void deleteByHonorId(String honorId);

	List<MedDeptHonorWinner> selectByHonorId(String honorId);

}

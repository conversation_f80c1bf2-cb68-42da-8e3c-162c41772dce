package cn.trasen.hrms.med.patient.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.patient.dao.PatientBirthMapper;
import cn.trasen.hrms.med.patient.model.PatientBirth;
import cn.trasen.hrms.med.patient.service.PatientBirthService;
import cn.trasen.hrms.utils.HnsrmyyHisJdbcUtil;
import lombok.extern.log4j.Log4j2;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PatientBirthServiceImpl
 * @Description TODO
 * @date 2025��4��8�� ����4:48:25
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Log4j2
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PatientBirthServiceImpl implements PatientBirthService {

	@Autowired
	private PatientBirthMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(PatientBirth record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(PatientBirth record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		PatientBirth record = new PatientBirth();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public PatientBirth selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<PatientBirth> getDataSetList(Page page, PatientBirth record) {
		Example example = new Example(PatientBirth.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<PatientBirth> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public void updateOrSavePatientBirth() {
		// TODO Auto-generated method stub
		try {    		
			StringBuilder sb = new StringBuilder();
			
    		sb.append(" SELECT  BABY_ID AS ID,INPATIENT_ID AS PATN_ID,INPATIENT_NO AS PATN_NO ,BIRTHDAY AS BIRTH_DATE, BOOK_DATE FROM ODSZYV10.ZY_INPATIENT_BABY   "
    				+ " ");
    		sb.append("    WHERE  (BIRTHDAY  >=TRUNC(SYSDATE -1) OR BOOK_DATE > TRUNC(SYSDATE -1) )   "
    				+ " ");
    		
			//sb.append("select  id as csltAppyId,appy_No as appyNo,patn_Name as patnName from  med_cslt_appy  where id = ？  ");
    		//List<CsltAppySyncHis> CsltAppyList = 	HnsrmyyHisJdbcUtil.query(sb.toString(),CsltAppySyncHis.class);//执行语句返回结果,反射映射有问题
    		log.info("===========sql:"+sb.toString());
    		List<PatientBirth> PatientBirthList = HnsrmyyHisJdbcUtil.queryPatientBirthSyncHis(sb.toString());//执行语句返回结果
    		log.info("===========PatientBirthList:"+PatientBirthList.size());
    		if(CollUtil.isNotEmpty(PatientBirthList)){
    			for(PatientBirth patientBirth : PatientBirthList) {
    				PatientBirth record = mapper.selectByPrimaryKey(patientBirth.getId());//根据患者id查询是否已经存在患者数据，存在则更新;
    				if(record != null) {
    					patientBirth.setUpdateDate(DateUtil.date());
    					mapper.updateByPrimaryKeySelective(patientBirth);
    				}else {
    					patientBirth.setIsDeleted("N");
    					patientBirth.setCreateDate(DateUtil.date());
    					patientBirth.setUpdateDate(DateUtil.date());
    					mapper.insertSelective(patientBirth);
    				}
    			}
    		}
		}catch(Exception e) {
    		e.printStackTrace();
    		log.error("获取影子库分娩信息数据异常：" + e.getMessage());
    	}
	}
}

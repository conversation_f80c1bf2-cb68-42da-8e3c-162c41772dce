package cn.trasen.hrms.med.radiate.controller;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.core.date.DateUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.radiate.model.RadiateDoseMonitoring;
import cn.trasen.hrms.med.radiate.service.RadiateDoseMonitoringService;
import cn.trasen.hrms.med.radiate.vo.RadiateDoseReqVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName RadiateDoseMonitoringController
 * @Description TODO
 * @date 2025��1��8�� ����11:15:27剂量监测登记
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "放射剂量监测登记")
public class RadiateDoseMonitoringController {

	private transient static final Logger logger = LoggerFactory.getLogger(RadiateDoseMonitoringController.class);

	@Autowired
	private RadiateDoseMonitoringService radiateDoseMonitoringService;

	/**
	 * @Title saveRadiateDoseMonitoring
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:15:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/RadiateDoseMonitoring/save")
	public PlatformResult<String> saveRadiateDoseMonitoring(@RequestBody RadiateDoseMonitoring record) {
		try {
			radiateDoseMonitoringService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateRadiateDoseMonitoring
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:15:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/RadiateDoseMonitoring/update")
	public PlatformResult<String> updateRadiateDoseMonitoring(@RequestBody RadiateDoseMonitoring record) {
		try {
			radiateDoseMonitoringService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectRadiateDoseMonitoringById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<RadiateDoseMonitoring>
	 * @date 2025��1��8�� ����11:15:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/RadiateDoseMonitoring/{id}")
	public PlatformResult<RadiateDoseMonitoring> selectRadiateDoseMonitoringById(@PathVariable String id) {
		try {
			RadiateDoseMonitoring record = radiateDoseMonitoringService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteRadiateDoseMonitoringById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:15:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/RadiateDoseMonitoring/delete/{id}")
	public PlatformResult<String> deleteRadiateDoseMonitoringById(@PathVariable String id) {
		try {
			radiateDoseMonitoringService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectRadiateDoseMonitoringList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RadiateDoseMonitoring>
	 * @date 2025��1��8�� ����11:15:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/RadiateDoseMonitoring/list")
	public DataSet<RadiateDoseMonitoring> selectRadiateDoseMonitoringList(Page page, RadiateDoseMonitoring record) {
		return radiateDoseMonitoringService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectRadiateDoseMonitoringList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RadiateDoseMonitoring>
	 * @date 2025��1��8�� ����11:15:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/RadiateDoseMonitoring/PageList")
	public DataSet<RadiateDoseMonitoring> selectRadiateDoseMonitoringPageList(Page page, RadiateDoseReqVo record) {
		String orgIds = record.getOrgIds();
		if(!ObjectUtils.isEmpty(orgIds)){
			record.setOrgIdList(Arrays.asList(orgIds.split(",")));
		}
		return radiateDoseMonitoringService.selectRadiateDoseMonitoringPageList(page, record);
	}
	
	
	/**
	 * 
	 * @param request
	 * @param response
	 * @param page
	 * @param record
	 */
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/RadiateDoseMonitoring/export")
    public void export(HttpServletRequest request, HttpServletResponse response, Page page, RadiateDoseReqVo record) {
		String orgIds = record.getOrgIds();
		if(!ObjectUtils.isEmpty(orgIds)){
			record.setOrgIdList(Arrays.asList(orgIds.split(",")));
		}
		page.setPageNo(1);
		page.setPageSize(Integer.MAX_VALUE);
		String name = "剂量监测信息表" + DateUtil.format(new Date(),"yyyyMMdd") + ".xls";
		String templateUrl = "template/radiate/doseMonitoringExport.xls";
		try {
			DataSet<RadiateDoseMonitoring> dataSetList = radiateDoseMonitoringService.selectRadiateDoseMonitoringPageList(page, record);
			List<RadiateDoseMonitoring> list = dataSetList.getRows();
            if (CollectionUtils.isNotEmpty(list)) {
            	for(RadiateDoseMonitoring radiateDoseMonitoring : list) {
            		if(!StringUtils.isEmpty(radiateDoseMonitoring.getMonitorStatus())) {
            			if("1".equals(radiateDoseMonitoring.getMonitorStatus())) {
            				radiateDoseMonitoring.setMonitorStatus("合格");
            			}else {
            				radiateDoseMonitoring.setMonitorStatus("不合格");
            			}
            		}
//            		if(!StringUtils.isEmpty(radiateDoseMonitoring.getMonitoringPeriod())) {
//            			if(MonitoringPeriodEmun.EVERY_QUARTER.getCode().equals(radiateDoseMonitoring.getMonitoringPeriod())) {
//            				radiateDoseMonitoring.setMonitoringDate("【季度】"+ radiateDoseMonitoring.getMonitoringDate());
//            			}
//            			if(MonitoringPeriodEmun.EVERY_YEAR.getCode().equals(radiateDoseMonitoring.getMonitoringPeriod())) {
//            				radiateDoseMonitoring.setMonitoringDate("【年度】"+ radiateDoseMonitoring.getMonitoringDate());
//            			}
//            		}
            	}
				ExportUtil.export(request, response, list, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

    }
	
	/**
	 * 导入
	 * @param file
	 * @return
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "导入数据", notes = "导入数据")
	@PostMapping(value = "/api/RadiateDoseMonitoring/import")
	public PlatformResult importDate(@RequestParam("file") MultipartFile file) {
		 try {
			 	List<RadiateDoseMonitoring> list = (List<RadiateDoseMonitoring>) ImportExcelUtil.getExcelDatas(file, RadiateDoseMonitoring.class);

	            if (!list.isEmpty()) {
	            	
	            	return radiateDoseMonitoringService.importData(list);
	            	
	            } else {
	                return PlatformResult.failure("数据为空");
	            }

	        } catch (Exception e) {
	            e.printStackTrace();
	            logger.error(e.getMessage(), e);
	            return PlatformResult.failure("导入数据失败，失败原因:" + e.getMessage());
	        }
	}
}

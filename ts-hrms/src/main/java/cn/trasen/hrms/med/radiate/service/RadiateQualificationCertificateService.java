package cn.trasen.hrms.med.radiate.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;
import cn.trasen.hrms.med.radiate.model.RadiateQualificationCertificate;
import cn.trasen.hrms.med.radiate.vo.RadiateCertificateReqVo;

import java.util.List;

/**
 * @ClassName RadiateQualificationCertificateService
 * @Description TODO
 * @date 2025��1��8�� ����11:16:41
 * <AUTHOR>
 * @version 1.0
 */
public interface RadiateQualificationCertificateService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��1��8�� ����11:16:41
	 * <AUTHOR>
	 */
	Integer save(RadiateQualificationCertificate record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��1��8�� ����11:16:41
	 * <AUTHOR>
	 */
	Integer update(RadiateQualificationCertificate record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��1��8�� ����11:16:41
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return RadiateQualificationCertificate
	 * @date 2025��1��8�� ����11:16:41
	 * <AUTHOR>
	 */
	RadiateQualificationCertificate selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<RadiateQualificationCertificate>
	 * @date 2025��1��8�� ����11:16:41
	 * <AUTHOR>
	 */
	DataSet<RadiateQualificationCertificate> getDataSetList(Page page, RadiateQualificationCertificate record);
	
	DataSet<RadiateQualificationCertificate> selectRadiateQualificationCertificatePagelist(Page page, RadiateCertificateReqVo record);

	/**
	 * @Title saveOrUpdateList
	 * @Description 批量信息
	 * @param records
	 * @param isAdd 新增标识
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	Integer saveOrUpdateList(List<RadiateQualificationCertificate> records, boolean isAdd, RadiatePersonnelRegister personnel);

	/**
	 * @Title selectByEmployeeId
	 * @Description 根据员工ID查询
	 * @param employeeId
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	List<RadiateQualificationCertificate> selectByEmployeeId(String employeeId);
	List<RadiateQualificationCertificate> getList();
	
	/**
	 * @Title deleteByEmployeeId
	 * @Description 根据员工ID删除
	 * @param employeeId
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	Integer deleteByEmployeeId(String employeeId);

	/**
	 * 导入数据
	 * 
	 * @param list
	 * @return
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	PlatformResult importData(List<RadiateQualificationCertificate> list);
}

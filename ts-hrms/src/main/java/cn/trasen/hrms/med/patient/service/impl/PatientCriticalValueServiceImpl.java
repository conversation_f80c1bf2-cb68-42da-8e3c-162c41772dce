package cn.trasen.hrms.med.patient.service.impl;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.log;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.patient.dao.PatientCriticalValueMapper;
import cn.trasen.hrms.med.patient.model.PatientCriticalValue;
import cn.trasen.hrms.med.patient.model.PatientOrderrecord;
import cn.trasen.hrms.med.patient.service.PatientCriticalValueService;
import cn.trasen.hrms.utils.HnsrmyyHisJdbcUtil;
import lombok.extern.log4j.Log4j2;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PatientCriticalValueServiceImpl
 * @Description TODO
 * @date 2025��4��8�� ����4:46:22
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Log4j2
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PatientCriticalValueServiceImpl implements PatientCriticalValueService {

	@Autowired
	private PatientCriticalValueMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(PatientCriticalValue record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(PatientCriticalValue record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		PatientCriticalValue record = new PatientCriticalValue();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public PatientCriticalValue selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<PatientCriticalValue> getDataSetList(Page page, PatientCriticalValue record) {
		Example example = new Example(PatientCriticalValue.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<PatientCriticalValue> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public void updateOrSavePatientCriticalValue() {
		// TODO Auto-generated method stub
		try {    		
			StringBuilder sb = new StringBuilder();
			
    		sb.append(" SELECT  A.ID,A.INPATIENT_ID AS PATN_ID,C.INPATIENT_NO AS PATN_NO,REPORT_DATE,PANIC_VALUE AS CRITICAL_VALUE,ORDER_ITEMID AS ORDER_ITEM_ID,ORDER_ITEMNAME AS ORDER_ITEM_NAME,REQUEST_DEPTID AS REQUEST_DEPT_ID,REQUEST_DEPTNAME AS REQUEST_DEPT_NAME,SIGN_DATE,PROCESS_DATE,STATUS   "
    				+ " 		FROM   ODSZYV10.ZY_CRITICAL_VALUE_MESSAGE A  INNER JOIN ODSZYV10.ZY_INPATIENT B ON A.INPATIENT_ID=B.INPATIENT_ID  "
    				+ "         INNER  JOIN  ODSZYV10.BASE_PATIENT_PROPERTY  C ON B.PATIENT_ID=C.PATIENT_ID    "
    				+ " ");
    		sb.append("   where  (REPORT_DATE  >  TRUNC(SYSDATE -1) or SIGN_DATE >  TRUNC(SYSDATE -1)  or PROCESS_DATE >  TRUNC(SYSDATE -1))   "
    				+ " ");
    		
			//sb.append("select  id as csltAppyId,appy_No as appyNo,patn_Name as patnName from  med_cslt_appy  where id = ？  ");
    		//List<CsltAppySyncHis> CsltAppyList = 	HnsrmyyHisJdbcUtil.query(sb.toString(),CsltAppySyncHis.class);//执行语句返回结果,反射映射有问题
    		log.info("===========sql:"+sb.toString());
    		List<PatientCriticalValue> PatientCriticalValueList = HnsrmyyHisJdbcUtil.queryPatientCriticalValueSyncHis(sb.toString());//执行语句返回结果
    		log.info("===========PatientCriticalValueList:"+PatientCriticalValueList.size());
    		if(CollUtil.isNotEmpty(PatientCriticalValueList)){
    			for(PatientCriticalValue patientCriticalValue : PatientCriticalValueList) {
    				PatientCriticalValue record = mapper.selectByPrimaryKey(patientCriticalValue.getId());//根据患者id查询是否已经存在患者数据，存在则更新;
    				if(record != null) {
    					patientCriticalValue.setUpdateDate(DateUtil.date());
    					mapper.updateByPrimaryKeySelective(patientCriticalValue);
    				}else {
    					patientCriticalValue.setIsDeleted("N");
    					patientCriticalValue.setCreateDate(DateUtil.date());
    					patientCriticalValue.setUpdateDate(DateUtil.date());
    					mapper.insertSelective(patientCriticalValue);
    				}
    			}
    		}
		}catch(Exception e) {
    		e.printStackTrace();
    		log.error("获取影子库危急值信息数据异常：" + e.getMessage());
    	}
	}
}

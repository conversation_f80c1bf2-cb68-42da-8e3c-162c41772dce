package cn.trasen.hrms.med.schedule.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "med_schedule_group")
@Setter
@Getter
public class MedScheduleGroup {
	
    @Id
    private String id;

    @Column(name = "group_name")
    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @Column(name = "group_emp_id")
    @ApiModelProperty(value = "分组人员id")
    private String groupEmpId;

    @Column(name = "group_emp_name")
    @ApiModelProperty(value = "分组人员名称")
    private String groupEmpName;
    
    @Column(name = "group_sort")
    @ApiModelProperty(value = "分组排序")
    private int groupSort;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

//    @Column(name = "sso_org_code")
//    @ApiModelProperty(value = "机构编码")
//    private String ssoOrgCode;
}
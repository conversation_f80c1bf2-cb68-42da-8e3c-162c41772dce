package cn.trasen.hrms.med.radiate.emun;

/**
 * 监测周期
 * <AUTHOR>
 *
 */
public enum MonitorCycleEmun {
	
	ONE_YEAR("1", "1年"),
	TWO_YEAR("2", "2年"),  
	FIVE_YEAR("5", "5年");
//	EVERY_YEAR("4", "每年"),  
//	EVERY_QUARTER("5", "季度");
	
	private String name;

	private String code;

	MonitorCycleEmun(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	

	/**
	 * 根据编码获取name
	 * 
	 * @param code
	 * @return
	 */
	public static String convert(String code) {
		for (MonitorCycleEmun emun : values()) {
			if (emun.getCode().equals(code))
				return emun.name;
		}
		return null;
	}

}

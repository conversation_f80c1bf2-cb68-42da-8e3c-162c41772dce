package cn.trasen.hrms.med.patient.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.patient.model.PatientCriticalValue;

/**
 * @ClassName PatientCriticalValueService
 * @Description TODO
 * @date 2025��4��8�� ����4:46:22
 * <AUTHOR>
 * @version 1.0
 */
public interface PatientCriticalValueService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��4��8�� ����4:46:22
	 * <AUTHOR>
	 */
	Integer save(PatientCriticalValue record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��4��8�� ����4:46:22
	 * <AUTHOR>
	 */
	Integer update(PatientCriticalValue record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��4��8�� ����4:46:22
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return PatientCriticalValue
	 * @date 2025��4��8�� ����4:46:22
	 * <AUTHOR>
	 */
	PatientCriticalValue selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<PatientCriticalValue>
	 * @date 2025��4��8�� ����4:46:22
	 * <AUTHOR>
	 */
	DataSet<PatientCriticalValue> getDataSetList(Page page, PatientCriticalValue record);
	
	void updateOrSavePatientCriticalValue();
}

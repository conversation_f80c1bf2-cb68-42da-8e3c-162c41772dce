package cn.trasen.hrms.med.radiate.model;

public class DictConstant {
	
	/** 性别 **/
	public static final String SEX = "SEX";
	/** 学历 **/
	public static final String EDUCATION_TYPE = "education_type";
	/** 技术职称 **/
	public static final String PERSONAL_IDENTITY = "personal_identity";
	/** 员工状态 **/
	public static final String EMPLOYEE_STATUS = "employee_status";
	/** 放射类型 **/
	public static final String RADIATION_TYPE = "radiation_type";
	/** 培训类型 **/
	public static final String RADIATE_TRAINING_TYPE = "radiate_training_type";
	/** 剂量计类型 **/
	public static final String DOSE_TYPE = "dose_type";
	/** 证件资质类型 **/
	public static final String CERTIFICATE_TYPE = "radiate_certificate_type";
	
	/** 放射登记类别 **/
	public static final String RADIATE_REGISTER_CATEGORY = "radiate_register_category";
	
	/** 放射监测周期 **/
	public static final String RADIATE_MONITOR_CYCLE = "radiate_monitor_cycle";
	
	/** 放射剂量监测周期 **/
	public static final String RADIATE_DOSE_MONITOR_CYCLE = "radiate_dose_monitor_cycle";
}

package cn.trasen.hrms.med.schedule.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.schedule.model.MedScheduleGroupUser;
import cn.trasen.hrms.med.schedule.service.MedScheduleGroupUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedScheduleGroupUserController
 * @Description TODO
 * @date 2025��4��7�� ����2:31:36
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "排班分组用户-新")
public class MedScheduleGroupUserController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedScheduleGroupUserController.class);

	@Autowired
	private MedScheduleGroupUserService medScheduleGroupUserService;

	/**
	 * @Title saveMedScheduleGroupUser
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��7�� ����2:31:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/scheduleGroupUser/save")
	public PlatformResult<String> saveMedScheduleGroupUser(@RequestBody MedScheduleGroupUser record) {
		try {
			medScheduleGroupUserService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedScheduleGroupUser
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��7�� ����2:31:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/scheduleGroupUser/update")
	public PlatformResult<String> updateMedScheduleGroupUser(@RequestBody MedScheduleGroupUser record) {
		try {
			medScheduleGroupUserService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedScheduleGroupUserById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedScheduleGroupUser>
	 * @date 2025��4��7�� ����2:31:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/scheduleGroupUser/{id}")
	public PlatformResult<MedScheduleGroupUser> selectMedScheduleGroupUserById(@PathVariable String id) {
		try {
			MedScheduleGroupUser record = medScheduleGroupUserService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedScheduleGroupUserById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��4��7�� ����2:31:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/scheduleGroupUser/delete/{id}")
	public PlatformResult<String> deleteMedScheduleGroupUserById(@PathVariable String id) {
		try {
			medScheduleGroupUserService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedScheduleGroupUserList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleGroupUser>
	 * @date 2025��4��7�� ����2:31:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/scheduleGroupUser/list")
	public DataSet<MedScheduleGroupUser> selectMedScheduleGroupUserList(Page page, MedScheduleGroupUser record) {
		return medScheduleGroupUserService.getDataSetList(page, record);
	}
}

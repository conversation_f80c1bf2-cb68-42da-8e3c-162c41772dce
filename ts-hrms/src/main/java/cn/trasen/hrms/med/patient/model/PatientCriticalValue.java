package cn.trasen.hrms.med.patient.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "med_patient_critical_value")
@Setter
@Getter
public class PatientCriticalValue {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 患者主键
     */
    @Column(name = "patn_id")
    @ApiModelProperty(value = "患者主键")
    private String patnId;

    /**
     * 患者住院号
     */
    @Column(name = "patn_no")
    @ApiModelProperty(value = "患者住院号")
    private String patnNo;

    /**
     * 报告日期
     */
    @Column(name = "report_date")
    @ApiModelProperty(value = "报告日期")
    private Date reportDate;

    /**
     * 危急值内容
     */
    @Column(name = "critical_value")
    @ApiModelProperty(value = "危急值内容")
    private String criticalValue;

    /**
     * 项目id
     */
    @Column(name = "order_item_id")
    @ApiModelProperty(value = "项目id")
    private String orderItemId;

    /**
     * 项目名称
     */
    @Column(name = "order_item_name")
    @ApiModelProperty(value = "项目名称")
    private String orderItemName;

    /**
     * 申请科室id
     */
    @Column(name = "request_dept_id")
    @ApiModelProperty(value = "申请科室id")
    private String requestDeptId;

    /**
     * 申请科室名称
     */
    @Column(name = "request_dept_name")
    @ApiModelProperty(value = "申请科室名称")
    private String requestDeptName;

    /**
     * 危急值签发时间
     */
    @Column(name = "sign_date")
    @ApiModelProperty(value = "危急值签发时间")
    private Date signDate;
    
    /**
     * 临床处理日期
     */
    @Column(name = "process_date")
    @ApiModelProperty(value = "临床处理日期")
    private Date processDate;
    
    /**
     * 处理状态
     */
    @Column(name = "status")
    @ApiModelProperty(value = "处理状态 0-未处理   1-已处理")
    private String status;
    
    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
}
package cn.trasen.hrms.med.radiate.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.radiate.model.RadiateCheckupRegister;
import cn.trasen.hrms.med.radiate.model.RadiateMonitor;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;
import cn.trasen.hrms.med.radiate.vo.RadiateCheckupReqVo;

import java.util.List;

/**
 * @ClassName RadiateCheckupRegisterService
 * @Description TODO
 * @date 2025��1��8�� ����11:03:28
 * <AUTHOR>
 * @version 1.0
 */
public interface RadiateCheckupRegisterService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��1��8�� ����11:03:28
	 * <AUTHOR>
	 */
	Integer save(RadiateCheckupRegister record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��1��8�� ����11:03:28
	 * <AUTHOR>
	 */
	Integer update(RadiateCheckupRegister record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��1��8�� ����11:03:28
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return RadiateCheckupRegister
	 * @date 2025��1��8�� ����11:03:28
	 * <AUTHOR>
	 */
	RadiateCheckupRegister selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<RadiateCheckupRegister>
	 * @date 2025��1��8�� ����11:03:28
	 * <AUTHOR>
	 */
	DataSet<RadiateCheckupRegister> getDataSetList(Page page, RadiateCheckupRegister record);
	
	DataSet<RadiateCheckupRegister> selectRadiateCheckupRegisterPageList(Page page, RadiateCheckupReqVo record);

	/**
	 * @Title saveOrUpdateList
	 * @Description 批量信息
	 * @param records
	 * @param monitor 监测设置
	 * @param isAdd 新增标识
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	Integer saveOrUpdateList(List<RadiateCheckupRegister> records, RadiateMonitor monitor, boolean isAdd, RadiatePersonnelRegister personnel);

	/**
	 * @Title selectByEmployeeId
	 * @Description 根据员工ID查询
	 * @param employeeId
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	List<RadiateCheckupRegister> selectByEmployeeId(String employeeId);
	List<RadiateCheckupRegister> getList();
	
	/**
	 * @Title deleteByEmployeeId
	 * @Description 根据员工ID删除
	 * @param employeeId
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	Integer deleteByEmployeeId(String employeeId);

	/**
	 * 导入数据
	 * 
	 * @param list
	 * @return
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	PlatformResult importData(List<RadiateCheckupRegister> list);
}

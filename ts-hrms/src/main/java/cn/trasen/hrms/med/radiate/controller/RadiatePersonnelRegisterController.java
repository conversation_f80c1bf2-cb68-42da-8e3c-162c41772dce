package cn.trasen.hrms.med.radiate.controller;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.core.date.DateUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;
import cn.trasen.hrms.med.radiate.service.RadiatePersonnelRegisterService;
import cn.trasen.hrms.med.radiate.vo.RadiatePersonnelReqVo;
import cn.trasen.hrms.med.radiate.vo.RadiatePersonnelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName RadiatePersonnelRegisterController
 * @Description TODO
 * @date 2025��1��8�� ����11:21:29放射人员登记
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "放射人员登记")
public class RadiatePersonnelRegisterController {

	private transient static final Logger logger = LoggerFactory.getLogger(RadiatePersonnelRegisterController.class);

	@Autowired
	private RadiatePersonnelRegisterService radiatePersonnelRegisterService;

	/**
	 * @Title saveRadiatePersonnelRegister
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:21:29
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/RadiatePersonnelRegister/save")
	public PlatformResult<String> saveRadiatePersonnelRegister(@RequestBody RadiatePersonnelRegister record) {
		try {
			radiatePersonnelRegisterService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateRadiatePersonnelRegister
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:21:29
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/RadiatePersonnelRegister/update")
	public PlatformResult<String> updateRadiatePersonnelRegister(@RequestBody RadiatePersonnelRegister record) {
		try {
			radiatePersonnelRegisterService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectRadiatePersonnelRegisterById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<RadiatePersonnelRegister>
	 * @date 2025��1��8�� ����11:21:29
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/RadiatePersonnelRegister/{id}")
	public PlatformResult<RadiatePersonnelRegister> selectRadiatePersonnelRegisterById(@PathVariable String id) {
		try {
			RadiatePersonnelRegister record = radiatePersonnelRegisterService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteRadiatePersonnelRegisterById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:21:29
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/RadiatePersonnelRegister/delete/{id}")
	public PlatformResult<String> deleteRadiatePersonnelRegisterById(@PathVariable String id) {
		try {
			radiatePersonnelRegisterService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectRadiatePersonnelRegisterList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RadiatePersonnelRegister>
	 * @date 2025��1��8�� ����11:21:29
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/RadiatePersonnelRegister/list")
	public PlatformResult<List<RadiatePersonnelRegister>> selectRadiatePersonnelRegisterList(RadiatePersonnelReqVo record) {
		try {
			return PlatformResult.success(radiatePersonnelRegisterService.getList(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * @Title selectRadiatePersonnelRegisterPageList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RadiatePersonnelRegister>
	 * @date 2025��1��8�� ����11:21:29
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/RadiatePersonnelRegister/PageList")
	public DataSet<RadiatePersonnelRegister> selectRadiatePersonnelRegisterPageList(Page page, RadiatePersonnelReqVo record) {
		String orgIds = record.getOrgIds();
		if(!ObjectUtils.isEmpty(orgIds)){
			record.setOrgIdList(Arrays.asList(orgIds.split(",")));
		}
		return radiatePersonnelRegisterService.selectRadiatePersonnelRegisterPageList(page, record);
	}
	
	
	/**
	 * 
	 * @param request
	 * @param response
	 * @param page
	 * @param record
	 */
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/RadiatePersonnelRegister/export")
    public void export(HttpServletRequest request, HttpServletResponse response,Page page, RadiatePersonnelReqVo record) {
		String orgIds = record.getOrgIds();
		if(!ObjectUtils.isEmpty(orgIds)){
			record.setOrgIdList(Arrays.asList(orgIds.split(",")));
		}
		page.setPageNo(1);
		page.setPageSize(Integer.MAX_VALUE);
		String name = "放射人员一览信息表" + DateUtil.format(new Date(),"yyyyMMdd") + ".xls";
		String templateUrl = "template/radiate/personnelRegisterExport.xls";
		try {
			DataSet<RadiatePersonnelRegister> dataSetList = radiatePersonnelRegisterService.selectRadiatePersonnelRegisterPageList(page, record);
            List<RadiatePersonnelRegister> list = dataSetList.getRows();
            if (CollectionUtils.isNotEmpty(list)) {
            	for(RadiatePersonnelRegister r : list){
            		String checkupCurrentResult = r.getCheckupDate() + "【" + r.getCheckupCurrentResult() + "】";
            		r.setCheckupCurrentResult(checkupCurrentResult);
            	}
				ExportUtil.export(request, response, list, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

    }

	/**
	 * @Title saveInfo
	 * @Description 新增放射人员关联信息
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025-05-21
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增放射人员关联信息", notes = "新增放射人员关联信息")
	@PostMapping("/api/RadiatePersonnelRegister/saveInfo")
	public PlatformResult<String> saveInfo(@RequestBody RadiatePersonnelVo record) {
		try {
			radiatePersonnelRegisterService.saveInfo(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateInfo
	 * @Description 更新放射人员关联信息
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025-05-21
	 * <AUTHOR>
	 */
	@ApiOperation(value = "更新放射人员关联信息", notes = "更新放射人员关联信息")
	@PostMapping("/api/RadiatePersonnelRegister/updateInfo")
	public PlatformResult<String> updateInfo(@RequestBody RadiatePersonnelVo record) {
		try {
			radiatePersonnelRegisterService.updateInfo(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * @Title selectByEmployeeId
	 * @Description 根据人员ID查询详情
	 * @param record
	 * @return PlatformResult<RadiatePersonnelVo>
	 * @date 2025-05-21
	 * <AUTHOR>
	 */
	@ApiOperation(value = "根据人员ID查询详情", notes = "根据人员ID查询详情")
	@GetMapping("/api/RadiatePersonnelRegister/detailByEmployeeId/{employeeId}")
	public PlatformResult<RadiatePersonnelVo> selectByEmployeeId(@PathVariable String employeeId) {
		try {
			RadiatePersonnelVo record = radiatePersonnelRegisterService.selectByEmployeeId(employeeId);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * 导入
	 * @param file
	 * @return
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "导入数据", notes = "导入数据")
	@PostMapping(value = "/api/RadiatePersonnelRegister/import")
	public PlatformResult importDate(@RequestParam("file") MultipartFile file) {
		 try {
			 	List<RadiatePersonnelRegister> list = (List<RadiatePersonnelRegister>) ImportExcelUtil.getExcelDatas(file, RadiatePersonnelRegister.class);

	            if (!list.isEmpty()) {
	            	
	            	return radiatePersonnelRegisterService.importData(list);
	            	
	            } else {
	                return PlatformResult.failure("数据为空");
	            }

	        } catch (Exception e) {
	            e.printStackTrace();
	            logger.error(e.getMessage(), e);
	            return PlatformResult.failure("导入数据失败，失败原因:" + e.getMessage());
	        }
	}
}

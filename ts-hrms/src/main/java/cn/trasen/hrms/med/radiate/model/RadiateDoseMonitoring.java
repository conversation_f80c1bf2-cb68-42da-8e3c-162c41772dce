package cn.trasen.hrms.med.radiate.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import org.jeecgframework.poi.excel.annotation.Excel;

import lombok.*;

/**
 * @Description 剂量监测
 * @date 2025-05-12 11:52:53
 * <AUTHOR> @version 1.0
 */
@Table(name = "med_radiate_dose_monitoring")
@Setter
@Getter
public class RadiateDoseMonitoring {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 人员id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "人员id")
    private String employeeId;

    /**
     * 人员工号
     */
    @Excel(name = "工号*")
    @Column(name = "employee_no")
    @ApiModelProperty(value = "人员工号")
    private String employeeNo;

    /**
     * 人员姓名
     */
    @Excel(name = "姓名*")
    @Column(name = "employee_name")
    @ApiModelProperty(value = "人员姓名")
    private String employeeName;

    /**
     * 人员科室id
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "人员科室id")
    private String orgId;

    /**
     * 人员科室名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "人员科室名称")
    private String orgName;
    
    /**
     * 剂量计类型
     */
    @Excel(name = "剂量计类型*")
    @Column(name = "dose_type")
    @ApiModelProperty(value = "剂量计类型：1-腕式/铅衣(外),2-胸章/铅衣(内)")
    private String doseType;

    /**
     * 监测周期:1季度,2年度
     */
    @Column(name = "monitoring_period")
    @ApiModelProperty(value = "监测周期:1-季度,2-年度")
    private String monitoringPeriod;

    /**
     * 监测年份
     */
    @Excel(name = "监测年份(YYYY)*")
    @Column(name = "monitoring_year")
    @ApiModelProperty(value = "监测年份")
    private String monitoringYear;

    /**
     * 监测季度,1,2,3,4季
     */
    @Excel(name = "监测季度(1,2,3,4)")
    @Column(name = "monitoring_quarter")
    @ApiModelProperty(value = "监测季度,1,2,3,4季")
    private String monitoringQuarter;

    /**
     * 监测周期时间
     */
    @Column(name = "monitoring_date")
    @ApiModelProperty(value = "监测周期时间")
    private String monitoringDate;
    
    @Column(name = "expiration_date")
    @ApiModelProperty(value = "到期时间")
    private String expirationDate;

    /**
     * 辐射品质
     */
    @Excel(name = "辐射品质")
    @Column(name = "radiation_quality")
    @ApiModelProperty(value = "辐射品质")
    private String radiationQuality;

    /**
     * 光子辐射个人剂量当量H p(10)
     */
    @Excel(name = "Hp(10)*")
    @Column(name = "personal_dose_equivalent_hp10")
    @ApiModelProperty(value = "光子辐射个人剂量当量H p(10)")
    private String personalDoseEquivalentHp10;

    /**
     * 光子辐射个人剂量当量H p(3)
     */
    @Excel(name = "Hp(3)*")
    @Column(name = "personal_dose_equivalent_hp3")
    @ApiModelProperty(value = "光子辐射个人剂量当量H p(3)")
    private String personalDoseEquivalentHp3;

    /**
     * 光子辐射个人剂量当量H p(0.07)
     */
    @Excel(name = "Hp(007)*")
    @Column(name = "personal_dose_equivalent_hp007")
    @ApiModelProperty(value = "光子辐射个人剂量当量H p(0.07)")
    private String personalDoseEquivalentHp007;

    /**
     * 注释
     */
    @Excel(name = "注释")
    @ApiModelProperty(value = "注释")
    private String remarks;

    /**
     * 监测状态:1合格,2不合格
     */
    @Excel(name = "状态(合格/不合格)*")
    @Column(name = "monitor_status")
    @ApiModelProperty(value = "监测状态:1合格,2不合格")
    private String monitorStatus;

    @Excel(name = "检测单位")
    @Column(name = "test_org")
    @ApiModelProperty(value = "检测单位")
    private String testOrg;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String files;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 创建人所属部门编码
     */
    @Column(name = "create_dept")
    @ApiModelProperty(value = "创建人所属部门编码")
    private String createDept;

    /**
     * 创建人所属部门名称
     */
    @Column(name = "create_dept_name")
    @ApiModelProperty(value = "创建人所属部门名称")
    private String createDeptName;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标记
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标记")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
    
    @Transient
    @ApiModelProperty(value = "剂量计类型文本")
    private String doseTypeText;
    
    @Transient
    @ApiModelProperty(value = "监测周期:1-季度,2-年度")
    private String monitoringPeriodText;

    @Transient
    @ApiModelProperty(value = "状态")
    private String employeeStatus;
    
    @Transient
    @ApiModelProperty(value = "员工状态文本")
    private String employeeStatusText;
    
    @Transient
    @ApiModelProperty(value = "技术职称")
    private String technicalTitle;
    
    @Transient
    @ApiModelProperty(value = "性别")
    private String sex;
    
    @Transient
    @ApiModelProperty(value = "性别文本")
    private String sexText;
}
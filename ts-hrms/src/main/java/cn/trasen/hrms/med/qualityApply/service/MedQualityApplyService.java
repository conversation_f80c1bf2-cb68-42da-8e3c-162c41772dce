package cn.trasen.hrms.med.qualityApply.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.qualityApply.model.MedQualityApply;

/**
 * @ClassName MedQualityApplyService
 * @Description TODO
 * @date 2025��2��27�� ����11:15:06
 * <AUTHOR>
 * @version 1.0
 */
public interface MedQualityApplyService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��2��27�� ����11:15:06
	 * <AUTHOR>
	 */
	Integer save(MedQualityApply record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��2��27�� ����11:15:06
	 * <AUTHOR>
	 */
	Integer update(MedQualityApply record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��2��27�� ����11:15:06
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedQualityApply
	 * @date 2025��2��27�� ����11:15:06
	 * <AUTHOR>
	 */
	MedQualityApply selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedQualityApply>
	 * @date 2025��2��27�� ����11:15:06
	 * <AUTHOR>
	 */
	DataSet<MedQualityApply> getDataSetList(Page page, MedQualityApply record);

	void saveOrUpdate(MedQualityApply record);

	void cancel(MedQualityApply record);

	List<MedQualityApply> getMyQualityApply(String applyStatus,String userCode);

	void examine(MedQualityApply record);
	
	void updateExpireStatus();

	String getApplyInfo(String userCode);

	List<Map<String, Object>> getNoDataApplyDept();

	void queryQualityApplyEmr();
}

package cn.trasen.hrms.med.patient.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.patient.dao.PatientOrderrecordMapper;
import cn.trasen.hrms.med.patient.model.PatientInfo;
import cn.trasen.hrms.med.patient.model.PatientOrderrecord;
import cn.trasen.hrms.med.patient.service.PatientOrderrecordService;
import cn.trasen.hrms.utils.HnsrmyyHisJdbcUtil;
import lombok.extern.log4j.Log4j2;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PatientOrderrecordServiceImpl
 * @Description TODO
 * @date 2025��4��8�� ����4:42:41
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Log4j2
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PatientOrderrecordServiceImpl implements PatientOrderrecordService {

	@Autowired
	private PatientOrderrecordMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(PatientOrderrecord record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(PatientOrderrecord record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		PatientOrderrecord record = new PatientOrderrecord();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public PatientOrderrecord selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<PatientOrderrecord> getDataSetList(Page page, PatientOrderrecord record) {
		Example example = new Example(PatientOrderrecord.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<PatientOrderrecord> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	@Transactional(readOnly = false)
	@Override
	public void updateOrSavePatientOrderrecord() {
		// TODO Auto-generated method stub
		try {    		
			StringBuilder sb = new StringBuilder();
			
    		sb.append(" SELECT   A.ORDER_ID AS ID, A.INPATIENT_ID AS PATN_ID,C.INPATIENT_NO AS PATN_NO,ORDER_DOC,ORDER_BDATE,ORDER_EDATE,HOITEM_ID,ORDER_CONTEXT,STATUS_FLAG,MNGTYPE,A.BOOK_DATE,A.ORDER_UPDATE_TIME,A.BABY_ID,A.DEPT_BR,A.DEPT_ID,A.DELETE_BIT,a.DELETE_BIT  "
    				+ "         FROM  ODSZYV10.ZY_ORDERRECORD A  INNER JOIN ODSZYV10.ZY_INPATIENT B ON A.INPATIENT_ID=B.INPATIENT_ID    INNER  JOIN  ODSZYV10.BASE_PATIENT_PROPERTY  C ON B.PATIENT_ID=C.PATIENT_ID   "
    			
    				+ " ");
    		sb.append("   WHERE (A.BOOK_DATE >   TRUNC(SYSDATE -1)  OR A.ORDER_UPDATE_TIME>  TRUNC(SYSDATE -1) ) AND (ORDER_CONTEXT LIKE '%病危%' OR ORDER_CONTEXT LIKE '%病重%')   "
    				
    				+ " ");
    		
			//sb.append("select  id as csltAppyId,appy_No as appyNo,patn_Name as patnName from  med_cslt_appy  where id = ？  ");
    		//List<CsltAppySyncHis> CsltAppyList = 	HnsrmyyHisJdbcUtil.query(sb.toString(),CsltAppySyncHis.class);//执行语句返回结果,反射映射有问题
    		log.info("===========sql:"+sb.toString());
    		List<PatientOrderrecord> PatientOrderrecordList = HnsrmyyHisJdbcUtil.queryPatientOrderrecordSyncHis(sb.toString());//执行语句返回结果
    		log.info("===========PatientOrderrecordList:"+PatientOrderrecordList.size());
    		if(CollUtil.isNotEmpty(PatientOrderrecordList)){
    			for(PatientOrderrecord patientOrderrecord : PatientOrderrecordList) {
    				PatientOrderrecord record = mapper.selectByPrimaryKey(patientOrderrecord.getId());//根据患者id查询是否已经存在患者数据，存在则更新;
    				if(record != null) {
    					patientOrderrecord.setUpdateDate(DateUtil.date());
    					mapper.updateByPrimaryKeySelective(patientOrderrecord);
    				}else {
    					patientOrderrecord.setIsDeleted("N");
    					patientOrderrecord.setCreateDate(DateUtil.date());
    					patientOrderrecord.setUpdateDate(DateUtil.date());
    					mapper.insertSelective(patientOrderrecord);
    				}
    			}
    		}
		}catch(Exception e) {
    		e.printStackTrace();
    		log.error("获取影子库病人病危/病重医嘱信息数据异常：" + e.getMessage());
    	}
	}

	@Override
	public List<PatientOrderrecord> selectPatientOrderrecord() {
		Example example = new Example(PatientOrderrecord.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		
		return mapper.selectByExample(example);
	}
	
	
}

package cn.trasen.hrms.med.wheelScience.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.med.qua.dao.QuaAuthItemDetlMapper;
import cn.trasen.hrms.med.wheelScience.dao.MedWheelScienceMapper;
import cn.trasen.hrms.med.wheelScience.model.MedWheelScience;
import cn.trasen.hrms.med.wheelScience.service.MedWheelScienceService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedWheelScienceServiceImpl
 * @Description TODO
 * @date 2025��1��22�� ����2:57:52
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedWheelScienceServiceImpl implements MedWheelScienceService {

	@Autowired
	private MedWheelScienceMapper mapper;
	
	@Autowired
	private QuaAuthItemDetlMapper quaAuthItemDetlMapper;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedWheelScience record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		record.setStatus("1");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		
		Example example = new Example(MedWheelScience.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("docCode", record.getDocCode());
		criteria.andIn("status", ListUtil.of("1","2"));
		List<MedWheelScience> list = mapper.selectByExample(example);
		if(CollectionUtils.isNotEmpty(list)) {
			Assert.isTrue(false, record.getDocName() + "存在未办结的轮科数据，请勿重复提交！");
		}
		
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedWheelScience record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		//如果考核结果填了 轮科结果没填  
		if(StringUtils.isNotBlank(record.getWheelResult()) && StringUtils.isBlank(record.getWheelFinish())) {
			record.setWheelResultDate(new Date());
			record.setWheelResultUser(user.getUsercode());
			record.setWheelResultUserName(user.getUsername());
		}
		
		if(StringUtils.isNotBlank(record.getWheelFinish())) {
			record.setWheelFinishDate(new Date());
			record.setWheelFinishUser(user.getUsercode());
			record.setWheelFinishUserName(user.getUsername());
			
			MedWheelScience medWheelScience = mapper.selectByPrimaryKey(record.getId());
			
			if("1".equals(record.getWheelFinish())) { //1定科
				//更新归属科室
				mapper.updateEmpOrgInfo(medWheelScience.getDocCode(),record.getWheelFinishDept());
			}
			if("2".equals(record.getWheelFinish())) {//2继续轮科 
				//需要新增轮科数据
				medWheelScience.setWheelStartDate(record.getWheelFinishStartDate());
				medWheelScience.setWheelEndDate(record.getWheelFinishEndDate());
				medWheelScience.setWheelCycle(record.getWheelFinishCycle());
				medWheelScience.setWheelArea(record.getWheelFinishArea());
				medWheelScience.setWheelOrgId(record.getWheelFinishDept());
				medWheelScience.setWheelResult(null);
				medWheelScience.setWheelResultFiles(null);
				medWheelScience.setWheelResultUser(null);
				medWheelScience.setWheelResultUserName(null);
				medWheelScience.setWheelResultDate(null);
				medWheelScience.setWheelFinish(null);
				medWheelScience.setWheelFinishArea(null);
				medWheelScience.setWheelFinishDept(null);
				medWheelScience.setWheelFinishStartDate(null);
				medWheelScience.setWheelFinishEndDate(null);
				medWheelScience.setWheelFinishCycle(null);
				medWheelScience.setWheelFinishRemark(null);
				medWheelScience.setWheelFinishFiles(null);
				medWheelScience.setWheelFinishUser(null);
				medWheelScience.setWheelFinishUserName(null);
				medWheelScience.setWheelFinishDate(null);
				
				medWheelScience.setId(IdGeneraterUtils.nextId());
				medWheelScience.setCreateDate(new Date());
				medWheelScience.setUpdateDate(new Date());
				medWheelScience.setIsDeleted("N");
				medWheelScience.setStatus("1");
				if (user != null) {
					medWheelScience.setCreateUser(user.getUsercode());
					medWheelScience.setCreateUserName(user.getUsername());
					medWheelScience.setUpdateUser(user.getUsercode());
					medWheelScience.setUpdateUserName(user.getUsername());
				}
				medWheelScience.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				mapper.insertSelective(medWheelScience);
			}
		}
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedWheelScience record = new MedWheelScience();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedWheelScience selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedWheelScience> getDataSetList(Page page, MedWheelScience record) {
		
		Boolean isadmin = UserInfoHolder.ISADMIN();
		Boolean YWGLY = UserInfoHolder.getRight("YWGLY"); //医务管理员
		Boolean KSGLY = false;
		if(!isadmin && !YWGLY) {
			List<String> orgIdList = quaAuthItemDetlMapper.selectManageDept(UserInfoHolder.getCurrentUserCode());
			if(CollectionUtils.isNotEmpty(orgIdList)) {
				KSGLY = true;
				record.setOrgIdList(orgIdList);  //科主任看自己科室的
			}else {
				record.setDocCode(UserInfoHolder.getCurrentUserCode()); //医生看自己的
			}
		}
		
		if(StringUtils.isNoneBlank(record.getOrgId())) {
			List<String> orgIds = Arrays.asList(record.getOrgId().split(","));
			record.setOrgIds(orgIds);
		}
		
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		
		List<MedWheelScience> records = mapper.getDataSetList(record, page);
		
		List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
		List<DictItemResp> educations = dictItemFeignService.getDictItemByTypeCode("education_type").getObject();
		
		for (MedWheelScience medWheelScience : records) {
			
			medWheelScience.setRoleName("0");//普通用户
			
			if(KSGLY) {
				medWheelScience.setRoleName("1");//科室管理员
			}
			
			if(YWGLY) {
				medWheelScience.setRoleName("2");//医务管理员
			}
			
			if(isadmin) {
				medWheelScience.setRoleName("3"); //超级管理员
			}
			
			DictItemResp area = areas.stream().filter(j -> StrUtil.equals(medWheelScience.getDocArea(), j.getItemCode())).findFirst().orElse(null);
			medWheelScience.setDocAreaText(null == area ? medWheelScience.getDocArea() : area.getItemName());
			
			DictItemResp area2 = areas.stream().filter(j -> StrUtil.equals(medWheelScience.getWheelArea(), j.getItemCode())).findFirst().orElse(null);
			medWheelScience.setWheelAreaText(null == area2 ? medWheelScience.getWheelArea() : area2.getItemName());
			
			DictItemResp education = educations.stream().filter(j -> StrUtil.equals(medWheelScience.getDocEducation(), j.getItemCode())).findFirst().orElse(null);
			medWheelScience.setDocEducationText(null == education ? medWheelScience.getDocEducation() : education.getItemName());
			
			if(StringUtils.isNotBlank(medWheelScience.getDocGender())) {
				if("1".equals(medWheelScience.getDocGender())) {
					medWheelScience.setDocGenderText("女");
				}else {
					medWheelScience.setDocGenderText("男");
				}
			}else {
				medWheelScience.setDocGenderText("未知");
			}
			
			
		}
		
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

package cn.trasen.hrms.med.patient.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "med_patient_transfer_dept")
@Setter
@Getter
public class PatientTransferDept {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 患者主键
     */
    @Column(name = "patn_id")
    @ApiModelProperty(value = "患者主键")
    private String patnId;

    /**
     * 患者住院号
     */
    @Column(name = "patn_no")
    @ApiModelProperty(value = "患者住院号")
    private String patnNo;

    /**
     * 转科日期
     */
    @Column(name = "transfer_date")
    @ApiModelProperty(value = "转科日期")
    private Date transferDate;

    /**
     * 转出科室id
     */
    @Column(name = "out_dept_id")
    @ApiModelProperty(value = "转出科室id")
    private String outDeptId;
    
    /**
     * 转出科室名称
     */
    @Column(name = "out_dept_name")
    @ApiModelProperty(value = "转出科室名称")
    private String outDeptName;

    /**
     * 转入科室ID
     */
    @Column(name = "in_dept_id")
    @ApiModelProperty(value = "转入科室ID")
    private String inDeptId;
    
    
    /**
     * 转入科室名称
     */
    @Column(name = "in_dept_name")
    @ApiModelProperty(value = "转入科室名称")
    private String inDeptName;
    
    /**
     * 转科原因(EMR)
     */
    @Column(name = "transfer_reason")
    @ApiModelProperty(value = "转科原因(EMR)")
    private String transferReason;
    
    
    /**
     * 转科诊断(EMR)
     */
    @Column(name = "transfer_diagnosis")
    @ApiModelProperty(value = "转科诊断(EMR)")
    private String transferDiagnosis;
    
    /**
     * 取消标记
     */
    @Column(name = "cancel_bit")
    @ApiModelProperty(value = "取消标记")
    private String cancelBit;
    
    /**
     * 完成标记
     */
    @Column(name = "finish_bit")
    @ApiModelProperty(value = "完成标记")
    private String finishBit;
    
    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
}
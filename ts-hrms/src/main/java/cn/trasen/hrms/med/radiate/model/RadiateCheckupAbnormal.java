package cn.trasen.hrms.med.radiate.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * @Description 体检复查
 * @date 2025-05-12 11:52:53
 * <AUTHOR> @version 1.0
 */
@Table(name = "med_radiate_checkup_abnormal")
@Setter
@Getter
public class RadiateCheckupAbnormal {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 检查id
     */
    @Column(name = "checkup_id")
    @ApiModelProperty(value = "检查id")
    private String checkupId;

    /**
     * 复检状态
     */
    @Column(name = "abnormal_status")
    @ApiModelProperty(value = "复检状态：0-异常，1-正常")
    private String abnormalStatus;

    /**
     * 异常结果
     */
    @Column(name = "abnormal_result")
    @ApiModelProperty(value = "异常结果")
    private String abnormalResult;

    /**
     * 复查日期
     */
    @Column(name = "review_date")
    @ApiModelProperty(value = "复查日期")
    private String reviewDate;

    /**
     * 复查结果
     */
    @Column(name = "review_result")
    @ApiModelProperty(value = "复查结果")
    private String reviewResult;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String files;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 创建人所属部门编码
     */
    @Column(name = "create_dept")
    @ApiModelProperty(value = "创建人所属部门编码")
    private String createDept;

    /**
     * 创建人所属部门名称
     */
    @Column(name = "create_dept_name")
    @ApiModelProperty(value = "创建人所属部门名称")
    private String createDeptName;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标记
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标记")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
}
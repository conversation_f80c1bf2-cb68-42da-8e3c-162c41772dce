package cn.trasen.hrms.med.delivery.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.delivery.model.MedDeliveryDict;
import cn.trasen.hrms.med.delivery.model.MedDeliveryRecord;
import cn.trasen.hrms.med.delivery.service.MedDeliveryRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedDeliveryRecordController
 * @Description TODO
 * @date 2025��5��20�� ����3:31:41
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "外送检验项目记录")
public class MedDeliveryRecordController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedDeliveryRecordController.class);

	@Autowired
	private MedDeliveryRecordService medDeliveryRecordService;

	/**
	 * @Title saveMedDeliveryRecord
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��5��20�� ����3:31:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/medDeliveryRecord/save")
	public PlatformResult<String> saveMedDeliveryRecord(@RequestBody MedDeliveryRecord record) {
		try {
			medDeliveryRecordService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedDeliveryRecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��5��20�� ����3:31:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/medDeliveryRecord/update")
	public PlatformResult<String> updateMedDeliveryRecord(@RequestBody MedDeliveryRecord record) {
		try {
			medDeliveryRecordService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedDeliveryRecordById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedDeliveryRecord>
	 * @date 2025��5��20�� ����3:31:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/medDeliveryRecord/{id}")
	public PlatformResult<MedDeliveryRecord> selectMedDeliveryRecordById(@PathVariable String id) {
		try {
			MedDeliveryRecord record = medDeliveryRecordService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedDeliveryRecordById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��5��20�� ����3:31:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/medDeliveryRecord/delete/{id}")
	public PlatformResult<String> deleteMedDeliveryRecordById(@PathVariable String id) {
		try {
			medDeliveryRecordService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedDeliveryRecordList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedDeliveryRecord>
	 * @date 2025��5��20�� ����3:31:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/medDeliveryRecord/list")
	public DataSet<MedDeliveryRecord> selectMedDeliveryRecordList(Page page, MedDeliveryRecord record) {
		return medDeliveryRecordService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "流程回调", notes = "流程回调")
	@PostMapping("/api/medDeliveryRecord/finishExamine")
	public void finishExamine(HttpServletRequest request) {
		try {
			
			logger.info("========外送检验项目回调==========");
			
			Map<String, Object> formData = new HashMap<>();
			Enumeration<String> enu = request.getParameterNames();
			while (enu.hasMoreElements()) {
				String key = (String) enu.nextElement();
				formData.put(key, request.getParameter(key));
			}
			// 业务id
			String L_BusinessId = formData.get("L_BusinessId").toString(); //业务id
		    String L_LaunchUserCode = (String) formData.get("L_LaunchUserCode");//流程发起人编码
		    String L_LaunchUserName = (String) formData.get("L_LaunchUserName");//流程发起人名称
		    
		    MedDeliveryRecord record = new MedDeliveryRecord();
		    record.setApplyName((String) formData.get("applyName"));
		    record.setApplyTel((String) formData.get("applyTel"));
		    record.setInPatientNo((String) formData.get("inPatientNo"));
		    record.setSex((String) formData.get("sex"));
		    record.setAge((String) formData.get("age"));
		    record.setDiagnosis((String) formData.get("diagnosis"));
		    record.setIllness((String) formData.get("illness"));
		    record.setReason((String) formData.get("reason"));
		    record.setApplyDate((String) formData.get("applyDate"));
		    record.setApplyOrg((String) formData.get("applyOrg"));
		    record.setApplyArea((String) formData.get("applyArea"));
		    record.setPatientName((String) formData.get("patientName"));
		    record.setWorkflowId(L_BusinessId);
		    record.setCreateUser(L_LaunchUserCode);
		    record.setCreateUserName(L_LaunchUserName);
		    
		    String deliveryRecords = (String) formData.get("deliveryRecords");
		    
		    JSONArray jsonObject =	JSONObject.parseArray(deliveryRecords);
			
		    for (int i = 0; i < jsonObject.size(); i++) {
		    	
		    	JSONObject obj = jsonObject.getJSONObject(i);
	   			
	   			record.setDictId(obj.getString("id"));
	   			record.setProjectName(obj.getString("projectName"));
	   			record.setSampleType(obj.getString("sampleType"));
	   			record.setSampleDose(obj.getString("sampleDose"));
	   			record.setTestOrg(obj.getString("testOrg"));
	   			record.setCost(obj.getString("cost"));
	   			record.setConnotation(obj.getString("connotation"));
	   			
	   			medDeliveryRecordService.save(record);
		    }
		    
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/medDeliveryRecord/export")
	public void export(Page page,HttpServletResponse response, HttpServletRequest request,MedDeliveryRecord record) {
		try {
			page.setPageNo(1);
			page.setPageSize(Integer.MAX_VALUE);
			
			DataSet<MedDeliveryRecord> dataSet = medDeliveryRecordService.getDataSetList(page, record);
			List<MedDeliveryRecord> list = dataSet.getRows();
			
			int i = 1;
			for (MedDeliveryRecord medDeliveryRecord : list) {
				medDeliveryRecord.setOrderNumber(i);
				i++;
			}
			
			//表头标题
            String name = "外送检验项目申请表" + DateUtil.format(new Date(),"yyyyMMdd") + ".xlsx";
            // 模板位置
            String templateUrl = "template/medDeliveryRecordExport.xlsx";
            
            Map<String, Object> map = new HashMap<String, Object>();
		    map.put("list", list);
		    map.put("exportDate", DateUtil.format(new Date(),"yyyy-MM-dd"));
		    map.put("exportUserName", UserInfoHolder.getCurrentUserName());
            
            cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, map, name, templateUrl);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}

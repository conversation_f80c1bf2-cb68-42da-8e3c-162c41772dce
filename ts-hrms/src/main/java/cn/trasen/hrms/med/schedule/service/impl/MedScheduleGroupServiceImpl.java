package cn.trasen.hrms.med.schedule.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.ListUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.schedule.dao.MedScheduleGroupMapper;
import cn.trasen.hrms.med.schedule.model.MedScheduleGroup;
import cn.trasen.hrms.med.schedule.model.MedScheduleGroupUser;
import cn.trasen.hrms.med.schedule.model.MedScheduleRecord;
import cn.trasen.hrms.med.schedule.model.MedScheduleType;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;
import cn.trasen.hrms.med.schedule.service.MedScheduleGroupService;
import cn.trasen.hrms.med.schedule.service.MedScheduleGroupUserService;
import cn.trasen.hrms.med.schedule.service.MedScheduleRecordService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedScheduleGroupServiceImpl
 * @Description TODO
 * @date 2025��4��3�� ����5:49:53
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedScheduleGroupServiceImpl implements MedScheduleGroupService {

	@Autowired
	private MedScheduleGroupMapper mapper;
	
	@Autowired
	private MedScheduleGroupUserService medScheduleGroupUserService;
	
	@Autowired
	private MedScheduleRecordService medScheduleRecordService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedScheduleGroup record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		record.setGroupSort(0);
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
//		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		
		if(StringUtils.isNotBlank(record.getGroupEmpId())) {
			
			List<String> groupEmpIdList =  ListUtil.of(record.getGroupEmpId().split(","));
			
			int i = 0;
			
			for (String empId : groupEmpIdList) {
				
				MedScheduleGroupUser medScheduleGroupUser = new MedScheduleGroupUser();
				medScheduleGroupUser.setEmployeeId(empId);
				medScheduleGroupUser.setGroupId(record.getId());
				medScheduleGroupUser.setEmpSort(i);
				medScheduleGroupUser.setCreateUser(user.getUsercode());
				medScheduleGroupUserService.save(medScheduleGroupUser);
				
				ScheduleEmployee scheduleEmployee = new ScheduleEmployee();
				scheduleEmployee.setEmployeeId(empId);
				List<MedScheduleRecord> medScheduleRecordList = medScheduleRecordService.selectMedScheduleRecordList(scheduleEmployee);
				
				if(CollectionUtils.isNotEmpty(medScheduleRecordList)) {
					List<String> updateRecordIds = new ArrayList<>();
					for (MedScheduleRecord medScheduleRecord : medScheduleRecordList) {
						updateRecordIds.add(medScheduleRecord.getId());
					}
					medScheduleRecordService.updateGroupId(record.getId(),updateRecordIds);
				}
				
				i++;
			}
		}
		
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedScheduleGroup record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		if(StringUtils.isNotBlank(record.getGroupEmpId())) {
			List<String> groupEmpIdList =  ListUtil.of(record.getGroupEmpId().split(","));
			
			int i = 0;
			
			medScheduleGroupUserService.deleteByGroupId(record.getId());
			
			for (String empId : groupEmpIdList) {
				
				MedScheduleGroupUser medScheduleGroupUser = new MedScheduleGroupUser();
				medScheduleGroupUser.setEmployeeId(empId);
				medScheduleGroupUser.setGroupId(record.getId());
				medScheduleGroupUser.setEmpSort(i);
				medScheduleGroupUser.setCreateUser(user.getUsercode());
				medScheduleGroupUserService.save(medScheduleGroupUser);
				
				ScheduleEmployee scheduleEmployee = new ScheduleEmployee();
				scheduleEmployee.setEmployeeId(empId);
				List<MedScheduleRecord> medScheduleRecordList = medScheduleRecordService.selectMedScheduleRecordList(scheduleEmployee);
				
				if(CollectionUtils.isNotEmpty(medScheduleRecordList)) {
					List<String> updateRecordIds = new ArrayList<>();
					for (MedScheduleRecord medScheduleRecord : medScheduleRecordList) {
						updateRecordIds.add(medScheduleRecord.getId());
					}
					medScheduleRecordService.updateGroupId(record.getId(),updateRecordIds);
				}
				
				i++;
			}
		}
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedScheduleGroup record = new MedScheduleGroup();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		medScheduleGroupUserService.deleteByGroupId(id);
		
		medScheduleRecordService.deleteByGroupId(id);
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedScheduleGroup selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedScheduleGroup> getDataSetList(Page page, MedScheduleGroup record) {
		Example example = new Example(MedScheduleGroup.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("createUser", UserInfoHolder.getCurrentUserCode());
		List<MedScheduleGroup> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	@Transactional(readOnly = false)
	public void updateSeq(List<MedScheduleGroup> records) {
		if(CollectionUtils.isNotEmpty(records)) {
			for (MedScheduleGroup medScheduleGroup : records) {
				mapper.updateByPrimaryKeySelective(medScheduleGroup);
			}
		}
	}
	
	
}

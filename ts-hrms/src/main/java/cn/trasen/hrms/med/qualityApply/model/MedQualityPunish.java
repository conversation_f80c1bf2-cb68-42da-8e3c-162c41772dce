package cn.trasen.hrms.med.qualityApply.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "med_quality_punish")
@Setter
@Getter
public class MedQualityPunish {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 申请表id
     */
    @Column(name = "quality_id")
    @ApiModelProperty(value = "申请表id")
    private String qualityId;

    /**
     * 延长任期结束时长
     */
    @Column(name = "punish_end_date")
    @ApiModelProperty(value = "延长任期结束时长")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date punishEndDate;

    /**
     * 处罚结果
     */
    @Column(name = "punish_result")
    @ApiModelProperty(value = "处罚结果")
    private String punishResult;

    /**
     * 处罚时间
     */
    @Column(name = "punish_date")
    @ApiModelProperty(value = "处罚时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date punishDate;

    /**
     * 处罚原因
     */
    @Column(name = "punish_remark")
    @ApiModelProperty(value = "处罚原因")
    private String punishRemark;
}
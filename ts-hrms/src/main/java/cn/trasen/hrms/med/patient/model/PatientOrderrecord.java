package cn.trasen.hrms.med.patient.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "med_patient_orderrecord")
@Setter
@Getter
public class PatientOrderrecord {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 患者主键
     */
    @Column(name = "patn_id")
    @ApiModelProperty(value = "患者主键")
    private String patnId;

    /**
     * 患者住院号
     */
    @Column(name = "patn_no")
    @ApiModelProperty(value = "患者住院号")
    private String patnNo;

    /**
     * 婴儿ID
     */
    @Column(name = "baby_id")
    @ApiModelProperty(value = "婴儿ID")
    private String babyId;
    
    /**
     *病人所在科室
     */
    @Column(name = "dept_br")
    @ApiModelProperty(value = "病人所在科室")
    private String deptBr;
    
    /**
     * 开单科室
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "开单科室")
    private String deptId;
    
    /**
     * 医嘱医生
     */
    @Column(name = "order_doc")
    @ApiModelProperty(value = "医嘱医生")
    private String orderDoc;

    /**
     * 医嘱开始日期
     */
    @Column(name = "order_bdate")
    @ApiModelProperty(value = "医嘱开始日期")
    private Date orderBdate;

    /**
     * 医嘱结束日期
     */
    @Column(name = "order_edate")
    @ApiModelProperty(value = "医嘱结束日期")
    private Date orderEdate;

    /**
     * 医嘱状态
     */
    @Column(name = "status_flag")
    @ApiModelProperty(value = "医嘱状态 0-医生保存1-医生发送 2-已经转抄3-医生停嘱 4-转抄停嘱 5-执行完毕 9补录医嘱")
    private String statusFlag;

    /**
     * 医嘱类型(0长期医嘱1临时医嘱2长期账单3临时账单5交病人医嘱)
     */
    @ApiModelProperty(value = "医嘱类型(0长期医嘱1临时医嘱2长期账单3临时账单5交病人医嘱)")
    private String mngtype;

    /**
     * 操作日期
     */
    @Column(name = "book_date")
    @ApiModelProperty(value = "操作日期")
    private Date bookDate;
    
    /**
     * 及时更新时间
     */
    @Column(name = "order_update_time")
    @ApiModelProperty(value = "及时更新时间")
    private Date orderUpdateTime;
    
    /**
     * 删除标志
     */
    @Column(name = "delete_bit")
    @ApiModelProperty(value = "删除标志")
    private String deleteBit;
    
    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 医嘱id
     */
    @Column(name = "hoitem_id")
    @ApiModelProperty(value = "医嘱id")
    private String hoitemId;

    /**
     * 医嘱内容
     */
    @Column(name = "order_context")
    @ApiModelProperty(value = "医嘱内容")
    private String orderContext;
}
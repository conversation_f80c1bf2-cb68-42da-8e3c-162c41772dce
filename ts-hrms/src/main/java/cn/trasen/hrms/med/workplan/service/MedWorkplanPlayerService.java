package cn.trasen.hrms.med.workplan.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.workplan.model.MedWorkplanPlayer;

/**
 * @ClassName MedWorkplanPlayerService
 * @Description TODO
 * @date 2024��11��12�� ����11:53:21
 * <AUTHOR>
 * @version 1.0
 */
public interface MedWorkplanPlayerService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��11��12�� ����11:53:21
	 * <AUTHOR>
	 */
	Integer save(MedWorkplanPlayer record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��11��12�� ����11:53:21
	 * <AUTHOR>
	 */
	Integer update(MedWorkplanPlayer record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��11��12�� ����11:53:21
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedWorkplanPlayer
	 * @date 2024��11��12�� ����11:53:21
	 * <AUTHOR>
	 */
	MedWorkplanPlayer selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedWorkplanPlayer>
	 * @date 2024��11��12�� ����11:53:21
	 * <AUTHOR>
	 */
	DataSet<MedWorkplanPlayer> getDataSetList(Page page, MedWorkplanPlayer record);

	/**
	 * 
	 * @param workPlanId
	 */
	void deleteByWorkPlanId(String workPlanId);

	/**
	 * 
	 * @param id
	 * @return
	 */
	List<MedWorkplanPlayer> selectByWorkplanId(String id);
}

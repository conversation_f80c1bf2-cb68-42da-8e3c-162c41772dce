package cn.trasen.hrms.med.schedule.model;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;

@Table(name = "med_schedule_group_user")
@Setter
@Getter
public class MedScheduleGroupUser {
    @Id
    private String id;

    @Column(name = "group_id")
    private String groupId;

    @Column(name = "employee_id")
    private String employeeId;

    @Column(name = "emp_sort")
    private Integer empSort;
    
    @Column(name = "create_user")
    private String createUser;
}
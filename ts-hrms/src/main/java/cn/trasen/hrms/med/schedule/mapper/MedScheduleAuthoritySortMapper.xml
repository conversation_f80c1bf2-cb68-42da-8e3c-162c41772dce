<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.schedule.dao.MedScheduleAuthoritySortMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.schedule.model.MedScheduleAuthoritySort">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="authority_id" jdbcType="VARCHAR" property="authorityId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
  </resultMap>
</mapper>
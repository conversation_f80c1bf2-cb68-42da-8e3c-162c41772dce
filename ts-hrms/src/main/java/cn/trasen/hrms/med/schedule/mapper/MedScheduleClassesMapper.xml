<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.schedule.dao.MedScheduleClassesMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.schedule.model.MedScheduleClasses">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="type_id" jdbcType="VARCHAR" property="typeId" />
    <result column="classes_name" jdbcType="VARCHAR" property="classesName" />
    <result column="classes_days" jdbcType="VARCHAR" property="classesDays" />
    <result column="classes_color" jdbcType="VARCHAR" property="classesColor" />
    <result column="classes_type" jdbcType="VARCHAR" property="classesType" />
    <result column="classes_remark" jdbcType="VARCHAR" property="classesRemark" />
    <result column="classes_worktime" jdbcType="VARCHAR" property="classesWorktime" />
    <result column="classes_status" jdbcType="VARCHAR" property="classesStatus" />
    <result column="classes_use_names" jdbcType="VARCHAR" property="classesUseNames" />
    <result column="classes_use_org" jdbcType="VARCHAR" property="classesUseOrg" />
    <result column="classes_use_user" jdbcType="VARCHAR" property="classesUseUser" />
    <result column="holiday_type" jdbcType="VARCHAR" property="holidayType" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>
  
  <select id="getScheduleClassesList" resultType="cn.trasen.hrms.med.schedule.model.MedScheduleClasses" parameterType="cn.trasen.hrms.med.schedule.model.MedScheduleClasses">
  		select * from med_schedule_classes
  		where is_deleted = 'N' and classes_status = '1'
  		<if test="classesName != null and classesName != ''">
  			and classes_name like CONCAT(CONCAT('%', #{classesName}), '%')
  		</if>
  		<if test="classesType != null and classesType != ''">
  			and classes_type=#{classesType}
  		</if>
  		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and sso_org_code=#{ssoOrgCode}
  		</if>
  		<choose>
	      <!-- 既有科室也有人员时 -->
	      <when test="scheduleOrgList != null and scheduleOrgList.size() > 0 and scheduleUserList != null and scheduleUserList.size() > 0">
	        and (
			    <foreach collection="scheduleOrgList" index="index" item="item" separator="or">
			      find_in_set(#{item}, classes_use_org)
			    </foreach>
			    OR
			    <foreach collection="scheduleUserList" index="index" item="item" separator="or">
			      find_in_set(#{item}, classes_use_user)
			    </foreach>
			    <if test="isPbAdmin != null and isPbAdmin != ''">
			      or class_attributes = '1'
		  		</if>
		  		  or create_user=#{createUser}
			  )
	      </when>
	      <!-- 只有科室时 -->
	      <when test="scheduleOrgList != null and scheduleOrgList.size() > 0">
	        and (
			    <foreach collection="scheduleOrgList" index="index" item="item" separator="or">
			      find_in_set(#{item}, classes_use_org)
			    </foreach>
			    <if test="isPbAdmin != null and isPbAdmin != ''">
			      or class_attributes = '1'
		  		</if>
		  		  or create_user=#{createUser}
			  )
	      </when>
	      <!-- 只有人员时 -->
	      <when test="scheduleUserList != null and scheduleUserList.size() > 0">
	       and (
			    <foreach collection="scheduleUserList" index="index" item="item" separator="or">
			      find_in_set(#{item}, classes_use_user)
			    </foreach>
			    <if test="isPbAdmin != null and isPbAdmin != ''">
			      or class_attributes = '1'
		  		</if>
		  		  or create_user=#{createUser}
			  )
	      </when>
	      <!-- 查看所有数据时 -->
	      <when test="isAll != null and isAll != ''">
	       
	      </when>
	      <!-- 所有 when 不满足时执行 -->
	      <otherwise>
	        and (class_attributes = '1'  or create_user=#{createUser})
	      </otherwise>
	    </choose>
		
  		<if test="ids != null and ids.size() > 0">
		  and ( id in 
	        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
		    <if test="isPbAdmin != null and isPbAdmin != ''">
		      or class_attributes = '1'
	  		</if>
	  		 or create_user=#{createUser}
		  )
		</if>
  </select>
  
  <select id="getDataSetList" resultType="cn.trasen.hrms.med.schedule.model.MedScheduleClasses" parameterType="cn.trasen.hrms.med.schedule.model.MedScheduleClasses">
  		select t1.*,t2.type_name from med_schedule_classes t1
  		left join med_schedule_type t2 on t1.type_id = t2.id
  		where t1.is_deleted = 'N'
  		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and t1.sso_org_code = #{ssoOrgCode}
  		</if>
  		<if test="typeId != null and typeId != ''">
  			and t1.type_id = #{typeId}
  		</if>
  		<if test="classesName != null and classesName != ''">
  			and t1.classes_name like CONCAT(CONCAT('%', #{classesName}), '%')
  		</if>
  		<if test="classAttributes != null and classAttributes != ''">
  			and t1.class_attributes = #{classAttributes}
  		</if>
  		<if test="classesStatus != null and classesStatus != ''">
  			and t1.classes_status = #{classesStatus}
  		</if>
  		<if test="classesUseNames != null and classesUseNames != ''">
  			and t1.classes_use_names like CONCAT(CONCAT('%', #{classesUseNames}), '%')
  		</if>
  		<choose>
	      <!-- 既有科室也有人员时 -->
	      <when test="scheduleOrgList != null and scheduleOrgList.size() > 0 and scheduleUserList != null and scheduleUserList.size() > 0">
	        and (
			    <foreach collection="scheduleOrgList" index="index" item="item" separator="or">
			      find_in_set(#{item}, t1.classes_use_org)
			    </foreach>
			    OR
			    <foreach collection="scheduleUserList" index="index" item="item" separator="or">
			      find_in_set(#{item}, t1.classes_use_user)
			    </foreach>
			    <if test="isPbAdmin != null and isPbAdmin != ''">
			      or t1.class_attributes = '1'
		  		</if>
		  		  or t1.create_user=#{createUser}
			  )
	      </when>
	      <!-- 只有科室时 -->
	      <when test="scheduleOrgList != null and scheduleOrgList.size() > 0">
	        and (
			    <foreach collection="scheduleOrgList" index="index" item="item" separator="or">
			      find_in_set(#{item}, t1.classes_use_org)
			    </foreach>
			    <if test="isPbAdmin != null and isPbAdmin != ''">
			      or t1.class_attributes = '1'
		  		</if>
		  		  or t1.create_user=#{createUser}
			  )
	      </when>
	      <!-- 只有人员时 -->
	      <when test="scheduleUserList != null and scheduleUserList.size() > 0">
	       and (
			    <foreach collection="scheduleUserList" index="index" item="item" separator="or">
			      find_in_set(#{item}, t1.classes_use_user)
			    </foreach>
			    <if test="isPbAdmin != null and isPbAdmin != ''">
			      or t1.class_attributes = '1'
		  		</if>
		  		  or t1.create_user=#{createUser}
			  )
	      </when>
	      <!-- 所有 when 不满足时执行 -->
	      
	      <when test="createUser != null and createUser != ''">
	       and (t1.class_attributes = '1'  or t1.create_user=#{createUser})
	      </when>
	      <otherwise>
	      </otherwise>
	    </choose>
		
  		<if test="ids != null and ids.size() > 0">
		  and ( t1.id in 
	        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
		    <if test="isPbAdmin != null and isPbAdmin != ''">
		      or t1.class_attributes = '1'
	  		</if>
		  )
		</if>
		<if test="classIdList != null and classIdList.size() > 0">
		  and ( t1.id in 
	        <foreach collection="classIdList" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
		  )
		</if>
  		
  </select>
  
  <select id="getScheduleClassesByEmp" resultType="cn.trasen.hrms.med.schedule.model.MedScheduleClasses" parameterType="cn.trasen.hrms.med.schedule.model.MedScheduleClasses">
  		select id from med_schedule_classes
  		where is_deleted = 'N' and classes_status = '1'
  		
  		<if test="employeeNo != null and employeeNo != ''">
  			and (
  				find_in_set(#{employeeNo}, classes_use_user)
  				or
  				find_in_set(#{orgCode}, classes_use_org)
  				or class_attributes = '1'
  			)
  		</if>
  		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and sso_org_code = #{ssoOrgCode}
  		</if>
  </select>
  
  <select id="getEmpListByDeptCodes" resultType="cn.trasen.homs.bean.base.EmployeeResp">
  		SELECT 
			e.*,
			i.*,
			o.NAME AS orgName, 
			o.CODE AS orgCode, 
			a4.post_name as postName,
			a5.position_name as positionName 
		FROM 
		cust_emp_base e 
		left join cust_emp_info i on e.employee_id = i.info_id 
		LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted ='N'
		LEFT JOIN comm_post AS a4 on i.post_id=a4.post_id
		left join comm_position as a5 on e.position_id=a5.position_id
		WHERE e.is_deleted ='N' and e.employee_status in ('1','5','6','9','12','99')
		<if test="scheduleOrgList !=null and scheduleOrgList.size()>0">
			AND o.code in
			<foreach collection="scheduleOrgList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		
  		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and e.sso_org_code = #{ssoOrgCode}
  		</if>
		order by e.emp_sort,e.employee_no
  </select>
</mapper>
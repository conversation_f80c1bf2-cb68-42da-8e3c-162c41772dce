package cn.trasen.hrms.med.schedule.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.schedule.model.MedScheduleTemplate;
import cn.trasen.hrms.med.schedule.model.MedScheduleTemplateExtend;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;

/**
 * @ClassName MedScheduleTemplateService
 * @Description TODO
 * @date 2025��4��1�� ����6:48:40
 * <AUTHOR>
 * @version 1.0
 */
public interface MedScheduleTemplateService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��4��1�� ����6:48:40
	 * <AUTHOR>
	 */
	Integer save(MedScheduleTemplate record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��4��1�� ����6:48:40
	 * <AUTHOR>
	 */
	Integer update(MedScheduleTemplate record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��4��1�� ����6:48:40
	 * <AUTHOR>
	 */
	Integer deleteById(String id);
	
	/**
	 * 
	 * @Title updateStatus
	 * @Description 更新启停
	 * @param id
	 * @return Integer
	 * @date 2025-06-27 14:30:00
	 * <AUTHOR>
	 */
	Integer updateStatus(String status);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedScheduleTemplate
	 * @date 2025��4��1�� ����6:48:40
	 * <AUTHOR>
	 */
	MedScheduleTemplate selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleTemplate>
	 * @date 2025��4��1�� ����6:48:40
	 * <AUTHOR>
	 */
	DataSet<MedScheduleTemplate> getDataSetList(Page page, MedScheduleTemplate record);

	void batchInsert(List<MedScheduleTemplate> list);

	@Deprecated
	List<MedScheduleTemplate> getScheduleTemplateList();
	
	void saveMedScheduleTemplate(ScheduleEmployee record);
	
	/**
	 * @Title getScheduleTemplateExtend
	 * @Description 查询当前登录人的模板信息
	 * @return MedScheduleTemplateExtend
	 * @date 2025-07-03 12:30:30
	 * <AUTHOR>
	 */
	MedScheduleTemplateExtend getScheduleTemplateExtend();
}

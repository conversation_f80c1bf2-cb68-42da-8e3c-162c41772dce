package cn.trasen.hrms.med.radiate.emun;

/**
 * 剂量监测周期
 * <AUTHOR>
 *
 */
public enum MonitoringPeriodEmun {
	
	EVERY_QUARTER("1", "季度"),
	EVERY_YEAR("2", "每年"); 
	
	private String name;

	private String code;

	MonitoringPeriodEmun(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	

	/**
	 * 根据编码获取name
	 * 
	 * @param code
	 * @return
	 */
	public static String convert(String code) {
		for (MonitoringPeriodEmun emun : values()) {
			if (emun.getCode().equals(code))
				return emun.name;
		}
		return null;
	}

}

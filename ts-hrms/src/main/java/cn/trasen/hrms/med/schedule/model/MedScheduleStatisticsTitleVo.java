package cn.trasen.hrms.med.schedule.model;

import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 排班统计动态表头
 * <AUTHOR>
 *
 */
@Setter
@Getter
public class MedScheduleStatisticsTitleVo {

    @ApiModelProperty(value = "字段中文名")
	private String label;

    @ApiModelProperty(value = "字段名")
	private String prop;

    @ApiModelProperty(value = "字段名")
	private String align;

    @ApiModelProperty(value = "字段宽度")
	private Integer width;

    @ApiModelProperty(value = "字段固定")
	private String fixed;
    
    public MedScheduleStatisticsTitleVo(String label, String prop, String align, Integer width, String fixed){
    	this.label = label;
    	this.prop = prop;
    	this.align = align;
    	this.width = width;
    	this.fixed = fixed;
    }
    
    /**
     * 获取个人排班统计表头
     * @return
     */
    public static List<MedScheduleStatisticsTitleVo> getPersonalStatisticsTitleList(){
    	List<MedScheduleStatisticsTitleVo> titleList = new ArrayList<>();
    	titleList.add(new MedScheduleStatisticsTitleVo("归属院区", "hospName", "left", 80, "left"));
    	titleList.add(new MedScheduleStatisticsTitleVo("归属科室", "deptName", "left", 80, "left"));
    	titleList.add(new MedScheduleStatisticsTitleVo("姓名", "employeeName", "left", 80, "left"));
    	titleList.add(new MedScheduleStatisticsTitleVo("人员类别", "orgAttributes", "left", 80, null));
    	titleList.add(new MedScheduleStatisticsTitleVo("职务", "positionName", "left", 80, null));
    	titleList.add(new MedScheduleStatisticsTitleVo("编制类型", "establishmentType", "left", 80, null));
    	titleList.add(new MedScheduleStatisticsTitleVo("职称", "technical", "left", 80, null));
    	titleList.add(new MedScheduleStatisticsTitleVo("联系方式", "phoneNumber", "left", 80, null));

    	titleList.add(new MedScheduleStatisticsTitleVo("出勤情况", "attendance", "center", 80, null));
    	titleList.add(new MedScheduleStatisticsTitleVo("应出勤(天)", "scheduledAttendance", "center", 80, null));
    	titleList.add(new MedScheduleStatisticsTitleVo("实际出勤(天)", "actualAttendance", "center", 80, null));
    	return titleList;
    }
    
    /**
     * 获取科室排班统计表头
     * @return
     */
    public static List<MedScheduleStatisticsTitleVo> getDeptStatisticsTitleList(){
    	List<MedScheduleStatisticsTitleVo> titleList = new ArrayList<>();
    	titleList.add(new MedScheduleStatisticsTitleVo("归属科室", "deptName", "left", 80, "left"));
    	titleList.add(new MedScheduleStatisticsTitleVo("出勤人数", "attendanceUserCount", "center", 80, "left"));
    	titleList.add(new MedScheduleStatisticsTitleVo("应出勤(天)", "scheduledAttendance", "center", 80, "left"));
    	titleList.add(new MedScheduleStatisticsTitleVo("实际出勤(天)", "actualAttendance", "center", 80, "left"));
    	titleList.add(new MedScheduleStatisticsTitleVo("平均实际出勤(天)", "averageAttendance", "center", 80, "left"));
    	return titleList;
    }
    
    /**
     * 获取分类排班统计表头
     * @return
     */
    public static List<MedScheduleStatisticsTitleVo> getCategoryStatisticsTitleList(){
    	List<MedScheduleStatisticsTitleVo> titleList = new ArrayList<>();
    	titleList.add(new MedScheduleStatisticsTitleVo("序号", "index", "left", 80, "left"));
    	titleList.add(new MedScheduleStatisticsTitleVo("归属院区", "hospName", "left", 80, "left"));
    	titleList.add(new MedScheduleStatisticsTitleVo("归属科室", "deptName", "left", 80, "left"));
    	titleList.add(new MedScheduleStatisticsTitleVo("姓名", "employeeName", "left", 80, "left"));
    	titleList.add(new MedScheduleStatisticsTitleVo("应出勤(天)", "scheduledAttendance", "center", 80, "left"));
    	titleList.add(new MedScheduleStatisticsTitleVo("实际出勤(天)", "actualAttendance", "center", 80, "left"));
    	return titleList;
    }

}

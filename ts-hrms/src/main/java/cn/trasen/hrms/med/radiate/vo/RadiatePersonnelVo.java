package cn.trasen.hrms.med.radiate.vo;

import java.util.List;

import cn.trasen.hrms.med.radiate.model.RadiateCheckupRegister;
import cn.trasen.hrms.med.radiate.model.RadiateDoseMonitoring;
import cn.trasen.hrms.med.radiate.model.RadiateMonitor;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;
import cn.trasen.hrms.med.radiate.model.RadiateQualificationCertificate;
import cn.trasen.hrms.med.radiate.model.RadiateTrainingRegister;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 放射诊疗人员信息
 * @date 2025-05-22 11:52:53
 * <AUTHOR> @version 1.0
 */
@Setter
@Getter
public class RadiatePersonnelVo {
	
	@ApiModelProperty(value = "基本信息")
	private RadiatePersonnelRegister personnel;
	
	@ApiModelProperty(value = "监测列表")
	private List<RadiateMonitor> monitorList;
	
	@ApiModelProperty(value = "证件资质列表")
	private List<RadiateQualificationCertificate> certificateList;
	
	@ApiModelProperty(value = "体检状况列表")
	private List<RadiateCheckupRegister> checkupList;
	
	@ApiModelProperty(value = "培训状况列表")
	private List<RadiateTrainingRegister> trainingList;
	
	@ApiModelProperty(value = "剂量监测列表")
	private List<RadiateDoseMonitoring> DoseList;

}

package cn.trasen.hrms.med.radiate.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.hrms.med.radiate.emun.MonitoringPeriodEmun;
import cn.trasen.hrms.med.radiate.emun.RegisterCategoryEmun;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.bean.ResultData;
import cn.trasen.hrms.med.radiate.dao.RadiateDoseMonitoringMapper;
import cn.trasen.hrms.med.radiate.dao.RadiatePersonnelRegisterMapper;
import cn.trasen.hrms.med.radiate.model.DictConstant;
import cn.trasen.hrms.med.radiate.model.RadiateDoseMonitoring;
import cn.trasen.hrms.med.radiate.model.RadiateMonitor;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;
import cn.trasen.hrms.med.radiate.service.RadiateDoseMonitoringService;
import cn.trasen.hrms.med.radiate.service.RadiateMonitorService;
import cn.trasen.hrms.med.radiate.util.MonitorCycleUtils;
import cn.trasen.hrms.med.radiate.vo.RadiateDoseReqVo;
import cn.trasen.hrms.med.radiate.vo.RadiatePersonnelReqVo;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.service.BaseDictItemService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.utils.DateUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName RadiateDoseMonitoringServiceImpl
 * @Description TODO
 * @date 2025��1��8�� ����11:15:27
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class RadiateDoseMonitoringServiceImpl implements RadiateDoseMonitoringService {

	@Autowired
	private RadiateDoseMonitoringMapper mapper;

	@Autowired
	private BaseDictItemService baseDictItemService;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;
	
	@Resource
	private HrmsEmployeeService hrmsEmployeeService;
	
	@Autowired
	private RadiatePersonnelRegisterMapper radiatePersonnelRegisterMapper;
	
	@Autowired
	private RadiateMonitorService radiateMonitorService;
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(RadiateDoseMonitoring record) {
		Assert.hasText(record.getDoseType(), "剂量计类型不能为空.");
		Assert.hasText(record.getMonitoringDate(), "监测周期时间不能为空.");
		//证件唯一性校验
		Example example = new Example(RadiateDoseMonitoring.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", record.getEmployeeId());
		criteria.andEqualTo("doseType", record.getDoseType());
		criteria.andEqualTo("monitoringDate", record.getMonitoringDate());
		List<RadiateDoseMonitoring> records = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(records)){
			throw new BusinessException(StrUtil.format("该用户监测周期时间{}已登记了！", record.getMonitoringDate()));
		}
		
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(RadiateDoseMonitoring record) {
		Assert.hasText(record.getDoseType(), "剂量计类型不能为空.");
		Assert.hasText(record.getMonitoringDate(), "监测周期时间不能为空.");
		//证件唯一性校验
		Example example = new Example(RadiateDoseMonitoring.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", record.getEmployeeId());
		criteria.andEqualTo("doseType", record.getDoseType());
		criteria.andEqualTo("monitoringDate", record.getMonitoringDate());
		criteria.andNotEqualTo("id", record.getId());
		List<RadiateDoseMonitoring> records = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(records)){
			throw new BusinessException(StrUtil.format("该用户监测周期时间{}已登记了！", record.getMonitoringDate()));
		}
		
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		RadiateDoseMonitoring record = new RadiateDoseMonitoring();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public RadiateDoseMonitoring selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<RadiateDoseMonitoring> getDataSetList(Page page, RadiateDoseMonitoring record) {
		Example example = new Example(RadiateDoseMonitoring.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<RadiateDoseMonitoring> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public DataSet<RadiateDoseMonitoring> selectRadiateDoseMonitoringPageList(Page page, RadiateDoseReqVo record) {
		if(!ObjectUtils.isEmpty(record.getExpired())){
			//设置当前时间字符串
			SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-dd");
			record.setToday(sdf.format(new Date()));
		}
		List<RadiateDoseMonitoring> records = mapper.selectRadiateDoseMonitoringPageList(page, record);
		Map<String, String> doseTypeMap  = baseDictItemService.convertDictMap(DictConstant.DOSE_TYPE);//获取剂量计类型字典Map
		Map<String, String>  employeeStatusMap  = baseDictItemService.convertDictMap(DictConstant.EMPLOYEE_STATUS);//员工状态
		Map<String, String>  monitoringPeriodMap  = baseDictItemService.convertDictMap(DictConstant.RADIATE_DOSE_MONITOR_CYCLE);//放射剂量监测周期
		for(RadiateDoseMonitoring item : records) {
			item.setEmployeeStatusText(employeeStatusMap.get(item.getEmployeeStatus()));
			item.setDoseTypeText(doseTypeMap.get(item.getDoseType()));//根据key,获取字典values
			item.setMonitoringPeriodText(monitoringPeriodMap.get(item.getMonitoringPeriod()));
			//返回监测时间带年度、季度-监测周期:1-季度,2-年度
			if(!ObjectUtils.isEmpty(item.getMonitoringPeriod())){
				if(item.getMonitoringPeriod().equals(MonitoringPeriodEmun.EVERY_YEAR.getCode())){
					item.setMonitoringDate("[年度]" + item.getMonitoringDate());
				} else if(item.getMonitoringPeriod().equals(MonitoringPeriodEmun.EVERY_QUARTER.getCode())){
					item.setMonitoringDate("[季度]" + item.getMonitoringDate());
				}
			}
		}
		
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer saveOrUpdateList(List<RadiateDoseMonitoring> records, List<RadiateMonitor> monitors, boolean isAdd, RadiatePersonnelRegister personnel) {
		if(CollUtil.isEmpty(records)){
			return 0;
		}
		Map<String, String> monitorMap = new HashMap<>();
		if(CollUtil.isNotEmpty(monitors)){
			for(RadiateMonitor m : monitors){
				if(!ObjectUtils.isEmpty(m.getRegisterType()) && !ObjectUtils.isEmpty(m.getMonitorCycle())){
					monitorMap.put(m.getRegisterType(), m.getMonitorCycle());
				}
			}
		}
		//新增
		if(isAdd){
			for (RadiateDoseMonitoring record : records){
				//计算到期时间
				String doseType = record.getDoseType();
				record.setMonitoringPeriod(monitorMap.get(doseType));
				String monitoringDate = calculateMonitoringDate(record);
				record.setMonitoringDate(monitoringDate);
				record.setExpirationDate(monitoringDate);
//				if(monitorMap.containsKey(doseType) && !ObjectUtils.isEmpty(monitoringDate)){
//					record.setExpirationDate(MonitorCycleUtils.calculateExpirationDateByPeriod(monitoringDate, monitorMap.get(doseType)));
//				}
				record.setEmployeeId(personnel.getEmployeeId());
				record.setEmployeeNo(personnel.getEmployeeNo());
				record.setEmployeeName(personnel.getEmployeeName());
				save(record);
			}
		} else {
			//先查询该用户一共有几条数据
			List<RadiateDoseMonitoring> oldRecords = selectByEmployeeId(personnel.getEmployeeId());
			//将更新的数据记录，用于后续排除删除
			Set<String> updateIds = new HashSet<>();
			//遍历新增的数据
			for (RadiateDoseMonitoring record : records){
				//判断该用户是否已经存在该数据
				boolean flag = false;
				for (RadiateDoseMonitoring oldRecord : oldRecords){
					if(record.getId().equals(oldRecord.getId())){
						flag = true;
						record.setId(oldRecord.getId());
						//只计算最新到期时间，并存入数据库-先判断到期时间是否已经超过当前，超过则按照之前的算，否则按照新的规则计算
						String doseType = record.getDoseType();
						if(ObjectUtils.isEmpty(oldRecord.getExpirationDate()) && monitorMap.containsKey(doseType)){
							record.setMonitoringPeriod(monitorMap.get(doseType));
							String monitoringDate = calculateMonitoringDate(record);
							record.setMonitoringDate(monitoringDate);
							record.setExpirationDate(monitoringDate);
//							if(!ObjectUtils.isEmpty(monitoringDate)){
//								record.setExpirationDate(MonitorCycleUtils.calculateExpirationDateByPeriod(monitoringDate, monitorMap.get(doseType)));
//							}
						}
						record.setEmployeeId(personnel.getEmployeeId());
						record.setEmployeeNo(personnel.getEmployeeNo());
						record.setEmployeeName(personnel.getEmployeeName());
						update(record);
						updateIds.add(record.getId());
					}
				}
				if(!flag){
					//计算到期时间
					String doseType = record.getDoseType();
					record.setMonitoringPeriod(monitorMap.get(doseType));
					String monitoringDate = calculateMonitoringDate(record);
					record.setMonitoringDate(monitoringDate);
					record.setExpirationDate(monitoringDate);
//					if(monitorMap.containsKey(doseType) && !ObjectUtils.isEmpty(monitoringDate)){
//						record.setExpirationDate(MonitorCycleUtils.calculateExpirationDateByPeriod(monitoringDate, monitorMap.get(doseType)));
//					}
					record.setEmployeeId(personnel.getEmployeeId());
					record.setEmployeeNo(personnel.getEmployeeNo());
					record.setEmployeeName(personnel.getEmployeeName());
					save(record);
				}
			}
			//删除多余的数据
			if(CollUtil.isNotEmpty(oldRecords) && oldRecords.size() != updateIds.size()){
				for(RadiateDoseMonitoring oldRecord : oldRecords){
					if(!updateIds.contains(oldRecord.getId())){
						deleteById(oldRecord.getId());
					}
				}
			}
		}
		return records.size();
	}
	
	/**
	 * 计算监测开始时间
	 * 
	 * @param record
	 * @return
	 */
	private String calculateMonitoringDate(RadiateDoseMonitoring record){
		//根据选择的 监测周期:1季度,2年度
		String monitoringPeriod = record.getMonitoringPeriod();
		String monitoringYear = record.getMonitoringYear();
		String monitoringQuarter = record.getMonitoringQuarter();
		if(!ObjectUtils.isEmpty(monitoringPeriod)){
			if(monitoringPeriod.equals(MonitoringPeriodEmun.EVERY_QUARTER.getCode())
					&& !ObjectUtils.isEmpty(monitoringYear) && !ObjectUtils.isEmpty(monitoringQuarter)){
				try {
					int year = Integer.valueOf(monitoringYear);
					int quarter = Integer.valueOf(monitoringQuarter);
					//当前选择季度的最后一天
					return DateUtils.getLastDayOfQuarter(year, quarter, null);
				} catch (Exception e) {
					return null;
				}
			} else if (monitoringPeriod.equals(MonitoringPeriodEmun.EVERY_YEAR.getCode())){
				return monitoringYear + "-12-31";
			}
		}
		return null;
	}

	@Override
	public List<RadiateDoseMonitoring> selectByEmployeeId(String employeeId) {
		Example example = new Example(RadiateDoseMonitoring.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return mapper.selectByExample(example);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteByEmployeeId(String employeeId) {
		Assert.hasText(employeeId, "人员ID不能为空.");
		Example example = new Example(RadiateDoseMonitoring.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		
		RadiateDoseMonitoring update = new RadiateDoseMonitoring();
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		update.setIsDeleted(Contants.IS_DELETED_TURE);
		update.setUpdateDate(new Date());
		if (user != null) {
			update.setUpdateUser(user.getUsercode());
			update.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByExampleSelective(update, example);
	}

	@Transactional(readOnly = false)
	@Override
	public PlatformResult importData(List<RadiateDoseMonitoring> list) {

		List<DictItemResp> doseTypeDic = dictItemFeignService.getDictItemByTypeCode(DictConstant.DOSE_TYPE).getObject(); //获取剂量计类型
		
		//查询所有的人
		HrmsEmployee  entity = new HrmsEmployee();
		List<HrmsEmployee> employeeList = hrmsEmployeeService.getList(entity);
		Map<String, HrmsEmployee> employeeMap = new HashMap<>();
		if(CollUtil.isNotEmpty(employeeList)){
			employeeMap = employeeList.stream().collect(Collectors.toMap(HrmsEmployee::getEmployeeNo, a -> a, (k1, k2) -> k1));
		}
		
		//查询所有的登记人员-用于去重处理
		List<RadiatePersonnelRegister> personnelList = radiatePersonnelRegisterMapper.getList(new RadiatePersonnelReqVo());
		Map<String, RadiatePersonnelRegister> personnelMap = new HashMap<>();
		if(CollUtil.isNotEmpty(personnelList)){
			personnelMap = personnelList.stream().collect(Collectors.toMap(RadiatePersonnelRegister::getEmployeeNo, a -> a, (k1, k2) -> k1));
		}
		
		//个人监控设置信息,key为工号和培训类型
		Map<String, String> personMonitorMap = new HashMap<>();
		List<RadiateMonitor> monitors = radiateMonitorService.selectAll();
		if(CollUtil.isNotEmpty(monitors)){
			for(RadiateMonitor m : monitors){
				if(m.getRegisterCategory().equals(RegisterCategoryEmun.DOSE.getCode())){
					personMonitorMap.put(m.getEmployeeId() + m.getRegisterType(), m.getMonitorCycle());
				}
			}
		}

		//查询所有人的监测，校验 工号+剂量计类型+体检日期 唯一性
		List<RadiateDoseMonitoring> doseList = getList();
		Set<String> doseSet = new HashSet<>();
		if(CollUtil.isNotEmpty(doseList)){
			for(RadiateDoseMonitoring c : doseList){
				doseSet.add(c.getEmployeeId() + c.getDoseType() + c.getMonitoringDate());
			}
		}
		
		List<String> fileIsnulls; //为空的字段
		List<String> fileDictNotFunds; //字典不能匹配字段
		
		List<ResultData> badList = new ArrayList<>();//失败信息
		
		Integer successCnt = 0;
		Integer errorCnt = 0;
		
		ResultData badData;
		DictItemResp dict;
		
		int i = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
		for (RadiateDoseMonitoring item : list) {
			
			i++;
			
			fileIsnulls = new ArrayList<String>();
			fileDictNotFunds = new ArrayList<String>();
			
			String employeeName = item.getEmployeeName();
			if(!StrUtil.isEmpty(employeeName)){
				item.setEmployeeName(employeeName.trim());
			}
			if(StrUtil.isEmpty(item.getEmployeeName())){
				fileIsnulls.add("姓名");
			}
			
			String employeeNo = item.getEmployeeNo();
			if(!StrUtil.isEmpty(employeeNo)){
				item.setEmployeeNo(employeeNo.trim());
			}
			if(StrUtil.isEmpty(item.getEmployeeNo())){
				fileIsnulls.add("工号");
			}
			
			String doseType = item.getDoseType();
			if(!StrUtil.isEmpty(doseType)){
				item.setDoseType(doseType.trim());
			}
			if(StrUtil.isEmpty(item.getDoseType())){
				fileIsnulls.add("剂量计类型");
			}
			
			String monitoringYear = item.getMonitoringYear();
			if(!StrUtil.isEmpty(monitoringYear)){
				item.setMonitoringYear(monitoringYear.trim());
			}
			if(StrUtil.isEmpty(item.getMonitoringYear())){
				fileIsnulls.add("监测年份");
			}
			
			String personalDoseEquivalentHp10 = item.getPersonalDoseEquivalentHp10();
			if(!StrUtil.isEmpty(personalDoseEquivalentHp10)){
				item.setPersonalDoseEquivalentHp10(personalDoseEquivalentHp10.trim());
			}
			if(StrUtil.isEmpty(item.getPersonalDoseEquivalentHp10())){
				fileIsnulls.add("Hp(10)");
			}
			
			String personalDoseEquivalentHp3 = item.getPersonalDoseEquivalentHp3();
			if(!StrUtil.isEmpty(personalDoseEquivalentHp3)){
				item.setPersonalDoseEquivalentHp3(personalDoseEquivalentHp3.trim());
			}
			if(StrUtil.isEmpty(item.getPersonalDoseEquivalentHp3())){
				fileIsnulls.add("Hp(3)");
			}
			
			String personalDoseEquivalentHp007 = item.getPersonalDoseEquivalentHp007();
			if(!StrUtil.isEmpty(personalDoseEquivalentHp007)){
				item.setPersonalDoseEquivalentHp007(personalDoseEquivalentHp007.trim());
			}
			if(StrUtil.isEmpty(item.getPersonalDoseEquivalentHp007())){
				fileIsnulls.add("Hp(007)");
			}
			
			if(CollUtil.isNotEmpty(fileIsnulls)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】为空!", i + 1,StrUtil.join("、", fileIsnulls)));
				badList.add(badData);
				errorCnt++;
				continue;
			}

			//判断是否已经登记
			employeeNo = item.getEmployeeNo();
			if(!personnelMap.containsKey(employeeNo)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】工号未登记!", i + 1 , employeeNo));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			//校验姓名和工号是否匹配
			if(!employeeMap.containsKey(item.getEmployeeNo())){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】工号不存在!", i + 1 , item.getEmployeeNo()));
				badList.add(badData);
				errorCnt++;
				continue;
			} else {
				HrmsEmployee emp = employeeMap.get(item.getEmployeeNo());
				if(!emp.getEmployeeName().equals(item.getEmployeeName())){
					badData = new ResultData();
					badData.setData(StrUtil.format("第【{}】行的【{}】姓名对应的工号不匹配!", i + 1 , item.getEmployeeName()));
					badList.add(badData);
					errorCnt++;
					continue;
				} else {
					item.setEmployeeId(emp.getEmployeeId());
					item.setEmployeeNo(emp.getEmployeeNo());
					item.setEmployeeName(emp.getEmployeeName());
				}
			}
			
			//数据字典
			dict = doseTypeDic.stream().filter(j -> StrUtil.equals(item.getDoseType(), j.getItemName())).findFirst().orElse(null);
			if(null == dict){
				fileDictNotFunds.add(StrUtil.format("剂量计类型->{}", item.getDoseType()));
			} else {
				item.setDoseType(dict.getItemNameValue());
			}
			
			if(CollUtil.isNotEmpty(fileDictNotFunds)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】无法匹配字典值!", i + 1,StrUtil.join("、", fileDictNotFunds)));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			//校验年份
			monitoringYear = item.getMonitoringYear();
			try {
				sdf.parse(item.getMonitoringYear());
			} catch (ParseException e) {
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的监测年份格式为YYYY，【{}】不正确!", i + 1 , item.getMonitoringYear()));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			//季度
			if(!StrUtil.isEmpty(item.getMonitoringQuarter())){
				item.setMonitoringQuarter(item.getMonitoringQuarter().trim());
			}
			String monitoringQuarter = item.getMonitoringQuarter();
			if(!ObjectUtils.isEmpty(monitoringQuarter) && 
					!"1".equals(monitoringQuarter) && !"2".equals(monitoringQuarter) && !"3".equals(monitoringQuarter) && !"4".equals(monitoringQuarter)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的监测季度格式为1,2,3,4，【{}】不正确!", i + 1 , item.getMonitoringQuarter()));
				badList.add(badData);
				errorCnt++;
				continue;
			} else if(!ObjectUtils.isEmpty(monitoringQuarter)){
				//有季度-填充监测周期时间
				item.setMonitoringPeriod(MonitoringPeriodEmun.EVERY_QUARTER.getCode()); 
				item.setMonitoringDate(
				DateUtils.getFirstDayOfQuarter(Integer.valueOf(monitoringYear), Integer.valueOf(monitoringQuarter), "yyyyMMdd")
				+ " - " + DateUtils.getLastDayOfQuarter(Integer.valueOf(monitoringYear), Integer.valueOf(monitoringQuarter), "yyyyMMdd"));
			} else {
				//没有季度-填充监测周期时间
				item.setMonitoringPeriod(MonitoringPeriodEmun.EVERY_YEAR.getCode()); 
				item.setMonitoringDate(monitoringYear + "0101 - " + monitoringYear + "1231");
			}
			
			//校验监测唯一性
			if(doseSet.contains(item.getEmployeeId() + item.getDoseType() + item.getMonitoringDate())){
				badData = new ResultData();
				if(item.getMonitoringPeriod().equals(MonitoringPeriodEmun.EVERY_YEAR.getCode())){
					badData.setData(StrUtil.format("第【{}】行的【{}】监测年份已登记!", i + 1 , monitoringYear));
				} else {
					badData.setData(StrUtil.format("第【{}】行的【{}】监测年份【{}】监测季度已登记!", i + 1 , monitoringYear, monitoringQuarter));
				}
				badList.add(badData);
				errorCnt++;
				continue;
			} else {
				doseSet.add(item.getEmployeeId() + item.getDoseType() + item.getMonitoringDate());
			}
			
			//HP的数字类型
			if(!isValidNumber(item.getPersonalDoseEquivalentHp10())){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的Ph(10)格式为数字，【{}】不正确!", i + 1 , item.getPersonalDoseEquivalentHp10()));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			if(!isValidNumber(item.getPersonalDoseEquivalentHp3())){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的Ph(3)格式为数字，【{}】不正确!", i + 1 , item.getPersonalDoseEquivalentHp3()));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			if(!isValidNumber(item.getPersonalDoseEquivalentHp007())){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的Ph(007)格式为数字，【{}】不正确!", i + 1 , item.getPersonalDoseEquivalentHp007()));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			//校验状态
			item.setMonitorStatus(item.getMonitorStatus().trim());
			if(!item.getMonitorStatus().equals("合格") && !item.getMonitorStatus().equals("不合格")){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的状态格式为 合格/不合格，【{}】不正确!", i + 1 , item.getMonitorStatus()));
				badList.add(badData);
				errorCnt++;
				continue;
			} else if(item.getMonitorStatus().equals("合格")) {
				item.setMonitorStatus("1");
			} else if(item.getMonitorStatus().equals("不合格")) {
				item.setMonitorStatus("2");
			}
			//计算到期时间
			if(personMonitorMap.containsKey(item.getEmployeeId() + item.getDoseType())) {
				item.setExpirationDate(MonitorCycleUtils.calculateExpirationDateByPeriod(item.getDoseType(), personMonitorMap.get(item.getEmployeeId() + item.getDoseType())));
			}
			
			save(item);
			successCnt++;
			
		}
		
		if(CollUtil.isNotEmpty(badList)){
			return PlatformResult.failure(StrUtil.format("信息导入 ,总条数:{}、成功:{}、失败{}", list.size(),successCnt,errorCnt),badList);
		}else{
			return PlatformResult.success(StrUtil.format("信息导入 ,总条数:{}、成功:{}、失败{}", list.size(),successCnt,errorCnt));
		}
	}
	
	public static boolean isValidNumber(String input) {
        // 匹配整数或小数（允许正负号、小数点）
        return input.matches("^[+-]?\\d+(\\.\\d+)?$");
    }

	@Override
	public List<RadiateDoseMonitoring> getList() {
		Example example = new Example(RadiateDoseMonitoring.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return mapper.selectByExample(example);
	}
}

package cn.trasen.hrms.med.schedule.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.schedule.model.MedScheduleType;
import cn.trasen.hrms.med.schedule.service.MedScheduleTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedScheduleTypeController
 * @Description TODO
 * @date 2025��3��29�� ����2:57:10
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "排班类别-新")
public class MedScheduleTypeController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedScheduleTypeController.class);

	@Autowired
	private MedScheduleTypeService medScheduleTypeService;

	/**
	 * @Title saveMedScheduleType
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��29�� ����2:57:10
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/scheduleType/save")
	public PlatformResult<String> saveMedScheduleType(@RequestBody MedScheduleType record) {
		try {
			medScheduleTypeService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedScheduleType
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��29�� ����2:57:10
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/scheduleType/update")
	public PlatformResult<String> updateMedScheduleType(@RequestBody MedScheduleType record) {
		try {
			medScheduleTypeService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedScheduleTypeById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedScheduleType>
	 * @date 2025��3��29�� ����2:57:10
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/scheduleType/{id}")
	public PlatformResult<MedScheduleType> selectMedScheduleTypeById(@PathVariable String id) {
		try {
			MedScheduleType record = medScheduleTypeService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedScheduleTypeById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��3��29�� ����2:57:10
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/scheduleType/delete/{id}")
	public PlatformResult<String> deleteMedScheduleTypeById(@PathVariable String id) {
		try {
			medScheduleTypeService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedScheduleTypeList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleType>
	 * @date 2025��3��29�� ����2:57:10
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/scheduleType/list")
	public DataSet<MedScheduleType> selectMedScheduleTypeList(Page page, MedScheduleType record) {
		return medScheduleTypeService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "获取班次类别集合", notes = "获取班次类别集合")
    @PostMapping("/api/scheduleType/getScheduleTypeList")
    public PlatformResult<List<MedScheduleType>> getScheduleTypeList() {
        try {
            return PlatformResult.success(medScheduleTypeService.getScheduleTypeList());
        } catch (Exception e) {
        	e.printStackTrace();
        	return PlatformResult.failure(e.getMessage());
        }
    }
	
	@ApiOperation(value = "排序", notes = "排序")
    @PostMapping("/api/scheduleType/updateSeq")
    public PlatformResult<String> updateSeq(@RequestBody List<MedScheduleType> records) {
        try {
        	medScheduleTypeService.updateSeq(records);
            return PlatformResult.success();
        } catch (Exception e) {
        	e.printStackTrace();
        	return PlatformResult.failure(e.getMessage());
        }
    }
	
}

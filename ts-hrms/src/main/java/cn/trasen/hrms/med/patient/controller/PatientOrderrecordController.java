package cn.trasen.hrms.med.patient.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.patient.model.PatientOrderrecord;
import cn.trasen.hrms.med.patient.service.PatientOrderrecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName PatientOrderrecordController
 * @Description TODO
 * @date 2025��4��8�� ����4:42:41
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "PatientOrderrecordController")
public class PatientOrderrecordController {

	private transient static final Logger logger = LoggerFactory.getLogger(PatientOrderrecordController.class);

	@Autowired
	private PatientOrderrecordService patientOrderrecordService;

	/**
	 * @Title savePatientOrderrecord
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:42:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/patientOrderrecord/save")
	public PlatformResult<String> savePatientOrderrecord(@RequestBody PatientOrderrecord record) {
		try {
			patientOrderrecordService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updatePatientOrderrecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:42:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/patientOrderrecord/update")
	public PlatformResult<String> updatePatientOrderrecord(@RequestBody PatientOrderrecord record) {
		try {
			patientOrderrecordService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectPatientOrderrecordById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<PatientOrderrecord>
	 * @date 2025��4��8�� ����4:42:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/patientOrderrecord/{id}")
	public PlatformResult<PatientOrderrecord> selectPatientOrderrecordById(@PathVariable String id) {
		try {
			PatientOrderrecord record = patientOrderrecordService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deletePatientOrderrecordById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:42:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/patientOrderrecord/delete/{id}")
	public PlatformResult<String> deletePatientOrderrecordById(@PathVariable String id) {
		try {
			patientOrderrecordService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectPatientOrderrecordList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<PatientOrderrecord>
	 * @date 2025��4��8�� ����4:42:41
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/patientOrderrecord/list")
	public DataSet<PatientOrderrecord> selectPatientOrderrecordList(Page page, PatientOrderrecord record) {
		return patientOrderrecordService.getDataSetList(page, record);
	}
}

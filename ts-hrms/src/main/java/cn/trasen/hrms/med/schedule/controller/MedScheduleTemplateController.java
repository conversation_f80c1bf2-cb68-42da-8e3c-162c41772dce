package cn.trasen.hrms.med.schedule.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.schedule.model.MedScheduleTemplate;
import cn.trasen.hrms.med.schedule.model.MedScheduleTemplateExtend;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;
import cn.trasen.hrms.med.schedule.service.MedScheduleAuthorityService;
import cn.trasen.hrms.med.schedule.service.MedScheduleTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * @ClassName MedScheduleTemplateController
 * @Description TODO
 * @date 2025��4��1�� ����6:48:40
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "排班模板-新")
public class MedScheduleTemplateController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedScheduleTemplateController.class);

	@Autowired
	private MedScheduleTemplateService medScheduleTemplateService;
	
	@Autowired
	private MedScheduleAuthorityService medScheduleAuthorityService;

	/**
	 * @Title saveMedScheduleTemplate
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��1�� ����6:48:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/scheduleTemplate/save")
	public PlatformResult<String> saveMedScheduleTemplate(@RequestBody MedScheduleTemplate record) {
		try {
			medScheduleTemplateService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedScheduleTemplate
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��1�� ����6:48:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/scheduleTemplate/update")
	public PlatformResult<String> updateMedScheduleTemplate(@RequestBody MedScheduleTemplate record) {
		try {
			medScheduleTemplateService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedScheduleTemplateById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedScheduleTemplate>
	 * @date 2025��4��1�� ����6:48:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/scheduleTemplate/{id}")
	public PlatformResult<MedScheduleTemplate> selectMedScheduleTemplateById(@PathVariable String id) {
		try {
			MedScheduleTemplate record = medScheduleTemplateService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title updateStatus
	 * @Description 启停模板
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025-06-27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "启停模板", notes = "启停模板")
	@GetMapping("/api/scheduleTemplate/updateStatus")
	public PlatformResult<String> updateStatus(@RequestParam("status")@ApiParam(value = "状态：0-禁用，1-启用") String status) {
		Assert.hasLength(status, "状态不能为空.");
		try {
			medScheduleTemplateService.updateStatus(status);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * 
	 * @Title deleteMedScheduleTemplateById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��4��1�� ����6:48:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/scheduleTemplate/delete/{id}")
	public PlatformResult<String> deleteMedScheduleTemplateById(@PathVariable String id) {
		try {
			medScheduleTemplateService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedScheduleTemplateList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleTemplate>
	 * @date 2025��4��1�� ����6:48:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/scheduleTemplate/list")
	public DataSet<MedScheduleTemplate> selectMedScheduleTemplateList(Page page, MedScheduleTemplate record) {
		return medScheduleTemplateService.getDataSetList(page, record);
	}

	/**
	 * @Title saveMedScheduleTemplate
	 * @Description 保存排班模板信息
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleTemplate>
	 * @date 2025-06-27 10:33:33
	 * <AUTHOR>
	 */
	@ApiOperation(value = "保存排班模板信息", notes = "保存排班模板信息")
	@PostMapping(value = "/api/scheduleTemplate/saveMedScheduleTemplate")
	public PlatformResult<String> saveMedScheduleTemplate(@RequestBody ScheduleEmployee record) {
		try {
			medScheduleTemplateService.saveMedScheduleTemplate(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
//	@ApiOperation(value = "保存排班模板信息", notes = "保存排班模板信息")
//	@PostMapping(value = "/api/scheduleTemplate/saveScheduleTemplate")
//	public PlatformResult<String> saveScheduleTemplate(@RequestBody List<MedScheduleTemplate> list) {
//		try {
//			if(CollectionUtils.isNotEmpty(list)) {
//				medScheduleTemplateService.batchInsert(list);
//			}else {
//				return PlatformResult.failure("请设置排班数据");
//			}
//			return PlatformResult.success();
//		} catch (Exception e) {
//			logger.error(e.getMessage(), e);
//			return PlatformResult.failure(e.getMessage());
//		}
//	}
	
//	@ApiOperation(value = "查询模板数据", notes = "查询模板数据")
//    @GetMapping("/api/scheduleTemplate/getScheduleTemplateList")
//    public PlatformResult<List<MedScheduleTemplate>> getScheduleTemplateList() {
//        try {
//            return PlatformResult.success(medScheduleTemplateService.getScheduleTemplateList());
//        } catch (Exception e) {
//        	e.printStackTrace();
//        	return PlatformResult.failure(e.getMessage());
//        }
//    }
	
	@ApiOperation(value = "查询模板数据", notes = "查询模板数据")
    @GetMapping("/api/scheduleTemplate/getScheduleTemplateList")
    public DataSet<ScheduleEmployee> getScheduleTemplateList(Page page,ScheduleEmployee record) {
		record.setTemplate(true);
		return medScheduleAuthorityService.getScheduleRecordList(page, record, null);
    }
	
	/**
	 * @Title getScheduleTemplateExtend
	 * @Description 查询当前登录人的模板信息
	 * @return PlatformResult<MedScheduleTemplateExtend>
	 * @date 2025-07-03 10:33:33
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询当前登录人的模板信息", notes = "查询当前登录人的模板信息")
    @GetMapping("/api/scheduleTemplate/getScheduleTemplateExtend")
    public PlatformResult<MedScheduleTemplateExtend> getScheduleTemplateExtend() {
		try {
			return PlatformResult.success(medScheduleTemplateService.getScheduleTemplateExtend());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
    }
}

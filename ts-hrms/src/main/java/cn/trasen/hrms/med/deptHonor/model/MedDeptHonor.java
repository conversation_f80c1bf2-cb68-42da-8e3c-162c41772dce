package cn.trasen.hrms.med.deptHonor.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

@Table(name = "med_dept_honor")
@Setter
@Getter
public class MedDeptHonor {
	
    @Id
    private String id;

    /**
     * 所属科室id
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "所属科室id")
    private String orgId;

    /**
     * 荣誉名称
     */
    @Column(name = "honor_name")
    @ApiModelProperty(value = "荣誉名称")
    private String honorName;

    /**
     * 颁发机构
     */
    @Column(name = "issue_org")
    @ApiModelProperty(value = "颁发机构")
    private String issueOrg;

    /**
     * 颁发日期
     */
    @Column(name = "issue_date")
    @ApiModelProperty(value = "颁发日期")
    private String issueDate;

    /**
     * 证书编号
     */
    @Column(name = "certificate_no")
    @ApiModelProperty(value = "证书编号")
    private String certificateNo;

    /**
     * 是否公开  0否  1是
     */
    @Column(name = "is_pub")
    @ApiModelProperty(value = "是否公开  0否  1是")
    private String isPub;

    /**
     * 荣誉描述
     */
    @Column(name = "honor_remark")
    @ApiModelProperty(value = "荣誉描述")
    private String honorRemark;

    /**
     * 获奖原因
     */
    @Column(name = "award_reason")
    @ApiModelProperty(value = "获奖原因")
    private String awardReason;

    /**
     * 相关项目
     */
    @Column(name = "related_item")
    @ApiModelProperty(value = "相关项目")
    private String relatedItem;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String files;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    @Transient
    private List<MedDeptHonorWinner> winnerList;  //获奖名单
    
    @Transient
    private List<String> childsList;
    
    @Transient
    private String issueDateStart;
    
    @Transient
    private String issueDateEnd;
    
    @Transient
    private String orgName;
    
}
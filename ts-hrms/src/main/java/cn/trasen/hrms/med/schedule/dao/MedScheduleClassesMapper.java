package cn.trasen.hrms.med.schedule.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.med.schedule.model.MedScheduleClasses;
import tk.mybatis.mapper.common.Mapper;

public interface MedScheduleClassesMapper extends Mapper<MedScheduleClasses> {

	List<MedScheduleClasses> getScheduleClassesList(MedScheduleClasses record);

	List<MedScheduleClasses> getDataSetList(MedScheduleClasses record, Page page);

	/**
	 * 根据emoloyeeNo查询权限班次ID
	 * @param record
	 * @return
	 * @update 2025-07-11 12:12:00
	 * <AUTHOR>
	 */
	List<MedScheduleClasses> getScheduleClassesByEmp(MedScheduleClasses record);

	/**
	 * 根据科室编码查询员工列表
	 * @param scheduleOrgList
	 * @param ssoOrgCode
	 * @return
	 * @update 2025-07-11 12:12:00
	 * <AUTHOR>
	 */
	List<EmployeeResp> getEmpListByDeptCodes(@Param("scheduleOrgList") List<String> scheduleOrgList, @Param("ssoOrgCode") String ssoOrgCode);
}
package cn.trasen.hrms.med.patient.model;


import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class PatientInfoInParameter {

	
	private String patnId;//患者id 多个用,隔开
	private String deptIds;//当前科室id集合 :多个用,隔开
	private String type;//类型:0新入,1病危、2病重、3死亡，4分娩
	private String statuss;//1-待入科室 3-在科室 4-出院医嘱 5-出区 6-结算 7-欠费结算 10-注销(入院)在院病人=住院状态in (3-在科室 4-出院医嘱)出院病人=住院状态in(5-出区 6-结算 7-欠费结算) :多个用,隔开
	private String name;//姓名
	private String patnNo;//住院号
	private String startInDate;//入院开始日期
	private String endInDate;//入院结束日期
	private String startOutDate;//入院开始日期
	private String endOutDate;//入院结束日期
	private String hospCode;//院区code
	private String orderTendLevel;//护理级别 1,2,3,4特级
	private String wzStatus;//危重状态,1病危、2病重
	private String bedDocId;//管床医生
	
	private String startOrderBDate;// 开始开嘱日期
	private String endOrderBDate;// 结束开嘱日期
	private String mngTypes;//0-长期医嘱 1-临时医嘱 2-长期账单 3-临时账单		:多个用,隔开
    private String statusFlags;//0-保存1-发送2-转抄3-停医嘱4-停医嘱转抄5-完成   :多个用,隔开
    private String sortOrder;//0-按创建时间 1-按更新时间 2-按开嘱日期 3-按停嘱日期
    private String cancelFlags;//0-正常1-作废 2-未执行 作废跟未执行的差别在于作废是医生操作的,未执行是护士操作的
  
    private String emrId;//病历文件ID
    private String templateFiletype;//模板病历文件类型:(0:普通病历 1：一般护理记录单 2.血糖 3.文书 4.评估单(竖版) 5.评估单(横版))
    private String seeType;//返回文件流类型（0:pdf 1:html）
    
    private String repNo;//检验单号
}

package cn.trasen.hrms.med.qualityApply.dao;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.med.qualityApply.model.MedQualityApply;
import tk.mybatis.mapper.common.Mapper;

public interface MedQualityApplyMapper extends Mapper<MedQualityApply> {

	List<MedQualityApply> getDataSetList(MedQualityApply record, Page page);

	String selectOrgName(String applyOrgId);

	List<Map<String, Object>> getNoDataApplyDept();
}
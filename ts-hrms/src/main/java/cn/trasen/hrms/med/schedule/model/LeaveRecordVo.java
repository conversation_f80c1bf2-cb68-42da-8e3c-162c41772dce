package cn.trasen.hrms.med.schedule.model;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 请假销假数据
 * <AUTHOR>
 *
 */
@Setter
@Getter
public class LeaveRecordVo {

    @ApiModelProperty(value = "ID")
	private String id;

    @ApiModelProperty(value = "员工ID")
	private String employeeId;

    @ApiModelProperty(value = "员工工号")
	private String employeeNo;

    @ApiModelProperty(value = "员工姓名")
	private String employeeName;

    @ApiModelProperty(value = "请假/销假类型")
	private String leaveType;

    @ApiModelProperty(value = "开始时间")
	private String startDate;

    @ApiModelProperty(value = "结束时间")
	private String endDate;
    
    @ApiModelProperty(value = "具体开始时间:1-上午开始，2-下午开始")
    private String startDateValue;

    @ApiModelProperty(value = "具体结束时间:1-上午结束，2-下午结束")
    private String endDateValue;

    @ApiModelProperty(value = "类型：2-请假数据，3-进修/规培/学习/下乡等，4-销假")
    private String type;
    
    @ApiModelProperty(value = "请假，进修/规培/学习/下乡，销假的天数")
    private BigDecimal days;

    @ApiModelProperty(value = "流程业务ID")
    private String workflowId;

    @ApiModelProperty(value = "序号")
    private String index;
    
    @ApiModelProperty(value = "天数-用于导出")
    private String daysText;

}

package cn.trasen.hrms.med.schedule.model;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ScheduleEmployee {
	
	@ApiModelProperty(value = "员工ID")
	private String employeeId;
	
	@ApiModelProperty(value = "员工工号")
	private String employeeNo;
	
	@ApiModelProperty(value = "员工姓名")
	private String employeeName;
	
	@ApiModelProperty(value = "员工组织机构编码")
	private String orgId;
	
	@ApiModelProperty(value = "员工组织机构名称")
	private String orgName;
	
	@ApiModelProperty(value = "员工所属院区编码")
	private String hospCode;
	
	@ApiModelProperty(value = "技术职称")
	private String technical;
	
	@ApiModelProperty(value = "编制类型")
	private String establishmentType;
	
	@ApiModelProperty(value = "职务名称")
	private String positionName;
	
	@ApiModelProperty(value = "手机号")
	private String phoneNumber;
	
	@ApiModelProperty(value = "分组ID")
	private String groupId;
	
	@ApiModelProperty(value = "分组名称")
	private String groupName;
	
	@ApiModelProperty(value = "在职状态")
	private String employeeStatus;
	
	private String classesIds;
	
	private List<String> scheduleOrgs;
	    
	private List<String> scheduleUsers;
	
	@ApiModelProperty(value = "排班集合")
	private List<MedScheduleRecord> scheduleRecords;
	
	@ApiModelProperty(value = "排班数量")
	private Integer recordCount;
	
	private String startDate;
	
	private String endDate;
	
	@ApiModelProperty(value = "院区编码")
	private String hospName;
	
	@ApiModelProperty(value = "Y-查看全部，否则不是")
	private String isAll;
	
	private String orgIds;
	
	private List<String> orgIdList;
	
	private List<String> classesIdList;

	private List<String> dateList;
	
	private List<String> existEmpIds;
	
	private List<String> employeeStatusList;
	
	private List<String> employeeIds;
	
	private String currentUserCode;
	
	private String copyType;
	
	private String archivesType;
	
	private String searchKey;
	
	@ApiModelProperty(value = "人员类型-数据字典ORG_ATTRIBUTES")
	private String orgAttributes;
	
	@ApiModelProperty(value = "排班结果：0-未排班，1-已排班")
	private String scheduleStatus;
	
	@ApiModelProperty(value = "模板状态：0-禁用，1-启用-用于模板保存时")
	private String status;
	
    @ApiModelProperty(value = "模板排班周次：1-星期一，2-星期二，3-星期三，4-星期四，5-星期五，6-星期六，7-星期日")
    private String scheduleWeek;
	
    @ApiModelProperty(value = "主键集合")
    private List<String> ids;
    
    @ApiModelProperty(value = "是否模板，默认false")
    private boolean template;

    @ApiModelProperty(value = "开始排班标识，默认false，用于回显模板排班数据")
    private boolean startSchedule;

    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    @ApiModelProperty(value = "查询关键字（姓名/联系方式）")
	private String keywords;
    
    @ApiModelProperty(value = "排班统计：0-禁用，1-开启，默认禁用")
    private String scheduleStatistics;
    
    @ApiModelProperty(value = "是否休息班次  0否 1是")
    private String holidayClasses;
    
    @ApiModelProperty(value = "实际出勤天数")
    private BigDecimal actualAttendance;
    
    @ApiModelProperty(value = "获取实际出勤天数，默认不返回")
    private boolean showActualAttendance;
}

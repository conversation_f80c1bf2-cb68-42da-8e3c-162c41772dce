package cn.trasen.hrms.med.radiate.emun;

/**
 * 剂量计类型
 * <AUTHOR>
 *
 */
public enum DoseTypeEmun {
	
	WRIST_TYPE("1", "腕式/铅衣(外)"),  
	BADGE_TYPE("2", "胸章/铅衣(内)");
	
	private String name;

	private String code;

	DoseTypeEmun(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	

	/**
	 * 根据编码获取name
	 * 
	 * @param code
	 * @return
	 */
	public static String convert(String code) {
		for (DoseTypeEmun emun : values()) {
			if (emun.getCode().equals(code))
				return emun.name;
		}
		return null;
	}

}

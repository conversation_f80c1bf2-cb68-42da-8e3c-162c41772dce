package cn.trasen.hrms.med.crisisValue.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.message.NoticeService;
import cn.trasen.hrms.interfaceRegister.service.CommInterfaceRegisterService;
import cn.trasen.hrms.med.crisisValue.dao.MedCrisisValueMapper;
import cn.trasen.hrms.med.crisisValue.dao.MedHisEmployeeMapper;
import cn.trasen.hrms.med.crisisValue.model.MedCrisisValue;
import cn.trasen.hrms.med.crisisValue.model.MedHisEmployee;
import cn.trasen.hrms.med.crisisValue.service.MedCrisisValueService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedCrisisValueServiceImpl
 * @Description TODO
 * @date 2025��5��24�� ����4:18:04
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedCrisisValueServiceImpl implements MedCrisisValueService {

	@Autowired
	private MedCrisisValueMapper mapper;
	
	@Autowired
	private MedHisEmployeeMapper medHisEmployeeMapper;
	
	@Autowired
	private CommInterfaceRegisterService commInterfaceRegisterService;
	
	@Value("${appconfig.login}")
    private String appUrl;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedCrisisValue record) {
		if(StringUtils.isBlank(record.getId())) {
			record.setId(IdGeneraterUtils.nextId());
		}
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedCrisisValue record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedCrisisValue record = new MedCrisisValue();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedCrisisValue selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedCrisisValue> getDataSetList(Page page, MedCrisisValue record) {
		
		//record.setCurrentUserCode(UserInfoHolder.getCurrentUserCode());
		
		record.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptId());
		
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		
		List<MedCrisisValue> records = mapper.getDataSetList(record, page);
		
		for (MedCrisisValue medCrisisValue : records) {
			medCrisisValue.setIndex(record.getIndex());
		}
		
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	@Override
	public MedCrisisValue selectByRepNo(String repNo) {
		Example example = new Example(MedCrisisValue.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("reportNo", repNo);
		List<MedCrisisValue> list = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(list)) {
			return list.get(0);
		}else {
			return null;
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void syncMedCrisisValue() {
		
		Map<String,Object> params = new HashMap<>();
		
		// 获取当前日期
        LocalDate today = LocalDate.now();
        // 减去7天
        LocalDate sevenDaysAgo = today.minusDays(7);
        
        // 格式化输出
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedSevenDaysAgo = sevenDaysAgo.format(formatter);
		
		params.put("beginReportDate", formattedSevenDaysAgo + " 00:00:00");
		params.put("endReportDate", DateUtil.today() + " 23:59:59");
		
		List<MedCrisisValue> crisisValueList = commInterfaceRegisterService.queryCrisisValue(params);
		
		if(CollUtil.isNotEmpty(crisisValueList)) {
			
			for (MedCrisisValue medCrisisValue : crisisValueList) {
				
				MedCrisisValue value = selectById(medCrisisValue.getId());
				
				if(null == value) {
					
					save(medCrisisValue);
					
					if("0".equals(medCrisisValue.getStatus()) ) {
						//危急值提醒
						if(!appUrl.endsWith("/")){
							appUrl += "/";
						}
						
						if(StrUtil.isNotBlank(medCrisisValue.getBedDoctorId())) {
							MedHisEmployee hisEmployee = medHisEmployeeMapper.selectHisEmployee(null,medCrisisValue.getBedDoctorId());
							
							StringBuffer contentSb = new StringBuffer();
							contentSb.append("<div class=\"normal\">病人姓名：").append(medCrisisValue.getName()).append("</div><br>");
							contentSb.append("<div class=\"highlight\">危急值项目：").append(medCrisisValue.getOrderItemName()).append("</div>");
							NoticeReq notice = NoticeReq.builder()
										.content(contentSb.toString())
										.noticeType("3")
										.receiver(hisEmployee.getOaEmployeeNo())
										.sender("admin")
										.senderName("admin")
										.subject("危急值提醒")
										.wxSendType("1")
										.url(appUrl + "mobile-container/ts-mobile-hrms/pages/medical-critical-value/index?fromPage=workBench&index=0")
										.build();
							
							NoticeService informationFeignClient = new NoticeService();
							
							informationFeignClient.sendAsynNotice(notice);
						}
					}
				}else {
					update(medCrisisValue);
				}
			}
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void handleMedCrisisValue(MedCrisisValue record) {
		
		commInterfaceRegisterService.updateCriticalValue(record);
		
		syncMedCrisisValue();
		
		//record.setStatus("1");
		//record.setProcessDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
		
		//mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public Map<String, Object> indexStatistics(MedCrisisValue record) {
		
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		
		Map<String, Object> indexStatistics = mapper.indexStatistics(record);
		
		if(null != indexStatistics && null != indexStatistics.get("handleNumbers") && null != indexStatistics.get("reportNumbers")) {
			
			long handleNumbers = (long)indexStatistics.get("handleNumbers");
			long reportNumbers = (long)indexStatistics.get("reportNumbers");
			double handleRate = handleNumbers/(double)reportNumbers * 100;
			if (!Double.isNaN(handleRate)) {
				indexStatistics.put("handleRate", String.format("%.2f", handleRate) + "%");
			}else {
				indexStatistics.put("handleRate", "0%");
			}
			
		}
		
		return indexStatistics;
	}

	@Override
	public Map<String, Object> tableStatistics(MedCrisisValue record) {
		//record.setCurrentUserCode(UserInfoHolder.getCurrentUserCode());
		record.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptId());
		return mapper.tableStatistics(record);
	}
	
}

package cn.trasen.hrms.med.patient.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.patient.model.PatientBirth;
import cn.trasen.hrms.med.patient.model.PatientTransferDept;
import cn.trasen.hrms.med.patient.service.PatientTransferDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName PatientTransferDeptController
 * @Description TODO
 * @date 2025��4��8�� ����4:48:25
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "PatientTransferDeptController")
public class PatientTransferDeptController {

	private transient static final Logger logger = LoggerFactory.getLogger(PatientTransferDeptController.class);

	@Autowired
	private PatientTransferDeptService patientTransferDeptService;

	/**
	 * @Title savePatientTransferDept
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/patientTransferDept/save")
	public PlatformResult<String> savePatientBirth(@RequestBody PatientTransferDept record) {
		try {
			patientTransferDeptService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updatePatientTransferDept
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/patientTransferDept/update")
	public PlatformResult<String> updatePatientBirth(@RequestBody PatientTransferDept record) {
		try {
			patientTransferDeptService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectPatientTransferDeptById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<PatientTransferDept>
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/patientTransferDept/{id}")
	public PlatformResult<PatientTransferDept> selectPatientBirthById(@PathVariable String id) {
		try {
			PatientTransferDept record = patientTransferDeptService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deletePatientTransferDeptById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/patientTransferDept/delete/{id}")
	public PlatformResult<String> deletePatientBirthById(@PathVariable String id) {
		try {
			patientTransferDeptService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectPatientTransferDeptList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<PatientTransferDept>
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/patientTransferDept/list")
	public DataSet<PatientTransferDept> selectPatientBirthList(Page page, PatientTransferDept record) {
		return patientTransferDeptService.getDataSetList(page, record);
	}
}

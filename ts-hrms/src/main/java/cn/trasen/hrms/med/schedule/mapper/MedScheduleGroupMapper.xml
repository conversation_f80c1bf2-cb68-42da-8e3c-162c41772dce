<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.schedule.dao.MedScheduleGroupMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.schedule.model.MedScheduleGroup">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="group_emp_id" jdbcType="VARCHAR" property="groupEmpId" />
    <result column="group_emp_name" jdbcType="VARCHAR" property="groupEmpName" />
    <result column="create_date" jdbcType="VARCHAR" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
  </resultMap>
</mapper>
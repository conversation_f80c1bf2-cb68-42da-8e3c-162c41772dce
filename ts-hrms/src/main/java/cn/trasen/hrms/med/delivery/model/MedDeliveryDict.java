package cn.trasen.hrms.med.delivery.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import org.jeecgframework.poi.excel.annotation.Excel;

import lombok.*;

@Table(name = "med_delivery_dict")
@Setter
@Getter
public class MedDeliveryDict {
	
    @Id
    private String id;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称（必须）")
    @Column(name = "project_name")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目内涵
     */
    @Excel(name = "项目内涵（必须）")
    @ApiModelProperty(value = "项目内涵")
    private String connotation;

    /**
     * 检验目的
     */
    @Excel(name = "检验目的")
    @ApiModelProperty(value = "检验目的")
    private String purpose;

    /**
     * 样本类型
     */
    @Excel(name = "样本类型（必须）")
    @Column(name = "sample_type")
    @ApiModelProperty(value = "样本类型")
    private String sampleType;

    /**
     * 样本量
     */
    @Excel(name = "样本量（必须）")
    @Column(name = "sample_dose")
    @ApiModelProperty(value = "样本量")
    private String sampleDose;

    /**
     * 外送检验机构
     */
    @Excel(name = "外送检验机构名称（必须）")
    @Column(name = "test_org")
    @ApiModelProperty(value = "外送检验机构")
    private String testOrg;

    /**
     * 检测方式
     */
    @Excel(name = "检测方式")
    @Column(name = "test_mean")
    @ApiModelProperty(value = "检测方式")
    private String testMean;

    /**
     * 费用
     */
    @Excel(name = "费用（必须）")
    @ApiModelProperty(value = "费用")
    private String cost;

    /**
     * 物价收费条目
     */
    @Column(name = "fee_item")
    @ApiModelProperty(value = "物价收费条目")
    private String feeItem;

    /**
     * 收费编码
     */
    @Column(name = "fee_code")
    @ApiModelProperty(value = "收费编码")
    private String feeCode;

    /**
     * 收费详情
     */
    @Column(name = "fee_details")
    @ApiModelProperty(value = "收费详情")
    private String feeDetails;

    /**
     * 收费价格
     */
    @Column(name = "fee_price")
    @ApiModelProperty(value = "收费价格")
    private String feePrice;

    /**
     * 物价判断
     */
    @Column(name = "price_judge")
    @ApiModelProperty(value = "物价判断")
    private String priceJudge;

    /**
     * 执行科室（天、岳）
     */
    @Column(name = "exc_dept")
    @ApiModelProperty(value = "执行科室（天、岳）")
    private String excDept;

    /**
     * 执行科室（马）
     */
    @Column(name = "exc_dept_m")
    @ApiModelProperty(value = "执行科室（马）")
    private String excDeptM;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 字典类型  1、无物价  2、有物价
     */
    @Column(name = "dict_type")
    @ApiModelProperty(value = "字典类型  1、无物价  2、有物价")
    private String dictType;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    @Transient
    private int orderNumber;
    
    @Transient
    private String importData;
}
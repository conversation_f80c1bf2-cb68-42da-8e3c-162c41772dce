package cn.trasen.hrms.med.schedule.model;

import io.swagger.annotations.*;
import javax.persistence.*;
import lombok.*;

@Table(name = "med_schedule_authority_sort")
@Setter
@Getter
public class MedScheduleAuthoritySort {
    @Id
    private String id;

    @Column(name = "authority_id")
    private String authorityId;

    @Column(name = "employee_id")
    private String employeeId;

    private Integer sort;
}
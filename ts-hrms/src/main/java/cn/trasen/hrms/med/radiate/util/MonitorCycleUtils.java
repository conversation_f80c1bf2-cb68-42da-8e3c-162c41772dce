package cn.trasen.hrms.med.radiate.util;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang3.time.DateUtils;
import org.springframework.util.ObjectUtils;

import cn.trasen.hrms.med.radiate.emun.MonitoringPeriodEmun;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MonitorCycleUtils {

	/**
	 * 根据开始日期和周期计算到期日期
	 * @return
	 */
	public static String calculateExpirationDate (String startDateStr, String cycleStr){
		if(ObjectUtils.isEmpty(startDateStr) || ObjectUtils.isEmpty(cycleStr)){
			return null;
		}
		//周期转int型
		int cycle = 0;
		try {
			cycle = Integer.parseInt(cycleStr);
		} catch (Exception e) {
			log.error("年度周期转换报错");
			return null;
		}
		Date startDate = null;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		try {
			startDate = sdf.parse(startDateStr);
		} catch (Exception e) {
			log.error("日期转换报错");
			return null;
		}
		Date endDate = DateUtils.addYears(startDate, cycle);
		return sdf.format(endDate);
	}
	

	public static String calculateExpirationDateByPeriod (String startDateStr, String cycle){
		if(ObjectUtils.isEmpty(startDateStr) || ObjectUtils.isEmpty(cycle)){
			return null;
		}
		Date startDate = null;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		try {
			startDate = sdf.parse(startDateStr);
		} catch (Exception e) {
			log.error("日期转换报错");
			return null;
		}
		Date endDate = null;
		if(cycle.equals(MonitoringPeriodEmun.EVERY_YEAR.getCode())){
			//计算一年后的到期日期
			endDate = DateUtils.addYears(startDate, 1);
		}else if(cycle.equals(MonitoringPeriodEmun.EVERY_QUARTER.getCode())){
			//计算一个季度后的到期日期
			endDate = DateUtils.addMonths(startDate, 3);
		}

		if(endDate != null){
			return sdf.format(endDate);
		}
		return null;
	}
}

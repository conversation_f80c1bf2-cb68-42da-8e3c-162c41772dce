<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.schedule.dao.MedScheduleAuthorityMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.schedule.model.MedScheduleAuthority">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="manage_name" jdbcType="VARCHAR" property="manageName" />
    <result column="manage_user_code" jdbcType="VARCHAR" property="manageUserCode" />
    <result column="schedule_name" jdbcType="VARCHAR" property="scheduleName" />
    <result column="schedule_org" jdbcType="VARCHAR" property="scheduleOrg" />
    <result column="schedule_user" jdbcType="VARCHAR" property="scheduleUser" />
    <result column="authority_remark" jdbcType="VARCHAR" property="authorityRemark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>
  
  <select id="selectEmployeeByOrgId" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee" resultType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee">
  			select 
	  			t1.employee_id as employeeId,
	  			t1.employee_no as employeeNo,
	  			t1.employee_name as employeeName,
	  			t1.org_id as orgId,
	  			t3.name as orgName
	  		from cust_emp_base t1
  			LEFT JOIN cust_emp_info t2 on t1.employee_id = t2.info_id
  			LEFT JOIN comm_organization t3 on t3.organization_id = t1.org_id
  			where t1.is_deleted = 'N'
  			and (
		  			select count(1) from med_schedule_record tt 
		  			where tt.is_deleted = 'N' and tt.employee_id = t1.employee_id
	  				and tt.schedule_date between #{startDate} and #{endDate}
	  			) > 0
  			<if test="archivesType != null and archivesType != ''">
	  			and t1.archives_type = #{archivesType}
  			</if>
  			
  			<if test="keywords != null and keywords != ''">
	  			and t1.employee_name like concat('%',#{keywords},'%') 
  			</if>
  			
  			<if test="employeeId != null and employeeId != ''">
	  			and t1.employee_id = #{employeeId}
  			</if>
  			
  			<if test="orgIdList != null and orgIdList.size() > 0">
	        	 and t1.org_id in
		        <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
        	</if>
        
			<if test="ssoOrgCode != null and ssoOrgCode != ''">
	  			and t1.sso_org_code=#{ssoOrgCode}
			</if>
  </select>
  
  <select id="selectEmployeeByAuthorit" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee" resultType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee">
  			select 
	  			t1.employee_id as employeeId,
	  			t1.employee_no as employeeNo,
	  			t1.employee_name as employeeName,
	  			t1.org_id as orgId,
	  			t3.name as orgName,
	  			t1.hosp_code as hospCode,
	  			t2.technical,
	  			t1.phone_number as phoneNumber,
	  			t5.group_name AS groupName,
	  			t1.org_attributes,
    			t2.establishment_type,
    			t6.position_name
  			
			<if test="scheduleStatus != null and scheduleStatus != '' and startDate != null and startDate != '' and endDate != null and endDate != ''">
	  			,COALESCE(
        		(   SELECT SUM(record_count) 
				FROM (
		  			select count(t11.id)  AS record_count, employee_id
			  		 from med_schedule_record t11
					left join med_schedule_classes t22 on t11.classes_id = t22.id
			  		where t11.is_deleted = 'N'
			  			and t11.schedule_date between #{startDate} and #{endDate}
						group by employee_id
			  		UNION ALL
			  		select count(id)  AS record_count, e.employee_id
			  		from hrms_leave_report r
			  		left join cust_emp_base e on e.employee_no=r.employee_code
					where r.is_deleted = 'N' and e.is_deleted = 'N'
						and r.start_date &lt;= #{endDate} and r.end_date >= #{startDate}
						group by e.employee_id
			  		UNION ALL
					select count(id)  AS record_count, e1.employee_id
		  			from hrms_cancel_leave_report r1
			  		left join cust_emp_base e1 on e1.employee_no=r1.employee_code
					where r1.is_deleted = 'N' and e1.is_deleted = 'N'
						and r1.start_date &lt;= #{endDate} and r1.end_date >= #{startDate}
						group by e1.employee_id
			  		UNION ALL
			  		select count(employee_id)  AS record_count, employee_id  
			  		from hrms_out_record
					where is_deleted = 'N' and out_type is not null and start_time is not null and end_time is not null 
						and start_time &lt;= #{endDate} and end_time >= #{startDate}
						group by employee_id
					UNION ALL
					select count(employee_id)  AS record_count, employee_id 
					from hrms_out_record_gp
					where is_deleted = 'N' and out_type is not null and start_time is not null and end_time is not null 
						and start_time &lt;= #{endDate} and end_time >= #{startDate}
						group by employee_id
					UNION ALL
					select count(employee_id)  AS record_count, employee_id  
					from hrms_out_record_hy
					where is_deleted = 'N' and out_type is not null and start_time is not null and end_time is not null
						and start_time &lt;= #{endDate} and end_time >= #{startDate}
						group by employee_id
					UNION ALL
					select count(employee_id)  AS record_count, employee_id  
					from hrms_out_record_xx
					where is_deleted = 'N' and out_type is not null and start_time is not null and end_time is not null
						and start_time &lt;= #{endDate} and end_time >= #{startDate}
						group by employee_id
				) AS ss  where ss.employee_id=t1.employee_id
				), 
		        0 
		    ) AS record_count 
  			</if>
  			from cust_emp_base t1
  			LEFT JOIN cust_emp_info t2 on t1.employee_id = t2.info_id
  			LEFT JOIN comm_organization t3 on t3.organization_id = t1.org_id
  			LEFT JOIN med_schedule_group_user t4 on t4.employee_id = t1.employee_id AND t4.create_user = #{currentUserCode}
  			LEFT JOIN med_schedule_group t5 on t5.id = t4.group_id AND t5.create_user = #{currentUserCode}
  			left join comm_position t6 on t6.position_id=t1.position_id and t6.is_deleted = 'N'
			where t1.is_deleted = 'N'
			
			<if test="archivesType != null and archivesType != ''">
	  			and t1.archives_type = #{archivesType}
  			</if>
  			
  			<if test="employeeId != null and employeeId != ''">
	  			and t1.employee_id = #{employeeId}
  			</if>
			
			<if test="orgAttributes != null and orgAttributes != ''">
	  			and t1.org_attributes = #{orgAttributes}
  			</if>
  			
  			<if test="searchKey != null and searchKey != ''">
	  			and (
					t1.employee_name like concat('%',#{searchKey},'%')
					or t1.employee_no like concat('%',#{searchKey},'%')
				) 
  			</if>
  			
  			<if test="keywords != null and keywords != ''">
	  			and (
					t1.employee_name like concat('%',#{keywords},'%')
					or t1.phone_number like concat('%',#{keywords},'%')
				) 
  			</if>
			
			<if test="employeeName != null and employeeName != ''">
	  			and (
					t1.employee_name like concat('%',#{employeeName},'%')
					or t1.phone_number like concat('%',#{employeeName},'%')
					or t1.employee_no like concat('%',#{employeeName},'%')
				) 
  			</if>
  			
  			<if test="groupName != null and groupName != ''">
	  			and t5.group_name like concat('%',#{groupName},'%')
  			</if>
  			
  			<if test="hospCode != null and hospCode != ''">
	  			and t1.hosp_code = #{hospCode}
  			</if>
  			
  			<if test="technical != null and technical != ''">
	  			and t2.technical like concat('%',#{technical},'%')
  			</if>
  			<if test="classesIdList != null and classesIdList.size() > 0 and template == false">
	  			and (
		  			select count(1) from med_schedule_record tt 
		  			where tt.is_deleted = 'N' and tt.employee_id = t1.employee_id
		  			<if test="classesIdList != null and classesIdList.size() > 0">
			        	 and tt.classes_id in
				        <foreach collection="classesIdList" index="index" item="item" open="(" separator="," close=")">
				            #{item}
				        </foreach>
		        	</if>
	  				and tt.schedule_date between #{startDate} and #{endDate}
	  			) > 0
  			</if>
  			
  			<if test="classesIdList != null and classesIdList.size() > 0 and template == true">
	  			and (
		  			select count(1) from med_schedule_template tt 
		  			where tt.is_deleted = 'N' and tt.employee_id = t1.employee_id
		  			<if test="classesIdList != null and classesIdList.size() > 0">
			        	 and tt.classes_id in
				        <foreach collection="classesIdList" index="index" item="item" open="(" separator="," close=")">
				            #{item}
				        </foreach>
		        	</if>
	  			) > 0
  			</if>
			
			<if test="scheduleOrgs != null and scheduleOrgs.size() > 0 and scheduleUsers != null and scheduleUsers.size() > 0">
	        	 and (t1.org_id in
		        <foreach collection="scheduleOrgs" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
		        or t1.employee_no in
		        <foreach collection="scheduleUsers" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
		        )
	        </if>
			
			<if test="scheduleOrgs != null and scheduleUsers == null">
	        	 and t1.org_id in
		        <foreach collection="scheduleOrgs" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
	        </if>
	      
	        <if test="scheduleUsers != null and scheduleOrgs == null">
	        	and t1.employee_no in
		        <foreach collection="scheduleUsers" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
	        </if>
	        
	        <if test="orgIdList != null and orgIdList.size() > 0 and (searchKey == null or searchKey == '')">
	        	 and t1.org_id in
		        <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
        	</if>
        	
        	<if test="employeeStatusList != null and employeeStatusList.size() > 0">
	        	 and t1.employee_status in
		        <foreach collection="employeeStatusList" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
        	</if>
        
			<if test="ssoOrgCode != null and ssoOrgCode != ''">
	  			and t1.sso_org_code=#{ssoOrgCode}
			</if>
        	
			<!--排班结果：0-未排班，1-已排班-->
			<if test="scheduleStatus != null and scheduleStatus == '0'.toString and startDate != null and startDate != '' and endDate != null and endDate != ''">
	  			having record_count = 0
  			</if>
			<if test="scheduleStatus != null and scheduleStatus == '1'.toString and startDate != null and startDate != '' and endDate != null and endDate != ''">
	  			having record_count > 0
  			</if>
  </select>
  
  <select id="selectDeptByAuthorit" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee" resultType="cn.trasen.hrms.med.schedule.model.ScheduleDept">
  		 select 
	  			distinct(t1.organization_id) as deptId,
	  			max(t1.code) dept_code,
	  			max(t1.name) dept_name
  			from comm_organization t1
  			LEFT JOIN cust_emp_base t2 on t1.organization_id = t2.org_id
			where t1.is_deleted = 'N' and t2.is_deleted = 'N'
			
			<if test="scheduleOrgs != null and scheduleOrgs.size() > 0 and scheduleUsers != null and scheduleUsers.size() > 0">
	        	 and (t2.org_id in
		        <foreach collection="scheduleOrgs" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
		        or t2.employee_no in
		        <foreach collection="scheduleUsers" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
		        )
	        </if>
			
			<if test="scheduleOrgs != null and scheduleUsers == null">
	        	 and t2.org_id in
		        <foreach collection="scheduleOrgs" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
	        </if>
	      
	        <if test="scheduleUsers != null and scheduleOrgs == null">
	        	and t2.employee_no in
		        <foreach collection="scheduleUsers" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
	        </if>
	        
	        <if test="orgIdList != null and orgIdList.size() > 0 and (searchKey == null or searchKey == '')">
	        	 and t1.organization_id in
		        <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
        	</if>
        
			<if test="ssoOrgCode != null and ssoOrgCode != ''">
	  			and t1.sso_org_code=#{ssoOrgCode}
			</if>
        	group by t1.organization_id
  </select>
  
  <select id="selectTreesData" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee" resultType="String">
  		select tree_ids from comm_organization 
		where is_deleted = 'N'
		<if test="scheduleOrgs != null and scheduleOrgs.size() > 0">
        	 and organization_id in
	        <foreach collection="scheduleOrgs" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
        </if>
        
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and sso_org_code=#{ssoOrgCode}
		</if>
  </select>
  
  <select id="selectTreesData2" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee" resultType="String">
  		select t2.tree_ids from cust_emp_base t1
		LEFT JOIN comm_organization t2 on t1.org_id = t2.organization_id
		where t2.is_deleted = 'N'
		<if test="scheduleUsers != null and scheduleUsers.size() > 0">
       		and t1.employee_id in
	        <foreach collection="scheduleUsers" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
       </if>
        
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and t2.sso_org_code=#{ssoOrgCode}
		</if>
  </select>
  
  <select id="selectOrganizationList" resultType="cn.trasen.hrms.med.schedule.model.ScheduleOrganization">
  		select * from comm_organization 
		where is_deleted = 'N' and is_enable = '1'
		<if test="orgIdList != null and orgIdList.size() > 0">
        	 and organization_id in
	        <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
        </if>
        
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and sso_org_code=#{ssoOrgCode}
		</if>
        order by seq_no
  </select>
  
  <select id="getScheduleManageEmployeeList" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee" resultType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee">
  			select 
	  			t1.employee_id as employeeId,
	  			t1.employee_no as employeeNo,
	  			t1.employee_name as employeeName,
	  			t1.org_id as orgId,
	  			t3.name as orgName,
	  			t1.hosp_code as hospCode,
	  			t2.technical,
	  			t1.phone_number as phoneNumber
  			from cust_emp_base t1
  			LEFT JOIN cust_emp_info t2 on t1.employee_id = t2.info_id
  			LEFT JOIN comm_organization t3 on t3.organization_id = t1.org_id
			where t1.is_deleted = 'N'
			<if test="employeeName != null and employeeName != ''">
	  			and (
					t1.employee_name like concat('%',#{employeeName},'%')
					or t1.phone_number like concat('%',#{employeeName},'%')
				) 
  			</if>
  			<if test="scheduleOrgs != null and scheduleUsers != null">
	        	 and (t1.org_id in
		        <foreach collection="scheduleOrgs" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
		        or t1.employee_no in
		        <foreach collection="scheduleUsers" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
		        )
	        </if>
			
			<if test="scheduleOrgs != null and scheduleUsers == null">
	        	 and t1.org_id in
		        <foreach collection="scheduleOrgs" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
	        </if>
	      
	        <if test="scheduleUsers != null and scheduleOrgs == null">
	        	and t1.employee_no in
		        <foreach collection="scheduleUsers" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
	        </if>
	        
	        <if test="existEmpIds != null and existEmpIds.size() > 0">
	        	and t1.employee_id not in
		        <foreach collection="existEmpIds" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
	        </if>
  </select>
  
  <select id="selectLeaveData" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee" resultType="cn.trasen.hrms.med.schedule.model.LeaveRecordVo">
  			select r.id,r.leave_type as leaveType,DATE_FORMAT(r.start_date, '%Y-%m-%d') as startDate, DATE_FORMAT(r.end_date, '%Y-%m-%d') as endDate ,
  			'2' type, r.start_date_value startDateValue, r.end_date_value endDateValue, CAST(r.days AS DECIMAL(10, 1)) days, r.workflow_Id, e.employee_no, e.employee_name, e.employee_id
  			from hrms_leave_report r
  			left join cust_emp_base e on e.employee_no=r.employee_code and e.sso_org_code=r.sso_org_code
			where r.is_deleted = 'N'
			and r.start_date &lt;= #{endDate} and r.end_date >= #{startDate}
			<if test="employeeNo != null and employeeNo != ''">
	  			and r.employee_code=#{employeeNo}
			</if>
	        <if test="employeeIds != null and employeeIds.size() > 0">
	        	and e.employee_id in
		        <foreach collection="employeeIds" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
	        </if>
			<if test="orgId != null and orgId != ''">
	  			and e.org_id=#{orgId}
			</if>
			<if test="ssoOrgCode != null and ssoOrgCode != ''">
	  			and r.sso_org_code=#{ssoOrgCode}
			</if>
  </select>
  
  <select id="selectCancelLeaveData" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee" resultType="cn.trasen.hrms.med.schedule.model.LeaveRecordVo">
  			select r.id,r.cancel_leave_type as leaveType,DATE_FORMAT(r.start_date, '%Y-%m-%d') as startDate, DATE_FORMAT(r.end_date, '%Y-%m-%d') as endDate ,
  			'4' type, r.start_date_value startDateValue, r.end_date_value endDateValue, CAST(r.days AS DECIMAL(10, 1)) days, r.workflow_Id, e.employee_no, e.employee_name, e.employee_id
  			from hrms_cancel_leave_report r
  			left join cust_emp_base e on e.employee_no=r.employee_code and e.sso_org_code=r.sso_org_code
			where r.is_deleted = 'N'
			and start_date &lt;= #{endDate} and end_date >= #{startDate}
			<if test="employeeNo != null and employeeNo != ''">
	  			and r.employee_code=#{employeeNo}
			</if>
	        <if test="employeeIds != null and employeeIds.size() > 0">
	        	and e.employee_id in
		        <foreach collection="employeeIds" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
	        </if>
			<if test="orgId != null and orgId != ''">
	  			and e.org_id=#{orgId}
			</if>
			<if test="ssoOrgCode != null and ssoOrgCode != ''">
	  			and r.sso_org_code=#{ssoOrgCode}
			</if>
  </select>
  
  <select id="selectOutRecordData" parameterType="cn.trasen.hrms.med.schedule.model.ScheduleEmployee" resultType="cn.trasen.hrms.med.schedule.model.LeaveRecordVo">
  			select r.id,r.employee_id as employeeId,DATE_FORMAT(r.start_time,'%Y-%m-%d') as startDate,DATE_FORMAT(r.end_time,'%Y-%m-%d') as endDate,r.out_type as leaveType ,
  			'3' type, CAST(r.out_days AS DECIMAL(10, 1)) days, r.work_id workflow_Id, e.employee_no, e.employee_name
  			from hrms_out_record r
  			left join cust_emp_base e on e.employee_id=r.employee_id
				where r.is_deleted = 'N' and r.out_type is not null and r.start_time is not null and r.end_time is not null 
				and start_time &lt;= #{endDate} and end_time >= #{startDate}
				<if test="employeeId != null and employeeId != ''">
		  			and r.employee_id=#{employeeId}
				</if>
		        <if test="employeeIds != null and employeeIds.size() > 0">
		        	and e.employee_id in
			        <foreach collection="employeeIds" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
		        </if>
				<if test="orgId != null and orgId != ''">
		  			and e.org_id=#{orgId}
				</if>
			UNION ALL
			select r.id,r.employee_id as employeeId,DATE_FORMAT(r.start_time,'%Y-%m-%d') as startDate,DATE_FORMAT(r.end_time,'%Y-%m-%d') as endDate,r.out_type as leaveType ,
			'3' type, CAST(r.out_days AS DECIMAL(10, 1)) days, r.work_id workflow_Id, e.employee_no, e.employee_name
			from hrms_out_record_gp r
  			left join cust_emp_base e on e.employee_id=r.employee_id
				where r.is_deleted = 'N' and r.out_type is not null and r.start_time is not null and r.end_time is not null 
				and start_time &lt;= #{endDate} and end_time >= #{startDate}
				<if test="employeeId != null and employeeId != ''">
		  			and r.employee_id=#{employeeId}
				</if>
		        <if test="employeeIds != null and employeeIds.size() > 0">
		        	and e.employee_id in
			        <foreach collection="employeeIds" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
		        </if>
				<if test="orgId != null and orgId != ''">
		  			and e.org_id=#{orgId}
				</if>
			UNION ALL
			select r.id,r.employee_id as employeeId,DATE_FORMAT(r.start_time,'%Y-%m-%d') as startDate,DATE_FORMAT(r.end_time,'%Y-%m-%d') as endDate,r.out_type as leaveType ,
			'3' type, CAST(r.out_days AS DECIMAL(10, 1)) days, r.work_id workflow_Id, e.employee_no, e.employee_name
			from hrms_out_record_hy r
  			left join cust_emp_base e on e.employee_id=r.employee_id
				where r.is_deleted = 'N' and r.out_type is not null and r.start_time is not null and r.end_time is not null
				and start_time &lt;= #{endDate} and end_time >= #{startDate}
				<if test="employeeId != null and employeeId != ''">
		  			and r.employee_id=#{employeeId}
				</if>
		        <if test="employeeIds != null and employeeIds.size() > 0">
		        	and e.employee_id in
			        <foreach collection="employeeIds" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
		        </if>
				<if test="orgId != null and orgId != ''">
		  			and e.org_id=#{orgId}
				</if>
			UNION ALL
			select r.id,r.employee_id as employeeId,DATE_FORMAT(r.start_time,'%Y-%m-%d') as startDate,DATE_FORMAT(r.end_time,'%Y-%m-%d') as endDate,r.out_type as leaveType ,
			'3' type, CAST(r.out_days AS DECIMAL(10, 1)) days, r.work_id workflow_Id, e.employee_no, e.employee_name
			from hrms_out_record_xx r
  			left join cust_emp_base e on e.employee_id=r.employee_id
				where r.is_deleted = 'N' and r.out_type is not null and r.start_time is not null and r.end_time is not null
				and start_time &lt;= #{endDate} and end_time >= #{startDate}
				<if test="employeeId != null and employeeId != ''">
		  			and r.employee_id=#{employeeId}
				</if>
		        <if test="employeeIds != null and employeeIds.size() > 0">
		        	and e.employee_id in
			        <foreach collection="employeeIds" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
		        </if>
				<if test="orgId != null and orgId != ''">
		  			and e.org_id=#{orgId}
				</if>
  </select>
</mapper>
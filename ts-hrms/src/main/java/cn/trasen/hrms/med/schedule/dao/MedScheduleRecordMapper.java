package cn.trasen.hrms.med.schedule.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.hrms.med.schedule.model.MedScheduleRecord;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;
import cn.trasen.hrms.model.HrmsEmployee;
import tk.mybatis.mapper.common.Mapper;

public interface MedScheduleRecordMapper extends Mapper<MedScheduleRecord> {

	List<MedScheduleRecord> selectMedScheduleRecordList(ScheduleEmployee record);

	void batchInsert(List<MedScheduleRecord> list);

	List<Map<String, Object>> selectExportScheduleRecord(ScheduleEmployee record);

	void deleteMedScheduleRecord(ScheduleEmployee record);

	void deleteByGroupId(String groupId);

	List<Map<String, Object>> statisticsScheduleRecordM(ScheduleEmployee record);

	List<Map<String, Object>> getOrgBaseInfo(ScheduleEmployee record);

	List<Map<String,Object>> selectTotalScheduleRecord(ScheduleEmployee record);

	List<Map<String, String>> unfinishScheduleDareM(ScheduleEmployee record);

	String selectEmployeeNo(String employeeId);

	List<String> selectTodayScheduleRecord(@Param("holiday") String holiday,@Param("ssoOrgCode") String ssoOrgCode);
	
	/**
	 * 根据员工状态和员工类型查询人员列表
	 * @param employeeStatus
	 * @param orgAttributes
	 * @param pyjssj
	 * @return
	 */
	List<HrmsEmployee> selectEmployeeList(@Param("employeeStatus") List<String> employeeStatus, @Param("orgAttributes") List<String> orgAttributes,
			@Param("pyjssj") String pyjssj, @Param("ssoOrgCode") String ssoOrgCode);
	
	/**
	 * 更新员工状态为“离职”
	 * @param employeeIdList
	 * @param employeeStatus
	 */
	void updateEmployeeeStatus(@Param("employeeIdList") List<String> employeeIdList, @Param("employeeStatus") String employeeStatus);

	List<Map<String, String>> selectShiftScheduleData(@Param("scheduleStartDate")String scheduleStartDate,@Param("scheduleEndDate")String scheduleEndDate, @Param("currentUserId")String currentUserId);
	
	List<Map<String, String>> getTakeoverDoctor(@Param("deptId")String deptId, @Param("today")String today, @Param("employeeName")String employeeName);

	List<Map<String, String>> selectEmpByOrgId(@Param("deptId")String deptId, @Param("employeeName")String employeeName);
}
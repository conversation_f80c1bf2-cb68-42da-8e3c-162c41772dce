package cn.trasen.hrms.med.deptHonor.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.deptHonor.model.MedDeptHonorWinner;
import cn.trasen.hrms.med.deptHonor.service.MedDeptHonorWinnerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedDeptHonorWinnerController
 * @Description TODO
 * @date 2025��1��3�� ����3:29:57
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "科室荣誉管理获奖名单")
public class MedDeptHonorWinnerController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedDeptHonorWinnerController.class);

	@Autowired
	private MedDeptHonorWinnerService medDeptHonorWinnerService;

	/**
	 * @Title saveMedDeptHonorWinner
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��3�� ����3:29:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/deptHonorWinner/save")
	public PlatformResult<String> saveMedDeptHonorWinner(@RequestBody MedDeptHonorWinner record) {
		try {
			medDeptHonorWinnerService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedDeptHonorWinner
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��3�� ����3:29:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/deptHonorWinner/update")
	public PlatformResult<String> updateMedDeptHonorWinner(@RequestBody MedDeptHonorWinner record) {
		try {
			medDeptHonorWinnerService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedDeptHonorWinnerById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedDeptHonorWinner>
	 * @date 2025��1��3�� ����3:29:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/deptHonorWinner/{id}")
	public PlatformResult<MedDeptHonorWinner> selectMedDeptHonorWinnerById(@PathVariable String id) {
		try {
			MedDeptHonorWinner record = medDeptHonorWinnerService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedDeptHonorWinnerById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��1��3�� ����3:29:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/deptHonorWinner/delete/{id}")
	public PlatformResult<String> deleteMedDeptHonorWinnerById(@PathVariable String id) {
		try {
			medDeptHonorWinnerService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedDeptHonorWinnerList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedDeptHonorWinner>
	 * @date 2025��1��3�� ����3:29:57
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/deptHonorWinner/list")
	public DataSet<MedDeptHonorWinner> selectMedDeptHonorWinnerList(Page page, MedDeptHonorWinner record) {
		return medDeptHonorWinnerService.getDataSetList(page, record);
	}
	
	/**
	 * 
	 * @param response
	 */
	@GetMapping(value = "/api/deptHonorWinner/downloadTemplate")
    @ApiOperation(value = "导入模版下载", notes = "导入模版下载")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            String filename = "科室荣誉获奖名单导入模板.xlsx";
            String template = "template/importDeptHonorWinner.xlsx";
            ClassPathResource resource = new ClassPathResource(template);
            exportExcelUtil.downloadExportExcel(filename, response, resource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
	
	@ApiOperation(value = "获取模板数据", notes = "获取模板数据")
	@PostMapping(value = "/api/deptHonorWinner/importHonorWinner")
	public PlatformResult<List<MedDeptHonorWinner>> importHonorWinner(@RequestParam("file") MultipartFile file) {
		
		 try {
			 	List<MedDeptHonorWinner> list = (List<MedDeptHonorWinner>) ImportExcelUtil.getExcelDatas(file, MedDeptHonorWinner.class);

	            if (!list.isEmpty()) {
	                return PlatformResult.success(list);
	            } else {
	                return PlatformResult.failure("数据为空");
	            }

	        } catch (Exception e) {
	            e.printStackTrace();
	            logger.error(e.getMessage(), e);
	            return PlatformResult.failure("导入数据失败，失败原因:" + e.getMessage());
	        }
	}
	
}

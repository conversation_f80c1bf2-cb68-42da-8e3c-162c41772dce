package cn.trasen.hrms.med.deptHonor.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.deptHonor.model.MedDeptHonor;

/**
 * @ClassName MedDeptHonorService
 * @Description TODO
 * @date 2025��1��3�� ����3:27:47
 * <AUTHOR>
 * @version 1.0
 */
public interface MedDeptHonorService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��1��3�� ����3:27:47
	 * <AUTHOR>
	 */
	Integer save(MedDeptHonor record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��1��3�� ����3:27:47
	 * <AUTHOR>
	 */
	Integer update(MedDeptHonor record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��1��3�� ����3:27:47
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedDeptHonor
	 * @date 2025��1��3�� ����3:27:47
	 * <AUTHOR>
	 */
	MedDeptHonor selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedDeptHonor>
	 * @date 2025��1��3�� ����3:27:47
	 * <AUTHOR>
	 */
	DataSet<MedDeptHonor> getDataSetList(Page page, MedDeptHonor record);
}

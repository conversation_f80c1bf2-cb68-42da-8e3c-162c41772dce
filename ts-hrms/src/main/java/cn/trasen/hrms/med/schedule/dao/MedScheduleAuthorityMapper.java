package cn.trasen.hrms.med.schedule.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.med.schedule.model.LeaveRecordVo;
import cn.trasen.hrms.med.schedule.model.MedScheduleAuthority;
import cn.trasen.hrms.med.schedule.model.ScheduleDept;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;
import cn.trasen.hrms.med.schedule.model.ScheduleOrganization;
import tk.mybatis.mapper.common.Mapper;

public interface MedScheduleAuthorityMapper extends Mapper<MedScheduleAuthority> {

	/**
	 * 查询班次统计的人员
	 * @param record
	 * @param page
	 * @return
	 */
	List<ScheduleEmployee> selectEmployeeByAuthorit(ScheduleEmployee record,Page page);
	
	/**
	 * 获取班次统计的科室
	 * @param record
	 * @param page
	 * @return
	 */
	List<ScheduleDept> selectDeptByAuthorit(ScheduleEmployee record,Page page);

	List<ScheduleEmployee> selectEmployeeByAuthorit(ScheduleEmployee record);

	List<ScheduleEmployee> selectEmployeeByOrgId(ScheduleEmployee record);

	List<String> selectTreesData(ScheduleEmployee record);

	List<String> selectTreesData2(ScheduleEmployee record);

	List<ScheduleOrganization> selectOrganizationList(@Param("orgIdList") List<String> orgIdList, @Param ("ssoOrgCode") String ssoOrgCode);

	List<ScheduleEmployee> getScheduleManageEmployeeList(ScheduleEmployee record);

	List<LeaveRecordVo> selectLeaveData(ScheduleEmployee record);

	List<LeaveRecordVo> selectOutRecordData(ScheduleEmployee record);

	List<LeaveRecordVo> selectCancelLeaveData(ScheduleEmployee record);
}
package cn.trasen.hrms.med.radiate.vo;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 放射诊疗证件查询参照
 * @date 2025-05-22 11:52:53
 * <AUTHOR> @version 1.0
 */
@Setter
@Getter
public class RadiateCertificateReqVo {
    
    @ApiModelProperty(value = "状态")
    private String employeeStatus;

    @ApiModelProperty(value = "人员工号")
    private String employeeNo;

    @ApiModelProperty(value = "人员id")
    private String employeeId;

    @ApiModelProperty(value = "人员姓名")
    private String employeeName;

    @ApiModelProperty(value = "技术职称")
    private String technicalTitle;
    
    @ApiModelProperty(value = "证件编号")
    private String certificateNumber;
    
    @ApiModelProperty(value = "证书类型")
    private String certificateType;
    
    @ApiModelProperty(value = "发证开始日期")
    private String issueDateBegin;
    
    @ApiModelProperty(value = "发证结束日期")
    private String issueDateEnd;
    
    @ApiModelProperty(value = "所属科室ID")
    private String orgId;
    
    @ApiModelProperty(value = "所属科室ID多个逗号隔开")
    private String orgIds;

    @ApiModelProperty(value = "所属科室ID集合")
    private List<String> orgIdList;
    
    @ApiModelProperty(value = "搜索关键字-工号/姓名")
    private String condition;

}

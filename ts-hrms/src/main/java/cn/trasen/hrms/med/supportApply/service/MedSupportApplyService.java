package cn.trasen.hrms.med.supportApply.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.supportApply.model.MedSupportApply;

/**
 * @ClassName MedSupportApplyService
 * @Description TODO
 * @date 2025��1��4�� ����5:27:35
 * <AUTHOR>
 * @version 1.0
 */
public interface MedSupportApplyService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��1��4�� ����5:27:35
	 * <AUTHOR>
	 */
	Integer save(MedSupportApply record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��1��4�� ����5:27:35
	 * <AUTHOR>
	 */
	Integer update(MedSupportApply record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��1��4�� ����5:27:35
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedSupportApply
	 * @date 2025��1��4�� ����5:27:35
	 * <AUTHOR>
	 */
	MedSupportApply selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedSupportApply>
	 * @date 2025��1��4�� ����5:27:35
	 * <AUTHOR>
	 */
	DataSet<MedSupportApply> getDataSetList(Page page, MedSupportApply record);
}

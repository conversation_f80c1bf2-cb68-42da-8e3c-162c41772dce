package cn.trasen.hrms.med.delivery.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

@Table(name = "med_delivery_record")
@Setter
@Getter
public class MedDeliveryRecord {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 流程id
     */
    @Column(name = "workflow_id")
    @ApiModelProperty(value = "流程id")
    private String workflowId;

    /**
     * 字典id
     */
    @Column(name = "dict_id")
    @ApiModelProperty(value = "字典id")
    private String dictId;

    /**
     * 项目名称
     */
    @Column(name = "project_name")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 申请医师
     */
    @Column(name = "apply_name")
    @ApiModelProperty(value = "申请医师")
    private String applyName;

    /**
     * 联系电话
     */
    @Column(name = "apply_tel")
    @ApiModelProperty(value = "联系电话")
    private String applyTel;
    
    @Column(name = "patient_name")
    @ApiModelProperty(value = " 患者姓名")
    private String patientName;
  
    /**
     * 住院号/门诊号
     */
    @Column(name = "in_patient_no")
    @ApiModelProperty(value = "住院号/门诊号")
    private String inPatientNo;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sex;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private String age;

    /**
     * 主要诊断
     */
    @ApiModelProperty(value = "主要诊断")
    private String diagnosis;

    /**
     * 患者病情简介
     */
    @ApiModelProperty(value = "患者病情简介")
    private String illness;

    /**
     * 样本类型
     */
    @Column(name = "sample_type")
    @ApiModelProperty(value = "样本类型")
    private String sampleType;

    /**
     * 样本量
     */
    @Column(name = "sample_dose")
    @ApiModelProperty(value = "样本量")
    private String sampleDose;

    /**
     * 外送检验机构名称
     */
    @Column(name = "test_org")
    @ApiModelProperty(value = "外送检验机构名称")
    private String testOrg;

    /**
     * 费用
     */
    @ApiModelProperty(value = "费用")
    private String cost;

    /**
     * 外送原因
     */
    @ApiModelProperty(value = "外送原因")
    private String reason;

    /**
     * 项目内涵
     */
    @ApiModelProperty(value = "项目内涵")
    private String connotation;

    /**
     * 申请日期
     */
    @Column(name = "apply_date")
    @ApiModelProperty(value = "申请日期")
    private String applyDate;

    /**
     * 申请科室
     */
    @Column(name = "apply_org")
    @ApiModelProperty(value = "申请科室")
    private String applyOrg;

    /**
     * 申请院区
     */
    @Column(name = "apply_area")
    @ApiModelProperty(value = "申请院区")
    private String applyArea;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;
    
    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    @Transient
    private int orderNumber;
    
    @Transient
    private String searchKey; //组合查询字段
    
    @Transient
    private String startDate; // 审批通过开始时间
    
    @Transient
    private String endDate;// 审批通结束时间
    
    @Transient
    private String applyDateStart; //申请开始时间
    
    @Transient
    private String applyDateEnd;//申请结束时间
    
    @Transient
    private String orgIds; //申请科室id
    
    @Transient
    private List<String> orgIdList;
}
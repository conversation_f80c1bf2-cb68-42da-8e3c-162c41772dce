package cn.trasen.hrms.med.radiate.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.hrms.med.radiate.util.MonitorCycleUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.bean.ResultData;
import cn.trasen.hrms.med.radiate.dao.RadiateCheckupRegisterMapper;
import cn.trasen.hrms.med.radiate.dao.RadiatePersonnelRegisterMapper;
import cn.trasen.hrms.med.radiate.emun.RegisterCategoryEmun;
import cn.trasen.hrms.med.radiate.model.DictConstant;
import cn.trasen.hrms.med.radiate.model.RadiateCheckupAbnormal;
import cn.trasen.hrms.med.radiate.model.RadiateCheckupRegister;
import cn.trasen.hrms.med.radiate.model.RadiateMonitor;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;
import cn.trasen.hrms.med.radiate.service.RadiateCheckupAbnormalService;
import cn.trasen.hrms.med.radiate.service.RadiateCheckupRegisterService;
import cn.trasen.hrms.med.radiate.service.RadiateMonitorService;
import cn.trasen.hrms.med.radiate.vo.RadiateCheckupReqVo;
import cn.trasen.hrms.med.radiate.vo.RadiatePersonnelReqVo;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.service.BaseDictItemService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName RadiateCheckupRegisterServiceImpl
 * @Description TODO
 * @date 2025��1��8�� ����11:03:28
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class RadiateCheckupRegisterServiceImpl implements RadiateCheckupRegisterService {

	@Autowired
	private RadiateCheckupRegisterMapper mapper;

	@Autowired
	private BaseDictItemService baseDictItemService;
	
	@Autowired
	private RadiateCheckupAbnormalService radiateCheckupAbnormalService;
	
	@Resource
	private HrmsEmployeeService hrmsEmployeeService;
	
	@Autowired
	private RadiatePersonnelRegisterMapper radiatePersonnelRegisterMapper;
	
	@Autowired
	private RadiateMonitorService radiateMonitorService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(RadiateCheckupRegister record) {
		Assert.hasText(record.getCheckupDate(), "体检日期不能为空.");
		//证件唯一性校验
		Example example = new Example(RadiateCheckupRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", record.getEmployeeId());
		criteria.andEqualTo("checkupDate", record.getCheckupDate());
		List<RadiateCheckupRegister> records = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(records)){
			throw new BusinessException(StrUtil.format("该用户体检日期{}已登记了！", record.getCheckupDate()));
		}
		
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//新增复查记录
		List<RadiateCheckupAbnormal> checkupAbnormalList = record.getCheckupAbnormalList();
		if(CollUtil.isNotEmpty(checkupAbnormalList)){
			for(RadiateCheckupAbnormal ca : checkupAbnormalList){
				ca.setCheckupId(record.getId());
			}
		}
		radiateCheckupAbnormalService.saveOrUpdateList(checkupAbnormalList, true);
		
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(RadiateCheckupRegister record) {
		Assert.hasText(record.getCheckupDate(), "体检日期不能为空.");
		//证件唯一性校验
		Example example = new Example(RadiateCheckupRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", record.getEmployeeId());
		criteria.andEqualTo("checkupDate", record.getCheckupDate());
		criteria.andNotEqualTo("id", record.getId());
		List<RadiateCheckupRegister> records = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(records)){
			throw new BusinessException(StrUtil.format("该用户体检日期{}已登记了！", record.getCheckupDate()));
		}
		
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//新增复查记录
		List<RadiateCheckupAbnormal> checkupAbnormalList = record.getCheckupAbnormalList();
		if(CollUtil.isNotEmpty(checkupAbnormalList)){
			for(RadiateCheckupAbnormal ca : checkupAbnormalList){
				ca.setCheckupId(record.getId());
			}
		}
		radiateCheckupAbnormalService.saveOrUpdateList(checkupAbnormalList, false);
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		RadiateCheckupRegister record = new RadiateCheckupRegister();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public RadiateCheckupRegister selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<RadiateCheckupRegister> getDataSetList(Page page, RadiateCheckupRegister record) {
		Example example = new Example(RadiateCheckupRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<RadiateCheckupRegister> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public DataSet<RadiateCheckupRegister> selectRadiateCheckupRegisterPageList(Page page, RadiateCheckupReqVo record) {
		if(!ObjectUtils.isEmpty(record.getExpired())){
			//设置当前时间字符串
			SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-dd");
			record.setToday(sdf.format(new Date()));
		}
		List<RadiateCheckupRegister> records = mapper.selectRadiateCheckupRegisterPageList(page, record);
		Map<String, String>  employeeStatusMap  = baseDictItemService.convertDictMap(DictConstant.EMPLOYEE_STATUS);//员工状态
		for(RadiateCheckupRegister item : records) {
			item.setEmployeeStatusText(employeeStatusMap.get(item.getEmployeeStatus()));
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer saveOrUpdateList(List<RadiateCheckupRegister> records, RadiateMonitor monitor, boolean isAdd, RadiatePersonnelRegister personnel) {
		if(CollUtil.isEmpty(records)){
			return 0;
		}
		//判断监测周期是否为空
		String monitorCycle = null;
		if(monitor != null && !ObjectUtils.isEmpty(monitor.getMonitorCycle())){
			monitorCycle =  monitor.getMonitorCycle();
		}
		//新增
		if(isAdd){
			for (RadiateCheckupRegister record : records){
				//计算到期时间
				if(!ObjectUtils.isEmpty(monitorCycle)){
					record.setExpirationDate(MonitorCycleUtils.calculateExpirationDate(record.getCheckupDate(), monitorCycle));
				}
				record.setEmployeeId(personnel.getEmployeeId());
				record.setEmployeeNo(personnel.getEmployeeNo());
				record.setEmployeeName(personnel.getEmployeeName());
				save(record);
			}
		} else {
			//先查询该用户一共有几条数据
			List<RadiateCheckupRegister> oldRecords = selectByEmployeeId(personnel.getEmployeeId());
			//将更新的数据记录，用于后续排除删除
			Set<String> updateIds = new HashSet<>();
			//遍历新增的数据
			for (RadiateCheckupRegister record : records){
				//判断该用户是否已经存在该数据
				boolean flag = false;
				for (RadiateCheckupRegister oldRecord : oldRecords){
					if(record.getId().equals(oldRecord.getId())){
						flag = true;
						record.setId(oldRecord.getId());
						//只计算最新到期时间，并存入数据库-先判断到期时间是否已经超过当前，超过则按照之前的算，否则按照新的规则计算
						if(ObjectUtils.isEmpty(oldRecord.getExpirationDate())){
							record.setExpirationDate(MonitorCycleUtils.calculateExpirationDate(record.getCheckupDate(), monitorCycle));
						}
						record.setEmployeeId(personnel.getEmployeeId());
						record.setEmployeeNo(personnel.getEmployeeNo());
						record.setEmployeeName(personnel.getEmployeeName());
						update(record);
						updateIds.add(record.getId());
					}
				}
				if(!flag){
					//计算到期时间
					if(!ObjectUtils.isEmpty(monitorCycle)){
						record.setExpirationDate(MonitorCycleUtils.calculateExpirationDate(record.getCheckupDate(), monitorCycle));
					}
					record.setEmployeeId(personnel.getEmployeeId());
					record.setEmployeeNo(personnel.getEmployeeNo());
					record.setEmployeeName(personnel.getEmployeeName());
					save(record);
				}
			}
			//删除多余的数据
			if(CollUtil.isNotEmpty(oldRecords) && oldRecords.size() != updateIds.size()){
				for(RadiateCheckupRegister oldRecord : oldRecords){
					if(!updateIds.contains(oldRecord.getId())){
						deleteById(oldRecord.getId());
					}
				}
			}
		}
		return records.size();
	}

	@Override
	public List<RadiateCheckupRegister> selectByEmployeeId(String employeeId) {
		Example example = new Example(RadiateCheckupRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<RadiateCheckupRegister> records = mapper.selectByExample(example);
		for(RadiateCheckupRegister checkup : records){
			List<RadiateCheckupAbnormal> checkupAbnormalList = radiateCheckupAbnormalService.getListByCheckupId(checkup.getId());
			checkup.setCheckupAbnormalList(checkupAbnormalList);
			checkup.setCheckupAbnormalCount(checkupAbnormalList.size());
		}
		return records;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteByEmployeeId(String employeeId) {
		Assert.hasText(employeeId, "人员ID不能为空.");
		Example example = new Example(RadiateCheckupRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		
		RadiateCheckupRegister update = new RadiateCheckupRegister();
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		update.setUpdateDate(new Date());
		update.setIsDeleted(Contants.IS_DELETED_TURE);
		if (user != null) {
			update.setUpdateUser(user.getUsercode());
			update.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByExampleSelective(update, example);
	}

	@Transactional(readOnly = false)
	@Override
	public PlatformResult importData(List<RadiateCheckupRegister> list) {
		
		//查询所有的人
		HrmsEmployee  entity = new HrmsEmployee();
		List<HrmsEmployee> employeeList = hrmsEmployeeService.getList(entity);
		Map<String, HrmsEmployee> employeeMap = new HashMap<>();
		if(CollUtil.isNotEmpty(employeeList)){
			employeeMap = employeeList.stream().collect(Collectors.toMap(HrmsEmployee::getEmployeeNo, a -> a, (k1, k2) -> k1));
		}
		
		//查询所有的登记人员-用于去重处理
		List<RadiatePersonnelRegister> personnelList = radiatePersonnelRegisterMapper.getList(new RadiatePersonnelReqVo());
		Map<String, RadiatePersonnelRegister> personnelMap = new HashMap<>();
		if(CollUtil.isNotEmpty(personnelList)){
			personnelMap = personnelList.stream().collect(Collectors.toMap(RadiatePersonnelRegister::getEmployeeNo, a -> a, (k1, k2) -> k1));
		}
		
		//人员监控设置信息
		Map<String, String> personMonitorMap = new HashMap<>();
		List<RadiateMonitor> monitors = radiateMonitorService.selectAll();
		if(CollUtil.isNotEmpty(monitors)){
			for(RadiateMonitor m : monitors){
				if(m.getRegisterCategory().equals(RegisterCategoryEmun.CHECKUP.getCode())){
					personMonitorMap.put(m.getEmployeeId(), m.getMonitorCycle());
				}
			}
		}

		//查询所有人的体检，校验 工号+体检日期 唯一性
		List<RadiateCheckupRegister> checkupList = getList();
		Set<String> checkupSet = new HashSet<>();
		if(CollUtil.isNotEmpty(checkupList)){
			for(RadiateCheckupRegister c : checkupList){
				checkupSet.add(c.getEmployeeId() + c.getCheckupDate());
			}
		}
		
		List<String> fileIsnulls; //为空的字段
		List<String> fileDictNotFunds; //字典不能匹配字段
		
		List<ResultData> badList = new ArrayList<>();//失败信息
		
		Integer successCnt = 0;
		Integer errorCnt = 0;
		
		ResultData badData;
		DictItemResp dict;
		
		int i = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		for (RadiateCheckupRegister item : list) {
			
			i++;
			
			fileIsnulls = new ArrayList<String>();
			fileDictNotFunds = new ArrayList<String>();

			
			String employeeName = item.getEmployeeName();
			if(!StrUtil.isEmpty(employeeName)){
				item.setEmployeeName(employeeName.trim());
			}
			if(StrUtil.isEmpty(item.getEmployeeName())){
				fileIsnulls.add("姓名");
			}
			
			String employeeNo = item.getEmployeeNo();
			if(!StrUtil.isEmpty(employeeNo)){
				item.setEmployeeNo(employeeNo.trim());
			}
			if(StrUtil.isEmpty(item.getEmployeeNo())){
				fileIsnulls.add("工号");
			}
			
			String checkupDate = item.getCheckupDate();
			if(!StrUtil.isEmpty(checkupDate)){
				item.setCheckupDate(checkupDate.trim());
			}
			if(StrUtil.isEmpty(item.getCheckupDate())){
				fileIsnulls.add("体检日期");
			}
			
			String status = item.getStatus();
			if(!StrUtil.isEmpty(status)){
				item.setStatus(status.trim());
			}
			if(StrUtil.isEmpty(item.getStatus())){
				fileIsnulls.add("体检状态");
			}
			
			if(CollUtil.isNotEmpty(fileIsnulls)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】为空!", i + 1,StrUtil.join("、", fileIsnulls)));
				badList.add(badData);
				errorCnt++;
				continue;
			}

			//判断是否已经登记
			employeeNo = item.getEmployeeNo();
			if(!personnelMap.containsKey(employeeNo)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】工号未登记!", i + 1 , employeeNo));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			//校验姓名和工号是否匹配
			if(!employeeMap.containsKey(item.getEmployeeNo())){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】工号不存在!", i + 1 , item.getEmployeeNo()));
				badList.add(badData);
				errorCnt++;
				continue;
			} else {
				HrmsEmployee emp = employeeMap.get(item.getEmployeeNo());
				if(!emp.getEmployeeName().equals(item.getEmployeeName())){
					badData = new ResultData();
					badData.setData(StrUtil.format("第【{}】行的【{}】姓名对应的工号不匹配!", i + 1 , item.getEmployeeName()));
					badList.add(badData);
					errorCnt++;
					continue;
				} else {
					item.setEmployeeId(emp.getEmployeeId());
					item.setEmployeeNo(emp.getEmployeeNo());
					item.setEmployeeName(emp.getEmployeeName());
				}
			}
			
			//校验日期格式
			try {
				sdf.parse(item.getCheckupDate());
			} catch (ParseException e) {
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的体检日期格式为YYYY-MM-DD，【{}】不正确!", i + 1 , item.getCheckupDate()));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			//校验体检唯一性
			if(checkupSet.contains(item.getEmployeeId() + item.getCheckupDate())){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】体检已登记!", i + 1 , item.getCheckupDate()));
				badList.add(badData);
				errorCnt++;
				continue;
			} else {
				checkupSet.add(item.getEmployeeId() + item.getCheckupDate());
			}
			
			//校验体检状态
			if(!item.getStatus().equals("正常") && !item.getStatus().equals("异常")){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的体检状态格式为 正常/异常，【{}】不正确!", i + 1 , item.getStatus()));
				badList.add(badData);
				errorCnt++;
				continue;
			} else if(item.getStatus().equals("正常")) {
				item.setStatus("1");
			} else if(item.getStatus().equals("异常")) {
				item.setStatus("0");
			}
			
			//计算到期时间
			if(personMonitorMap.containsKey(item.getEmployeeId())) {
				item.setExpirationDate(MonitorCycleUtils.calculateExpirationDate(item.getCheckupDate(), personMonitorMap.get(item.getEmployeeId())));
			}
			
			save(item);
			successCnt++;
			
		}
		
		if(CollUtil.isNotEmpty(badList)){
			return PlatformResult.failure(StrUtil.format("信息导入 ,总条数:{}、成功:{}、失败{}", list.size(),successCnt,errorCnt),badList);
		}else{
			return PlatformResult.success(StrUtil.format("信息导入 ,总条数:{}、成功:{}、失败{}", list.size(),successCnt,errorCnt));
		}
	}

	@Override
	public List<RadiateCheckupRegister> getList() {
		Example example = new Example(RadiateCheckupRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return mapper.selectByExample(example);
	}
}

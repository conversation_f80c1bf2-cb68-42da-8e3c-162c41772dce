package cn.trasen.hrms.med.deptHonor.model;

import io.swagger.annotations.*;
import javax.persistence.*;

import org.jeecgframework.poi.excel.annotation.Excel;

import lombok.*;

@Table(name = "med_dept_honor_winner")
@Setter
@Getter
public class MedDeptHonorWinner {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 荣誉主表id
     */
    @Column(name = "honor_id")
    @ApiModelProperty(value = "荣誉主表id")
    private String honorId;

    /**
     * 工号
     */
    @Excel(name = "工号")
    @Column(name = "user_code")
    @ApiModelProperty(value = "工号")
    private String userCode;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    @Column(name = "user_name")
    @ApiModelProperty(value = "姓名")
    private String userName;

    /**
     * 岗位名称
     */
    @Excel(name = "岗位名称")
    @Column(name = "post_name")
    @ApiModelProperty(value = "岗位名称")
    private String postName;

    /**
     * 性别
     */
    @Excel(name = "性别")
    @ApiModelProperty(value = "性别")
    private String sex;
}
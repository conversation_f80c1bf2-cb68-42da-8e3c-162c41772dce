package cn.trasen.hrms.med.radiate.controller;

import java.util.ArrayList;
import java.util.List;

import cn.trasen.hrms.med.radiate.service.*;
import cn.trasen.hrms.med.radiate.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName RadiatePersonnelWorkbenchController
 * @Description 放射人员登记工作台
 * @date 2025-05-24 11:21:29
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@Api(tags = "放射人员登记工作台")
public class RadiatePersonnelWorkbenchController {
	
	@Autowired
	private RadiatePersonnelRegisterService radiatePersonnelRegisterService;

	@Autowired
	private RadiateQualificationCertificateService radiateQualificationCertificateService;
	
	@Autowired
	private RadiateCheckupRegisterService radiateCheckupRegisterService;

	@Autowired
	private RadiateTrainingRegisterService radiateTrainingRegisterService;

	@Autowired
	private RadiateDoseMonitoringService radiateDoseMonitoringService;

	/**
	 * 
	 * @Title selectWorkbenchList
	 * @return PlatformResult<List<PersonnelWkVo>>
	 * @date 2025-05-24 11:21:29
	 * <AUTHOR>
	 */
	@ApiOperation(value = "放射人员监控数据", notes = "放射人员监控数据")
	@GetMapping("/api/RadiatePersonnelWorkbench/list")
	public PlatformResult<List<PersonnelWkVo>> selectWorkbenchList() {
		try {
			Page page = new Page();
			page.setPageSize(Integer.MAX_VALUE);
			List<PersonnelWkVo> list = new ArrayList<>();
			//放射人员
			PersonnelWkVo vo = new PersonnelWkVo();
			vo.setItemName("放射人员(人)");
			vo.setItemCode("monitorList");
			vo.setCount(radiatePersonnelRegisterService.selectRadiatePersonnelRegisterPageList(page, new RadiatePersonnelReqVo()).getTotalCount());
			list.add(vo);
			
			//证件资质
			vo = new PersonnelWkVo();
			vo.setItemName("证件资质(份)");
			vo.setItemCode("certificateList");
			vo.setCount(radiateQualificationCertificateService.getDataSetList(page, null).getTotalCount());
			list.add(vo);
			
			//体检到期
			vo = new PersonnelWkVo();
			//每个人最近一次体检的到期日期与当前时间比较，比当前时间大，则到期
			RadiateCheckupReqVo checkupReq = new RadiateCheckupReqVo();
			vo.setItemName("体检到期(人)");
			vo.setItemCode("checkupList-expired");
			checkupReq.setExpired("1");
			vo.setCount(radiateCheckupRegisterService.selectRadiateCheckupRegisterPageList(page, checkupReq).getTotalCount());
			list.add(vo);
			
			//体检异常
			vo = new PersonnelWkVo();
			//每个人最近一次体检异常的人数
			checkupReq = new RadiateCheckupReqVo();
			checkupReq.setLatestStatus("0");
			vo.setItemName("体检异常(人)");
			vo.setItemCode("checkupList-latestStatus");
			vo.setCount(radiateCheckupRegisterService.selectRadiateCheckupRegisterPageList(page, checkupReq).getTotalCount());
			list.add(vo);
			
			//培训到期
			vo = new PersonnelWkVo();
			vo.setItemName("培训到期(条)");
			vo.setItemCode("trainingList-expired");
			//每个人每种类型最近一次培训的到期日期与当前时间比较，比当前时间大，则到期
			RadiateTrainingReqVo trainingReq = new RadiateTrainingReqVo();
			trainingReq.setExpired("1");
			vo.setCount(radiateTrainingRegisterService.selectRadiateTrainingRegisterPageList(page, trainingReq).getTotalCount());
			list.add(vo);

			//剂量监测不合格
			vo = new PersonnelWkVo();
			vo.setItemName("剂量监测不合格(条)");
			vo.setItemCode("doseList-latestMonitorStatus");
			//每个人每种监测最近一次剂量监测不合格人数
			RadiateDoseReqVo doseReq = new RadiateDoseReqVo();
			doseReq.setLatestMonitorStatus("2");
			vo.setCount(radiateDoseMonitoringService.selectRadiateDoseMonitoringPageList(page, doseReq).getTotalCount());
			list.add(vo);
			
			//剂量监测到期(人)
			vo = new PersonnelWkVo();
			vo.setItemName("剂量监测到期(条)");
			vo.setItemCode("doseList-expired");
			//每个人每种监测类型最近一次剂量监测到期日期与当前时间比较，比当前时间大，则到期
			doseReq = new RadiateDoseReqVo();
			doseReq.setExpired("1");
			vo.setCount(radiateDoseMonitoringService.selectRadiateDoseMonitoringPageList(page, doseReq).getTotalCount());
			list.add(vo);
			
			return PlatformResult.success(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}

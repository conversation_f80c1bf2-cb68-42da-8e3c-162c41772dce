package cn.trasen.hrms.med.schedule.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.hrms.med.schedule.dao.MedScheduleAuthorityMapper;
import cn.trasen.hrms.med.schedule.dao.MedScheduleClassesMapper;
import cn.trasen.hrms.med.schedule.model.MedScheduleAuthority;
import cn.trasen.hrms.med.schedule.model.MedScheduleClasses;
import cn.trasen.hrms.med.schedule.model.MedScheduleClassesValidateCopyRes;
import cn.trasen.hrms.med.schedule.model.MedScheduleClassesValidateCopyVo;
import cn.trasen.hrms.med.schedule.service.MedScheduleClassesService;
import cn.trasen.hrms.med.schedule.service.MedScheduleRecordService;
import cn.trasen.hrms.utils.DateUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedScheduleClassesServiceImpl
 * @Description TODO
 * @date 2025��3��29�� ����2:58:06
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedScheduleClassesServiceImpl implements MedScheduleClassesService {

	@Autowired
	private MedScheduleClassesMapper mapper;
	
	@Autowired
	private MedScheduleRecordService medScheduleRecordService;

	@Autowired
	private MedScheduleAuthorityMapper medScheduleAuthorityMapper;
	
	@Autowired
	private HrmsEmployeeFeignService hrmsEmployeeFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedScheduleClasses record) {
		
		Example example = new Example(MedScheduleClasses.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("classesName", record.getClassesName());
		criteria.andEqualTo("classesWorktime", record.getClassesWorktime());
		List<MedScheduleClasses> classesList = mapper.selectByExample(example);
		
		if(CollectionUtils.isNotEmpty(classesList)) {
			Assert.isTrue(false, record.getClassesName() + "存在相同的班次名称和班次时间!");
		}
		
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		
		if(StringUtils.isNotBlank(record.getClassesWorktime())) {
			//考勤小时计算
			String classesWorktime = record.getClassesWorktime();
			String[] split = classesWorktime.split(",");
			double countDate = 0;
			String min = "2021-05-01 ";
			String max = "2021-05-02 ";
			for (int i = 0; i < split.length; i++) {
				String[] split2 = split[i].split("-");  //00:00-04:00
				if(DateUtils.judgeSize(min +split2[0], min +split2[1])) {
					countDate += DateUtils.dateDiff(min +split2[0], min +split2[1]);
				}else {
					countDate += DateUtils.dateDiff(min +split2[0], max +split2[1]);
				}
			}
			record.setClassesHours(String.format("%.1f", (countDate / 60)));
		}else {
			record.setClassesHours("0");
		}
		
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedScheduleClasses record) {
		
		Example example = new Example(MedScheduleClasses.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("classesName", record.getClassesName());
		criteria.andEqualTo("classesWorktime", record.getClassesWorktime());
		criteria.andNotEqualTo("id", record.getId());
		List<MedScheduleClasses> classesList = mapper.selectByExample(example);
		
		if(CollectionUtils.isNotEmpty(classesList)) {
			Assert.isTrue(false, record.getClassesName() + "存在相同的班次名称和班次时间!");
		}
		
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		if(StringUtils.isNotBlank(record.getClassesWorktime())) {
			//考勤小时计算
			String classesWorktime = record.getClassesWorktime();
			String[] split = classesWorktime.split(",");
			double countDate = 0;
			String min = "2021-05-01 ";
			String max = "2021-05-02 ";
			for (int i = 0; i < split.length; i++) {
				String[] split2 = split[i].split("-");  //00:00-04:00
				if(DateUtils.judgeSize(min +split2[0], min +split2[1])) {
					countDate += DateUtils.dateDiff(min +split2[0], min +split2[1]);
				}else {
					countDate += DateUtils.dateDiff(min +split2[0], max +split2[1]);
				}
			}
			record.setClassesHours(String.format("%.1f", (countDate / 60)));
		}else {
			record.setClassesHours("0");
		}
		
		return mapper.updateByPrimaryKeySelective(record);
	}
	
	

	@Override
	@Transactional(readOnly = false)
	public void disableOrEnable(MedScheduleClasses record) {
		mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		
		int count = medScheduleRecordService.selectCountByClassesId(id);
		if(count > 0) {
			Assert.isTrue(false,"已存在排班数据，不允许删除!");
		}
		
		MedScheduleClasses record = new MedScheduleClasses();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedScheduleClasses selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedScheduleClasses> getDataSetList(Page page, MedScheduleClasses record) {
		/**
		 * 添加数据权限
		 * 1.如果是排班设置员，角色编码为  SCHEDULER_SETTING_MANAGER 可以查看所有的排班班次
		 * 2.如果是排班管理员，角色编码为 pb 只可以查看管辖范围内排班科室的班次以及排班属性为全院的排班
		 * 
		 */
		Boolean isAdmin = UserInfoHolder.ISADMIN();
		Boolean isPbSettingAdmin = UserInfoHolder.getRight("SCHEDULER_SETTING_MANAGER"); //排班设置员
		Boolean YWGLY = UserInfoHolder.getRight("YWGLY"); //医务管理员
		Boolean superAdmin = UserInfoHolder.ISSUPERADMIN();//超级管理员
		Boolean isPb = UserInfoHolder.getRight("pb");//排班科室管理管理员
		
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		if(!isAdmin && !isPbSettingAdmin && !YWGLY && !superAdmin){
			record.setCreateUser(UserInfoHolder.getCurrentUserCode());
			if(isPb){
				//排班权限中，排班管理员字段有当前登录人的工号数据
				Example example = new Example(MedScheduleAuthority.class);
				Example.Criteria criteria = example.createCriteria();
				criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
				criteria.andCondition("find_in_set('" + UserInfoHolder.getCurrentUserCode() + "',manage_user_code)");
				criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
				List<MedScheduleAuthority> list = medScheduleAuthorityMapper.selectByExample(example);
				List<String> scheduleOrgList = new ArrayList<>();
				List<String> scheduleUserList = new ArrayList<>();//查询管辖范围人用于获取管辖范围的科室
				if(CollectionUtils.isNotEmpty(list)) {
					for (MedScheduleAuthority authority : list) {
						if(StringUtils.isNotBlank(authority.getScheduleOrg())) {
							for(String org : authority.getScheduleOrg().split(",")){
								if(!scheduleOrgList.contains(org)){
									scheduleOrgList.add(org);
								}
							}
						}
						if(StringUtils.isNotBlank(authority.getScheduleUser())) {
							for(String user : authority.getScheduleUser().split(",")){
								if(!scheduleUserList.contains(user)){
									scheduleUserList.add(user);
								}
							}
						}
					}
					//根据人员范围查询人员所属的机构编码
					if(scheduleUserList.size() > 0){
						PlatformResult<List<EmployeeResp>> resp = hrmsEmployeeFeignService.getEmployeeDetailByCodes(scheduleUserList);
						if(resp.isSuccess() && resp.getObject().size() > 0){
							for(EmployeeResp emp : resp.getObject()){
								String orgCode = emp.getOrgCode();
								if(!ObjectUtils.isEmpty(orgCode) && !scheduleOrgList.contains(orgCode)){
									scheduleOrgList.add(orgCode);
								}
							}
						}
					}
					//根据科室范围查询管辖的人员工号
					if(scheduleOrgList.size() > 0){
						List<EmployeeResp> userList = mapper.getEmpListByDeptCodes(scheduleOrgList, UserInfoHolder.getCurrentUserCorpCode());
						if(userList.size() > 0){
							for(EmployeeResp emp : userList){
								String employeeNo = emp.getEmployeeNo();
								if(!ObjectUtils.isEmpty(employeeNo) && !scheduleUserList.contains(employeeNo)){
									scheduleUserList.add(employeeNo);
								}
							}
						}
					}
				}
				record.setScheduleOrgList(scheduleOrgList);
				record.setScheduleUserList(scheduleUserList);
				record.setIsPbAdmin("1");
			} else {
				return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), new ArrayList<>());
			}
		}
		//查询被排班人有哪些班次-用于单元格给个人排班时使用
		String employeeId = record.getEmployeeId();
		if(!ObjectUtils.isEmpty(employeeId)){
			PlatformResult<EmployeeResp> resp = hrmsEmployeeFeignService.findByEmployeeId(employeeId);
			if(resp.isSuccess()){
				EmployeeResp employee = resp.getObject();
				MedScheduleClasses selectRecord = new MedScheduleClasses();
				selectRecord.setEmployeeNo(employee.getEmployeeNo());
				selectRecord.setOrgCode(employee.getOrgCode());
				selectRecord.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				List<MedScheduleClasses> list = mapper.getScheduleClassesByEmp(selectRecord);
				List<String> ids = new ArrayList<>();
				if(CollectionUtils.isNotEmpty(list)){
					for(MedScheduleClasses ca : list){
						ids.add(ca.getId());
					}
					record.setIds(ids);
				} else {
					return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), new ArrayList<>());
				}
			}
		}
		if(!ObjectUtils.isEmpty(record.getClassIds())){
			record.setClassIdList(Arrays.asList(record.getClassIds().split(",")));
		}
		List<MedScheduleClasses> records = mapper.getDataSetList(record, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<MedScheduleClasses> getScheduleClassesList(MedScheduleClasses record) {
		
		Boolean isadmin = UserInfoHolder.ISADMIN();
		Boolean isPbSettingAdmin = UserInfoHolder.getRight("SCHEDULER_SETTING_MANAGER"); //排班设置员
		Boolean superAdmin = UserInfoHolder.ISSUPERADMIN();//超级管理员
		Boolean YWGLY = UserInfoHolder.getRight("YWGLY"); //医务管理员
		Boolean isPb = UserInfoHolder.getRight("pb");//排班科室管理管理员
		/**
		 * 根据排班权限，查询当前登录人可以管理的人员，以及管理科室关联的员工列表的合集作为查询班次的人员范围条件
		 */
		
		if(StringUtils.isBlank(record.getIsAll()) && !isadmin && !YWGLY && !isPbSettingAdmin && !superAdmin) {
//			record.setClassesUseUser(UserInfoHolder.getCurrentUserCode());
//			record.setClassesUseOrg(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			if(isPb){
				//排班权限中，排班管理员字段有当前登录人的工号数据
				Example example = new Example(MedScheduleAuthority.class);
				Example.Criteria criteria = example.createCriteria();
				criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
				criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
				criteria.andCondition("find_in_set('" + UserInfoHolder.getCurrentUserCode() + "',manage_user_code)");
				List<MedScheduleAuthority> list = medScheduleAuthorityMapper.selectByExample(example);
				List<String> scheduleOrgList = new ArrayList<>();
				List<String> scheduleUserList = new ArrayList<>();//查询管辖范围人用于获取管辖范围的科室
				if(CollectionUtils.isNotEmpty(list)) {
					for (MedScheduleAuthority authority : list) {
						if(StringUtils.isNotBlank(authority.getScheduleOrg())) {
							for(String org : authority.getScheduleOrg().split(",")){
								if(!scheduleOrgList.contains(org)){
									scheduleOrgList.add(org);
								}
							}
						}
						if(StringUtils.isNotBlank(authority.getScheduleUser())) {
							for(String user : authority.getScheduleUser().split(",")){
								if(!scheduleUserList.contains(user)){
									scheduleUserList.add(user);
								}
							}
						}
					}
					//根据人员范围查询人员所属的机构编码
					if(scheduleUserList.size() > 0){
                        PlatformResult<List<EmployeeResp>> resp = hrmsEmployeeFeignService.getEmployeeDetailByCodes(scheduleUserList);
                        if(resp.isSuccess() && resp.getObject().size() > 0){
                               for(EmployeeResp emp : resp.getObject()){
                                     String orgCode = emp.getOrgCode();
                                     if(!ObjectUtils.isEmpty(orgCode) && !scheduleOrgList.contains(orgCode)){
                                            scheduleOrgList.add(orgCode);
                                     }
                               }
                        }
                    }
					//根据科室范围查询管辖的人员工号
					if(scheduleOrgList.size() > 0){
						List<EmployeeResp> userList = mapper.getEmpListByDeptCodes(scheduleOrgList, UserInfoHolder.getCurrentUserCorpCode());
						if(userList.size() > 0){
							for(EmployeeResp emp : userList){
								String employeeNo = emp.getEmployeeNo();
								if(!ObjectUtils.isEmpty(employeeNo) && !scheduleUserList.contains(employeeNo)){
									scheduleUserList.add(employeeNo);
								}
							}
						}
					}
				}
				record.setScheduleOrgList(scheduleOrgList);
				record.setScheduleUserList(scheduleUserList);
				record.setIsPbAdmin("1");
			} else {
				return new ArrayList<>();
			}
		} else {
			//查看所有的数据
			record.setIsAll("1");
		}
		//查询被排班人有哪些班次-用于单元格给个人排班时使用
		String employeeId = record.getEmployeeId();
		if(!ObjectUtils.isEmpty(employeeId)){
			PlatformResult<EmployeeResp> resp = hrmsEmployeeFeignService.findByEmployeeId(employeeId);
			if(resp.isSuccess()){
				EmployeeResp employee = resp.getObject();
				MedScheduleClasses selectRecord = new MedScheduleClasses();
				selectRecord.setEmployeeNo(employee.getEmployeeNo());
				selectRecord.setOrgCode(employee.getOrgCode());
				selectRecord.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				List<MedScheduleClasses> list = mapper.getScheduleClassesByEmp(selectRecord);
				List<String> ids = new ArrayList<>();
				if(CollectionUtils.isNotEmpty(list)){
					for(MedScheduleClasses ca : list){
						ids.add(ca.getId());
					}
					record.setIds(ids);
				} else {
					return new ArrayList<>();
				}
			}
		}
		record.setCreateUser(UserInfoHolder.getCurrentUserCode());
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.getScheduleClassesList(record);
	}

	@Override
	public int selectByTypeId(String typeId) {
		Example example = new Example(MedScheduleClasses.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("typeId", typeId);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		return mapper.selectCountByExample(example);
	}

	@Override
	public List<MedScheduleAuthority> selectUsageRange(String classesId) {
		//如果传了classesId，则按照班次ID查询班次创建人的使用范围返回数据，否则按照当前登录人的使用返回返回
		String currentUserCode = UserInfoHolder.getCurrentUserCode();
		if(!ObjectUtils.isEmpty(classesId)){
			MedScheduleClasses classes = mapper.selectByPrimaryKey(classesId);
			currentUserCode = classes.getCreateUser();
		}
		Example example = new Example(MedScheduleAuthority.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andCondition("find_in_set('" + currentUserCode + "',manage_user_code)");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		return medScheduleAuthorityMapper.selectByExample(example);
	}

	@Override
	public List<MedScheduleClassesValidateCopyRes> validateCopy(MedScheduleClassesValidateCopyVo record) {
		if(ObjectUtils.isEmpty(record.getEmployeeId())){
			return null;
		}
		List<MedScheduleClassesValidateCopyRes> result = new ArrayList<>();
		MedScheduleClasses entity = new MedScheduleClasses();
		entity.setEmployeeId(record.getEmployeeId());
		List<MedScheduleClasses> classList = getScheduleClassesList(entity);
		if(CollUtil.isNotEmpty(classList) && CollUtil.isNotEmpty(record.getScheduleClassesIdList())){
			List<String> classIdList = classList.stream().map(MedScheduleClasses::getId).collect(Collectors.toList());
			for(String id : record.getScheduleClassesIdList()){
				MedScheduleClassesValidateCopyRes re = new MedScheduleClassesValidateCopyRes();
				re.setScheduleClassesId(id);
				if(classIdList.contains(id)){
					re.setValidate(true);
				} else {
					re.setValidate(false);
				}
				result.add(re);
			}
		} else if(CollUtil.isNotEmpty(record.getScheduleClassesIdList())){
			for(String id : record.getScheduleClassesIdList()){
				MedScheduleClassesValidateCopyRes re = new MedScheduleClassesValidateCopyRes();
				re.setScheduleClassesId(id);
				re.setValidate(false);
				result.add(re);
			}
		}
		return result;
	}
	
	
}

package cn.trasen.hrms.med.integral.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.hrms.med.integral.dao.MedIntegralRecordMapper;
import cn.trasen.hrms.med.integral.model.MedIntegralRecord;
import cn.trasen.hrms.med.integral.service.MedIntegralRecordService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedIntegralRecordServiceImpl
 * @Description TODO
 * @date 2024��11��12�� ����11:14:05
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedIntegralRecordServiceImpl implements MedIntegralRecordService {

	@Autowired
	private MedIntegralRecordMapper mapper;
	
	@Autowired
	private HrmsOrganizationFeignService hrmsOrganizationFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedIntegralRecord record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		
		record.setStatus("0");	
		record.setIntegralTitle(DateUtil.format(record.getCreateDate(),"yyyyMMddHHssmm"));
		
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedIntegralRecord record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}
	
	@Override
	@Transactional(readOnly = false)
	public void examine(List<MedIntegralRecord> record) {
		
		for (MedIntegralRecord medIntegralRecord : record) {
			medIntegralRecord.setUpdateDate(new Date());
			medIntegralRecord.setExamineDate(new Date());
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				medIntegralRecord.setUpdateUser(user.getUsercode());
				medIntegralRecord.setUpdateUserName(user.getUsername());
				medIntegralRecord.setExamineUser(user.getUsercode());
				medIntegralRecord.setExamineUserName(user.getUsername());
			}
			
			mapper.updateByPrimaryKeySelective(medIntegralRecord);
		}
		
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedIntegralRecord record = new MedIntegralRecord();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedIntegralRecord selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedIntegralRecord> getDataSetList(Page page, MedIntegralRecord record) {
		PlatformResult<List<String>> hrmsOrganizationAndNextList = hrmsOrganizationFeignService.getHrmsOrganizationAndNextList(record.getIntegralOrg());
		if(hrmsOrganizationAndNextList.isSuccess()){
			record.setOrgIdList(hrmsOrganizationAndNextList.getObject());
		}
		List<MedIntegralRecord> records = mapper.getDataSetList(record, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<MedIntegralRecord> selectByUseCode(String currentUserCode) {
		Example example = new Example(MedIntegralRecord.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("integralUser", UserInfoHolder.getCurrentUserCode());
		criteria.andEqualTo("status", "1");
		return mapper.selectByExample(example);
	}
	
	
	
}

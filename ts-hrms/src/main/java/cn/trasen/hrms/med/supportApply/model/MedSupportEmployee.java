package cn.trasen.hrms.med.supportApply.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "med_support_employee")
@Setter
@Getter
public class MedSupportEmployee {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 申请id
     */
    @Column(name = "apply_id")
    @ApiModelProperty(value = "申请id")
    private String applyId;

    /**
     * 支援人员code
     */
    @Column(name = "support_user")
    @ApiModelProperty(value = "支援人员code")
    private String supportUser;

    /**
     * 支援人员名称
     */
    @Column(name = "support_user_name")
    @ApiModelProperty(value = "支援人员名称")
    private String supportUserName;

    /**
     * 支援开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "support_start_date")
    @ApiModelProperty(value = "支援开始时间")
    private Date supportStartDate;

    /**
     * 支援结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "support_end_date")
    @ApiModelProperty(value = "支援结束时间")
    private Date supportEndDate;
}
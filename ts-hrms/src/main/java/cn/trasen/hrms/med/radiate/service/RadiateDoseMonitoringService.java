package cn.trasen.hrms.med.radiate.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.radiate.model.RadiateDoseMonitoring;
import cn.trasen.hrms.med.radiate.model.RadiateMonitor;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;
import cn.trasen.hrms.med.radiate.vo.RadiateDoseReqVo;

import java.util.List;

/**
 * @ClassName RadiateDoseMonitoringService
 * @Description TODO
 * @date 2025��1��8�� ����11:15:27
 * <AUTHOR>
 * @version 1.0
 */
public interface RadiateDoseMonitoringService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��1��8�� ����11:15:27
	 * <AUTHOR>
	 */
	Integer save(RadiateDoseMonitoring record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��1��8�� ����11:15:27
	 * <AUTHOR>
	 */
	Integer update(RadiateDoseMonitoring record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��1��8�� ����11:15:27
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return RadiateDoseMonitoring
	 * @date 2025��1��8�� ����11:15:27
	 * <AUTHOR>
	 */
	RadiateDoseMonitoring selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<RadiateDoseMonitoring>
	 * @date 2025��1��8�� ����11:15:27
	 * <AUTHOR>
	 */
	DataSet<RadiateDoseMonitoring> getDataSetList(Page page, RadiateDoseMonitoring record);
	
	DataSet<RadiateDoseMonitoring> selectRadiateDoseMonitoringPageList(Page page, RadiateDoseReqVo record);

	/**
	 * @Title saveOrUpdateList
	 * @Description 批量信息
	 * @param records
	 * @param monitors 放射监测设置
	 * @param isAdd 新增标识
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	Integer saveOrUpdateList(List<RadiateDoseMonitoring> records, List<RadiateMonitor> monitors, boolean isAdd, RadiatePersonnelRegister personnel);

	/**
	 * @Title selectByEmployeeId
	 * @Description 根据员工ID查询
	 * @param employeeId
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	List<RadiateDoseMonitoring> selectByEmployeeId(String employeeId);
	List<RadiateDoseMonitoring> getList();
	
	/**
	 * @Title deleteByEmployeeId
	 * @Description 根据员工ID删除
	 * @param employeeId
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	Integer deleteByEmployeeId(String employeeId);

	/**
	 * 导入数据
	 * 
	 * @param list
	 * @return
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	PlatformResult importData(List<RadiateDoseMonitoring> list);
}

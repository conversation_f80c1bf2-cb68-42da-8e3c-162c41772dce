package cn.trasen.hrms.med.schedule.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.schedule.model.MedScheduleRecord;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;

/**
 * @ClassName MedScheduleRecordService
 * @Description TODO
 * @date 2025��3��29�� ����2:58:59
 * <AUTHOR>
 * @version 1.0
 */
public interface MedScheduleRecordService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��3��29�� ����2:58:59
	 * <AUTHOR>
	 */
	Integer save(MedScheduleRecord record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��3��29�� ����2:58:59
	 * <AUTHOR>
	 */
	Integer update(MedScheduleRecord record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��3��29�� ����2:58:59
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedScheduleRecord
	 * @date 2025��3��29�� ����2:58:59
	 * <AUTHOR>
	 */
	MedScheduleRecord selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleRecord>
	 * @date 2025��3��29�� ����2:58:59
	 * <AUTHOR>
	 */
	DataSet<MedScheduleRecord> getDataSetList(Page page, MedScheduleRecord record);

	int selectCountByTypeId(String typeId);

	int selectCountByClassesId(String classesId);

	List<MedScheduleRecord> selectMedScheduleRecordList(ScheduleEmployee record);

	/**
	 * 批量保存排班
	 * @param record
	 * @param saveTemplateRecord 是否保存模板排班数据
	 */
	void batchInsert(ScheduleEmployee record, boolean saveTemplateRecord);
	
	void saveMedScheduleTemplateRecord(ScheduleEmployee record);

	List<Map<String, Object>> selectExportScheduleRecord(ScheduleEmployee record);

	void copyScheduleRecord(ScheduleEmployee record);

	void clearScheduleRecord(ScheduleEmployee record);

	void updateGroupId(String groupId, List<String> updateRecordIds);

	void deleteByGroupId(String groupId);

	List<Map<String,Object>> statisticsScheduleRecordM(ScheduleEmployee record);

	List<Map<String,Object>> finishRateScheduleRecordM(ScheduleEmployee record);

	List<Map<String,String>> unfinishScheduleDareM(ScheduleEmployee record);

	List<MedScheduleRecord> getPersonMedScheduleRecord(ScheduleEmployee record);

	/**
	 * @Description 根据过期删除人员类型为 学生  的排班，并修改人员状态为离职
	 * @date 2025-06-28 9:30:00
	 * <AUTHOR>
	 */
	void deleteScheduleByOutofdate();

}

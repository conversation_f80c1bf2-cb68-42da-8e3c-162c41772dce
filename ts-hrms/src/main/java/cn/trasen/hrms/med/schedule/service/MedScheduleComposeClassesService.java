package cn.trasen.hrms.med.schedule.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.schedule.model.MedScheduleComposeClasses;

/**
 * @ClassName MedScheduleComposeClassesService
 * @Description TODO
 * @date 2025��4��29�� ����3:19:48
 * <AUTHOR>
 * @version 1.0
 */
public interface MedScheduleComposeClassesService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��4��29�� ����3:19:48
	 * <AUTHOR>
	 */
	Integer save(MedScheduleComposeClasses record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��4��29�� ����3:19:48
	 * <AUTHOR>
	 */
	Integer update(MedScheduleComposeClasses record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��4��29�� ����3:19:48
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedScheduleComposeClasses
	 * @date 2025��4��29�� ����3:19:48
	 * <AUTHOR>
	 */
	MedScheduleComposeClasses selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleComposeClasses>
	 * @date 2025��4��29�� ����3:19:48
	 * <AUTHOR>
	 */
	DataSet<MedScheduleComposeClasses> getDataSetList(Page page, MedScheduleComposeClasses record);
}

package cn.trasen.hrms.med.radiate.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 放射监控
 * @date 2025-05-12 11:52:53
 * <AUTHOR>
 * @version 1.0
 */
@Table(name = "med_radiate_monitor")
@Setter
@Getter
public class RadiateMonitor {

	@Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    @Column(name = "employee_id")
    @ApiModelProperty(value = "人员id")
    private String employeeId;

    @Column(name = "register_category")
    @ApiModelProperty(value = "登记类别")
    private String registerCategory;

    @Column(name = "register_type")
    @ApiModelProperty(value = "登记类型")
    private String registerType;

    @Column(name = "monitor_cycle")
    @ApiModelProperty(value = "监测周期：1-1年，2-2年，5-5年")
    private String monitorCycle;

    @Column(name = "monitor_times")
    @ApiModelProperty(value = "监测次数")
    private Integer monitorTimes;

    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标记")
    private String isDeleted;
    
    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    @Transient
    @ApiModelProperty(value = "登记类别文本")
    private String registerCategoryText;

    @Transient
    @ApiModelProperty(value = "登记类型文本")
    private String registerTypeText;
    
    @Transient
    @ApiModelProperty(value = "监测周期文本")
    private String monitorCycleText;

}

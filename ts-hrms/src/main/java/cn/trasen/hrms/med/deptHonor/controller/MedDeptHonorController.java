package cn.trasen.hrms.med.deptHonor.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.deptHonor.model.MedDeptHonor;
import cn.trasen.hrms.med.deptHonor.service.MedDeptHonorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedDeptHonorController
 * @Description TODO
 * @date 2025��1��3�� ����3:27:47
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "科室荣誉管理")
public class MedDeptHonorController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedDeptHonorController.class);

	@Autowired
	private MedDeptHonorService medDeptHonorService;

	/**
	 * @Title saveMedDeptHonor
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��3�� ����3:27:47
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/deptHonor/save")
	public PlatformResult<String> saveMedDeptHonor(@RequestBody MedDeptHonor record) {
		try {
			medDeptHonorService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedDeptHonor
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��3�� ����3:27:47
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/deptHonor/update")
	public PlatformResult<String> updateMedDeptHonor(@RequestBody MedDeptHonor record) {
		try {
			medDeptHonorService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedDeptHonorById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedDeptHonor>
	 * @date 2025��1��3�� ����3:27:47
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/deptHonor/{id}")
	public PlatformResult<MedDeptHonor> selectMedDeptHonorById(@PathVariable String id) {
		try {
			MedDeptHonor record = medDeptHonorService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedDeptHonorById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��1��3�� ����3:27:47
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/deptHonor/delete/{id}")
	public PlatformResult<String> deleteMedDeptHonorById(@PathVariable String id) {
		try {
			medDeptHonorService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedDeptHonorList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedDeptHonor>
	 * @date 2025��1��3�� ����3:27:47
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/deptHonor/list")
	public DataSet<MedDeptHonor> selectMedDeptHonorList(Page page, MedDeptHonor record) {
		return medDeptHonorService.getDataSetList(page, record);
	}
}

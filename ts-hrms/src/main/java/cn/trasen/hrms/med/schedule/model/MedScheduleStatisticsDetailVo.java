package cn.trasen.hrms.med.schedule.model;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 排班统计查看
 * <AUTHOR>
 *
 */
@Setter
@Getter
public class MedScheduleStatisticsDetailVo {

    @ApiModelProperty(value = "员工ID")
	private String employeeId;

    @ApiModelProperty(value = "员工工号")
	private String employeeNo;

    @ApiModelProperty(value = "员工姓名")
	private String employeeName;

    @ApiModelProperty(value = "所属院区编码")
	private String hospCode;

    @ApiModelProperty(value = "所属院区名称")
	private String hospName;

    @ApiModelProperty(value = "所属科室ID")
	private String orgId;

    @ApiModelProperty(value = "所属科室名称")
	private String orgName;
	
	@ApiModelProperty(value = "人员类型-数据字典ORG_ATTRIBUTES")
	private String orgAttributes;
	
	@ApiModelProperty(value = "技术职称")
	private String technical;
	
	@ApiModelProperty(value = "联系方式")
	private String phoneNumber;
	
	@ApiModelProperty(value = "分组名称")
	private String groupName;
	
	@ApiModelProperty(value = "排班集合")
	private List<MedScheduleRecord> scheduleRecords;
	
	@ApiModelProperty(value = "oa信息集合")
	private List<LeaveRecordVo> oaRecords;
	
	@ApiModelProperty(value = "科室查看人数统计")
	private Integer userCount;


}

package cn.trasen.hrms.med.supportApply.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.hrms.med.supportApply.model.MedSupportEmployee;
import tk.mybatis.mapper.common.Mapper;

public interface MedSupportEmployeeMapper extends Mapper<MedSupportEmployee> {

	List<MedSupportEmployee> selectByWorkflowId(@Param("workflowId") String workflowId);

	List<String> selectSupportData(@Param("ssoOrgCode")String ssoOrgCode);

	List<String> selectSupportData();

	Long selectMySupportSize(@Param("currentUserCode")String currentUserCode);

}
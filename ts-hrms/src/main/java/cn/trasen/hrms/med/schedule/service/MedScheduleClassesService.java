package cn.trasen.hrms.med.schedule.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.schedule.model.MedScheduleAuthority;
import cn.trasen.hrms.med.schedule.model.MedScheduleClasses;
import cn.trasen.hrms.med.schedule.model.MedScheduleClassesValidateCopyRes;
import cn.trasen.hrms.med.schedule.model.MedScheduleClassesValidateCopyVo;

/**
 * @ClassName MedScheduleClassesService
 * @Description TODO
 * @date 2025��3��29�� ����2:58:06
 * <AUTHOR>
 * @version 1.0
 */
public interface MedScheduleClassesService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��3��29�� ����2:58:06
	 * <AUTHOR>
	 */
	Integer save(MedScheduleClasses record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��3��29�� ����2:58:06
	 * <AUTHOR>
	 */
	Integer update(MedScheduleClasses record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��3��29�� ����2:58:06
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedScheduleClasses
	 * @date 2025��3��29�� ����2:58:06
	 * <AUTHOR>
	 */
	MedScheduleClasses selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleClasses>
	 * @date 2025��3��29�� ����2:58:06
	 * <AUTHOR>
	 */
	DataSet<MedScheduleClasses> getDataSetList(Page page, MedScheduleClasses record);

	List<MedScheduleClasses> getScheduleClassesList(MedScheduleClasses record);

	int selectByTypeId(String typeId);

	void disableOrEnable(MedScheduleClasses record);
	
	/**
	 * @Title selectUsageRange
	 * @Description 班次使用范围列表
	 * @param classesId
	 * @return List<MedScheduleAuthority>
	 * @date 2025-07-09 12:58:06
	 * <AUTHOR>
	 */
	List<MedScheduleAuthority> selectUsageRange(String classesId);
	
	/**
	 * @Title validateCopy
	 * @Description 校验班次是否能够复制
	 * @param record
	 * @return List<MedScheduleClassesValidateCopyRes>
	 * @date 2025-07-11 14:58:06
	 * <AUTHOR>
	 */
	List<MedScheduleClassesValidateCopyRes> validateCopy(MedScheduleClassesValidateCopyVo record);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.schedule.dao.MedScheduleGroupUserMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.schedule.model.MedScheduleGroupUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="emp_sort" jdbcType="INTEGER" property="empSort" />
  </resultMap>
  
  <select id="selectMyGroupUser" resultType="String" parameterType="String">
  		select t1.employee_id from med_schedule_group_user t1
		LEFT JOIN med_schedule_group t2 ON t1.group_id = t2.id
		WHERE is_deleted = 'N' AND t2.create_user = #{currentUserCode} 
		<if test="groupId != null and groupId != ''">
			and t1.group_id != #{groupId}
		</if>
  </select>
</mapper>
package cn.trasen.hrms.med.workplan.model;

import io.swagger.annotations.*;
import javax.persistence.*;

import org.jeecgframework.poi.excel.annotation.Excel;

import lombok.*;

@Table(name = "med_workplan_player")
@Setter
@Getter
public class MedWorkplanPlayer {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 工作计划id
     */
    @Column(name = "workplan_id")
    @ApiModelProperty(value = "工作计划id")
    private String workplanId;

    /**
     * 参与人工号
     */
    @Excel(name = "工号")
    @Column(name = "player_user")
    @ApiModelProperty(value = "参与人工号")
    private String playerUser;

    /**
     * 参与人姓名
     */
    @Excel(name = "姓名")
    @Column(name = "player_user_name")
    @ApiModelProperty(value = "参与人姓名")
    private String playerUserName;

    /**
     * 参与人岗位
     */
    @Excel(name = "岗位名称")
    @Column(name = "player_identity")
    @ApiModelProperty(value = "参与人岗位")
    private String playerIdentity;

    /**
     * 参与人性别
     */
    @Excel(name = "性别")
    @Column(name = "player_gender")
    @ApiModelProperty(value = "参与人性别")
    private String playerGender;

    /**
     * 参与人科室
     */
    @Excel(name = "所属科室")
    @Column(name = "player_org")
    @ApiModelProperty(value = "参与人科室")
    private String playerOrg;
}
package cn.trasen.hrms.med.patient.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.patient.model.PatientBirth;
import cn.trasen.hrms.med.patient.service.PatientBirthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName PatientBirthController
 * @Description TODO
 * @date 2025��4��8�� ����4:48:25
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "PatientBirthController")
public class PatientBirthController {

	private transient static final Logger logger = LoggerFactory.getLogger(PatientBirthController.class);

	@Autowired
	private PatientBirthService patientBirthService;

	/**
	 * @Title savePatientBirth
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/patientBirth/save")
	public PlatformResult<String> savePatientBirth(@RequestBody PatientBirth record) {
		try {
			patientBirthService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updatePatientBirth
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/patientBirth/update")
	public PlatformResult<String> updatePatientBirth(@RequestBody PatientBirth record) {
		try {
			patientBirthService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectPatientBirthById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<PatientBirth>
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/patientBirth/{id}")
	public PlatformResult<PatientBirth> selectPatientBirthById(@PathVariable String id) {
		try {
			PatientBirth record = patientBirthService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deletePatientBirthById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/patientBirth/delete/{id}")
	public PlatformResult<String> deletePatientBirthById(@PathVariable String id) {
		try {
			patientBirthService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectPatientBirthList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<PatientBirth>
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/patientBirth/list")
	public DataSet<PatientBirth> selectPatientBirthList(Page page, PatientBirth record) {
		return patientBirthService.getDataSetList(page, record);
	}
}

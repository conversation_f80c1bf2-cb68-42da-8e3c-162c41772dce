package cn.trasen.hrms.med.schedule.controller;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.date.DateUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.schedule.model.MedScheduleAuthority;
import cn.trasen.hrms.med.schedule.model.MedScheduleClasses;
import cn.trasen.hrms.med.schedule.model.MedScheduleClassesValidateCopyRes;
import cn.trasen.hrms.med.schedule.model.MedScheduleClassesValidateCopyVo;
import cn.trasen.hrms.med.schedule.service.MedScheduleClassesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * @ClassName MedScheduleClassesController
 * @Description TODO
 * @date 2025��3��29�� ����2:58:06
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "排班班次设置-新")
public class MedScheduleClassesController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedScheduleClassesController.class);

	@Autowired
	private MedScheduleClassesService medScheduleClassesService;

	/**
	 * @Title saveMedScheduleClasses
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��29�� ����2:58:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/scheduleClasses/save")
	public PlatformResult<String> saveMedScheduleClasses(@RequestBody MedScheduleClasses record) {
		try {
			medScheduleClassesService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedScheduleClasses
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��29�� ����2:58:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/scheduleClasses/update")
	public PlatformResult<String> updateMedScheduleClasses(@RequestBody MedScheduleClasses record) {
		try {
			medScheduleClassesService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "启用禁用", notes = "启用禁用")
	@PostMapping("/api/scheduleClasses/disableOrEnable")
	public PlatformResult<String> disableOrEnable(@RequestBody MedScheduleClasses record) {
		try {
			medScheduleClassesService.disableOrEnable(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}


	/**
	 * 
	 * @Title selectMedScheduleClassesById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedScheduleClasses>
	 * @date 2025��3��29�� ����2:58:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/scheduleClasses/{id}")
	public PlatformResult<MedScheduleClasses> selectMedScheduleClassesById(@PathVariable String id) {
		try {
			MedScheduleClasses record = medScheduleClassesService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedScheduleClassesById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��3��29�� ����2:58:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/scheduleClasses/delete/{id}")
	public PlatformResult<String> deleteMedScheduleClassesById(@PathVariable String id) {
		try {
			medScheduleClassesService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedScheduleClassesList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleClasses>
	 * @date 2025��3��29�� ����2:58:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/scheduleClasses/list")
	public DataSet<MedScheduleClasses> selectMedScheduleClassesList(Page page, MedScheduleClasses record) {
		return medScheduleClassesService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "查询所有排班班次类型列表", notes = "查询所有排班班次类型列表")
    @PostMapping("/api/scheduleClasses/getScheduleClassesList")
    public PlatformResult<List<MedScheduleClasses>> getScheduleClassesList(@RequestBody MedScheduleClasses record) {
        try {
            return PlatformResult.success(medScheduleClassesService.getScheduleClassesList(record));
        } catch (Exception e) {
        	e.printStackTrace();
        	return PlatformResult.failure(e.getMessage());
        }
    }

	/**
	 * @Description 班次导出
	 * @param page
	 * @param record
	 * @date 2025-06-25
	 * <AUTHOR>
	 */
	@ApiOperation(value = "班次导出", notes = "班次导出")
	@GetMapping(value = "/api/scheduleClasses/exportScheduleClasses")
    public void export(HttpServletRequest request, HttpServletResponse response, Page page, MedScheduleClasses record) {
		page.setPageNo(1);
		page.setPageSize(Integer.MAX_VALUE);
		String name = "班次设置信息表" + DateUtil.format(new Date(),"yyyyMMdd") + ".xls";
		String templateUrl = "template/scheduleClassesExport.xls";
		try {
			DataSet<MedScheduleClasses> dataSetList = medScheduleClassesService.getDataSetList(page, record);
			List<MedScheduleClasses> list = dataSetList.getRows();
            if (CollectionUtils.isNotEmpty(list)) {
            	for(MedScheduleClasses c : list){
            		//状态 0禁用  1启用
            		if(!ObjectUtils.isEmpty(c.getClassesStatus()) && "1".equals(c.getClassesStatus())){
            			c.setClassesStatus("启用");
            		} else {
            			c.setClassesStatus("禁用");
            		}
            		//班次属性 1全院  2科室
            		if(!ObjectUtils.isEmpty(c.getClassAttributes()) && "1".equals(c.getClassAttributes())){
            			c.setClassAttributes("全院");
            		} else {
            			c.setClassAttributes("科室");
            		}
            	}
				ExportUtil.export(request, response, list, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

    }

	/**
	 * @Title selectMedScheduleClassesList
	 * @Description 班次使用范围列表
	 * @param classesId
	 * @return PlatformResult<List<MedScheduleAuthority>>
	 * @date 2025-07-09 12:58:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "班次使用范围列表", notes = "班次使用范围列表")
	@GetMapping("/api/scheduleClasses/selectUsageRange")
	public PlatformResult<List<MedScheduleAuthority>> selectUsageRange(@RequestParam(value = "classesId", required = false )@ApiParam(value = "班次ID")String classesId) {
		try {
            return PlatformResult.success(medScheduleClassesService.selectUsageRange(classesId));
        } catch (Exception e) {
        	e.printStackTrace();
        	return PlatformResult.failure(e.getMessage());
        }
	}

	/**
	 * @Title validateCopy
	 * @Description 校验班次是否能够复制
	 * @param classesId
	 * @return PlatformResult<List<MedScheduleClassesValidateCopyRes>>
	 * @date 2025-07-09 12:58:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "校验班次是否能够复制", notes = "校验班次是否能够复制")
	@PostMapping("/api/scheduleClasses/validateCopy")
	public PlatformResult<List<MedScheduleClassesValidateCopyRes>> validateCopy(@RequestBody MedScheduleClassesValidateCopyVo record) {
		try {
            return PlatformResult.success(medScheduleClassesService.validateCopy(record));
        } catch (Exception e) {
        	e.printStackTrace();
        	return PlatformResult.failure(e.getMessage());
        }
	}
}

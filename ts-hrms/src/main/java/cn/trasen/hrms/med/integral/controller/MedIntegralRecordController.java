package cn.trasen.hrms.med.integral.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.integral.model.MedIntegralRecord;
import cn.trasen.hrms.med.integral.service.MedIntegralRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedIntegralRecordController
 * @Description TODO
 * @date 2024��11��12�� ����11:14:05
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "积分奖惩登记")
public class MedIntegralRecordController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedIntegralRecordController.class);

	@Autowired
	private MedIntegralRecordService medIntegralRecordService;

	/**
	 * @Title saveMedIntegralRecord
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��11��12�� ����11:14:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/integralRecord/save")
	public PlatformResult<String> saveMedIntegralRecord(@RequestBody MedIntegralRecord record) {
		try {
			medIntegralRecordService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedIntegralRecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��11��12�� ����11:14:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/integralRecord/update")
	public PlatformResult<String> updateMedIntegralRecord(@RequestBody MedIntegralRecord record) {
		try {
			medIntegralRecordService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "审核", notes = "审核")
	@PostMapping("/api/integralRecord/examine")
	public PlatformResult<String> examine(@RequestBody List<MedIntegralRecord> record) {
		try {
			medIntegralRecordService.examine(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedIntegralRecordById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedIntegralRecord>
	 * @date 2024��11��12�� ����11:14:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/integralRecord/{id}")
	public PlatformResult<MedIntegralRecord> selectMedIntegralRecordById(@PathVariable String id) {
		try {
			MedIntegralRecord record = medIntegralRecordService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedIntegralRecordById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��11��12�� ����11:14:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/integralRecord/delete/{id}")
	public PlatformResult<String> deleteMedIntegralRecordById(@PathVariable String id) {
		try {
			medIntegralRecordService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedIntegralRecordList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedIntegralRecord>
	 * @date 2024��11��12�� ����11:14:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/integralRecord/list")
	public DataSet<MedIntegralRecord> selectMedIntegralRecordList(Page page, MedIntegralRecord record) {
		return medIntegralRecordService.getDataSetList(page, record);
	}
	
//	@GetMapping(value = "/api/integralRecord/downloadTemplate")
//    @ApiOperation(value = "下载导入模板", notes = "下载导入模板")
//    public void downloadTemplate(HttpServletResponse response) {
//        try {
//            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
//            String filename = "积分奖惩登记模板.xlsx";
//            String template = "template/importIntegralRecord.xlsx";
//            ClassPathResource resource = new ClassPathResource(template);
//            exportExcelUtil.downloadExportExcel(filename, response, resource);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
	
	/**
	 * 
	 * @param page
	 * @param request
	 * @param response
	 * @param hrmsPersonnelIncident
	 */
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/integralRecord/export")
    public void export(HttpServletRequest request, HttpServletResponse response,Page page,MedIntegralRecord record) {
		
		page.setPageSize(Integer.MAX_VALUE);

		String name = "积分奖惩登记.xls";
		
		String templateUrl = "template/integralRecord.xls";
		
		try {
			DataSet<MedIntegralRecord> dataSetList = medIntegralRecordService.getDataSetList(page, record);
            
            List<MedIntegralRecord> list = dataSetList.getRows();
            
            ExportUtil.export(request, response, list, name, templateUrl);
            
		} catch (Exception e) {
			e.printStackTrace();
		}

    }
}

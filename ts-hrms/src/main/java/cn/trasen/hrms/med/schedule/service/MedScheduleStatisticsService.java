package cn.trasen.hrms.med.schedule.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.med.schedule.model.MedCustomStatisticsExport;
import cn.trasen.hrms.med.schedule.model.MedCustomStatisticsTitle;
import cn.trasen.hrms.med.schedule.model.MedScheduleStatisticsDetailVo;
import cn.trasen.hrms.med.schedule.model.MedScheduleStatisticsReq;
import cn.trasen.hrms.med.schedule.model.MedScheduleStatisticsTitleVo;

/**
 * @ClassName MedScheduleStatisticsService
 * @Description 排班统计服务类
 * @date 2025-08-13 16:48:40
 * <AUTHOR>
 * @version 1.0
 */
public interface MedScheduleStatisticsService {
	
	/**
	 * 排班统计列表
	 * @param page
	 * @param record
	 * @return
	 * @date 2025-08-13 16:48:40
	 * <AUTHOR>
	 */
	List<Map<String, Object>> statisticsTableDataList(Page page, MedScheduleStatisticsReq record);
	
	/**
	 * 排班按科室统计合计
	 * @param record
	 * @return
	 */
	Map<String, Object> statisticsTotalTableDataList(MedScheduleStatisticsReq record);
	
	/**
	 * 获取统计动态表头
	 * @param record
	 * @return
	 * @date 2025-08-13 16:48:40
	 * <AUTHOR>
	 */
	List<MedScheduleStatisticsTitleVo> getStatisticsTitleList(MedScheduleStatisticsReq record);
	
	/**
	 * 保存自定义班次-用于显示表头
	 * @param type
	 * @return
	 * @date 2025-08-13 16:48:40
	 * <AUTHOR>
	 */
	void saveCustomStatisticsTitle(List<MedCustomStatisticsTitle> records);
	
	/**
	 * 查询自定义班次-用于显示表头
	 * @param type
	 * @return
	 * @date 2025-08-13 16:48:40
	 * <AUTHOR>
	 */
	List<MedCustomStatisticsTitle> selectCustomStatisticsTitle();
	
	/**
	 * 查询统计明细
	 * @param record
	 * @return
	 * @date 2025-08-18 16:48:40
	 * <AUTHOR>
	 */
	MedScheduleStatisticsDetailVo selectDetail(MedScheduleStatisticsReq record);
	
	/**
	 * 保存自定义导出
	 * @param records
	 * @return
	 * @date 2025-08-20 16:48:40
	 * <AUTHOR>
	 */
	void saveMedCustomStatisticsExport(List<MedCustomStatisticsExport> records);
	
	/**
	 * 查询自定义导出
	 * @param type
	 * @return List<MedCustomStatisticsExport>
	 * @date 2025-08-20 16:48:40
	 * <AUTHOR>
	 */
	List<MedCustomStatisticsExport> selectMedCustomStatisticsExport(String type);
	
	/**
	 * 获取OA类型数据
	 * @return
	 */
	List<MedScheduleStatisticsTitleVo> selectAaTypeList();
	
}

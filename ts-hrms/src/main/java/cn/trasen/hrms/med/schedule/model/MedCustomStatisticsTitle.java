package cn.trasen.hrms.med.schedule.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 自定义统计表头
 *
 */
@Table(name = "med_custom_statistics_title")
@Setter
@Getter
public class MedCustomStatisticsTitle {
	
    @Id
    @ApiModelProperty(value = "主键")
    private String id;
    
    @Column(name = "classes_id")
    @ApiModelProperty(value = "班次ID")
    private String classesId;
    
    @Column(name = "classes_name")
    @ApiModelProperty(value = "班次名称")
    private String classesName; 
    
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

}

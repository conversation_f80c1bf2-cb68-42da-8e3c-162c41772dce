package cn.trasen.hrms.med.radiate.controller;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.core.date.DateUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.radiate.model.RadiateCheckupRegister;
import cn.trasen.hrms.med.radiate.service.RadiateCheckupRegisterService;
import cn.trasen.hrms.med.radiate.vo.RadiateCheckupReqVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName RadiateCheckupRegisterController
 * @Description TODO
 * @date 2025��1��8�� ����11:03:28体检状况登记
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "放射体检状况登记")
public class RadiateCheckupRegisterController {

	private transient static final Logger logger = LoggerFactory.getLogger(RadiateCheckupRegisterController.class);

	@Autowired
	private RadiateCheckupRegisterService radiateCheckupRegisterService;

	/**
	 * @Title saveRadiateCheckupRegister
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:03:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/RadiateCheckupRegister/save")
	public PlatformResult<String> saveRadiateCheckupRegister(@RequestBody RadiateCheckupRegister record) {
		try {
			radiateCheckupRegisterService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateRadiateCheckupRegister
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:03:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/RadiateCheckupRegister/update")
	public PlatformResult<String> updateRadiateCheckupRegister(@RequestBody RadiateCheckupRegister record) {
		try {
			radiateCheckupRegisterService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectRadiateCheckupRegisterById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<RadiateCheckupRegister>
	 * @date 2025��1��8�� ����11:03:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/RadiateCheckupRegister/{id}")
	public PlatformResult<RadiateCheckupRegister> selectRadiateCheckupRegisterById(@PathVariable String id) {
		try {
			RadiateCheckupRegister record = radiateCheckupRegisterService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteRadiateCheckupRegisterById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��1��8�� ����11:03:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/RadiateCheckupRegister/delete/{id}")
	public PlatformResult<String> deleteRadiateCheckupRegisterById(@PathVariable String id) {
		try {
			radiateCheckupRegisterService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectRadiateCheckupRegisterList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RadiateCheckupRegister>
	 * @date 2025��1��8�� ����11:03:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/RadiateCheckupRegister/list")
	public DataSet<RadiateCheckupRegister> selectRadiateCheckupRegisterList(Page page, RadiateCheckupRegister record) {
		return radiateCheckupRegisterService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectRadiateCheckupRegisterPageList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RadiateCheckupRegister>
	 * @date 2025��1��8�� ����11:03:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/RadiateCheckupRegister/PageList")
	public DataSet<RadiateCheckupRegister> selectRadiateCheckupRegisterPageList(Page page, RadiateCheckupReqVo record) {
		String orgIds = record.getOrgIds();
		if(!ObjectUtils.isEmpty(orgIds)){
			record.setOrgIdList(Arrays.asList(orgIds.split(",")));
		}
		return radiateCheckupRegisterService.selectRadiateCheckupRegisterPageList(page, record);
	}
	
	/**
	 * 
	 * @param request
	 * @param response
	 * @param page
	 * @param record
	 */
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/RadiateCheckupRegister/export")
    public void export(HttpServletRequest request, HttpServletResponse response, Page page, RadiateCheckupReqVo record) {
		page.setPageNo(1);
		page.setPageSize(Integer.MAX_VALUE);
		String orgIds = record.getOrgIds();
		if(!ObjectUtils.isEmpty(orgIds)){
			record.setOrgIdList(Arrays.asList(orgIds.split(",")));
		}
		String name = "体检状况信息表" + DateUtil.format(new Date(),"yyyyMMdd") + ".xls";
		String templateUrl = "template/radiate/checkupRegisterExport.xls";
		try {
			DataSet<RadiateCheckupRegister> dataSetList = radiateCheckupRegisterService.selectRadiateCheckupRegisterPageList(page, record);
			List<RadiateCheckupRegister> list = dataSetList.getRows();
            if (CollectionUtils.isNotEmpty(list)) {
            	for(RadiateCheckupRegister radiateCheckupRegister : list) {
            		if(!StringUtils.isEmpty(radiateCheckupRegister.getStatus())) {
            			if("1".equals(radiateCheckupRegister.getStatus())) {
            				radiateCheckupRegister.setStatus("正常");
            			}else {
            				radiateCheckupRegister.setStatus("异常");
            			}
            		}
            	}
				ExportUtil.export(request, response, list, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

    }
	
	/**
	 * 导入
	 * @param file
	 * @return
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "导入数据", notes = "导入数据")
	@PostMapping(value = "/api/RadiateCheckupRegister/import")
	public PlatformResult importDate(@RequestParam("file") MultipartFile file) {
		 try {
			 	List<RadiateCheckupRegister> list = (List<RadiateCheckupRegister>) ImportExcelUtil.getExcelDatas(file, RadiateCheckupRegister.class);

	            if (!list.isEmpty()) {
	            	
	            	return radiateCheckupRegisterService.importData(list);
	            	
	            } else {
	                return PlatformResult.failure("数据为空");
	            }

	        } catch (Exception e) {
	            e.printStackTrace();
	            logger.error(e.getMessage(), e);
	            return PlatformResult.failure("导入数据失败，失败原因:" + e.getMessage());
	        }
	}
}

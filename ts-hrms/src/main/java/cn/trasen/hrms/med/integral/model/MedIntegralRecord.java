package cn.trasen.hrms.med.integral.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "med_integral_record")
@Setter
@Getter
public class MedIntegralRecord {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**
     * 标题
     */
    @Column(name = "INTEGRAL_TITLE")
    @ApiModelProperty(value = "标题")
    private String integralTitle;
    
    
    /**
     * 医师工号
     */
    @Column(name = "integral_user")
    @ApiModelProperty(value = "医师工号")
    private String integralUser;

    /**
     * 医师姓名
     */
    @Column(name = "integral_user_name")
    @ApiModelProperty(value = "医师姓名")
    private String integralUserName;

    /**
     * 所属科室
     */
    @Column(name = "integral_org")
    @ApiModelProperty(value = "所属科室")
    private String integralOrg;
    
    @Column(name = "integral_org_name")
    @ApiModelProperty(value = "所属科室名称")
    private String integralOrgName;

    /**
     * 积分类型
     */
    @Column(name = "integral_type")
    @ApiModelProperty(value = "积分类型 1奖励  2惩罚")
    private String integralType;

    /**
     * 积分数额
     */
    @Column(name = "integral_score")
    @ApiModelProperty(value = "积分数额")
    private String integralScore;

    /**
     * 发生日期
     */
    @Column(name = "integral_date")
    @ApiModelProperty(value = "发生日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date integralDate;

    /**
     * 奖惩原因
     */
    @ApiModelProperty(value = "奖惩原因")
    private String reason;

    /**
     * 相关事项
     */
    @Column(name = "related_matters")
    @ApiModelProperty(value = "相关事项")
    private String relatedMatters;

    /**
     * 积分变更历史
     */
    @Column(name = "change_history")
    @ApiModelProperty(value = "积分变更历史")
    private String changeHistory;

    /**
     * 反馈与建议
     */
    @ApiModelProperty(value = "反馈与建议")
    private String feedback;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String files;

    /**
     * 审核状态 0待审核 1审核通过  2审核不通过
     */
    @ApiModelProperty(value = "审核状态 0待审核 1审核通过  2审核不通过")
    private String status;

    /**
     * 审核人工号
     */
    @Column(name = "examine_user")
    @ApiModelProperty(value = "审核人工号")
    private String examineUser;

    /**
     * 审核人名称
     */
    @Column(name = "examine_user_name")
    @ApiModelProperty(value = "审核人名称")
    private String examineUserName;

    /**
     * 审核时间
     */
    @Column(name = "examine_date")
    @ApiModelProperty(value = "审核时间")
    private Date examineDate;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    @Transient
    private String integralStartDate;
    
    @Transient
    private String integralEndDate;
    
    @Transient
    private List<String> orgIdList;
}
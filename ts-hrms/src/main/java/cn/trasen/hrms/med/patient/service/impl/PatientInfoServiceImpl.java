package cn.trasen.hrms.med.patient.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Maps;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.hrms.dao.HrmsOrganizationMapper;
import cn.trasen.hrms.interfaceRegister.dao.CommInterfaceRegisterMapper;
import cn.trasen.hrms.interfaceRegister.model.CommInterfaceLogs;
import cn.trasen.hrms.interfaceRegister.model.CommInterfaceRegister;
import cn.trasen.hrms.interfaceRegister.service.CommInterfaceLogsService;
import cn.trasen.hrms.med.crisisValue.dao.MedHisEmployeeMapper;
import cn.trasen.hrms.med.crisisValue.model.MedHisEmployee;
import cn.trasen.hrms.med.crisisValue.service.MedCrisisValueService;
import cn.trasen.hrms.med.patient.dao.PatientInfoMapper;
import cn.trasen.hrms.med.patient.model.PatientInfo;
import cn.trasen.hrms.med.patient.model.PatientInfoInParameter;
import cn.trasen.hrms.med.patient.service.PatientBirthService;
import cn.trasen.hrms.med.patient.service.PatientCriticalValueService;
import cn.trasen.hrms.med.patient.service.PatientInfoService;
import cn.trasen.hrms.med.patient.service.PatientOperationService;
import cn.trasen.hrms.med.patient.service.PatientOrderrecordService;
import cn.trasen.hrms.med.patient.service.PatientTransferDeptService;
import cn.trasen.hrms.med.risk.model.RiskOperationDiscuss;
import cn.trasen.hrms.med.shift.model.MedShiftPatient;
import cn.trasen.hrms.med.shift.service.MedShiftPatientService;
import cn.trasen.hrms.utils.HnsrmyyHisJdbcUtil;
import cn.trasen.hrms.utils.HttpClient;
import cn.trasen.hrms.utils.WssfyPacsJdbcUtil;
import dm.jdbc.util.StringUtil;
import lombok.extern.log4j.Log4j2;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PatientInfoServiceImpl
 * @Description TODO
 * @date 2025��4��7�� ����3:32:28
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Log4j2
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PatientInfoServiceImpl implements PatientInfoService {

	@Autowired
	private PatientInfoMapper mapper;

	@Autowired
	private HrmsEmployeeFeignService hrmsEmployeeFeignService;

	@Autowired
	private DictItemFeignService dictItemFeignService;
	
	@Autowired
	private CommInterfaceRegisterMapper commInterfaceRegisterMapper;
	
	@Autowired
	private CommInterfaceLogsService commInterfaceLogsService;
	
	@Autowired
	private PatientOrderrecordService patientOrderrecordService;
	
	@Autowired
	private PatientOperationService patientOperationService;
	
	@Autowired
	private PatientCriticalValueService patientCriticalValueService;
	
	@Autowired
	private PatientBirthService patientBirthService;
	
	@Autowired
	private PatientTransferDeptService patientTransferDeptService;
	
	@Autowired
	private HrmsOrganizationMapper hrmsOrganizationMapper;
	
	@Autowired
	private MedShiftPatientService medShiftPatientService;
	
	@Autowired
	private MedHisEmployeeMapper medHisEmployeeMapper;

	@Autowired
	private MedCrisisValueService medCrisisValueService;

	
	@Value("${hisRequestVersion:}")
	private String hisRequestVersion;
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(PatientInfo record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(PatientInfo record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		PatientInfo record = new PatientInfo();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public PatientInfo selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<PatientInfo> getDataSetList(Page page, PatientInfo record) {
		Example example = new Example(PatientInfo.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<PatientInfo> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public void updateOrSavePatientInfo() {
		// TODO Auto-generated method stub
		try {
			
			StringBuilder sb = new StringBuilder();
			
    		sb.append(" SELECT  B.INPATIENT_ID AS ID,A.INPATIENT_NO AS PATN_NO,A.SOCIAL_NO AS ID_CARD,A.NAME,A.SEX,A.BIRTHDAY,A.HOME_TEL AS PHONE_NUMBER,A.HOME_STREET,B.OUT_DATE,B.IN_DATE,B.ZY_DOC AS DOCTOR,EMP.NAME AS DOCTOR_NAME, "
    				+ "         B.IN_DEPT,DEPT2.NAME AS IN_DEPT_NAME,B.DEPT_ID,DEPT.NAME AS DEPT_NAME,DEPT.ZXKSMLID AS HOSP_AREA,B.IN_DIAGNOSIS,B.OUT_DIAGNOSIS,B.DIAGNOSE_DATE,B.FLAG,B.OUT_MODE,B.BOOK_DATE,B.CANCEL_BIT,DEPT.ZXKSMLID AS HOSP_AREA,HL.ORDER_BW,HL.ORDER_BZ,BED.BED_NO "
    				+ "        FROM ODSZYV10.BASE_PATIENT_PROPERTY A INNER JOIN ODSZYV10.ZY_INPATIENT B ON A.PATIENT_ID=B.PATIENT_ID "
    				+ "        LEFT JOIN  ODSZYV10.BASE_EMPLOYEE_PROPERTY EMP ON B.ZY_DOC=EMP.EMPLOYEE_ID "
    				+ "        LEFT JOIN ODSZYV10.BASE_DEPT_PROPERTY DEPT ON B.DEPT_ID=DEPT.DEPT_ID "
    				+ "        LEFT JOIN ODSZYV10.BASE_DEPT_PROPERTY DEPT2 ON B.IN_DEPT=DEPT2.DEPT_ID  "
    				+ "        LEFT JOIN ODSZYV10.ZY_INPATIENT_HL HL ON B.INPATIENT_ID=HL.INPATIENT_ID  "
    				+ "        LEFT JOIN ODSZYV10.ZY_BEDDICTION BED ON B.BED_ID=BED.BED_ID  "
    				+ " ");
    		sb.append("  WHERE   (FLAG IN (1,3,4)    "
    				+ "OR ( (B.BOOK_DATE >   TRUNC(SYSDATE -1))  OR (B.OUT_DATE >   TRUNC(SYSDATE -1))  OR (B.IN_DATE >  TRUNC(SYSDATE -1)) OR (A.PLAT_CREATE_TIME >   TRUNC(SYSDATE -1)) "
    				+ ")) ");
    		
			//sb.append("select  id as csltAppyId,appy_No as appyNo,patn_Name as patnName from  med_cslt_appy  where id = ？  ");
    		//List<CsltAppySyncHis> CsltAppyList = 	HnsrmyyHisJdbcUtil.query(sb.toString(),CsltAppySyncHis.class);//执行语句返回结果,反射映射有问题
    		log.info("===========sql:"+sb.toString());
    		List<PatientInfo> PatientInfoList = HnsrmyyHisJdbcUtil.queryPatientInfoSyncHis(sb.toString());//执行语句返回结果
    		log.info("===========PatientInfoList:"+PatientInfoList.size());
    		if(CollUtil.isNotEmpty(PatientInfoList)){
    			for(PatientInfo patientInfo : PatientInfoList) {
    				PatientInfo record = mapper.selectByPrimaryKey(patientInfo.getId());//根据患者id查询是否已经存在患者数据，存在则更新;
    				if(record != null) {
    					patientInfo.setUpdateDate(DateUtil.date());
    					mapper.updateByPrimaryKeySelective(patientInfo);
    				}else {
    					patientInfo.setIsDeleted("N");
    					patientInfo.setCreateDate(DateUtil.date());
    					patientInfo.setUpdateDate(DateUtil.date());
    					mapper.insertSelective(patientInfo);
    				}
    			}
    		}
    		
    		patientOrderrecordService.updateOrSavePatientOrderrecord();//插入或者更新住院患者(病危/病重医嘱信息)
    		
    		patientOperationService.updateOrSavePatientOperation();//插入或者更新住院患者(手术信息)
    		
    		patientCriticalValueService.updateOrSavePatientCriticalValue();//插入或者更新住院患者(危急值信息)
    		
    		patientBirthService.updateOrSavePatientBirth();//插入或者更新住院患者(分娩婴儿信息)
    		
    		patientTransferDeptService.updateOrSavePatientTransferDept();//插入或者更新住院患者(转科信息)
    		
    		//更新患者标记状态
    		mapper.updatePatientInfoBj();
		}catch(Exception e) {
    		e.printStackTrace();
    		log.error("获取影子库病人信息数据异常：" + e.getMessage());
    	}
	}
	
	
	@Transactional(readOnly = false)
	@Override
	public void syncPlatformPatientInfo() {
		// TODO Auto-generated method stub
		long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询住院患者");//住院患者基本信息
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);//查询配置信息
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				//平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构
				String orgType = medHisEmployeeMapper.selectPlatformOrgType();
				
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				
				Map<String,Object> requestParams = new HashMap<>();
				// 创建一个List对象
		        List<String> statusList = new ArrayList<>();
		        statusList.add("3");
		        statusList.add("4");
		        
				if( "1".equals(orgType)) {
					requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
				}else {
					requestParams.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
				}
				
				if("2".equals(orgType)) {
					//紧密型 需要传院区查询
					MedHisEmployee hisEmployee = medHisEmployeeMapper.selectHisEmployee(UserInfoHolder.getCurrentUserCode(), null);
					requestParams.put("onlyHosp", hisEmployee.getHospCode());
				}
				
				requestParams.put("sortOrder", "0");//0-按创建日期 1-按入院日期 默认 0
				requestParams.put("statuss", statusList);//查询在院状态的住院患者
				requestParams.put("pageIndex", "1");
				requestParams.put("pageSize", "1000000");
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				JSONObject reuslt = JSON.parseObject(bodyStr);
				
				JSONArray jsonArray = reuslt.getJSONArray("list");//获取list数据
				if(null != jsonArray && jsonArray.size() > 0) {
					List<PatientInfo> patientInfoList = new ArrayList<>();
					 // 定义日期格式
		            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					for (int i = 0; i < jsonArray.size(); i++) {
						PatientInfo patientInfo = new PatientInfo();
						JSONObject obj = jsonArray.getJSONObject(i);
						patientInfo.setId(obj.getString("id"));//住院病人ID
						patientInfo.setPatnNo(obj.getString("inPatientNo"));//住院号
						patientInfo.setIdCard(obj.getString("idCard"));//身份证号
						patientInfo.setName(obj.getString("name"));//姓名
						patientInfo.setSex(obj.getString("sex"));//性别
						patientInfo.setPhoneNumber(obj.getString("contactPhone"));//联系电话
						patientInfo.setHomeStreet(obj.getString("address"));//地址
						try {
							patientInfo.setBirthday(dateFormat.parse(obj.getString("birthDay")));//出生日期
							patientInfo.setInDate(dateFormat.parse(obj.getString("inDate")));//入院日期
							patientInfo.setOutDate(dateFormat.parse(obj.getString("outDate")));//出院日期
						} catch (java.text.ParseException e) {
							e.printStackTrace();
						}
						patientInfo.setDoctor(obj.getString("mainDocId"));//主治医生
						patientInfo.setDoctorName(obj.getString("mainDocName"));//主治医生姓名
						patientInfo.setInDept(obj.getString("inDept"));//入院科室
						patientInfo.setInDeptName(obj.getString("inDeptName"));//入院科室姓名
						patientInfo.setDeptId(obj.getString("deptId"));//科室id
						patientInfo.setDeptName(obj.getString("deptName"));//科室姓名
						patientInfo.setHospArea(obj.getString("hospCode"));//院区
						patientInfo.setInDiagnosis(obj.getString("inDiagnosisName"));//入院诊断
						patientInfo.setOutDiagnosis(obj.getString("outDiagnosisName"));//出院诊断
						//patientInfo.setDiagnoseDate(dateFormat.parse(obj.getString("inPatientNo")));//诊断日期
						patientInfo.setFlag(obj.getString("status"));//1-待入科室 3-在科室 4-出院医嘱 5-出区 6-结算 7-欠费结算 10-注销(入院)
						patientInfo.setOutMode(obj.getString("outMode"));//出院方式：1治愈、2好转、3未愈、4死亡、5其他
						//patientInfo.setBookDate(dateFormat.parse(obj.getString("inPatientNo")));//操作日期
						//patientInfo.setCancelBit(obj.getString("inPatientNo"));//取消标志
						patientInfoList.add(patientInfo);
					}
					if(CollUtil.isNotEmpty(patientInfoList)){
		    			for(PatientInfo patientInfo : patientInfoList) {
		    				PatientInfo record = mapper.selectByPrimaryKey(patientInfo.getId());//根据患者id查询是否已经存在患者数据，存在则更新;
		    				if(record != null) {
		    					patientInfo.setUpdateDate(DateUtil.date());
		    					mapper.updateByPrimaryKeySelective(patientInfo);
		    				}else {
		    					patientInfo.setIsDeleted("N");
		    					patientInfo.setCreateDate(DateUtil.date());
		    					patientInfo.setUpdateDate(DateUtil.date());
		    					mapper.insertSelective(patientInfo);
		    				}
		    			}
		    		}
					/*
					if(CollectionUtils.isNotEmpty(patientInfoList)) {
						//发送数据量过大  拆分数据 
						List<List<PatientInfo>> saveList = CommonUtils.averageAssign(patientInfoList,1000);
						//使用并行流插入数据
						saveList.stream().forEach(ls ->{
							mapper.batchInsert(ls);
				        });
					}*/
				}
				
				long endTime = System.currentTimeMillis();  
				
				CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
				commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
				commInterfaceLogs.setInterfaceName("集成平台-查询住院患者");
				commInterfaceLogs.setInterworkPlatform("集成平台");
				commInterfaceLogs.setRequestUrl(requestUrl);
				commInterfaceLogs.setRequestParams(jsonString);
				commInterfaceLogs.setResponseParams(reuslt.getString("Code") + ":" + reuslt.getString("Message") + ":" + reuslt.getString("exception"));
				
				if("200".equals(reuslt.getString("Code"))) {
					commInterfaceLogs.setResponseStatus("1");
				}else {
					commInterfaceLogs.setResponseStatus("2");
				}
				
				commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
				commInterfaceLogsService.save(commInterfaceLogs);
			}
		}
	}

	@Override
	public DataSet<Map<String,Object>> getQueryInPatientOrder(Page page, PatientInfoInParameter parame) {
		// TODO Auto-generated method stub
		// TODO Auto-generated method stub
		//long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询医嘱信息");//住院患者基本信息
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);//查询配置信息
		List<Map<String,Object>> result = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				//平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构
				String orgType = medHisEmployeeMapper.selectPlatformOrgType();
				
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				Map<String,Object> requestParams = new HashMap<>();
				
				//hisRequestVersion  1通山版本  2文山版本  其他的是标准版本
				if(StringUtils.isNotBlank(hisRequestVersion) && ("2".equals(hisRequestVersion) || "1".equals(hisRequestVersion))) {
					requestParams.put("IsCompress", "false");
					requestParams.put("ServiceName", "OuterClinicService");//服务名
					requestParams.put("InterfaceName", "QueryInPatientOrder");//接口名
					requestParams.put("TimeOut", 15000);//超时时间
					
					Map<String,Object> paramsMap = new HashMap<>();
					
					//判断是否多机构  多机构用登陆人机构编码去查询
					if( "1".equals(orgType)) {
						paramsMap.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						paramsMap.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					if(StringUtils.isEmpty(parame.getSortOrder())) {
						paramsMap.put("sortOrder", "0");//0-按创建时间 1-按更新时间 2-按开嘱日期 3-按停嘱日期
					}else {
						paramsMap.put("sortOrder", parame.getSortOrder());
					}
					if(!StringUtils.isEmpty(parame.getPatnId())) {
						paramsMap.put("inPatientIds", Arrays.asList(parame.getPatnId().split(",")));
					}
					if(!StringUtils.isEmpty(parame.getMngTypes())) {
						paramsMap.put("mngTypes", Arrays.asList(parame.getMngTypes().split(",")));
					}
					if(!StringUtils.isEmpty(parame.getStatusFlags())) {
						paramsMap.put("statusFlags", Arrays.asList(parame.getStatusFlags().split(",")));
					}
					if(!StringUtils.isEmpty(parame.getCancelFlags())) {
						paramsMap.put("cancelFlags", Arrays.asList(parame.getCancelFlags().split(",")));
					}
					if(!StringUtils.isEmpty(parame.getStartOrderBDate())) {
						paramsMap.put("startOrderBDate", parame.getStartOrderBDate()+" 00:00:00");//添加时分秒
					}
					if(!StringUtils.isEmpty(parame.getEndOrderBDate())) {
						paramsMap.put("endOrderBDate", parame.getEndOrderBDate()+" 23:59:59");
					}
					
					paramsMap.put("pageIndex", page.getPageNo());
					paramsMap.put("pageSize", page.getPageSize());
					
					requestParams.put("Parameter",paramsMap);
				}else {
					
					//判断是否多机构  多机构用登陆人机构编码去查询
					if( "1".equals(orgType)) {
						requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						requestParams.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					if(StringUtils.isEmpty(parame.getSortOrder())) {
						requestParams.put("sortOrder", "0");//0-按创建时间 1-按更新时间 2-按开嘱日期 3-按停嘱日期
					}else {
						requestParams.put("sortOrder", parame.getSortOrder());
					}
					if(!StringUtils.isEmpty(parame.getPatnId())) {
						requestParams.put("inPatientIds", Arrays.asList(parame.getPatnId().split(",")));
					}
					if(!StringUtils.isEmpty(parame.getMngTypes())) {
						requestParams.put("mngTypes", Arrays.asList(parame.getMngTypes().split(",")));
					}
					if(!StringUtils.isEmpty(parame.getStatusFlags())) {
						requestParams.put("statusFlags", Arrays.asList(parame.getStatusFlags().split(",")));
					}
					if(!StringUtils.isEmpty(parame.getCancelFlags())) {
						requestParams.put("cancelFlags", Arrays.asList(parame.getCancelFlags().split(",")));
					}
					if(!StringUtils.isEmpty(parame.getStartOrderBDate())) {
						requestParams.put("startOrderBDate", parame.getStartOrderBDate()+" 00:00:00");//添加时分秒
					}
					if(!StringUtils.isEmpty(parame.getEndOrderBDate())) {
						requestParams.put("endOrderBDate", parame.getEndOrderBDate()+" 23:59:59");
					}
					
					requestParams.put("pageIndex", page.getPageNo());
					requestParams.put("pageSize", page.getPageSize());
				}
				
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				log.info("返回参数：" + bodyStr);
				JSONObject reusltObject = JSON.parseObject(bodyStr);
				JSONArray jsonArray = null;
				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
					JSONObject data = reusltObject.getJSONObject("data");
					JSONObject Value = data.getJSONObject("Value");
					jsonArray = Value.getJSONArray("list");
					Integer totalCount = Value.getInteger("totalCount");
			   		page.setTotalCount(totalCount);
				}else {
					jsonArray = reusltObject.getJSONArray("list");//获取list数据
					Integer totalCount = reusltObject.getInteger("totalCount");
			   		page.setTotalCount(totalCount);
				}
				
				if(null != jsonArray && jsonArray.size() > 0) {
					 // 定义日期格式
		            //SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					for (int i = 0; i < jsonArray.size(); i++) {
						Map<String,Object> map = new HashMap<>();
						JSONObject obj = jsonArray.getJSONObject(i);
						map.put("inPatientId", obj.get("inPatientId"));//住院病人id
						map.put("orderType", obj.get("orderType"));//医嘱类型
						map.put("orderTypeName", obj.get("orderTypeName"));//医嘱类型名称
						map.put("orderRecordId", obj.get("orderRecordId"));//医嘱明细唯一id
						map.put("deptId", obj.get("deptId"));//开单科室
						map.put("deptName", obj.get("deptName"));//开单科室名称
						map.put("orderDocId", obj.get("orderDocId"));//开单医生
						map.put("orderDocName", obj.get("orderDocName"));//开单医生姓名
						map.put("orderBDate", obj.get("orderBDate"));//开单日期
						map.put("orderName", obj.get("orderName"));//医嘱内容
						map.put("dosage", obj.get("dosage"));//剂量
						map.put("dosageUnitName", obj.get("dosageUnitName"));//剂量单位名称
						map.put("usageName", obj.get("usageName"));//用法
						map.put("frequencyName", obj.get("frequencyName"));//频次名称
						map.put("dropsper", obj.get("dropsper"));//滴量
						map.put("execDeptId", obj.get("execDeptId"));//执行科室id
						map.put("execDeptName", obj.get("execDeptName"));//执行科室名称
						map.put("statusFlag", obj.get("statusFlag"));//医嘱状态0-保存1-发送2-转抄3-停医嘱4-停医嘱转抄5-完成
						map.put("orderEDate", obj.get("orderEDate"));//停嘱时间
						map.put("orderEDocId", obj.get("orderEDocId"));//停嘱医生id
						map.put("orderEDocName", obj.get("orderEDocName"));//停嘱医生姓名
						map.put("skinTestResult", obj.get("skinTestResult"));//皮试结果
						map.put("auditingUserId", obj.get("auditingUserId"));//转抄护士id
						map.put("auditingUserName", obj.get("auditingUserName"));//转抄护士姓名
						map.put("auditingUserId1", obj.get("auditingUserId1"));//核对护士姓名
						map.put("auditingUserName1", obj.get("auditingUserName1"));//核对护士姓名
						map.put("groupId", obj.get("groupId"));//医嘱组号,医嘱组id
						map.put("firstTimes", obj.get("firstTimes"));//首次
						map.put("termialTimes", obj.get("termialTimes"));//末次
						map.put("dropsper", obj.get("dropsper"));//滴量
						map.put("amount", obj.get("amount"));//总量
						map.put("amountUnitId", obj.get("amountUnitId"));//总量单位id
						map.put("amountUnitName", obj.get("amountUnitName"));//总量单位名称
						map.put("amountUnitRate", obj.get("amountUnitRate"));//总量单位比例
						map.put("amountUnitType", obj.get("amountUnitType"));//总量单位类型
						map.put("price", obj.get("price"));//单价
		       			result.add(map);
					}
				}
				
				//long endTime = System.currentTimeMillis();
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), result);
	}
	
	@Override
	public List<Map<String,Object>> getExamRptRequestNew(PatientInfoInParameter parame) {
		
		//文山妇幼直接调用pacs数据库视图
		if("WSSFY".equals(UserInfoHolder.getCurrentUserCorpCode())) {
			
			StringBuilder sb = new StringBuilder();
			
    		sb.append(" SELECT * FROM v_exam_result where visitNo = '").append(parame.getPatnNo()).append("'");
    		
    		List<Map<String,Object>> vexamResultList = WssfyPacsJdbcUtil.queryVExamResult(sb.toString());//执行语句返回结果
    		
    		return vexamResultList;
    		
		}else {
			Example example = new Example(CommInterfaceRegister.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("callType", "2");
			criteria.andEqualTo("interfaceName", "集成平台-查询检查报告");//住院患者基本信息
			List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);//查询配置信息
			List<Map<String,Object>> result = new ArrayList<>();
			if(CollectionUtils.isNotEmpty(registerList)) {
				CommInterfaceRegister commInterfaceRegister = registerList.get(0);
				if("1".equals(commInterfaceRegister.getStatus())) {
					
					//平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构
					String orgType = medHisEmployeeMapper.selectPlatformOrgType();
					
					Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
					Map<String,Object> requestParams = new HashMap<>();
					
					if(StringUtils.isNotBlank(hisRequestVersion) && "3".equals(hisRequestVersion) ) {
						requestParams.put("IsCompress", "false");
						requestParams.put("ServiceName", "PlatformPacsService");//服务名
						requestParams.put("InterfaceName", "ExamRptRequestNew");//接口名
						requestParams.put("TimeOut", 15000);//超时时间
						
						Map<String,Object> paramsMap = new HashMap<>();
						
						if( "1".equals(orgType)) {
							paramsMap.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
						}else {
							paramsMap.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
						}
						
						/*
						if(!StringUtils.isEmpty(parame.getHospCode())) {
							requestParams.put("hospCode", parame.getHospCode());//院区需要前段传输
						}else {
							requestParams.put("hospCode", commInterfaceRegister.getPlatformHospCode());
						}*/
						paramsMap.put("visitId", parame.getPatnId());//住院病人id
						paramsMap.put("visitType", "2");//查询住院
						requestParams.put("Parameter",paramsMap);
					}else {
						
						if( "1".equals(orgType)) {
							requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
						}else {
							requestParams.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
						}
						
						/*
						if(!StringUtils.isEmpty(parame.getHospCode())) {
							requestParams.put("hospCode", parame.getHospCode());//院区需要前段传输
						}else {
							requestParams.put("hospCode", commInterfaceRegister.getPlatformHospCode());
						}*/
						requestParams.put("visitId", parame.getPatnId());//住院病人id
						requestParams.put("visitType", "2");//查询住院
					}
					
					
					String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
					
					String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
					String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
					log.info("返回参数：" + bodyStr);
					JSONObject reusltObject = JSON.parseObject(bodyStr);
					JSONArray jsonArray = null;
					if(StringUtils.isNotBlank(hisRequestVersion) && "3".equals(hisRequestVersion)) {
						JSONObject data = reusltObject.getJSONObject("data");
						JSONObject Value = data.getJSONObject("Value");
						jsonArray = Value.getJSONArray("itemList");
					}else {
						jsonArray = reusltObject.getJSONArray("itemList");
//						if (reusltObject.get("itemList") instanceof JSONArray) {//文山判断itemList里是否是json
//							jsonArray = reusltObject.getJSONArray("itemList");//获取list数据
//						}
					}
					if(null != jsonArray && jsonArray.size() > 0) {
						 // 定义日期格式
			            //SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						for (int i = 0; i < jsonArray.size(); i++) {
							Map<String,Object> map = new HashMap<>();
							JSONObject obj = jsonArray.getJSONObject(i);
							map.put("reportNo", obj.get("reportNo"));//报告单号
							map.put("examTypeLargeName", obj.get("examTypeLargeName"));//检查大类名称
							map.put("examTypeName", obj.get("examTypeName"));//检查类型名称
							map.put("patientId", obj.get("patientId"));//病人id
							map.put("name", obj.get("name"));//姓名
							map.put("sexName", obj.get("sexName"));//性别名称
							map.put("birthday", obj.get("birthday"));//出生日期
							map.put("requestUserId", obj.get("requestUserId"));//申请人id
							map.put("requestUserName", obj.get("requestUserName"));//申请人名称
							map.put("requestDeptId", obj.get("requestDeptId"));//申请科室id
							map.put("requestDeptName", obj.get("requestDeptName"));//申请科室名称
							map.put("applyDateTime", obj.get("applyDateTime"));//申请日期
							map.put("reporDeptId", obj.get("reporDeptId"));//报告科室id
							map.put("reportDeptName", obj.get("reportDeptName"));//报告科室名称
							map.put("examBodyName", obj.get("examBodyName"));//检查部位名称
							map.put("examWayName", obj.get("examWayName"));//检查方法名称
							map.put("examEquipment", obj.get("examEquipment"));//检查设备名称
							map.put("reportDate", obj.get("reportDate"));//报告日期
							map.put("reportUserName", obj.get("reportUserName"));//报告人姓名
							map.put("reportUserId", obj.get("reportUserId"));//报告人id
							map.put("reportResult", obj.get("reportResult"));//报告结果
							map.put("examDate", obj.get("examDate"));//检查日期
							map.put("examUserName", obj.get("examUserName"));//检查人姓名
							map.put("examUserId", obj.get("examUserId"));//检查人id
							map.put("examFinding", obj.get("examFinding"));//检查所见
							map.put("reportRemarks", obj.get("reportRemarks"));//报告备注
							map.put("fileLink", obj.get("fileLink"));//文件链接（pdf地址）
							map.put("filePicLink", obj.get("filePicLink"));//文件链接（图片地址）
							map.put("crisisFlag", obj.get("crisisFlag"));//危急值标志  0-否 1-是
							map.put("crisisDesc", obj.get("crisisDesc"));//危急值描述
			       			result.add(map);
						}
					}
					//long endTime = System.currentTimeMillis();
				}
			}
			return result;
		}
	}
	
	@Override
	public List<Map<String,Object>> getTestRptItemRequestNew(PatientInfoInParameter parame) {
		// TODO Auto-generated method stub
		//long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询检验报告");
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);
		List<Map<String,Object>> result = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				//平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构
				String orgType = medHisEmployeeMapper.selectPlatformOrgType();
				
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				Map<String,Object> requestParams = new HashMap<>();
				if(StringUtils.isNotBlank(hisRequestVersion) && ("2".equals(hisRequestVersion) || "1".equals(hisRequestVersion))) {
					requestParams.put("IsCompress", "false");
					//requestParams.put("ServiceName", "PlatformLisService");//服务名
					requestParams.put("ServiceName", "Lis-LisOutService");//服务名
					requestParams.put("InterfaceName", "TestRptItemRequestNew");//接口名
					requestParams.put("TimeOut", 20000);//超时时间
					
					Map<String,Object> paramsMap = new HashMap<>();
					
					if( "1".equals(orgType)) {
						paramsMap.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						paramsMap.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					//文山不需要传院区编码 传了拿不到
//					if(!StringUtils.isEmpty(parame.getHospCode())) {
//						paramsMap.put("hospCode", parame.getHospCode());//院区需要前段传输
//					}else {
//						MedHisEmployee hisEmployee = medHisEmployeeMapper.selectHisEmployee(UserInfoHolder.getCurrentUserCode(),null);
//						paramsMap.put("hospCode", hisEmployee.getHospCode());
//					}
					
					paramsMap.put("isGetFileLink","1");//是否获取pdf地址--fileLink
					if(StringUtils.isNoneBlank(parame.getPatnId())) {
						paramsMap.put("visitId", parame.getPatnId());//住院病人id
					}
					if(StringUtils.isNoneBlank(parame.getRepNo())) {
						paramsMap.put("repNo", parame.getRepNo());//检验报告单号
					}
					
					paramsMap.put("visitType", "2");//查询住院
			       	
			       	requestParams.put("Parameter",paramsMap);
			       	
				}else {
					
					if( "1".equals(orgType)) {
						requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						requestParams.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					//文山不需要传院区编码 传了拿不到
//					if(!StringUtils.isEmpty(parame.getHospCode())) {
//						requestParams.put("hospCode", parame.getHospCode());//院区需要前段传输
//					}else {
//						MedHisEmployee hisEmployee = medHisEmployeeMapper.selectHisEmployee(UserInfoHolder.getCurrentUserCode(),null);
//						requestParams.put("hospCode", hisEmployee.getHospCode());
//					}
					requestParams.put("isGetFileLink","1");//是否获取pdf地址--fileLink
					if(StringUtils.isNoneBlank(parame.getPatnId())) {
						requestParams.put("visitId", parame.getPatnId());//住院病人id
					}
					if(StringUtils.isNoneBlank(parame.getRepNo())) {
						requestParams.put("repNo", parame.getRepNo());//检验报告单号
					}
					requestParams.put("visitType", "2");//查询住院
				}
				
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				log.info("返回参数：" + bodyStr);
				JSONObject reusltObject = JSON.parseObject(bodyStr);
				JSONArray jsonArray = null;
				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
					JSONObject data = reusltObject.getJSONObject("data");
					JSONObject Value = data.getJSONObject("Value");
					jsonArray = Value.getJSONArray("itemList");
				}else {
					jsonArray = reusltObject.getJSONArray("itemList");//获取list数据
				}
				if(null != jsonArray && jsonArray.size() > 0) {
					 // 定义日期格式
		            //SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					for (int i = 0; i < jsonArray.size(); i++) {
						Map<String,Object> map = new HashMap<>();
						JSONObject obj = jsonArray.getJSONObject(i);
						map.put("reportNo", obj.get("reportNo"));//报告单号
						map.put("reportType", obj.get("reportType"));//报告类型
						map.put("operatorId", obj.get("operatorId"));//操作人id
						map.put("operatorName", obj.get("operatorName"));//操作人姓名
						map.put("patientId", obj.get("patientId"));//病人id
						map.put("name", obj.get("name"));//姓名
						map.put("sexName", obj.get("sexName"));//性别名称
						map.put("birthday", obj.get("birthday"));//出生日期
						map.put("age", obj.get("age"));//年龄
						map.put("idCard", obj.get("idCard"));//身份证号
						map.put("requestId", obj.get("requestId"));//申请id
						map.put("requestUserId", obj.get("requestUserId"));//申请人id
						map.put("requestUserName", obj.get("requestUserName"));//申请人名称
						map.put("requestDeptId", obj.get("requestDeptId"));//申请科室id
						map.put("requestDeptName", obj.get("requestDeptName"));//申请科室名称
						map.put("requestDate", obj.get("requestDate"));//申请时间
						map.put("reporDeptId", obj.get("reporDeptId"));//报告科室id
						map.put("reportDeptName", obj.get("reportDeptName"));//报告科室名称
						map.put("reportDate", obj.get("reportDate"));//报告日期
						map.put("reportUserName", obj.get("reportUserName"));//报告人姓名
						map.put("reportUserId", obj.get("reportUserId"));//报告人id
						map.put("reportRemark", obj.get("reportRemark"));//报告备注
						map.put("testDate", obj.get("testDate"));//检验日期
						map.put("testUserId", obj.get("testUserId"));//检验人id
						map.put("testUserName", obj.get("testUserName"));//检验人
						map.put("collectDate", obj.get("collectDate"));//采集日期
						map.put("collectUserId", obj.get("collectUserId"));//采集人id
						map.put("collectUserName", obj.get("collectUserName"));//采集人
						map.put("sampleName", obj.get("sampleName"));//标本名称
						map.put("sampleRecieveDate", obj.get("sampleRecieveDate"));//接收标本时间
						map.put("fileLink", obj.get("fileLink"));//文件链接（pdf地址）
						map.put("crisisFlag", obj.get("crisisFlag"));//危急值标志  0-否 1-是
						map.put("crisisDesc", obj.get("crisisDesc"));//危急值描述
						map.put("orderName", obj.get("orderName"));//医嘱项目
		       			result.add(map);
					}
				}
				//long endTime = System.currentTimeMillis();
			}
		}
		return result;
	}
	
	@Override
	public List<Map<String, Object>> getTestRptItemRequestNewXdt(PatientInfoInParameter parame) {
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询检验报告(心电图)");
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);
		List<Map<String,Object>> result = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				Map<String,Object> requestParams = new HashMap<>();
				requestParams.put("ORG_CODE", commInterfaceRegister.getPlatformOrgCode());
				requestParams.put("HOSP_CODE", commInterfaceRegister.getPlatformHospCode());
				requestParams.put("VIS_TYPE_NAME","住院");
				requestParams.put("EXAM_STATUS","已报告");
				requestParams.put("INPATIENT_NO",parame.getPatnNo());
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				log.info("返回参数：" + bodyStr);
				JSONObject reusltObject = JSON.parseObject(bodyStr);
				JSONArray jsonArray = reusltObject.getJSONArray("data");
				if(null != jsonArray && jsonArray.size() > 0) {
					for (int i = 0; i < jsonArray.size(); i++) {
						Map<String,Object> map = new HashMap<>();
						JSONObject obj = jsonArray.getJSONObject(i);
						map.put("fileLink", obj.get("FILE_LINK"));
		       			result.add(map);
					}
				}
			}
		}
		return result;
	}

	@Override
	public List<Map<String,Object>> getTestRptItemDetailNew(PatientInfoInParameter parame) {
		// TODO Auto-generated method stub
		//long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询检验报告(结果明细)");//住院患者基本信息
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);//查询配置信息
		List<Map<String,Object>> result = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				//平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构
				String orgType = medHisEmployeeMapper.selectPlatformOrgType();
				
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				Map<String,Object> requestParams = new HashMap<>();
				//hisRequestVersion  1通山版本  2文山版本  其他的是标准版本
				if(StringUtils.isNotBlank(hisRequestVersion) && ("2".equals(hisRequestVersion) || "1".equals(hisRequestVersion))) {
					requestParams.put("IsCompress", "false");
					//requestParams.put("ServiceName", "PlatformLisService");//服务名
					requestParams.put("ServiceName", "Lis-LisOutService");//服务名
					requestParams.put("InterfaceName", "TestRptItemDetailNew");//接口名
					requestParams.put("TimeOut", 15000);//超时时间
					
					Map<String,Object> paramsMap = new HashMap<>();
					
					if( "1".equals(orgType)) {
						paramsMap.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						paramsMap.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					/*
					if(!StringUtils.isEmpty(parame.getHospCode())) {
						paramsMap.put("hospCode", parame.getHospCode());//院区需要前段传输
					}else {
						paramsMap.put("hospCode", commInterfaceRegister.getPlatformHospCode());
					}*/
					paramsMap.put("repNo", parame.getRepNo());//检验单号
					//paramsMap.put("visitId", parame.getPatnId());//住院病人id
					//paramsMap.put("visitType", "2");//查询住院
			       	
			       	requestParams.put("Parameter",paramsMap);
			       	
				}else {
					
					if( "1".equals(orgType)) {
						requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						requestParams.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					/*
					if(!StringUtils.isEmpty(parame.getHospCode())) {
						requestParams.put("hospCode", parame.getHospCode());//院区需要前段传输
					}else {
						requestParams.put("hospCode", commInterfaceRegister.getPlatformHospCode());
					}*/
					requestParams.put("repNo", parame.getRepNo());//检验单号
					//requestParams.put("visitId", parame.getPatnId());//住院病人id
					//requestParams.put("visitType", "2");//查询住院
				}
				
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				log.info("返回参数：" + bodyStr);
				JSONObject reusltObject = JSON.parseObject(bodyStr);
				JSONArray jsonArray = null;
				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
					JSONObject data = reusltObject.getJSONObject("data");
					JSONObject Value = data.getJSONObject("Value");
					jsonArray = Value.getJSONArray("routineResults");
				}else {
					jsonArray = reusltObject.getJSONArray("routineResults");//获取list数据:常规检验结果集合
				}
				
				//JSONArray jsonArray2 = reusltObject.getJSONArray("germResults");//细菌培养结果集合
				if(null != jsonArray && jsonArray.size() > 0) {
					 // 定义日期格式
		            //SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					for (int i = 0; i < jsonArray.size(); i++) {
						Map<String,Object> map = new HashMap<>();
						JSONObject obj = jsonArray.getJSONObject(i);
						map.put("reportNo", obj.get("reportNo"));//报告单号
						map.put("resultId", obj.get("resultId"));//结果id
						map.put("routineType", obj.get("routineType"));//1常规检验结果，2微生物报告结果
						map.put("orderItemId", obj.get("orderItemId"));//医嘱项目id
						map.put("orderItemName", obj.get("orderItemName"));//医嘱项目名称
						map.put("itemCode", obj.get("itemCode"));//项目编码
						map.put("itemName", obj.get("itemName"));//项目名称
						map.put("engName", obj.get("engName"));//英文名称
						map.put("result", obj.get("result"));//检验结果
						map.put("unit", obj.get("unit"));//剂量单位
						map.put("referenceRange", obj.get("referenceRange"));//参考区间
						map.put("referenceMemo", obj.get("referenceMemo"));//参考区间说明
						map.put("minVal", obj.get("minVal"));//最小区间值
						map.put("maxVal", obj.get("maxVal"));//最大区间值
						map.put("resultFlag", obj.get("resultFlag"));//结果提示0-正常 1-偏高 ↑  2-偏低 ↓ 5-阴性(-)  6-阳性(+)  7-弱阳性(±)  9-其他 *
						map.put("description", obj.get("description"));//说明
						map.put("crisisFlag", obj.get("crisisFlag"));//危急值标志0-否 1-是
						map.put("crisisDesc", obj.get("crisisDesc"));//危机值描述
		       			result.add(map);
					}
				}
				//long endTime = System.currentTimeMillis();
			}
		}
		return result;
	}
	
	@Override
	public List<Map<String,Object>> getQueryInpEmrFileInfo(PatientInfoInParameter parame) {
		// TODO Auto-generated method stub
		//long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询住院病历");//查询住院病历
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);//查询配置信息
		List<Map<String,Object>> result = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				//平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构
				String orgType = medHisEmployeeMapper.selectPlatformOrgType();
				
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				Map<String,Object> requestParams = new HashMap<>();
				
				//hisRequestVersion  1通山版本  2文山版本  其他的是标准版本
				if(StringUtils.isNotBlank(hisRequestVersion) && ("2".equals(hisRequestVersion) || "1".equals(hisRequestVersion))) {
					requestParams.put("IsCompress", "false");
					requestParams.put("ServiceName", "IOutsideInterfaceService");//服务名
					requestParams.put("InterfaceName", "QueryInpEmrFileInfo");//接口名
					requestParams.put("TimeOut", 15000);//超时时间
					
					Map<String,Object> paramsMap = new HashMap<>();
					
					if( "1".equals(orgType)) {
						paramsMap.put("ORG_CODE", commInterfaceRegister.getPlatformOrgCode());
					}else {
						paramsMap.put("ORG_CODE", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					if(!StringUtils.isEmpty(parame.getHospCode())) {
						paramsMap.put("HOSP_CODE", parame.getHospCode());//院区需要前段传输
					}else {
						MedHisEmployee hisEmployee = medHisEmployeeMapper.selectHisEmployee(UserInfoHolder.getCurrentUserCode(),null);
						paramsMap.put("HOSP_CODE", hisEmployee.getHospCode());
					}
					if(!StringUtils.isEmpty(parame.getPatnId())) {
						paramsMap.put("INPATIENT_IDS", Arrays.asList(parame.getPatnId().split(",")));//住院病人id集合
					}
					//paramsMap.put("IsSelectNur", "1");
			       	
			       	requestParams.put("Parameter",paramsMap);
			       	
				}else {
					requestParams.put("ORG_CODE", commInterfaceRegister.getPlatformOrgCode());
					if(!StringUtils.isEmpty(parame.getHospCode())) {
						requestParams.put("HOSP_CODE", parame.getHospCode());//院区需要前段传输
					}else {
						MedHisEmployee hisEmployee = medHisEmployeeMapper.selectHisEmployee(UserInfoHolder.getCurrentUserCode(),null);
						requestParams.put("HOSP_CODE", hisEmployee.getHospCode());
					}
					
					if(!StringUtils.isEmpty(parame.getPatnId())) {
						requestParams.put("INPATIENT_IDS", Arrays.asList(parame.getPatnId().split(",")));//住院病人id集合
					}
					//requestParams.put("IsSelectNur", "1");
				}
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress(); //+ "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				log.info("返回参数：" + bodyStr);
				JSONObject reusltObject = JSON.parseObject(bodyStr);
				
				JSONArray jsonArray = null;
				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
					JSONObject data = reusltObject.getJSONObject("data");
					JSONObject Value = data.getJSONObject("Value");
					jsonArray = Value.getJSONArray("Data");
				}else {
					jsonArray = reusltObject.getJSONArray("Data");//获取list数据
				}
				
				if(null != jsonArray && jsonArray.size() > 0) {
					 // 定义日期格式
		            //SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					for (int i = 0; i < jsonArray.size(); i++) {
						Map<String,Object> map = new HashMap<>();
						JSONObject obj = jsonArray.getJSONObject(i);
						map.put("INPATIENT_ID", obj.get("INPATIENT_ID"));//住院ID
						map.put("EMR_ID", obj.get("EMR_ID"));//病历文件ID
						map.put("INPATIENT_NO", obj.get("INPATIENT_NO"));//住院号
						map.put("PATIENT_NAME", obj.get("PATIENT_NAME"));//患者姓名
						map.put("PATIENT_SEX", obj.get("PATIENT_SEX"));//患者性别(1：男性 2:女性)
						map.put("MR_TYPE", obj.get("MR_TYPE"));//病历类型
						map.put("MR_TITLE", obj.get("MR_TITLE"));//病历标题
						map.put("MR_TITLEDATE", obj.get("MR_TITLEDATE"));//标题时间
						map.put("CREATE_USER", obj.get("CREATE_USER"));//创建人
						map.put("CREATE_DATE", obj.get("CREATE_DATE"));//创建日期
						map.put("TEMPLATE_FILETYPE", obj.get("TEMPLATE_FILETYPE"));//模板病历文件类型(0:普通病历 1：一般护理记录单 2.血糖 3.文书 4.评估单(竖版) 5.评估单(横版))
						map.put("WEBPAGE_URL", obj.get("WEBPAGE_URL"));//浏览病历内容的网页地址
		       			result.add(map);
					}
				}
				//long endTime = System.currentTimeMillis();
			}
		}
		return result;
	}
	
	
	@Override
	public List<Map<String,Object>> getQueryInpEmrFileInfoPdfStream(PatientInfoInParameter parame) {
		// TODO Auto-generated method stub
		//long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询住院病历pdf流");//查询住院病历
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);//查询配置信息
		List<Map<String,Object>> result = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				//平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构
				String orgType = medHisEmployeeMapper.selectPlatformOrgType();
				
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				Map<String,Object> requestParams = new HashMap<>();
				
				//hisRequestVersion  1通山版本  2文山版本  其他的是标准版本
				if(StringUtils.isNotBlank(hisRequestVersion) && ("2".equals(hisRequestVersion) || "1".equals(hisRequestVersion))) {
					requestParams.put("IsCompress", "false");
					requestParams.put("ServiceName", "IOutsideInterfaceService");//服务名
					requestParams.put("InterfaceName", "QueryInpEmrFilePdfInfo");//接口名
					requestParams.put("TimeOut", 15000);//超时时间
					
					Map<String,Object> paramsMap = new HashMap<>();
					
					if( "1".equals(orgType)) {
						paramsMap.put("ORG_CODE", commInterfaceRegister.getPlatformOrgCode());
					}else {
						paramsMap.put("ORG_CODE", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					if(!StringUtils.isEmpty(parame.getHospCode())) {
						paramsMap.put("HOSP_CODE", parame.getHospCode());//院区需要前段传输
					}else {
						MedHisEmployee hisEmployee = medHisEmployeeMapper.selectHisEmployee(UserInfoHolder.getCurrentUserCode(),null);
						paramsMap.put("HOSP_CODE", hisEmployee.getHospCode());
					}
					paramsMap.put("INPATIENT_ID", parame.getPatnId());//住院病人id
					paramsMap.put("TEMPLATE_FILETYPE", parame.getTemplateFiletype());//模板病历文件类型((0:普通病历 1：一般护理记录单 2.血糖 3.文书 4.评估单(竖版) 5.评估单(横版)))
					paramsMap.put("EMR_ID", parame.getEmrId());//病历文件ID
					if(StringUtils.isEmpty(parame.getSeeType())) {
						paramsMap.put("SEE_TYPE", 0);//返回文件流类型（0:pdf 1:html）
					}else {
						paramsMap.put("SEE_TYPE", parame.getSeeType());//住院病人id
					}
					//paramsMap.put("IsSelectNur", "1");
			       	
			       	requestParams.put("Parameter",paramsMap);
			       	
				}else {
					
					if( "1".equals(orgType)) {
						requestParams.put("ORG_CODE", commInterfaceRegister.getPlatformOrgCode());
					}else {
						requestParams.put("ORG_CODE", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					if(!StringUtils.isEmpty(parame.getHospCode())) {
						requestParams.put("HOSP_CODE", parame.getHospCode());//院区需要前段传输
					}else {
						MedHisEmployee hisEmployee = medHisEmployeeMapper.selectHisEmployee(UserInfoHolder.getCurrentUserCode(),null);
						requestParams.put("HOSP_CODE", hisEmployee.getHospCode());
						
					}
					requestParams.put("INPATIENT_ID", parame.getPatnId());//住院病人id
					requestParams.put("TEMPLATE_FILETYPE", parame.getTemplateFiletype());//模板病历文件类型((0:普通病历 1：一般护理记录单 2.血糖 3.文书 4.评估单(竖版) 5.评估单(横版)))
					requestParams.put("EMR_ID", parame.getEmrId());//病历文件ID
					if(StringUtils.isEmpty(parame.getSeeType())) {
						requestParams.put("SEE_TYPE", 0);//返回文件流类型（0:pdf 1:html）
					}else {
						requestParams.put("SEE_TYPE", parame.getSeeType());//住院病人id
					}
					//requestParams.put("IsSelectNur", "1");
				}
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress(); //+ "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				log.info("返回参数：" + bodyStr);
				JSONObject reusltObject = JSON.parseObject(bodyStr);
				
				JSONObject dataObject = new JSONObject();
				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
					JSONObject data = reusltObject.getJSONObject("data");
					JSONObject Value = data.getJSONObject("Value");
					dataObject = Value.getJSONObject("Data");//获取list数据
				}else {
					 dataObject = reusltObject.getJSONObject("Data");//获取list数据
				}
				//JSONObject dataObject = reusltObject.getJSONObject("Data");//获取list数据
					 // 定义日期格式
		            //SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						Map<String,Object> map = new HashMap<>();
						map.put("EMR_ID", dataObject.get("EMR_ID"));//病历文件ID
						map.put("TEMPLATE_FILETYPE", dataObject.get("TEMPLATE_FILETYPE"));//模板病历文件类型(0:普通病历 1：一般护理记录单 2.血糖 3.文书 4.评估单(竖版) 5.评估单(横版))
						map.put("SEE_TYPE", dataObject.get("SEE_TYPE"));//返回文件流类型（0:pdf 1:html）
						map.put("BASESTR", dataObject.get("BASESTR"));//病历文件BASE64数据流
		       			result.add(map);
				//long endTime = System.currentTimeMillis();
			}
		}
		return result;
	}
	
	
	@Override
	public DataSet<Map<String,Object>> getQueryInPatientAccountInfo(Page page, PatientInfoInParameter parame) {
		// TODO Auto-generated method stub
		//long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询住院病人账目信息");//住院患者基本信息
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);//查询配置信息
		List<Map<String,Object>> result = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				//平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构
				String orgType = medHisEmployeeMapper.selectPlatformOrgType();
				
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				Map<String,Object> requestParams = new HashMap<>();
				
				//hisRequestVersion  1通山版本  2文山版本  其他的是标准版本
				if(StringUtils.isNotBlank(hisRequestVersion) && ("2".equals(hisRequestVersion) || "1".equals(hisRequestVersion))) {
					requestParams.put("IsCompress", "false");
					requestParams.put("ServiceName", "OuterPatPayService");//服务名
					requestParams.put("InterfaceName", "QueryInPatientAccountInfo");//接口名
					requestParams.put("TimeOut", 15000);//超时时间
					
					Map<String,Object> paramsMap = new HashMap<>();
					
					if( "1".equals(orgType)) {
						paramsMap.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						paramsMap.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					if(!StringUtils.isEmpty(parame.getPatnId())) {
						paramsMap.put("inPatientIds", Arrays.asList(parame.getPatnId().split(",")));//住院病人id集合
					}
					paramsMap.put("pageIndex", page.getPageNo());
					paramsMap.put("pageSize", page.getPageSize());
			       	
			       	requestParams.put("Parameter",paramsMap);
			       	
				}else {
					
					if( "1".equals(orgType)) {
						requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						requestParams.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					if(!StringUtils.isEmpty(parame.getPatnId())) {
						requestParams.put("inPatientIds", Arrays.asList(parame.getPatnId().split(",")));//住院病人id集合
					}
					requestParams.put("pageIndex", page.getPageNo());
					requestParams.put("pageSize", page.getPageSize());
				}
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				log.info("返回参数：" + bodyStr);
				JSONObject reusltObject = JSON.parseObject(bodyStr);
				
				JSONArray jsonArray = null;
				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
					JSONObject data = reusltObject.getJSONObject("data");
					JSONObject Value = data.getJSONObject("Value");
					jsonArray = Value.getJSONArray("list");
					Integer totalCount = Value.getInteger("totalCount");
			   		page.setTotalCount(totalCount);
				}else {
					jsonArray = reusltObject.getJSONArray("list");//获取list数据
					Integer totalCount = reusltObject.getInteger("totalCount");
			   		page.setTotalCount(totalCount);
				}
				if(null != jsonArray && jsonArray.size() > 0) {
					 // 定义日期格式
		            //SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					for (int i = 0; i < jsonArray.size(); i++) {
						Map<String,Object> map = new HashMap<>();
						JSONObject obj = jsonArray.getJSONObject(i);
						map.put("inPatientId", obj.get("inPatientId"));//住院病人id
						map.put("sumFee", obj.get("sumFee"));//总费用
						map.put("sumDeposits", obj.get("sumDeposits"));//总预交
						map.put("oweLimit", obj.get("oweLimit"));//欠费限额
						map.put("insurePay", obj.get("insurePay"));//医保报销
		       			result.add(map);
					}
				}
				//long endTime = System.currentTimeMillis();
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), result);
	}
	
	
	@Override
	public Map<String,Object> getQueryInPatFeeItemSum(PatientInfoInParameter parame) {
		// TODO Auto-generated method stub
		//long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询住院清单费用汇总");//查询住院清单费用汇总
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);//查询配置信息
		Map<String,Object> result = new HashMap<>();;
		List<Map<String,Object>> resultList = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				//平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构
				String orgType = medHisEmployeeMapper.selectPlatformOrgType();
				
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				Map<String,Object> requestParams = new HashMap<>();
				
				//hisRequestVersion  1通山版本  2文山版本  其他的是标准版本
				if(StringUtils.isNotBlank(hisRequestVersion) && ("2".equals(hisRequestVersion) || "1".equals(hisRequestVersion))) {
					requestParams.put("IsCompress", "false");
					requestParams.put("ServiceName", "OuterPatPayService");//服务名
					requestParams.put("InterfaceName", "QueryInPatFeeItemSum");//接口名
					requestParams.put("TimeOut", 15000);//超时时间
					
					Map<String,Object> paramsMap = new HashMap<>();
					
					if( "1".equals(orgType)) {
						paramsMap.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						paramsMap.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					if(!StringUtils.isEmpty(parame.getPatnId())) {
						paramsMap.put("inPatientId", parame.getPatnId());//住院病人id
					}
			       	
			       	requestParams.put("Parameter",paramsMap);
			       	
				}else {
					
					if( "1".equals(orgType)) {
						requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						requestParams.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					if(!StringUtils.isEmpty(parame.getPatnId())) {
						requestParams.put("inPatientId", parame.getPatnId());//住院病人id
					}
				}
				
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				log.info("返回参数：" + bodyStr);
				JSONObject reusltObject = JSON.parseObject(bodyStr);
				
				JSONArray jsonArray = null;
				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
					JSONObject data = reusltObject.getJSONObject("data");
					JSONObject Value = data.getJSONObject("Value");
					jsonArray = Value.getJSONArray("list");
					result.put("totalDepositMoney",Value.getString("totalDepositMoney"));//总预交金额
					result.put("totalFeeMoney",Value.getString("totalFeeMoney"));//总费用金额
				}else {
					jsonArray = reusltObject.getJSONArray("list");//获取list数据
					result.put("totalDepositMoney",reusltObject.getString("totalDepositMoney"));//总预交金额
					result.put("totalFeeMoney",reusltObject.getString("totalFeeMoney"));//总费用金额
				}
				if(null != jsonArray && jsonArray.size() > 0) {
					 // 定义日期格式
		            //SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					for (int i = 0; i < jsonArray.size(); i++) {
						Map<String,Object> map = new HashMap<>();
						JSONObject obj = jsonArray.getJSONObject(i);
						map.put("feeTypeCode", obj.get("feeTypeCode"));//费用分类编码
						map.put("feeTypeName", obj.get("feeTypeName"));//费用分类名称
						map.put("money", obj.get("money"));//费用分类金额
						resultList.add(map);
					}
				}
				//long endTime = System.currentTimeMillis();
				
				result.put("resultList",resultList);
			}
		}
		return result;
	}
	
	@Override
	public DataSet<Map<String,Object>> getQueryInPatientOperationRecord(Page page, PatientInfoInParameter parame) {
		// TODO Auto-generated method stub
		//long startTime = System.currentTimeMillis();    
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询手术记录");//住院患者基本信息
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);//查询配置信息
		List<Map<String,Object>> result = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				//平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构
				String orgType = medHisEmployeeMapper.selectPlatformOrgType();
				
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				Map<String,Object> requestParams = new HashMap<>();
				
				//hisRequestVersion  1通山版本  2文山版本  其他的是标准版本
				if(StringUtils.isNotBlank(hisRequestVersion) && ("2".equals(hisRequestVersion) || "1".equals(hisRequestVersion))) {
					requestParams.put("IsCompress", "false");
					requestParams.put("ServiceName", "OuterClinicService");//服务名
					requestParams.put("InterfaceName", "QueryInPatientOperationRecord");//接口名
					requestParams.put("TimeOut", 15000);//超时时间
					
					Map<String,Object> paramsMap = new HashMap<>();
					
					if( "1".equals(orgType)) {
						paramsMap.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						paramsMap.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					if(!StringUtils.isEmpty(parame.getPatnId())) {
						paramsMap.put("inPatientIds", Arrays.asList(parame.getPatnId().split(",")));//住院病人id集合
					}
					paramsMap.put("pageIndex", page.getPageNo());
					paramsMap.put("pageSize", page.getPageSize());
			       	
			       	requestParams.put("Parameter",paramsMap);
			       	
				}else {
					
					if( "1".equals(orgType)) {
						requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						requestParams.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					if(!StringUtils.isEmpty(parame.getPatnId())) {
						requestParams.put("inPatientIds", Arrays.asList(parame.getPatnId().split(",")));//住院病人id集合
					}
					requestParams.put("pageIndex", page.getPageNo());
					requestParams.put("pageSize", page.getPageSize());
				}
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				log.info("返回参数：" + bodyStr);
				JSONObject reusltObject = JSON.parseObject(bodyStr);
				
				JSONArray jsonArray = null;
				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
					JSONObject data = reusltObject.getJSONObject("data");
					JSONObject Value = data.getJSONObject("Value");
					jsonArray = Value.getJSONArray("list");
					Integer totalCount = Value.getInteger("totalCount");
			   		page.setTotalCount(totalCount);
				}else {
					jsonArray = reusltObject.getJSONArray("list");//获取list数据
					Integer totalCount = reusltObject.getInteger("totalCount");
			   		page.setTotalCount(totalCount);
				}
				if(null != jsonArray && jsonArray.size() > 0) {
					 // 定义日期格式
		            //SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					for (int i = 0; i < jsonArray.size(); i++) {
						Map<String,Object> map = new HashMap<>();
						JSONObject obj = jsonArray.getJSONObject(i);
						map.put("inPatientId", obj.get("inPatientId"));//住院病人id
						map.put("inPatientNo", obj.get("inPatientNo"));//住院号
						map.put("name", obj.get("name"));//病人姓名
						map.put("age", obj.get("age"));//年龄
						map.put("operationName", obj.get("operationName"));//手术名称
						map.put("surgicalLevelName", obj.get("surgicalLevelName"));//手术等级名称
						map.put("operationTime", obj.get("operationTime"));//手术时间
						map.put("applicationDate", obj.get("applicationDate"));//手术申请时间
						map.put("apllicationDeptName", obj.get("apllicationDeptName"));//申请科室名称
						map.put("applicationDoctorName", obj.get("applicationDoctorName"));//申请医生姓名
						map.put("surgeonName", obj.get("surgeonName"));//主刀医生姓名
						map.put("operationRoomName", obj.get("operationRoomName"));//手术室名称
		       			result.add(map);
					}
				}
				//long endTime = System.currentTimeMillis();
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), result);
	}
	
	@Override
	public DataSet<Map<String,Object>> getQueryInPatient(Page page, PatientInfoInParameter parame) {
		// TODO Auto-generated method stub
		//long startTime = System.currentTimeMillis();    
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询住院患者");//住院患者基本信息
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);//查询配置信息
		List<Map<String,Object>> result = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				//平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构
				String orgType = medHisEmployeeMapper.selectPlatformOrgType();
				
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				Map<String,Object> requestParams = new HashMap<>();
				
				//hisRequestVersion  1通山版本  2文山版本  其他的是标准版本
				if(StringUtils.isNotBlank(hisRequestVersion) && ("2".equals(hisRequestVersion) || "1".equals(hisRequestVersion))) {
					requestParams.put("IsCompress", "false");
					requestParams.put("ServiceName", "OuterPatPayService");//服务名
					requestParams.put("InterfaceName", "QueryInPatient");//接口名
					requestParams.put("TimeOut", 15000);//超时时间
					
					Map<String,Object> paramsMap = new HashMap<>();
					
					if( "1".equals(orgType)) {
						paramsMap.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						paramsMap.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					if("2".equals(orgType)) {
						//紧密型 需要传院区查询
						MedHisEmployee hisEmployee = medHisEmployeeMapper.selectHisEmployee(UserInfoHolder.getCurrentUserCode(), null);
						paramsMap.put("hospcode", hisEmployee.getHospCode());
					}
					
					if(!StringUtils.isEmpty(parame.getPatnId())) {
						paramsMap.put("ids", Arrays.asList(parame.getPatnId().split(",")));//住院病人id集合
					}
					if(!StringUtils.isEmpty(parame.getStatuss())) {
						paramsMap.put("statuss", Arrays.asList(parame.getStatuss().split(",")));//住院病人状态集合  //查询在院状态的住院患者
					}
					if(StringUtils.isEmpty(parame.getSortOrder())) {
						paramsMap.put("sortOrder", "0");//0-按创建日期 1-按入院日期 默认 0
					}else {
						paramsMap.put("sortOrder", parame.getSortOrder());
					}
					if(!StringUtils.isEmpty(parame.getName())) {
						paramsMap.put("name", parame.getName());//姓名查询条件
					}
					if(!StringUtils.isEmpty(parame.getPatnNo())) {
						paramsMap.put("inPatientNo", parame.getPatnNo());//住院号
					}
					if(!StringUtils.isEmpty(parame.getBedDocId())) {
						///查询当前登录人、对应HIS系统ID、暂时未做
						String sysEmpId = mapper.getHisEmpIdByRyys(parame.getBedDocId(),"HIS");
						if(StringUtils.isNotEmpty(sysEmpId)) {
							paramsMap.put("bedDocId", sysEmpId);//管床医生，查询自己的病人
						}else {
							paramsMap.put("bedDocId", parame.getBedDocId());//管床医生,未找到映射关系，传OA的
						}
					}
					if(!StringUtils.isEmpty(parame.getStartInDate()) && !StringUtils.isEmpty(parame.getEndInDate()) ) {
						paramsMap.put("startInDate", parame.getStartInDate()+" 00:00:00");//开始入院日期
						paramsMap.put("endInDate", parame.getEndInDate()+" 23:59:59");//结束入院日期
					}
					if(!StringUtils.isEmpty(parame.getStartOutDate()) && !StringUtils.isEmpty(parame.getEndOutDate()) ) {
						paramsMap.put("startOutDate", parame.getStartOutDate()+" 00:00:00");//开始出院日期
						paramsMap.put("endOutDate", parame.getEndOutDate()+" 23:59:59");//结束出院日期
					}
					paramsMap.put("pageIndex", 1);
					paramsMap.put("pageSize", 10000);
					if(!StringUtils.isEmpty(parame.getDeptIds())) {
						//List<String> deptIds = mapper.getHisDeptIds(Arrays.asList(parame.getDeptIds().split(",")));
						List<String> deptIds = mapper.getHisDeptIdsByksys(Arrays.asList(parame.getDeptIds().split(",")),"HIS");
						if(CollectionUtils.isNotEmpty(deptIds)) {
							paramsMap.put("deptIds", deptIds);
						}else {
							paramsMap.put("deptIds", Arrays.asList(parame.getDeptIds().split(",")));
						}
					}
			       	requestParams.put("Parameter",paramsMap);
			       	
				}else {
					
					if( "1".equals(orgType)) {
						requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					}else {
						requestParams.put("orgCode", UserInfoHolder.getCurrentUserCorpCode());
					}
					
					if("2".equals(orgType)) {
						//紧密型 需要传院区查询
						MedHisEmployee hisEmployee = medHisEmployeeMapper.selectHisEmployee(UserInfoHolder.getCurrentUserCode(), null);
						requestParams.put("hospcode", hisEmployee.getHospCode());
					}
					
					if(!StringUtils.isEmpty(parame.getPatnId())) {
						requestParams.put("ids", Arrays.asList(parame.getPatnId().split(",")));//住院病人id集合
					}
					if(!StringUtils.isEmpty(parame.getStatuss())) {
						requestParams.put("statuss", Arrays.asList(parame.getStatuss().split(",")));//住院病人状态集合  //查询在院状态的住院患者
					}
					if(StringUtils.isEmpty(parame.getSortOrder())) {
						requestParams.put("sortOrder", "0");//0-按创建日期 1-按入院日期 默认 0
					}else {
						requestParams.put("sortOrder", parame.getSortOrder());
					}
					if(!StringUtils.isEmpty(parame.getName())) {
						requestParams.put("name", parame.getName());//姓名查询条件
					}
					if(!StringUtils.isEmpty(parame.getPatnNo())) {
						requestParams.put("inPatientNo", parame.getPatnNo());//住院号
					}
					if(!StringUtils.isEmpty(parame.getBedDocId())) {
						///查询当前登录人、对应HIS系统ID、暂时未做
						String sysEmpId = mapper.getHisEmpIdByRyys(parame.getBedDocId(),"HIS");
						if(StringUtils.isNotEmpty(sysEmpId)) {
							requestParams.put("bedDocId", sysEmpId);//管床医生，查询自己的病人
						}else {
							requestParams.put("bedDocId", parame.getBedDocId());//管床医生,未找到映射关系，传OA的
						}
					}
					if(!StringUtils.isEmpty(parame.getStartInDate()) && !StringUtils.isEmpty(parame.getEndInDate()) ) {
						requestParams.put("startInDate", parame.getStartInDate()+" 00:00:00");//开始入院日期
						requestParams.put("endInDate", parame.getEndInDate()+" 23:59:59");//结束入院日期
					}
					if(!StringUtils.isEmpty(parame.getStartOutDate()) && !StringUtils.isEmpty(parame.getEndOutDate()) ) {
						requestParams.put("startOutDate", parame.getStartOutDate()+" 00:00:00");//开始出院日期
						requestParams.put("endOutDate", parame.getEndOutDate()+" 23:59:59");//结束出院日期
					}
					requestParams.put("pageIndex", 1);
					requestParams.put("pageSize", 10000);
					if(!StringUtils.isEmpty(parame.getDeptIds())) {
						//List<String> deptIds = mapper.getHisDeptIds(Arrays.asList(parame.getDeptIds().split(",")));
						List<String> deptIds = mapper.getHisDeptIdsByksys(Arrays.asList(parame.getDeptIds().split(",")),"HIS");
						if(CollectionUtils.isNotEmpty(deptIds)) {
							requestParams.put("deptIds", deptIds);
						}else {
							requestParams.put("deptIds", Arrays.asList(parame.getDeptIds().split(",")));
						}
						
					}
				}
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				JSONObject reusltObject = JSON.parseObject(bodyStr);
				
				JSONArray jsonArray = null;
				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
					JSONObject data = reusltObject.getJSONObject("data");
					JSONObject Value = data.getJSONObject("Value");
					jsonArray = Value.getJSONArray("list");
					Integer totalCount = Value.getInteger("totalCount");
			   		page.setTotalCount(totalCount);
				}else {
					jsonArray = reusltObject.getJSONArray("list");//获取list数据
					Integer totalCount = reusltObject.getInteger("totalCount");
			   		page.setTotalCount(totalCount);
				}
				if(null != jsonArray && jsonArray.size() > 0) {
					 // 定义日期格式
		            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
					// 定义日期格式
		            //DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
					for (int i = 0; i < jsonArray.size(); i++) {
						Map<String,Object> map = new HashMap<>();
						JSONObject obj = jsonArray.getJSONObject(i);
						
						map.put("orgCode", obj.getString("orgCode"));//医院编码
						map.put("orgName", obj.getString("orgName"));//医院名称
						map.put("patnId", obj.getString("id"));//住院病人ID(每次住院唯一id)
						map.put("patientId", obj.getString("patientId"));//病人ID
						map.put("inPatientNo", obj.getString("inPatientNo"));//住院号
						map.put("inTimes", obj.getString("inTimes"));//住院次数
						map.put("idCard", obj.getString("idCard"));//身份证号
						map.put("name", obj.getString("name"));//姓名
						map.put("sex", obj.getString("sex"));//性别
						map.put("sexName", obj.getString("sexName"));//性别
						map.put("contactPhone", obj.getString("contactPhone"));//联系电话
						map.put("address", obj.getString("address"));//地址
						map.put("birthDay", obj.getString("birthDay"));//出生日期
						map.put("age", obj.getString("age"));//年龄
						map.put("babyFlag", obj.getString("babyFlag"));//婴儿标志-1-普通大人 0-母亲 1-baby
						map.put("babyNum", obj.getString("babyNum"));//婴儿编号 
						map.put("motherId", obj.getString("motherId"));//母亲住院id：婴儿必须有值，大人为空
						map.put("inDate", obj.getString("inDate"));//入院日期
						map.put("outDate", obj.getString("outDate"));//出院日期
						map.put("mainDocId", obj.getString("mainDocId"));//主治医生
						map.put("mainDocName", obj.getString("mainDocName"));//主治医生姓名
						map.put("bedDocId", obj.getString("bedDocId"));//管床医生
						map.put("bedDocName", obj.getString("bedDocName"));//管床医生姓名
						map.put("chargeNurseId", obj.getString("chargeNurseId"));//责任护士
						map.put("chargeNurseName", obj.getString("chargeNurseName"));//责任护士姓名
						map.put("inDept", obj.getString("inDept"));//入院科室
						map.put("inDeptName", obj.getString("inDeptName"));//入院科室姓名
						map.put("deptId", obj.getString("deptId"));//当前科室id
						map.put("deptName", obj.getString("deptName"));//当前科室姓名
						map.put("hospCode", obj.getString("hospCode"));//院区
						map.put("hospName", obj.getString("hospName"));//院区名称
						map.put("inDiagnosisName", obj.getString("inDiagnosisName"));//入院诊断
						map.put("outDiagnosisName", obj.getString("outDiagnosisName"));//出院诊断
						map.put("bedNo", obj.getString("bedNo"));//床位号
						map.put("medicType", obj.getString("medicType"));//医保类型编码
						map.put("medicTypeName", obj.getString("medicTypeName"));//医保类型名称
						map.put("status", obj.getString("status"));//1-待入科室 3-在科室 4-出院医嘱 5-出区 6-结算 7-欠费结算 10-注销(入院)
						map.put("outMode", obj.getString("outMode"));//出院方式：1治愈、2好转、3未愈、4死亡、5其他
						map.put("outModeName", obj.getString("outModeName"));//出院状态名称
						map.put("orderTendLevel", obj.getString("orderTendLevel"));//护理级别编码
						map.put("orderTendLevelName", obj.getString("orderTendLevelName"));//护理级别名称
						map.put("patientCondition", obj.getString("patientCondition"));//病人病情
						map.put("orderBz", obj.getString("orderBz"));//病重医嘱id
						map.put("orderBw", obj.getString("orderBw"));//病危医嘱id
						/*
						// 获取今天的日期
				        LocalDate today = LocalDate.now();
						// 将字符串转换为LocalDate
				        if(!StringUtils.isEmpty(obj.getString("inDate"))) {
				        	LocalDateTime givenDate = LocalDateTime.parse(obj.getString("inDate"), formatter);
					        // 比较给定日期和今天的日期
					        if (givenDate.toLocalDate().equals(today)) {
					        	map.put("is_new_patient", "1");//新入
					        }else {
					        	map.put("is_new_patient", "0");
					        }
				        }*/
						if(!StringUtils.isEmpty(obj.getString("inDate"))) {
							try {
								Date inDate =  dateFormat.parse(obj.getString("inDate"));
								String  inDateString =  dateFormat.format(inDate);
								if (inDateString.equals(dateFormat.format(new Date()))) {//入院日期等于当日的算新入
						        	map.put("is_new_patient", "1");//新入
						        }else {
						        	map.put("is_new_patient", "0");
						        }
							} catch (java.text.ParseException e) {
								e.printStackTrace();
							}
						}else {
							map.put("is_new_patient", "0");
						}
						
						map.put("WzStatus", "0");//非危重患者
				        if(StringUtils.isEmpty(obj.getString("orderBz"))) {
				        	map.put("is_bz", "0");
				        }else {
				        	map.put("is_bz", "1");//病重
				        	map.put("WzStatus", "2");//病重
				        	
				        }
				        if(StringUtils.isEmpty(obj.getString("orderBw"))) {
				        	map.put("is_bw", "0");
				        }else {
				        	map.put("is_bw", "1");//病危
				        	map.put("WzStatus", "1");//病危
				        }
				        if(!StringUtils.isEmpty(obj.getString("outMode")) && "4".equals(obj.getString("outMode"))) {
				        	map.put("is_death", "1");//死亡
				        }else {
				        	map.put("is_death", "0");
				        }
				        if(!StringUtils.isEmpty(obj.getString("babyFlag")) && "0".equals(obj.getString("babyFlag"))) {
				        	map.put("is_birth", "1");//分娩
				        }else {
				        	map.put("is_birth", "0");
				        }
				        
				        
						if(!StringUtils.isEmpty(parame.getType())) {
							if("0".equals(parame.getType())) {//0新入,1病危、2病重、3死亡，4分娩
								if("1".equals(map.get("is_new_patient").toString())) {
									result.add(map);
						        }
							}else if("1".equals(parame.getType())) {//0新入,1病危、2病重、3死亡，4分娩
								if("1".equals(map.get("is_bw").toString())) {
									result.add(map);
						        }
							}else if("2".equals(parame.getType())) {//0新入,1病危、2病重、3死亡，4分娩
								if("1".equals(map.get("is_bz").toString())) {
									result.add(map);
						        }
							}else if("3".equals(parame.getType())) {//0新入,1病危、2病重、3死亡，4分娩
								if("1".equals(map.get("is_death").toString())) {
									result.add(map);
						        }
							}else if("4".equals(parame.getType())) {//0新入,1病危、2病重、3死亡，4分娩
								if("1".equals(map.get("is_birth").toString())) {
									result.add(map);
						        }
							}else if("-1".equals(parame.getType())) {//-1普通,0新入,1病危、2病重、3死亡，4分娩
								if("0".equals(map.get("is_new_patient").toString()) && "0".equals(map.get("is_bw").toString()) 
										&& "0".equals(map.get("is_bz").toString()) && "0".equals(map.get("is_death").toString()) 
										&& "0".equals(map.get("is_birth").toString())) {
									result.add(map);
						        }
							}
							
						}else if(StringUtil.isNotEmpty(parame.getBedDocId())) {
							//移动端 我的患者
							if(parame.getBedDocId().equals(map.get("bedDocId"))) {
								result.add(map);
							}
							
							patientFilter(parame, result, map);
							
						} else if(StringUtil.isNotEmpty(parame.getWzStatus()) || StringUtil.isNotEmpty(parame.getOrderTendLevel())) {
							
							//移动端 全科患者
							patientFilter(parame, result, map);
							
						}  else {
							result.add(map);	//查全部患者
						}
					}
				}
			}
		}
		
		
		List<Map<String,Object>> pageResult = result.stream().skip((page.getPageNo() - 1) * page.getPageSize()).limit(page.getPageSize()).collect(Collectors.toList());
		return new DataSet<>(page.getPageNo(), page.getPageSize(), pageResult.size(), result.size(), pageResult);
		
//		if(StringUtils.isEmpty(parame.getType())) {//查全部，用前段传的分页参数
//			return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), result);
//		}else {//查类型的，先查询全部存储分页
//			List<Map<String,Object>> pageResult = result.stream().skip((page.getPageNo() - 1) * page.getPageSize()).limit(page.getPageSize()).collect(Collectors.toList());
//			return new DataSet<>(page.getPageNo(), page.getPageSize(), pageResult.size(), result.size(), pageResult);
//		}
		//List<Map<String,Object>> pageResult = result.stream().skip((page.getPageNo() - 1) * page.getPageSize()).limit(page.getPageSize()).collect(Collectors.toList());
		//return new DataSet<>(page.getPageNo(), page.getPageSize(), pageResult.size(), result.size(), pageResult);
		//return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), result);
	}

	private void patientFilter(PatientInfoInParameter parame, List<Map<String, Object>> result,
			Map<String, Object> map) {
		
		if(StringUtil.isNotEmpty(parame.getWzStatus()) && parame.getWzStatus().contains("1")) {
			if("1".equals(map.get("is_bw").toString())) {
				result.add(map);
			}
		}
		
		if(StringUtil.isNotEmpty(parame.getWzStatus()) && parame.getWzStatus().contains("2")) {
			if("1".equals(map.get("is_bz").toString())) {
				result.add(map);
			}
		}
		
		if(StringUtil.isNotEmpty(parame.getOrderTendLevel()) && parame.getOrderTendLevel().contains("1")) {
			if(null != map.get("orderTendLevel") &&
					("1".equals(map.get("orderTendLevel").toString()) || "01".equals(map.get("orderTendLevel").toString()))) {
				result.add(map);
			}
		}
		if(StringUtil.isNotEmpty(parame.getOrderTendLevel()) && parame.getOrderTendLevel().contains("2")) {
			if(null != map.get("orderTendLevel") && 
					("2".equals(map.get("orderTendLevel").toString()) || "02".equals(map.get("orderTendLevel").toString()))) {
				result.add(map);
			}
		}
		if(StringUtil.isNotEmpty(parame.getOrderTendLevel()) && parame.getOrderTendLevel().contains("3")) {
			if(null != map.get("orderTendLevel") && 
					("3".equals(map.get("orderTendLevel").toString()) || "03".equals(map.get("orderTendLevel").toString()))) {
				result.add(map);
			}
		}
		if(StringUtil.isNotEmpty(parame.getOrderTendLevel()) && parame.getOrderTendLevel().contains("4")) {
			if(null != map.get("orderTendLevel") && 
					("4".equals(map.get("orderTendLevel").toString()) || "04".equals(map.get("orderTendLevel").toString()))) {
				result.add(map);
			}
		}
	}
	
	@Override
	public DataSet<PatientInfo> getPageList(Page page, PatientInfo record) {
		// TODO Auto-generated method stub
		
		//查询his 映射科室id
		if(StringUtils.isNotBlank(record.getDeptIds())) {
			List<String> hisOrgIds = hrmsOrganizationMapper.selectHisOrgId(ListUtil.of(record.getDeptIds().split(",")));
			
			if(CollUtil.isNotEmpty(hisOrgIds)) {
				record.setDeptIdList(hisOrgIds);
			}else {
				record.setDeptIdList(ListUtil.of(record.getDeptIds().split(",")));
			}
		}
		
		List<PatientInfo> records = mapper.getPageList(page, record);
		if(CollectionUtils.isNotEmpty(records)) {
			Map<String, String>  hospAreaMap  = convertDictMap("hosp_area");//获取院区字典map
			
			for(PatientInfo patientInfo : records) {
				
				//查询映射科室
				List<Map<String,String>> orgIds = hrmsOrganizationMapper.selectByPlatformId(patientInfo.getDeptId());
				if(CollectionUtils.isNotEmpty(orgIds)) {
					patientInfo.setOaOrgName(orgIds.get(0).get("oa_dept_name"));
				}
				
				patientInfo.setHospAreaText(hospAreaMap.get(patientInfo.getHospArea()));
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }

	@Override
	public List<PatientInfo> getPatientInfoDetails(PatientInfo record) {
		Example example = new Example(PatientInfo.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("cancelBit", "0");
		criteria.andIn("flag", ListUtil.of("2","3","4"));
		
		if(StrUtil.isNotBlank(record.getName())) {
			criteria.andLike("name", "%" + record.getName() + "%");
		}
		
		//需要过滤掉已同步过的患者
		List<MedShiftPatient> medShiftPatientList = medShiftPatientService.selectByRecordId(record.getShiftRecordId());
		List<String> patientIds = new ArrayList<>();
		for (MedShiftPatient medShiftPatient : medShiftPatientList) {
			patientIds.add(medShiftPatient.getPatientId());
		}
		if(CollUtil.isNotEmpty(patientIds)) {
			criteria.andNotIn("id", patientIds);
		}
		
		//根据登录人科室编码查询映射的his科室
		List<String> hisOrgIds = hrmsOrganizationMapper.selectHisOrgId(ListUtil.of(UserInfoHolder.getCurrentUserInfo().getDeptId()));
		
		criteria.andIn("deptId", hisOrgIds);
		
		return mapper.selectByExample(example);
	}
}

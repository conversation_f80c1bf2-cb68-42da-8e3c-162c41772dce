package cn.trasen.hrms.med.schedule.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.hrms.med.schedule.model.MedScheduleRecord;
import cn.trasen.hrms.med.schedule.model.MedScheduleTemplate;
import cn.trasen.hrms.med.schedule.model.MedScheduleTemplateExtend;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;
import tk.mybatis.mapper.common.Mapper;

public interface MedScheduleTemplateMapper extends Mapper<MedScheduleTemplate> {

	void batchInsert(List<MedScheduleTemplate> list);

	void deleteMedScheduleTemplateRecord(ScheduleEmployee record);
	
	List<MedScheduleRecord> selectMedScheduleTemplateRecordList(ScheduleEmployee record);
	
	MedScheduleTemplateExtend selectTemplateExtendByEmp(@Param("employeeNo")String employeeNo, @Param("ssoOrCode")String ssoOrCode);
}
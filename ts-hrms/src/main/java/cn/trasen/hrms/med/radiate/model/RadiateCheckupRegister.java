package cn.trasen.hrms.med.radiate.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import org.jeecgframework.poi.excel.annotation.Excel;

import lombok.*;

/**
 * @Description 体检登记
 * @date 2025-05-12 11:52:53
 * <AUTHOR> @version 1.0
 */
@Table(name = "med_radiate_checkup_register")
@Setter
@Getter
public class RadiateCheckupRegister {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 人员id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "人员id")
    private String employeeId;

    /**
     * 人员工号
     */
    @Excel(name = "工号*")
    @Column(name = "employee_no")
    @ApiModelProperty(value = "人员工号")
    private String employeeNo;

    /**
     * 人员姓名
     */
    @Excel(name = "姓名*")
    @Column(name = "employee_name")
    @ApiModelProperty(value = "人员姓名")
    private String employeeName;

    /**
     * 人员科室id
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "人员科室id")
    private String orgId;

    /**
     * 人员科室名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "人员科室名称")
    private String orgName;
    
    /**
     * 体检日期
     */
    @Excel(name = "体检日期(YYYY-MM-DD)*")
    @Column(name = "checkup_date")
    @ApiModelProperty(value = "体检日期")
    private String checkupDate;
    

    @Column(name = "expiration_date")
    @ApiModelProperty(value = "到期时间")
    private String expirationDate;

    /**
     * 状态:0正常,1异常
     */
    @Excel(name = "体检状态(正常/异常)*")
    @ApiModelProperty(value = "状态:0异常,1正常")
    private String status;

    /**
     * 体检结果
     */
    @Excel(name = "体检结果")
    @Column(name = "checkup_result")
    @ApiModelProperty(value = "体检结果")
    private String checkupResult;

    @Excel(name = "体检编号")
    @Column(name = "physical_no")
    @ApiModelProperty(value = "体检编号")
    private String physicalNo;

    @Excel(name = "体检机构")
    @Column(name = "physical_org")
    @ApiModelProperty(value = "体检机构")
    private String physicalOrg;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String files;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 创建人所属部门编码
     */
    @Column(name = "create_dept")
    @ApiModelProperty(value = "创建人所属部门编码")
    private String createDept;

    /**
     * 创建人所属部门名称
     */
    @Column(name = "create_dept_name")
    @ApiModelProperty(value = "创建人所属部门名称")
    private String createDeptName;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标记
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标记")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
    
    @Transient
    @ApiModelProperty(value = "复检次数")
    private Integer checkupAbnormalCount;
    
    @Transient
    @ApiModelProperty(value = "最新复查结果")
    private String checkupLatestResult;

    @Transient
    @ApiModelProperty(value = "状态")
    private String employeeStatus;
    
    @Transient
    @ApiModelProperty(value = "员工状态文本")
    private String employeeStatusText;
    
    @Transient
    @ApiModelProperty(value = "技术职称")
    private String technicalTitle;
    
    @Transient
    @ApiModelProperty(value = "性别")
    private String sex;
    
    @Transient
    @ApiModelProperty(value = "性别文本")
    private String sexText;
    
    @Transient
    @ApiModelProperty(value = "复查数据列表")
    private List<RadiateCheckupAbnormal> checkupAbnormalList;
}
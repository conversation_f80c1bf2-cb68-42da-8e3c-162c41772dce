package cn.trasen.hrms.med.radiate.dao;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;
import cn.trasen.hrms.med.radiate.vo.RadiatePersonnelReqVo;
import tk.mybatis.mapper.common.Mapper;

public interface RadiatePersonnelRegisterMapper extends Mapper<RadiatePersonnelRegister> {
	
	List<RadiatePersonnelRegister> selectRadiatePersonnelRegisterPageList(Page page, RadiatePersonnelReqVo record);
	
	List<RadiatePersonnelRegister> getList(RadiatePersonnelReqVo record);
}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.qualityApply.dao.MedQualityPunishMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.qualityApply.model.MedQualityPunish">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="quality_id" jdbcType="VARCHAR" property="qualityId" />
    <result column="punish_end_date" jdbcType="DATE" property="punishEndDate" />
    <result column="punish_result" jdbcType="VARCHAR" property="punishResult" />
    <result column="punish_date" jdbcType="VARCHAR" property="punishDate" />
    <result column="punish_remark" jdbcType="VARCHAR" property="punishRemark" />
  </resultMap>
</mapper>
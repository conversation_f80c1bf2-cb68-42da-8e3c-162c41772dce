<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.patient.dao.PatientOperationMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.patient.model.PatientOperation">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="patn_id" jdbcType="VARCHAR" property="patnId" />
    <result column="patn_no" jdbcType="VARCHAR" property="patnNo" />
    <result column="operation_date" jdbcType="TIMESTAMP" property="operationDate" />
    <result column="operation_name" jdbcType="VARCHAR" property="operationName" />
    <result column="operation_after_diagnosis" jdbcType="VARCHAR" property="operationAfterDiagnosis" />
    <result column="zdys" jdbcType="VARCHAR" property="zdys" />
    <result column="mzys" jdbcType="VARCHAR" property="mzys" />
    <result column="wcbj" jdbcType="VARCHAR" property="wcbj" />
    <result column="wcsj" jdbcType="TIMESTAMP" property="wcsj" />
    <result column="qklx" jdbcType="VARCHAR" property="qklx" />
    <result column="yxssdj" jdbcType="VARCHAR" property="yxssdj" />
    <result column="bdelete" jdbcType="VARCHAR" property="bdelete" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
</mapper>
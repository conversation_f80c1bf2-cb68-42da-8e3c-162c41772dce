package cn.trasen.hrms.med.radiate.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.bean.ResultData;
import cn.trasen.hrms.med.radiate.dao.RadiatePersonnelRegisterMapper;
import cn.trasen.hrms.med.radiate.dao.RadiateTrainingRegisterMapper;
import cn.trasen.hrms.med.radiate.emun.RegisterCategoryEmun;
import cn.trasen.hrms.med.radiate.model.DictConstant;
import cn.trasen.hrms.med.radiate.model.RadiateMonitor;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;
import cn.trasen.hrms.med.radiate.model.RadiateTrainingRegister;
import cn.trasen.hrms.med.radiate.service.RadiateMonitorService;
import cn.trasen.hrms.med.radiate.service.RadiateTrainingRegisterService;
import cn.trasen.hrms.med.radiate.util.MonitorCycleUtils;
import cn.trasen.hrms.med.radiate.vo.RadiatePersonnelReqVo;
import cn.trasen.hrms.med.radiate.vo.RadiateTrainingReqVo;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.service.BaseDictItemService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName RadiateTrainingRegisterServiceImpl
 * @Description TODO
 * @date 2025��1��8�� ����11:17:02
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class RadiateTrainingRegisterServiceImpl implements RadiateTrainingRegisterService {

	@Autowired
	private RadiateTrainingRegisterMapper mapper;

	@Autowired
	private BaseDictItemService baseDictItemService;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;
	
	@Resource
	private HrmsEmployeeService hrmsEmployeeService;
	
	@Autowired
	private RadiatePersonnelRegisterMapper radiatePersonnelRegisterMapper;
	
	@Autowired
	private RadiateMonitorService radiateMonitorService;
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(RadiateTrainingRegister record) {
		Assert.hasText(record.getTrainingType(), "培训类型不能为空.");
		Assert.hasText(record.getTrainingDate(), "培训日期不能为空.");
		//证件唯一性校验
		Example example = new Example(RadiateTrainingRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", record.getEmployeeId());
		criteria.andEqualTo("trainingDate", record.getTrainingDate());
		criteria.andEqualTo("trainingType", record.getTrainingType());
		List<RadiateTrainingRegister> records = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(records)){
			throw new BusinessException(StrUtil.format("该用户培训日期{}已登记了！", record.getTrainingDate()));
		}
		
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(RadiateTrainingRegister record) {
		Assert.hasText(record.getTrainingType(), "培训类型不能为空.");
		Assert.hasText(record.getTrainingDate(), "培训日期不能为空.");
		//证件唯一性校验
		Example example = new Example(RadiateTrainingRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", record.getEmployeeId());
		criteria.andEqualTo("trainingDate", record.getTrainingDate());
		criteria.andEqualTo("trainingType", record.getTrainingType());
		criteria.andNotEqualTo("id", record.getId());
		List<RadiateTrainingRegister> records = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(records)){
			throw new BusinessException(StrUtil.format("该用户培训日期{}已登记了！", record.getTrainingDate()));
		}
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		RadiateTrainingRegister record = new RadiateTrainingRegister();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public RadiateTrainingRegister selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<RadiateTrainingRegister> getDataSetList(Page page, RadiateTrainingRegister record) {
		Example example = new Example(RadiateTrainingRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<RadiateTrainingRegister> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public DataSet<RadiateTrainingRegister> selectRadiateTrainingRegisterPageList(Page page, RadiateTrainingReqVo record) {
		if(!ObjectUtils.isEmpty(record.getExpired())){
			//设置当前时间字符串
			SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-dd");
			record.setToday(sdf.format(new Date()));
		}
		List<RadiateTrainingRegister> records = mapper.selectRadiateTrainingRegisterPageList(page, record);
		Map<String, String>  trainingTypeMap  = baseDictItemService.convertDictMap(DictConstant.RADIATE_TRAINING_TYPE);//获取培训类型字典Map
		Map<String, String>  employeeStatusMap  = baseDictItemService.convertDictMap(DictConstant.EMPLOYEE_STATUS);//员工状态
		for(RadiateTrainingRegister item : records) {
			item.setEmployeeStatusText(employeeStatusMap.get(item.getEmployeeStatus()));
			item.setTrainingTypeText(trainingTypeMap.get(item.getTrainingType()));//根据key,获取字典values
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer saveOrUpdateList(List<RadiateTrainingRegister> records, List<RadiateMonitor> monitors, boolean isAdd, RadiatePersonnelRegister personnel) {
		if(CollUtil.isEmpty(records)){
			return 0;
		}
		Map<String, String> monitorMap = new HashMap<>();
		if(CollUtil.isNotEmpty(monitors)){
			for(RadiateMonitor m : monitors){
				if(!ObjectUtils.isEmpty(m.getRegisterType()) && !ObjectUtils.isEmpty(m.getMonitorCycle())){
					monitorMap.put(m.getRegisterType(), m.getMonitorCycle());
				}
			}
		}
		//新增
		if(isAdd){
			for (RadiateTrainingRegister record : records){
				//计算到期时间
				String trainingType = record.getTrainingType();
				if(monitorMap.containsKey(trainingType)){
					record.setExpirationDate(MonitorCycleUtils.calculateExpirationDate(record.getTrainingDate(), monitorMap.get(trainingType)));
				}
				record.setEmployeeId(personnel.getEmployeeId());
				record.setEmployeeNo(personnel.getEmployeeNo());
				record.setEmployeeName(personnel.getEmployeeName());
				save(record);
			}
		} else {
			//先查询该用户一共有几条数据
			List<RadiateTrainingRegister> oldRecords = selectByEmployeeId(personnel.getEmployeeId());
			//将更新的数据记录，用于后续排除删除
			Set<String> updateIds = new HashSet<>();
			//遍历新增的数据
			for (RadiateTrainingRegister record : records){
				//判断该用户是否已经存在该数据
				boolean flag = false;
				for (RadiateTrainingRegister oldRecord : oldRecords){
					if(record.getId().equals(oldRecord.getId())){
						flag = true;
						record.setId(oldRecord.getId());
						//只计算最新到期时间，并存入数据库-先判断到期时间是否已经超过当前，超过则按照之前的算，否则按照新的规则计算
						String trainingType = record.getTrainingType();
						if(ObjectUtils.isEmpty(oldRecord.getExpirationDate()) && monitorMap.containsKey(trainingType)){
							record.setExpirationDate(MonitorCycleUtils.calculateExpirationDate(record.getTrainingDate(), monitorMap.get(trainingType)));
						}
						record.setEmployeeId(personnel.getEmployeeId());
						record.setEmployeeNo(personnel.getEmployeeNo());
						record.setEmployeeName(personnel.getEmployeeName());
						update(record);
						updateIds.add(record.getId());
					}
				}
				if(!flag){
					//计算到期时间
					String trainingType = record.getTrainingType();
					if(monitorMap.containsKey(trainingType)){
						record.setExpirationDate(MonitorCycleUtils.calculateExpirationDate(record.getTrainingDate(), monitorMap.get(trainingType)));
					}
					record.setEmployeeId(personnel.getEmployeeId());
					record.setEmployeeNo(personnel.getEmployeeNo());
					record.setEmployeeName(personnel.getEmployeeName());
					save(record);
				}
			}
			//删除多余的数据
			if(CollUtil.isNotEmpty(oldRecords) && oldRecords.size() != updateIds.size()){
				for(RadiateTrainingRegister oldRecord : oldRecords){
					if(!updateIds.contains(oldRecord.getId())){
						deleteById(oldRecord.getId());
					}
				}
			}
		}
		return records.size();
	}

	@Override
	public List<RadiateTrainingRegister> selectByEmployeeId(String employeeId) {
		Example example = new Example(RadiateTrainingRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return mapper.selectByExample(example);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteByEmployeeId(String employeeId) {
		Assert.hasText(employeeId, "人员ID不能为空.");
		Example example = new Example(RadiateTrainingRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		
		RadiateTrainingRegister update = new RadiateTrainingRegister();
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		update.setIsDeleted(Contants.IS_DELETED_TURE);
		update.setUpdateDate(new Date());
		if (user != null) {
			update.setUpdateUser(user.getUsercode());
			update.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByExampleSelective(update, example);
	}

	@Transactional(readOnly = false)
	@Override
	public PlatformResult importData(List<RadiateTrainingRegister> list) {
		
		List<DictItemResp> trainingTypeDic = dictItemFeignService.getDictItemByTypeCode(DictConstant.RADIATE_TRAINING_TYPE).getObject(); //培训类型
		//查询所有的人
		HrmsEmployee  entity = new HrmsEmployee();
		List<HrmsEmployee> employeeList = hrmsEmployeeService.getList(entity);
		Map<String, HrmsEmployee> employeeMap = new HashMap<>();
		if(CollUtil.isNotEmpty(employeeList)){
			employeeMap = employeeList.stream().collect(Collectors.toMap(HrmsEmployee::getEmployeeNo, a -> a, (k1, k2) -> k1));
		}
		
		//查询所有的登记人员-用于去重处理
		List<RadiatePersonnelRegister> personnelList = radiatePersonnelRegisterMapper.getList(new RadiatePersonnelReqVo());
		Map<String, RadiatePersonnelRegister> personnelMap = new HashMap<>();
		if(CollUtil.isNotEmpty(personnelList)){
			personnelMap = personnelList.stream().collect(Collectors.toMap(RadiatePersonnelRegister::getEmployeeNo, a -> a, (k1, k2) -> k1));
		}
		
		//个人监控设置信息,key为工号和培训类型
		Map<String, String> personMonitorMap = new HashMap<>();
		List<RadiateMonitor> monitors = radiateMonitorService.selectAll();
		if(CollUtil.isNotEmpty(monitors)){
			for(RadiateMonitor m : monitors){
				if(m.getRegisterCategory().equals(RegisterCategoryEmun.TRAINING.getCode())){
					personMonitorMap.put(m.getEmployeeId() + m.getRegisterType(), m.getMonitorCycle());
				}
			}
		}

		//查询所有人的培训，校验 工号+培训类型+体检日期 唯一性
		List<RadiateTrainingRegister> trainingList = getList();
		Set<String> trainingSet = new HashSet<>();
		if(CollUtil.isNotEmpty(trainingList)){
			for(RadiateTrainingRegister c : trainingList){
				trainingSet.add(c.getEmployeeId() + c.getTrainingType() + c.getTrainingDate());
			}
		}
		
		List<String> fileIsnulls; //为空的字段
		List<String> fileDictNotFunds; //字典不能匹配字段
		
		List<ResultData> badList = new ArrayList<>();//失败信息
		
		Integer successCnt = 0;
		Integer errorCnt = 0;
		
		ResultData badData;
		DictItemResp dict;
		
		int i = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		for (RadiateTrainingRegister item : list) {
			
			i++;
			
			fileIsnulls = new ArrayList<String>();
			fileDictNotFunds = new ArrayList<String>();
			
			String employeeName = item.getEmployeeName();
			if(!StrUtil.isEmpty(employeeName)){
				item.setEmployeeName(employeeName.trim());
			}
			if(StrUtil.isEmpty(item.getEmployeeName())){
				fileIsnulls.add("姓名");
			}
			
			String employeeNo = item.getEmployeeNo();
			if(!StrUtil.isEmpty(employeeNo)){
				item.setEmployeeNo(employeeNo.trim());
			}
			if(StrUtil.isEmpty(item.getEmployeeNo())){
				fileIsnulls.add("工号");
			}
			
			String trainingType = item.getTrainingType();
			if(!StrUtil.isEmpty(trainingType)){
				item.setTrainingType(trainingType.trim());
			}
			if(StrUtil.isEmpty(item.getTrainingType())){
				fileIsnulls.add("培训类型");
			}
			
			String trainingDate = item.getTrainingDate();
			if(!StrUtil.isEmpty(trainingDate)){
				item.setTrainingDate(trainingDate.trim());
			}
			if(StrUtil.isEmpty(item.getTrainingDate())){
				fileIsnulls.add("培训日期");
			}
			
			if(CollUtil.isNotEmpty(fileIsnulls)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】为空!", i + 1,StrUtil.join("、", fileIsnulls)));
				badList.add(badData);
				errorCnt++;
				continue;
			}

			//判断是否已经登记
			employeeNo = item.getEmployeeNo();
			if(!personnelMap.containsKey(employeeNo)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】工号未登记!", i + 1 , employeeNo));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			//校验姓名和工号是否匹配
			if(!employeeMap.containsKey(item.getEmployeeNo())){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】工号不存在!", i + 1 , item.getEmployeeNo()));
				badList.add(badData);
				errorCnt++;
				continue;
			} else {
				HrmsEmployee emp = employeeMap.get(item.getEmployeeNo());
				if(!emp.getEmployeeName().equals(item.getEmployeeName())){
					badData = new ResultData();
					badData.setData(StrUtil.format("第【{}】行的【{}】姓名对应的工号不匹配!", i + 1 , item.getEmployeeName()));
					badList.add(badData);
					errorCnt++;
					continue;
				} else {
					item.setEmployeeId(emp.getEmployeeId());
					item.setEmployeeNo(emp.getEmployeeNo());
					item.setEmployeeName(emp.getEmployeeName());
				}
			}
			
			//校验数据字典
			dict = trainingTypeDic.stream().filter(j -> StrUtil.equals(item.getTrainingType(), j.getItemName())).findFirst().orElse(null);
			if(null == dict){
				fileDictNotFunds.add(StrUtil.format("培训类型->{}", item.getTrainingType()));
			} else {
				item.setTrainingType(dict.getItemNameValue());
			}
			
			if(CollUtil.isNotEmpty(fileDictNotFunds)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】无法匹配字典值!", i + 1,StrUtil.join("、", fileDictNotFunds)));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			//校验日期格式
			try {
				sdf.parse(item.getTrainingDate());
			} catch (ParseException e) {
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的培训日期格式为YYYY-MM-DD，【{}】不正确!", i + 1 , item.getTrainingDate()));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			//校验培训唯一性
			if(trainingSet.contains(item.getEmployeeId() + item.getTrainingType() + item.getTrainingDate())){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】培训已登记!", i + 1 , item.getTrainingDate()));
				badList.add(badData);
				errorCnt++;
				continue;
			} else {
				trainingSet.add(item.getEmployeeId() + item.getTrainingType() + item.getTrainingDate());
			}

			//计算到期时间
			if(personMonitorMap.containsKey(item.getEmployeeId() + item.getTrainingType())) {
				item.setExpirationDate(MonitorCycleUtils.calculateExpirationDate(item.getTrainingDate(), personMonitorMap.get(item.getEmployeeId() + item.getTrainingType())));
			}
			
			save(item);
			successCnt++;
			
		}
		
		if(CollUtil.isNotEmpty(badList)){
			return PlatformResult.failure(StrUtil.format("信息导入 ,总条数:{}、成功:{}、失败{}", list.size(),successCnt,errorCnt),badList);
		}else{
			return PlatformResult.success(StrUtil.format("信息导入 ,总条数:{}、成功:{}、失败{}", list.size(),successCnt,errorCnt));
		}
	}

	@Override
	public List<RadiateTrainingRegister> getList() {
		Example example = new Example(RadiateTrainingRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return mapper.selectByExample(example);
	}
}

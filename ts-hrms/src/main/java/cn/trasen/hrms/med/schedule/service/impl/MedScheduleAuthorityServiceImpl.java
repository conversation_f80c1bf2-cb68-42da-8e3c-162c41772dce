package cn.trasen.hrms.med.schedule.service.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.base.EmployeeListReq;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.base.HrmsOrganizationResp;
import cn.trasen.homs.bean.sso.UserRoleReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.PageDataReq;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.homs.feign.sso.RightFeignService;
import cn.trasen.hrms.med.schedule.dao.MedScheduleAuthorityMapper;
import cn.trasen.hrms.med.schedule.dao.MedScheduleTemplateMapper;
import cn.trasen.hrms.med.schedule.model.LeaveRecordVo;
import cn.trasen.hrms.med.schedule.model.MedScheduleAuthority;
import cn.trasen.hrms.med.schedule.model.MedScheduleRecord;
import cn.trasen.hrms.med.schedule.model.ScheduleDept;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;
import cn.trasen.hrms.med.schedule.model.ScheduleOrganization;
import cn.trasen.hrms.med.schedule.service.MedScheduleAuthorityService;
import cn.trasen.hrms.med.schedule.service.MedScheduleGroupUserService;
import cn.trasen.hrms.med.schedule.service.MedScheduleRecordService;
import cn.trasen.hrms.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedScheduleAuthorityServiceImpl
 * @Description TODO
 * @date 2025��3��29�� ����2:58:34
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedScheduleAuthorityServiceImpl implements MedScheduleAuthorityService {

	//排班管理员角色ID
	@Value("${roleCode-pb:1}")
	String pbRoleCode;

	@Autowired
	private MedScheduleAuthorityMapper mapper;
	
	@Autowired
	private MedScheduleRecordService medScheduleRecordService;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;
	
	@Autowired
	private MedScheduleGroupUserService medScheduleGroupUserService;
	
	@Autowired
	private MedScheduleTemplateMapper medScheduleTemplateMapper;
	
	@Autowired
	private HrmsEmployeeFeignService hrmsEmployeeFeignService;
	
	@Autowired
	private HrmsOrganizationFeignService hrmsOrganizationFeignService;

	@Autowired
	private RightFeignService rightService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedScheduleAuthority record) {
		
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		
		if(StringUtils.isNotBlank(record.getAutoGenerate())) {
			record.setManageName(user.getUsername());
			record.setManageUserCode(user.getUsercode());
		}
		
		Example example = new Example(MedScheduleAuthority.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andCondition("find_in_set('" + record.getManageUserCode() + "',manage_user_code)");
		List<MedScheduleAuthority> list = mapper.selectByExample(example);
		
		if(CollectionUtils.isEmpty(list)) {
			record.setId(IdGeneraterUtils.nextId());
			record.setCreateDate(new Date());
			record.setUpdateDate(new Date());
			record.setIsDeleted("N");
			
			if (user != null) {
				record.setCreateUser(user.getUsercode());
				record.setCreateUserName(user.getUsername());
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			return mapper.insertSelective(record);
		}else {
			//Assert.isTrue(false, record.getManageName() + "已存在,请勿重复添加!");
		}

		//校验使用范围的人员、人员所在科室，以及科室，是否在其他组
//		if(!record.isForceUpdate()){
//			Map<String, Map<String, String>> resultMap = validateUseRangeList(record);
//			StringBuffer message = new StringBuffer();
//			if(resultMap.containsKey("dept")){
//				Map<String, String> deptMap = resultMap.get("dept");
//				for(String dept : deptMap.keySet()){
//					message.append(",");
//					message.append(deptMap.get(dept));
//				}
//			}
//			if(resultMap.containsKey("user")){
//				Map<String, String> userMap = resultMap.get("user");
//				for(String userName : userMap.keySet()){
//					message.append(",");
//					message.append(userMap.get(userName));
//				}
//			}
//			String ss = message.toString();
//			if(!ObjectUtils.isEmpty(ss)){
//				ss = ss.substring(1);
//				throw new BusinessException(ss);
//			}
//		} else {
//			validateUseRangeList(record);
//		}
		
		//排班管理员关联SSO的排班管理员  pb
		String manageUserCode = record.getManageUserCode();//排班管理员用户
		if(!"1".equals(pbRoleCode) && !ObjectUtils.isEmpty(manageUserCode)){
			List<String> userCodeList = new ArrayList<>(Arrays.asList(manageUserCode.split(",")));
			PlatformResult<List<EmployeeResp>> resp = hrmsEmployeeFeignService.getEmployeeDetailByCodes(userCodeList);
			if(resp.isSuccess() && resp.getObject().size() > 0){
				UserRoleReq userRoleReq = new UserRoleReq();
				List<String> userIdList = new ArrayList<>();
				for(EmployeeResp emp : resp.getObject()){
					userIdList.add(emp.getEmployeeId());
				}
				userRoleReq.setUserIdList(userIdList);
				userRoleReq.setRoleIdList(Arrays.asList(pbRoleCode));
				rightService.doBatchUserRoleSave(userRoleReq);
			}
		}
		
		return 0;
	}
	
	/**
	 * 校验是否有重复使用范围
	 * @param record
	 * @return
	 */
	private Map<String, Map<String, String>> validateUseRangeList(MedScheduleAuthority record){
		Map<String, Map<String, String>> resultMap = new HashMap<>();
	    List<String> scheduleNameList = new ArrayList<String>();//使用范围名称
	    List<String> scheduleOrgList = new ArrayList<String>();//使用范围科室
	    List<String> scheduleOrgNameList = new ArrayList<String>();//使用范围科室名称
	    List<String> scheduleUserList = new ArrayList<String>();//使用范围人员
	    List<String> scheduleUserNameList = new ArrayList<String>();//使用范围人员姓名
	    
	    if(!ObjectUtils.isEmpty(record.getScheduleOrg())){
	    	scheduleOrgList = new ArrayList<>(Arrays.asList(record.getScheduleOrg().split(",")));
	    }
	    if(!ObjectUtils.isEmpty(record.getScheduleUser())){
	    	scheduleUserList = new ArrayList<>(Arrays.asList(record.getScheduleUser().split(",")));
	    }
	    if(!ObjectUtils.isEmpty(record.getScheduleName())){
	    	scheduleNameList = new ArrayList<>(Arrays.asList(record.getScheduleName().split(",")));
	    }
	    int orgSize = scheduleOrgList.size();
	    for(int i = 0; i < scheduleNameList.size(); i++){
	    	if(orgSize > 0 && i < orgSize){
	    		scheduleOrgNameList.add(scheduleNameList.get(i));
	    	} else {
	    		scheduleUserNameList.add(scheduleNameList.get(i));
	    	}
	    }
	    
	    //根据人员范围查询人员所属的机构编码
		if(scheduleUserList.size() > 0){
			PlatformResult<List<EmployeeResp>> resp = hrmsEmployeeFeignService.getEmployeeDetailByCodes(scheduleUserList);
			if(resp.isSuccess() && resp.getObject().size() > 0){
				for(EmployeeResp emp : resp.getObject()){
					String orgCode = emp.getOrgCode();
					if(!ObjectUtils.isEmpty(orgCode) && !scheduleOrgList.contains(orgCode)){
						scheduleOrgList.add(orgCode);
						scheduleOrgNameList.add(emp.getOrgName());
					}
				}
			}
		}
		Map<String, String> userNameMap = new HashMap<>();//所有的员工，用于更新剔除重复人员的名称
		Map<String, String> orgNameMap = new HashMap<>();//所有的科室，用于更新剔除重复科室的名称
		if(record.isForceUpdate()){
			//所有的员工，用于更新剔除重复人员的名称
			PageDataReq<EmployeeListReq> pageDataReq = new PageDataReq<EmployeeListReq>();
			EmployeeListReq req = new EmployeeListReq();
			pageDataReq.setData(req);
			pageDataReq.setPageNum(1);
			pageDataReq.setPageSize(Integer.MAX_VALUE);
			DataSet<EmployeeResp> userResp = hrmsEmployeeFeignService.getEmployeePageList(pageDataReq);
			List<EmployeeResp> userList = userResp.getRows();
			if(CollUtil.isNotEmpty(userList)){
				userNameMap = userList.stream().collect(Collectors.toMap(EmployeeResp::getEmployeeNo, EmployeeResp::getEmployeeName));
			}
			//获取所有的科室，用于更新剔除重复科室的名称
			PlatformResult<List<HrmsOrganizationResp>> orgResp = hrmsOrganizationFeignService.getOrgAllList();
			if(orgResp.isSuccess() && null != orgResp.getObject() && orgResp.getObject().size() > 0){
				List<HrmsOrganizationResp> orgList = orgResp.getObject();
				orgNameMap = orgList.stream().collect(Collectors.toMap(HrmsOrganizationResp::getCode, HrmsOrganizationResp::getName));
			}
		}
	    //获取其他权限组数据
	    Example example = new Example(MedScheduleAuthority.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(!ObjectUtil.isEmpty(record.getId())){
			criteria.andNotEqualTo("id", record.getId());	
		}
		List<MedScheduleAuthority> list = mapper.selectByExample(example);
	    if(CollUtil.isNotEmpty(list)){
	    	for(MedScheduleAuthority authority : list){
	    		for(int index = 0; index < scheduleOrgList.size(); index++){
	    			//检验科室重复
	    			if(containsString(authority.getScheduleOrg(), scheduleOrgList.get(index))){
	    				if(resultMap.containsKey("dept")){
	    					Map<String, String> deptMap = resultMap.get("dept");
	    					deptMap.put(scheduleOrgList.get(index), scheduleOrgNameList.get(index));
	    				} else {
	    					Map<String, String> deptMap = new HashMap<>();
	    					deptMap.put(scheduleOrgList.get(index), scheduleOrgNameList.get(index));
	    					resultMap.put("dept", deptMap);
	    				}
	    			}
	    		}
	    		for(int index = 0; index < scheduleUserList.size(); index++){
	    			//检验人员重复
	    			if(containsString(authority.getScheduleUser(), scheduleUserList.get(index))){
	    				if(resultMap.containsKey("user")){
	    					Map<String, String> deptMap = resultMap.get("user");
	    					deptMap.put(scheduleUserList.get(index), scheduleUserNameList.get(index));
	    				} else {
	    					Map<String, String> userMap = new HashMap<>();
	    					userMap.put(scheduleUserList.get(index), scheduleUserNameList.get(index));
	    					resultMap.put("user", userMap);
	    				}
	    			}
	    		}
	    		//更新剔除后的使用范围
	    		if(record.isForceUpdate()){
	    			updateAuthorityWithoutDuplicates(authority, scheduleOrgList, scheduleUserList, userNameMap, orgNameMap);
	    		}
	    	}
	    }
	    
		return resultMap;
	}
	
	/**
	 * 从权限记录中剔除重复的使用范围并更新到数据库
	 */
	private void updateAuthorityWithoutDuplicates(MedScheduleAuthority authority, List<String> duplicateOrgs, List<String> duplicateUsers, Map<String, String> userNameMap, Map<String, String> orgNameMap) {
	    try {
	        // 剔除重复的科室
	        String updatedOrg = removeDuplicates(authority.getScheduleOrg(), duplicateOrgs);
	        // 剔除重复的人员
	        String updatedUser = removeDuplicates(authority.getScheduleUser(), duplicateUsers);
	        
	        // 如果有更新，则保存到数据库
	        if (!Objects.equals(updatedOrg, authority.getScheduleOrg()) ||
	            !Objects.equals(updatedUser, authority.getScheduleUser())) {
	            StringBuffer scheduleName = new StringBuffer();
	            authority.setScheduleOrg(updatedOrg);
	            authority.setScheduleUser(updatedUser);
	            //将对应的科室名称权限范围名称更新
	            if(!ObjectUtil.isEmpty(updatedOrg)){
	            	for(String org : Arrays.asList(updatedOrg.split(","))){
	            		if(orgNameMap.containsKey(org)){
	            			scheduleName.append("," + orgNameMap.get(org));
	            		}else {
	            			scheduleName.append("," + org);
	            		}
	            	}
	            	
	            }
	            //将对应的员工名称权限范围名称更新
	            if(!ObjectUtil.isEmpty(updatedUser)){
	            	for(String user : Arrays.asList(updatedUser.split(","))){
	            		if(userNameMap.containsKey(user)){
	            			scheduleName.append("," + userNameMap.get(user));
	            		}else {
	            			scheduleName.append("," + user);
	            		}
	            	}
	            }
	            String name = scheduleName.toString();
				if(!ObjectUtils.isEmpty(name)){
					name = name.substring(1);
				}
				authority.setScheduleName(name);
	            // 更新到数据库
	            mapper.updateByPrimaryKeySelective(authority);
	        }
	    } catch (Exception e) {
	        // 记录错误日志，避免因单个记录更新失败影响整个流程
	        log.error("更新权限记录使用范围失败，ID=" + authority.getId(), e);
	    }
	}
	
	/**
	 * 从逗号分隔的字符串中移除指定的重复项
	 */
	private String removeDuplicates(String original, List<String> duplicates) {
	    if (original == null || original.isEmpty() || duplicates.isEmpty()) {
	        return original;
	    }
	    
	    // 将原始字符串拆分为集合
	    List<String> items = new ArrayList<>(Arrays.asList(original.split(",")));
	    
	    // 移除重复项
	    items.removeIf(item -> {
	        String trimmedItem = item.trim();
	        return duplicates.contains(trimmedItem);
	    });
	    
	    // 重新组合为逗号分隔的字符串
	    return String.join(",", items);
	}
	
	/**
     * 检查由逗号分隔的字符串数组中是否包含特定字符串
     * @param commaSeparatedString 逗号分隔的字符串
     * @param targetString 要查找的目标字符串
     * @return 如果包含返回true，否则返回false
     */
    private boolean containsString(String commaSeparatedString, String targetString) {
        // 处理输入为空的情况
        if (commaSeparatedString == null || commaSeparatedString.isEmpty()) {
            return false;
        }
        
        // 处理目标字符串为null的情况
        if (targetString == null) {
            return false;
        }
        
        // 按逗号分割字符串，并去除每个元素的前后空格
        String[] parts = commaSeparatedString.split(",");
        for (String part : parts) {
            // 去除前后空格后比较
            if (targetString.equals(part.trim())) {
                return true;
            }
        }
        
        return false;
    }

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedScheduleAuthority record) {
		
//		Example example = new Example(MedScheduleAuthority.class);
//		Example.Criteria criteria = example.createCriteria();
//		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
//		criteria.andNotEqualTo("id", record.getId());
//		criteria.andCondition("find_in_set('" + record.getManageUserCode() + "',manage_user_code)");
//		List<MedScheduleAuthority> list = mapper.selectByExample(example);
//		if(CollectionUtils.isNotEmpty(list)) {
//			Assert.isTrue(false, "排班管理员" + record.getManageName() + "已存在,请勿重复添加!");
//		}
		
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//校验使用范围的人员、人员所在科室，以及科室，是否在其他组
//		if(!record.isForceUpdate()){
//			Map<String, Map<String, String>> resultMap = validateUseRangeList(record);
//			StringBuffer message = new StringBuffer();
//			if(resultMap.containsKey("dept")){
//				Map<String, String> deptMap = resultMap.get("dept");
//				for(String dept : deptMap.keySet()){
//					message.append(",");
//					message.append(deptMap.get(dept));
//				}
//			}
//			if(resultMap.containsKey("user")){
//				Map<String, String> userMap = resultMap.get("user");
//				for(String userName : userMap.keySet()){
//					message.append(",");
//					message.append(userMap.get(userName));
//				}
//			}
//			String ss = message.toString();
//			if(!ObjectUtils.isEmpty(ss)){
//				ss = ss.substring(1);
//				throw new BusinessException(ss + "已在其他考勤组里面！");
//			}
//		} else {
//			validateUseRangeList(record);
//		}
		
		//排班管理员关联SSO的排班管理员  pb
		String manageUserCode = record.getManageUserCode();//排班管理员用户
		if(!"1".equals(pbRoleCode) && !ObjectUtils.isEmpty(manageUserCode)){
			List<String> userCodeList = new ArrayList<>(Arrays.asList(manageUserCode.split(",")));
			PlatformResult<List<EmployeeResp>> resp = hrmsEmployeeFeignService.getEmployeeDetailByCodes(userCodeList);
			if(resp.isSuccess() && resp.getObject().size() > 0){
				UserRoleReq userRoleReq = new UserRoleReq();
				List<String> userIdList = new ArrayList<>();
				for(EmployeeResp emp : resp.getObject()){
					userIdList.add(emp.getEmployeeId());
				}
				userRoleReq.setUserIdList(userIdList);
				userRoleReq.setRoleIdList(Arrays.asList(pbRoleCode));
				rightService.doBatchUserRoleSave(userRoleReq);
			}
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedScheduleAuthority record = new MedScheduleAuthority();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedScheduleAuthority selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedScheduleAuthority> getDataSetList(Page page, MedScheduleAuthority record) {
		Example example = new Example(MedScheduleAuthority.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		
		Boolean isAdmin = UserInfoHolder.ISADMIN();
		Boolean isPbSettingAdmin = UserInfoHolder.getRight("SCHEDULER_SETTING_MANAGER"); //排班设置员
		Boolean YWGLY = UserInfoHolder.getRight("YWGLY"); //医务管理员
		Boolean superAdmin = UserInfoHolder.ISSUPERADMIN();//超级管理员
		
		if(!isAdmin && !YWGLY && !isPbSettingAdmin && !superAdmin) {
			criteria.andCondition("find_in_set('" + UserInfoHolder.getCurrentUserCode() + "',manage_user_code)");
		}
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<MedScheduleAuthority> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	@Override
	public List<TreeModel> getScheduleZTree(ScheduleEmployee record) {
		Boolean isAdmin = UserInfoHolder.ISADMIN();
		Boolean isPbSettingAdmin = UserInfoHolder.getRight("SCHEDULER_SETTING_MANAGER"); //排班设置员
		Boolean YWGLY = UserInfoHolder.getRight("YWGLY"); //医务管理员
		Boolean superAdmin = UserInfoHolder.ISSUPERADMIN();//超级管理员
		
		Example example = new Example(MedScheduleAuthority.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andCondition("find_in_set('" + UserInfoHolder.getCurrentUserCode() + "',manage_user_code)");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<MedScheduleAuthority> list = mapper.selectByExample(example);
		
		String scheduleOrg = null;
		String scheduleUser = null;
		if(CollectionUtils.isNotEmpty(list)) {
			MedScheduleAuthority medScheduleAuthority = list.get(0);
			scheduleOrg = medScheduleAuthority.getScheduleOrg();
			scheduleUser = medScheduleAuthority.getScheduleUser();
		}
		
		if(StringUtils.isBlank(scheduleOrg) && StringUtils.isBlank(scheduleUser) && !isAdmin && !YWGLY && !isPbSettingAdmin && superAdmin) {
			return ListUtil.empty();
		}
		
		List<String> orgIdList = new ArrayList<>();
		
		 List<ScheduleOrganization> organizationList = new ArrayList<>();
		
 		if(!isAdmin && !YWGLY && !isPbSettingAdmin && superAdmin) {
 			
 			List<String> allTreesList = new ArrayList<>();
 			
 			if(StringUtils.isNotBlank(scheduleOrg)) {
 				List<String> scheduleOrgs =  ListUtil.of(scheduleOrg.split(","));
 				record.setScheduleOrgs(scheduleOrgs);
 				record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
 				List<String> treesList = mapper.selectTreesData(record);
 				
 				allTreesList.addAll(treesList);
 			}
 			if(StringUtils.isNotBlank(scheduleUser)) {
 				List<String> scheduleUsers =  ListUtil.of(scheduleUser.split(","));
 				record.setScheduleUsers(scheduleUsers);
 				record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
 				List<String> treesList2 = mapper.selectTreesData2(record);
 				
 				allTreesList.addAll(treesList2);
 			}
 			
    		 
 			if(CollectionUtils.isNotEmpty(allTreesList)) {
 				for (String treeId : allTreesList) {
 	    			 if(StringUtils.isNotBlank(treeId)) {
 	    				 String[] treeIdArray = treeId.split(",");
 	    				 orgIdList.addAll(Arrays.asList(treeIdArray));
 	    			 }
 				 }
 	    		 organizationList = mapper.selectOrganizationList(orgIdList, UserInfoHolder.getCurrentUserCorpCode());
 			}
    	}else {
    		organizationList = mapper.selectOrganizationList(orgIdList, UserInfoHolder.getCurrentUserCorpCode());
    	}
		
 		// 寻找根节点
 		TreeModel treeModel = null;
        List<TreeModel> roots = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(organizationList)) {
           for (ScheduleOrganization organization : organizationList) {
               if ("0".equals(organization.getParentId()) || StringUtils.isBlank(organization.getParentId())) {
            	   treeModel = new TreeModel();
            	   treeModel.setId(organization.getOrganizationId());
            	   treeModel.setName(organization.getName());
            	   treeModel.setParent(true);
            	   treeModel.setOpen(true);
            	   treeModel.setPid(organization.getParentId());
            	   treeModel.setCode(organization.getOrganizationId());
            	   
                   recursionOrganization(treeModel, organizationList, organization.getOrganizationId());
                   roots.add(treeModel);
               }
           }
        }
        return roots;
	}
	
	private void recursionOrganization(TreeModel root, List<ScheduleOrganization> organizationList, String organizationId) {
        List<TreeModel> children = null;
        for (ScheduleOrganization organization : organizationList) {
            if (!ObjectUtils.isEmpty(organization.getParentId()) && organization.getParentId().equals(organizationId)) {
                if (children == null) {
                    children = new ArrayList<>();
                }
                TreeModel treeModel = new TreeModel();
                treeModel.setId(organization.getOrganizationId());
         	    treeModel.setName(organization.getName());
         	    treeModel.setParent(false);
         	    treeModel.setOpen(false);
         	    treeModel.setPid(organization.getParentId());
         	    treeModel.setCode(organization.getOrganizationId());
                
         	    recursionOrganization(treeModel, organizationList, organization.getOrganizationId());
                
                children.add(treeModel);
            }
        }
        root.setChildren(children);
    }
	
	/**
	 * 渲染查询参数
	 * @param record
	 * @return
	 */
	@Override
	public boolean setQueryData(ScheduleEmployee record){
		Boolean isadmin = UserInfoHolder.ISADMIN();
		Boolean YWGLY = UserInfoHolder.getRight("YWGLY"); //医务管理员
		Boolean isPbSettingAdmin = UserInfoHolder.getRight("SCHEDULER_SETTING_MANAGER"); //排班设置员
		Boolean superAdmin = UserInfoHolder.ISSUPERADMIN();//超级管理员
		
		if(StringUtils.isBlank(record.getIsAll()) && !isadmin && !YWGLY && !isPbSettingAdmin && !superAdmin){
			
			Example example = new Example(MedScheduleAuthority.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andCondition("find_in_set('" + UserInfoHolder.getCurrentUserCode() + "',manage_user_code)");
			criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
			List<MedScheduleAuthority> list = mapper.selectByExample(example);
			
			StringBuffer scheduleOrg = null;
			StringBuffer scheduleUser = null;
			if(CollectionUtils.isNotEmpty(list)) {
				
				scheduleOrg = new StringBuffer();
				scheduleUser = new StringBuffer();
				for (MedScheduleAuthority authority : list) {
					if(StringUtils.isNotBlank(authority.getScheduleOrg())) {
						scheduleOrg.append(authority.getScheduleOrg()).append(",");
					}
					if(StringUtils.isNotBlank(authority.getScheduleUser())) {
						scheduleUser.append(authority.getScheduleUser()).append(",");
					}
				}
			}
			
			if(StringUtils.isBlank(scheduleOrg) && StringUtils.isBlank(scheduleUser) && !isadmin && !YWGLY && !isPbSettingAdmin) {
				return false;
			}
			
			if(StringUtils.isNotBlank(scheduleOrg)) {
				List<String> scheduleOrgs =  ListUtil.of(scheduleOrg.toString().split(","));
				record.setScheduleOrgs(scheduleOrgs);
			}
			if(StringUtils.isNotBlank(scheduleUser)) {
				List<String> scheduleUsers =  ListUtil.of(scheduleUser.toString().split(","));
				record.setScheduleUsers(scheduleUsers);
			}
		}else {
			//查询已经设置了排班的人员
			Example example = new Example(MedScheduleAuthority.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
			List<MedScheduleAuthority> list = mapper.selectByExample(example);
			
			StringBuffer scheduleOrg = new StringBuffer();
			StringBuffer scheduleUser = new StringBuffer();
			if(CollectionUtils.isNotEmpty(list)) {
				
				for (MedScheduleAuthority authority : list) {
					if(StringUtils.isNotBlank(authority.getScheduleOrg())) {
						scheduleOrg.append(authority.getScheduleOrg()).append(",");
					}
					if(StringUtils.isNotBlank(authority.getScheduleUser())) {
						scheduleUser.append(authority.getScheduleUser()).append(",");
					}
				}
			}
			
			if(StringUtils.isBlank(scheduleOrg) && StringUtils.isBlank(scheduleUser)) {
				return false;
			}
			
			if(StringUtils.isNotBlank(scheduleOrg)) {
				List<String> scheduleOrgs =  ListUtil.of(scheduleOrg.toString().split(","));
				record.setScheduleOrgs(scheduleOrgs);
			}
			if(StringUtils.isNotBlank(scheduleUser)) {
				List<String> scheduleUsers =  ListUtil.of(scheduleUser.toString().split(","));
				record.setScheduleUsers(scheduleUsers);
			}
		}
		
		if(StringUtils.isNotBlank(record.getOrgIds())) {
			List<String> orgIds = Arrays.asList(record.getOrgIds().split(","));
			record.setOrgIdList(orgIds);
		}
		if(StringUtils.isNotBlank(record.getClassesIds())) {
			List<String> classesIds = Arrays.asList(record.getClassesIds().split(","));
			record.setClassesIdList(classesIds);
		}
		if(StringUtils.isNotBlank(record.getEmployeeStatus())) {
			List<String> employeeStatusList = Arrays.asList(record.getEmployeeStatus().split(","));
			record.setEmployeeStatusList(employeeStatusList);
		}
		
		
		record.setCurrentUserCode(UserInfoHolder.getCurrentUserCode());
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return true;
	}

	@Override
	public DataSet<ScheduleEmployee> getScheduleRecordList(Page page,ScheduleEmployee record, List<ScheduleEmployee> records) {
		
		if(!setQueryData(record)){
			return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), ListUtil.empty());
		}
		
		if(null == records){
			records = mapper.selectEmployeeByAuthorit(record,page);
		}
		
		//获取当前登录人的模板排班是否开启
		boolean scheduleTemplateStatus = false;
		if(record.isStartSchedule() || record.isTemplate()){
			scheduleTemplateStatus = true;
//			MedScheduleTemplateExtend templateExtend = medScheduleTemplateMapper.selectTemplateExtendByEmp(UserInfoHolder.getCurrentUserCode(), UserInfoHolder.getCurrentUserCorpCode());
//			if(null != templateExtend){
//				String status = templateExtend.getStatus();
//				if(!ObjectUtils.isEmpty(status) && "1".equals(status)){
//					scheduleTemplateStatus = true;
//				}
//			}
		}
		
		//根据开始时间和结束时间生成日期按日生成集合
		List<String> scheduleDateSet = new ArrayList<>();
		if(record.isStartSchedule() && scheduleTemplateStatus){
			getScheduleDateSet(record, scheduleDateSet);
		}

		//模板数据，key为用户id，value为该用户的模板数据
		Map<String, List<MedScheduleRecord>> templateMap = new HashMap<>();
		if((record.isStartSchedule() && scheduleTemplateStatus) || record.isTemplate()){
			//将模板数据根据用户ID分组
			List<MedScheduleRecord> list = medScheduleTemplateMapper.selectMedScheduleTemplateRecordList(record);
			for(MedScheduleRecord re : list){
				String employeeId= re.getEmployeeId();
				if(templateMap.containsKey(employeeId)){
					templateMap.get(employeeId).add(re);
				} else {
					List<MedScheduleRecord> ll = new ArrayList<MedScheduleRecord>();
					ll.add(re);
					templateMap.put(employeeId, ll);
				}
			}
		}
		
		List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
		List<DictItemResp> orgAttributes = dictItemFeignService.getDictItemByTypeCode("ORG_ATTRIBUTES").getObject();
		
		for (ScheduleEmployee scheduleEmployee : records) {
			
			DictItemResp area = areas.stream().filter(j -> StrUtil.equals(scheduleEmployee.getHospCode(), j.getItemCode())).findFirst().orElse(null);
			scheduleEmployee.setHospName(null == area ? scheduleEmployee.getHospCode() : area.getItemName());
			
			DictItemResp orgAttribute = orgAttributes.stream().filter(j -> StrUtil.equals(scheduleEmployee.getOrgAttributes(), j.getItemCode())).findFirst().orElse(null);
			scheduleEmployee.setOrgAttributes(null == orgAttribute ? scheduleEmployee.getOrgAttributes() : orgAttribute.getItemName());
			String employeeId = scheduleEmployee.getEmployeeId();
			record.setEmployeeId(employeeId);
			record.setEmployeeNo(scheduleEmployee.getEmployeeNo());
			//判断是否是模板
			if(!record.isTemplate()){
				
				scheduleEmployee.setScheduleRecords(getScheduleRecordLists(record));
				if(record.isShowActualAttendance()){
					scheduleEmployee.setActualAttendance(record.getActualAttendance());
				}
				
			} else {
				//查询模板排班数据
				List<MedScheduleRecord> scheduleTemplateList = templateMap.get(scheduleEmployee.getEmployeeId());
				if(scheduleTemplateStatus){
					scheduleEmployee.setStatus("1");
				} else {
					scheduleEmployee.setStatus("0");
				}
				if(null == scheduleTemplateList){
					scheduleEmployee.setScheduleRecords(new ArrayList<MedScheduleRecord>());
				} else {
					scheduleEmployee.setScheduleRecords(scheduleTemplateList);
				}
			}
			
			//如果当前登录人的模板开启，并且被排班人的班次列表为空，则填充模板排班数据
			if(record.isStartSchedule() && scheduleTemplateStatus){
				setTemplateRecords(scheduleEmployee, templateMap, scheduleDateSet);
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	/**
	 * 获取排班数据，请假，进修/规培/学习/下乡，销假数据
	 * @param record
	 * @return
	 * <AUTHOR>
	 * @update 2025-08-12
	 */
	public List<MedScheduleRecord> getScheduleRecordLists(ScheduleEmployee record) {
		String employeeId = record.getEmployeeId();
		
		BigDecimal actualAttendance = BigDecimal.ZERO;//实际出勤天数
		boolean showActualAttendance = record.isShowActualAttendance();
		
		List<MedScheduleRecord> schedulerecordList = new ArrayList<>();
		//有请假数据的日期请假时长的统计-用于判断当前日期是否排班
		Map<String, BigDecimal> scheduleDateDaysCountMap = new HashMap<>();
		
		//查询请假数据 填充进去
		List<LeaveRecordVo> leaveDataList = mapper.selectLeaveData(record);
		setLeaveData(employeeId, leaveDataList, schedulerecordList, scheduleDateDaysCountMap);
		
		//销假数据填充进去
		List<LeaveRecordVo> cancelLeaveDataList = mapper.selectCancelLeaveData(record);
		setCancelLeaveData(employeeId, cancelLeaveDataList, schedulerecordList, scheduleDateDaysCountMap);
		
		//查询进修、规培、学习会议、下乡等数据 填充进去
		List<LeaveRecordVo> outRecordDataList = mapper.selectOutRecordData(record);
		setOutRecordData(employeeId, outRecordDataList, schedulerecordList, scheduleDateDaysCountMap);
		
		//排班数据
		List<MedScheduleRecord> recordList = medScheduleRecordService.selectMedScheduleRecordList(record);
		
		//当日请假时长 > 出勤日不显示排班数据
		if(scheduleDateDaysCountMap.size() == 0 && recordList.size() > 0){
			schedulerecordList.addAll(recordList);
			if(showActualAttendance){
				for(MedScheduleRecord re : recordList){
					actualAttendance = actualAttendance.add(re.getClassesDays());
				}
			}
		} else if(recordList.size() > 0){
			List<MedScheduleRecord> resultList = new ArrayList<>();
			List<String> existDate = new ArrayList<>();//存储已经计算过的请假数据，避免重复计算
			for(MedScheduleRecord re : recordList){
				String day = re.getScheduleDate();
				if(scheduleDateDaysCountMap.containsKey(day)){
					//判断统计时长 < 1 ,则加到列表中，否则不添加到列表
					if(scheduleDateDaysCountMap.get(day).compareTo(re.getClassesDays()) < 0){
						resultList.add(re);
						if(showActualAttendance && !existDate.contains(day)){
							existDate.add(day);
							//当天实际出勤 = 排班天数 - 请假、下乡等数据
							actualAttendance = actualAttendance.add(re.getClassesDays().subtract(scheduleDateDaysCountMap.get(day)));
						} else if(showActualAttendance){
							//当天实际出勤 = 排班天数 - 请假、下乡等数据
							actualAttendance = actualAttendance.add(re.getClassesDays());
						}
					}
				} else {
					resultList.add(re);
					if(showActualAttendance){
						actualAttendance = actualAttendance.add(re.getClassesDays());
					}
				}
			}
			schedulerecordList.addAll(resultList);
		}
		
		record.setActualAttendance(actualAttendance);
		return schedulerecordList;
	}
	
	/**
	 * 渲染请假数据
	 * @param employeeId 员工ID
	 * @param leaveDataList 员工请假数据
	 * @param schedulerecordList 排班列表
	 * @param scheduleDateDaysCountMap 排班日期请假时长统计
	 * <AUTHOR>
	 * @update 2025-08-12
	 */
	private void setLeaveData(String employeeId, List<LeaveRecordVo> leaveDataList, List<MedScheduleRecord> schedulerecordList, 
			Map<String, BigDecimal> scheduleDateDaysCountMap) {
		if(CollectionUtils.isEmpty(leaveDataList)) {
			return;
		}
		for (LeaveRecordVo leaveData : leaveDataList) {
			if(StringUtils.isNotBlank(leaveData.getStartDate()) && StringUtils.isNotBlank(leaveData.getEndDate())) {
				List<String> days = DateUtils.getDays(leaveData.getStartDate(), leaveData.getEndDate());
				String startDateValue = leaveData.getStartDateValue();//具体开始时间
				String endDateValue = leaveData.getEndDateValue();//具体结束时间
				int index = 0;//第几天，用于后续判断上午还是下午
				for (String day : days) {
					index++;
					MedScheduleRecord medScheduleRecord = new MedScheduleRecord();
					medScheduleRecord.setClassesColor("#026329");
					medScheduleRecord.setClassesId("88888888");
					medScheduleRecord.setClassesName(leaveData.getLeaveType());
					medScheduleRecord.setTypeId("88888888");
					//处理请假天数-用于判断是否可以排班
					if(!ObjectUtils.isEmpty(startDateValue) && !ObjectUtils.isEmpty(endDateValue)){
						if(days.size() == 1){//请假开始和结束时间在同一天
							if(("1".equals(startDateValue) && "1".equals(endDateValue)) || ("2".equals(startDateValue) && "2".equals(endDateValue))){
								//上午开始请假，上午结束请假，算 0.5 天，或者下午开始请假，下午结束请假，算 0.5 天
								medScheduleRecord.setDays(new BigDecimal("0.5"));
							} else {
								medScheduleRecord.setDays(new BigDecimal(1));
							}
							medScheduleRecord.setStartDateValue(startDateValue);
							medScheduleRecord.setEndDateValue(endDateValue);
						} else {
							if((index == 1 && "2".equals(startDateValue)) || (index == days.size() && "1".equals(endDateValue))){
								//第一天下午开始请假 或者 最后一天上午结束请假
								medScheduleRecord.setDays(new BigDecimal("0.5"));
								if(index == 1 && "2".equals(startDateValue)){
									medScheduleRecord.setStartDateValue("2");
									medScheduleRecord.setEndDateValue("2");
								} else if(index == days.size() && "1".equals(endDateValue)) {
									medScheduleRecord.setStartDateValue("1");
									medScheduleRecord.setEndDateValue("1");
								}
							} else {
								medScheduleRecord.setDays(new BigDecimal(1));
								medScheduleRecord.setStartDateValue("1");
								medScheduleRecord.setEndDateValue("2");
							}
						}
					} else {
						medScheduleRecord.setDays(new BigDecimal(1));
						medScheduleRecord.setStartDateValue("1");
						medScheduleRecord.setEndDateValue("2");
					}

					scheduleDateDaysCountMap.put(day, medScheduleRecord.getDays());
					
					medScheduleRecord.setScheduleDate(day);
					medScheduleRecord.setEmployeeId(employeeId);
					medScheduleRecord.setType(leaveData.getType());
					schedulerecordList.add(medScheduleRecord);
				}
			}
		}
	}
	
	/**
	 * 渲染销假数据
	 * @param employeeId 员工ID
	 * @param leaveDataList 员工请假数据
	 * @param schedulerecordList 排班列表
	 * <AUTHOR>
	 * @update 2025-08-12
	 */
	private void setCancelLeaveData(String employeeId, List<LeaveRecordVo> cancelLeaveDataList, List<MedScheduleRecord> schedulerecordList,
			Map<String, BigDecimal> scheduleDateDaysCountMap) {
		if(CollectionUtils.isEmpty(cancelLeaveDataList)) {
			return;
		}

		for (LeaveRecordVo leaveData : cancelLeaveDataList) {
			if(StringUtils.isNotBlank(leaveData.getStartDate()) && StringUtils.isNotBlank(leaveData.getEndDate())) {
				List<String> days = DateUtils.getDays(leaveData.getStartDate(), leaveData.getEndDate());
				String startDateValue = leaveData.getStartDateValue();//具体开始时间
				String endDateValue = leaveData.getEndDateValue();//具体结束时间
				int index = 0;//第几天，用于后续判断上午还是下午
				for (String day : days) {
					index++;
					MedScheduleRecord medScheduleRecord = new MedScheduleRecord();
					medScheduleRecord.setClassesColor("#e91242");
					medScheduleRecord.setClassesId("88888888");
					medScheduleRecord.setClassesName("销假-" + leaveData.getLeaveType());
					medScheduleRecord.setTypeId("88888888");
					//处理销假天数-用于判断是否可以排班
					if(!ObjectUtils.isEmpty(startDateValue) && !ObjectUtils.isEmpty(endDateValue)){
						if(days.size() == 1){//销假开始和结束时间在同一天
							if(("1".equals(startDateValue) && "1".equals(endDateValue)) || ("2".equals(startDateValue) && "2".equals(endDateValue))){
								//上午开始销假，上午结束销假，算 0.5 天，或者下午开始销假，下午结束销假，算 0.5 天
								medScheduleRecord.setDays(new BigDecimal("0.5"));
							} else {
								medScheduleRecord.setDays(new BigDecimal(1));
							}
							medScheduleRecord.setStartDateValue(startDateValue);
							medScheduleRecord.setEndDateValue(endDateValue);
						} else {
							if((index == 1 && "2".equals(startDateValue)) || (index == days.size() && "1".equals(endDateValue))){
								//第一天下午开始销假 或者 最后一天上午结束销假
								medScheduleRecord.setDays(new BigDecimal("0.5"));
								if(index == 1 && "2".equals(startDateValue)){
									medScheduleRecord.setStartDateValue("2");
									medScheduleRecord.setEndDateValue("2");
								} else if(index == days.size() && "1".equals(endDateValue)) {
									medScheduleRecord.setStartDateValue("1");
									medScheduleRecord.setEndDateValue("1");
								}
							} else {
								medScheduleRecord.setDays(new BigDecimal(1));
								medScheduleRecord.setStartDateValue("1");
								medScheduleRecord.setEndDateValue("2");
							}
						}
					} else {
						medScheduleRecord.setDays(new BigDecimal(1));
						medScheduleRecord.setStartDateValue("1");
						medScheduleRecord.setEndDateValue("2");
					}
					
					//当天请假时长 = 请假时长-销假时长
					if(scheduleDateDaysCountMap.containsKey(day)){
						BigDecimal leaveDays = scheduleDateDaysCountMap.get(day);//请假时长
						BigDecimal cancelLeaveDays = medScheduleRecord.getDays();//销假时长
						if(leaveDays.compareTo(cancelLeaveDays) >= 0){//请假时长 > 销假时长
							scheduleDateDaysCountMap.put(day, leaveDays.subtract(cancelLeaveDays));
						} else if(leaveDays.compareTo(cancelLeaveDays) < 0){//请假时长 = 销假时长  -不需要统计时长
							scheduleDateDaysCountMap.remove(day);
						}
					}
					
					medScheduleRecord.setScheduleDate(day);
					medScheduleRecord.setEmployeeId(employeeId);
					medScheduleRecord.setType(leaveData.getType());
					schedulerecordList.add(medScheduleRecord);
				}
			}
		}
	
	}
	
	/**
	 * 渲染进修、规培、学习会议、下乡等数据
	 * @param employeeId 员工ID
	 * @param outRecordDataList 员工进修、规培、学习会议、下乡等数据
	 * @param schedulerecordList 排班列表
	 * <AUTHOR>
	 * @update 2025-08-12
	 */
	private void setOutRecordData(String employeeId, List<LeaveRecordVo> outRecordDataList, List<MedScheduleRecord> schedulerecordList,
			Map<String, BigDecimal> scheduleDateDaysCountMap){
		if(CollectionUtils.isEmpty(outRecordDataList)) {
			return;
		}
		for (LeaveRecordVo leaveData : outRecordDataList) {
			if(StringUtils.isNotBlank(leaveData.getStartDate()) && StringUtils.isNotBlank(leaveData.getEndDate())) {
				List<String> days = DateUtils.getDays(leaveData.getStartDate(), leaveData.getEndDate());
				for (String day : days) {
					MedScheduleRecord medScheduleRecord = new MedScheduleRecord();
					medScheduleRecord.setClassesColor("#a5cb30");
					medScheduleRecord.setClassesId("88888888");
					medScheduleRecord.setClassesName(leaveData.getLeaveType());
					medScheduleRecord.setTypeId("88888888");
					medScheduleRecord.setScheduleDate(day);
					medScheduleRecord.setDays(new BigDecimal(1));
					scheduleDateDaysCountMap.put(day, medScheduleRecord.getDays());
					medScheduleRecord.setEmployeeId(employeeId);
					medScheduleRecord.setType(leaveData.getType());
					schedulerecordList.add(medScheduleRecord);
				}
			}
		}
	}
	
	/**
	 * 渲染模版数据
	 * @param scheduleEmployee
	 * @param templateMap
	 * @param scheduleDateSet
	 * <AUTHOR>
	 * @update 2025-08-12
	 */
	private void setTemplateRecords(ScheduleEmployee scheduleEmployee, Map<String, List<MedScheduleRecord>> templateMap, List<String> scheduleDateSet){
		List<MedScheduleRecord> scheduleRecordList = scheduleEmployee.getScheduleRecords();
		Map<String, MedScheduleRecord> scheduleRecordMap = new HashMap<>();
		if (scheduleRecordList != null && scheduleRecordList.size() > 0) {
			scheduleRecordMap = scheduleRecordList.stream().collect(Collectors.toMap(MedScheduleRecord::getScheduleDate, a -> a, (k1, k2) -> k1));
        }
		if(scheduleRecordList == null){
			scheduleRecordList = new ArrayList<>();
		}
		//获取模板数据
		List<MedScheduleRecord> scheduleTemplateList = templateMap.get(scheduleEmployee.getEmployeeId());
		Map<String, List<MedScheduleRecord>> scheduleTemplateMap = new HashMap<>();
		if (scheduleTemplateList != null && scheduleTemplateList.size() > 0) {
			for(MedScheduleRecord mr : scheduleTemplateList){
				String scheduleWeek = mr.getScheduleWeek();
				if(scheduleTemplateMap.containsKey(scheduleWeek)){
					scheduleTemplateMap.get(scheduleWeek).add(mr);
				} else {
					List<MedScheduleRecord> list = new ArrayList<>();
					list.add(mr);
					scheduleTemplateMap.put(scheduleWeek, list);
				}
			}
        }
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		for(String scheduleDate : scheduleDateSet){
			//如果员工没有scheduleDate日期的排班，则获取模板数据填充
			if(!scheduleRecordMap.containsKey(scheduleDate)){
				//将排班的日期换算成星期几
				LocalDate date = LocalDate.parse(scheduleDate, formatter);
				// 获取星期几
				DayOfWeek dayOfWeek = date.getDayOfWeek();
				String dayOfWeekValue = String.valueOf(dayOfWeek.getValue());
				//将模板数据填充到排班里面去
				if(scheduleTemplateMap.containsKey(dayOfWeekValue)){
					List<MedScheduleRecord> msrList = scheduleTemplateMap.get(dayOfWeekValue);
					for(MedScheduleRecord msr : msrList){
						MedScheduleRecord newMsr = new MedScheduleRecord();
						BeanUtils.copyProperties(msr, newMsr);
						newMsr.setId(null);
						newMsr.setScheduleDate(scheduleDate);
						scheduleRecordList.add(newMsr);
					}
				}
			}
		}
		scheduleEmployee.setScheduleRecords(scheduleRecordList);
	}
	
	/**
	 * 根据开始时间和结束时间生成日期按日生成集合
	 * @param record
	 * @param scheduleDateSet
	 * <AUTHOR>
	 * @update 2025-08-12
	 */
	private void getScheduleDateSet (ScheduleEmployee record, List<String> scheduleDateSet){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String today = sdf.format(new Date());
		String startDate = record.getStartDate();
		String endDate = record.getEndDate();
		if(ObjectUtils.isNotEmpty(startDate) && ObjectUtils.isNotEmpty(endDate)){
			if(!isBefore(startDate, today)){
				scheduleDateSet.add(startDate);
			}
			while(!startDate.equals(endDate)){
				try {
					Calendar ca = Calendar.getInstance();
					ca.setTime(sdf.parse(startDate));
					ca.add(Calendar.DAY_OF_YEAR, 1);
					startDate = sdf.format(ca.getTime());
					//和当前时间比较，小于当前时间则不加入
					if(!isBefore(startDate, today)){
						scheduleDateSet.add(startDate);
					}
				} catch (ParseException e) {
				}
			}
		}
	}
	/**
	 * 比较两个日期大小
	 * @param dateStr1
	 * @param dateStr2
	 * @return
	 */
	private static boolean isBefore(String dateStr1, String dateStr2){
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        
        // 解析字符串为LocalDate
        LocalDate date1 = LocalDate.parse(dateStr1, formatter);
        LocalDate date2 = LocalDate.parse(dateStr2, formatter);
        
        // 比较日期
        return date1.isBefore(date2);
	}

	@Override
	public List<ScheduleEmployee> getScheduleManageEmployeeList(ScheduleEmployee record) {
		Example example = new Example(MedScheduleAuthority.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andCondition("find_in_set('" + UserInfoHolder.getCurrentUserCode() + "',manage_user_code)");
		List<MedScheduleAuthority> list = mapper.selectByExample(example);
		
		StringBuffer scheduleOrg = new StringBuffer();
		StringBuffer scheduleUser = new StringBuffer();
		if(CollectionUtils.isNotEmpty(list)) {
			
			for (MedScheduleAuthority authority : list) {
				if(StringUtils.isNotBlank(authority.getScheduleOrg())) {
					scheduleOrg.append(authority.getScheduleOrg()).append(",");
				}
				if(StringUtils.isNotBlank(authority.getScheduleUser())) {
					scheduleUser.append(authority.getScheduleUser()).append(",");
				}
			}
		}
		
		if(StringUtils.isBlank(scheduleOrg) && StringUtils.isBlank(scheduleUser)) {
			return ListUtil.empty();
		}
		
		if(StringUtils.isNotBlank(scheduleOrg)) {
			List<String> scheduleOrgs =  ListUtil.of(scheduleOrg.toString().split(","));
			record.setScheduleOrgs(scheduleOrgs);
		}
		if(StringUtils.isNotBlank(scheduleUser)) {
			List<String> scheduleUsers =  ListUtil.of(scheduleUser.toString().split(","));
			record.setScheduleUsers(scheduleUsers);
		}
		
		//需要查询已经分组了的人员  并排除掉
		List<String> existEmpIds = medScheduleGroupUserService.selectMyGroupUser(UserInfoHolder.getCurrentUserCode(),record.getGroupId());
		
		record.setExistEmpIds(existEmpIds);
		
		return mapper.getScheduleManageEmployeeList(record);
	}
	
}

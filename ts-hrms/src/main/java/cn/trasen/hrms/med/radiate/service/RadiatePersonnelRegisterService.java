package cn.trasen.hrms.med.radiate.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;
import cn.trasen.hrms.med.radiate.vo.RadiatePersonnelReqVo;
import cn.trasen.hrms.med.radiate.vo.RadiatePersonnelVo;

/**
 * @ClassName RadiatePersonnelRegisterService
 * @Description TODO
 * @date 2025��1��8�� ����11:21:29
 * <AUTHOR>
 * @version 1.0
 */
public interface RadiatePersonnelRegisterService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��1��8�� ����11:21:29
	 * <AUTHOR>
	 */
	Integer save(RadiatePersonnelRegister record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��1��8�� ����11:21:29
	 * <AUTHOR>
	 */
	Integer update(RadiatePersonnelRegister record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��1��8�� ����11:21:29
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return RadiatePersonnelRegister
	 * @date 2025��1��8�� ����11:21:29
	 * <AUTHOR>
	 */
	RadiatePersonnelRegister selectById(String id);

	List<RadiatePersonnelRegister> getList(RadiatePersonnelReqVo record);
	
	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<RadiatePersonnelRegister>
	 * @date 2025-05-21 15:21:29
	 * <AUTHOR>
	 */
	DataSet<RadiatePersonnelRegister> selectRadiatePersonnelRegisterPageList(Page page, RadiatePersonnelReqVo record);
	
	/**
	 * @Title saveInfo
	 * @Description 保存放射人员基本信息
	 * @param record
	 * @return Integer
	 * @date 2025-05-21 15:21:29
	 * <AUTHOR>
	 */
	Integer saveInfo(RadiatePersonnelVo record);
	
	/**
	 * @Title updateInfo
	 * @Description 更新放射人员基本信息
	 * @param record
	 * @return Integer
	 * @date 2025-05-21 15:21:29
	 * <AUTHOR>
	 */
	Integer updateInfo(RadiatePersonnelVo record);
	
	/**
	 * @Title selectByEmployeeId
	 * @Description 根据人员ID查询详情数据
	 * @param record
	 * @return Integer
	 * @date 2025-05-21 15:21:29
	 * <AUTHOR>
	 */
	RadiatePersonnelVo selectByEmployeeId(String employeeId);
	
	/**
	 * 导入数据
	 * 
	 * @param list
	 * @return
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	PlatformResult importData(List<RadiatePersonnelRegister> list);
	
}

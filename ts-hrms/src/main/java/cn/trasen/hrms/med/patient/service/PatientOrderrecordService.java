package cn.trasen.hrms.med.patient.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.patient.model.PatientOrderrecord;

/**
 * @ClassName PatientOrderrecordService
 * @Description TODO
 * @date 2025��4��8�� ����4:42:41
 * <AUTHOR>
 * @version 1.0
 */
public interface PatientOrderrecordService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��4��8�� ����4:42:41
	 * <AUTHOR>
	 */
	Integer save(PatientOrderrecord record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��4��8�� ����4:42:41
	 * <AUTHOR>
	 */
	Integer update(PatientOrderrecord record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��4��8�� ����4:42:41
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return PatientOrderrecord
	 * @date 2025��4��8�� ����4:42:41
	 * <AUTHOR>
	 */
	PatientOrderrecord selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<PatientOrderrecord>
	 * @date 2025��4��8�� ����4:42:41
	 * <AUTHOR>
	 */
	DataSet<PatientOrderrecord> getDataSetList(Page page, PatientOrderrecord record);
	
	void updateOrSavePatientOrderrecord();

	List<PatientOrderrecord> selectPatientOrderrecord();
}

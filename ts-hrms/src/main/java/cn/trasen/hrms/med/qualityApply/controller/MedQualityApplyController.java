package cn.trasen.hrms.med.qualityApply.controller;

import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.qualityApply.model.MedQualityApply;
import cn.trasen.hrms.med.qualityApply.service.MedQualityApplyService;
import cn.trasen.hrms.train.model.HrmsTrainPlan;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedQualityApplyController
 * @Description TODO
 * @date 2025��2��27�� ����11:15:06
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "质控专员管理")
public class MedQualityApplyController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedQualityApplyController.class);

	@Autowired
	private MedQualityApplyService medQualityApplyService;

	/**
	 * @Title saveMedQualityApply
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��2��27�� ����11:15:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/qualityApply/save")
	public PlatformResult<String> saveMedQualityApply(@RequestBody MedQualityApply record) {
		try {
			medQualityApplyService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedQualityApply
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��2��27�� ����11:15:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/qualityApply/update")
	public PlatformResult<String> updateMedQualityApply(@RequestBody MedQualityApply record) {
		try {
			medQualityApplyService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	

	/**
	 * 
	 * @Title selectMedQualityApplyById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedQualityApply>
	 * @date 2025��2��27�� ����11:15:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/qualityApply/{id}")
	public PlatformResult<MedQualityApply> selectMedQualityApplyById(@PathVariable String id) {
		try {
			MedQualityApply record = medQualityApplyService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "查询已考核、已中止数据", notes = "查询已考核、已中止数据")
	@GetMapping("/api/qualityApply/getMyQualityApply")
	public PlatformResult<List<MedQualityApply>> getMyQualityApply(String applyStatus,String userCode) {
		try {
			List<MedQualityApply> record = medQualityApplyService.getMyQualityApply(applyStatus,userCode);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "查询任期信息", notes = "查询任期信息")
	@GetMapping("/api/qualityApply/getApplyInfo")
	public PlatformResult<String> getApplyInfo(String userCode) {
		try {
			String info = medQualityApplyService.getApplyInfo(userCode);
			return PlatformResult.success(info);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "无质控专员科室", notes = "无质控专员科室")
	@GetMapping("/api/qualityApply/getNoDataApplyDept")
	public PlatformResult<List<Map<String,Object>>> getNoDataApplyDept() {
		try {
			List<Map<String,Object>> list = medQualityApplyService.getNoDataApplyDept();
			return PlatformResult.success(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "考核", notes = "考核")
	@PostMapping("/api/qualityApply/examine")
	public PlatformResult<String> examine(@RequestBody MedQualityApply record) {
		try {
			medQualityApplyService.examine(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "中止", notes = "中止")
	@PostMapping("/api/qualityApply/cancel")
	public PlatformResult<MedQualityApply> cancel(@RequestBody MedQualityApply record) {
		try {
			medQualityApplyService.cancel(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * 
	 * @Title deleteMedQualityApplyById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��2��27�� ����11:15:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/qualityApply/delete/{id}")
	public PlatformResult<String> deleteMedQualityApplyById(@PathVariable String id) {
		try {
			medQualityApplyService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedQualityApplyList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedQualityApply>
	 * @date 2025��2��27�� ����11:15:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/qualityApply/list")
	public DataSet<MedQualityApply> selectMedQualityApplyList(Page page, MedQualityApply record) {
		return medQualityApplyService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "流程回调", notes = "流程回调")
	@PostMapping("/api/qualityApply/finishExamine")
	public void finishExamine(HttpServletRequest request) {
		try {
			
			logger.info("========质控专员管理回调==========");
			
			Map<String, Object> formData = new HashMap<>();
			Enumeration<String> enu = request.getParameterNames();
			while (enu.hasMoreElements()) {
				String key = (String) enu.nextElement();
				formData.put(key, request.getParameter(key));
			}
			// 业务id
			String L_BusinessId = formData.get("L_BusinessId").toString(); //业务id
		    String L_LaunchUserCode = (String) formData.get("L_LaunchUserCode");//流程发起人编码
		    String L_LaunchUserName = (String) formData.get("L_LaunchUserName");//流程发起人名称
		    
		    MedQualityApply record = new MedQualityApply();
		    record.setApplyDate((String) formData.get("applyDate"));
		    record.setApplyStartDate(DateUtil.parse((String) formData.get("applyStartDate"), "yyyy-MM-dd"));
		    record.setApplyEndDate(DateUtil.parse((String) formData.get("applyEndDate"), "yyyy-MM-dd"));
		    record.setWorkExperience((String) formData.get("workExperience"));
		    record.setWorkflowId(L_BusinessId);
		    record.setApplyName(L_LaunchUserName);
		    record.setApplyCode(L_LaunchUserCode);
		    
		    medQualityApplyService.saveOrUpdate(record);
		    
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/qualityApply/export")
	public void export(Page page,HttpServletResponse response, HttpServletRequest request,MedQualityApply record) {
		try {
			page.setPageNo(1);
			page.setPageSize(100000);
			
			// 0待考核  1已考核  2已终止
			String title = "";
			String templateUrl = "";
			String applyStatus = record.getApplyStatus();
			if("0".equals(applyStatus)) {
				title = "(待考核)";
				templateUrl = "template/medQualityApplyExport.xlsx";
			}
			if("1".equals(applyStatus)) {
				title = "(已考核)";
				templateUrl = "template/medQualityApplyExport2.xlsx";
			}
			if("2".equals(applyStatus)) {
				title = "(已中止)";
				templateUrl = "template/medQualityApplyExport3.xlsx";
			}
			
			DataSet<MedQualityApply> dataSet = medQualityApplyService.getDataSetList(page, record);
			List<MedQualityApply> list = dataSet.getRows();
			
			int i = 1;
			for (MedQualityApply medQualityApply : list) {
				medQualityApply.setOrderNumber(i);
				
				if("0".equals(medQualityApply.getExpireStatus())) {
					medQualityApply.setExpireStatus("未过期");
				}else {
					medQualityApply.setExpireStatus("已过期");
				}
				if("0".equals(medQualityApply.getIsOverdue())) {
					medQualityApply.setIsOverdue("否");
				}else {
					medQualityApply.setIsOverdue("是");
				}
				if("1".equals(medQualityApply.getApplyResult())) {
					medQualityApply.setApplyResult("合格");
				}else {
					medQualityApply.setApplyResult("不合格");
				}
				
				medQualityApply.setApplyRealTime(medQualityApply.getApplyRealTime() + "个月");
				
				i++;
			}
			
			//表头标题
            String name = "质控专员信息表" + title + DateUtil.format(new Date(),"yyyyMMdd") + ".xlsx";
            
            Map<String, Object> map = new HashMap<String, Object>();
		    map.put("list", list);
		    map.put("exportDate", DateUtil.format(new Date(),"yyyy-MM-dd"));
		    map.put("exportUserName", UserInfoHolder.getCurrentUserName());
            
            cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, map, name, templateUrl);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}

package cn.trasen.hrms.med.schedule.model;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 排班模板拓展表
 *
 */
@Table(name = "med_schedule_template_extend")
@Setter
@Getter
public class MedScheduleTemplateExtend {
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    @Column(name = "status")
    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private String status;
    
    @Column(name = "employee_no")
    @ApiModelProperty(value = "模板设置人工号")
    private String employeeNo;

    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

}

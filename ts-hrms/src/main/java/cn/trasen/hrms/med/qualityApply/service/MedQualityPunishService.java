package cn.trasen.hrms.med.qualityApply.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.qualityApply.model.MedQualityPunish;

/**
 * @ClassName MedQualityPunishService
 * @Description TODO
 * @date 2025��2��27�� ����11:15:32
 * <AUTHOR>
 * @version 1.0
 */
public interface MedQualityPunishService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��2��27�� ����11:15:32
	 * <AUTHOR>
	 */
	Integer save(MedQualityPunish record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��2��27�� ����11:15:32
	 * <AUTHOR>
	 */
	Integer update(MedQualityPunish record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��2��27�� ����11:15:32
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedQualityPunish
	 * @date 2025��2��27�� ����11:15:32
	 * <AUTHOR>
	 */
	MedQualityPunish selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedQualityPunish>
	 * @date 2025��2��27�� ����11:15:32
	 * <AUTHOR>
	 */
	DataSet<MedQualityPunish> getDataSetList(Page page, MedQualityPunish record);

	void deleteByQualityId(String qualityId);

	List<MedQualityPunish> selectByQualityId(String qualityId);
}

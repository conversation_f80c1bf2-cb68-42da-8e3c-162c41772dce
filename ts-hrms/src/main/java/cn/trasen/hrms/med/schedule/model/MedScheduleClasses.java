package cn.trasen.hrms.med.schedule.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

/**
 * 排班班次
 *
 */
@Table(name = "med_schedule_classes")
@Setter
@Getter
public class MedScheduleClasses {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 班次类别id
     */
    @Column(name = "type_id")
    @ApiModelProperty(value = "班次类别id")
    private String typeId;

    /**
     * 班次名称
     */
    @Column(name = "classes_name")
    @ApiModelProperty(value = "班次名称")
    private String classesName;

    /**
     * 出勤天数
     */
    @Column(name = "classes_days")
    @ApiModelProperty(value = "出勤天数")
    private String classesDays;
    
    @Column(name = "classes_hours")
    @ApiModelProperty(value = "班次时长")
    private String classesHours;

    /**
     * 排班颜色
     */
    @Column(name = "classes_color")
    @ApiModelProperty(value = "排班颜色")
    private String classesColor;

    /**
     * 班次类型
     */
    @Column(name = "classes_type")
    @ApiModelProperty(value = "班次类型")
    private String classesType;

    /**
     * 班次备注
     */
    @Column(name = "classes_remark")
    @ApiModelProperty(value = "班次备注")
    private String classesRemark;

    /**
     * 考勤时间 多个分号隔开
     */
    @Column(name = "classes_worktime")
    @ApiModelProperty(value = "考勤时间 多个逗号隔开")
    private String classesWorktime;

    /**
     * 状态 0禁用  1启用
     */
    @Column(name = "classes_status")
    @ApiModelProperty(value = "状态 0禁用  1启用")
    private String classesStatus;

    /**
     * 班次属性 1全院  2科室
     */
    @Column(name = "class_attributes")
    @ApiModelProperty(value = "班次属性 1全院  2科室")
    private String classAttributes;

    /**
     * 使用范围名称
     */
    @Column(name = "classes_use_names")
    @ApiModelProperty(value = "使用范围名称")
    private String classesUseNames;

    /**
     * 使用范围科室
     */
    @Column(name = "classes_use_org")
    @ApiModelProperty(value = "使用范围科室")
    private String classesUseOrg;

    /**
     * 使用范围人员
     */
    @Column(name = "classes_use_user")
    @ApiModelProperty(value = "使用范围人员")
    private String classesUseUser;

    /**
     * 休息设置  1按国家法定节假日休息 2周末不算休息，法定节日休息  3周末、法定节日都不算休息
     */
    @Column(name = "holiday_type")
    @ApiModelProperty(value = "休息设置  1按国家法定节假日休息 2周末不算休息，法定节日休息  3周末、法定节日都不算休息")
    private String holidayType;
    
    @Column(name = "holiday_classes")
    @ApiModelProperty(value = "是否休息班次  0否 1是")
    private String holidayClasses;
    
    @Column(name = "schedule_statistics")
    @ApiModelProperty(value = "排班统计：0-禁用，1-开启，默认禁用")
    private String scheduleStatistics;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
    
    @Transient
    @ApiModelProperty(value = "查看所有，值非空则查看所有")
    private String isAll;
    
    @Transient
    private String typeName;
    
    /**
     * 可以排班的权限科室编码列表
     */
    @Transient
    private List<String> scheduleOrgList;
    
    /**
     * 可以排班的权限人员工号列表
     */
    @Transient
    private List<String> scheduleUserList;
    
    @Transient
    @ApiModelProperty(value = "是否排班管理员")
    private String isPbAdmin;
    
    @Transient
    @ApiModelProperty(value = "被排班人ID")
    private String employeeId;
    
    @Transient
    @ApiModelProperty(value = "被排班人工号")
    private String employeeNo;
    
    @Transient
    @ApiModelProperty(value = "被排班人组织机构编码")
    private String orgCode;
    
    @Transient
    @ApiModelProperty(value = "班次id集合")
    private String classIds;
    
    @Transient
    @ApiModelProperty(value = "班次id集合-用于后端查询")
    private List<String> classIdList;
    
    @Transient
    @ApiModelProperty(value = "被排班人的权限班次ID集合-用于后端查询")
    private List<String> ids;
}
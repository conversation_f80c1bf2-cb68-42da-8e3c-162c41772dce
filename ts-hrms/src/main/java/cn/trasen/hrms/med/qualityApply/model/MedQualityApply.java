package cn.trasen.hrms.med.qualityApply.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "med_quality_apply")
@Setter
@Getter
public class MedQualityApply {
	
    @Id
    private String id;

    /**
     * 姓名
     */
    @Column(name = "apply_name")
    @ApiModelProperty(value = "姓名")
    private String applyName;

    /**
     * 工号
     */
    @Column(name = "apply_code")
    @ApiModelProperty(value = "工号")
    private String applyCode;

    /**
     * 院区
     */
    @Column(name = "apply_area")
    @ApiModelProperty(value = "院区")
    private String applyArea;

    /**
     * 科室
     */
    @Column(name = "apply_org_id")
    @ApiModelProperty(value = "科室")
    private String applyOrgId;

    /**
     * 身份证号
     */
    @Column(name = "apply_idcard")
    @ApiModelProperty(value = "身份证号")
    private String applyIdcard;

    /**
     * 技术职称
     */
    @Column(name = "apply_technical")
    @ApiModelProperty(value = "技术职称")
    private String applyTechnical;

    /**
     * 联系方式
     */
    @Column(name = "apply_phone")
    @ApiModelProperty(value = "联系方式")
    private String applyPhone;

    /**
     * 申请时间
     */
    @Column(name = "apply_date")
    @ApiModelProperty(value = "申请时间")
    private String applyDate;
    
//    @Column(name = "hold_area")
//    @ApiModelProperty(value = "担任院区")
//    private String holdArea;
//    
//    @Column(name = "hold_org_id")
//    @ApiModelProperty(value = "担任科室")
//    private String holdOrgId;

    /**
     * 任期开始时间
     */
    @Column(name = "apply_start_date")
    @ApiModelProperty(value = "任期开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date applyStartDate;

    /**
     * 任期结束时间
     */
    @Column(name = "apply_end_date")
    @ApiModelProperty(value = "任期结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date applyEndDate;
    
    @Column(name = "work_experience")
    @ApiModelProperty(value = "临床工作经历")
    private String workExperience;

    /**
     * 应任期时长
     */
    @Column(name = "apply_term_time")
    @ApiModelProperty(value = "应任期时长")
    private String applyTermTime;

    /**
     * 实际结束时间
     */
    @Column(name = "apply_real_date")
    @ApiModelProperty(value = "实际结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date applyRealDate;
    
    @Column(name = "apply_search_date")
    private Date applySearchDate;

    /**
     * 已担任时长
     */
    @Column(name = "apply_real_time")
    @ApiModelProperty(value = "已担任时长")
    private String applyRealTime;

    /**
     * 附件
     */
    @Column(name = "apply_files")
    @ApiModelProperty(value = "附件")
    private String applyFiles;

    /**
     * 状态
     */
    @Column(name = "apply_status")
    @ApiModelProperty(value = "状态  0待考核  1已考核  2已终止")
    private String applyStatus;

    /**
     * 考核结果
     */
    @Column(name = "apply_result")
    @ApiModelProperty(value = "考核结果  0考核中  1合格  2不合格")
    private String applyResult;
    
    @Column(name = "workflow_id")
    @ApiModelProperty(value = "流程id")
    private String workflowId;
    
    @Column(name = "is_overdue")
    @ApiModelProperty(value = "是否超期任职 0否 1是")
    private String isOverdue;
    
    @Column(name = "opt_date")
    @ApiModelProperty(value = "考核日期/中止日期")
    private String optDate;
    
    @Column(name = "stop_remark")
    @ApiModelProperty(value = "中止原因")
    private String stopRemark;
    
    @Column(name = "disable_time")
    @ApiModelProperty(value = "禁用申请时间")
    private String disableTime;
    
    @Column(name = "expire_status")
    @ApiModelProperty(value = "过期状态")
    private String expireStatus;
    
    @Column(name = "quality_rate")
    @ApiModelProperty(value = "质控率")
    private String qualityRate;
    
    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    @Transient
    List<MedQualityPunish> qualityPunishList;
    
    @Transient
    private String orgName;
    
    @Transient
    private String startDate;
    
    @Transient
    private String endDate;
    
    @Transient
    private String optStartDate;
    
    @Transient
    private String optEndDate;
    
    @Transient
    private String realStartDate;
    
    @Transient
    private String realEndDate;
    
    @Transient
    private String orgId;
    
    @Transient
    private List<String> orgIds;
    
    @Transient
    private List<String> orgIdList;
    
    @Transient
    private String applyAreaText;
    
    @Transient
    private String holdAreaText;
    
    @Transient
    private String holdOrgName;
    
    @Transient
    private Long punishNumbers;
    
    @Transient
    private int orderNumber;
    
    @Transient
    private String applyResultText;
    
    @Transient
    private String punishResultText;
    
    @Transient
    private String roleName;
    
    @Transient
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date punishEndDate;
    
    @Transient
    private List<String> holdOrgIds;
    
}
package cn.trasen.hrms.med.workplan.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.workplan.model.MedWorkplanRecord;

/**
 * @ClassName MedWorkplanRecordService
 * @Description TODO
 * @date 2024��11��12�� ����11:52:20
 * <AUTHOR>
 * @version 1.0
 */
public interface MedWorkplanRecordService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��11��12�� ����11:52:20
	 * <AUTHOR>
	 */
	Integer save(MedWorkplanRecord record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��11��12�� ����11:52:20
	 * <AUTHOR>
	 */
	Integer update(MedWorkplanRecord record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��11��12�� ����11:52:20
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedWorkplanRecord
	 * @date 2024��11��12�� ����11:52:20
	 * <AUTHOR>
	 */
	MedWorkplanRecord selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedWorkplanRecord>
	 * @date 2024��11��12�� ����11:52:20
	 * <AUTHOR>
	 */
	DataSet<MedWorkplanRecord> getDataSetList(Page page, MedWorkplanRecord record);

	/**
	 * 
	 * @param record
	 */
	void examine(List<MedWorkplanRecord> record);
}

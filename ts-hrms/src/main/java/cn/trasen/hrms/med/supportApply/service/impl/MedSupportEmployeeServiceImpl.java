package cn.trasen.hrms.med.supportApply.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.supportApply.dao.MedSupportEmployeeMapper;
import cn.trasen.hrms.med.supportApply.model.MedSupportEmployee;
import cn.trasen.hrms.med.supportApply.service.MedSupportEmployeeService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedSupportEmployeeServiceImpl
 * @Description TODO
 * @date 2025��1��4�� ����5:27:58
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedSupportEmployeeServiceImpl implements MedSupportEmployeeService {

	@Autowired
	private MedSupportEmployeeMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedSupportEmployee record) {
		record.setId(IdGeneraterUtils.nextId());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedSupportEmployee record) {
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedSupportEmployee record = new MedSupportEmployee();
		record.setId(id);
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedSupportEmployee selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedSupportEmployee> getDataSetList(Page page, MedSupportEmployee record) {
		Example example = new Example(MedSupportEmployee.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<MedSupportEmployee> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<MedSupportEmployee> selectByWorkflowId(String transferRecordId) {
		return mapper.selectByWorkflowId(transferRecordId);
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteByApplyId(String applyId) {
		MedSupportEmployee medSupportEmployee = new MedSupportEmployee();
		medSupportEmployee.setApplyId(applyId);
		mapper.delete(medSupportEmployee);
	}

	@Override
	public List<MedSupportEmployee> selectByApplyId(String applyId) {
		Example example = new Example(MedSupportEmployee.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("applyId", applyId);
		return mapper.selectByExample(example);
	}
	
	
	
	
}

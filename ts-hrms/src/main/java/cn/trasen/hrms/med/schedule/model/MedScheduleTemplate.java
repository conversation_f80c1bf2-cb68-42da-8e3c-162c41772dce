package cn.trasen.hrms.med.schedule.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 排班模板
 *
 */
@Table(name = "med_schedule_template")
@Setter
@Getter
public class MedScheduleTemplate {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 班次id
     */
    @Column(name = "classes_id")
    @ApiModelProperty(value = "班次id")
    private String classesId;

    /**
     * 排班周次
     */
    @Column(name = "schedule_week")
    @ApiModelProperty(value = "排班周次：1-星期一，2-星期二，3-星期三，4-星期四，5-星期五，6-星期六，7-星期日")
    private String scheduleWeek;

    /**
     * 类型id
     */
    @Column(name = "type_id")
    @ApiModelProperty(value = "类型id")
    private String typeId;

    /**
     * 排班人员id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "被排班人员id")
    private String employeeId;

    /**
     * 排班人员机构id
     */
    @Column(name = "emp_org_id")
    @ApiModelProperty(value = "被排班人员机构id")
    private String empOrgId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 状态：0-禁用，1-启用
     */
    @Column(name = "status")
    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private String status;

    @Column(name = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
}
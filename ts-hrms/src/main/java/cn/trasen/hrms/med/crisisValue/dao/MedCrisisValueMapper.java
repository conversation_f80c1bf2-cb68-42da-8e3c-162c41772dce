package cn.trasen.hrms.med.crisisValue.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.med.crisisValue.model.MedCrisisValue;
import tk.mybatis.mapper.common.Mapper;

public interface MedCrisisValueMapper extends Mapper<MedCrisisValue> {

	List<MedCrisisValue> getDataSetList(MedCrisisValue record, Page page);

	Map<String, Object> indexStatistics(MedCrisisValue record);

	Map<String, Object> tableStatistics(MedCrisisValue record);

	Long selectMyCrisisValue(@Param("currentUserCode") String currentUserCode);
}
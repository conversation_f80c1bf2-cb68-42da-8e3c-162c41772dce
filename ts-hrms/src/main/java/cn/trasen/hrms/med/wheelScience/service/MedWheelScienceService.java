package cn.trasen.hrms.med.wheelScience.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.wheelScience.model.MedWheelScience;

/**
 * @ClassName MedWheelScienceService
 * @Description TODO
 * @date 2025��1��22�� ����2:57:52
 * <AUTHOR>
 * @version 1.0
 */
public interface MedWheelScienceService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��1��22�� ����2:57:52
	 * <AUTHOR>
	 */
	Integer save(MedWheelScience record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��1��22�� ����2:57:52
	 * <AUTHOR>
	 */
	Integer update(MedWheelScience record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��1��22�� ����2:57:52
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedWheelScience
	 * @date 2025��1��22�� ����2:57:52
	 * <AUTHOR>
	 */
	MedWheelScience selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedWheelScience>
	 * @date 2025��1��22�� ����2:57:52
	 * <AUTHOR>
	 */
	DataSet<MedWheelScience> getDataSetList(Page page, MedWheelScience record);
}

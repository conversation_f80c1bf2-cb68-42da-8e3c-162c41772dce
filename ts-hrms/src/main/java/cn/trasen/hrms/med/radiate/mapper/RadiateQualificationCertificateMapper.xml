<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.radiate.dao.RadiateQualificationCertificateMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.radiate.model.RadiateQualificationCertificate">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_no" jdbcType="VARCHAR" property="employeeNo" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="issue_date" jdbcType="VARCHAR" property="issueDate" />
    <result column="certificate_number" jdbcType="VARCHAR" property="certificateNumber" />
    <result column="files" jdbcType="VARCHAR" property="files" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_dept" jdbcType="VARCHAR" property="createDept" />
    <result column="create_dept_name" jdbcType="VARCHAR" property="createDeptName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>
  
   <select id="selectRadiateQualificationCertificatePagelist" resultType="cn.trasen.hrms.med.radiate.model.RadiateQualificationCertificate" parameterType="cn.trasen.hrms.med.radiate.vo.RadiateCertificateReqVo">
	 select c.id,c.employee_id,c.employee_no,c.employee_name,c.certificate_number,c.certificate_type,c.files,c.issue_date,
	 c.create_date,c.create_user,c.create_user_name,c.update_user,c.update_user_name,c.update_date,c.is_deleted,
	 e.gender sex,e.org_id,o.NAME AS org_name,e.employee_status,e.sso_org_code,e.sso_org_name,
	 i.technical technical_title
	 from med_radiate_qualification_certificate c
	 left join  med_radiate_personnel_register  m on m.employee_id=c.employee_id
	 left join cust_emp_base e on e.employee_id=m.employee_id
	 left join cust_emp_info i on e.employee_id = i.info_id
	 LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted = 'N' 
	 where c.is_deleted='N' and m.is_deleted='N' and e.is_deleted ='N'
 	 <if test="issueDateBegin !=null and issueDateBegin !=''">
  		and c.issue_date   >=  #{issueDateBegin}   
 	 </if>
 	 <if test="issueDateEnd !=null and issueDateEnd !=''">
  		and c.issue_date    &lt;=   #{issueDateEnd}   
 	 </if>
 	  <if test="orgId !=null and orgId !=''">
  		and o.organization_id = #{orgId}
 	 </if>
 	 <if test="orgIdList != null and orgIdList.size() > 0">
		and (o.organization_id in
		<foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
      )
	 </if>
 	 <if test="employeeNo !=null and employeeNo !=''">
	  	and c.employee_no = #{employeeNo}  
	 </if>
	 <if test="employeeId !=null and employeeId !=''">
	  	and c.employee_id = #{employeeId}  
	 </if>
	 <if test="employeeName !=null and employeeName !=''">
	  	and c.employee_name like concat('%',#{employeeName},'%') 
	 </if>
 	 <if test="employeeStatus !=null and employeeStatus !=''">
  		and e.employee_status = #{employeeStatus}  
 	 </if>
	 <if test="certificateType !=null and certificateType !=''">
	  	and c.certificate_type = #{certificateType}  
	 </if>
 	 <if test="technicalTitle !=null and technicalTitle !=''">
  		and i.technical like concat('%',#{technicalTitle},'%')  
 	 </if>
 	 <if test="condition !=null and condition !=''">
	  	and (c.employee_name  like concat('%',#{condition},'%') or  c.employee_no like concat('%',#{condition},'%')  ) 
	 </if>
	 <if test="certificateNumber !=null and certificateNumber !=''">
	  	and c.certificate_number  like concat('%',#{certificateNumber},'%') 
	 </if>
	 order by c.update_date desc, c.create_date asc 
   </select>
</mapper>
package cn.trasen.hrms.med.radiate.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.radiate.model.RadiateCheckupAbnormal;
import cn.trasen.hrms.med.radiate.service.RadiateCheckupAbnormalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName RadiateCheckupAbnormalController
 * @Description TODO
 * @date 2025��2��12�� ����9:50:28
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "放射体检复查登记")
public class RadiateCheckupAbnormalController {

	private transient static final Logger logger = LoggerFactory.getLogger(RadiateCheckupAbnormalController.class);

	@Autowired
	private RadiateCheckupAbnormalService medRadiateCheckupAbnormalService;

	/**
	 * @Title saveMedRadiateCheckupAbnormal
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��2��12�� ����9:50:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/RadiateCheckupAbnormal/save")
	public PlatformResult<String> saveMedRadiateCheckupAbnormal(@RequestBody RadiateCheckupAbnormal record) {
		try {
			medRadiateCheckupAbnormalService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * @Title batchSaveMedRadiateCheckupAbnormal
	 * @Description 批量保存
	 * @param records
	 * @return PlatformResult<String>
	 * @date 2025-06-06 14:50:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "批量保存", notes = "批量保存")
	@PostMapping("/api/RadiateCheckupAbnormal/batchSave")
	public PlatformResult<String> batchSaveMedRadiateCheckupAbnormal(@RequestBody List<RadiateCheckupAbnormal> records) {
		try {
			medRadiateCheckupAbnormalService.batchSave(records);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedRadiateCheckupAbnormal
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��2��12�� ����9:50:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/RadiateCheckupAbnormal/update")
	public PlatformResult<String> updateMedRadiateCheckupAbnormal(@RequestBody RadiateCheckupAbnormal record) {
		try {
			medRadiateCheckupAbnormalService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedRadiateCheckupAbnormalById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedRadiateCheckupAbnormal>
	 * @date 2025��2��12�� ����9:50:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/RadiateCheckupAbnormal/{id}")
	public PlatformResult<RadiateCheckupAbnormal> selectMedRadiateCheckupAbnormalById(@PathVariable String id) {
		try {
			RadiateCheckupAbnormal record = medRadiateCheckupAbnormalService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedRadiateCheckupAbnormalById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��2��12�� ����9:50:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/RadiateCheckupAbnormal/delete/{id}")
	public PlatformResult<String> deleteMedRadiateCheckupAbnormalById(@PathVariable String id) {
		try {
			medRadiateCheckupAbnormalService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedRadiateCheckupAbnormalList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedRadiateCheckupAbnormal>
	 * @date 2025��2��12�� ����9:50:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/RadiateCheckupAbnormal/list")
	public DataSet<RadiateCheckupAbnormal> selectMedRadiateCheckupAbnormalList(Page page, RadiateCheckupAbnormal record) {
		return medRadiateCheckupAbnormalService.getDataSetList(page, record);
	}
}

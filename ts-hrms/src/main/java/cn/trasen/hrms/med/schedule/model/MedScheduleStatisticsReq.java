package cn.trasen.hrms.med.schedule.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 排班统计入参
 * <AUTHOR>
 *
 */
@Setter
@Getter
public class MedScheduleStatisticsReq {

    @ApiModelProperty(value = "统计类型：1-个人，2-科室，3-班次分类统计，默认个人")
	private String type;

    @ApiModelProperty(value = "查询关键字（姓名/联系方式）")
	private String keywords;

    @ApiModelProperty(value = "院区编码")
	private String hospCode;

    @ApiModelProperty(value = "归属科室ID，多个用逗号隔开")
	private String deptIds;

    @ApiModelProperty(value = "职称")
	private String technical;

    @ApiModelProperty(value = "排班开始时间默认上个月第一天，格式 yyyy-MM-dd")
	private String startDate;

    @ApiModelProperty(value = "排班结束时间默认上个月最后一天，格式 yyyy-MM-dd")
	private String endDate;

    @ApiModelProperty(value = "员工ID-用于查看明细统计数据")
	private String employeeId;

    @ApiModelProperty(value = "科室ID-用于查看明细统计数据")
	private String deptId;
	
	@ApiModelProperty(value = "在职状态")
	private String employeeStatus;
	
	@ApiModelProperty(value = "班次ID,多个逗号隔开")
	private String classesIds;
	
	@ApiModelProperty(value = "oa数据类型,请假，销假，进修/培训等，多个用逗号隔开")
	private String oaType;
	
	@ApiModelProperty(value = "科室明细类型：1-班次数据，2-OA数据，默认班次数据")
	private String deptExportType;

}

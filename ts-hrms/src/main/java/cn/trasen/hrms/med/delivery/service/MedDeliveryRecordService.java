package cn.trasen.hrms.med.delivery.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.delivery.model.MedDeliveryRecord;

/**
 * @ClassName MedDeliveryRecordService
 * @Description TODO
 * @date 2025��5��20�� ����3:31:41
 * <AUTHOR>
 * @version 1.0
 */
public interface MedDeliveryRecordService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��5��20�� ����3:31:41
	 * <AUTHOR>
	 */
	Integer save(MedDeliveryRecord record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��5��20�� ����3:31:41
	 * <AUTHOR>
	 */
	Integer update(MedDeliveryRecord record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��5��20�� ����3:31:41
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedDeliveryRecord
	 * @date 2025��5��20�� ����3:31:41
	 * <AUTHOR>
	 */
	MedDeliveryRecord selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedDeliveryRecord>
	 * @date 2025��5��20�� ����3:31:41
	 * <AUTHOR>
	 */
	DataSet<MedDeliveryRecord> getDataSetList(Page page, MedDeliveryRecord record);
}

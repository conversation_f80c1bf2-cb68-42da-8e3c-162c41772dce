<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.radiate.dao.RadiatePersonnelRegisterMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_no" jdbcType="VARCHAR" property="employeeNo" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="sex" jdbcType="VARCHAR" property="sex" />
    <result column="birthday" jdbcType="VARCHAR" property="birthday" />
    <result column="education" jdbcType="VARCHAR" property="education" />
    <result column="technical_title" jdbcType="VARCHAR" property="technicalTitle" />
    <result column="employee_status" jdbcType="VARCHAR" property="employeeStatus" />
    <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
    <result column="radiation_type" jdbcType="VARCHAR" property="radiationType" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_dept" jdbcType="VARCHAR" property="createDept" />
    <result column="create_dept_name" jdbcType="VARCHAR" property="createDeptName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>
  
  <select id="selectRadiatePersonnelRegisterPageList" resultType="cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister" parameterType="cn.trasen.hrms.med.radiate.vo.RadiatePersonnelReqVo">
	 select m.id,m.employee_id,m.employee_no,m.employee_name,m.operation_type,m.radiation_type,m.remarks,m.create_date,m.create_user,m.create_user_name,
	 m.create_dept,m.create_dept_name,m.update_user,m.update_user_name,m.update_date,m.is_deleted,
	 e.gender sex,e.org_id,o.NAME AS org_name,e.employee_status,date_format(e.birthday, '%Y-%m-%d') birthday,e.sso_org_code,e.sso_org_name,
	 i.technical technical_title,i.first_education_type education,
	 cu.checkup_date, cu.status checkup_status,cu.id checkup_id,cu.checkup_result checkup_current_result,cu.expiration_date checkup_end_date, cua.review_result checkup_latest_result,
	 (select count(1) from med_radiate_qualification_certificate where employee_id=m.employee_id and is_deleted = 'N') as certificate_count,
	 (select count(1) from med_radiate_checkup_register where employee_id=m.employee_id and is_deleted = 'N') as checkup_count,
	 (select count(1) from med_radiate_training_register where employee_id=m.employee_id and is_deleted = 'N') as training_count,
	 (select count(1) from med_radiate_dose_monitoring where employee_id=m.employee_id and is_deleted = 'N') as dose_count,
	 (select count(a1.id) from med_radiate_checkup_abnormal a1
	  left join med_radiate_checkup_register a2 on a1.checkup_id=a2.id
	  where a1.is_deleted = 'N' and a2.is_deleted = 'N' and a2.employee_id=m.employee_id and cu.id=a2.id
	  ) as checkup_abnormal_count,
	 e.identity_number emp_id_card
	 from  med_radiate_personnel_register  m
	 left join cust_emp_base e on e.employee_id=m.employee_id
	 left join cust_emp_info i on e.employee_id = i.info_id
	 LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted = 'N' 
	 LEFT JOIN (
	    SELECT b1.*
	    FROM med_radiate_checkup_register b1
	    LEFT JOIN med_radiate_checkup_register b2 
	        ON b1.employee_id = b2.employee_id AND b1.checkup_date &lt; b2.checkup_date
	    WHERE b2.employee_id IS NULL
	) cu on cu.employee_id=m.employee_id  and cu.is_deleted ='N'
	 LEFT JOIN (
	    SELECT b1.*
	    FROM med_radiate_checkup_abnormal b1
	    LEFT JOIN med_radiate_checkup_abnormal b2 
	        ON b1.checkup_id = b2.checkup_id AND b1.review_date &lt; b2.review_date
	    WHERE b2.checkup_id IS NULL
	) cua on cua.checkup_id=cu.id  and cua.is_deleted ='N'
	 where m.is_deleted='N' and e.is_deleted ='N' and m.sso_org_code = #{ssoOrgCode}
	 
 	 <if test="sex !=null and sex !=''">
  		and e.gender = #{sex}
 	 </if>
 	 <if test="radiationType !=null and radiationType !=''">
 	 	and m.radiation_type = #{radiationType}  
 	 </if>
 	 <if test="employeeStatus !=null and employeeStatus !=''">
  		and e.employee_status = #{employeeStatus}  
 	 </if>
 	  <if test="education !=null and education !=''">
  		and i.first_education_type = #{education}  
 	 </if>
 	 <if test="technicalTitle !=null and technicalTitle !=''">
  		and i.technical like concat('%',#{technicalTitle},'%')  
 	 </if>
 	 <if test="orgId !=null and orgId !=''">
  		and o.organization_id = #{orgId}
 	 </if>
 	 <if test="orgIdList != null and orgIdList.size() > 0">
		and (o.organization_id in
		<foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
      )
	 </if>
 	 <if test="employeeNo !=null and employeeNo !=''">
	  	and m.employee_no = #{employeeNo}  
	 </if>
	 <if test="employeeId !=null and employeeId !=''">
	  	and m.employee_id = #{employeeId}  
	 </if>
	 <if test="employeeName !=null and employeeName !=''">
	  	and m.employee_name like concat('%',#{employeeName},'%') 
	 </if>
 	 <if test="condition !=null and condition !=''">
	  	and (m.employee_name  like concat('%',#{condition},'%') or  m.employee_no like concat('%',#{condition},'%')  ) 
	 </if>
	  <if test="operationType !=null and operationType !=''">
	  	and m.operation_type  like concat('%',#{operationType},'%') 
	 </if>
	  <if test="checkupStatus !=null and checkupStatus !=''">
	  	and cu.status = #{checkupStatus}  
	 </if>
	 <choose>
        <when test='checkupEndStatus != null and checkupEndStatus != "" and checkupEndStatus == "1"'>
            and cu.expiration_date  &lt; #{today}
        </when>
        <when test='checkupEndStatus != null and checkupEndStatus != "" and checkupEndStatus == "0"'>
            and (cu.expiration_date  > #{today} or cu.expiration_date  is null)
        </when>
        <otherwise>
            
        </otherwise>
    </choose>
	 order by m.update_date desc, m.create_date asc 
  </select>
  
  <select id="getList" resultType="cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister" parameterType="cn.trasen.hrms.med.radiate.vo.RadiatePersonnelReqVo">
	 select m.*
	 from  med_radiate_personnel_register  m
	 left join cust_emp_base e on e.employee_id=m.employee_id
	 left join cust_emp_info i on e.employee_id = i.info_id
	 where m.is_deleted='N' and e.is_deleted ='N' and m.sso_org_code = #{ssoOrgCode}
	 
 	 <if test="condition !=null and condition !=''">
	  	and (m.employee_name  like concat('%',#{condition},'%') or  m.employee_no like concat('%',#{condition},'%')  ) 
	 </if>
	 order by m.update_date desc, m.create_date asc 
  </select>
	  
</mapper>
package cn.trasen.hrms.med.crisisValue.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.crisisValue.model.MedHisEmployee;
import cn.trasen.hrms.med.crisisValue.service.MedHisEmployeeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedHisEmployeeController
 * @Description TODO
 * @date 2025��5��26�� ����4:18:09
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "MedHisEmployeeController")
public class MedHisEmployeeController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedHisEmployeeController.class);

	@Autowired
	private MedHisEmployeeService medHisEmployeeService;

	/**
	 * @Title saveMedHisEmployee
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��5��26�� ����4:18:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/medHisEmployee/save")
	public PlatformResult<String> saveMedHisEmployee(@RequestBody MedHisEmployee record) {
		try {
			medHisEmployeeService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedHisEmployee
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��5��26�� ����4:18:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/medHisEmployee/update")
	public PlatformResult<String> updateMedHisEmployee(@RequestBody MedHisEmployee record) {
		try {
			medHisEmployeeService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedHisEmployeeById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedHisEmployee>
	 * @date 2025��5��26�� ����4:18:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/medHisEmployee/{id}")
	public PlatformResult<MedHisEmployee> selectMedHisEmployeeById(@PathVariable String id) {
		try {
			MedHisEmployee record = medHisEmployeeService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedHisEmployeeById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��5��26�� ����4:18:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/medHisEmployee/delete/{id}")
	public PlatformResult<String> deleteMedHisEmployeeById(@PathVariable String id) {
		try {
			medHisEmployeeService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedHisEmployeeList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedHisEmployee>
	 * @date 2025��5��26�� ����4:18:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/medHisEmployee/list")
	public DataSet<MedHisEmployee> selectMedHisEmployeeList(Page page, MedHisEmployee record) {
		return medHisEmployeeService.getDataSetList(page, record);
	}
}

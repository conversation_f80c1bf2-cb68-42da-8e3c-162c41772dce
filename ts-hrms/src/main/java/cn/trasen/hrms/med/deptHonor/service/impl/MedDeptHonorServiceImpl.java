package cn.trasen.hrms.med.deptHonor.service.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.hrms.med.deptHonor.dao.MedDeptHonorMapper;
import cn.trasen.hrms.med.deptHonor.model.MedDeptHonor;
import cn.trasen.hrms.med.deptHonor.model.MedDeptHonorWinner;
import cn.trasen.hrms.med.deptHonor.service.MedDeptHonorService;
import cn.trasen.hrms.med.deptHonor.service.MedDeptHonorWinnerService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedDeptHonorServiceImpl
 * @Description TODO
 * @date 2025��1��3�� ����3:27:47
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedDeptHonorServiceImpl implements MedDeptHonorService {

	@Autowired
	private MedDeptHonorMapper mapper;
	
	@Autowired
	private MedDeptHonorWinnerService medDeptHonorWinnerService;
	
	@Autowired
	private HrmsOrganizationFeignService hrmsOrganizationFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedDeptHonor record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		if(CollectionUtils.isNotEmpty(record.getWinnerList())) {
			for (MedDeptHonorWinner winner : record.getWinnerList()) {
				winner.setHonorId(record.getId());
				medDeptHonorWinnerService.save(winner);
			}
		}
		
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedDeptHonor record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		medDeptHonorWinnerService.deleteByHonorId(record.getId());
		
		if(CollectionUtils.isNotEmpty(record.getWinnerList())) {
			
			for (MedDeptHonorWinner winner : record.getWinnerList()) {
				winner.setHonorId(record.getId());
				medDeptHonorWinnerService.save(winner);
			}
		}
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedDeptHonor record = new MedDeptHonor();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedDeptHonor selectById(String id) {
	
		Assert.hasText(id, "ID不能为空.");
		
		MedDeptHonor medDeptHonor = mapper.selectByPrimaryKey(id);
		
		List<MedDeptHonorWinner> winnerList = medDeptHonorWinnerService.selectByHonorId(id);
		
		medDeptHonor.setWinnerList(winnerList);
		
		return medDeptHonor;
	}

	@Override
	public DataSet<MedDeptHonor> getDataSetList(Page page, MedDeptHonor record) {
		
		if(StringUtils.isNotBlank(record.getOrgId())) {
			 PlatformResult<List<String>> result = hrmsOrganizationFeignService.getHrmsOrganizationAndNextList(record.getOrgId());
	          if(result.isSuccess()) {
	        	  List<String> childsList = result.getObject();
	        	  record.setChildsList(childsList);
	          }
		}
		
		if(StringUtils.isNotBlank(record.getHonorName())) {//拆词拼接%号
			 String output = record.getHonorName().chars().mapToObj(c -> (char) c).map(c -> "%" + c).collect(Collectors.joining());
	         record.setHonorName(output.substring(1));
		}
		
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		
		List<MedDeptHonor> records = mapper.getDataSetList(record, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}

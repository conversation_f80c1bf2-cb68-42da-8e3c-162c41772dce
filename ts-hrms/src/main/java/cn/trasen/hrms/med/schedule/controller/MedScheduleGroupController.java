package cn.trasen.hrms.med.schedule.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.schedule.model.MedScheduleGroup;
import cn.trasen.hrms.med.schedule.model.MedScheduleType;
import cn.trasen.hrms.med.schedule.service.MedScheduleGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedScheduleGroupController
 * @Description TODO
 * @date 2025��4��3�� ����5:49:53
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "排班分组设置-新")
public class MedScheduleGroupController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedScheduleGroupController.class);

	@Autowired
	private MedScheduleGroupService medScheduleGroupService;

	/**
	 * @Title saveMedScheduleGroup
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��3�� ����5:49:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/scheduleGroup/save")
	public PlatformResult<String> saveMedScheduleGroup(@RequestBody MedScheduleGroup record) {
		try {
			medScheduleGroupService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedScheduleGroup
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��3�� ����5:49:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/scheduleGroup/update")
	public PlatformResult<String> updateMedScheduleGroup(@RequestBody MedScheduleGroup record) {
		try {
			medScheduleGroupService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedScheduleGroupById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedScheduleGroup>
	 * @date 2025��4��3�� ����5:49:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/scheduleGroup/{id}")
	public PlatformResult<MedScheduleGroup> selectMedScheduleGroupById(@PathVariable String id) {
		try {
			MedScheduleGroup record = medScheduleGroupService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedScheduleGroupById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��4��3�� ����5:49:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/scheduleGroup/delete/{id}")
	public PlatformResult<String> deleteMedScheduleGroupById(@PathVariable String id) {
		try {
			medScheduleGroupService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedScheduleGroupList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleGroup>
	 * @date 2025��4��3�� ����5:49:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/scheduleGroup/list")
	public DataSet<MedScheduleGroup> selectMedScheduleGroupList(Page page, MedScheduleGroup record) {
		return medScheduleGroupService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "排序", notes = "排序")
    @PostMapping("/api/scheduleGroup/updateSeq")
    public PlatformResult<String> updateSeq(@RequestBody List<MedScheduleGroup> records) {
        try {
        	medScheduleGroupService.updateSeq(records);
            return PlatformResult.success();
        } catch (Exception e) {
        	e.printStackTrace();
        	return PlatformResult.failure(e.getMessage());
        }
    }
}

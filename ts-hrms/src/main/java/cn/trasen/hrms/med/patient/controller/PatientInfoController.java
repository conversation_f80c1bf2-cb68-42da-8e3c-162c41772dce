package cn.trasen.hrms.med.patient.controller;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.hrms.med.patient.model.PatientInfo;
import cn.trasen.hrms.med.patient.model.PatientInfoInParameter;
import cn.trasen.hrms.med.patient.service.PatientInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * @ClassName PatientInfoController
 * @Description TODO
 * @date 2025��4��7�� ����3:32:28
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "PatientInfoController")
@Log4j2
public class PatientInfoController {

	private transient static final Logger logger = LoggerFactory.getLogger(PatientInfoController.class);

	@Autowired
	private PatientInfoService patientInfoService;

	@Autowired
	private HrmsOrganizationFeignService hrmsOrganizationService;
	
	@Value("${ftpFileLinkUser:}")
	private String ftpFileLinkUser; //ftp账号
	
	@Value("${ftpFileLinkPassword:}")
	private String ftpFileLinkPassword; //ftp密码
	/**
	 * @Title savePatientInfo
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/patientInfo/save")
	public PlatformResult<String> savePatientInfo(@RequestBody PatientInfo record) {
		try {
			patientInfoService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updatePatientInfo
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/patientInfo/update")
	public PlatformResult<String> updatePatientInfo(@RequestBody PatientInfo record) {
		try {
			patientInfoService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectPatientInfoById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<PatientInfo>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/patientInfo/{id}")
	public PlatformResult<PatientInfo> selectPatientInfoById(@PathVariable String id) {
		try {
			PatientInfo record = patientInfoService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deletePatientInfoById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/patientInfo/delete/{id}")
	public PlatformResult<String> deletePatientInfoById(@PathVariable String id) {
		try {
			patientInfoService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectPatientInfoList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<PatientInfo>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/patientInfo/list")
	public DataSet<PatientInfo> selectPatientInfoList(Page page, PatientInfo record) {
		return patientInfoService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectPatientInfoList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<PatientInfo>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/patientInfo/pageList")
	public DataSet<PatientInfo> selectPatientInfopageList(Page page, PatientInfo record) {
		return patientInfoService.getPageList(page, record);
	}
	
	/**
	 * @Title getQueryInPatientOrder
	 * @Description 查询医嘱信息
	 * @param page
	 * @param record
	 * @return DataSet<Map<String,Object>>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询医嘱信息", notes = "查询医嘱信息")
    @PostMapping("/api/patientInfo/his/getQueryInPatientOrder")
	public  DataSet<Map<String,Object>> getQueryInPatientOrder(Page page, PatientInfoInParameter parame) {
		return patientInfoService.getQueryInPatientOrder(page,parame);
	}
	
	
	/**
	 * @Title getExamRptRequestNew
	 * @Descriptio  查询检查报告项目信息
	 * @param page
	 * @param record
	 * @return DataSet<Map<String,Object>>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询检查报告项目信息", notes = "查询检查报告项目信息")
    @PostMapping("/api/patientInfo/his/getExamRptRequestNew")
	public  List<Map<String,Object>> getExamRptRequestNew(PatientInfoInParameter parame) {
		return patientInfoService.getExamRptRequestNew(parame);
	}
	
	
	/**
	 * @Title getTestRptItemRequestNew
	 * @Descriptio  查询检验报告项目信息
	 * @param page
	 * @param record
	 * @return DataSet<Map<String,Object>>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询检验报告项目信息", notes = "查询检验报告项目信息")
    @PostMapping("/api/patientInfo/his/getTestRptItemRequestNew")
	public  List<Map<String,Object>> getTestRptItemRequestNew(PatientInfoInParameter parame) {
		return patientInfoService.getTestRptItemRequestNew(parame);
	}
	
	@ApiOperation(value = "查询检验报告项目信息(心电图系统)", notes = "查询检验报告项目信息(心电图系统)")
    @PostMapping("/api/patientInfo/his/getTestRptItemRequestNewXdt")
	public  List<Map<String,Object>> getTestRptItemRequestNewXdt(PatientInfoInParameter parame) {
		return patientInfoService.getTestRptItemRequestNewXdt(parame);
	}
	
	
	/**
	 * @Title getTestRptItemDetailNew
	 * @Descriptio  查询检验报告项目信息(结果明细查询)
	 * @param page
	 * @param record
	 * @return DataSet<Map<String,Object>>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询检验报告项目信息(结果明细查询)", notes = "查询检验报告项目信息(结果明细查询)")
    @PostMapping("/api/patientInfo/his/getTestRptItemDetailNew")
	public  List<Map<String,Object>> getTestRptItemDetailNew(PatientInfoInParameter parame) {
		return patientInfoService.getTestRptItemDetailNew(parame);
	}
	
	
	/**
	 * @Title getQueryInpEmrFileInfo
	 * @Descriptio  查询住院病历
	 * @param page
	 * @param record
	 * @return DataSet<Map<String,Object>>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询住院病历", notes = "查询住院病历")
    @PostMapping("/api/patientInfo/his/getQueryInpEmrFileInfo")
	public  List<Map<String,Object>> getQueryInpEmrFileInfo(PatientInfoInParameter parame) {
		return patientInfoService.getQueryInpEmrFileInfo(parame);
	}
	
	/**
	 * @Title getQueryInpEmrFileInfoPdfStream
	 * @Descriptio  查询住院病历(PDF流)
	 * @param page
	 * @param record
	 * @return DataSet<Map<String,Object>>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询住院病历(PDF流)", notes = "查询住院病历(PDF流)")
    @PostMapping("/api/patientInfo/his/getQueryInpEmrFileInfoPdfStream")
	public  List<Map<String,Object>> getQueryInpEmrFileInfoPdfStream(PatientInfoInParameter parame) {
		return patientInfoService.getQueryInpEmrFileInfoPdfStream(parame);
	}
	
	/**
	 * @Title getQueryInPatientAccountInfo
	 * @Description 查询住院病人账目信息
	 * @param page
	 * @param record
	 * @return DataSet<Map<String,Object>>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询住院病人账目信息", notes = "查询住院病人账目信息")
    @PostMapping("/api/patientInfo/his/getQueryInPatientAccountInfo")
	public  DataSet<Map<String,Object>> getQueryInPatientAccountInfo(Page page, PatientInfoInParameter parame) {
		return patientInfoService.getQueryInPatientAccountInfo(page,parame);
	}
	
	
	/**
	 * @Title getQueryInPatFeeItemSum
	 * @Description 查询住院清单费用汇总
	 * @param page
	 * @param record
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询住院清单费用汇总", notes = "查询住院清单费用汇总")
    @PostMapping("/api/patientInfo/his/getQueryInPatFeeItemSum")
	public  Map<String,Object> getQueryInPatFeeItemSum(PatientInfoInParameter parame) {
		return patientInfoService.getQueryInPatFeeItemSum(parame);
	}
	
	/**
	 * @Title getQueryInPatientOperationRecord
	 * @Description 查询查询手术记录
	 * @param page
	 * @param record
	 * @return DataSet<Map<String,Object>>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询手术记录", notes = "查询手术记录")
    @PostMapping("/api/patientInfo/his/getQueryInPatientOperationRecord")
	public  DataSet<Map<String,Object>> getQueryInPatientOperationRecord(Page page, PatientInfoInParameter parame) {
		return patientInfoService.getQueryInPatientOperationRecord(page,parame);
	}
	
	/**
	 * @Title getQueryInPatient
	 * @Description 查询住院患者
	 * @param page
	 * @param record
	 * @return DataSet<Map<String,Object>>
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询住院患者", notes = "查询住院患者")
    @PostMapping("/api/patientInfo/his/getQueryInPatient")
	public  DataSet<Map<String,Object>> getQueryInPatient(Page page, PatientInfoInParameter parame) {
		//科室数据权限
		if(StrUtil.isNotEmpty(parame.getDeptIds())){
			
		}else if(!UserInfoHolder.ISADMIN() && !UserInfoHolder.ISALL() && !UserInfoHolder.getRight("YWHZGL")){//医务患者管理角色
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if(null != user){
				List<String> orgIds = hrmsOrganizationService.getHrmsOrganizationAndNextList(user.getDeptId()).getObject();
				String orgRang = UserInfoHolder.getOrgRang();
				if(StrUtil.isNotEmpty(orgRang)){
					orgRang = StrUtil.replace(StrUtil.removeSuffix(StrUtil.removePrefix(orgRang, "("), ")"), "'", "");
					orgIds.addAll(ListUtil.of(orgRang.split(",")));
				}
				parame.setDeptIds(orgIds.stream().distinct().collect(Collectors.joining(",")));
				//parame.setOrgIdList(orgIds.stream().distinct().collect(Collectors.toList()));
			}
		}
		return patientInfoService.getQueryInPatient(page,parame);
	}
	
	
	@ApiOperation(value = "将pdf文件内网访问地址，转换成Base64字符串", notes = "将pdf文件内网访问地址，转换成Base64字符串")
	@GetMapping("/api/patientInfo/his/getBase64Pdf")
	public  String getBase64Pdf(HttpServletRequest request,HttpServletResponse response) {
		String filePath = request.getParameter("filePath");//
		String fileType = request.getParameter("fileType") == null ? "1" : request.getParameter("fileType");//1http;2ftp
		String base64Str = "";
		try {
			if("1".equals(fileType)) {
				URL url = new URL(filePath); // 替换为实际的文件URL
	            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
	            InputStream inputStream = connection.getInputStream();
	            if (inputStream == null) {
	                log.error("=>获取文件流失败!");
	            }
	            BufferedInputStream bf = new BufferedInputStream(inputStream);
	            byte[] byteArray = toByteArray(bf);
	            base64Str = Base64.getEncoder().encodeToString(byteArray).trim();
			}else {
				URL url = URLUtil.url(filePath);
                String host = url.getHost();
                Integer port = url.getPort();
                if (port.intValue() == -1) {
                    port = url.getDefaultPort();
                }
                String path = url.getPath();

                FTPClient ftpClient = new FTPClient();
                ftpClient.connect(host, port);
                //ftpClient.login("账号", "密码");//需业务系统提供
                ftpClient.login(ftpFileLinkUser, ftpFileLinkPassword);
                ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
                // login后设置被动模式
                ftpClient.enterLocalPassiveMode();
                // login后设置编码
                String LOCAL_CHARSET = "GBK";
                // 开启服务器对UTF-8的支持，如果服务器支持就用UTF-8编码，否则就使用本地编码（GBK）.
                if (FTPReply.isPositiveCompletion(ftpClient.sendCommand("OPTS UTF8", "ON"))) {
                    LOCAL_CHARSET = "UTF-8";
                }
                ftpClient.setControlEncoding(LOCAL_CHARSET);

                InputStream inputStream = ftpClient.retrieveFileStream(new String(path.getBytes("UTF-8"), FTP.DEFAULT_CONTROL_ENCODING));
                if (inputStream == null) {
                    log.error("=>获取文件流失败!");
                }
                // 获取文件大小
                int fileBig = inputStream.available();
                byte[] fileBytes = IoUtil.readBytes(inputStream);
                // 获取文件内容
                base64Str = Base64.getEncoder().encodeToString(fileBytes);
			}
	        return base64Str; // 返回Base64编码后的字符串
	        } catch (IOException e) {
	            throw new RuntimeException("=>获取文件流-异常!", e);
	        }
	}
	
	
    private static byte[] toByteArray(BufferedInputStream bis) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int read;
        while ((read = bis.read(buffer, 0, buffer.length)) != -1) {
            baos.write(buffer, 0, read);
        }
        return baos.toByteArray();
    }
    
    /**
	 * @Description 查询交接班时的病人数据
	 * @date 2025��4��7�� ����3:32:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询交接班时的病人数据", notes = "查询交接班时的病人数据")
	@GetMapping("/api/patientInfo/getPatientInfoDetails")
	public PlatformResult<List<PatientInfo>> getPatientInfoDetails(PatientInfo record) {
		try {
			List<PatientInfo> records = patientInfoService.getPatientInfoDetails(record);
			return PlatformResult.success(records);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.deptHonor.dao.MedDeptHonorMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.deptHonor.model.MedDeptHonor">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="honor_name" jdbcType="VARCHAR" property="honorName" />
    <result column="issue_org" jdbcType="VARCHAR" property="issueOrg" />
    <result column="issue_date" jdbcType="VARCHAR" property="issueDate" />
    <result column="certificate_no" jdbcType="VARCHAR" property="certificateNo" />
    <result column="is_pub" jdbcType="VARCHAR" property="isPub" />
    <result column="honor_remark" jdbcType="VARCHAR" property="honorRemark" />
    <result column="award_reason" jdbcType="VARCHAR" property="awardReason" />
    <result column="related_item" jdbcType="VARCHAR" property="relatedItem" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="files" jdbcType="VARCHAR" property="files" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
  
  <select id="getDataSetList" resultType="cn.trasen.hrms.med.deptHonor.model.MedDeptHonor" parameterType="cn.trasen.hrms.med.deptHonor.model.MedDeptHonor">
  		select t1.*,t2.`name` as orgName from med_dept_honor t1
		LEFT JOIN comm_organization t2 on t1.org_id = t2.organization_id
		where t1.is_deleted = 'N' and t1.sso_org_code = #{ssoOrgCode}
  		<if test="honorName != null and honorName != ''">
  			 and t1.honor_name like concat('%',#{honorName},'%')
  		</if>
  		<if test="certificateNo != null and certificateNo != ''">
  			 and t1.certificate_no like concat('%',#{certificateNo},'%')
  		</if>
  		<if test="issueDateStart != null and issueDateStart != '' and issueDateEnd != null and issueDateEnd != ''">
  			 and t1.issue_date BETWEEN #{issueDateStart} and #{issueDateEnd}
  		</if>
  		<if test="childsList != null and childsList.size() > 0">
        	 and t1.org_id in
	        <foreach collection="childsList" index="index" item="item" open="(" separator="," close=")">
	            #{item,jdbcType=VARCHAR}
	        </foreach>
        </if>
  </select>
  
</mapper>
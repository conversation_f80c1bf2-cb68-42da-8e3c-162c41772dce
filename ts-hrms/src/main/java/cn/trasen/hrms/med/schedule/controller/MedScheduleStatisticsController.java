package cn.trasen.hrms.med.schedule.controller;

import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.schedule.model.LeaveRecordVo;
import cn.trasen.hrms.med.schedule.model.MedCustomStatisticsExport;
import cn.trasen.hrms.med.schedule.model.MedCustomStatisticsTitle;
import cn.trasen.hrms.med.schedule.model.MedScheduleRecord;
import cn.trasen.hrms.med.schedule.model.MedScheduleStatisticsDetailVo;
import cn.trasen.hrms.med.schedule.model.MedScheduleStatisticsReq;
import cn.trasen.hrms.med.schedule.model.MedScheduleStatisticsTitleVo;
import cn.trasen.hrms.med.schedule.service.MedScheduleStatisticsService;
import cn.trasen.hrms.utils.ExcelExportUtils;
import cn.trasen.hrms.utils.ExcelStyleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName MedScheduleStatisticsController
 * @Description 排班统计
 * @date 2025-08-13 14:30:00
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@Api(tags = "排班统计")
public class MedScheduleStatisticsController {
	
	@Autowired
	private MedScheduleStatisticsService medScheduleStatisticsService;

	/**
	 * @Title statisticsTableDataList
	 * @Description 排班统计列表-当前接口返回结果有两个双引号，故此返回String，前端再转json
	 * @param record
	 * @return DataSet<JSONObject>
	 * @date 2025-08-13 14:30:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "排班统计列表", notes = "排班统计列表")
	@GetMapping("/api/scheduleStatistics/tableDataList")
	public DataSet<Map<String, Object>> statisticsTableDataList(Page page, MedScheduleStatisticsReq record) {
		try {
			String type = record.getType();
			if(ObjectUtils.isEmpty(type)){
				record.setType("1");//个人
			}
			List<Map<String, Object>> map = medScheduleStatisticsService.statisticsTableDataList(page, record);
			return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), map);
		} catch (Exception e) {
			log.error("获取数据失败:{}", e.getMessage());
			return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), null);
		}
	}

	/**
	 * @Title statisticsTotalTableDataList
	 * @Description 排班按科室统计合计
	 * @param record
	 * @return PlatformResult<Map<String, Object>>
	 * @date 2025-08-23 14:30:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "排班按科室统计合计", notes = "排班按科室统计合计")
	@GetMapping("/api/scheduleStatistics/totalDeptTableData")
	public PlatformResult<Map<String, Object>> statisticsTotalTableDataList(MedScheduleStatisticsReq record) {
		try {
			Map<String, Object> map = medScheduleStatisticsService.statisticsTotalTableDataList(record);
	        return PlatformResult.success(map);
		} catch (Exception e) {
			log.error("获取数据失败:{}", e.getMessage());
			return PlatformResult.failure();
		}
	}

	/**
	 * @Title statisticsTableTitleList
	 * @Description 排班统计表头
	 * @param record
	 * @return PlatformResult<JSONObject>
	 * @date 2025-08-13 14:30:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "排班统计表头", notes = "排班统计表头")
	@GetMapping("/api/scheduleStatistics/tableTitleList")
	public PlatformResult<List<MedScheduleStatisticsTitleVo>> statisticsTableTitleList(MedScheduleStatisticsReq record) {
		try {
			String type = record.getType();
			if(ObjectUtils.isEmpty(type)){
				record.setType("1");//个人
			}
			return PlatformResult.success(medScheduleStatisticsService.getStatisticsTitleList(record));
		} catch (Exception e) {
			log.error("获取表头失败:{}", e.getMessage());
			return PlatformResult.failure("获取表头失败！");
		}
	}

	/**
	 * @Title saveCustomStatisticsTitle
	 * @Description 保存自定义班次
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025-08-13 14:30:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "保存自定义班次", notes = "保存自定义班次")
	@PostMapping("/api/scheduleStatistics/saveCustomStatisticsTitle")
	public PlatformResult<String> saveCustomStatisticsTitle(@RequestBody List<MedCustomStatisticsTitle> records) {
		try {
			medScheduleStatisticsService.saveCustomStatisticsTitle(records);
			return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure("保存失败");
		}
	}

	/**
	 * @Title selectCustomStatisticsTitle
	 * @Description 查询自定义班次
	 * @return PlatformResult<List<MedCustomStatisticsTitle>>
	 * @date 2025-08-13 14:30:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询自定义班次", notes = "查询自定义班次")
	@GetMapping("/api/scheduleStatistics/selectCustomStatisticsTitle")
	public PlatformResult<List<MedCustomStatisticsTitle>> selectCustomStatisticsTitle() {
		try {
			return PlatformResult.success(medScheduleStatisticsService.selectCustomStatisticsTitle());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure("查询失败");
		}
	}

	/**
	 * @Title selectDetail
	 * @Description 查询统计明细
	 * @return PlatformResult<MedScheduleStatisticsDetailVo>
	 * @date 2025-08-18 14:30:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询统计明细", notes = "查询统计明细")
	@GetMapping("/api/scheduleStatistics/selectDetail")
	public PlatformResult<MedScheduleStatisticsDetailVo> selectDetail(MedScheduleStatisticsReq record) {
		try {
			return PlatformResult.success(medScheduleStatisticsService.selectDetail(record));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure("查询失败");
		}
	}
	
	/**
	 * 导出科室的明细数据
	 * @param request
	 * @param response
	 * @param record
	 * @date 2025-08-23 8:30:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "导出科室的明细数据", notes = "导出科室的明细数据")
	@GetMapping("/api/scheduleStatistics/exportDeptDetail")
    public void export(HttpServletRequest request, HttpServletResponse response, MedScheduleStatisticsReq record) {
		String deptId = record.getDeptId();
		if(ObjectUtils.isEmpty(deptId)){
			throw new BusinessException("科室ID不能为空！");
		}
		String deptExportType = record.getDeptExportType();
		if(ObjectUtils.isEmpty(deptExportType)){
			throw new BusinessException("科室导出类型不能为空！");
		}
		
		String name = "班次信息数据.xls";
		String templateUrl = "template/scheduleStatistics.xls";
		try {
			MedScheduleStatisticsDetailVo vo = medScheduleStatisticsService.selectDetail(record);
			if(deptExportType.equals("1")){//班次数据
				name = vo.getOrgName() + "班次信息数据.xls";
				List<MedScheduleRecord> scheduleRecords = vo.getScheduleRecords();
				if(CollUtil.isNotEmpty(scheduleRecords)){
					Integer index = 1;
					for(MedScheduleRecord re : scheduleRecords){
						re.setIndex(index.toString());
						index++;
						
						//天数
						re.setClassesDaysText(re.getClassesDaysText() + " 天");
						
//						String scheduleDate = re.getScheduleDate();
//						//处理时间显示  08:00-12:00,14:30-17:30
//						String classesWorktime = re.getClassesWorktime();
//						String classesWorktimeText = "";
//						if(!ObjectUtils.isEmpty(classesWorktime)){
//							String[] classesWorktimeArr = classesWorktime.split(",");
//							for(String cw : classesWorktimeArr){
//								String[] cwArr = cw.split("-");
//								classesWorktimeText += scheduleDate + " " + cwArr[0] + " 至 " + scheduleDate + " " + cwArr[1] + "\n";
//							}
//						}
//						re.setClassesWorktime(classesWorktimeText);
					}
					ExportUtil.export(request, response, scheduleRecords, name, templateUrl);
				} else {
					ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
				}
			} else {//OA数据
				name = vo.getOrgName() + "OA信息数据.xls";
				templateUrl = "template/scheduleOaStatistics.xls";
				List<LeaveRecordVo> oaRecords = vo.getOaRecords();
				if(CollUtil.isNotEmpty(oaRecords)){
					Integer index = 1;
					for(LeaveRecordVo re : oaRecords){
						re.setIndex(index.toString());
						index++;
						//天数
						re.setDaysText(re.getDaysText() + " 天");
					}
					ExportUtil.export(request, response, oaRecords, name, templateUrl);
				} else {
					ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * @Title saveMedCustomStatisticsExport
	 * @Description 保存出勤明细自定义导出配置
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025-08-20 14:30:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "保存出勤明细自定义导出配置", notes = "保存出勤明细自定义导出配置")
	@PostMapping("/api/scheduleStatistics/saveMedCustomStatisticsExport")
	public PlatformResult<String> saveMedCustomStatisticsExport(@RequestBody List<MedCustomStatisticsExport> records) {
		try {
			medScheduleStatisticsService.saveMedCustomStatisticsExport(records);
			return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure("保存失败");
		}
	}

	/**
	 * @Title selectMedCustomStatisticsExport
	 * @Description 查询出勤明细自定义导出配置
	 * @param type
	 * @return PlatformResult<List<MedCustomStatisticsExport>>
	 * @date 2025-08-20 14:30:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询出勤明细自定义导出配置", notes = "查询出勤明细自定义导出配置")
	@GetMapping("/api/scheduleStatistics/selectMedCustomStatisticsExport")
	public PlatformResult<List<MedCustomStatisticsExport>> selectMedCustomStatisticsExport(@RequestParam(value = "type", required = false )@ApiParam(value = "类型：1-个人，2-科室")String type) {
		try {
			return PlatformResult.success(medScheduleStatisticsService.selectMedCustomStatisticsExport(type));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure("保存失败");
		}
	}

	/**
	 * @Title selectAaTypeList
	 * @Description 获取OA信息类型下拉数据
	 * @param type
	 * @return PlatformResult<List<MedCustomStatisticsExport>>
	 * @date 2025-08-20 14:30:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "获取OA信息类型下拉数据", notes = "获取OA信息类型下拉数据")
	@GetMapping("/api/scheduleStatistics/oaTypeList")
	public PlatformResult<List<MedScheduleStatisticsTitleVo>> selectAaTypeList() {
		try {
			return PlatformResult.success(medScheduleStatisticsService.selectAaTypeList());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure("保存失败");
		}
	}
	
	/**
	 * @Title exportScheduleRecord
	 * @Description 排班统计明细导出
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025-08-20 14:30:00
	 * <AUTHOR>
	 */
	@ApiOperation(value = "排班统计明细导出", notes = "排班统计明细导出")
	@GetMapping(value = "/api/scheduleStatistics/export")
	public void exportScheduleRecord(MedScheduleStatisticsReq record,HttpServletResponse response, Page page) {
		try {
			String sheetName = "排班个人出勤明细情况";
			String type = record.getType();
			if(ObjectUtils.isEmpty(type)){
				record.setType("1");//个人
			}
			// 查询需要导出的字段
			LinkedList<String> headList = new LinkedList<>();
			Map<String, String> statisticsExportMap = new HashMap<>();
			if(type.equals("3")){
				sheetName = "排班分类统计情况";
				List<MedScheduleStatisticsTitleVo> statisticsTitleList = MedScheduleStatisticsTitleVo.getCategoryStatisticsTitleList();
				//分类统计
				statisticsTitleList.addAll(medScheduleStatisticsService.getStatisticsTitleList(record));
				statisticsExportMap = statisticsTitleList.stream().collect(Collectors.toMap(MedScheduleStatisticsTitleVo::getProp, MedScheduleStatisticsTitleVo::getLabel));
				for(MedScheduleStatisticsTitleVo title : statisticsTitleList){
					headList.add(title.getLabel());
				}
			} else {
				if(type.equals("2")){
					sheetName = "排班科室出勤明细情况";
				}
				List<MedCustomStatisticsExport> titleList = medScheduleStatisticsService.selectMedCustomStatisticsExport(type);
				statisticsExportMap = titleList.stream().collect(Collectors.toMap(MedCustomStatisticsExport::getProp, MedCustomStatisticsExport::getLabel));
				for(MedCustomStatisticsExport export : titleList){
					headList.add(export.getLabel());
				}
			}
			
			//查询排班统计数据
			List<Map<String, Object>> mapList = medScheduleStatisticsService.statisticsTableDataList(page, record);
			
			LinkedList<Map<String,Object>> rows = new LinkedList<>();
			for(Map<String, Object> empMap : mapList){
				Map<String,Object> row = new HashMap<>();
				for(String key : empMap.keySet()){
					if(statisticsExportMap.containsKey(key)){
						if (empMap.get(key) instanceof BigDecimal){
							//如果是0，则不显示，否则保留一位小数
							if(empMap.get(key) == null){
								row.put(statisticsExportMap.get(key), null);
							} else if(((BigDecimal) empMap.get(key)).compareTo(BigDecimal.ZERO) == 0){
								row.put(statisticsExportMap.get(key), 0);
							} else {
								if(key.equals("attendanceUserCount")){
									row.put(statisticsExportMap.get(key), ((BigDecimal) empMap.get(key)));
								} else {
									row.put(statisticsExportMap.get(key), ((BigDecimal) empMap.get(key)).setScale(1, RoundingMode.HALF_UP));
								}
							}
						} else {
							row.put(statisticsExportMap.get(key), empMap.get(key));
						}
					}
				}
				rows.add(row);
			}
			
			//科室导出添加合计数据
			if(type.equals("2") && rows.size() > 0){
				Map<String, Object> totalMap = medScheduleStatisticsService.statisticsTotalTableDataList(record);
				Map<String,Object> row = new HashMap<>();
				for(String key : totalMap.keySet()){
					if(statisticsExportMap.containsKey(key)){
						if(key.equals("deptName")){
							//便于合并单元格
							row.put("index", totalMap.get(key));
						} else {
							//如果是0，则不显示，否则保留一位小数
							if(totalMap.get(key) == null){
								row.put(statisticsExportMap.get(key), null);
							} else {
								if (totalMap.get(key) instanceof BigDecimal){
									if(((BigDecimal) totalMap.get(key)).compareTo(BigDecimal.ZERO) == 0){
										row.put(statisticsExportMap.get(key), "0");
									} else {
										if(key.equals("attendanceUserCount")){
											row.put(statisticsExportMap.get(key), ((BigDecimal) totalMap.get(key)));
										} else {
											row.put(statisticsExportMap.get(key), ((BigDecimal) totalMap.get(key)).setScale(1, RoundingMode.HALF_UP).toPlainString());
										}
									}
								} else {
									row.put(statisticsExportMap.get(key), (String) totalMap.get(key));
								}
							}
						}
					}
				}
				rows.add(row);
			}
			
			List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();

			ExcelExportUtils.setColList(colList, headList);
			
			String filename = record.getStartDate() + "~" + record.getEndDate() + sheetName;

			ExportParams exportParams = new ExportParams(filename, sheetName, ExcelType.XSSF);
			exportParams.setStyle(ExcelStyleUtil.class);
			
			Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, rows);
			Sheet sheet = workbook.getSheet(sheetName);
			
			CellStyle firstStyle = workbook.createCellStyle();
			firstStyle.setWrapText(true);
			firstStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
			firstStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中（可选）
			// 设置边框样式（细实线）
			firstStyle.setBorderBottom(BorderStyle.THIN);
			firstStyle.setBorderLeft(BorderStyle.THIN);
			firstStyle.setBorderRight(BorderStyle.THIN);
			firstStyle.setBorderTop(BorderStyle.THIN);
			// 设置字体为黑体并加粗
		    Font font = workbook.createFont();
		    font.setFontName("宋体"); 
		    font.setBold(true);         // 加粗
		    font.setFontHeightInPoints((short) 11); // 字体大小
		    firstStyle.setFont(font);
			Row firstRow = sheet.getRow(0);
			for (int j = 0; j < firstRow.getLastCellNum(); j++) {
		        Cell cell = firstRow.getCell(j);
		        if (cell != null) {
		            cell.setCellStyle(firstStyle);
		        }
		    }
			
			workbook.getSheet(sheetName).getRow(1).setHeight((short) (12*50));
			// 新增：设置自适应高度的代码
			CellStyle wrapStyle = workbook.createCellStyle();
			wrapStyle.setWrapText(true);
			wrapStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
			wrapStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中（可选）
			// 设置边框样式（细实线）
			wrapStyle.setBorderBottom(BorderStyle.THIN);
			wrapStyle.setBorderLeft(BorderStyle.THIN);
			wrapStyle.setBorderRight(BorderStyle.THIN);
			wrapStyle.setBorderTop(BorderStyle.THIN);

			//合计行样式
			CellStyle totalStyle = workbook.createCellStyle();
			totalStyle.setWrapText(true);
			totalStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
			totalStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中（可选）
			// 设置边框样式（细实线）
			totalStyle.setBorderBottom(BorderStyle.THIN);
			totalStyle.setBorderLeft(BorderStyle.THIN);
			totalStyle.setBorderRight(BorderStyle.THIN);
			totalStyle.setBorderTop(BorderStyle.THIN);
			//设置合计行字体加粗
			totalStyle.setFont(font);

			int startRowIndex = 2;
			for (int index = startRowIndex; index <= sheet.getLastRowNum(); index++) {
			    Row row = sheet.getRow(index);
			    if (row == null) continue;
			    
			    //合计行
			    if(type.equals("2") && index == sheet.getLastRowNum()){
			    	for (int j = 0; j < row.getLastCellNum(); j++) {
			    		Cell cell = row.getCell(j);
			    		if (cell != null) {
			    			cell.setCellStyle(totalStyle);
			    		}
			    	}
			    } else {
			    	for (int j = 0; j < row.getLastCellNum(); j++) {
			    		Cell cell = row.getCell(j);
			    		if (cell != null) {
			    			cell.setCellStyle(wrapStyle);
			    		}
			    	}
			    }
			    
			    sheet.autoSizeColumn(mapList.size());
			    row.setHeight((short)-1);
			    
			    if (row.getHeight() > 500) {
			        row.setHeight((short)500);
			    }
			}
			
			//合计行字体加粗，合并单元格
			if(type.equals("2") && sheet.getLastRowNum() > 2){
				// 创建合并单元格区域（从第1列到最后一列）
				CellRangeAddress region = new CellRangeAddress(
					sheet.getLastRowNum(), // 起始行
					sheet.getLastRowNum(), // 结束行
				    0,            // 起始列
				    1 // 结束列（基于标题行的列数）
				);
				sheet.addMergedRegion(region);
				//设置合并单元格的值
				Row totalCell = sheet.getRow(sheet.getLastRowNum());
				totalCell.getCell(0).setCellValue("合计");
			}
			
			//最后一行合并单元格
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			String content = "制表人： " + user.getUsername() + "                                             " + "导出日期：" + DateUtil.format(new Date(),"yyyy-MM-dd");
			int lastRowIndex = sheet.getLastRowNum() + 1; // 获取最后一行索引并加1
			Row footerRow = sheet.createRow(lastRowIndex); // 创建新行

			// 创建合并单元格区域（从第1列到最后一列）
			CellRangeAddress region = new CellRangeAddress(
			    lastRowIndex, // 起始行
			    lastRowIndex, // 结束行
			    0,            // 起始列
			    sheet.getRow(0).getLastCellNum() - 1 // 结束列（基于标题行的列数）
			);
			sheet.addMergedRegion(region);

			// 创建单元格并设置值
			Cell footerCell = footerRow.createCell(0);
			footerCell.setCellValue(content);

			// 设置合并单元格样式
			CellStyle footerStyle = workbook.createCellStyle();
			footerStyle.setWrapText(true);
			footerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
			footerStyle.setAlignment(HorizontalAlignment.CENTER);
			footerStyle.setBorderBottom(BorderStyle.THIN);
			footerStyle.setBorderLeft(BorderStyle.THIN);
			footerStyle.setBorderRight(BorderStyle.THIN);
			footerStyle.setBorderTop(BorderStyle.THIN);

			// 设置边框（关键修改：为合并后的整个区域设置边框）
			ExcelExportUtils.setBorderForMergedRegion(sheet, region, footerStyle);
			
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-disposition",
					"attachment; filename=" + new String(filename.getBytes("gbk"), "iso8859-1") + ".xls");

			OutputStream fos = response.getOutputStream();
			workbook.write(fos);
			fos.close();
			
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
		}
	}
}

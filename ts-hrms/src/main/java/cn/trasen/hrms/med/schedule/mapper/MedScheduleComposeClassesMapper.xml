<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.schedule.dao.MedScheduleComposeClassesMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.schedule.model.MedScheduleComposeClasses">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="compose_name" jdbcType="VARCHAR" property="composeName" />
    <result column="compose_content" jdbcType="VARCHAR" property="composeContent" />
    <result column="compose_content_id" jdbcType="VARCHAR" property="composeContentId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
  
  <select id="getPageList" resultType="cn.trasen.hrms.med.schedule.model.MedScheduleComposeClasses" parameterType="cn.trasen.hrms.med.schedule.model.MedScheduleComposeClasses">
  		select * 
  		from med_schedule_compose_classes
  		where is_deleted = 'N'
  		<if test="composeName != null and composeName != ''">
  			and compose_name like CONCAT(CONCAT('%', #{composeName}), '%')
  		</if>
  		<if test="ssoOrgCode != null and ssoOrgCode != ''">
  			and sso_org_code = #{ssoOrgCode}
  		</if>
  		<if test="isSchedule != null and isSchedule != '' and isSchedule == 'Y'.toString">
  			and (create_user=#{currentUserCode} or classes_use_user='-1')
  		</if>
  		<if test="isSchedule == null or isSchedule == '' or isSchedule == 'N'.toString">
  			and create_user=#{currentUserCode}
  		</if>
  		<if test="employeeNo != null and employeeNo != '' and orgCode != null and orgCode != ''">
  			and ((
  				find_in_set(#{employeeNo}, classes_use_user)
  				or
  				find_in_set(#{orgCode}, classes_use_org)
  			) or classes_use_user='-1')
  		</if>
  </select>
</mapper>
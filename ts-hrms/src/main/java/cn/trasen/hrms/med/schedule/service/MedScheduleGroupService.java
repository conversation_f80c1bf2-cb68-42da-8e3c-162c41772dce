package cn.trasen.hrms.med.schedule.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.schedule.model.MedScheduleGroup;

/**
 * @ClassName MedScheduleGroupService
 * @Description TODO
 * @date 2025��4��3�� ����5:49:53
 * <AUTHOR>
 * @version 1.0
 */
public interface MedScheduleGroupService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��4��3�� ����5:49:53
	 * <AUTHOR>
	 */
	Integer save(MedScheduleGroup record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��4��3�� ����5:49:53
	 * <AUTHOR>
	 */
	Integer update(MedScheduleGroup record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��4��3�� ����5:49:53
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedScheduleGroup
	 * @date 2025��4��3�� ����5:49:53
	 * <AUTHOR>
	 */
	MedScheduleGroup selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleGroup>
	 * @date 2025��4��3�� ����5:49:53
	 * <AUTHOR>
	 */
	DataSet<MedScheduleGroup> getDataSetList(Page page, MedScheduleGroup record);

	void updateSeq(List<MedScheduleGroup> records);
}

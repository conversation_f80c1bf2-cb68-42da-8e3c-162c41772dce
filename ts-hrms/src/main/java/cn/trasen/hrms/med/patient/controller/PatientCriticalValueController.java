package cn.trasen.hrms.med.patient.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.patient.model.PatientCriticalValue;
import cn.trasen.hrms.med.patient.service.PatientCriticalValueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName PatientCriticalValueController
 * @Description TODO
 * @date 2025��4��8�� ����4:46:22
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "PatientCriticalValueController")
public class PatientCriticalValueController {

	private transient static final Logger logger = LoggerFactory.getLogger(PatientCriticalValueController.class);

	@Autowired
	private PatientCriticalValueService patientCriticalValueService;

	/**
	 * @Title savePatientCriticalValue
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:46:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/patientCriticalValue/save")
	public PlatformResult<String> savePatientCriticalValue(@RequestBody PatientCriticalValue record) {
		try {
			patientCriticalValueService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updatePatientCriticalValue
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:46:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/patientCriticalValue/update")
	public PlatformResult<String> updatePatientCriticalValue(@RequestBody PatientCriticalValue record) {
		try {
			patientCriticalValueService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectPatientCriticalValueById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<PatientCriticalValue>
	 * @date 2025��4��8�� ����4:46:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/patientCriticalValue/{id}")
	public PlatformResult<PatientCriticalValue> selectPatientCriticalValueById(@PathVariable String id) {
		try {
			PatientCriticalValue record = patientCriticalValueService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deletePatientCriticalValueById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:46:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/patientCriticalValue/delete/{id}")
	public PlatformResult<String> deletePatientCriticalValueById(@PathVariable String id) {
		try {
			patientCriticalValueService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectPatientCriticalValueList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<PatientCriticalValue>
	 * @date 2025��4��8�� ����4:46:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/patientCriticalValue/list")
	public DataSet<PatientCriticalValue> selectPatientCriticalValueList(Page page, PatientCriticalValue record) {
		return patientCriticalValueService.getDataSetList(page, record);
	}
}

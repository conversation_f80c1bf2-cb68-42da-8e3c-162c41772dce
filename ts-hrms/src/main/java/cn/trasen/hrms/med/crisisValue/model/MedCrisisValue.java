package cn.trasen.hrms.med.crisisValue.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "med_crisis_value")
@Setter
@Getter
public class MedCrisisValue {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 就诊id
     */
    @Column(name = "visit_id")
    @ApiModelProperty(value = "就诊id")
    private String visitId;

    /**
     * 申请单id
     */
    @Column(name = "request_id")
    @ApiModelProperty(value = "申请单id")
    private String requestId;

    /**
     * 就诊类型 1-门诊 2-住院
     */
    @Column(name = "visit_type")
    @ApiModelProperty(value = "就诊类型 1-门诊 2-住院")
    private String visitType;

    /**
     * 门诊号/住院
     */
    @Column(name = "visit_no")
    @ApiModelProperty(value = "门诊号/住院")
    private String visitNo;

    /**
     * 患者id
     */
    @Column(name = "patient_id")
    @ApiModelProperty(value = "患者id")
    private String patientId;

    /**
     * 患者姓名
     */
    @ApiModelProperty(value = "患者姓名")
    private String name;

    /**
     * 性别
     */
    @Column(name = "sex_name")
    @ApiModelProperty(value = "性别")
    private String sexName;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private String age;

    /**
     * 床号
     */
    @Column(name = "bed_no")
    @ApiModelProperty(value = "床号")
    private String bedNo;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String tel;

    /**
     * 申请科室
     */
    @Column(name = "request_dept_name")
    @ApiModelProperty(value = "申请科室")
    private String requestDeptName;

    /**
     * 临床诊断
     */
    @Column(name = "diagnosis_name")
    @ApiModelProperty(value = "临床诊断")
    private String diagnosisName;

    /**
     * 检查报告编号
     */
    @Column(name = "report_no")
    @ApiModelProperty(value = "检查报告编号")
    private String reportNo;

    
    @Column(name = "item_name")
    @ApiModelProperty(value = "检查项目")
    private String itemName;
    
    
    @Column(name = "sample_name")
    @ApiModelProperty(value = "标本名称")
    private String sampleName;
    
    
    /**
     * 检查项目
     */
    @Column(name = "order_item_name")
    @ApiModelProperty(value = "检查项目（不要用这个）")
    private String orderItemName;

    /**
     * 危急值内容
     */
    @Column(name = "panic_value")
    @ApiModelProperty(value = "危急值内容")
    private String panicValue;

    /**
     * 上报日期
     */
    @Column(name = "report_date")
    @ApiModelProperty(value = "上报日期")
    private String reportDate;

    /**
     * 上报人
     */
    @Column(name = "sign_user_name")
    @ApiModelProperty(value = "上报人")
    private String signUserName;

    /**
     * 上报科室
     */
    @Column(name = "sign_dept_name")
    @ApiModelProperty(value = "上报科室")
    private String signDeptName;

    /**
     * 管床医生ID
     */
    @Column(name = "bed_doctor_id")
    @ApiModelProperty(value = "管床医生ID")
    private String bedDoctorId;
    
    @Column(name = "bed_doctor_code")
    @ApiModelProperty(value = "管床医生Code")
    private String bedDoctorCode;

    /**
     * 管床医生姓名
     */
    @Column(name = "bed_doctor_name")
    @ApiModelProperty(value = "管床医生姓名")
    private String bedDoctorName;

    /**
     * 主治医生ID
     */
    @Column(name = "main_doctor_id")
    @ApiModelProperty(value = "主治医生ID")
    private String mainDoctorId;

    /**
     * 主治医生姓名
     */
    @Column(name = "main_doctor_name")
    @ApiModelProperty(value = "主治医生姓名")
    private String mainDoctorName;

    /**
     * 主任医生ID
     */
    @Column(name = "chief_doctor_id")
    @ApiModelProperty(value = "主任医生ID")
    private String chiefDoctorId;

    /**
     * 主任医生姓名
     */
    @Column(name = "chief_doctor_name")
    @ApiModelProperty(value = "主任医生姓名")
    private String chiefDoctorName;

    /**
     * 接受状态0:未接收 1:已接收
     */
    @Column(name = "receive_status")
    @ApiModelProperty(value = "接受状态0:未接收 1:已接收")
    private String receiveStatus;

    /**
     * 1:已通知(仅通知类消息) 0:未处理 1:已处理
     */
    @ApiModelProperty(value = "-1:已通知(仅通知类消息) 0:未处理 1:已处理")
    private String status;

    /**
     * 处理时间
     */
    @Column(name = "process_date")
    @ApiModelProperty(value = "处理时间")
    private String processDate;

    /**
     * 处理措施
     */
    @Column(name = "process_desc")
    @ApiModelProperty(value = "处理措施")
    private String processDesc;

    /**
     * 处理人ID
     */
    @Column(name = "process_user_id")
    @ApiModelProperty(value = "处理人ID")
    private String processUserId;

    /**
     * 处理人姓名
     */
    @Column(name = "process_user_name")
    @ApiModelProperty(value = "处理人姓名")
    private String processUserName;
    
    @Column(name = "system_code")
    @ApiModelProperty(value = "系统编码")
    private String systemCode;
    

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    @Transient
    @ApiModelProperty(value = "//1 危急值处理-全部  2危急值处理-未处理  3危急值处理-已处理  5 工作台查询")
    private String index;  //1 危急值处理-全部  2危急值处理-未处理  3危急值处理-已处理
     
    @Transient
    private String currentUserCode;
    
    @Transient
    private String orgId;
    
    @Transient
    @ApiModelProperty(value = "查询开始时间")
    private String startDate;
    
    @Transient
    @ApiModelProperty(value = "查询结束时间")
    private String endDate;
    
    @Transient
    @ApiModelProperty(value = "组合查询")
    private String searchKey;
    
    @Transient
    private int orderNumber;
    
}
package cn.trasen.hrms.common;

/**    
  * <P> @Description: 生命周期排序工具list</p>
  * <P> @Date: 2021年4月20日  下午2:53:32 </p>
  * <P> @Author: wang<PERSON>hua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
    
public class LifeCycleList {
	
	private Object data;
	private String sort;
	private String type;
	private String title;
	
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
	public String getSort() {
		return sort;
	}
	public void setSort(String sort) {
		this.sort = sort;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public LifeCycleList(Object data, String sort, String type,String title) {
		super();
		this.data = data;
		this.sort = sort;
		this.type = type;
		this.title = title;
	}
	public LifeCycleList() {
		super();
		// TODO Auto-generated constructor stub
	}
	
}

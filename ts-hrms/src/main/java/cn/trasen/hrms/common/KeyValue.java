package cn.trasen.hrms.common;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**   
 * @Title: KeyValue.java 
 * @Package cn.trasen.hrms.common 
 * @Description: 针对KeyValue形式数据结构实体类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月27日 上午11:20:55 
 * @version V1.0   
 */
@Getter
@Setter
public class KeyValue implements Serializable {

	private static final long serialVersionUID = -494049716797405599L;

	/**
	 * 编码
	 */
	private String code;
	/**
	 * 文本值
	 */
	private String text;

}

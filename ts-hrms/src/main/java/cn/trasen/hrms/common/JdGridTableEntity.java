package cn.trasen.hrms.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * @Title: JdGridTableEntity.java 
 * @Package cn.trasen.hrms.common 
 * @Description: 针对JdGrrd表格封装实体
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月7日 下午4:43:13 
 * @version V1.0   
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JdGridTableEntity {

	/**
	 * 显示文本
	 */
	@ApiModelProperty(value = "显示文本")
	private String label;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private String index;
	/**
	 * 宽度
	 */
	@ApiModelProperty(value = "宽度")
	private int width;
	/**
	 * 是否编辑
	 */
	@ApiModelProperty(value = "是否编辑")
	private boolean editable;
	/**
	 * 是否隐藏
	 */
	@ApiModelProperty(value = "是否隐藏")
	private boolean hidden;
	/**
	 * 是否排序
	 */
	@ApiModelProperty(value = "是否排序")
	private boolean sortable;
	/**
	 * 对齐方式
	 */
	@ApiModelProperty(value = "对齐方式")
	private String align;
	
	/**
	 * 数据格式
	 */
//	@ApiModelProperty(value = "数据格式")
//	private String formatter;
	
	/**
	 * 是否统计
	 */
//	@ApiModelProperty(value = "是否统计")
//	private String summaryType;
	
	//是否冻结列
	@ApiModelProperty(value = "是否统计")
	private boolean frozen;

	/**
	 * 
	 * @Title:  JdGridTableEntity   
	 * @Description:    不带固定列的构造函数
	 * @param:  @param label
	 * @param:  @param name
	 * @param:  @param index
	 * @param:  @param width
	 * @param:  @param editable
	 * @param:  @param hidden
	 * @param:  @param sortable
	 * @param:  @param align  
	 * @throws
	 */
	public JdGridTableEntity(String label, String name, String index, int width, boolean editable, boolean hidden,
			boolean sortable, String align) {
		super();
		this.label = label;
		this.name = name;
		this.index = index;
		this.width = width;
		this.editable = editable;
		this.hidden = hidden;
		this.sortable = sortable;
		this.align = align;
	}
}

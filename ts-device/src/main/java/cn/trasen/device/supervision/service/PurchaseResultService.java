package cn.trasen.device.supervision.service;

import cn.trasen.device.supervision.bean.*;
import cn.trasen.device.supervision.bean.comm.ImportResp;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.PurchaseResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseResultService
 * @Description TODO
 * @date 2024��4��2�� ����7:27:49
 */
public interface PurchaseResultService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024��4��2�� ����7:27:49
     * <AUTHOR>
     */
    Integer save(PurchaseResult record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024��4��2�� ����7:27:49
     * <AUTHOR>
     */
    Integer update(PurchaseResult record);

    void batchUpdate(PurchaseResultReq record);

    ImportResp importUpdate(List<PurchaseResultImport> record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024��4��2�� ����7:27:49
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return PurchaseResult
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024��4��2�� ����7:27:49
     * <AUTHOR>
     */
    PurchaseResult selectById(String id);

    PurchaseResultResp selectByIdV3(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<PurchaseResult>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024��4��2�� ����7:27:49
     * <AUTHOR>
     */
    DataSet<PurchaseResult> getDataSetList(Page page, PurchaseResult record);


    DataSet<Map> getDataSetListV25(Page page, PurchaseResult record);


    DataSet<PurchaseResult> getDataSetListV3(Page page, PurchaseResult record);

    List<PurchaseResult> getDataSetListV3NoPage(PurchaseResult record);

    /**
     * @MethodName: syncPurchaseResultData
     * @Description: TODO
     * <AUTHOR> void
     * @date 2024-04-03 02:18:39
     */
    void syncPurchaseResultData();


    /**
     * @param :
     * @return viod
     * <AUTHOR>
     * @description v2版本数据迁移到v3版本
     * @date 2024/4/10 11:32
     */
    void dataV22V3();

    void fixDeviceSysCode();

    void cancel(String id, String purchaseRemark);

    void check(String id, String status, String remark);

    void batchBindAttachment(PurchaseResultBatchBindAttachmentReq req);

    void batchCheck(PurchaseResultBatchCheckReq req);
}

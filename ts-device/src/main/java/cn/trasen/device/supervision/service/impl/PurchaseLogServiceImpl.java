package cn.trasen.device.supervision.service.impl;

import java.util.Date;
import java.util.List;

import cn.trasen.device.supervision.model.PurchaseGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.device.supervision.dao.PurchaseLogMapper;
import cn.trasen.device.supervision.model.PurchaseLog;
import cn.trasen.device.supervision.service.PurchaseLogService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseLogServiceImpl
 * @Description TODO
 * @date 2024年4月9日 上午10:58:37
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PurchaseLogServiceImpl implements PurchaseLogService {

    @Autowired
    private PurchaseLogMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(PurchaseLog record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(PurchaseLog record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        PurchaseLog record = new PurchaseLog();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public void deleteByPurchaseGroupId(String purchaseGroupId) {
        Example example = new Example(PurchaseLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("purchaseGroupId", purchaseGroupId);
        mapper.deleteByExample(example);
    }

    @Override
    public PurchaseLog selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<PurchaseLog> getDataSetList(Page page, PurchaseLog record) {
        Example example = new Example(PurchaseLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<PurchaseLog> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    // 根据分组ID查询采购记录
    @Override
    public List<PurchaseLog> getListByPurchaseGroupId(String purchaseGroupId) {
        return mapper.getListByPurchaseGroupId(purchaseGroupId);
    }

    @Override
    public List<PurchaseLog> getListByPurchaseResultId(String purchaseResultId) {
        return mapper.getListByPurchaseResultId(purchaseResultId);
    }

    @Override
    public List<PurchaseLog> getListByPurchaseResultIdList(List purchaseResultIdList) {
        return mapper.getListByPurchaseResultIdList(purchaseResultIdList);
    }


}

package cn.trasen.device.supervision.service.impl;

import cn.trasen.device.supervision.service.BusinessLockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collections;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.service.impl
 * @className: BusinessLockServiceImpl
 * @author: chenbin
 * @description: TODO
 * @date: 2024/2/16 10:39
 * @version: 1.0
 */

@Service
public class BusinessLockServiceImpl implements BusinessLockService {

    @Autowired
    private RedisTemplate redisTemplate;


    private String generateLockKey(String bussinessType, String businessUniqueID) {
        return "locker:" + bussinessType + ":" + businessUniqueID;
    }

    @Override
    public boolean acquireLock(String bussinessType, String businessUniqueID, Duration expireTime) {
        String lockKey = generateLockKey(bussinessType, businessUniqueID);
        // 使用SETNX命令尝试获取锁
        Boolean success = redisTemplate.opsForValue().setIfAbsent(lockKey, "1");

        // 设置锁的过期时间，防止死锁
        if (success != null && success) {
            redisTemplate.expire(lockKey, expireTime);
            return true;
        }

        return false;
    }

    @Override
    public boolean releaseLock(String bussinessType, String businessUniqueID) {
        String lockKey = generateLockKey(bussinessType, businessUniqueID);
        // Lua脚本确保原子性，防止误删其他线程的锁
        String luaScript = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        RedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);

        // 执行Lua脚本
        Long result = (Long) redisTemplate.execute(redisScript, Collections.singletonList(lockKey), "1");

        // 返回释放锁是否成功
        return result != null && result == 1;
    }
}

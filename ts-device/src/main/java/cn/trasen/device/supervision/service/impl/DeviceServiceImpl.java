package cn.trasen.device.supervision.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.device.supervision.bean.*;
import cn.trasen.device.supervision.bean.index.IndexBlockYYZBReq;
import cn.trasen.device.supervision.dao.ProcureApplyMapper;
import cn.trasen.device.supervision.model.*;

import cn.trasen.device.supervision.model.organization.Organization;
import cn.trasen.device.supervision.service.*;
import cn.trasen.device.supervision.util.Comm;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.device.supervision.dao.DeviceMapper;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DeviceServiceImpl
 * @Description TODO
 * @date 2024年3月21日 下午2:42:16
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class DeviceServiceImpl implements DeviceService {

    @Autowired
    private DeviceMapper mapper;


    @Autowired
    private DeviceMaintenanceLogService deviceMaintenanceLogService;

    @Autowired
    private DeviceOperateLogService deviceOperateLogService;

    @Autowired
    private DeviceRepairLogService deviceRepairLogService;

    @Autowired
    private PurchaseLogService purchaseLogService;

    @Autowired
    private PurchaseGroupService purchaseGroupService;

    @Autowired
    private PurchaseResultService purchaseResultService;

    @Autowired
    private DeviceHelperService deviceHelperService;

    @Autowired
    private FeeCodeService feeCodeService;


    @Autowired
    private ProcureApplyMapper procureApplyMapper;

    @Autowired
    private StepperService stepperService;

    @Autowired
    private CategoryService categoryService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(Device record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());

        if (record.getSysCode() == null) {
            record.setSysCode(genSysCode(record.getDeptId()));
        }

        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {

            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Device record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Device record = new Device();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public void deleteByPurchaseLogId(String purchaseLogId) {
        Example example = new Example(Device.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("purchaseLogId", purchaseLogId);
        mapper.deleteByExample(example);
    }

    @Override
    public Device selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }


    @Override
    public DataSet<DeviceManageListResp> getDataSetList(Page page, Device record) {
        String deptCode = deviceHelperService.getDeptCode();
        if (!deptCode.equals("admin")) {
            // 如果不是管理员，deptCode只允许为自身
            record.setDeptId(deptCode);
        }
        List<DeviceManageListResp> records = mapper.getDataSetList(page, record);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<Device> getListByPurchaseLogId(String purchaseLogId) {
        return mapper.getListByPurchaseLogId(purchaseLogId);
    }

    @Override
    public DataSet<DeviceParamResp> getDeviceParamList(Page page, Device record) {
        List<DeviceParamResp> records = mapper.getDeviceParamList(page, record);
        // 遍历 records 查询采购信息
        for (DeviceParamResp deviceParamResp : records) {
            List<Map<String, String>> files = procureApplyMapper.selectFilesByBusinessId(deviceParamResp.getTenderParameter());
            if (CollectionUtils.isNotEmpty(files)) {
                deviceParamResp.setTenderParameter(JSON.toJSONString(files));
            } else {
                deviceParamResp.setTenderParameter("");
            }

        }

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }


    @Transactional(readOnly = false)
    @Override
    public void submit(DeviceManageSaveReq record) {

        Device deviceForm = record.getDevice();

        if (deviceForm == null) {
            throw new BusinessException("数据格式提交错误");
        }

        PurchaseLog purchaseLogForm = record.getPurchaseLog();

        PurchaseGroup purchaseGroupForm = record.getPurchaseGroup();


        if (deviceForm.getId() == null) {
            // 新增设备的时候需要把 purchase_log purchase_group purchase_result 一并保存
            // save
            String deptCode = deviceHelperService.justDeptCode();
            Organization organization = deviceHelperService.getOrganizationByOrgId(deptCode);


            // 新增采购结果信息
            PurchaseResult purchaseResult = new PurchaseResult();
            purchaseResult.setDeptId(deptCode);
            purchaseResult.setApplyOrg(organization.getName());
            purchaseResult.setDeviceName(deviceForm.getName());
            purchaseResult.setApplyDate(Comm.getCurDate("yyyy-MM-dd"));
            purchaseResult.setType(deviceForm.getType().equals("0") ? "医疗专用设备采购申请表" : "一般设备采购申请表");
            purchaseResult.setSpec(deviceForm.getSpec());
            purchaseResult.setApplyNumbers("1");
            if (purchaseLogForm != null) {
                purchaseResult.setApplyPrice(purchaseLogForm.getPurchasePrice());
                purchaseResult.setApplyTotalPrice(purchaseLogForm.getPurchaseTotalPrice());
            }
            purchaseResult.setStatus("1");
            purchaseResult.setApprovalDate(Comm.getCurDate("yyyy-MM-dd"));
            purchaseResultService.save(purchaseResult);

            // 新增块信息
            String purchaseGroupId = "";
            if (purchaseGroupForm != null) {
                purchaseGroupForm.setPurchaseResultId(purchaseResult.getId());
                purchaseGroupForm.setDeptId(deptCode);
                purchaseGroupService.save(purchaseGroupForm);
                purchaseGroupId = purchaseGroupForm.getId();
            } else {
                PurchaseGroup purchaseGroup = new PurchaseGroup();
                purchaseGroup.setPurchaseResultId(purchaseResult.getId());
                purchaseGroup.setDeptId(deptCode);
                purchaseGroupService.save(purchaseGroup);
                purchaseGroupId = purchaseGroup.getId();
            }


            // 新增采购记录
            String purchaseLogId = "";
            // 容错采购日志不提交的问题
            if (purchaseLogForm != null) {
                purchaseLogForm.setPurchaseGroupId(purchaseGroupId);
                purchaseLogForm.setDeptId(deptCode);
                purchaseLogService.save(purchaseLogForm);
                purchaseLogId = purchaseLogForm.getId();
            } else {
                // 新增采购记录
                PurchaseLog purchaseLog = new PurchaseLog();
                purchaseLog.setPurchaseGroupId(purchaseGroupId);
                purchaseLog.setDeptId(deptCode);
                purchaseLogService.save(purchaseLog);
                purchaseLogId = purchaseLog.getId();
            }

            // 新增设备
            deviceForm.setPurchaseLogId(purchaseLogId);
            deviceForm.setDeptId(deptCode);
            deviceForm.setIsComplete("1");
            deviceForm.setStatus("0");

            save(deviceForm);

        } else {
            // update
            update(deviceForm);
            if (purchaseLogForm != null && purchaseLogForm.getId() != null) {
                purchaseLogService.update(purchaseLogForm);
            }
        }

        /*-----------------------------------------维修日志---------------------------------------*/
        // 这里包含了save update delete
        // 查询出所有与本设备相关存在的维保日志 id 与 当前 id 集合 交集做更新处理，新提交的差新增，过去存在的差删除
        List<DeviceRepairLog> deviceRepairLogListFrom = record.getDeviceRepairLogList();
        List<DeviceRepairLog> deviceRepairLogListExist = deviceRepairLogService.getListByDeviceId(deviceForm.getId());

        // get id from deviceRepairLogListExist to a hashset
        Set<String> deviceRepairLogIdSetExist = deviceRepairLogListExist.stream().map(DeviceRepairLog::getId).collect(Collectors.toSet());

        // 当前端没有提交维保日志的时候
        if (deviceRepairLogListFrom == null || deviceRepairLogListFrom.size() == 0) {
            // delete all
            // 当存在历史维保日志的时候进行删除
            if (deviceRepairLogIdSetExist != null && deviceRepairLogIdSetExist.size() > 0) {
                for (String id : deviceRepairLogIdSetExist) {
                    deviceRepairLogService.deleteById(id);
                }
            }
        } else {
            // 当存在维保日志提交的时候

            for (DeviceRepairLog deviceRepairLog : deviceRepairLogListFrom) {

                if (StringUtils.isBlank(deviceRepairLog.getId())) {
                    deviceRepairLog.setDeviceId(deviceForm.getId());
                    deviceRepairLogService.save(deviceRepairLog);
                } else {
                    // 移除已经处理的 id
                    deviceRepairLogIdSetExist.remove(deviceRepairLog.getId());
                    deviceRepairLogService.update(deviceRepairLog);
                }
            }

            if (deviceRepairLogIdSetExist != null && deviceRepairLogIdSetExist.size() > 0) {
                // 对生效的维保日志进行删除
                for (String id : deviceRepairLogIdSetExist) {
                    deviceRepairLogService.deleteById(id);
                }
            }
        }



        /*-----------------------------------------维修日志---------------------------------------*/

        /*-----------------------------------------保养日志---------------------------------------*/
        // 查询出所有与本设备相关存在的维保日志 id 与 当前 id 集合 交集做更新处理，新提交的差新增，过去存在的差删除
        List<DeviceMaintenanceLog> deviceMaintenanceLogListForm = record.getDeviceMaintenanceLogList();
        List<DeviceMaintenanceLog> deviceMaintenanceLogListExist = deviceMaintenanceLogService.getListByDeviceId(deviceForm.getId());

        // get id from deviceRepairLogListExist to a hashset
        Set<String> deviceMaintenanceLogIdSetExist = deviceMaintenanceLogListExist.stream().map(DeviceMaintenanceLog::getId).collect(Collectors.toSet());

        // 当前端没有提交维保日志的时候
        if (deviceMaintenanceLogListForm == null || deviceMaintenanceLogListForm.size() == 0) {
            // delete all
            // 当存在历史维保日志的时候进行删除
            if (deviceMaintenanceLogListExist != null && deviceMaintenanceLogListExist.size() > 0) {
                for (String id : deviceMaintenanceLogIdSetExist) {
                    deviceMaintenanceLogService.deleteById(id);
                }
            }
        } else {
            // 当存在维保日志提交的时候

            for (DeviceMaintenanceLog deviceMaintenanceLog : deviceMaintenanceLogListForm) {

                if (StringUtils.isBlank(deviceMaintenanceLog.getId())) {
                    deviceMaintenanceLog.setDeviceId(deviceForm.getId());
                    deviceMaintenanceLogService.save(deviceMaintenanceLog);
                } else {
                    // 移除已经处理的 id
                    deviceMaintenanceLogIdSetExist.remove(deviceMaintenanceLog.getId());
                    deviceMaintenanceLogService.update(deviceMaintenanceLog);
                }
            }

            // 对生效的维保日志进行删除
            if (deviceMaintenanceLogIdSetExist != null && deviceMaintenanceLogIdSetExist.size() > 0) {
                for (String id : deviceMaintenanceLogIdSetExist) {
                    deviceMaintenanceLogService.deleteById(id);
                }
            }
        }


        /*-----------------------------------------保养日志---------------------------------------*/

        /*-----------------------------------------运营日志---------------------------------------*/

        // 查询出所有与本设备相关存在的维保日志 id 与 当前 id 集合 交集做更新处理，新提交的差新增，过去存在的差删除
        List<DeviceOperateLog> deviceOperateLogListForm = record.getDeviceOperateLogList();
        List<DeviceOperateLog> deviceOperateLogListExist = deviceOperateLogService.getListByDeviceId(deviceForm.getId(), "year");

        // get id from deviceRepairLogListExist to a hashset
        Set<String> deviceOperateLogIdSetExist = deviceOperateLogListExist.stream().map(DeviceOperateLog::getId).collect(Collectors.toSet());

        // 当前端没有提交维保日志的时候
        if (deviceOperateLogListForm == null || deviceOperateLogListForm.size() == 0) {
            // delete all
            // 当存在历史维保日志的时候进行删除
            if (deviceOperateLogListExist != null && deviceOperateLogListExist.size() > 0) {
                for (String id : deviceOperateLogIdSetExist) {
                    deviceOperateLogService.deleteById(id);
                }
            }
        } else {
            // 当存在维保日志提交的时候

            for (DeviceOperateLog deviceOperateLog : deviceOperateLogListForm) {
                deviceOperateLog.setGranularity("year");
                if (StringUtils.isBlank(deviceOperateLog.getId())) {
                    deviceOperateLog.setDeviceId(deviceForm.getId());
                    // 判断月份数据是否存在 否则不添加
                    Boolean exist = deviceOperateLogService.checkExist(deviceOperateLog);
                    if (!exist) {
                        deviceOperateLogService.save(deviceOperateLog);
                    }
                } else {
                    // 移除已经处理的 id
                    deviceOperateLogIdSetExist.remove(deviceOperateLog.getId());
                    deviceOperateLogService.update(deviceOperateLog);
                }
            }

            // 对生效的维保日志进行删除
            if (deviceOperateLogIdSetExist != null && deviceOperateLogIdSetExist.size() > 0) {
                for (String id : deviceOperateLogIdSetExist) {
                    deviceOperateLogService.deleteById(id);
                }
            }
        }

        /*-----------------------------------------收费编码---------------------------------------*/
        // 查询出所有与本设备相关存在的收费编码 id 与 当前 id 集合 交集做更新处理，新提交的差新增，过去存在的差删除
        List<FeeCode> feeCodeListForm = record.getFeeCodeList();
        List<FeeCode> feeCodeListExist = feeCodeService.getListByDeviceId(deviceForm.getId());


        Set<String> feeCodeIdSetExist = feeCodeListExist.stream().map(FeeCode::getId).collect(Collectors.toSet());

        // 当前端没有提交维保日志的时候
        if (feeCodeListForm == null || feeCodeListForm.size() == 0) {
            // delete all
            // 当存在历史编码绑定信息的时候进行删除
            if (feeCodeListExist != null && feeCodeListExist.size() > 0) {
                for (String id : feeCodeIdSetExist) {
                    feeCodeService.deleteById(id);
                }
            }
        } else {
            // 当存在维保日志提交的时候

            for (FeeCode feeCode : feeCodeListForm) {

                if (StringUtils.isBlank(feeCode.getId())) {
                    feeCode.setDeviceId(deviceForm.getId());
                    feeCodeService.save(feeCode);
                } else {
                    // 移除已经处理的 id
                    feeCodeIdSetExist.remove(feeCode.getId());
                    feeCodeService.update(feeCode);
                }
            }

            // 对生效的维保日志进行删除
            if (feeCodeIdSetExist != null && feeCodeIdSetExist.size() > 0) {
                for (String id : feeCodeIdSetExist) {
                    feeCodeService.deleteById(id);
                }
            }
        }

        /*-----------------------------------------收费编码---------------------------------------*/

    }

    @Override
    public DeviceManageDetailResp detail(String id) {
        DeviceManageDetailResp resp = new DeviceManageDetailResp();
        Device device = selectById(id);

        if (device.getCateId() != null) {
            Category category = categoryService.selectById(device.getCateId());
            if (category != null) {
                device.setCateName(category.getName());
            }
        }

        if (device == null) {
            throw new BusinessException("设备不存在");
        }
        List<DeviceRepairLog> deviceRepairLogListExist = deviceRepairLogService.getListByDeviceId(device.getId());
        List<DeviceMaintenanceLog> deviceMaintenanceLogListExist = deviceMaintenanceLogService.getListByDeviceId(device.getId());
        List<DeviceOperateLog> deviceOperateLogListExist = deviceOperateLogService.getListByDeviceId(device.getId(), "year");

        resp.setDevice(device);
        resp.setDeviceMaintenanceLogList(deviceMaintenanceLogListExist);
        resp.setDeviceOperateLogList(deviceOperateLogListExist);
        resp.setDeviceRepairLogList(deviceRepairLogListExist);

        // 查询采购信息
        PurchaseLog purchaseLog = purchaseLogService.selectById(device.getPurchaseLogId());
        if (purchaseLog != null) {
            // 查询采购组信息 招标相关文件
            PurchaseGroup purchaseGroup = purchaseGroupService.selectById(purchaseLog.getPurchaseGroupId());
            PurchaseResult purchaseResult = purchaseResultService.selectById(purchaseGroup.getPurchaseResultId());
            resp.setPurchaseLog(purchaseLog);
            resp.setPurchaseGroup(purchaseGroup);
            resp.setPurchaseResult(purchaseResult);
        }
        // 查询收费编码列表
        resp.setFeeCodeList(feeCodeService.getListByDeviceId(device.getId()));

        return resp;
    }

    /**
     * @param deptCode:
     * @return String
     * <AUTHOR>
     * @description 获取系统编码，兼容deptCode 指定和不指定的情况
     * @date 2024/4/19 09:13
     */
    @Override
    public String genSysCode(String deptCode) {
        if (deptCode == null) {
            deptCode = deviceHelperService.justDeptCode();
        }
        // 医院编码+yyyyMMDD+数量，不足6位补0

        Organization organization = deviceHelperService.getOrganizationByOrgId(deptCode);

        String customeCode = "";
        if (organization == null || organization.getCustomCode() == null) {
            customeCode = "99";
        } else {
            customeCode = organization.getCustomCode();
        }

        String date = Comm.getCurDate("yyMMdd");

        String sysCode = customeCode + date;

        String key = "genSysCode" + date;
        long step = stepperService.step(key, 86400);

        sysCode = String.format("%s%04d", sysCode, step);

        return sysCode;
    }

    /**
     * @param :
     * @return List<Device>
     * <AUTHOR>
     * @description 返回所有系统编码为空的设备
     * @date 2024/4/19 10:28
     */
    @Override
    public List<Device> nullSysCodeList() {
        Example example = new Example(Device.class);
        example.createCriteria().andIsNull("sysCode");
        return mapper.selectByExample(example);
    }

    @Override
    public Integer countByCateId(String cateId) {
        Example example = new Example(Device.class);
        example.createCriteria().andEqualTo("cateId", cateId).andEqualTo("isDeleted", "N");
        return mapper.selectCountByExample(example);
    }

    @Override
    public Integer sbzs(IndexBlockYYZBReq req) {
        return mapper.sbzs(req);
    }

    @Override
    public Device getByDeviceCode(String deviceCode) {
        Example example = new Example(Device.class);
        example.createCriteria().andEqualTo("deviceCode", deviceCode);
        // 加一个order by create_date asc
        example.orderBy("createDate").asc();
        List<Device> devices = mapper.selectByExample(example);

        if (devices.size() > 0) {
            return devices.get(0);
        }
        return null;
    }

    /**
     * @param :
     * @return List<CountsGroupByDeptIdResp>
     * <AUTHOR>
     * @description 按照医院ID统计设备数量
     * @date 2024/6/18 20:05
     */
    @Override
    public List<CountsGroupByDeptIdResp> countsGroupByDeptId() {
        return mapper.countsGroupByDeptId();
    }

    @Override
    public List<Device> selectAll() {
        Example example = new Example(Device.class);
        example.createCriteria().andEqualTo("isDeleted", "N");
        List<Device> deviceList = mapper.selectByExample(example);
        return deviceList;
    }

}

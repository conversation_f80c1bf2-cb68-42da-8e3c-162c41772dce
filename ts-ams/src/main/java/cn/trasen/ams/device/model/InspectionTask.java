package cn.trasen.ams.device.model;

import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "d_inspection_task")
@Setter
@Getter
public class InspectionTask {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 流水号
     */
    @Column(name = "serial_no")
    @ApiModelProperty(value = "流水号")
    private String serialNo;

    /**
     * 巡检计划ID
     */
    @Column(name = "inspection_plan_id")
    @ApiModelProperty(value = "巡检计划ID")
    private String inspectionPlanId;


    @Transient
    @ApiModelProperty(value = "巡检计划名称")
    private String inspectionPlanName;

    /**
     * 执行人ID
     */
    @Column(name = "engineer_id_set")
    @ApiModelProperty(value = "执行人ID")
    private String engineerIdSet;

    /**
     * 设备ID
     */
    @Column(name = "device_id")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    /**
     * 巡检得到的结果 1 正常使用 2 可以使用，部分功能失效 3 无法使用，故障维修 4 无法维修，待报废
     */
    @Column(name = "inspection_status")
    @ApiModelProperty(value = "巡检得到的结果 1 正常使用 2 可以使用，部分功能失效 3 无法使用，故障维修 4 无法维修，待报废")
    private String inspectionStatus;

    @Transient
    @ApiModelProperty(value = "巡检得到的结果翻译")
    private String inspectionStatusShow;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String note;

    /**
     * 状态 0
     */
    @ApiModelProperty(value = "状态 0 已废除 1 正常")
    private String status;

    @Transient
    @ApiModelProperty(value = "状态翻译")
    private String statusShow;

    /**
     * 执行状态 0 未完成 1 已完成 2 异常
     */
    @Column(name = "complete_status")
    @ApiModelProperty(value = "执行状态 0 未完成 1 已完成 2 异常")
    private String completeStatus;

    @Transient
    @ApiModelProperty(value = "完成状态翻译")
    private String completeStatusShow;

    /**
     * 任务完成时间
     */
    @Column(name = "complete_at")
    @ApiModelProperty(value = "任务完成时间")
    private Date completeAt;

    /**
     * 处理方案 0 未处理 1 暂不处理 2 转报修 3 转报废
     */
    @ApiModelProperty(value = "处理方案 0 未处理 1 暂不处理 2 转报修 3 转报废")
    private String solution;

    @Transient
    @ApiModelProperty(value = "处理方案翻译")
    private String solutionShow;

    /**
     * 处理意见
     */
    @ApiModelProperty(value = "处理意见")
    private String opinion;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 对应当前模板的json值存储
     */
    @Column(name = "json_value")
    @ApiModelProperty(value = "对应当前模板的json值存储")
    private String jsonValue;

    /**
     * 保养前附件 逗号分隔多个
     */
    @Column(name = "before_file_set")
    @ApiModelProperty(value = "保养前附件 逗号分隔多个")
    private String beforeFileSet;

    /**
     * 保养后附件 逗号分隔多个
     */
    @Column(name = "after_files_set")
    @ApiModelProperty(value = "保养后附件 逗号分隔多个")
    private String afterFilesSet;

    /**
     * 其他附件
     */
    @Column(name = "other_file_set")
    @ApiModelProperty(value = "其他附件")
    private String otherFileSet;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}
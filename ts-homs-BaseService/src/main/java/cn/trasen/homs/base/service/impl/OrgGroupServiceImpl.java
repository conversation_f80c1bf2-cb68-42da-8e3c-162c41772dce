package cn.trasen.homs.base.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.BootComm.excel.ExcelNewUtils;
import cn.trasen.BootComm.utils.CommTree;
import cn.trasen.homs.base.bean.EMPResp;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bean.OrgGroupEmpExportExcel;
import cn.trasen.homs.base.bean.OrgGroupListRes;
import cn.trasen.homs.base.bo.OrgGroupEmpInBO;
import cn.trasen.homs.base.bo.OrgGroupEmpOutBO;
import cn.trasen.homs.base.bo.OrgGroupInBO;
import cn.trasen.homs.base.mapper.OrgGroupClassMapper;
import cn.trasen.homs.base.mapper.OrgGroupMapper;
import cn.trasen.homs.base.mapper.OrgUserGroupMapper;
import cn.trasen.homs.base.mapper.OrganizationLeaderMapper;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.model.OrgGroup;
import cn.trasen.homs.base.model.OrgGroupClass;
import cn.trasen.homs.base.model.OrgUserGroup;
import cn.trasen.homs.base.model.OrganizationLeader;
import cn.trasen.homs.base.service.HrmsEmployeeService;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.service.OrgGroupService;
import cn.trasen.homs.base.utils.StringConvertPinyin;
import cn.trasen.homs.base.utils.Utils;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.DataBaseProvider;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * @Description: 自定义群组实现层
 * @Date: 2020/1/13 18:30
 * @Author: Lizhihuo
 * @Company: 湖南创星
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class OrgGroupServiceImpl implements OrgGroupService {
	
    @Autowired
    private OrgGroupMapper orgGroupMapper;
    
    @Autowired
    private OrgGroupClassMapper orgGroupClassMapper;

    @Autowired
    private OrgUserGroupMapper orgUserGroupMapper;

    @Autowired
    private HrmsEmployeeService hrmsEmployeeService;
    
    @Autowired
    private IDictItemService dictItemService;
    
    @Autowired
    private OrganizationLeaderMapper organizationLeaderMapper;
    
//    @Autowired
//    private DataBaseProvider dataBaseProvider;

    /**
     * @Author: Lizhihuo
     * @Description: 查询自定义群组列表
     * @Date: 2020/1/13 20:22
     * @Param:
     * @return: java.util.List<cn.trasen.hrm.model.OrgGroup>
     **/
    @Override
    public List<OrgGroup> getDataList(Page page, OrgGroup orgGroup) {
        Example example = new Example(OrgGroup.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (orgGroup != null && null != orgGroup.getGroupType()) {
            example.and().andEqualTo("groupType", orgGroup.getGroupType());
        }
        if (orgGroup != null && null != orgGroup.getGroupType() && 1 == orgGroup.getGroupType()) {//自定义群组
            example.and().andEqualTo("createUser", UserInfoHolder.getCurrentUserCode());
        }
        if (StringUtils.isNotBlank(orgGroup.getGroupName())) {//群组名称
            example.and().andLike("groupName", "%" + orgGroup.getGroupName() + "%");
        }
        if (StringUtils.isNotBlank(orgGroup.getGroupUserNames())) {//群组用户
            example.and().andEqualTo("groupUserNames", "%" + orgGroup.getGroupUserNames() + "%");
        }
        /**
         * 增加不同数据库方言特殊处理 
         * add lijr#trasen.cn 2024-04-03 
         */
        if (DataBaseProvider.databaseId.contains("mysql")) {
        	example.setOrderByClause("CONVERT(GROUP_ORDER,SIGNED) asc,CREATE_DATE desc");
        }
        if (DataBaseProvider.databaseId.contains("kingbase")) {
        	example.setOrderByClause("cast(GROUP_ORDER as SIGNED) asc,CREATE_DATE desc");
        }
        return orgGroupMapper.selectByExampleAndRowBounds(example, page);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 新增自定义群组
     * @Date: 2020/1/13 20:22
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public OrgGroup insert(OrgGroup orgGroup) {

        Example example = new Example(OrgGroup.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("groupName", orgGroup.getGroupName());
        criteria.andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        
        if(1 == orgGroup.getGroupType()){
            criteria.andEqualTo("createUser", UserInfoHolder.getCurrentUserCode());
        }

        example.setOrderByClause(" GROUP_ID desc LIMIT 1 ");
        
        OrgGroup orgGroupOld = orgGroupMapper.selectOneByExample(example);
        if (orgGroupOld != null) {
            throw new BusinessException("群组名称不能重复");
        }

        StringConvertPinyin cte = new StringConvertPinyin();
        String groupId = String.valueOf(IdWork.id.nextId());
        orgGroup.setGroupId(groupId);
        orgGroup.setIsEnable("1");
        orgGroup.setIsDeleted(Contants.IS_DELETED_FALSE);
        //orgGroup.setGroupType((short) 1);
        orgGroup.setCreateDate(new Date());
        orgGroup.setUpdateDate(new Date());//移动端  置顶操作
        orgGroup.setCreateUser(UserInfoHolder.getCurrentUserCode());
        orgGroup.setCreateUserName(UserInfoHolder.getCurrentUserName());
        orgGroup.setDomainId(String.valueOf(Utils.STATUS_ENABLED));
        orgGroup.setGroupPinyin(cte.getAllFirstLetter(orgGroup.getGroupName()));
        //部门\部门名称
        orgGroup.setCreateDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        orgGroup.setCreateDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
        
        //将组用户添加到群组关联用户表里面
        if (StringUtils.isNoneBlank(orgGroup.getGroupUserString())) {
            List<String> list = Arrays.asList(orgGroup.getGroupUserString().split(","));
            List<OrgUserGroup> orgUserGroupList = new ArrayList<>();
            for (String s : list) {
                OrgUserGroup orgUserGroup = new OrgUserGroup();
                orgUserGroup.setGroupId(groupId);
                orgUserGroup.setUserId(s);
                orgUserGroup.setIsDeleted(Contants.IS_DELETED_FALSE);
                orgUserGroup.setType(orgGroup.getGroupType());
                orgUserGroup.setCreateDate(new Date());
                orgUserGroup.setCreateUser(UserInfoHolder.getCurrentUserCode());
                orgUserGroup.setCreateUserName(UserInfoHolder.getCurrentUserName());
                orgUserGroup.setCreateDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
                orgUserGroup.setCreateDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
                orgUserGroup.setHospCode(UserInfoHolder.getCurrentUserInfo().getHospCode());
                orgUserGroup.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                orgUserGroupList.add(orgUserGroup);
            }
            orgUserGroupMapper.batchInsert(orgUserGroupList);
        }
        orgGroup.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        orgGroupMapper.insertSelective(orgGroup);
        return orgGroup;
    }

    /**
     * @Author: Lizhihuo
     * @Description: 修改自定义群组
     * @Date: 2020/1/13 20:22
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int update(OrgGroup orgGroup) {


        Example example = new Example(OrgGroup.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("groupName", orgGroup.getGroupName());
        criteria.andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        criteria.andNotEqualTo("groupId", orgGroup.getGroupId());

        example.setOrderByClause(" GROUP_ID desc LIMIT 1 ");
        OrgGroup orgGroupOld = orgGroupMapper.selectOneByExample(example);
        if (orgGroupOld != null) {
            throw new BusinessException("群组名称不能重复");
        }


        orgGroup.setUpdateDate(new Date());
        orgGroup.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        orgGroup.setUpdateUserName(UserInfoHolder.getCurrentUserName());

        //将数据改成未置顶
        if (StringUtils.isNoneBlank(orgGroup.getIsTop())) {
            List<OrgGroup> orgGroupList = orgGroupMapper.getOrgGroupData(orgGroup.getGroupId(), orgGroup.getGroupType());
            if (orgGroupList.size() > 0) {
                for (OrgGroup entity : orgGroupList) {
                    entity.setIsTop("1");//移动端 是否置顶（0：是、1：否）
                    orgGroupMapper.updateByPrimaryKeySelective(entity);
                }
            }
        }

        //将组用户添加到群组关联用户表里面
        if (StringUtils.isNoneBlank(orgGroup.getGroupUserString())) {
            orgUserGroupMapper.deleteByGroupId(orgGroup.getGroupId());
            List<String> list = Arrays.asList(orgGroup.getGroupUserString().split(","));
            List<OrgUserGroup> orgUserGroupList = new ArrayList<>();
            for (String s : list) {
                OrgUserGroup orgUserGroup = new OrgUserGroup();
                orgUserGroup.setGroupId(orgGroup.getGroupId());
                orgUserGroup.setUserId(s);
                orgUserGroup.setIsDeleted(Contants.IS_DELETED_FALSE);
                orgUserGroup.setType(orgGroup.getGroupType());
                orgUserGroup.setCreateDate(new Date());
                orgUserGroup.setCreateUser(UserInfoHolder.getCurrentUserCode());
                orgUserGroup.setCreateUserName(UserInfoHolder.getCurrentUserName());
                orgUserGroup.setCreateDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
                orgUserGroup.setCreateDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
                orgUserGroup.setHospCode(UserInfoHolder.getCurrentUserInfo().getHospCode());
                orgUserGroup.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                orgUserGroupList.add(orgUserGroup);
            }
            orgUserGroupMapper.batchInsert(orgUserGroupList);
        }
        return orgGroupMapper.updateByPrimaryKeySelective(orgGroup);
    }


    /**
     * @Author: Lizhihuo
     * @Description: 删除自定义群组
     * @Date: 2020/1/13 20:22
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int deleted(String id) {
        OrgGroup orgGroup = orgGroupMapper.selectByPrimaryKey(id);

        orgGroup.setIsDeleted(Contants.IS_DELETED_TURE);//是否删除
        return orgGroupMapper.updateByPrimaryKeySelective(orgGroup);
    }


    /**
     * 禁用启用
     *
     * @param id
     * @param enableType 1=是; 2=否;
     * @return int
     * <AUTHOR>
     * @date 2022/2/8 15:21
     */
    @Override
    @Transactional(readOnly = false)
    public int enable(String id, String enableType) {
        OrgGroup orgGroup = new OrgGroup();
        orgGroup.setGroupId(id);
        orgGroup.setUpdateDate(new Date());
        orgGroup.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        orgGroup.setIsEnable(enableType);
        return orgGroupMapper.updateByPrimaryKeySelective(orgGroup);
    }


    /**
     * 查询群组
     *
     * @param page
     * @param orgGroup
     * @return java.util.List<cn.trasen.basicsbottom.bean.OrgGroupListRes>
     * <AUTHOR>
     * @date 2021/12/4 16:09
     */
    @Override
    public List<OrgGroupListRes> getOrgGroupList(Page page, OrgGroup orgGroup) {
        
        orgGroup.setCreateUser(UserInfoHolder.getCurrentUserCode());
        orgGroup.setCreateDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        orgGroup.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
       
        List<OrgGroup> orgGroupList = orgGroupMapper.getOrgGroupList(page, orgGroup);
        List<OrgGroupListRes> orgGroupResps = new ArrayList<>();

        Set<String> empCodeList = new HashSet<>();
        for (OrgGroup group : orgGroupList) {
            if (StringUtils.isBlank(group.getGroupUserString()) == false) {
                for (String empCode : group.getGroupUserString().split(",")) {
                    empCodeList.add(empCode);
                }
            }
            if (StringUtils.isBlank(group.getRangeEmp()) == false) {
                for (String empCode : group.getRangeEmp().split(",")) {
                    empCodeList.add(empCode);
                }
            }
        }
        List<HrmsEmployeeResp> hrmsEmployeeRespList = new ArrayList<>();
        if (CollectionUtils.isEmpty(empCodeList) == false) {
            hrmsEmployeeRespList = hrmsEmployeeService.getEmployeeDetailByCodes(new ArrayList<>(empCodeList));

        }

        if (orgGroupList.size() > 0) {
            for (OrgGroup group : orgGroupList) {
                OrgGroupListRes orgGroupResp = new OrgGroupListRes();
                BeanUtil.copyProperties(group, orgGroupResp);

                if (CollectionUtils.isEmpty(empCodeList) == false) {
                    List<EMPResp> empRespsA = new ArrayList<>();
                    
                    Page page2 = new Page();
                    page2.setPageNo(1);
                    page2.setPageSize(10000);
                    List<Map<String, Object>> userGroupList = orgUserGroupMapper.selectUserGroupList(page2,group.getGroupId(),UserInfoHolder.getCurrentUserCorpCode());
                    for (Map<String, Object> map : userGroupList) {
                        EMPResp empResp = new EMPResp();
                        empResp.setUserAccounts((String) map.get("userAccounts"));
                        empResp.setEmpName((String) map.get("empName"));
                        empResp.setEmpDeptName((String) map.get("empDeptName"));
                        empResp.setId((String) map.get("id"));
                        if(StringUtils.isNotBlank((String) map.get("empSex"))) {
                            empResp.setEmpSex(Short.parseShort((String)map.get("empSex")));
                        }
                        empResp.setEmpCode((String) map.get("userAccounts"));
                        if (StringUtils.isBlank((String)map.get("empStatus")) == false) {
                            empResp.setEmpStatus(Short.parseShort((String)map.get("empStatus")));
                        }
                        empResp.setAvatar((String) map.get("avatar"));
                        empResp.setEmpBusinessPhone((String) map.get("empBusinessPhone"));
                        empResp.setEmpShortPhone((String) map.get("empBusinessPhone"));
                        empResp.setLandlineNumber((String) map.get("landlineNumber"));
                        empResp.setEmpPhoneSecond((String) map.get("empPhoneSecond"));
                        empResp.setEmpTelecomBusinessPhone((String) map.get("empTelecomBusinessPhone"));
                        empResp.setEmpUnicomBusinessPhone((String) map.get("empUnicomBusinessPhone"));
                        empResp.setPositionId((String) map.get("positionId"));
                        empResp.setPositionName((String) map.get("positionName"));
                        
                        empResp.setEmpEmail((String) map.get("empEmail"));
                        empResp.setEmpPhone((String) map.get("empPhone"));
                        
                        empRespsA.add(empResp);
                    }
                    
                    orgGroupResp.setEmployeeList(empRespsA);
                    List<EMPResp> empRespsb = new ArrayList<>();
                    if (StringUtils.isBlank(group.getRangeEmp()) == false) {
                        for (String empCode : group.getRangeEmp().split(",")) {
                            for (HrmsEmployeeResp e : hrmsEmployeeRespList) {
                                if (empCode.equals(e.getEmployeeNo())) {
                                    EMPResp empResp = new EMPResp();
                                    empResp.setUserAccounts(e.getEmployeeNo());
                                    empResp.setEmpName(e.getEmployeeName());
                                    empResp.setEmpDeptName(e.getOrgName());
                                    empResp.setId(e.getEmployeeId());
                                    if(StringUtils.isNotBlank(e.getGender())) {
                                        empResp.setEmpSex(Short.parseShort(e.getGender()));
                                    }
                                    empResp.setEmpCode(e.getEmployeeNo());
                                    if (StringUtils.isBlank(e.getEmployeeStatus()) == false) {
                                        empResp.setEmpStatus(Short.parseShort(e.getEmployeeStatus()));
                                    }
                                    empResp.setPositionId(e.getPositionId());
                                    empResp.setPositionName(e.getPositionName());

                                    empResp.setAvatar(e.getAvatar());
                                    empResp.setEmpBusinessPhone(e.getEmpBusinessPhone());
                                    empResp.setEmpShortPhone(e.getLandlineNumber());
                                    empResp.setLandlineNumber(e.getLandlineNumber());
                                    empResp.setEmpPhoneSecond(e.getEmpPhoneSecond());
                                    empResp.setEmpTelecomBusinessPhone(e.getEmpTelecomBusinessPhone());
                                    empResp.setEmpUnicomBusinessPhone(e.getEmpUnicomBusinessPhone());
                                    empResp.setPositionId(e.getPositionId());
                                    empResp.setPositionName(e.getPositionName());
                                    empRespsb.add(empResp);
                                }
                            }
                        }
                    }
                    orgGroupResp.setRangeEmployeeList(empRespsb);
                }
                orgGroupResps.add(orgGroupResp);
            }
        }
        return orgGroupResps;
    }

    private List<String> batchSelect(List<String> str) {
        List<String> empList = new ArrayList<>();
        int listSize = str.size();//数据大小
        int count = 1000; //初始化数据大小
        int loopSize = (listSize / count) + 1;//循环多少次
        for (int i = 0; i < loopSize; i++) {
            if ((i + 1) == loopSize) {
                int start = (i * count);
                int end = listSize;//总数
                empList = str.subList(start, end);//返回一个List集合的其中一部分视图。包含start，不包含end
            } else {
                int start = (i * count);
                int end = ((i + 1) * count);
                empList = str.subList(start, end);
            }
            return empList;
        }
        return null;
    }

    @Override
    public List<OrgGroup> getOrgGroupTree(Map<String, Object> params) {
        params.put("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        return orgGroupMapper.getOrgGroupTree(params);
    }

    @Override
    public List<Map<String, Object>> getOrgGroupUser(Page page, String groupId) {
        
        return orgUserGroupMapper.selectUserGroupList(page,groupId,UserInfoHolder.getCurrentUserCorpCode());
    }

    @Override
    public List<String> selectGroupIdByUserCode(String userCode) {
        return orgGroupMapper.selectGroupIdByUserCode(userCode,UserInfoHolder.getCurrentUserCorpCode());
    }

    @Override
    public List<Map<String, String>> selectOrgGroupList(String groupName) {
        return orgGroupMapper.selectOrgGroupList(groupName,UserInfoHolder.getCurrentUserCorpCode());
    }


    /**
     * 获取基础数据列表
     *
     * @return java.util.List<cn.trasen.basicsbottom.model.OrgGroup>
     * <AUTHOR>
     * @date 2022/2/8 16:29
     */
    public OrgGroup getBase(OrgGroupInBO orgGroupInBO) {

        Example example = new Example(OrgGroup.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (StringUtils.isBlank(orgGroupInBO.getGroupId()) == false) {
            criteria.andEqualTo("groupId", orgGroupInBO.getGroupId());
        }
        if (StringUtils.isBlank(orgGroupInBO.getEqGroupName()) == false) {
            criteria.andEqualTo("groupName", orgGroupInBO.getEqGroupName());
        }
        example.setOrderByClause(" GROUP_ID desc LIMIT 1 ");
        return orgGroupMapper.selectOneByExample(example);
    }

    /**
     * 获取
     *
     * @param orgGroupEmpInBO
     * @return java.util.List<cn.trasen.basicsbottom.model.OrgUserGroup>
     * <AUTHOR>
     * @date 2022/2/9 9:45
     */
    public List<OrgUserGroup> listBaseOrgUserGroup(OrgGroupEmpInBO orgGroupEmpInBO) {
        Example example = new Example(OrgUserGroup.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isBlank(orgGroupEmpInBO.getGroupId()) == false) {
            criteria.andEqualTo("groupId", orgGroupEmpInBO.getGroupId());
        }
        return orgUserGroupMapper.selectByExample(example);
    }

    @Override
    /**
     * 获取员工列表
     *
     * @return
     * <AUTHOR>
     * @date 2022/2/8 16:02
     */
    public List<OrgGroupEmpOutBO> listOrgGroupEmp(OrgGroupEmpInBO orgGroupEmpInBO) {
        return listOrgGroupEmp(null, orgGroupEmpInBO);
    }

    @Override
    /**
     * 获取员工列表
     *
     * @return
     * <AUTHOR>
     * @date 2022/2/8 16:02
     */
    public List<OrgGroupEmpOutBO> listOrgGroupEmp(Page page, OrgGroupEmpInBO orgGroupEmpInBO) {
        List<OrgGroupEmpOutBO> orgGroupEmpOutBOList = new ArrayList<>();
        
        
        orgGroupEmpInBO.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        
        if(StringUtils.isEmpty(orgGroupEmpInBO.getGroupId())){
             return new ArrayList<>();
        }
        
        if (page == null) {
            orgGroupEmpOutBOList = orgUserGroupMapper.list(orgGroupEmpInBO);
        } else {
            orgGroupEmpOutBOList = orgUserGroupMapper.list(page, orgGroupEmpInBO);
        }
        if (CollectionUtils.isEmpty(orgGroupEmpOutBOList)) {
            return new ArrayList<>();
        }
        Set<String> empCodeList = new HashSet<>();
        orgGroupEmpOutBOList.forEach(u -> {
            empCodeList.add(u.getEmpCode());
        });
        List<HrmsEmployeeResp> employeeRespList = hrmsEmployeeService.getEmployeeDetailByCodes(new ArrayList<>(empCodeList));
        orgGroupEmpOutBOList.forEach(e -> {
            employeeRespList.forEach(emp -> {
                if (emp.getEmployeeNo().equals(e.getEmpCode())) {
                    e.setEmpId(emp.getEmployeeId());
                    e.setEmpName(emp.getEmployeeName());
                    e.setAvatar(emp.getAvatar());
                    e.setGender(emp.getGender());
                    e.setGenderText(emp.getGenderText());
                    e.setEmpMobile(emp.getPhoneNumber());
                    e.setIsEnable(emp.getIsEnable());
                    e.setOrgId(emp.getOrgId());
                    e.setOrgName(emp.getOrgName());
                    e.setPersonalIdentity(emp.getPersonalIdentity());
                    e.setPersonalIdentityName(emp.getPersonalIdentityName());
                    e.setPositionId(emp.getPositionId());
                    e.setPositionName(emp.getPositionName());
                    e.setCreateDate(emp.getCreateDate());
                }
            });
        });
        
        orgGroupEmpOutBOList = orgGroupEmpOutBOList.stream().sorted(Comparator.comparing(OrgGroupEmpOutBO::getSort)).collect(Collectors.toList());
        
        return orgGroupEmpOutBOList;
    }


    @Override
    @Transactional(readOnly = false)
    /**
     * 移除员工
     * @param groupId
     * @param empCode
     * @return void
     * <AUTHOR>
     * @date 2022/2/8 17:52
     */
    public void deleteGroupEmp(String groupId, String empCode) {
        Example example = new Example(OrgUserGroup.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("groupId", groupId);
        criteria.andEqualTo("userId", empCode);
        orgUserGroupMapper.deleteByExample(example);

        OrgGroupEmpInBO orgGroupEmpInBO = new OrgGroupEmpInBO();
        orgGroupEmpInBO.setGroupId(groupId);
        List<OrgUserGroup> orgUserGroupList = listBaseOrgUserGroup(orgGroupEmpInBO);
        
       
        if(null != orgUserGroupList && orgUserGroupList.size() > 0){
              OrgGroup orgGroupUpdate = new OrgGroup();
              orgGroupUpdate.setGroupId(groupId);
              List<String> oldEmpList = orgUserGroupList.stream().map(OrgUserGroup::getUserId).collect(Collectors.toList());

              List<HrmsEmployeeResp> employeeRespList = hrmsEmployeeService.getEmployeeDetailByCodes(oldEmpList);

              List<String> empNameList = employeeRespList.stream().map(HrmsEmployeeResp::getEmployeeName).collect(Collectors.toList());
              List<String> empCodeList = employeeRespList.stream().map(HrmsEmployeeResp::getEmployeeNo).collect(Collectors.toList());
              
            
              orgGroupUpdate.setGroupUserNames(org.apache.commons.lang.StringUtils.join(empNameList.toArray(), ","));
              orgGroupUpdate.setGroupUserString(org.apache.commons.lang.StringUtils.join(empCodeList.toArray(), ","));
              
              orgGroupMapper.updateByPrimaryKeySelective(orgGroupUpdate);
        }else{
             OrgGroup orgGroupUpdate = orgGroupMapper.selectByPrimaryKey(groupId);
             orgGroupUpdate.setGroupUserNames("");
             orgGroupUpdate.setGroupUserString("");
             orgGroupMapper.updateByPrimaryKey(orgGroupUpdate);
        }

    }


    @Override
    @Transactional(readOnly = false)
    /**
     * 保存分组员工信息
     * @param groupId
     * @param empCode
     * @return void
     * <AUTHOR>
     * @date 2022/2/8 17:52
     */
    public void saveGroupEmp(String groupId, List<String> empCodeList) {
        if (StringUtils.isBlank(groupId)) {
            throw new BusinessException("分组不能为空！");
        }
        OrgGroupInBO orgGroupInBO = new OrgGroupInBO();
        orgGroupInBO.setGroupId(groupId);
        OrgGroup orgGroup = getBase(orgGroupInBO);
        Example example = new Example(OrgUserGroup.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("groupId", groupId);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        orgUserGroupMapper.deleteByExample(example);

        /**
         * 批量新增可以优化
         */
        empCodeList.forEach(e -> {
            OrgUserGroup orgUserGroup = new OrgUserGroup();
            orgUserGroup.setUserId(e);
            orgUserGroup.setCreateDate(new Date());
            orgUserGroup.setUpdateDate(new Date());
            orgUserGroup.setGroupId(groupId);
            orgUserGroup.setCreateUser(UserInfoHolder.getCurrentUserCode());
            orgUserGroup.setUpdateUser(UserInfoHolder.getCurrentUserCode());
            orgUserGroup.setIsDeleted(Contants.IS_DELETED_FALSE);
            orgUserGroup.setType(orgGroup.getGroupType());
            orgUserGroup.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            orgUserGroupMapper.insertSelective(orgUserGroup);
        });

        List<HrmsEmployeeResp> employeeRespList = hrmsEmployeeService.getEmployeeDetailByCodes(empCodeList);

        List<String> empName = employeeRespList.stream().map(HrmsEmployeeResp::getEmployeeName).collect(Collectors.toList());
        List<String> empCode = employeeRespList.stream().map(HrmsEmployeeResp::getEmployeeNo).collect(Collectors.toList());

        OrgGroup orgGroupUpdate = new OrgGroup();
        orgGroupUpdate.setGroupUserNames(org.apache.commons.lang.StringUtils.join(empName.toArray(), ","));
        orgGroupUpdate.setGroupUserString(org.apache.commons.lang.StringUtils.join(empCode.toArray(), ","));
        orgGroupUpdate.setGroupId(groupId);
        orgGroupMapper.updateByPrimaryKeySelective(orgGroupUpdate);
    }


    @Override
    /**
     *
     * @param orgGroupEmpInBO
     * @return byte[]
     * <AUTHOR>
     * @date 2022/2/9 10:34
     */
    public byte[] downloadExportGroupEmp(OrgGroupEmpInBO orgGroupEmpInBO) throws IOException {
        ClassPathResource resource = new ClassPathResource("/template/orggroup/exportOrgUserGroup.xlsx");
        if (!resource.exists()) {
            throw new BusinessException("数据模板不存在！");
        }
        List<OrgGroupEmpOutBO> orgGroupEmpOutBOList = listOrgGroupEmp(orgGroupEmpInBO);
        List<OrgGroupEmpExportExcel> orgGroupEmpExportExcelList = BeanUtil.copyToList(orgGroupEmpOutBOList, OrgGroupEmpExportExcel.class);
        byte[] excelBytes = ExcelNewUtils.write(resource.getInputStream(), orgGroupEmpExportExcelList, 1);
        return excelBytes;
    }


    @Override
    /**
     * 获取当前用户用户分株
     * @param orgGroupInBO
     * @return java.util.List<cn.trasen.basicsbottom.model.OrgGroup>
     * <AUTHOR>
     * @date 2022/3/7 14:34
     */
    public List<OrgGroup> listCurrentEmpOrgGroup(OrgGroupInBO orgGroupInBO) {

        Example example = new Example(OrgGroup.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        criteria.andEqualTo("groupType", "0");
        criteria.andEqualTo("isEnable", "1");


        if (StringUtils.isNotBlank(orgGroupInBO.getGroupName())) {//群组名称
            criteria.andLike("groupName", "%" + orgGroupInBO.getGroupName() + "%");
        }


        StringBuilder stringBuilderSql = new StringBuilder();
        stringBuilderSql.append(" ((IFNULL(RANGE_EMP,'')=''  AND IFNULL(RANGE_ORG,'')='' )  ");
        stringBuilderSql.append(" or (CONCAT(',',RANGE_EMP,',') like '%," + UserInfoHolder.getCurrentUserCode() + ",%')  ");
        stringBuilderSql.append(" or (CONCAT(',',RANGE_ORG,',') like '%," + UserInfoHolder.getCurrentUserInfo().getDeptId() + ",%'))  ");

        criteria.andCondition(stringBuilderSql.toString());
        /**
         * 增加不同数据库方言特殊处理 
         * add lijr#trasen.cn 2024-04-07 
         */
        if (DataBaseProvider.databaseId.contains("mysql")) {
        	example.setOrderByClause("CONVERT(GROUP_ORDER,SIGNED) asc,CREATE_DATE desc");
        }
        if (DataBaseProvider.databaseId.contains("kingbase")) {
        	example.setOrderByClause("cast(GROUP_ORDER as SIGNED) asc,CREATE_DATE desc");
        }
        return orgGroupMapper.selectByExample(example);
    }


    @Override
    /**
     * @description: 批量修改排序
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(List<OrgGroupInBO> orgGroupInBOList) {
        for (OrgGroupInBO orgGroupInBO : orgGroupInBOList) {
            OrgGroup entity = new OrgGroup();
            entity.setGroupId(orgGroupInBO.getGroupId());
            entity.setGroupOrder(orgGroupInBO.getGroupOrder() + "");
            orgGroupMapper.updateByPrimaryKeySelective(entity);
        }
    }

    @Override
    public List<TreeModel> getOrgGroupTreeList(OrgGroup record) {
        
        Example example1 = new Example(OrgGroupClass.class);
        example1.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        example1.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        List<OrgGroupClass> orgGroupClassList = orgGroupClassMapper.selectByExample(example1);
        
        Example example = new Example(OrgGroup.class);
        example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        List<OrgGroup> orgGroupList = orgGroupMapper.selectByExample(example);
        List<TreeModel> trees = Lists.newLinkedList();
        
        List<TreeModel> nodes = Lists.newLinkedList();
        
        for (OrgGroupClass orgGroupClass : orgGroupClassList) {
            TreeModel node = new TreeModel();
            node.setId(orgGroupClass.getId());
            node.setName(orgGroupClass.getClassName());
            node.setPid("");
            nodes.add(node);
        }
        
        orgGroupList.stream().forEach(item -> {
            TreeModel node = new TreeModel();
            node.setId(item.getGroupId());
            node.setName(item.getGroupName());
            node.setPid(item.getGroupClassId());
            nodes.add(node);
        });
        
        CommTree commTree = new CommTree();
        trees = commTree.CommTreeList(nodes);
        return trees;
    }

    @Override
    @Transactional(readOnly = false)
    public void updateUserGroupSort(List<OrgUserGroup> orgGroupUpdateSortList) {
        for (OrgUserGroup orgUserGroup : orgGroupUpdateSortList) {
            orgUserGroupMapper.updateUserGroupSort(orgUserGroup);
        }
    }

	@Override
	@Transactional(readOnly = false)
	public void syncLeaderToGroup() {
		//初始化分类  先判断有没有 没有新增  有不管
		OrgGroupClass orgGroupClass = orgGroupClassMapper.selectByPrimaryKey("888888888888888888");
		if(null == orgGroupClass) {
			orgGroupClass = new OrgGroupClass();
			orgGroupClass.setId("888888888888888888");
			orgGroupClass.setClassName("组织机构领导");
			orgGroupClass.setClassDescription("系统预设分类，自定义群组的不要选这个分类");
			orgGroupClass.setSortNo((short) 999);
			orgGroupClass.setDomainId("0");
			orgGroupClass.setCreateDate(new Date());
			orgGroupClass.setCreateUser(UserInfoHolder.getCurrentUserCode());
			orgGroupClass.setCreateUserName(UserInfoHolder.getCurrentUserName());
			orgGroupClass.setIsDeleted("N");
			orgGroupClass.setClassType((short) 0);
			orgGroupClass.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			orgGroupClassMapper.insertSelective(orgGroupClass);
		}
		
		StringConvertPinyin cte = new StringConvertPinyin();
		//查询科室领导字典
		List<DictItem> dictItemByTypeCode = dictItemService.getDictItemByTypeCode("LDJS");
		for (DictItem dictItem : dictItemByTypeCode) {
			
			//查询领导id
			String leaderCodes = organizationLeaderMapper.selectLeaderCodeByRoleId(dictItem.getItemNameValue());
			if(StringUtils.isNotBlank(leaderCodes)) {
				List<HrmsEmployeeResp> employeeList = hrmsEmployeeService.getEmployeeByCodes(ListUtil.of(leaderCodes.split(",")));
				
				List<String> empNames = new ArrayList<>();
				List<String> empCodes = new ArrayList<>();
				for (HrmsEmployeeResp emp : employeeList) {
					empNames.add(emp.getEmployeeName());
					empCodes.add(emp.getEmployeeNo());
				}
				
				//科室秘书
				if("1".equals(dictItem.getItemNameValue()) && CollectionUtils.isNotEmpty(empCodes)) {
					//初始化分组 先判断有没有 没有新增  有更新
					generateLeaderGroup(cte, empNames, empCodes,"111111111111111111" + UserInfoHolder.getCurrentUserCorpCode(),"科室秘书(机构)");
				}
				
				//护士长
				if("2".equals(dictItem.getItemNameValue()) && CollectionUtils.isNotEmpty(empCodes)) {
					generateLeaderGroup(cte, empNames, empCodes,"222222222222222222" + UserInfoHolder.getCurrentUserCorpCode(),"护士长(机构)");
				}
				
				//科室主任
				if("3".equals(dictItem.getItemNameValue()) && CollectionUtils.isNotEmpty(empCodes)) {
					generateLeaderGroup(cte, empNames, empCodes,"333333333333333333" + UserInfoHolder.getCurrentUserCorpCode(),"科室主任(机构)");
				}
				
				//医疗主任
				if("4".equals(dictItem.getItemNameValue()) && CollectionUtils.isNotEmpty(empCodes)) {
					generateLeaderGroup(cte, empNames, empCodes,"444444444444444444" + UserInfoHolder.getCurrentUserCorpCode(),"医疗主任(机构)");
				}
				
				//部门领导
				if("5".equals(dictItem.getItemNameValue()) && CollectionUtils.isNotEmpty(empCodes)) {
					generateLeaderGroup(cte, empNames, empCodes,"555555555555555555" + UserInfoHolder.getCurrentUserCorpCode(),"部门领导(机构)");
				}
				
				//分管领导
				if("6".equals(dictItem.getItemNameValue()) && CollectionUtils.isNotEmpty(empCodes)) {
					generateLeaderGroup(cte, empNames, empCodes,"666666666666666666" + UserInfoHolder.getCurrentUserCorpCode(),"分管领导(机构)");
				}
				
				//直接领导
				if("7".equals(dictItem.getItemNameValue()) && CollectionUtils.isNotEmpty(empCodes)) {
					generateLeaderGroup(cte, empNames, empCodes,"777777777777777777" + UserInfoHolder.getCurrentUserCorpCode(),"直接领导(机构)");
				}
				
				//部门长
				if("8".equals(dictItem.getItemNameValue()) && CollectionUtils.isNotEmpty(empCodes)) {
					generateLeaderGroup(cte, empNames, empCodes,"888888888888888888" + UserInfoHolder.getCurrentUserCorpCode(),"部门长(机构)");
				}
				
				//护士长（副）
				if("9".equals(dictItem.getItemNameValue()) && CollectionUtils.isNotEmpty(empCodes)) {
					generateLeaderGroup(cte, empNames, empCodes,"999999999999999999" + UserInfoHolder.getCurrentUserCorpCode(),"护士长（副）(机构)");
				}
				
				//科室主任（副）
				if("10".equals(dictItem.getItemNameValue()) && CollectionUtils.isNotEmpty(empCodes)) {
					generateLeaderGroup(cte, empNames, empCodes,"000000000000000000" + UserInfoHolder.getCurrentUserCorpCode(),"科室主任（副）(机构)");
				}
			}
		}
	}

	private void generateLeaderGroup(StringConvertPinyin cte, List<String> empNames, List<String> empCodes,String groupId,String groupName) {
		OrgGroup orgGroup = orgGroupMapper.selectByPrimaryKey(groupId);
		if(null == orgGroup) {
			orgGroup = new OrgGroup();
			orgGroup.setGroupId(groupId);
			orgGroup.setGroupName(groupName);
			orgGroup.setGroupPinyin(cte.getAllFirstLetter(orgGroup.getGroupName()));
			orgGroup.setGroupUserNames(String.join(",", empNames));
			orgGroup.setGroupUserString(String.join(",", empCodes));
			orgGroup.setDomainId("0");
			orgGroup.setGroupType((short) 0);
			orgGroup.setGroupClassId("888888888888888888");
			orgGroup.setGroupClassName("组织机构领导");
			orgGroup.setGroupOrder("1");
			orgGroup.setCreateUser(UserInfoHolder.getCurrentUserCode());
			orgGroup.setCreateUserName(UserInfoHolder.getCurrentUserName());
			orgGroup.setCreateDate(new Date());
			orgGroup.setIsDeleted("N");
			orgGroup.setIsEnable("1");
			orgGroup.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			orgGroupMapper.insertSelective(orgGroup);
		}else {
			orgGroup.setGroupUserNames(String.join(",", empNames));
			orgGroup.setGroupUserString(String.join(",", empCodes));
			orgGroupMapper.updateByPrimaryKeySelective(orgGroup);
		}
		
		
		//先删除之前的 
		orgUserGroupMapper.deleteByGroupId(groupId);
		
		for (String empCode : empCodes) {
			OrgUserGroup orgUserGroup = new OrgUserGroup();
			orgUserGroup.setUserId(empCode);
			orgUserGroup.setGroupId(groupId);
			orgUserGroup.setCreateUser(orgUserGroup.getCreateUser());
			orgUserGroup.setCreateUserName(UserInfoHolder.getCurrentUserName());
			orgUserGroup.setCreateDate(new Date());
			orgUserGroup.setIsDeleted("N");
			orgUserGroup.setSort(0);
			orgUserGroup.setType((short) 0);
			orgUserGroup.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			orgUserGroupMapper.insertSelective(orgUserGroup);
		}
	}
    
    
}
